export interface ResponseResult {
  success: boolean;
  message: string;
  data?: any;
}

export interface LoginModel {
    UserName: string;
    Password: string;
    isReadAgree: boolean;
    selectLanguage: string;
}

export interface LoginSuccessModel {
  token: string;
  refreshToken: string;
  sysUserResponseDTO: SysUserResponseDTO;
}


export interface SysUserResponseDTO {
  id: string;
  userNTAccount: string;
  userName: string;
  givenName: string;
  sn: string;
  mail: string;
  department: string;
  favCollecitonId: string;
  docupediaToken: string;
  status: number;
  groups: SysGroup[];
}

export interface SysUser {
  id: string;
  userNTAccount: string;
  userName: string;
  givenName: string;
  sn: string;
  mail: string;
  department: string;
  favCollecitonId: string;
  docupediaToken: string;
  status: number;
}

export interface SysUserAdd {
  userNTAccount: string;
  userName: string;
  givenName: string;
  sn: string;
  mail: string;
  department: string;
  favCollecitonId: string;
  docupediaToken: string;
  status: number;
}

export interface SysUserUpdate {
  id: string;
  userNTAccount: string;
  userName: string;
  givenName: string;
  sn: string;
  mail: string;
  department: string;
  favCollecitonId: string;
  docupediaToken: string;
  status: number;
}

export interface SysUserFilter {
  userNTAccount?: string;
  userName?: string;
  givenName?: string;
  sn?: string;
  mail?: string;
  department?: string;
}


export interface SysGroupResponseDTO {
  id: string;
  name: string;
  size: number;
  users: SysUserResponseDTO[];
}

export interface SysGroup {
  id: string;
  name: string;
  size: number;
  users: SysUser[];
}

export interface SysGroupAdd {
  name: string;
  size: number;
}

export interface SysGroupUpdate {
  id: string;
  name: string;
  size: number;
}
