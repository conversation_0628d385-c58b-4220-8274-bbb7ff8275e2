.token-guide-content {
  max-width: 600px;
  padding: 20px 0;

  .warning-section {
    display: flex;
    align-items: center;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;

    .warning-icon {
      color: #f39c12;
      margin-right: 12px;
      font-size: 24px;
    }

    .warning-text {
      margin: 0;
      color: #856404;
      font-weight: 500;
    }
  }

  .guide-section {
    margin-bottom: 24px;

    h3 {
      color: #2c3e50;
      margin-bottom: 16px;
      font-size: 18px;
    }

    .guide-steps {
      padding-left: 20px;

      li {
        margin-bottom: 16px;
        line-height: 1.6;

        strong {
          color: #2c3e50;
          display: block;
          margin-bottom: 4px;
        }

        p {
          margin: 4px 0;
          color: #555;
        }

        ul {
          margin: 8px 0;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
          }
        }

        .important-note {
          background-color: #ffeaa7;
          padding: 8px 12px;
          border-radius: 4px;
          border-left: 4px solid #f39c12;
          margin: 8px 0;
        }
      }
    }
  }

  .help-section {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #007bff;

    h4 {
      margin: 0 0 8px 0;
      color: #2c3e50;
    }

    p {
      margin: 0;
      color: #555;
    }

    a {
      color: #007bff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .token-input-section {
    margin: 24px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;

    .full-width {
      width: 100%;
    }

    .success-message {
      display: flex;
      align-items: center;
      color: #28a745;
      margin-top: 12px;
      font-weight: 500;

      mat-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }

    .error-message {
      display: flex;
      align-items: center;
      color: #dc3545;
      margin-top: 12px;
      font-weight: 500;

      mat-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }
  }
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}
