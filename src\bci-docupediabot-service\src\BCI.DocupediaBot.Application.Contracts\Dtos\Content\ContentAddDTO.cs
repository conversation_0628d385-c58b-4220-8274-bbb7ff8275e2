﻿using System;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Content
{
  public class ContentAddDTO
	{
		public Guid PageId { get; set; } = default!;

		public string Title { get; set; } = default!;

		public string Url { get; set; } = default!;
		public int VersionNo { get; set; } = default!;

		public string SourceId { get; set; } = default!;

		public string? Path { get; set; } = default!;

		public DateTimeOffset SourceModificationTime { get; set; } = default!;
		public string OriginContent { get; set; } = default!;
		public string ProcessedContent { get; set; } = default!;
    public string? SummarizedContent { get; set; } = default!;

	}
}
