﻿using BCI.DocupediaBot.Domain.Enums;
using System;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Collection
{
  public class CollectionAddDTO
	{
		public string Name { get; set; } = default!;
		public string Comment { get; set; } = default!;
		public EmbeddingModel EmbeddingModel { get; set; } = default!;
		public int ChunkSize { get; set; } = default!;
		public int Status { get; set; } = default!;
		public bool IsAutomaticUpdate { get; set; } = default!;
		public UpdateType? UpdateType { get; set; } = default!;
		public int? IntervalNumber { get; set; } = default!;
		public TimeOnly? UpdateTime { get; set; }
	}
}
