import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QualityGateComponent } from './components/quality-gate/quality-gate.component';

const routes: Routes = [
  {
    path: 'quality-gate',
    component: QualityGateComponent
  },
  {
    path: '',
    redirectTo: 'quality-gate',
    pathMatch: 'full'
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DataGenerationRoutingModule { }
