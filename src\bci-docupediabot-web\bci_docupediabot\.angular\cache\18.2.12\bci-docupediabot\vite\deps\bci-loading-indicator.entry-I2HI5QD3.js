import {
  h,
  registerInstance
} from "./chunk-SKBP5EI5.js";
import "./chunk-Y4T55RDF.js";

// node_modules/@bci-web-core/web-components/dist/esm/bci-loading-indicator.entry.js
var loadingIndicatorComponentCss = '/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */:host{position:relative}.bci-activity-indicator-container{position:relative}.bci-activity-indicator-container .bci-indicator-overlay{position:relative;z-index:10000;width:72px;height:72px;transform:translate(-50%, -50%);margin:0 auto;transform:initial;margin:0;left:50%;transform:translateX(-50%)}.bci-activity-indicator-container .bci-indicator-overlay::before{content:" ";width:24px;height:24px;position:absolute;animation:top-right 6s infinite}.bci-activity-indicator-container .bci-indicator-overlay::after{content:" ";width:24px;height:24px;position:absolute;animation:bottom-left 6s infinite}@keyframes bottom-left{0%{background-color:transparent;left:24px;top:24px}6.25%{background-color:#791d73;left:24px;top:24px}12.5%{background-color:#791d73;left:24px;top:48px}18.75%{background-color:#ed0007;left:24px;top:24px}25%{background-color:#ed0007;left:0;top:24px}31.25%{background-color:#004523;left:24px;top:24px}37.5%{background-color:#004523;left:24px;top:48px}43.75%{background-color:#00884a;left:24px;top:24px}50%{background-color:#00884a;left:0;top:24px}56.25%{background-color:#18837e;left:24px;top:24px}62.5%{background-color:#18837e;left:24px;top:48px}68.75%{background-color:#007bc0;left:24px;top:24px}75%{background-color:#007bc0;left:0;top:24px}81.25%{background-color:#005587;left:24px;top:24px}87.5%{background-color:#005587;left:24px;top:48px}93.75%{background-color:#c535bc;left:24px;top:24px}100%{background-color:transparent;left:24px;top:24px}}@keyframes top-right{0%{background-color:transparent;left:24px;top:24px}6.25%{background-color:#791d73;left:24px;top:24px}12.5%{background-color:#c535bc;left:24px;top:0}18.75%{background-color:#ed0007;left:24px;top:24px}25%{background-color:#791d73;left:48px;top:24px}31.25%{background-color:#004523;left:24px;top:24px}37.5%{background-color:#ed0007;left:24px;top:0}43.75%{background-color:#00884a;left:24px;top:24px}50%{background-color:#004523;left:48px;top:24px}56.25%{background-color:#18837e;left:24px;top:24px}62.5%{background-color:#00884a;left:24px;top:0}68.75%{background-color:#007bc0;left:24px;top:24px}75%{background-color:#18837e;left:48px;top:24px}81.25%{background-color:#005587;left:24px;top:24px}87.5%{background-color:#007bc0;left:24px;top:0}93.75%{background-color:#c535bc;left:24px;top:24px}100%{background-color:transparent;left:24px;top:24px}}.bci-loading-indicator-hidden{display:none}.bci-content-container:not(:empty)~.indicator-overlay{position:absolute;top:30%}';
var LoadingIndicatorComponent = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.isLoading = true;
  }
  render() {
    return h("div", {
      class: !this.isLoading ? "bci-loading-indicator-hidden" : ""
    }, h("div", {
      class: "bci-activity-indicator-container"
    }, h("div", {
      class: "bci-content-container"
    }), h("div", {
      class: "bci-indicator-overlay"
    })));
  }
};
LoadingIndicatorComponent.style = loadingIndicatorComponentCss;
export {
  LoadingIndicatorComponent as bci_loading_indicator
};
//# sourceMappingURL=bci-loading-indicator.entry-I2HI5QD3.js.map
