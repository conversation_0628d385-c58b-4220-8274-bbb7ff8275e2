import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DataRefreshService {
  private userDataRefreshSubject = new Subject<void>();
  private groupDataRefreshSubject = new Subject<void>();


  userDataRefresh$ = this.userDataRefreshSubject.asObservable();
  groupDataRefresh$ = this.groupDataRefreshSubject.asObservable();

  refreshUserData(): void {
    this.userDataRefreshSubject.next();
  }

  refreshGroupData(): void {
    this.groupDataRefreshSubject.next();
  }

  refreshAllData(): void {
    this.refreshUserData();
    this.refreshGroupData();
  }
}
