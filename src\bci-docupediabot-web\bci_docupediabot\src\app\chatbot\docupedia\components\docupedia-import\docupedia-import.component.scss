@import "@bci-web-core/common-styles/sass/bci/bci-variables";


:host {
  display: block;
}


bci-page-content,
bci-master-view {
  height: auto !important;
  min-height: auto !important;
  overflow: visible !important;
}


:host ::ng-deep {
  bci-page-content,
  bci-master-view,
  .bci-master-view-content,
  .bci-page-content {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
  }


  bci-paginator {
    overflow: visible !important;


    .bci-select,
    .mat-select,
    .mat-form-field {
      overflow: visible !important;
    }


    .mat-select-panel,
    .cdk-overlay-pane {
      overflow: visible !important;
      max-height: none !important;
    }
  }


  .cdk-overlay-container {
    overflow: visible !important;
  }
}

.tab-content{
  padding: $grid-size * 3 $grid-size * 4;
}

@mixin truncate-text() {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

mat-card.mat-mdc-card.mdc-card {
  container-type: inline-size;
}

hr {
  margin: $grid-size * 3 0;
}

mat-card{
  margin-top: $grid-size;
}

.card-row-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr) auto;
  align-items: center;
}

.card-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  width: 50px;
  height: 40px;
}

/* Responsive design */
@media screen and (max-width: 900px) {
  .card-row-grid {
    grid-template-columns: 1fr 1fr auto;
    grid-template-areas:
      "name name buttons"
      "embedding-model auto-update buttons"
      "update-type interval buttons"
      "last-modified last-modified buttons";
    row-gap: 12px;
  }

  .card-item {
    &.name { grid-area: name; }
    &.embedding-model { grid-area: embedding-model; }
    &.auto-update { grid-area: auto-update; }
    &.update-type { grid-area: update-type; }
    &.interval { grid-area: interval; }
    &.last-modified { grid-area: last-modified; }
  }

  .action-buttons {
    grid-area: buttons;
    flex-wrap: wrap;
    max-width: 120px;
    align-self: start;
  }
}

@media screen and (max-width: 600px) {
  .card-row-grid {
    grid-template-columns: 1fr auto;
    grid-template-areas:
      "name buttons"
      "embedding-model buttons"
      "auto-update buttons"
      "update-type buttons"
      "interval buttons"
      "last-modified buttons";
  }

  .action-buttons {
    max-width: 80px;
    align-self: start;
  }
}

mat-table {
  width: 100%;
  min-width: 800px; // Ensure minimum width for all columns
  table-layout: fixed; // Use fixed layout for better column control
}

.mat-table-container {
  overflow-x: auto;
  max-width: 100%;
}

mat-header-cell,
mat-cell {
  padding: 8px 16px;
}

// Page table column widths
.mat-column-title {
  width: 45%;
  min-width: 250px;

  // Prevent title from wrapping
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mat-column-modificationTime {
  width: 16%;
  min-width: 150px;
  text-align: center;
}

.mat-column-embeddingVersionNo {
  width: 12%;
  min-width: 120px;
  text-align: center;
}

.mat-column-isIncludeChild,
.mat-column-isEmbedding,
.mat-column-contentNumber,
.mat-column-versionNo {
  width: 7%;
  min-width: 70px;
  max-width: 90px;
  text-align: center;
}

.mat-column-actions {
  width: 8%;
  min-width: 120px;
  max-width: 120px;
}

// Default for other columns
mat-header-cell:not(.actions-header),
mat-cell:not(.actions-cell) {
  min-width: 80px;
  width: auto;
}

.actions-header,
.actions-cell {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  justify-content: flex-end;
  padding-right: 8px;
}

.subpage-actions {
  display: flex;
  justify-content: flex-end;
  gap: 24px;
  padding-right: 16px;
}

mat-cell.actions-cell {
  overflow: visible;
}


.pagination-container {
  margin-top: $grid-size * 2;
  padding: $grid-size 0;
  border-top: 1px solid #e0e0e0;
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

bci-paginator {
  width: 100%;
  overflow: visible !important;
  height: auto !important;
}


.center-in-space {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin: $grid-size * 4 0;
}


.content-wrapper {
  display: block;
}

.collections-content {
  margin-bottom: 0;
}


.process-card {
  margin-bottom: $grid-size;
}


