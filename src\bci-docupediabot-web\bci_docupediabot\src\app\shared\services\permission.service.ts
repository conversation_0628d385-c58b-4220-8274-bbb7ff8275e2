import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class PermissionService {

  constructor(private authService: AuthService) {}

  /**
   * Check if user has permission to access System menu
   * Only Admin group users or user with username yye4szh can access
   */
  canAccessSystemMenu(): boolean {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser) {
      return false;
    }


    if (currentUser.userNTAccount === 'yye4szh') {
      return true;
    }


    if (currentUser.groups && currentUser.groups.length > 0) {
      return currentUser.groups.some(group =>
        group.name.toLowerCase() === 'admin'
      );
    }

    return false;
  }

  /**
   * Check if user is administrator
   */
  isAdmin(): boolean {
    return this.canAccessSystemMenu();
  }

  /**
   * Check if user has permission for specific group
   */
  hasGroupPermission(groupName: string): boolean {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser?.groups) {
      return false;
    }

    return currentUser.groups.some(group =>
      group.name.toLowerCase() === groupName.toLowerCase()
    );
  }

  /**
   * Check if user can create Collection and Page
   * Users in Default group cannot create Collection and Page
   */
  canCreateCollectionAndPage(): boolean {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser?.groups) {
      return true;
    }

    return !currentUser.groups.some(group =>
      group.name.toLowerCase() === 'default'
    );
  }
}
