{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/index-93dc8059.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nconst NAMESPACE = 'bci-web-core-web-components';\n\n/**\n * Virtual DOM patching algorithm based on Snabbdom by\n * <PERSON> (@paldepind)\n * Licensed under the MIT License\n * https://github.com/snabbdom/snabbdom/blob/master/LICENSE\n *\n * Modified for Stencil's renderer and slot projection\n */\nlet scopeId;\nlet contentRef;\nlet hostTagName;\nlet useNativeShadowDom = false;\nlet checkSlotFallbackVisibility = false;\nlet checkSlotRelocate = false;\nlet isSvgMode = false;\nlet renderingRef = null;\nlet queuePending = false;\nconst createTime = (fnName, tagName = '') => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nconst uniqueTime = (key, measureText) => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nconst HYDRATED_CSS = '{visibility:hidden}.hydrated{visibility:inherit}';\n/**\n * Default style mode id\n */\n/**\n * Reusable empty obj/array\n * Don't add values to these!!\n */\nconst EMPTY_OBJ = {};\n/**\n * Namespaces\n */\nconst SVG_NS = 'http://www.w3.org/2000/svg';\nconst HTML_NS = 'http://www.w3.org/1999/xhtml';\nconst isDef = v => v != null;\n/**\n * Check whether a value is a 'complex type', defined here as an object or a\n * function.\n *\n * @param o the value to check\n * @returns whether it's a complex type or not\n */\nconst isComplexType = o => {\n  // https://jsperf.com/typeof-fn-object/5\n  o = typeof o;\n  return o === 'object' || o === 'function';\n};\n/**\n * Helper method for querying a `meta` tag that contains a nonce value\n * out of a DOM's head.\n *\n * @param doc The DOM containing the `head` to query against\n * @returns The content of the meta tag representing the nonce value, or `undefined` if no tag\n * exists or the tag has no content.\n */\nfunction queryNonceMetaTagContent(doc) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc.head) === null || _a === void 0 ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) === null || _b === void 0 ? void 0 : _b.getAttribute('content')) !== null && _c !== void 0 ? _c : undefined;\n}\n/**\n * Production h() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, child?: d.ChildType): d.VNode;\n// export function h(nodeName: string | d.FunctionalComponent, vnodeData: d.PropsType, ...children: d.ChildType[]): d.VNode;\nconst h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i = 0; i < c.length; i++) {\n      child = c[i];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== 'boolean') {\n        if (simple = typeof nodeName !== 'function' && !isComplexType(child)) {\n          child = String(child);\n        }\n        if (simple && lastSimple) {\n          // If the previous child was simple (string), we merge both\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          // Append a new vNode, if it's text, we create a text vNode\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    // normalize class / className attributes\n    {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== 'object' ? classData : Object.keys(classData).filter(k => classData[k]).join(' ');\n      }\n    }\n  }\n  if (typeof nodeName === 'function') {\n    // nodeName is a functional component\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  {\n    vnode.$key$ = key;\n  }\n  {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\n/**\n * A utility function for creating a virtual DOM node from a tag and some\n * possible text content.\n *\n * @param tag the tag for this element\n * @param text possible text content for the node\n * @returns a newly-minted virtual DOM node\n */\nconst newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    vnode.$attrs$ = null;\n  }\n  {\n    vnode.$key$ = null;\n  }\n  {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nconst Host = {};\n/**\n * Check whether a given node is a Host node or not\n *\n * @param node the virtual DOM node to check\n * @returns whether it's a Host node or not\n */\nconst isHost = node => node && node.$tag$ === Host;\n/**\n * Implementation of {@link d.FunctionalUtilities} for Stencil's VDom.\n *\n * Note that these functions convert from {@link d.VNode} to\n * {@link d.ChildNode} to give functional component developers a friendly\n * interface.\n */\nconst vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\n/**\n * Convert a {@link d.VNode} to a {@link d.ChildNode} in order to present a\n * friendlier public interface (hence, 'convertToPublic').\n *\n * @param node the virtual DOM node to convert\n * @returns a converted child node\n */\nconst convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\n/**\n * Convert a {@link d.ChildNode} back to an equivalent {@link d.VNode} in\n * order to use the resulting object in the virtual DOM. The initial object was\n * likely created as part of presenting a public API, so converting it back\n * involved making it 'private' again (hence, `convertToPrivate`).\n *\n * @param node the child node to convert\n * @returns a converted virtual DOM node\n */\nconst convertToPrivate = node => {\n  if (typeof node.vtag === 'function') {\n    const vnodeData = Object.assign({}, node.vattrs);\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\n/**\n * Parse a new property value for a given property type.\n *\n * While the prop value can reasonably be expected to be of `any` type as far as TypeScript's type checker is concerned,\n * it is not safe to assume that the string returned by evaluating `typeof propValue` matches:\n *   1. `any`, the type given to `propValue` in the function signature\n *   2. the type stored from `propType`.\n *\n * This function provides the capability to parse/coerce a property's value to potentially any other JavaScript type.\n *\n * Property values represented in TSX preserve their type information. In the example below, the number 0 is passed to\n * a component. This `propValue` will preserve its type information (`typeof propValue === 'number'`). Note that is\n * based on the type of the value being passed in, not the type declared of the class member decorated with `@Prop`.\n * ```tsx\n * <my-cmp prop-val={0}></my-cmp>\n * ```\n *\n * HTML prop values on the other hand, will always a string\n *\n * @param propValue the new value to coerce to some type\n * @param propType the type of the prop, expressed as a binary number\n * @returns the parsed/coerced value\n */\nconst parsePropertyValue = (propValue, propType) => {\n  // ensure this value is of the correct prop type\n  if (propValue != null && !isComplexType(propValue)) {\n    if (propType & 4 /* MEMBER_FLAGS.Boolean */) {\n      // per the HTML spec, any string value means it is a boolean true value\n      // but we'll cheat here and say that the string \"false\" is the boolean false\n      return propValue === 'false' ? false : propValue === '' || !!propValue;\n    }\n    if (propType & 2 /* MEMBER_FLAGS.Number */) {\n      // force it to be a number\n      return parseFloat(propValue);\n    }\n    if (propType & 1 /* MEMBER_FLAGS.String */) {\n      // could have been passed as a number or boolean\n      // but we still want it as a string\n      return String(propValue);\n    }\n    // redundant return here for better minification\n    return propValue;\n  }\n  // not sure exactly what type we want\n  // so no need to change to a different type\n  return propValue;\n};\nconst getElement = ref => getHostRef(ref).$hostElement$;\nconst createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* EVENT_FLAGS.Bubbles */),\n        composed: !!(flags & 2 /* EVENT_FLAGS.Composed */),\n        cancelable: !!(flags & 1 /* EVENT_FLAGS.Cancellable */),\n        detail\n      });\n    }\n  };\n};\n/**\n * Helper function to create & dispatch a custom Event on a provided target\n * @param elm the target of the Event\n * @param name the name to give the custom Event\n * @param opts options for configuring a custom Event\n * @returns the custom Event\n */\nconst emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\nconst rootAppliedStyles = /*@__PURE__*/new WeakMap();\nconst registerStyle = (scopeId, cssText, allowCS) => {\n  let style = styles.get(scopeId);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === 'string') {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId, style);\n};\nconst addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId = getScopeId(cmpMeta);\n  const style = styles.get(scopeId);\n  // if an element is NOT connected then getRootNode() will return the wrong root node\n  // so the fallback is to always use the document for the root node in those cases\n  styleContainerNode = styleContainerNode.nodeType === 11 /* NODE_TYPE.DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === 'string') {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = new Set());\n      }\n      if (!appliedStyles.has(scopeId)) {\n        {\n          styleElm = doc.createElement('style');\n          styleElm.innerHTML = style;\n          // Apply CSP nonce to the style tag if it exists\n          const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute('nonce', nonce);\n          }\n          styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector('link'));\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId);\n        }\n      }\n    } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId;\n};\nconst attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime('attachStyles', cmpMeta.$tagName$);\n  const scopeId = addStyle(elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta);\n  if (flags & 10 /* CMP_FLAGS.needsScopedEncapsulation */) {\n    // only required when we're NOT using native shadow dom (slot)\n    // or this browser doesn't support native shadow dom\n    // and this host element was NOT created with SSR\n    // let's pick out the inner content for slot projection\n    // create a node to represent where the original\n    // content was first placed, which is useful later on\n    // DOM WRITE!!\n    elm['s-sc'] = scopeId;\n    elm.classList.add(scopeId + '-h');\n  }\n  endAttachStyles();\n};\nconst getScopeId = (cmp, mode) => 'sc-' + cmp.$tagName$;\n/**\n * Production setAccessor() function based on Preact by\n * Jason Miller (@developit)\n * Licensed under the MIT License\n * https://github.com/developit/preact/blob/master/LICENSE\n *\n * Modified for Stencil's compiler and vdom\n */\n/**\n * When running a VDom render set properties present on a VDom node onto the\n * corresponding HTML element.\n *\n * Note that this function has special functionality for the `class`,\n * `style`, `key`, and `ref` attributes, as well as event handlers (like\n * `onClick`, etc). All others are just passed through as-is.\n *\n * @param elm the HTMLElement onto which attributes should be set\n * @param memberName the name of the attribute to set\n * @param oldValue the old value for the attribute\n * @param newValue the new value for the attribute\n * @param isSvg whether we're in an svg context or not\n * @param flags bitflags for Vdom variables\n */\nconst setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (memberName === 'class') {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    } else if (memberName === 'style') {\n      // update style attribute, css properties and values\n      {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (prop.includes('-')) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = '';\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (prop.includes('-')) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (memberName === 'key') ;else if (memberName === 'ref') {\n      // minifier will clean this up\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (!isProp && memberName[0] === 'o' && memberName[1] === 'n') {\n      // Event Handlers\n      // so if the member name starts with \"on\" and the 3rd characters is\n      // a capital letter, and it's not already a member on the element,\n      // then we're assuming it's an event listener\n      if (memberName[2] === '-') {\n        // on- prefixed events\n        // allows to be explicit about the dom event to listen without any magic\n        // under the hood:\n        // <my-cmp on-click> // listens for \"click\"\n        // <my-cmp on-Click> // listens for \"Click\"\n        // <my-cmp on-ionChange> // listens for \"ionChange\"\n        // <my-cmp on-EVENTS> // listens for \"EVENTS\"\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        // standard event\n        // the JSX attribute could have been \"onMouseOver\" and the\n        // member name \"onmouseover\" is on the window's prototype\n        // so let's add the listener \"mouseover\", which is all lowercased\n        memberName = ln.slice(2);\n      } else {\n        // custom event\n        // the JSX attribute could have been \"onMyCustomEvent\"\n        // so let's trim off the \"on\" prefix and lowercase the first character\n        // and add the listener \"myCustomEvent\"\n        // except for the first character, we keep the event name case\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue) {\n        plt.rel(elm, memberName, oldValue, false);\n      }\n      if (newValue) {\n        plt.ael(elm, memberName, newValue, false);\n      }\n    } else {\n      // Set property if it exists and it's not a SVG\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes('-')) {\n            const n = newValue == null ? '' : newValue;\n            // Workaround for Safari, moving the <input> caret when re-assigning the same valued\n            if (memberName === 'list') {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {}\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === '') {\n          {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* VNODE_FLAGS.isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? '' : newValue;\n        {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nconst parseClassListRegex = /\\s/;\nconst parseClassList = value => !value ? [] : value.split(parseClassListRegex);\nconst updateElement = (oldVnode, newVnode, isSvgMode, memberName) => {\n  // if the element passed in is a shadow root, which is a document fragment\n  // then we want to be adding attrs/props to the shadow root's \"host\" element\n  // if it's not a shadow root, then we add attrs/props to the same element\n  const elm = newVnode.$elm$.nodeType === 11 /* NODE_TYPE.DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  {\n    // remove attributes no longer present on the vnode by setting them to undefined\n    for (memberName in oldVnodeAttrs) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], undefined, isSvgMode, newVnode.$flags$);\n      }\n    }\n  }\n  // add new & update changed attributes\n  for (memberName in newVnodeAttrs) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode, newVnode.$flags$);\n  }\n};\n/**\n * Create a DOM Node corresponding to one of the children of a given VNode.\n *\n * @param oldParentVNode the parent VNode from the previous render\n * @param newParentVNode the parent VNode from the current render\n * @param childIndex the index of the VNode, in the _new_ parent node's\n * children, for which we will create a new DOM node\n * @param parentElm the parent DOM node which our new node will be a child of\n * @returns the newly created node\n */\nconst createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  // tslint:disable-next-line: prefer-const\n  const newVNode = newParentVNode.$children$[childIndex];\n  let i = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (!useNativeShadowDom) {\n    // remember for later we need to check to relocate nodes\n    checkSlotRelocate = true;\n    if (newVNode.$tag$ === 'slot') {\n      if (scopeId) {\n        // scoped css needs to add its scoped id to the parent element\n        parentElm.classList.add(scopeId + '-s');\n      }\n      newVNode.$flags$ |= newVNode.$children$ ?\n      // slot element has fallback content\n      2 /* VNODE_FLAGS.isSlotFallback */ :\n      // slot element does not have fallback content\n      1 /* VNODE_FLAGS.isSlotReference */;\n    }\n  }\n  if (newVNode.$text$ !== null) {\n    // create text node\n    elm = newVNode.$elm$ = doc.createTextNode(newVNode.$text$);\n  } else if (newVNode.$flags$ & 1 /* VNODE_FLAGS.isSlotReference */) {\n    // create a slot reference node\n    elm = newVNode.$elm$ = slotReferenceDebugNode(newVNode);\n  } else {\n    if (!isSvgMode) {\n      isSvgMode = newVNode.$tag$ === 'svg';\n    }\n    // create element\n    elm = newVNode.$elm$ = doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, newVNode.$flags$ & 2 /* VNODE_FLAGS.isSlotFallback */ ? 'slot-fb' : newVNode.$tag$);\n    if (isSvgMode && newVNode.$tag$ === 'foreignObject') {\n      isSvgMode = false;\n    }\n    // add css classes, attrs, props, listeners, etc.\n    {\n      updateElement(null, newVNode, isSvgMode);\n    }\n    if (isDef(scopeId) && elm['s-si'] !== scopeId) {\n      // if there is a scopeId and this is the initial render\n      // then let's add the scopeId as a css class\n      elm.classList.add(elm['s-si'] = scopeId);\n    }\n    if (newVNode.$children$) {\n      for (i = 0; i < newVNode.$children$.length; ++i) {\n        // create the node\n        childNode = createElm(oldParentVNode, newVNode, i, elm);\n        // return node could have been null\n        if (childNode) {\n          // append our new node\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    {\n      if (newVNode.$tag$ === 'svg') {\n        // Only reset the SVG context when we're exiting <svg> element\n        isSvgMode = false;\n      } else if (elm.tagName === 'foreignObject') {\n        // Reenter SVG context when we're exiting <foreignObject> element\n        isSvgMode = true;\n      }\n    }\n  }\n  {\n    elm['s-hn'] = hostTagName;\n    if (newVNode.$flags$ & (2 /* VNODE_FLAGS.isSlotFallback */ | 1 /* VNODE_FLAGS.isSlotReference */)) {\n      // remember the content reference comment\n      elm['s-sr'] = true;\n      // remember the content reference comment\n      elm['s-cr'] = contentRef;\n      // remember the slot name, or empty string for default slot\n      elm['s-sn'] = newVNode.$name$ || '';\n      // check if we've got an old vnode for this slot\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode.$tag$ && oldParentVNode.$elm$) {\n        // we've got an old slot vnode and the wrapper is being replaced\n        // so let's move the old slot content back to it's original location\n        putBackInOriginalLocation(oldParentVNode.$elm$, false);\n      }\n    }\n  }\n  return elm;\n};\nconst putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n  const oldSlotChildNodes = parentElm.childNodes;\n  for (let i = oldSlotChildNodes.length - 1; i >= 0; i--) {\n    const childNode = oldSlotChildNodes[i];\n    if (childNode['s-hn'] !== hostTagName && childNode['s-ol']) {\n      // // this child node in the old element is from another component\n      // // remove this node from the old slot's parent\n      // childNode.remove();\n      // and relocate it back to it's original location\n      parentReferenceNode(childNode).insertBefore(childNode, referenceNode(childNode));\n      // remove the old original location comment entirely\n      // later on the patch function will know what to do\n      // and move this to the correct spot if need be\n      childNode['s-ol'].remove();\n      childNode['s-ol'] = undefined;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n};\n/**\n * Create DOM nodes corresponding to a list of {@link d.Vnode} objects and\n * add them to the DOM in the appropriate place.\n *\n * @param parentElm the DOM node which should be used as a parent for the new\n * DOM nodes\n * @param before a child of the `parentElm` which the new children should be\n * inserted before (optional)\n * @param parentVNode the parent virtual DOM node\n * @param vnodes the new child virtual DOM nodes to produce DOM nodes for\n * @param startIdx the index in the child virtual DOM nodes at which to start\n * creating DOM nodes (inclusive)\n * @param endIdx the index in the child virtual DOM nodes at which to stop\n * creating DOM nodes (inclusive)\n */\nconst addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = parentElm['s-cr'] && parentElm['s-cr'].parentNode || parentElm;\n  let childNode;\n  if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        containerElm.insertBefore(childNode, referenceNode(before));\n      }\n    }\n  }\n};\n/**\n * Remove the DOM elements corresponding to a list of {@link d.VNode} objects.\n * This can be used to, for instance, clean up after a list of children which\n * should no longer be shown.\n *\n * This function also handles some of Stencil's slot relocation logic.\n *\n * @param vnodes a list of virtual DOM nodes to remove\n * @param startIdx the index at which to start removing nodes (inclusive)\n * @param endIdx the index at which to stop removing nodes (inclusive)\n */\nconst removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        {\n          // we're removing this element\n          // so it's possible we need to show slot fallback content now\n          checkSlotFallbackVisibility = true;\n          if (elm['s-ol']) {\n            // remove the original location comment\n            elm['s-ol'].remove();\n          } else {\n            // it's possible that child nodes of the node\n            // that's being removed are slot nodes\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        // remove the vnode's element from the dom\n        elm.remove();\n      }\n    }\n  }\n};\n/**\n * Reconcile the children of a new VNode with the children of an old VNode by\n * traversing the two collections of children, identifying nodes that are\n * conserved or changed, calling out to `patch` to make any necessary\n * updates to the DOM, and rearranging DOM nodes as needed.\n *\n * The algorithm for reconciling children works by analyzing two 'windows' onto\n * the two arrays of children (`oldCh` and `newCh`). We keep track of the\n * 'windows' by storing start and end indices and references to the\n * corresponding array entries. Initially the two 'windows' are basically equal\n * to the entire array, but we progressively narrow the windows until there are\n * no children left to update by doing the following:\n *\n * 1. Skip any `null` entries at the beginning or end of the two arrays, so\n *    that if we have an initial array like the following we'll end up dealing\n *    only with a window bounded by the highlighted elements:\n *\n *    [null, null, VNode1 , ... , VNode2, null, null]\n *                 ^^^^^^         ^^^^^^\n *\n * 2. Check to see if the elements at the head and tail positions are equal\n *    across the windows. This will basically detect elements which haven't\n *    been added, removed, or changed position, i.e. if you had the following\n *    VNode elements (represented as HTML):\n *\n *    oldVNode: `<div><p><span>HEY</span></p></div>`\n *    newVNode: `<div><p><span>THERE</span></p></div>`\n *\n *    Then when comparing the children of the `<div>` tag we check the equality\n *    of the VNodes corresponding to the `<p>` tags and, since they are the\n *    same tag in the same position, we'd be able to avoid completely\n *    re-rendering the subtree under them with a new DOM element and would just\n *    call out to `patch` to handle reconciling their children and so on.\n *\n * 3. Check, for both windows, to see if the element at the beginning of the\n *    window corresponds to the element at the end of the other window. This is\n *    a heuristic which will let us identify _some_ situations in which\n *    elements have changed position, for instance it _should_ detect that the\n *    children nodes themselves have not changed but merely moved in the\n *    following example:\n *\n *    oldVNode: `<div><element-one /><element-two /></div>`\n *    newVNode: `<div><element-two /><element-one /></div>`\n *\n *    If we find cases like this then we also need to move the concrete DOM\n *    elements corresponding to the moved children to write the re-order to the\n *    DOM.\n *\n * 4. Finally, if VNodes have the `key` attribute set on them we check for any\n *    nodes in the old children which have the same key as the first element in\n *    our window on the new children. If we find such a node we handle calling\n *    out to `patch`, moving relevant DOM nodes, and so on, in accordance with\n *    what we find.\n *\n * Finally, once we've narrowed our 'windows' to the point that either of them\n * collapse (i.e. they have length 0) we then handle any remaining VNode\n * insertion or deletion that needs to happen to get a DOM state that correctly\n * reflects the new child VNodes. If, for instance, after our window on the old\n * children has collapsed we still have more nodes on the new children that\n * we haven't dealt with yet then we need to add them, or if the new children\n * collapse but we still have unhandled _old_ children then we need to make\n * sure the corresponding DOM nodes are removed.\n *\n * @param parentElm the node into which the parent VNode is rendered\n * @param oldCh the old children of the parent node\n * @param newVNode the new VNode which will replace the parent\n * @param newCh the new children of the parent node\n */\nconst updateChildren = (parentElm, oldCh, newVNode, newCh) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      // VNode might have been moved left\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode)) {\n      // if the start nodes are the same then we should patch the new VNode\n      // onto the old one, and increment our `newStartIdx` and `oldStartIdx`\n      // indices to reflect that. We don't need to move any DOM Nodes around\n      // since things are matched up in order.\n      patch(oldStartVnode, newStartVnode);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode)) {\n      // likewise, if the end nodes are the same we patch new onto old and\n      // decrement our end indices, and also likewise in this case we don't\n      // need to move any DOM Nodes.\n      patch(oldEndVnode, newEndVnode);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode)) {\n      // case: \"Vnode moved right\"\n      //\n      // We've found that the last node in our window on the new children is\n      // the same VNode as the _first_ node in our window on the old children\n      // we're dealing with now. Visually, this is the layout of these two\n      // nodes:\n      //\n      // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n      //                                    ^^^^^^^^^^^\n      // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n      //              ^^^^^^^^^^^^^\n      //\n      // In this situation we need to patch `newEndVnode` onto `oldStartVnode`\n      // and move the DOM element for `oldStartVnode`.\n      if (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot') {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode);\n      // We need to move the element for `oldStartVnode` into a position which\n      // will be appropriate for `newEndVnode`. For this we can use\n      // `.insertBefore` and `oldEndVnode.$elm$.nextSibling`. If there is a\n      // sibling for `oldEndVnode.$elm$` then we want to move the DOM node for\n      // `oldStartVnode` between `oldEndVnode` and it's sibling, like so:\n      //\n      // <old-start-node />\n      // <some-intervening-node />\n      // <old-end-node />\n      // <!-- ->              <-- `oldStartVnode.$elm$` should be inserted here\n      // <next-sibling />\n      //\n      // If instead `oldEndVnode.$elm$` has no sibling then we just want to put\n      // the node for `oldStartVnode` at the end of the children of\n      // `parentElm`. Luckily, `Node.nextSibling` will return `null` if there\n      // aren't any siblings, and passing `null` to `Node.insertBefore` will\n      // append it to the children of the parent element.\n      parentElm.insertBefore(oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode)) {\n      // case: \"Vnode moved left\"\n      //\n      // We've found that the first node in our window on the new children is\n      // the same VNode as the _last_ node in our window on the old children.\n      // Visually, this is the layout of these two nodes:\n      //\n      // newCh: [..., newStartVnode , ... , newEndVnode , ...]\n      //              ^^^^^^^^^^^^^\n      // oldCh: [..., oldStartVnode , ... , oldEndVnode , ...]\n      //                                    ^^^^^^^^^^^\n      //\n      // In this situation we need to patch `newStartVnode` onto `oldEndVnode`\n      // (which will handle updating any changed attributes, reconciling their\n      // children etc) but we also need to move the DOM node to which\n      // `oldEndVnode` corresponds.\n      if (oldStartVnode.$tag$ === 'slot' || newEndVnode.$tag$ === 'slot') {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode);\n      // We've already checked above if `oldStartVnode` and `newStartVnode` are\n      // the same node, so since we're here we know that they are not. Thus we\n      // can move the element for `oldEndVnode` _before_ the element for\n      // `oldStartVnode`, leaving `oldStartVnode` to be reconciled in the\n      // future.\n      parentElm.insertBefore(oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      // Here we do some checks to match up old and new nodes based on the\n      // `$key$` attribute, which is set by putting a `key=\"my-key\"` attribute\n      // in the JSX for a DOM element in the implementation of a Stencil\n      // component.\n      //\n      // First we check to see if there are any nodes in the array of old\n      // children which have the same key as the first node in the new\n      // children.\n      idxInOld = -1;\n      {\n        for (i = oldStartIdx; i <= oldEndIdx; ++i) {\n          if (oldCh[i] && oldCh[i].$key$ !== null && oldCh[i].$key$ === newStartVnode.$key$) {\n            idxInOld = i;\n            break;\n          }\n        }\n      }\n      if (idxInOld >= 0) {\n        // We found a node in the old children which matches up with the first\n        // node in the new children! So let's deal with that\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          // the tag doesn't match so we'll need a new DOM element\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode);\n          // invalidate the matching old node so that we won't try to update it\n          // again later on\n          oldCh[idxInOld] = undefined;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        // We either didn't find an element in the old children that matches\n        // the key of the first new child OR the build is not using `key`\n        // attributes at all. In either case we need to create a new element\n        // for the new node.\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        // if we created a new node then handle inserting it to the DOM\n        {\n          parentReferenceNode(oldStartVnode.$elm$).insertBefore(node, referenceNode(oldStartVnode.$elm$));\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    // we have some more new nodes to add which don't match up with old nodes\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode, newCh, newStartIdx, newEndIdx);\n  } else if (newStartIdx > newEndIdx) {\n    // there are nodes in the `oldCh` array which no longer correspond to nodes\n    // in the new array, so lets remove them (which entails cleaning up the\n    // relevant DOM nodes)\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\n/**\n * Compare two VNodes to determine if they are the same\n *\n * **NB**: This function is an equality _heuristic_ based on the available\n * information set on the two VNodes and can be misleading under certain\n * circumstances. In particular, if the two nodes do not have `key` attrs\n * (available under `$key$` on VNodes) then the function falls back on merely\n * checking that they have the same tag.\n *\n * So, in other words, if `key` attrs are not set on VNodes which may be\n * changing order within a `children` array or something along those lines then\n * we could obtain a false negative and then have to do needless re-rendering\n * (i.e. we'd say two VNodes aren't equal when in fact they should be).\n *\n * @param leftVNode the first VNode to check\n * @param rightVNode the second VNode to check\n * @returns whether they're equal or not\n */\nconst isSameVnode = (leftVNode, rightVNode) => {\n  // compare if two vnode to see if they're \"technically\" the same\n  // need to have the same element tag, and same key to be the same\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (leftVNode.$tag$ === 'slot') {\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    // this will be set if components in the build have `key` attrs set on them\n    {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n  }\n  return false;\n};\nconst referenceNode = node => {\n  // this node was relocated to a new location in the dom\n  // because of some other component's slot\n  // but we still have an html comment in place of where\n  // it's original location was according to it's original vdom\n  return node && node['s-ol'] || node;\n};\nconst parentReferenceNode = node => (node['s-ol'] ? node['s-ol'] : node).parentNode;\n/**\n * Handle reconciling an outdated VNode with a new one which corresponds to\n * it. This function handles flushing updates to the DOM and reconciling the\n * children of the two nodes (if any).\n *\n * @param oldVNode an old VNode whose DOM element and children we want to update\n * @param newVNode a new VNode representing an updated version of the old one\n */\nconst patch = (oldVNode, newVNode) => {\n  const elm = newVNode.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode.$children$;\n  const tag = newVNode.$tag$;\n  const text = newVNode.$text$;\n  let defaultHolder;\n  if (text === null) {\n    {\n      // test if we're rendering an svg element, or still rendering nodes inside of one\n      // only add this to the when the compiler sees we're using an svg somewhere\n      isSvgMode = tag === 'svg' ? true : tag === 'foreignObject' ? false : isSvgMode;\n    }\n    {\n      if (tag === 'slot') ;else {\n        // either this is the first render of an element OR it's an update\n        // AND we already know it's possible it could have changed\n        // this updates the element's css classes, attrs, props, listeners, etc.\n        updateElement(oldVNode, newVNode, isSvgMode);\n      }\n    }\n    if (oldChildren !== null && newChildren !== null) {\n      // looks like there's child vnodes for both the old and new vnodes\n      // so we need to call `updateChildren` to reconcile them\n      updateChildren(elm, oldChildren, newVNode, newChildren);\n    } else if (newChildren !== null) {\n      // no old child vnodes, but there are new child vnodes to add\n      if (oldVNode.$text$ !== null) {\n        // the old vnode was text, so be sure to clear it out\n        elm.textContent = '';\n      }\n      // add the new vnode children\n      addVnodes(elm, null, newVNode, newChildren, 0, newChildren.length - 1);\n    } else if (oldChildren !== null) {\n      // no new child vnodes, but there are old child vnodes to remove\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (isSvgMode && tag === 'svg') {\n      isSvgMode = false;\n    }\n  } else if (defaultHolder = elm['s-cr']) {\n    // this element has slotted content\n    defaultHolder.parentNode.textContent = text;\n  } else if (oldVNode.$text$ !== text) {\n    // update the text content for the text only vnode\n    // and also only if the text is different than before\n    elm.data = text;\n  }\n};\n/**\n * Adjust the `.hidden` property as-needed on any nodes in a DOM subtree which\n * are slot fallbacks nodes.\n *\n * A slot fallback node should be visible by default. Then, it should be\n * conditionally hidden if:\n *\n * - it has a sibling with a `slot` property set to its slot name or if\n * - it is a default fallback slot node, in which case we hide if it has any\n *   content\n *\n * @param elm the element of interest\n */\nconst updateFallbackSlotVisibility = elm => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n      if (childNode['s-sr']) {\n        // this is a slot fallback node\n        // get the slot name for this slot reference node\n        const slotName = childNode['s-sn'];\n        // by default always show a fallback slot node\n        // then hide it if there are other slots in the light dom\n        childNode.hidden = false;\n        // we need to check all of its sibling nodes in order to see if\n        // `childNode` should be hidden\n        for (const siblingNode of childNodes) {\n          if (siblingNode['s-hn'] !== childNode['s-hn'] || slotName !== '') {\n            // this sibling node is from a different component OR is a named\n            // fallback slot node\n            if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ && slotName === siblingNode.getAttribute('slot')) {\n              childNode.hidden = true;\n              break;\n            }\n          } else {\n            // this is a default fallback slot node\n            // any element or text node (with content)\n            // should hide the default fallback slot node\n            if (siblingNode.nodeType === 1 /* NODE_TYPE.ElementNode */ || siblingNode.nodeType === 3 /* NODE_TYPE.TextNode */ && siblingNode.textContent.trim() !== '') {\n              childNode.hidden = true;\n              break;\n            }\n          }\n        }\n      }\n      // keep drilling down\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\n/**\n * Component-global information about nodes which are either currently being\n * relocated or will be shortly.\n */\nconst relocateNodes = [];\n/**\n * Mark the contents of a slot for relocation via adding references to them to\n * the {@link relocateNodes} data structure. The actual work of relocating them\n * will then be handled in {@link renderVdom}.\n *\n * @param elm a render node whose child nodes need to be relocated\n */\nconst markSlotContentForRelocation = elm => {\n  // tslint:disable-next-line: prefer-const\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    // we need to find child nodes which are slot references so we can then try\n    // to match them up with nodes that need to be relocated\n    if (childNode['s-sr'] && (node = childNode['s-cr']) && node.parentNode) {\n      // first get the content reference comment node ('s-cr'), then we get\n      // its parent, which is where all the host content is now\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode['s-sn'];\n      // iterate through all the nodes under the location where the host was\n      // originally rendered\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        // check that the node is not a content reference node or a node\n        // reference and then check that the host name does not match that of\n        // childNode\n        if (!node['s-cn'] && !node['s-nr'] && node['s-hn'] !== childNode['s-hn']) {\n          // if `node` is located in the slot that `childNode` refers to (via the\n          // `'s-sn'` property) then we need to relocate it from it's current spot\n          // (under the host element parent) to the right slot location\n          if (isNodeLocatedInSlot(node, slotName)) {\n            // it's possible we've already decided to relocate this node\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            // made some changes to slots\n            // let's make sure we also double check\n            // fallbacks are correctly hidden or shown\n            checkSlotFallbackVisibility = true;\n            // ensure that the slot-name attr is correct\n            node['s-sn'] = node['s-sn'] || slotName;\n            if (relocateNodeData) {\n              // we marked this node for relocation previously but didn't find\n              // out the slot reference node to which it needs to be relocated\n              // so write it down now!\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              // add to our list of nodes to relocate\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node['s-sr']) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node['s-sn'])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            // the node is not found within the slot (`childNode`) that we're\n            // currently looking at, so we stick it into `relocateNodes` to\n            // handle later. If we never find a home for this element then\n            // we'll need to hide it\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    // if we're dealing with any type of element (capable of itself being a\n    // slot reference or containing one) then we recur\n    if (childNode.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\n/**\n * Check whether a node is located in a given named slot.\n *\n * @param nodeToRelocate the node of interest\n * @param slotName the slot name to check\n * @returns whether the node is located in the slot or not\n */\nconst isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n    if (nodeToRelocate.getAttribute('slot') === null && slotName === '') {\n      // if the node doesn't have a slot attribute, and the slot we're checking\n      // is not a named slot, then we assume the node should be within the slot\n      return true;\n    }\n    if (nodeToRelocate.getAttribute('slot') === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate['s-sn'] === slotName) {\n    return true;\n  }\n  return slotName === '';\n};\n/**\n * 'Nullify' any VDom `ref` callbacks on a VDom node or its children by calling\n * them with `null`. This signals that the DOM element corresponding to the VDom\n * node has been removed from the DOM.\n *\n * @param vNode a virtual DOM node\n */\nconst nullifyVNodeRefs = vNode => {\n  {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\n/**\n * The main entry point for Stencil's virtual DOM-based rendering engine\n *\n * Given a {@link d.HostRef} container and some virtual DOM nodes, this\n * function will handle creating a virtual DOM tree with a single root, patching\n * the current virtual DOM tree onto an old one (if any), dealing with slot\n * relocation, and reflecting attributes.\n *\n * @param hostRef data needed to root and render the virtual DOM tree, such as\n * the DOM node into which it should be rendered.\n * @param renderFnResults the virtual DOM nodes to be rendered\n * @param isInitialLoad whether or not this is the first call after page load\n */\nconst renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  // if `renderFnResults` is a Host node then we can use it directly. If not,\n  // we need to call `h` again to wrap the children of our component in a\n  // 'dummy' Host node (well, an empty vnode) since `renderVdom` assumes\n  // implicitly that the top-level vdom node is 1) an only child and 2)\n  // contains attrs that need to be set on the host element.\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  // On the first render and *only* on the first render we want to check for\n  // any attributes set on the host element which are also set on the vdom\n  // node. If we find them, we override the value on the VDom node attrs with\n  // the value from the host element, which allows developers building apps\n  // with Stencil components to override e.g. the `role` attribute on a\n  // component even if it's already set on the `Host`.\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      // We have a special implementation in `setAccessor` for `style` and\n      // `class` which reconciles values coming from the VDom with values\n      // already present on the DOM element, so we don't want to override those\n      // attributes on the VDom tree with values from the host element if they\n      // are present.\n      //\n      // Likewise, `ref` and `key` are special internal values for the Stencil\n      // runtime and we don't want to override those either.\n      if (hostElm.hasAttribute(key) && !['key', 'ref', 'style', 'class'].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* VNODE_FLAGS.isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm;\n  {\n    scopeId = hostElm['s-sc'];\n  }\n  {\n    contentRef = hostElm['s-cr'];\n    useNativeShadowDom = (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) !== 0;\n    // always reset\n    checkSlotFallbackVisibility = false;\n  }\n  // synchronous patch\n  patch(oldVNode, rootVnode);\n  {\n    // while we're moving nodes around existing nodes, temporarily disable\n    // the disconnectCallback from working\n    plt.$flags$ |= 1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      let relocateData;\n      let nodeToRelocate;\n      let orgLocationNode;\n      let parentNodeRef;\n      let insertBeforeNode;\n      let refNode;\n      let i = 0;\n      for (; i < relocateNodes.length; i++) {\n        relocateData = relocateNodes[i];\n        nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate['s-ol']) {\n          // add a reference node marking this node's original location\n          // keep a reference to this node for later lookups\n          orgLocationNode = originalLocationDebugNode(nodeToRelocate);\n          orgLocationNode['s-nr'] = nodeToRelocate;\n          nodeToRelocate.parentNode.insertBefore(nodeToRelocate['s-ol'] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (i = 0; i < relocateNodes.length; i++) {\n        relocateData = relocateNodes[i];\n        nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (relocateData.$slotRefNode$) {\n          // by default we're just going to insert it directly\n          // after the slot reference node\n          parentNodeRef = relocateData.$slotRefNode$.parentNode;\n          insertBeforeNode = relocateData.$slotRefNode$.nextSibling;\n          orgLocationNode = nodeToRelocate['s-ol'];\n          while (orgLocationNode = orgLocationNode.previousSibling) {\n            refNode = orgLocationNode['s-nr'];\n            if (refNode && refNode['s-sn'] === nodeToRelocate['s-sn'] && parentNodeRef === refNode.parentNode) {\n              refNode = refNode.nextSibling;\n              if (!refNode || !refNode['s-nr']) {\n                insertBeforeNode = refNode;\n                break;\n              }\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            // we've checked that it's worth while to relocate\n            // since that the node to relocate\n            // has a different next sibling or parent relocated\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!nodeToRelocate['s-hn'] && nodeToRelocate['s-ol']) {\n                // probably a component in the index.html that doesn't have its hostname set\n                nodeToRelocate['s-hn'] = nodeToRelocate['s-ol'].parentNode.nodeName;\n              }\n              // add it back to the dom but in its new home\n              parentNodeRef.insertBefore(nodeToRelocate, insertBeforeNode);\n            }\n          }\n        } else {\n          // this node doesn't have a slot home to go to, so let's hide it\n          if (nodeToRelocate.nodeType === 1 /* NODE_TYPE.ElementNode */) {\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    // done moving nodes around\n    // allow the disconnect callback to work again\n    plt.$flags$ &= ~1 /* PLATFORM_FLAGS.isTmpDisconnected */;\n    // always reset\n    relocateNodes.length = 0;\n  }\n};\n// slot comment debug nodes only created with the `--debug` flag\n// otherwise these nodes are text nodes w/out content\nconst slotReferenceDebugNode = slotVNode => doc.createComment(`<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : ''}> (host=${hostTagName.toLowerCase()})`);\nconst originalLocationDebugNode = nodeToRelocate => doc.createComment(`org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate['s-hn']})` : `[${nodeToRelocate.textContent}]`));\nconst attachToAncestor = (hostRef, ancestorComponent) => {\n  if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent['s-p']) {\n    ancestorComponent['s-p'].push(new Promise(r => hostRef.$onRenderResolve$ = r));\n  }\n};\nconst scheduleUpdate = (hostRef, isInitialLoad) => {\n  {\n    hostRef.$flags$ |= 16 /* HOST_FLAGS.isQueuedForUpdate */;\n  }\n  if (hostRef.$flags$ & 4 /* HOST_FLAGS.isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* HOST_FLAGS.needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  // there is no ancestor component or the ancestor component\n  // has already fired off its lifecycle update then\n  // fire off the initial update\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return writeTask(dispatch);\n};\n/**\n * Dispatch initial-render and update lifecycle hooks, enqueuing calls to\n * component lifecycle methods like `componentWillLoad` as well as\n * {@link updateComponent}, which will kick off the virtual DOM re-render.\n *\n * @param hostRef a reference to a host DOM node\n * @param isInitialLoad whether we're on the initial load or not\n * @returns an empty Promise which is used to enqueue a series of operations for\n * the component\n */\nconst dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime('scheduleUpdate', hostRef.$cmpMeta$.$tagName$);\n  const instance = hostRef.$lazyInstance$;\n  // We're going to use this variable together with `enqueue` to implement a\n  // little promise-based queue. We start out with it `undefined`. When we add\n  // the first function to the queue we'll set this variable to be that\n  // function's return value. When we attempt to add subsequent values to the\n  // queue we'll check that value and, if it was a `Promise`, we'll then chain\n  // the new function off of that `Promise` using `.then()`. This will give our\n  // queue two nice properties:\n  //\n  // 1. If all functions added to the queue are synchronous they'll be called\n  //    synchronously right away.\n  // 2. If all functions added to the queue are asynchronous they'll all be\n  //    called in order after `dispatchHooks` exits.\n  let maybePromise;\n  if (isInitialLoad) {\n    {\n      hostRef.$flags$ |= 256 /* HOST_FLAGS.isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = undefined;\n      }\n    }\n    emitLifecycleEvent(elm, 'componentWillLoad');\n    {\n      // If `componentWillLoad` returns a `Promise` then we want to wait on\n      // whatever's going on in that `Promise` before we launch into\n      // rendering the component, doing other lifecycle stuff, etc. So\n      // in that case we assign the returned promise to the variable we\n      // declared above to hold a possible 'queueing' Promise\n      maybePromise = safeCall(instance, 'componentWillLoad');\n    }\n  } else {\n    emitLifecycleEvent(elm, 'componentWillUpdate');\n  }\n  emitLifecycleEvent(elm, 'componentWillRender');\n  {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, 'componentWillRender'));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\n/**\n * This function uses a Promise to implement a simple first-in, first-out queue\n * of functions to be called.\n *\n * The queue is ordered on the basis of the first argument. If it's\n * `undefined`, then nothing is on the queue yet, so the provided function can\n * be called synchronously (although note that this function may return a\n * `Promise`). The idea is that then the return value of that enqueueing\n * operation is kept around, so that if it was a `Promise` then subsequent\n * functions can be enqueued by calling this function again with that `Promise`\n * as the first argument.\n *\n * @param maybePromise either a `Promise` which should resolve before the next function is called or an 'empty' sentinel\n * @param fn a function to enqueue\n * @returns either a `Promise` or the return value of the provided function\n */\nconst enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn) : fn();\n/**\n * Check that a value is a `Promise`. To check, we first see if the value is an\n * instance of the `Promise` global. In a few circumstances, in particular if\n * the global has been overwritten, this is could be misleading, so we also do\n * a little 'duck typing' check to see if the `.then` property of the value is\n * defined and a function.\n *\n * @param maybePromise it might be a promise!\n * @returns whether it is or not\n */\nconst isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === 'function';\n/**\n * Update a component given reference to its host elements and so on.\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param isInitialLoad whether or not this function is being called as part of\n * the first render cycle\n */\nconst updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime('update', hostRef.$cmpMeta$.$tagName$);\n  const rc = elm['s-rc'];\n  if (isInitialLoad) {\n    // DOM WRITE!\n    attachStyles(hostRef);\n  }\n  const endRender = createTime('render', hostRef.$cmpMeta$.$tagName$);\n  {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (rc) {\n    // ok, so turns out there are some child host elements\n    // waiting on this parent element to load\n    // let's fire off all update callbacks waiting\n    rc.map(cb => cb());\n    elm['s-rc'] = undefined;\n  }\n  endRender();\n  endUpdate();\n  {\n    const childrenPromises = (_a = elm['s-p']) !== null && _a !== void 0 ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* HOST_FLAGS.isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  }\n};\n/**\n * Handle making the call to the VDom renderer with the proper context given\n * various build variables\n *\n * @param hostRef an object containing references to the element's host node,\n * VDom nodes, and other metadata\n * @param instance a reference to the underlying host element where it will be\n * rendered\n * @param elm the Host element for the component\n * @param isInitialLoad whether or not this function is being called as part of\n * @returns an empty promise\n */\nconst callRender = (hostRef, instance, elm, isInitialLoad) => {\n  try {\n    renderingRef = instance;\n    /**\n     * minification optimization: `allRenderFn` is `true` if all components have a `render`\n     * method, so we can call the method immediately. If not, check before calling it.\n     */\n    instance = instance.render();\n    {\n      hostRef.$flags$ &= ~16 /* HOST_FLAGS.isQueuedForUpdate */;\n    }\n    {\n      hostRef.$flags$ |= 2 /* HOST_FLAGS.hasRendered */;\n    }\n    {\n      {\n        // looks like we've got child nodes to render into this host element\n        // or we need to update the css class/attrs on the host element\n        // DOM WRITE!\n        {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nconst getRenderingRef = () => renderingRef;\nconst postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime('postUpdate', tagName);\n  const instance = hostRef.$lazyInstance$;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  {\n    safeCall(instance, 'componentDidRender');\n  }\n  emitLifecycleEvent(elm, 'componentDidRender');\n  if (!(hostRef.$flags$ & 64 /* HOST_FLAGS.hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* HOST_FLAGS.hasLoadedComponent */;\n    {\n      // DOM WRITE!\n      addHydratedFlag(elm);\n    }\n    {\n      safeCall(instance, 'componentDidLoad');\n    }\n    emitLifecycleEvent(elm, 'componentDidLoad');\n    endPostUpdate();\n    {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad();\n      }\n    }\n  } else {\n    emitLifecycleEvent(elm, 'componentDidUpdate');\n    endPostUpdate();\n  }\n  {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  // load events fire from bottom to top\n  // the deepest elements load first then bubbles up\n  {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = undefined;\n    }\n    if (hostRef.$flags$ & 512 /* HOST_FLAGS.needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* HOST_FLAGS.isWaitingForChildren */ | 512 /* HOST_FLAGS.needsRerender */);\n  }\n  // ( •_•)\n  // ( •_•)>⌐■-■\n  // (⌐■_■)\n};\nconst forceUpdate = ref => {\n  {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    // Returns \"true\" when the forced update was successfully scheduled\n    return isConnected;\n  }\n};\nconst appDidLoad = who => {\n  // on appload\n  // we have finish the first big initial render\n  {\n    addHydratedFlag(doc.documentElement);\n  }\n  nextTick(() => emitEvent(win, 'appload', {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n};\n/**\n * Allows to safely call a method, e.g. `componentDidLoad`, on an instance,\n * e.g. custom element node. If a build figures out that e.g. no component\n * has a `componentDidLoad` method, the instance method gets removed from the\n * output bundle and this function returns `undefined`.\n * @param instance any object that may or may not contain methods\n * @param method method name\n * @param arg single arbitrary argument\n * @returns result of method call if it exists, otherwise `undefined`\n */\nconst safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return undefined;\n};\n/**\n * For debugging purposes as `BUILD.lifecycleDOMEvents` is `false` by default and will\n * get removed by the compiler. Used for timing events to see how long they take.\n * @param elm the target of the Event\n * @param lifecycleName name of the event\n */\nconst emitLifecycleEvent = (elm, lifecycleName) => {\n  {\n    emitEvent(elm, 'stencil_' + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nconst addHydratedFlag = elm => elm.classList.add('hydrated');\nconst getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nconst setValue = (ref, propName, newVal, cmpMeta) => {\n  // check our new property value against our internal value\n  const hostRef = getHostRef(ref);\n  const elm = hostRef.$hostElement$;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = hostRef.$lazyInstance$;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  // explicitly check for NaN on both sides, as `NaN === NaN` is always false\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!(flags & 8 /* HOST_FLAGS.isConstructingInstance */) || oldVal === undefined) && didValueChange) {\n    // gadzooks! the property's value has changed!!\n    // set our new value!\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (instance) {\n      // get an array of method names of watch functions to call\n      if (cmpMeta.$watchers$ && flags & 128 /* HOST_FLAGS.isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          // this instance is watching for when this property changed\n          watchMethods.map(watchMethodName => {\n            try {\n              // fire off each of the watch methods that are watching this property\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if ((flags & (2 /* HOST_FLAGS.hasRendered */ | 16 /* HOST_FLAGS.isQueuedForUpdate */)) === 2 /* HOST_FLAGS.hasRendered */) {\n        // looks like this value actually changed, so we've got work to do!\n        // but only if we've already rendered, otherwise just chill out\n        // queue that we need to do an update, but don't worry about queuing\n        // up millions cuz this function ensures it only runs once\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n/**\n * Attach a series of runtime constructs to a compiled Stencil component\n * constructor, including getters and setters for the `@Prop` and `@State`\n * decorators, callbacks for when attributes change, and so on.\n *\n * @param Cstr the constructor for a component that we need to process\n * @param cmpMeta metadata collected previously about the component\n * @param flags a number used to store a series of bit flags\n * @returns a reference to the same constructor passed in (but now mutated)\n */\nconst proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a;\n  if (cmpMeta.$members$) {\n    if (Cstr.watchers) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    // It's better to have a const than two Object.entries()\n    const members = Object.entries(cmpMeta.$members$);\n    const prototype = Cstr.prototype;\n    members.map(([memberName, [memberFlags]]) => {\n      if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ || flags & 2 /* PROXY_FLAGS.proxyState */ && memberFlags & 32 /* MEMBER_FLAGS.State */) {\n        // proxyComponent - prop\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            // proxyComponent, get value\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            // proxyComponent, set value\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (flags & 1 /* PROXY_FLAGS.isElementConstructor */ && memberFlags & 64 /* MEMBER_FLAGS.Method */) {\n        // proxyComponent - method\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            const ref = getHostRef(this);\n            return ref.$onInstancePromise$.then(() => ref.$lazyInstance$[memberName](...args));\n          }\n        });\n      }\n    });\n    if (flags & 1 /* PROXY_FLAGS.isElementConstructor */) {\n      const attrNameToPropName = new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          const propName = attrNameToPropName.get(attrName);\n          //  In a web component lifecycle the attributeChangedCallback runs prior to connectedCallback\n          //  in the case where an attribute was set inline.\n          //  ```html\n          //    <my-component some-attribute=\"some-value\"></my-component>\n          //  ```\n          //\n          //  There is an edge case where a developer sets the attribute inline on a custom element and then\n          //  programmatically changes it before it has been upgraded as shown below:\n          //\n          //  ```html\n          //    <!-- this component has _not_ been upgraded yet -->\n          //    <my-component id=\"test\" some-attribute=\"some-value\"></my-component>\n          //    <script>\n          //      // grab non-upgraded component\n          //      el = document.querySelector(\"#test\");\n          //      el.someAttribute = \"another-value\";\n          //      // upgrade component\n          //      customElements.define('my-component', MyComponent);\n          //    </script>\n          //  ```\n          //  In this case if we do not un-shadow here and use the value of the shadowing property, attributeChangedCallback\n          //  will be called with `newValue = \"some-value\"` and will set the shadowed property (this.someAttribute = \"another-value\")\n          //  to the value that was set inline i.e. \"some-value\" from above example. When\n          //  the connectedCallback attempts to un-shadow it will use \"some-value\" as the initial value rather than \"another-value\"\n          //\n          //  The case where the attribute was NOT set inline but was not set programmatically shall be handled/un-shadowed\n          //  by connectedCallback as this attributeChangedCallback will not fire.\n          //\n          //  https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n          //\n          //  TODO(STENCIL-16) we should think about whether or not we actually want to be reflecting the attributes to\n          //  properties here given that this goes against best practices outlined here\n          //  https://developers.google.com/web/fundamentals/web-components/best-practices#avoid-reentrancy\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === 'number' && this[propName] == newValue) {\n            // if the propName exists on the prototype of `Cstr`, this update may be a result of Stencil using native\n            // APIs to reflect props as attributes. Calls to `setAttribute(someElement, propName)` will result in\n            // `propName` to be converted to a `DOMString`, which may not be what we want for other primitive props.\n            return;\n          } else if (propName == null) {\n            // At this point we should know this is not a \"member\", so we can treat it like watching an attribute\n            // on a vanilla web component\n            const hostRef = getHostRef(this);\n            const flags = hostRef === null || hostRef === void 0 ? void 0 : hostRef.$flags$;\n            // We only want to trigger the callback(s) if:\n            // 1. The instance is ready\n            // 2. The watchers are ready\n            // 3. The value has changed\n            if (!(flags & 8 /* HOST_FLAGS.isConstructingInstance */) && flags & 128 /* HOST_FLAGS.isWatchReady */ && newValue !== oldValue) {\n              const instance = hostRef.$lazyInstance$;\n              const entry = cmpMeta.$watchers$[attrName];\n              entry === null || entry === void 0 ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === 'boolean' ? false : newValue;\n        });\n      };\n      // Create an array of attributes to observe\n      // This list in comprised of all strings used within a `@Watch()` decorator\n      // on a component as well as any Stencil-specific \"members\" (`@Prop()`s and `@State()`s).\n      // As such, there is no way to guarantee type-safety here that a user hasn't entered\n      // an invalid attribute.\n      Cstr.observedAttributes = Array.from(new Set([...Object.keys((_a = cmpMeta.$watchers$) !== null && _a !== void 0 ? _a : {}), ...members.filter(([_, m]) => m[0] & 15 /* MEMBER_FLAGS.HasAttribute */).map(([propName, m]) => {\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (m[0] & 512 /* MEMBER_FLAGS.ReflectAttr */) {\n          cmpMeta.$attrsToReflect$.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n/**\n * Initialize a Stencil component given a reference to its host element, its\n * runtime bookkeeping data structure, runtime metadata about the component,\n * and (optionally) an HMR version ID.\n *\n * @param elm a host element\n * @param hostRef the element's runtime bookkeeping object\n * @param cmpMeta runtime metadata for the Stencil component\n * @param hmrVersionId an (optional) HMR version ID\n */\nconst initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  // initializeComponent\n  if ((hostRef.$flags$ & 32 /* HOST_FLAGS.hasInitializedComponent */) === 0) {\n    // Let the runtime know that the component has been initialized\n    hostRef.$flags$ |= 32 /* HOST_FLAGS.hasInitializedComponent */;\n    {\n      // lazy loaded components\n      // request the component's implementation to be\n      // wired up with the host element\n      Cstr = loadModule(cmpMeta);\n      if (Cstr.then) {\n        // Await creates a micro-task avoid if possible\n        const endLoad = uniqueTime();\n        Cstr = await Cstr;\n        endLoad();\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (!Cstr.isProxied) {\n        // we've never proxied this Constructor before\n        // let's add the getters/setters to its prototype before\n        // the first time we create an instance of the implementation\n        {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* PROXY_FLAGS.proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime('createInstance', cmpMeta.$tagName$);\n      // ok, time to construct the instance\n      // but let's keep track of when we start and stop\n      // so that the getters/setters don't incorrectly step on data\n      {\n        hostRef.$flags$ |= 8 /* HOST_FLAGS.isConstructingInstance */;\n      }\n      // construct the lazy-loaded component implementation\n      // passing the hostRef is very important during\n      // construction in order to directly wire together the\n      // host element and the lazy-loaded instance\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e);\n      }\n      {\n        hostRef.$flags$ &= ~8 /* HOST_FLAGS.isConstructingInstance */;\n      }\n      {\n        hostRef.$flags$ |= 128 /* HOST_FLAGS.isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$);\n    }\n    if (Cstr.style) {\n      // this component has styles but we haven't registered them yet\n      let style = Cstr.style;\n      const scopeId = getScopeId(cmpMeta);\n      if (!styles.has(scopeId)) {\n        const endRegisterStyles = createTime('registerStyles', cmpMeta.$tagName$);\n        registerStyle(scopeId, style, !!(cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  // we've successfully created a lazy instance\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (ancestorComponent && ancestorComponent['s-rc']) {\n    // this is the initial load and this component it has an ancestor component\n    // but the ancestor component has NOT fired its will update lifecycle yet\n    // so let's just cool our jets and wait for the ancestor to continue first\n    // this will get fired off when the ancestor component\n    // finally gets around to rendering its lazy self\n    // fire off the initial update\n    ancestorComponent['s-rc'].push(schedule);\n  } else {\n    schedule();\n  }\n};\nconst fireConnectedCallback = instance => {\n  {\n    safeCall(instance, 'connectedCallback');\n  }\n};\nconst connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime('connectedCallback', cmpMeta.$tagName$);\n    if (!(hostRef.$flags$ & 1 /* HOST_FLAGS.hasConnected */)) {\n      // first time this component has connected\n      hostRef.$flags$ |= 1 /* HOST_FLAGS.hasConnected */;\n      {\n        // initUpdate\n        // if the slot polyfill is required we'll need to put some nodes\n        // in here to act as original content anchors as we move nodes around\n        // host element has been connected to the DOM\n        if (\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* CMP_FLAGS.hasSlotRelocation */ | 8 /* CMP_FLAGS.needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      {\n        // find the first ancestor component (if there is one) and register\n        // this component as one of the actively loading child components for its ancestor\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          // climb up the ancestors looking for the first\n          // component that hasn't finished its lifecycle update yet\n          if (ancestorComponent['s-p']) {\n            // we found this components first ancestor component\n            // keep a reference to this component's ancestor component\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      // Lazy properties\n      // https://developers.google.com/web/fundamentals/web-components/best-practices#lazy-properties\n      if (cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* MEMBER_FLAGS.Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      // not the first time this has connected\n      // reattach any event listeners to the host\n      // since they would have been removed when disconnected\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n      // fire off connectedCallback() on component instance\n      if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nconst setContentReference = elm => {\n  // only required when we're NOT using native shadow dom (slot)\n  // or this browser doesn't support native shadow dom\n  // and this host element was NOT created with SSR\n  // let's pick out the inner content for slot projection\n  // create a node to represent where the original\n  // content was first placed, which is useful later on\n  const contentRefElm = elm['s-cr'] = doc.createComment(`content-ref (host=${elm.localName})`);\n  contentRefElm['s-cn'] = true;\n  elm.insertBefore(contentRefElm, elm.firstChild);\n};\nconst disconnectInstance = instance => {\n  {\n    safeCall(instance, 'disconnectedCallback');\n  }\n};\nconst disconnectedCallback = async elm => {\n  if ((plt.$flags$ & 1 /* PLATFORM_FLAGS.isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map(rmListener => rmListener());\n        hostRef.$rmListeners$ = undefined;\n      }\n    }\n    if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$);\n    } else if (hostRef === null || hostRef === void 0 ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n    }\n  }\n};\nconst bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  const endBootstrap = createTime();\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements = win.customElements;\n  const head = doc.head;\n  const metaCharset = /*@__PURE__*/head.querySelector('meta[charset]');\n  const visibilityStyle = /*@__PURE__*/doc.createElement('style');\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || './', doc.baseURI).href;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      {\n        cmpMeta.$watchers$ = (_a = compactMeta[4]) !== null && _a !== void 0 ? _a : {};\n      }\n      const tagName = cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          // @ts-ignore\n          super(self);\n          self = this;\n          registerHost(self, cmpMeta);\n          if (cmpMeta.$flags$ & 1 /* CMP_FLAGS.shadowDomEncapsulation */) {\n            // this component is using shadow dom\n            // and this browser supports shadow dom\n            // add the read-only property \"shadowRoot\" to the host element\n            // adding the shadow root build conditionals to minimize runtime\n            {\n              {\n                self.attachShadow({\n                  mode: 'open'\n                });\n              }\n            }\n          }\n        }\n        connectedCallback() {\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            // connectedCallback will be processed once all components have been registered\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* PROXY_FLAGS.isElementConstructor */));\n      }\n    });\n  });\n  {\n    visibilityStyle.innerHTML = cmpTags + HYDRATED_CSS;\n    visibilityStyle.setAttribute('data-styles', '');\n    // Apply CSP nonce to the style tag if it exists\n    const nonce = (_a = plt.$nonce$) !== null && _a !== void 0 ? _a : queryNonceMetaTagContent(doc);\n    if (nonce != null) {\n      visibilityStyle.setAttribute('nonce', nonce);\n    }\n    head.insertBefore(visibilityStyle, metaCharset ? metaCharset.nextSibling : head.firstChild);\n  }\n  // Process deferred connectedCallbacks now all components have been registered\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  // Fallback appLoad event\n  endBootstrap();\n};\nconst addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (listeners) {\n    listeners.map(([flags, name, method]) => {\n      const target = getHostListenerTarget(elm, flags);\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nconst hostListenerProxy = (hostRef, methodName) => ev => {\n  try {\n    {\n      if (hostRef.$flags$ & 256 /* HOST_FLAGS.isListenReady */) {\n        // instance is ready, let's call it's member method for this event\n        hostRef.$lazyInstance$[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nconst getHostListenerTarget = (elm, flags) => {\n  if (flags & 4 /* LISTENER_FLAGS.TargetDocument */) return doc;\n  if (flags & 8 /* LISTENER_FLAGS.TargetWindow */) return win;\n  if (flags & 16 /* LISTENER_FLAGS.TargetBody */) return doc.body;\n  return elm;\n};\n// prettier-ignore\nconst hostListenerOpts = flags => (flags & 2 /* LISTENER_FLAGS.Capture */) !== 0;\n/**\n * Assigns the given value to the nonce property on the runtime platform object.\n * During runtime, this value is used to set the nonce attribute on all dynamically created script and style tags.\n * @param nonce The value to be assigned to the platform nonce property.\n * @returns void\n */\nconst setNonce = nonce => plt.$nonce$ = nonce;\n/**\n * A WeakMap mapping runtime component references to their corresponding host reference\n * instances.\n */\nconst hostRefs = /*@__PURE__*/new WeakMap();\n/**\n * Given a {@link d.RuntimeRef} retrieve the corresponding {@link d.HostRef}\n *\n * @param ref the runtime ref of interest\n * @returns the Host reference (if found) or undefined\n */\nconst getHostRef = ref => hostRefs.get(ref);\n/**\n * Register a lazy instance with the {@link hostRefs} object so it's\n * corresponding {@link d.HostRef} can be retrieved later.\n *\n * @param lazyInstance the lazy instance of interest\n * @param hostRef that instances `HostRef` object\n * @returns a reference to the host ref WeakMap\n */\nconst registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\n/**\n * Register a host element for a Stencil component, setting up various metadata\n * and callbacks based on {@link BUILD} flags as well as the component's runtime\n * metadata.\n *\n * @param hostElement the host element to register\n * @param cmpMeta runtime metadata for that component\n * @returns a reference to the host ref WeakMap\n */\nconst registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: new Map()\n  };\n  {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement['s-p'] = [];\n    hostElement['s-rc'] = [];\n  }\n  addHostEventListeners(hostElement, hostRef, cmpMeta.$listeners$);\n  return hostRefs.set(hostElement, hostRef);\n};\nconst isMemberInElement = (elm, memberName) => memberName in elm;\nconst consoleError = (e, el) => (0, console.error)(e, el);\nconst cmpModules = /*@__PURE__*/new Map();\nconst loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  // loadModuleImport\n  const exportName = cmpMeta.$tagName$.replace(/-/g, '_');\n  const bundleId = cmpMeta.$lazyBundleId$;\n  const module = cmpModules.get(bundleId);\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import( /* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${''}`).then(importedModule => {\n    {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\nconst styles = /*@__PURE__*/new Map();\nconst win = typeof window !== 'undefined' ? window : {};\nconst doc = win.document || {\n  head: {}\n};\nconst plt = {\n  $flags$: 0,\n  $resourcesUrl$: '',\n  jmp: h => h(),\n  raf: h => requestAnimationFrame(h),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nconst promiseResolve = v => Promise.resolve(v);\nconst supportsConstructableStylesheets = /*@__PURE__*/(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === 'function';\n  } catch (e) {}\n  return false;\n})();\nconst queueDomReads = [];\nconst queueDomWrites = [];\nconst queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* PLATFORM_FLAGS.queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nconst consume = queue => {\n  for (let i = 0; i < queue.length; i++) {\n    try {\n      queue[i](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nconst flush = () => {\n  // always force a bunch of medium callbacks to run, but still have\n  // a throttle on how many can run in a certain time\n  // DOM READS!!!\n  consume(queueDomReads);\n  // DOM WRITES!!!\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      // still more to do yet, but we've run out of time\n      // let's let this thing cool off and try again in the next tick\n      plt.raf(flush);\n    }\n  }\n};\nconst nextTick = /*@__PURE__*/cb => promiseResolve().then(cb);\nconst writeTask = /*@__PURE__*/queueTask(queueDomWrites, true);\nexport { Host as H, getElement as a, bootstrapLazy as b, createEvent as c, forceUpdate as f, getRenderingRef as g, h, promiseResolve as p, registerInstance as r, setNonce as s };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,YAAY;AAUlB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,qBAAqB;AACzB,IAAI,8BAA8B;AAClC,IAAI,oBAAoB;AACxB,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAM,aAAa,CAAC,QAAQ,UAAU,OAAO;AAC3C;AACE,WAAO,MAAM;AACX;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,aAAa,CAAC,KAAK,gBAAgB;AACvC;AACE,WAAO,MAAM;AACX;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,eAAe;AAQrB,IAAM,YAAY,CAAC;AAInB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,QAAQ,OAAK,KAAK;AAQxB,IAAM,gBAAgB,OAAK;AAEzB,MAAI,OAAO;AACX,SAAO,MAAM,YAAY,MAAM;AACjC;AASA,SAAS,yBAAyBA,MAAK;AACrC,MAAI,IAAI,IAAI;AACZ,UAAQ,MAAM,MAAM,KAAKA,KAAI,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,wBAAwB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa,SAAS,OAAO,QAAQ,OAAO,SAAS,KAAK;AACxN;AAWA,IAAM,IAAI,CAAC,UAAU,cAAc,aAAa;AAC9C,MAAI,QAAQ;AACZ,MAAI,MAAM;AACV,MAAI,WAAW;AACf,MAAI,SAAS;AACb,MAAI,aAAa;AACjB,QAAM,gBAAgB,CAAC;AACvB,QAAM,OAAO,OAAK;AAChB,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,cAAQ,EAAE,CAAC;AACX,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAK,KAAK;AAAA,MACZ,WAAW,SAAS,QAAQ,OAAO,UAAU,WAAW;AACtD,YAAI,SAAS,OAAO,aAAa,cAAc,CAAC,cAAc,KAAK,GAAG;AACpE,kBAAQ,OAAO,KAAK;AAAA,QACtB;AACA,YAAI,UAAU,YAAY;AAExB,wBAAc,cAAc,SAAS,CAAC,EAAE,UAAU;AAAA,QACpD,OAAO;AAEL,wBAAc,KAAK,SAAS,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,QAC3D;AACA,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACA,OAAK,QAAQ;AACb,MAAI,WAAW;AACb,QAAI,UAAU,KAAK;AACjB,YAAM,UAAU;AAAA,IAClB;AACA,QAAI,UAAU,MAAM;AAClB,iBAAW,UAAU;AAAA,IACvB;AAEA;AACE,YAAM,YAAY,UAAU,aAAa,UAAU;AACnD,UAAI,WAAW;AACb,kBAAU,QAAQ,OAAO,cAAc,WAAW,YAAY,OAAO,KAAK,SAAS,EAAE,OAAO,OAAK,UAAU,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,MACzH;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,aAAa,YAAY;AAElC,WAAO,SAAS,cAAc,OAAO,CAAC,IAAI,WAAW,eAAe,WAAW;AAAA,EACjF;AACA,QAAM,QAAQ,SAAS,UAAU,IAAI;AACrC,QAAM,UAAU;AAChB,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,aAAa;AAAA,EACrB;AACA;AACE,UAAM,QAAQ;AAAA,EAChB;AACA;AACE,UAAM,SAAS;AAAA,EACjB;AACA,SAAO;AACT;AASA,IAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,QAAM,QAAQ;AAAA,IACZ,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACA;AACE,UAAM,UAAU;AAAA,EAClB;AACA;AACE,UAAM,QAAQ;AAAA,EAChB;AACA;AACE,UAAM,SAAS;AAAA,EACjB;AACA,SAAO;AACT;AACA,IAAM,OAAO,CAAC;AAOd,IAAM,SAAS,UAAQ,QAAQ,KAAK,UAAU;AAQ9C,IAAM,cAAc;AAAA,EAClB,SAAS,CAAC,UAAU,OAAO,SAAS,IAAI,eAAe,EAAE,QAAQ,EAAE;AAAA,EACnE,KAAK,CAAC,UAAU,OAAO,SAAS,IAAI,eAAe,EAAE,IAAI,EAAE,EAAE,IAAI,gBAAgB;AACnF;AAQA,IAAM,kBAAkB,WAAS;AAAA,EAC/B,QAAQ,KAAK;AAAA,EACb,WAAW,KAAK;AAAA,EAChB,MAAM,KAAK;AAAA,EACX,OAAO,KAAK;AAAA,EACZ,MAAM,KAAK;AAAA,EACX,OAAO,KAAK;AACd;AAUA,IAAM,mBAAmB,UAAQ;AAC/B,MAAI,OAAO,KAAK,SAAS,YAAY;AACnC,UAAM,YAAY,OAAO,OAAO,CAAC,GAAG,KAAK,MAAM;AAC/C,QAAI,KAAK,MAAM;AACb,gBAAU,MAAM,KAAK;AAAA,IACvB;AACA,QAAI,KAAK,OAAO;AACd,gBAAU,OAAO,KAAK;AAAA,IACxB;AACA,WAAO,EAAE,KAAK,MAAM,WAAW,GAAI,KAAK,aAAa,CAAC,CAAE;AAAA,EAC1D;AACA,QAAM,QAAQ,SAAS,KAAK,MAAM,KAAK,KAAK;AAC5C,QAAM,UAAU,KAAK;AACrB,QAAM,aAAa,KAAK;AACxB,QAAM,QAAQ,KAAK;AACnB,QAAM,SAAS,KAAK;AACpB,SAAO;AACT;AAwBA,IAAM,qBAAqB,CAAC,WAAW,aAAa;AAElD,MAAI,aAAa,QAAQ,CAAC,cAAc,SAAS,GAAG;AAClD,QAAI,WAAW,GAA8B;AAG3C,aAAO,cAAc,UAAU,QAAQ,cAAc,MAAM,CAAC,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,GAA6B;AAE1C,aAAO,WAAW,SAAS;AAAA,IAC7B;AACA,QAAI,WAAW,GAA6B;AAG1C,aAAO,OAAO,SAAS;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AACA,IAAM,aAAa,SAAO,WAAW,GAAG,EAAE;AAC1C,IAAM,cAAc,CAAC,KAAK,MAAM,UAAU;AACxC,QAAM,MAAM,WAAW,GAAG;AAC1B,SAAO;AAAA,IACL,MAAM,YAAU;AACd,aAAO,UAAU,KAAK,MAAM;AAAA,QAC1B,SAAS,CAAC,EAAE,QAAQ;AAAA,QACpB,UAAU,CAAC,EAAE,QAAQ;AAAA,QACrB,YAAY,CAAC,EAAE,QAAQ;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAQA,IAAM,YAAY,CAAC,KAAK,MAAM,SAAS;AACrC,QAAM,KAAK,IAAI,GAAG,MAAM,IAAI;AAC5B,MAAI,cAAc,EAAE;AACpB,SAAO;AACT;AACA,IAAM,oBAAiC,oBAAI,QAAQ;AACnD,IAAM,gBAAgB,CAACC,UAAS,SAAS,YAAY;AACnD,MAAI,QAAQ,OAAO,IAAIA,QAAO;AAC9B,MAAI,oCAAoC,SAAS;AAC/C,YAAQ,SAAS,IAAI,cAAc;AACnC,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ;AAAA,IACV,OAAO;AACL,YAAM,YAAY,OAAO;AAAA,IAC3B;AAAA,EACF,OAAO;AACL,YAAQ;AAAA,EACV;AACA,SAAO,IAAIA,UAAS,KAAK;AAC3B;AACA,IAAM,WAAW,CAAC,oBAAoB,SAAS,SAAS;AACtD,MAAI;AACJ,QAAMA,WAAU,WAAW,OAAO;AAClC,QAAM,QAAQ,OAAO,IAAIA,QAAO;AAGhC,uBAAqB,mBAAmB,aAAa,KAAsC,qBAAqB;AAChH,MAAI,OAAO;AACT,QAAI,OAAO,UAAU,UAAU;AAC7B,2BAAqB,mBAAmB,QAAQ;AAChD,UAAI,gBAAgB,kBAAkB,IAAI,kBAAkB;AAC5D,UAAI;AACJ,UAAI,CAAC,eAAe;AAClB,0BAAkB,IAAI,oBAAoB,gBAAgB,oBAAI,IAAI,CAAC;AAAA,MACrE;AACA,UAAI,CAAC,cAAc,IAAIA,QAAO,GAAG;AAC/B;AACE,qBAAW,IAAI,cAAc,OAAO;AACpC,mBAAS,YAAY;AAErB,gBAAM,SAAS,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK,yBAAyB,GAAG;AAC9F,cAAI,SAAS,MAAM;AACjB,qBAAS,aAAa,SAAS,KAAK;AAAA,UACtC;AACA,6BAAmB,aAAa,UAAU,mBAAmB,cAAc,MAAM,CAAC;AAAA,QACpF;AACA,YAAI,eAAe;AACjB,wBAAc,IAAIA,QAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,WAAW,CAAC,mBAAmB,mBAAmB,SAAS,KAAK,GAAG;AACjE,yBAAmB,qBAAqB,CAAC,GAAG,mBAAmB,oBAAoB,KAAK;AAAA,IAC1F;AAAA,EACF;AACA,SAAOA;AACT;AACA,IAAM,eAAe,aAAW;AAC9B,QAAM,UAAU,QAAQ;AACxB,QAAM,MAAM,QAAQ;AACpB,QAAM,QAAQ,QAAQ;AACtB,QAAM,kBAAkB,WAAW,gBAAgB,QAAQ,SAAS;AACpE,QAAMA,WAAU,SAAS,IAAI,aAAa,IAAI,aAAa,IAAI,YAAY,GAAG,OAAO;AACrF,MAAI,QAAQ,IAA6C;AAQvD,QAAI,MAAM,IAAIA;AACd,QAAI,UAAU,IAAIA,WAAU,IAAI;AAAA,EAClC;AACA,kBAAgB;AAClB;AACA,IAAM,aAAa,CAAC,KAAK,SAAS,QAAQ,IAAI;AAwB9C,IAAM,cAAc,CAAC,KAAK,YAAY,UAAU,UAAU,OAAO,UAAU;AACzE,MAAI,aAAa,UAAU;AACzB,QAAI,SAAS,kBAAkB,KAAK,UAAU;AAC9C,QAAI,KAAK,WAAW,YAAY;AAChC,QAAI,eAAe,SAAS;AAC1B,YAAM,YAAY,IAAI;AACtB,YAAM,aAAa,eAAe,QAAQ;AAC1C,YAAM,aAAa,eAAe,QAAQ;AAC1C,gBAAU,OAAO,GAAG,WAAW,OAAO,OAAK,KAAK,CAAC,WAAW,SAAS,CAAC,CAAC,CAAC;AACxE,gBAAU,IAAI,GAAG,WAAW,OAAO,OAAK,KAAK,CAAC,WAAW,SAAS,CAAC,CAAC,CAAC;AAAA,IACvE,WAAW,eAAe,SAAS;AAEjC;AACE,mBAAW,QAAQ,UAAU;AAC3B,cAAI,CAAC,YAAY,SAAS,IAAI,KAAK,MAAM;AACvC,gBAAI,KAAK,SAAS,GAAG,GAAG;AACtB,kBAAI,MAAM,eAAe,IAAI;AAAA,YAC/B,OAAO;AACL,kBAAI,MAAM,IAAI,IAAI;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW,QAAQ,UAAU;AAC3B,YAAI,CAAC,YAAY,SAAS,IAAI,MAAM,SAAS,IAAI,GAAG;AAClD,cAAI,KAAK,SAAS,GAAG,GAAG;AACtB,gBAAI,MAAM,YAAY,MAAM,SAAS,IAAI,CAAC;AAAA,UAC5C,OAAO;AACL,gBAAI,MAAM,IAAI,IAAI,SAAS,IAAI;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,eAAe,MAAO;AAAA,aAAU,eAAe,OAAO;AAE/D,UAAI,UAAU;AACZ,iBAAS,GAAG;AAAA,MACd;AAAA,IACF,WAAW,CAAC,UAAU,WAAW,CAAC,MAAM,OAAO,WAAW,CAAC,MAAM,KAAK;AAKpE,UAAI,WAAW,CAAC,MAAM,KAAK;AAQzB,qBAAa,WAAW,MAAM,CAAC;AAAA,MACjC,WAAW,kBAAkB,KAAK,EAAE,GAAG;AAKrC,qBAAa,GAAG,MAAM,CAAC;AAAA,MACzB,OAAO;AAML,qBAAa,GAAG,CAAC,IAAI,WAAW,MAAM,CAAC;AAAA,MACzC;AACA,UAAI,UAAU;AACZ,YAAI,IAAI,KAAK,YAAY,UAAU,KAAK;AAAA,MAC1C;AACA,UAAI,UAAU;AACZ,YAAI,IAAI,KAAK,YAAY,UAAU,KAAK;AAAA,MAC1C;AAAA,IACF,OAAO;AAEL,YAAM,YAAY,cAAc,QAAQ;AACxC,WAAK,UAAU,aAAa,aAAa,SAAS,CAAC,OAAO;AACxD,YAAI;AACF,cAAI,CAAC,IAAI,QAAQ,SAAS,GAAG,GAAG;AAC9B,kBAAM,IAAI,YAAY,OAAO,KAAK;AAElC,gBAAI,eAAe,QAAQ;AACzB,uBAAS;AAAA,YACX,WAAW,YAAY,QAAQ,IAAI,UAAU,KAAK,GAAG;AACnD,kBAAI,UAAU,IAAI;AAAA,YACpB;AAAA,UACF,OAAO;AACL,gBAAI,UAAU,IAAI;AAAA,UACpB;AAAA,QACF,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AACA,UAAI,YAAY,QAAQ,aAAa,OAAO;AAC1C,YAAI,aAAa,SAAS,IAAI,aAAa,UAAU,MAAM,IAAI;AAC7D;AACE,gBAAI,gBAAgB,UAAU;AAAA,UAChC;AAAA,QACF;AAAA,MACF,YAAY,CAAC,UAAU,QAAQ,KAA8B,UAAU,CAAC,WAAW;AACjF,mBAAW,aAAa,OAAO,KAAK;AACpC;AACE,cAAI,aAAa,YAAY,QAAQ;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,sBAAsB;AAC5B,IAAM,iBAAiB,WAAS,CAAC,QAAQ,CAAC,IAAI,MAAM,MAAM,mBAAmB;AAC7E,IAAM,gBAAgB,CAAC,UAAU,UAAUC,YAAW,eAAe;AAInE,QAAM,MAAM,SAAS,MAAM,aAAa,MAAuC,SAAS,MAAM,OAAO,SAAS,MAAM,OAAO,SAAS;AACpI,QAAM,gBAAgB,YAAY,SAAS,WAAW;AACtD,QAAM,gBAAgB,SAAS,WAAW;AAC1C;AAEE,SAAK,cAAc,eAAe;AAChC,UAAI,EAAE,cAAc,gBAAgB;AAClC,oBAAY,KAAK,YAAY,cAAc,UAAU,GAAG,QAAWA,YAAW,SAAS,OAAO;AAAA,MAChG;AAAA,IACF;AAAA,EACF;AAEA,OAAK,cAAc,eAAe;AAChC,gBAAY,KAAK,YAAY,cAAc,UAAU,GAAG,cAAc,UAAU,GAAGA,YAAW,SAAS,OAAO;AAAA,EAChH;AACF;AAWA,IAAM,YAAY,CAAC,gBAAgB,gBAAgB,YAAY,cAAc;AAE3E,QAAMC,YAAW,eAAe,WAAW,UAAU;AACrD,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,CAAC,oBAAoB;AAEvB,wBAAoB;AACpB,QAAIA,UAAS,UAAU,QAAQ;AAC7B,UAAI,SAAS;AAEX,kBAAU,UAAU,IAAI,UAAU,IAAI;AAAA,MACxC;AACA,MAAAA,UAAS,WAAWA,UAAS;AAAA;AAAA,QAE7B;AAAA;AAAA;AAAA,QAEA;AAAA;AAAA,IACF;AAAA,EACF;AACA,MAAIA,UAAS,WAAW,MAAM;AAE5B,UAAMA,UAAS,QAAQ,IAAI,eAAeA,UAAS,MAAM;AAAA,EAC3D,WAAWA,UAAS,UAAU,GAAqC;AAEjE,UAAMA,UAAS,QAAQ,uBAAuBA,SAAQ;AAAA,EACxD,OAAO;AACL,QAAI,CAAC,WAAW;AACd,kBAAYA,UAAS,UAAU;AAAA,IACjC;AAEA,UAAMA,UAAS,QAAQ,IAAI,gBAAgB,YAAY,SAAS,SAASA,UAAS,UAAU,IAAqC,YAAYA,UAAS,KAAK;AAC3J,QAAI,aAAaA,UAAS,UAAU,iBAAiB;AACnD,kBAAY;AAAA,IACd;AAEA;AACE,oBAAc,MAAMA,WAAU,SAAS;AAAA,IACzC;AACA,QAAI,MAAM,OAAO,KAAK,IAAI,MAAM,MAAM,SAAS;AAG7C,UAAI,UAAU,IAAI,IAAI,MAAM,IAAI,OAAO;AAAA,IACzC;AACA,QAAIA,UAAS,YAAY;AACvB,WAAK,IAAI,GAAG,IAAIA,UAAS,WAAW,QAAQ,EAAE,GAAG;AAE/C,oBAAY,UAAU,gBAAgBA,WAAU,GAAG,GAAG;AAEtD,YAAI,WAAW;AAEb,cAAI,YAAY,SAAS;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA;AACE,UAAIA,UAAS,UAAU,OAAO;AAE5B,oBAAY;AAAA,MACd,WAAW,IAAI,YAAY,iBAAiB;AAE1C,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA;AACE,QAAI,MAAM,IAAI;AACd,QAAIA,UAAS,WAAW,IAAqC,IAAsC;AAEjG,UAAI,MAAM,IAAI;AAEd,UAAI,MAAM,IAAI;AAEd,UAAI,MAAM,IAAIA,UAAS,UAAU;AAEjC,iBAAW,kBAAkB,eAAe,cAAc,eAAe,WAAW,UAAU;AAC9F,UAAI,YAAY,SAAS,UAAUA,UAAS,SAAS,eAAe,OAAO;AAGzE,kCAA0B,eAAe,OAAO,KAAK;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,4BAA4B,CAAC,WAAW,cAAc;AAC1D,MAAI,WAAW;AACf,QAAM,oBAAoB,UAAU;AACpC,WAAS,IAAI,kBAAkB,SAAS,GAAG,KAAK,GAAG,KAAK;AACtD,UAAM,YAAY,kBAAkB,CAAC;AACrC,QAAI,UAAU,MAAM,MAAM,eAAe,UAAU,MAAM,GAAG;AAK1D,0BAAoB,SAAS,EAAE,aAAa,WAAW,cAAc,SAAS,CAAC;AAI/E,gBAAU,MAAM,EAAE,OAAO;AACzB,gBAAU,MAAM,IAAI;AACpB,0BAAoB;AAAA,IACtB;AACA,QAAI,WAAW;AACb,gCAA0B,WAAW,SAAS;AAAA,IAChD;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAClB;AAgBA,IAAM,YAAY,CAAC,WAAW,QAAQ,aAAa,QAAQ,UAAU,WAAW;AAC9E,MAAI,eAAe,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,cAAc;AACxE,MAAI;AACJ,MAAI,aAAa,cAAc,aAAa,YAAY,aAAa;AACnE,mBAAe,aAAa;AAAA,EAC9B;AACA,SAAO,YAAY,QAAQ,EAAE,UAAU;AACrC,QAAI,OAAO,QAAQ,GAAG;AACpB,kBAAY,UAAU,MAAM,aAAa,UAAU,SAAS;AAC5D,UAAI,WAAW;AACb,eAAO,QAAQ,EAAE,QAAQ;AACzB,qBAAa,aAAa,WAAW,cAAc,MAAM,CAAC;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AACF;AAYA,IAAM,eAAe,CAAC,QAAQ,UAAU,WAAW;AACjD,WAAS,QAAQ,UAAU,SAAS,QAAQ,EAAE,OAAO;AACnD,UAAM,QAAQ,OAAO,KAAK;AAC1B,QAAI,OAAO;AACT,YAAM,MAAM,MAAM;AAClB,uBAAiB,KAAK;AACtB,UAAI,KAAK;AACP;AAGE,wCAA8B;AAC9B,cAAI,IAAI,MAAM,GAAG;AAEf,gBAAI,MAAM,EAAE,OAAO;AAAA,UACrB,OAAO;AAGL,sCAA0B,KAAK,IAAI;AAAA,UACrC;AAAA,QACF;AAEA,YAAI,OAAO;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AAqEA,IAAM,iBAAiB,CAAC,WAAW,OAAOA,WAAU,UAAU;AAC5D,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,MAAI,WAAW;AACf,MAAI,IAAI;AACR,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI,YAAY,MAAM,SAAS;AAC/B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,cAAc,MAAM,SAAS;AACjC,MAAI;AACJ,MAAI;AACJ,SAAO,eAAe,aAAa,eAAe,WAAW;AAC3D,QAAI,iBAAiB,MAAM;AAEzB,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,eAAe,MAAM;AAC9B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,iBAAiB,MAAM;AAChC,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,eAAe,MAAM;AAC9B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,YAAY,eAAe,aAAa,GAAG;AAKpD,YAAM,eAAe,aAAa;AAClC,sBAAgB,MAAM,EAAE,WAAW;AACnC,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,WAAW,YAAY,aAAa,WAAW,GAAG;AAIhD,YAAM,aAAa,WAAW;AAC9B,oBAAc,MAAM,EAAE,SAAS;AAC/B,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,YAAY,eAAe,WAAW,GAAG;AAelD,UAAI,cAAc,UAAU,UAAU,YAAY,UAAU,QAAQ;AAClE,kCAA0B,cAAc,MAAM,YAAY,KAAK;AAAA,MACjE;AACA,YAAM,eAAe,WAAW;AAkBhC,gBAAU,aAAa,cAAc,OAAO,YAAY,MAAM,WAAW;AACzE,sBAAgB,MAAM,EAAE,WAAW;AACnC,oBAAc,MAAM,EAAE,SAAS;AAAA,IACjC,WAAW,YAAY,aAAa,aAAa,GAAG;AAgBlD,UAAI,cAAc,UAAU,UAAU,YAAY,UAAU,QAAQ;AAClE,kCAA0B,YAAY,MAAM,YAAY,KAAK;AAAA,MAC/D;AACA,YAAM,aAAa,aAAa;AAMhC,gBAAU,aAAa,YAAY,OAAO,cAAc,KAAK;AAC7D,oBAAc,MAAM,EAAE,SAAS;AAC/B,sBAAgB,MAAM,EAAE,WAAW;AAAA,IACrC,OAAO;AASL,iBAAW;AACX;AACE,aAAK,IAAI,aAAa,KAAK,WAAW,EAAE,GAAG;AACzC,cAAI,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,UAAU,QAAQ,MAAM,CAAC,EAAE,UAAU,cAAc,OAAO;AACjF,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,YAAY,GAAG;AAGjB,oBAAY,MAAM,QAAQ;AAC1B,YAAI,UAAU,UAAU,cAAc,OAAO;AAE3C,iBAAO,UAAU,SAAS,MAAM,WAAW,GAAGA,WAAU,UAAU,SAAS;AAAA,QAC7E,OAAO;AACL,gBAAM,WAAW,aAAa;AAG9B,gBAAM,QAAQ,IAAI;AAClB,iBAAO,UAAU;AAAA,QACnB;AACA,wBAAgB,MAAM,EAAE,WAAW;AAAA,MACrC,OAAO;AAKL,eAAO,UAAU,SAAS,MAAM,WAAW,GAAGA,WAAU,aAAa,SAAS;AAC9E,wBAAgB,MAAM,EAAE,WAAW;AAAA,MACrC;AACA,UAAI,MAAM;AAER;AACE,8BAAoB,cAAc,KAAK,EAAE,aAAa,MAAM,cAAc,cAAc,KAAK,CAAC;AAAA,QAChG;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,cAAc,WAAW;AAE3B,cAAU,WAAW,MAAM,YAAY,CAAC,KAAK,OAAO,OAAO,MAAM,YAAY,CAAC,EAAE,OAAOA,WAAU,OAAO,aAAa,SAAS;AAAA,EAChI,WAAW,cAAc,WAAW;AAIlC,iBAAa,OAAO,aAAa,SAAS;AAAA,EAC5C;AACF;AAmBA,IAAM,cAAc,CAAC,WAAW,eAAe;AAG7C,MAAI,UAAU,UAAU,WAAW,OAAO;AACxC,QAAI,UAAU,UAAU,QAAQ;AAC9B,aAAO,UAAU,WAAW,WAAW;AAAA,IACzC;AAEA;AACE,aAAO,UAAU,UAAU,WAAW;AAAA,IACxC;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,gBAAgB,UAAQ;AAK5B,SAAO,QAAQ,KAAK,MAAM,KAAK;AACjC;AACA,IAAM,sBAAsB,WAAS,KAAK,MAAM,IAAI,KAAK,MAAM,IAAI,MAAM;AASzE,IAAM,QAAQ,CAAC,UAAUA,cAAa;AACpC,QAAM,MAAMA,UAAS,QAAQ,SAAS;AACtC,QAAM,cAAc,SAAS;AAC7B,QAAM,cAAcA,UAAS;AAC7B,QAAM,MAAMA,UAAS;AACrB,QAAM,OAAOA,UAAS;AACtB,MAAI;AACJ,MAAI,SAAS,MAAM;AACjB;AAGE,kBAAY,QAAQ,QAAQ,OAAO,QAAQ,kBAAkB,QAAQ;AAAA,IACvE;AACA;AACE,UAAI,QAAQ,OAAQ;AAAA,WAAM;AAIxB,sBAAc,UAAUA,WAAU,SAAS;AAAA,MAC7C;AAAA,IACF;AACA,QAAI,gBAAgB,QAAQ,gBAAgB,MAAM;AAGhD,qBAAe,KAAK,aAAaA,WAAU,WAAW;AAAA,IACxD,WAAW,gBAAgB,MAAM;AAE/B,UAAI,SAAS,WAAW,MAAM;AAE5B,YAAI,cAAc;AAAA,MACpB;AAEA,gBAAU,KAAK,MAAMA,WAAU,aAAa,GAAG,YAAY,SAAS,CAAC;AAAA,IACvE,WAAW,gBAAgB,MAAM;AAE/B,mBAAa,aAAa,GAAG,YAAY,SAAS,CAAC;AAAA,IACrD;AACA,QAAI,aAAa,QAAQ,OAAO;AAC9B,kBAAY;AAAA,IACd;AAAA,EACF,WAAW,gBAAgB,IAAI,MAAM,GAAG;AAEtC,kBAAc,WAAW,cAAc;AAAA,EACzC,WAAW,SAAS,WAAW,MAAM;AAGnC,QAAI,OAAO;AAAA,EACb;AACF;AAcA,IAAM,+BAA+B,SAAO;AAC1C,QAAM,aAAa,IAAI;AACvB,aAAW,aAAa,YAAY;AAClC,QAAI,UAAU,aAAa,GAA+B;AACxD,UAAI,UAAU,MAAM,GAAG;AAGrB,cAAM,WAAW,UAAU,MAAM;AAGjC,kBAAU,SAAS;AAGnB,mBAAW,eAAe,YAAY;AACpC,cAAI,YAAY,MAAM,MAAM,UAAU,MAAM,KAAK,aAAa,IAAI;AAGhE,gBAAI,YAAY,aAAa,KAAiC,aAAa,YAAY,aAAa,MAAM,GAAG;AAC3G,wBAAU,SAAS;AACnB;AAAA,YACF;AAAA,UACF,OAAO;AAIL,gBAAI,YAAY,aAAa,KAAiC,YAAY,aAAa,KAA8B,YAAY,YAAY,KAAK,MAAM,IAAI;AAC1J,wBAAU,SAAS;AACnB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,mCAA6B,SAAS;AAAA,IACxC;AAAA,EACF;AACF;AAKA,IAAM,gBAAgB,CAAC;AAQvB,IAAM,+BAA+B,SAAO;AAE1C,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,aAAW,aAAa,IAAI,YAAY;AAGtC,QAAI,UAAU,MAAM,MAAM,OAAO,UAAU,MAAM,MAAM,KAAK,YAAY;AAGtE,yBAAmB,KAAK,WAAW;AACnC,YAAM,WAAW,UAAU,MAAM;AAGjC,WAAK,IAAI,iBAAiB,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,eAAO,iBAAiB,CAAC;AAIzB,YAAI,CAAC,KAAK,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,UAAU,MAAM,GAAG;AAIxE,cAAI,oBAAoB,MAAM,QAAQ,GAAG;AAEvC,gBAAI,mBAAmB,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI;AAI1E,0CAA8B;AAE9B,iBAAK,MAAM,IAAI,KAAK,MAAM,KAAK;AAC/B,gBAAI,kBAAkB;AAIpB,+BAAiB,gBAAgB;AAAA,YACnC,OAAO;AAEL,4BAAc,KAAK;AAAA,gBACjB,eAAe;AAAA,gBACf,kBAAkB;AAAA,cACpB,CAAC;AAAA,YACH;AACA,gBAAI,KAAK,MAAM,GAAG;AAChB,4BAAc,IAAI,kBAAgB;AAChC,oBAAI,oBAAoB,aAAa,kBAAkB,KAAK,MAAM,CAAC,GAAG;AACpE,qCAAmB,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI;AACtE,sBAAI,oBAAoB,CAAC,aAAa,eAAe;AACnD,iCAAa,gBAAgB,iBAAiB;AAAA,kBAChD;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,WAAW,CAAC,cAAc,KAAK,OAAK,EAAE,qBAAqB,IAAI,GAAG;AAKhE,0BAAc,KAAK;AAAA,cACjB,kBAAkB;AAAA,YACpB,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,UAAU,aAAa,GAA+B;AACxD,mCAA6B,SAAS;AAAA,IACxC;AAAA,EACF;AACF;AAQA,IAAM,sBAAsB,CAAC,gBAAgB,aAAa;AACxD,MAAI,eAAe,aAAa,GAA+B;AAC7D,QAAI,eAAe,aAAa,MAAM,MAAM,QAAQ,aAAa,IAAI;AAGnE,aAAO;AAAA,IACT;AACA,QAAI,eAAe,aAAa,MAAM,MAAM,UAAU;AACpD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,MAAM,MAAM,UAAU;AACvC,WAAO;AAAA,EACT;AACA,SAAO,aAAa;AACtB;AAQA,IAAM,mBAAmB,WAAS;AAChC;AACE,UAAM,WAAW,MAAM,QAAQ,OAAO,MAAM,QAAQ,IAAI,IAAI;AAC5D,UAAM,cAAc,MAAM,WAAW,IAAI,gBAAgB;AAAA,EAC3D;AACF;AAcA,IAAM,aAAa,CAAC,SAAS,iBAAiB,gBAAgB,UAAU;AACtE,QAAM,UAAU,QAAQ;AACxB,QAAM,UAAU,QAAQ;AACxB,QAAM,WAAW,QAAQ,WAAW,SAAS,MAAM,IAAI;AAMvD,QAAM,YAAY,OAAO,eAAe,IAAI,kBAAkB,EAAE,MAAM,MAAM,eAAe;AAC3F,gBAAc,QAAQ;AACtB,MAAI,QAAQ,kBAAkB;AAC5B,cAAU,UAAU,UAAU,WAAW,CAAC;AAC1C,YAAQ,iBAAiB,IAAI,CAAC,CAAC,UAAU,SAAS,MAAM,UAAU,QAAQ,SAAS,IAAI,QAAQ,QAAQ,CAAC;AAAA,EAC1G;AAOA,MAAI,iBAAiB,UAAU,SAAS;AACtC,eAAW,OAAO,OAAO,KAAK,UAAU,OAAO,GAAG;AAShD,UAAI,QAAQ,aAAa,GAAG,KAAK,CAAC,CAAC,OAAO,OAAO,SAAS,OAAO,EAAE,SAAS,GAAG,GAAG;AAChF,kBAAU,QAAQ,GAAG,IAAI,QAAQ,GAAG;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,YAAU,QAAQ;AAClB,YAAU,WAAW;AACrB,UAAQ,UAAU;AAClB,YAAU,QAAQ,SAAS,QAAQ,QAAQ,cAAc;AACzD;AACE,cAAU,QAAQ,MAAM;AAAA,EAC1B;AACA;AACE,iBAAa,QAAQ,MAAM;AAC3B,0BAAsB,QAAQ,UAAU,OAA8C;AAEtF,kCAA8B;AAAA,EAChC;AAEA,QAAM,UAAU,SAAS;AACzB;AAGE,QAAI,WAAW;AACf,QAAI,mBAAmB;AACrB,mCAA6B,UAAU,KAAK;AAC5C,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,IAAI;AACR,aAAO,IAAI,cAAc,QAAQ,KAAK;AACpC,uBAAe,cAAc,CAAC;AAC9B,yBAAiB,aAAa;AAC9B,YAAI,CAAC,eAAe,MAAM,GAAG;AAG3B,4BAAkB,0BAA0B,cAAc;AAC1D,0BAAgB,MAAM,IAAI;AAC1B,yBAAe,WAAW,aAAa,eAAe,MAAM,IAAI,iBAAiB,cAAc;AAAA,QACjG;AAAA,MACF;AACA,WAAK,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AACzC,uBAAe,cAAc,CAAC;AAC9B,yBAAiB,aAAa;AAC9B,YAAI,aAAa,eAAe;AAG9B,0BAAgB,aAAa,cAAc;AAC3C,6BAAmB,aAAa,cAAc;AAC9C,4BAAkB,eAAe,MAAM;AACvC,iBAAO,kBAAkB,gBAAgB,iBAAiB;AACxD,sBAAU,gBAAgB,MAAM;AAChC,gBAAI,WAAW,QAAQ,MAAM,MAAM,eAAe,MAAM,KAAK,kBAAkB,QAAQ,YAAY;AACjG,wBAAU,QAAQ;AAClB,kBAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,GAAG;AAChC,mCAAmB;AACnB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,oBAAoB,kBAAkB,eAAe,cAAc,eAAe,gBAAgB,kBAAkB;AAIvH,gBAAI,mBAAmB,kBAAkB;AACvC,kBAAI,CAAC,eAAe,MAAM,KAAK,eAAe,MAAM,GAAG;AAErD,+BAAe,MAAM,IAAI,eAAe,MAAM,EAAE,WAAW;AAAA,cAC7D;AAEA,4BAAc,aAAa,gBAAgB,gBAAgB;AAAA,YAC7D;AAAA,UACF;AAAA,QACF,OAAO;AAEL,cAAI,eAAe,aAAa,GAA+B;AAC7D,2BAAe,SAAS;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,6BAA6B;AAC/B,mCAA6B,UAAU,KAAK;AAAA,IAC9C;AAGA,QAAI,WAAW,CAAC;AAEhB,kBAAc,SAAS;AAAA,EACzB;AACF;AAGA,IAAM,yBAAyB,eAAa,IAAI,cAAc,QAAQ,UAAU,SAAS,YAAY,UAAU,SAAS,MAAM,EAAE,WAAW,YAAY,YAAY,CAAC,GAAG;AACvK,IAAM,4BAA4B,oBAAkB,IAAI,cAAc,uBAAuB,eAAe,YAAY,IAAI,eAAe,SAAS,WAAW,eAAe,MAAM,CAAC,MAAM,IAAI,eAAe,WAAW,IAAI;AAC7N,IAAM,mBAAmB,CAAC,SAAS,sBAAsB;AACvD,MAAI,qBAAqB,CAAC,QAAQ,qBAAqB,kBAAkB,KAAK,GAAG;AAC/E,sBAAkB,KAAK,EAAE,KAAK,IAAI,QAAQ,OAAK,QAAQ,oBAAoB,CAAC,CAAC;AAAA,EAC/E;AACF;AACA,IAAM,iBAAiB,CAAC,SAAS,kBAAkB;AACjD;AACE,YAAQ,WAAW;AAAA,EACrB;AACA,MAAI,QAAQ,UAAU,GAAyC;AAC7D,YAAQ,WAAW;AACnB;AAAA,EACF;AACA,mBAAiB,SAAS,QAAQ,mBAAmB;AAIrD,QAAM,WAAW,MAAM,cAAc,SAAS,aAAa;AAC3D,SAAO,UAAU,QAAQ;AAC3B;AAWA,IAAM,gBAAgB,CAAC,SAAS,kBAAkB;AAChD,QAAM,MAAM,QAAQ;AACpB,QAAM,cAAc,WAAW,kBAAkB,QAAQ,UAAU,SAAS;AAC5E,QAAM,WAAW,QAAQ;AAazB,MAAI;AACJ,MAAI,eAAe;AACjB;AACE,cAAQ,WAAW;AACnB,UAAI,QAAQ,mBAAmB;AAC7B,gBAAQ,kBAAkB,IAAI,CAAC,CAAC,YAAY,KAAK,MAAM,SAAS,UAAU,YAAY,KAAK,CAAC;AAC5F,gBAAQ,oBAAoB;AAAA,MAC9B;AAAA,IACF;AACA,uBAAmB,KAAK,mBAAmB;AAC3C;AAME,qBAAe,SAAS,UAAU,mBAAmB;AAAA,IACvD;AAAA,EACF,OAAO;AACL,uBAAmB,KAAK,qBAAqB;AAAA,EAC/C;AACA,qBAAmB,KAAK,qBAAqB;AAC7C;AACE,mBAAe,QAAQ,cAAc,MAAM,SAAS,UAAU,qBAAqB,CAAC;AAAA,EACtF;AACA,cAAY;AACZ,SAAO,QAAQ,cAAc,MAAM,gBAAgB,SAAS,UAAU,aAAa,CAAC;AACtF;AAiBA,IAAM,UAAU,CAAC,cAAc,OAAO,WAAW,YAAY,IAAI,aAAa,KAAK,EAAE,IAAI,GAAG;AAW5F,IAAM,aAAa,kBAAgB,wBAAwB,WAAW,gBAAgB,aAAa,QAAQ,OAAO,aAAa,SAAS;AAWxI,IAAM,kBAAkB,CAAO,SAAS,UAAU,kBAAkB;AAClE,MAAI;AACJ,QAAM,MAAM,QAAQ;AACpB,QAAM,YAAY,WAAW,UAAU,QAAQ,UAAU,SAAS;AAClE,QAAM,KAAK,IAAI,MAAM;AACrB,MAAI,eAAe;AAEjB,iBAAa,OAAO;AAAA,EACtB;AACA,QAAM,YAAY,WAAW,UAAU,QAAQ,UAAU,SAAS;AAClE;AACE,eAAW,SAAS,UAAU,KAAK,aAAa;AAAA,EAClD;AACA,MAAI,IAAI;AAIN,OAAG,IAAI,QAAM,GAAG,CAAC;AACjB,QAAI,MAAM,IAAI;AAAA,EAChB;AACA,YAAU;AACV,YAAU;AACV;AACE,UAAM,oBAAoB,KAAK,IAAI,KAAK,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC7E,UAAM,aAAa,MAAM,oBAAoB,OAAO;AACpD,QAAI,iBAAiB,WAAW,GAAG;AACjC,iBAAW;AAAA,IACb,OAAO;AACL,cAAQ,IAAI,gBAAgB,EAAE,KAAK,UAAU;AAC7C,cAAQ,WAAW;AACnB,uBAAiB,SAAS;AAAA,IAC5B;AAAA,EACF;AACF;AAaA,IAAM,aAAa,CAAC,SAAS,UAAU,KAAK,kBAAkB;AAC5D,MAAI;AACF,mBAAe;AAKf,eAAW,SAAS,OAAO;AAC3B;AACE,cAAQ,WAAW,CAAC;AAAA,IACtB;AACA;AACE,cAAQ,WAAW;AAAA,IACrB;AACA;AACE;AAIE;AACE,qBAAW,SAAS,UAAU,aAAa;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,iBAAa,GAAG,QAAQ,aAAa;AAAA,EACvC;AACA,iBAAe;AACf,SAAO;AACT;AACA,IAAM,kBAAkB,MAAM;AAC9B,IAAM,sBAAsB,aAAW;AACrC,QAAM,UAAU,QAAQ,UAAU;AAClC,QAAM,MAAM,QAAQ;AACpB,QAAM,gBAAgB,WAAW,cAAc,OAAO;AACtD,QAAM,WAAW,QAAQ;AACzB,QAAM,oBAAoB,QAAQ;AAClC;AACE,aAAS,UAAU,oBAAoB;AAAA,EACzC;AACA,qBAAmB,KAAK,oBAAoB;AAC5C,MAAI,EAAE,QAAQ,UAAU,KAAyC;AAC/D,YAAQ,WAAW;AACnB;AAEE,sBAAgB,GAAG;AAAA,IACrB;AACA;AACE,eAAS,UAAU,kBAAkB;AAAA,IACvC;AACA,uBAAmB,KAAK,kBAAkB;AAC1C,kBAAc;AACd;AACE,cAAQ,iBAAiB,GAAG;AAC5B,UAAI,CAAC,mBAAmB;AACtB,mBAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,OAAO;AACL,uBAAmB,KAAK,oBAAoB;AAC5C,kBAAc;AAAA,EAChB;AACA;AACE,YAAQ,oBAAoB,GAAG;AAAA,EACjC;AAGA;AACE,QAAI,QAAQ,mBAAmB;AAC7B,cAAQ,kBAAkB;AAC1B,cAAQ,oBAAoB;AAAA,IAC9B;AACA,QAAI,QAAQ,UAAU,KAAoC;AACxD,eAAS,MAAM,eAAe,SAAS,KAAK,CAAC;AAAA,IAC/C;AACA,YAAQ,WAAW,EAAE,IAA0C;AAAA,EACjE;AAIF;AACA,IAAM,cAAc,SAAO;AACzB;AACE,UAAM,UAAU,WAAW,GAAG;AAC9B,UAAM,cAAc,QAAQ,cAAc;AAC1C,QAAI,gBAAgB,QAAQ,WAAW,IAAiC,SAA4C,GAAgC;AAClJ,qBAAe,SAAS,KAAK;AAAA,IAC/B;AAEA,WAAO;AAAA,EACT;AACF;AACA,IAAM,aAAa,SAAO;AAGxB;AACE,oBAAgB,IAAI,eAAe;AAAA,EACrC;AACA,WAAS,MAAM,UAAU,KAAK,WAAW;AAAA,IACvC,QAAQ;AAAA,MACN,WAAW;AAAA,IACb;AAAA,EACF,CAAC,CAAC;AACJ;AAWA,IAAM,WAAW,CAAC,UAAU,QAAQ,QAAQ;AAC1C,MAAI,YAAY,SAAS,MAAM,GAAG;AAChC,QAAI;AACF,aAAO,SAAS,MAAM,EAAE,GAAG;AAAA,IAC7B,SAAS,GAAG;AACV,mBAAa,CAAC;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AAOA,IAAM,qBAAqB,CAAC,KAAK,kBAAkB;AACjD;AACE,cAAU,KAAK,aAAa,eAAe;AAAA,MACzC,SAAS;AAAA,MACT,UAAU;AAAA,MACV,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,kBAAkB,SAAO,IAAI,UAAU,IAAI,UAAU;AAC3D,IAAM,WAAW,CAAC,KAAK,aAAa,WAAW,GAAG,EAAE,iBAAiB,IAAI,QAAQ;AACjF,IAAM,WAAW,CAAC,KAAK,UAAU,QAAQ,YAAY;AAEnD,QAAM,UAAU,WAAW,GAAG;AAC9B,QAAM,MAAM,QAAQ;AACpB,QAAM,SAAS,QAAQ,iBAAiB,IAAI,QAAQ;AACpD,QAAM,QAAQ,QAAQ;AACtB,QAAM,WAAW,QAAQ;AACzB,WAAS,mBAAmB,QAAQ,QAAQ,UAAU,QAAQ,EAAE,CAAC,CAAC;AAElE,QAAM,aAAa,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,MAAM;AAC9D,QAAM,iBAAiB,WAAW,UAAU,CAAC;AAC7C,OAAK,EAAE,QAAQ,MAA8C,WAAW,WAAc,gBAAgB;AAGpG,YAAQ,iBAAiB,IAAI,UAAU,MAAM;AAC7C,QAAI,UAAU;AAEZ,UAAI,QAAQ,cAAc,QAAQ,KAAmC;AACnE,cAAM,eAAe,QAAQ,WAAW,QAAQ;AAChD,YAAI,cAAc;AAEhB,uBAAa,IAAI,qBAAmB;AAClC,gBAAI;AAEF,uBAAS,eAAe,EAAE,QAAQ,QAAQ,QAAQ;AAAA,YACpD,SAAS,GAAG;AACV,2BAAa,GAAG,GAAG;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,SAAS,IAAiC,SAA4C,GAAgC;AAKzH,uBAAe,SAAS,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AACF;AAWA,IAAM,iBAAiB,CAAC,MAAM,SAAS,UAAU;AAC/C,MAAI;AACJ,MAAI,QAAQ,WAAW;AACrB,QAAI,KAAK,UAAU;AACjB,cAAQ,aAAa,KAAK;AAAA,IAC5B;AAEA,UAAM,UAAU,OAAO,QAAQ,QAAQ,SAAS;AAChD,UAAM,YAAY,KAAK;AACvB,YAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM;AAC3C,UAAI,cAAc,MAA8B,QAAQ,KAAkC,cAAc,IAA6B;AAEnI,eAAO,eAAe,WAAW,YAAY;AAAA,UAC3C,MAAM;AAEJ,mBAAO,SAAS,MAAM,UAAU;AAAA,UAClC;AAAA,UACA,IAAI,UAAU;AAEZ,qBAAS,MAAM,YAAY,UAAU,OAAO;AAAA,UAC9C;AAAA,UACA,cAAc;AAAA,UACd,YAAY;AAAA,QACd,CAAC;AAAA,MACH,WAAW,QAAQ,KAA4C,cAAc,IAA8B;AAEzG,eAAO,eAAe,WAAW,YAAY;AAAA,UAC3C,SAAS,MAAM;AACb,kBAAM,MAAM,WAAW,IAAI;AAC3B,mBAAO,IAAI,oBAAoB,KAAK,MAAM,IAAI,eAAe,UAAU,EAAE,GAAG,IAAI,CAAC;AAAA,UACnF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,QAAI,QAAQ,GAA0C;AACpD,YAAM,qBAAqB,oBAAI,IAAI;AACnC,gBAAU,2BAA2B,SAAU,UAAU,UAAU,UAAU;AAC3E,YAAI,IAAI,MAAM;AACZ,gBAAM,WAAW,mBAAmB,IAAI,QAAQ;AAkChD,cAAI,KAAK,eAAe,QAAQ,GAAG;AACjC,uBAAW,KAAK,QAAQ;AACxB,mBAAO,KAAK,QAAQ;AAAA,UACtB,WAAW,UAAU,eAAe,QAAQ,KAAK,OAAO,KAAK,QAAQ,MAAM,YAAY,KAAK,QAAQ,KAAK,UAAU;AAIjH;AAAA,UACF,WAAW,YAAY,MAAM;AAG3B,kBAAM,UAAU,WAAW,IAAI;AAC/B,kBAAMC,SAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAKxE,gBAAI,EAAEA,SAAQ,MAA8CA,SAAQ,OAAqC,aAAa,UAAU;AAC9H,oBAAM,WAAW,QAAQ;AACzB,oBAAM,QAAQ,QAAQ,WAAW,QAAQ;AACzC,wBAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,QAAQ,kBAAgB;AAC1E,oBAAI,SAAS,YAAY,KAAK,MAAM;AAClC,2BAAS,YAAY,EAAE,KAAK,UAAU,UAAU,UAAU,QAAQ;AAAA,gBACpE;AAAA,cACF,CAAC;AAAA,YACH;AACA;AAAA,UACF;AACA,eAAK,QAAQ,IAAI,aAAa,QAAQ,OAAO,KAAK,QAAQ,MAAM,YAAY,QAAQ;AAAA,QACtF,CAAC;AAAA,MACH;AAMA,WAAK,qBAAqB,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,QAAQ,gBAAgB,QAAQ,OAAO,SAAS,KAAK,CAAC,CAAC,GAAG,GAAG,QAAQ;AAAA,QAAO,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI;AAAA;AAAA,MAAkC,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM;AAC3N,cAAM,WAAW,EAAE,CAAC,KAAK;AACzB,2BAAmB,IAAI,UAAU,QAAQ;AACzC,YAAI,EAAE,CAAC,IAAI,KAAoC;AAC7C,kBAAQ,iBAAiB,KAAK,CAAC,UAAU,QAAQ,CAAC;AAAA,QACpD;AACA,eAAO;AAAA,MACT,CAAC,CAAC,CAAC,CAAC;AAAA,IACN;AAAA,EACF;AACA,SAAO;AACT;AAWA,IAAM,sBAAsB,CAAO,KAAK,SAAS,SAAS,iBAAiB;AACzE,MAAI;AAEJ,OAAK,QAAQ,UAAU,QAAiD,GAAG;AAEzE,YAAQ,WAAW;AACnB;AAIE,aAAO,WAAW,OAAO;AACzB,UAAI,KAAK,MAAM;AAEb,cAAM,UAAU,WAAW;AAC3B,eAAO,MAAM;AACb,gBAAQ;AAAA,MACV;AACA,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,oBAAoB,QAAQ,SAAS,IAAI,QAAQ,UAAU,iBAAiB;AAAA,MAC9F;AACA,UAAI,CAAC,KAAK,WAAW;AAInB;AACE,kBAAQ,aAAa,KAAK;AAAA,QAC5B;AACA;AAAA,UAAe;AAAA,UAAM;AAAA,UAAS;AAAA;AAAA,QAA8B;AAC5D,aAAK,YAAY;AAAA,MACnB;AACA,YAAM,iBAAiB,WAAW,kBAAkB,QAAQ,SAAS;AAIrE;AACE,gBAAQ,WAAW;AAAA,MACrB;AAKA,UAAI;AACF,YAAI,KAAK,OAAO;AAAA,MAClB,SAAS,GAAG;AACV,qBAAa,CAAC;AAAA,MAChB;AACA;AACE,gBAAQ,WAAW,CAAC;AAAA,MACtB;AACA;AACE,gBAAQ,WAAW;AAAA,MACrB;AACA,qBAAe;AACf,4BAAsB,QAAQ,cAAc;AAAA,IAC9C;AACA,QAAI,KAAK,OAAO;AAEd,UAAI,QAAQ,KAAK;AACjB,YAAMH,WAAU,WAAW,OAAO;AAClC,UAAI,CAAC,OAAO,IAAIA,QAAO,GAAG;AACxB,cAAM,oBAAoB,WAAW,kBAAkB,QAAQ,SAAS;AACxE,sBAAcA,UAAS,OAAO,CAAC,EAAE,QAAQ,UAAU,EAAyC;AAC5F,0BAAkB;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,QAAM,oBAAoB,QAAQ;AAClC,QAAM,WAAW,MAAM,eAAe,SAAS,IAAI;AACnD,MAAI,qBAAqB,kBAAkB,MAAM,GAAG;AAOlD,sBAAkB,MAAM,EAAE,KAAK,QAAQ;AAAA,EACzC,OAAO;AACL,aAAS;AAAA,EACX;AACF;AACA,IAAM,wBAAwB,cAAY;AACxC;AACE,aAAS,UAAU,mBAAmB;AAAA,EACxC;AACF;AACA,IAAM,oBAAoB,SAAO;AAC/B,OAAK,IAAI,UAAU,OAA8C,GAAG;AAClE,UAAM,UAAU,WAAW,GAAG;AAC9B,UAAM,UAAU,QAAQ;AACxB,UAAM,eAAe,WAAW,qBAAqB,QAAQ,SAAS;AACtE,QAAI,EAAE,QAAQ,UAAU,IAAkC;AAExD,cAAQ,WAAW;AACnB;AAKE;AAAA;AAAA,UAEA,QAAQ,WAAW,IAAsC;AAAA,UAAuC;AAC9F,8BAAoB,GAAG;AAAA,QACzB;AAAA,MACF;AACA;AAGE,YAAI,oBAAoB;AACxB,eAAO,oBAAoB,kBAAkB,cAAc,kBAAkB,MAAM;AAGjF,cAAI,kBAAkB,KAAK,GAAG;AAG5B,6BAAiB,SAAS,QAAQ,sBAAsB,iBAAiB;AACzE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,UAAI,QAAQ,WAAW;AACrB,eAAO,QAAQ,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM;AACrE,cAAI,cAAc,MAA8B,IAAI,eAAe,UAAU,GAAG;AAC9E,kBAAM,QAAQ,IAAI,UAAU;AAC5B,mBAAO,IAAI,UAAU;AACrB,gBAAI,UAAU,IAAI;AAAA,UACpB;AAAA,QACF,CAAC;AAAA,MACH;AACA;AACE,4BAAoB,KAAK,SAAS,OAAO;AAAA,MAC3C;AAAA,IACF,OAAO;AAIL,4BAAsB,KAAK,SAAS,QAAQ,WAAW;AAEvD,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,gBAAgB;AAC5E,8BAAsB,QAAQ,cAAc;AAAA,MAC9C,WAAW,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB;AACrF,gBAAQ,iBAAiB,KAAK,MAAM,sBAAsB,QAAQ,cAAc,CAAC;AAAA,MACnF;AAAA,IACF;AACA,iBAAa;AAAA,EACf;AACF;AACA,IAAM,sBAAsB,SAAO;AAOjC,QAAM,gBAAgB,IAAI,MAAM,IAAI,IAAI,cAAc,qBAAqB,IAAI,SAAS,GAAG;AAC3F,gBAAc,MAAM,IAAI;AACxB,MAAI,aAAa,eAAe,IAAI,UAAU;AAChD;AACA,IAAM,qBAAqB,cAAY;AACrC;AACE,aAAS,UAAU,sBAAsB;AAAA,EAC3C;AACF;AACA,IAAM,uBAAuB,CAAM,QAAO;AACxC,OAAK,IAAI,UAAU,OAA8C,GAAG;AAClE,UAAM,UAAU,WAAW,GAAG;AAC9B;AACE,UAAI,QAAQ,eAAe;AACzB,gBAAQ,cAAc,IAAI,gBAAc,WAAW,CAAC;AACpD,gBAAQ,gBAAgB;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,gBAAgB;AAC5E,yBAAmB,QAAQ,cAAc;AAAA,IAC3C,WAAW,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,kBAAkB;AACrF,cAAQ,iBAAiB,KAAK,MAAM,mBAAmB,QAAQ,cAAc,CAAC;AAAA,IAChF;AAAA,EACF;AACF;AACA,IAAM,gBAAgB,CAAC,aAAa,UAAU,CAAC,MAAM;AACnD,MAAI;AACJ,QAAM,eAAe,WAAW;AAChC,QAAM,UAAU,CAAC;AACjB,QAAM,UAAU,QAAQ,WAAW,CAAC;AACpC,QAAM,iBAAiB,IAAI;AAC3B,QAAM,OAAO,IAAI;AACjB,QAAM,cAA2B,KAAK,cAAc,eAAe;AACnE,QAAM,kBAA+B,IAAI,cAAc,OAAO;AAC9D,QAAM,6BAA6B,CAAC;AACpC,MAAI;AACJ,MAAI,kBAAkB;AACtB,SAAO,OAAO,KAAK,OAAO;AAC1B,MAAI,iBAAiB,IAAI,IAAI,QAAQ,gBAAgB,MAAM,IAAI,OAAO,EAAE;AACxE,cAAY,IAAI,gBAAc;AAC5B,eAAW,CAAC,EAAE,IAAI,iBAAe;AAC/B,UAAII;AACJ,YAAM,UAAU;AAAA,QACd,SAAS,YAAY,CAAC;AAAA,QACtB,WAAW,YAAY,CAAC;AAAA,QACxB,WAAW,YAAY,CAAC;AAAA,QACxB,aAAa,YAAY,CAAC;AAAA,MAC5B;AACA;AACE,gBAAQ,YAAY,YAAY,CAAC;AAAA,MACnC;AACA;AACE,gBAAQ,cAAc,YAAY,CAAC;AAAA,MACrC;AACA;AACE,gBAAQ,mBAAmB,CAAC;AAAA,MAC9B;AACA;AACE,gBAAQ,cAAcA,MAAK,YAAY,CAAC,OAAO,QAAQA,QAAO,SAASA,MAAK,CAAC;AAAA,MAC/E;AACA,YAAM,UAAU,QAAQ;AACxB,YAAM,cAAc,cAAc,YAAY;AAAA;AAAA,QAE5C,YAAY,MAAM;AAEhB,gBAAM,IAAI;AACV,iBAAO;AACP,uBAAa,MAAM,OAAO;AAC1B,cAAI,QAAQ,UAAU,GAA0C;AAK9D;AACE;AACE,qBAAK,aAAa;AAAA,kBAChB,MAAM;AAAA,gBACR,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,oBAAoB;AAClB,cAAI,iBAAiB;AACnB,yBAAa,eAAe;AAC5B,8BAAkB;AAAA,UACpB;AACA,cAAI,iBAAiB;AAEnB,uCAA2B,KAAK,IAAI;AAAA,UACtC,OAAO;AACL,gBAAI,IAAI,MAAM,kBAAkB,IAAI,CAAC;AAAA,UACvC;AAAA,QACF;AAAA,QACA,uBAAuB;AACrB,cAAI,IAAI,MAAM,qBAAqB,IAAI,CAAC;AAAA,QAC1C;AAAA,QACA,mBAAmB;AACjB,iBAAO,WAAW,IAAI,EAAE;AAAA,QAC1B;AAAA,MACF;AACA,cAAQ,iBAAiB,WAAW,CAAC;AACrC,UAAI,CAAC,QAAQ,SAAS,OAAO,KAAK,CAAC,eAAe,IAAI,OAAO,GAAG;AAC9D,gBAAQ,KAAK,OAAO;AACpB,uBAAe,OAAO,SAAS;AAAA,UAAe;AAAA,UAAa;AAAA,UAAS;AAAA;AAAA,QAAwC,CAAC;AAAA,MAC/G;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD;AACE,oBAAgB,YAAY,UAAU;AACtC,oBAAgB,aAAa,eAAe,EAAE;AAE9C,UAAM,SAAS,KAAK,IAAI,aAAa,QAAQ,OAAO,SAAS,KAAK,yBAAyB,GAAG;AAC9F,QAAI,SAAS,MAAM;AACjB,sBAAgB,aAAa,SAAS,KAAK;AAAA,IAC7C;AACA,SAAK,aAAa,iBAAiB,cAAc,YAAY,cAAc,KAAK,UAAU;AAAA,EAC5F;AAEA,oBAAkB;AAClB,MAAI,2BAA2B,QAAQ;AACrC,+BAA2B,IAAI,UAAQ,KAAK,kBAAkB,CAAC;AAAA,EACjE,OAAO;AACL;AACE,UAAI,IAAI,MAAM,kBAAkB,WAAW,YAAY,EAAE,CAAC;AAAA,IAC5D;AAAA,EACF;AAEA,eAAa;AACf;AACA,IAAM,wBAAwB,CAAC,KAAK,SAAS,WAAW,0BAA0B;AAChF,MAAI,WAAW;AACb,cAAU,IAAI,CAAC,CAAC,OAAO,MAAM,MAAM,MAAM;AACvC,YAAM,SAAS,sBAAsB,KAAK,KAAK;AAC/C,YAAM,UAAU,kBAAkB,SAAS,MAAM;AACjD,YAAM,OAAO,iBAAiB,KAAK;AACnC,UAAI,IAAI,QAAQ,MAAM,SAAS,IAAI;AACnC,OAAC,QAAQ,gBAAgB,QAAQ,iBAAiB,CAAC,GAAG,KAAK,MAAM,IAAI,IAAI,QAAQ,MAAM,SAAS,IAAI,CAAC;AAAA,IACvG,CAAC;AAAA,EACH;AACF;AACA,IAAM,oBAAoB,CAAC,SAAS,eAAe,QAAM;AACvD,MAAI;AACF;AACE,UAAI,QAAQ,UAAU,KAAoC;AAExD,gBAAQ,eAAe,UAAU,EAAE,EAAE;AAAA,MACvC,OAAO;AACL,SAAC,QAAQ,oBAAoB,QAAQ,qBAAqB,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;AAAA,MACrF;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,iBAAa,CAAC;AAAA,EAChB;AACF;AACA,IAAM,wBAAwB,CAAC,KAAK,UAAU;AAC5C,MAAI,QAAQ,EAAuC,QAAO;AAC1D,MAAI,QAAQ,EAAqC,QAAO;AACxD,MAAI,QAAQ,GAAoC,QAAO,IAAI;AAC3D,SAAO;AACT;AAEA,IAAM,mBAAmB,YAAU,QAAQ,OAAoC;AAO/E,IAAM,WAAW,WAAS,IAAI,UAAU;AAKxC,IAAM,WAAwB,oBAAI,QAAQ;AAO1C,IAAM,aAAa,SAAO,SAAS,IAAI,GAAG;AAS1C,IAAM,mBAAmB,CAAC,cAAc,YAAY,SAAS,IAAI,QAAQ,iBAAiB,cAAc,OAAO;AAU/G,IAAM,eAAe,CAAC,aAAa,YAAY;AAC7C,QAAM,UAAU;AAAA,IACd,SAAS;AAAA,IACT,eAAe;AAAA,IACf,WAAW;AAAA,IACX,kBAAkB,oBAAI,IAAI;AAAA,EAC5B;AACA;AACE,YAAQ,sBAAsB,IAAI,QAAQ,OAAK,QAAQ,sBAAsB,CAAC;AAAA,EAChF;AACA;AACE,YAAQ,mBAAmB,IAAI,QAAQ,OAAK,QAAQ,mBAAmB,CAAC;AACxE,gBAAY,KAAK,IAAI,CAAC;AACtB,gBAAY,MAAM,IAAI,CAAC;AAAA,EACzB;AACA,wBAAsB,aAAa,SAAS,QAAQ,WAAW;AAC/D,SAAO,SAAS,IAAI,aAAa,OAAO;AAC1C;AACA,IAAM,oBAAoB,CAAC,KAAK,eAAe,cAAc;AAC7D,IAAM,eAAe,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,GAAG,EAAE;AACxD,IAAM,aAA0B,oBAAI,IAAI;AACxC,IAAM,aAAa,CAAC,SAAS,SAAS,iBAAiB;AAErD,QAAM,aAAa,QAAQ,UAAU,QAAQ,MAAM,GAAG;AACtD,QAAM,WAAW,QAAQ;AACzB,QAAM,SAAS,WAAW,IAAI,QAAQ;AACtC,MAAI,QAAQ;AACV,WAAO,OAAO,UAAU;AAAA,EAC1B;AAEA,wIAIA,yBAAK,QAAQ,YAAY,EAAE,IAAI,KAAK,oBAAkB;AACpD;AACE,iBAAW,IAAI,UAAU,cAAc;AAAA,IACzC;AACA,WAAO,eAAe,UAAU;AAAA,EAClC,GAAG,YAAY;AACjB;AACA,IAAM,SAAsB,oBAAI,IAAI;AACpC,IAAM,MAAM,OAAO,WAAW,cAAc,SAAS,CAAC;AACtD,IAAM,MAAM,IAAI,YAAY;AAAA,EAC1B,MAAM,CAAC;AACT;AACA,IAAM,MAAM;AAAA,EACV,SAAS;AAAA,EACT,gBAAgB;AAAA,EAChB,KAAK,CAAAC,OAAKA,GAAE;AAAA,EACZ,KAAK,CAAAA,OAAK,sBAAsBA,EAAC;AAAA,EACjC,KAAK,CAAC,IAAI,WAAW,UAAU,SAAS,GAAG,iBAAiB,WAAW,UAAU,IAAI;AAAA,EACrF,KAAK,CAAC,IAAI,WAAW,UAAU,SAAS,GAAG,oBAAoB,WAAW,UAAU,IAAI;AAAA,EACxF,IAAI,CAAC,WAAW,SAAS,IAAI,YAAY,WAAW,IAAI;AAC1D;AACA,IAAM,iBAAiB,OAAK,QAAQ,QAAQ,CAAC;AAC7C,IAAM,oCAAiD,MAAM;AAC3D,MAAI;AACF,QAAI,cAAc;AAClB,WAAO,OAAO,IAAI,cAAc,EAAE,gBAAgB;AAAA,EACpD,SAAS,GAAG;AAAA,EAAC;AACb,SAAO;AACT,GAAG;AACH,IAAM,gBAAgB,CAAC;AACvB,IAAM,iBAAiB,CAAC;AACxB,IAAM,YAAY,CAAC,OAAO,UAAU,QAAM;AACxC,QAAM,KAAK,EAAE;AACb,MAAI,CAAC,cAAc;AACjB,mBAAe;AACf,QAAI,SAAS,IAAI,UAAU,GAAkC;AAC3D,eAAS,KAAK;AAAA,IAChB,OAAO;AACL,UAAI,IAAI,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAM,UAAU,WAAS;AACvB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI;AACF,YAAM,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,IAC5B,SAAS,GAAG;AACV,mBAAa,CAAC;AAAA,IAChB;AAAA,EACF;AACA,QAAM,SAAS;AACjB;AACA,IAAM,QAAQ,MAAM;AAIlB,UAAQ,aAAa;AAErB;AACE,YAAQ,cAAc;AACtB,QAAI,eAAe,cAAc,SAAS,GAAG;AAG3C,UAAI,IAAI,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAM,WAAwB,QAAM,eAAe,EAAE,KAAK,EAAE;AAC5D,IAAM,YAAyB,UAAU,gBAAgB,IAAI;", "names": ["doc", "scopeId", "isSvgMode", "newVNode", "flags", "_a", "h"]}