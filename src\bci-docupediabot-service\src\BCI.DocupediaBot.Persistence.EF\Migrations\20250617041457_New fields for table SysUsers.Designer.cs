﻿
using System;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{
    [DbContext(typeof(DocupediaBotDbContext))]
    [Migration("20250617041457_New fields for table SysUsers")]
    partial class NewfieldsfortableSysUsers
    {

        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.ChatHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Answer")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CollectionId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Prompt")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransformedQuestion")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ChatHistory", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Collection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ChunkSize")
                        .HasColumnType("integer");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("EmbeddingModel")
                        .HasColumnType("integer");

                    b.Property<int?>("IntervalNumber")
                        .HasColumnType("integer");

                    b.Property<bool>("IsAutomaticUpdate")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<TimeOnly?>("UpdateTime")
                        .HasColumnType("time without time zone");

                    b.Property<int?>("UpdateType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Collection", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Content", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("EmbeddingVersionNo")
                        .HasColumnType("integer");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OriginContent")
                        .HasColumnType("text");

                    b.Property<string>("Path")
                        .HasColumnType("text");

                    b.Property<string>("ProcessedContent")
                        .HasColumnType("text");

                    b.Property<string>("SourceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("SourceModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SummarizedContent")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.Property<int>("VersionNo")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SourceId")
                        .IsUnique();

                    b.ToTable("Content", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.ContentsInPage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ContentId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("ContentsInPage", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Page", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsIncludeChild")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SourceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("SourceId")
                        .IsUnique();

                    b.ToTable("Page", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.PagesInCollection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CollectionId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsEmbedding")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("PagesInCollection", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.SysGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Size")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("SysGroup", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.SysUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DocupediaToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("FavCollecitonId")
                        .HasColumnType("uuid");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NTAcount")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("SysUser", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.SysUsersInGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("SysUsersInGroup", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
