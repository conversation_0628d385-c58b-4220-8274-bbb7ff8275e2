﻿using BCI.DocupediaBot.Domain.Enums;
using System;
using System.Text.Json.Serialization;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Chat
{
  public class ChatHistoryAddDTO
  {
    public required string Question { get; set; }

    public required Guid CollectionId { get; set; }

    public required string TransformedQuestion { get; set; }

    public required string Prompt { get; set; }

    public string Answer { get; set; }
  }
}
