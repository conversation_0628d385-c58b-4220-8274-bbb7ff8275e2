﻿using Bosch.Foundation.Exceptions;
using System;
using System.Runtime.Serialization;

namespace BCI.DocupediaBot.Infrastructure.Exceptions
{
  [Serializable]
  public class TenantIdEmptyException : SharedBaseException
  {
    private const int _defaultErrorCode = SharedErrorCodes.TenantNotFound;

    public TenantIdEmptyException(string message, int errorCode = _defaultErrorCode) : base(message, errorCode) { }

    public TenantIdEmptyException(string message, Exception innerException, int errorCode = _defaultErrorCode) : base(message, innerException, errorCode) { }

  }
}
