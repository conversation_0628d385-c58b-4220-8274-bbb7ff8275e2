﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.SysUser;
using BCI.DocupediaBot.Application.Services.SysUsersInGroup;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.SysUser
{
  public class SysUserService : ISysUserService
  {
    private readonly ISysUserRepository _sysUserRepository;
    private readonly ISysGroupRepository _sysGroupRepository;
    private readonly ISysUsersInGroupService _sysUsersInGroupService;
    private readonly IMapper _mapper;
    private readonly ILogger<SysUserService> _logger;

    public SysUserService(
      ISysUserRepository sysUserRepository,
      ISysGroupRepository sysGroupRepository,
      ISysUsersInGroupService sysUsersInGroupService,
      IMapper mapper,
      ILogger<SysUserService> logger)
    {
      _sysUserRepository = sysUserRepository ?? throw new ArgumentNullException(nameof(sysUserRepository));
      _sysGroupRepository = sysGroupRepository ?? throw new ArgumentNullException(nameof(sysGroupRepository));
      _sysUsersInGroupService = sysUsersInGroupService ?? throw new ArgumentNullException(nameof(sysUsersInGroupService));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ResponseResult> AddUserAsync(SysUserAddDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("AddUserAsync received null DTO.");
        return new ResponseResult { IsSuccess = false, Msg = "User data cannot be null." };
      }

      _logger.LogInformation("Adding new user with NT: {UserNTAccount}", dto.UserNTAccount);

      var user = _mapper.Map<Domain.Entities.SysUser>(dto);

      try
      {
        await _sysUserRepository.CreateAsync(user);
        _logger.LogInformation("User {UserNTAccount} added successfully with ID: {UserId}", user.UserNTAccount, user.Id);

        var defaultGroups = await _sysGroupRepository.QueryAsync(g => g.Name.ToLower() == "default");
        var defaultGroup = defaultGroups.FirstOrDefault();

        if (defaultGroup != null)
        {
          var assignResult = await _sysUsersInGroupService.AddMappingAsync(user.Id, defaultGroup.Id);
          if (assignResult.IsSuccess)
          {
            _logger.LogInformation("User {UserId} assigned to Default group successfully.", user.Id);
          }
          else
          {
            _logger.LogWarning("Failed to assign user {UserId} to Default group: {Error}", user.Id, assignResult.Msg);
          }
        }
        else
        {
          _logger.LogWarning("Default group not found, user {UserId} created without group assignment.", user.Id);
        }

        return new ResponseResult { IsSuccess = true, Msg = "User added successfully.", Data = user.Id };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to add user with UserNTAccount: {UserNTAccount}", dto.UserNTAccount);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to add user: {ex.Message}" };
      }
    }

    public async Task<ResponseResult> AssignUserToGroupAsync(Guid userId, Guid groupId)
    {
      _logger.LogInformation("Assigning user {UserId} to group {GroupId}", userId, groupId);

      var user = await _sysUserRepository.GetAsync(userId);
      if (user == null)
      {
        _logger.LogWarning("User not found for ID: {UserId}", userId);
        return new ResponseResult { IsSuccess = false, Msg = "User not found." };
      }

      var group = await _sysGroupRepository.GetAsync(groupId);
      if (group == null)
      {
        _logger.LogWarning("Group not found for ID: {GroupId}", groupId);
        return new ResponseResult { IsSuccess = false, Msg = "Group not found." };
      }

      try
      {
        var result = await _sysUsersInGroupService.AddMappingAsync(userId, groupId);
        if (result.IsSuccess)
        {
          _logger.LogInformation("User {UserId} assigned to group {GroupId} successfully.", userId, groupId);
        }
        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to assign user {UserId} to group {GroupId}", userId, groupId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to assign user to group: {ex.Message}" };
      }
    }

    public async Task<ResponseResult> DeleteUserByUserIdAsync(Guid userId)
    {
      _logger.LogInformation("Deleting user with ID: {UserId}", userId);

      var user = await _sysUserRepository.GetAsync(userId);
      if (user == null)
      {
        _logger.LogWarning("User not found for ID: {UserId}", userId);
        return new ResponseResult { IsSuccess = false, Msg = "User not found." };
      }

      try
      {

        var deleteResult = await _sysUsersInGroupService.DeleteMappingsByUserIdAsync(userId);
        if (!deleteResult.IsSuccess)
        {
          _logger.LogWarning("Failed to delete group mappings for user ID: {UserId}. Error: {Error}", userId, deleteResult.Msg);
          return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete group mappings: {deleteResult.Msg}" };
        }


        await _sysUserRepository.DeleteAsync(user);

        _logger.LogInformation("User {UserId} and its group mappings deleted successfully.", userId);
        return new ResponseResult { IsSuccess = true, Msg = "User and its group mappings deleted successfully." };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete user with ID: {UserId}", userId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete user: {ex.Message}" };
      }
    }

    public async Task<List<SysUserResponseDTO>> QueryUsersAsync()
    {
      _logger.LogInformation("Querying all users.");

      try
      {
        var users = await _sysUserRepository.GetAllAsync();
        if (!users.Any())
        {
          _logger.LogInformation("No users found in database.");
          return new List<SysUserResponseDTO>();
        }


        var userIds = users.Select(u => u.Id).ToList();
        var userGroupsDict = await _sysUsersInGroupService.QueryGroupIdsByUserIdsBatchAsync(userIds);


        var allGroupIds = userGroupsDict.Values.SelectMany(groupIds => groupIds).Distinct().ToList();
        var allGroups = allGroupIds.Any()
          ? await _sysGroupRepository.QueryAsync(g => allGroupIds.Contains(g.Id))
          : new List<Domain.Entities.SysGroup>();
        var groupDict = allGroups.ToDictionary(g => g.Id, g => g);

        var userDtos = new List<SysUserResponseDTO>();

        foreach (var user in users)
        {
          var userDto = _mapper.Map<SysUserResponseDTO>(user);


          if (userGroupsDict.TryGetValue(user.Id, out var groupIds) && groupIds.Any())
          {
            var groups = groupIds
              .Where(groupId => groupDict.ContainsKey(groupId))
              .Select(groupId => _mapper.Map<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>(groupDict[groupId]))
              .ToList();
            userDto.Groups = groups;
          }
          else
          {
            userDto.Groups = new List<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>();
          }

          userDtos.Add(userDto);
        }

        _logger.LogInformation("Retrieved {Count} users with group information.", userDtos.Count);
        return userDtos;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query users.");
        return new List<SysUserResponseDTO>();
      }
    }

    public async Task<List<SysUserResponseDTO>> QueryUsersByGroupIdAsync(Guid groupId)
    {
      _logger.LogInformation("Querying users by group ID: {GroupId}", groupId);

      try
      {
        var userIds = await _sysUsersInGroupService.QueryUserIdsByGroupIdAsync(groupId);
        if (userIds == null || !userIds.Any())
        {
          _logger.LogInformation("No users found for group ID: {GroupId}", groupId);
          return new List<SysUserResponseDTO>();
        }


        var users = await _sysUserRepository.QueryAsync(u => userIds.Contains(u.Id));
        var userDtos = users.Select(u => _mapper.Map<SysUserResponseDTO>(u)).ToList();

        _logger.LogInformation("Retrieved {Count} users for group ID: {GroupId}", userDtos.Count, groupId);
        return userDtos;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query users by group ID: {GroupId}", groupId);
        return new List<SysUserResponseDTO>();
      }
    }

    public async Task<SysUserResponseDTO?> QueryUserWithGroupsAsync(Guid userId)
    {
      _logger.LogInformation("Querying user with groups for ID: {UserId}", userId);

      var user = await _sysUserRepository.GetAsync(userId);
      if (user == null)
      {
        _logger.LogWarning("User not found for ID: {UserId}", userId);
        return null;
      }

      try
      {
        var groupIds = await _sysUsersInGroupService.QueryGroupIdsByUserIdAsync(userId);
        if (groupIds == null || !groupIds.Any())
        {
          _logger.LogInformation("No groups found for user ID: {UserId}", userId);
          var emptyDto = _mapper.Map<SysUserResponseDTO>(user);
          emptyDto.Groups = new List<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>();
          return emptyDto;
        }


        var groups = await _sysGroupRepository.QueryAsync(g => groupIds.Contains(g.Id));
        var groupDtos = groups.Select(g => _mapper.Map<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>(g))
                            .ToList();

        var userDto = _mapper.Map<SysUserResponseDTO>(user);
        userDto.Groups = groupDtos;

        _logger.LogInformation("Retrieved user {UserId} with {GroupCount} groups.", userId, groupDtos.Count);
        return userDto;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query user with groups for ID: {UserId}", userId);
        return null;
      }
    }

    public async Task<SysUserResponseDTO?> QueryUserByNTAccountAsync(string ntAccount)
    {
      _logger.LogInformation("Querying user by NT Account: {NTAccount}", ntAccount);

      if (string.IsNullOrWhiteSpace(ntAccount))
      {
        _logger.LogWarning("NT Account cannot be null or empty");
        return null;
      }

      try
      {
        var users = await _sysUserRepository.QueryAsync(u => u.UserNTAccount == ntAccount);
        var user = users.FirstOrDefault();

        if (user == null)
        {
          _logger.LogWarning("User not found for NT Account: {NTAccount}", ntAccount);
          return null;
        }

        var userDto = _mapper.Map<SysUserResponseDTO>(user);


        var groupIds = await _sysUsersInGroupService.QueryGroupIdsByUserIdAsync(user.Id);
        if (groupIds != null && groupIds.Any())
        {
          var groups = await _sysGroupRepository.QueryAsync(g => groupIds.Contains(g.Id));
          var groupDtos = groups.Select(g => _mapper.Map<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>(g))
                              .ToList();
          userDto.Groups = groupDtos;
        }
        else
        {
          userDto.Groups = new List<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>();
        }

        _logger.LogInformation("Retrieved user {UserId} for NT Account: {NTAccount}", user.Id, ntAccount);
        return userDto;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query user by NT Account: {NTAccount}", ntAccount);
        return null;
      }
    }

    public async Task<ResponseResult> RemoveUserFromGroupAsync(Guid userId, Guid groupId)
    {
      _logger.LogInformation("Removing user {UserId} from group {GroupId}", userId, groupId);

      var user = await _sysUserRepository.GetAsync(userId);
      if (user == null)
      {
        _logger.LogWarning("User not found for ID: {UserId}", userId);
        return new ResponseResult { IsSuccess = false, Msg = "User not found." };
      }

      var group = await _sysGroupRepository.GetAsync(groupId);
      if (group == null)
      {
        _logger.LogWarning("Group not found for ID: {GroupId}", groupId);
        return new ResponseResult { IsSuccess = false, Msg = "Group not found." };
      }

      try
      {
        var result = await _sysUsersInGroupService.DeleteMappingAsync(userId, groupId);
        if (result.IsSuccess)
        {
          _logger.LogInformation("User {UserId} removed from group {GroupId} successfully.", userId, groupId);
        }
        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to remove user {UserId} from group {GroupId}", userId, groupId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to remove user from group: {ex.Message}" };
      }
    }

    public async Task<ResponseResult> UpdateUserAsync(SysUserUpdateDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("UpdateUserAsync received null DTO.");
        return new ResponseResult { IsSuccess = false, Msg = "User data cannot be null." };
      }

      _logger.LogInformation("Updating user with ID: {UserId}", dto.Id);

      var user = await _sysUserRepository.GetAsync(dto.Id);
      if (user == null)
      {
        _logger.LogWarning("User not found for ID: {UserId}", dto.Id);
        return new ResponseResult { IsSuccess = false, Msg = $"User with ID {dto.Id} not found." };
      }

      try
      {
        _mapper.Map(dto, user);
        await _sysUserRepository.UpdateAsync(user);

        _logger.LogInformation("User {UserId} updated successfully.", dto.Id);
        return new ResponseResult { IsSuccess = true, Msg = "User updated successfully." };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update user with ID: {UserId}", dto.Id);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to update user: {ex.Message}" };
      }
    }

    public async Task<List<SysUserResponseDTO>> QueryUsersWithFilterAsync(SysUserFilterDTO filter)
    {
      _logger.LogInformation("Querying users with filter.");

      if (filter == null)
      {
        _logger.LogWarning("QueryUsersWithFilterAsync received null filter DTO.");
        return new List<SysUserResponseDTO>();
      }

      try
      {
        var users = await _sysUserRepository.GetAllAsync();


        var filteredUsers = users.AsQueryable();

        if (!string.IsNullOrEmpty(filter.UserNTAccount))
        {
          filteredUsers = filteredUsers.Where(u => u.UserNTAccount.Contains(filter.UserNTAccount, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(filter.UserName))
        {
          filteredUsers = filteredUsers.Where(u => u.UserName.Contains(filter.UserName, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(filter.GivenName))
        {
          filteredUsers = filteredUsers.Where(u => u.GivenName.Contains(filter.GivenName, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(filter.SN))
        {
          filteredUsers = filteredUsers.Where(u => u.SN.Contains(filter.SN, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(filter.Mail))
        {
          filteredUsers = filteredUsers.Where(u => u.Mail.Contains(filter.Mail, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(filter.Department))
        {
          filteredUsers = filteredUsers.Where(u => u.Department.Contains(filter.Department, StringComparison.OrdinalIgnoreCase));
        }

        var userList = filteredUsers.ToList();
        var userDtos = new List<SysUserResponseDTO>();

        foreach (var user in userList)
        {
          var userDto = _mapper.Map<SysUserResponseDTO>(user);


          var groupIds = await _sysUsersInGroupService.QueryGroupIdsByUserIdAsync(user.Id);
          if (groupIds != null && groupIds.Any())
          {
            var groups = await _sysGroupRepository.QueryAsync(g => groupIds.Contains(g.Id));
            var groupDtos = groups.Select(g => _mapper.Map<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>(g))
                                .ToList();
            userDto.Groups = groupDtos;
          }
          else
          {
            userDto.Groups = new List<BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup.SysGroupResponseDTO>();
          }

          userDtos.Add(userDto);
        }

        _logger.LogInformation("Retrieved {Count} users after applying filters.", userDtos.Count);
        return userDtos;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query users with filter.");
        return new List<SysUserResponseDTO>();
      }
    }
  }
}