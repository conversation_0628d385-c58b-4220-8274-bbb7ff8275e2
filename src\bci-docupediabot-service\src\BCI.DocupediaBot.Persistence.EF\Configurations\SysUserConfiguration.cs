﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class SysUserConfiguration : IEntityTypeConfiguration<SysUser>
  {
    public void Configure(EntityTypeBuilder<SysUser> entityBuilder)
    {
      entityBuilder.ToTable(nameof(SysUser));


      entityBuilder.HasIndex(x => x.UserNTAccount).IsUnique();
      entityBuilder.HasIndex(x => x.Status);
      entityBuilder.HasIndex(x => x.Mail);
      entityBuilder.HasIndex(x => x.Department);
      entityBuilder.HasIndex(x => x.FavCollecitonId);


      entityBuilder.HasIndex(x => new { x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.Creator, x.TenantId, x.Is<PERSON>eleted });
      entityBuilder.HasIndex(x => new { x.CreationTime, x.TenantId, x.IsDeleted });
    }
  }
}
