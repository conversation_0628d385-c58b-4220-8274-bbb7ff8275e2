using BCI.DocupediaBot.Application.Contracts.Dtos.QualityGate;
using BCI.DocupediaBot.Application.Services.ConfluenceUrl;
using BCI.DocupediaBot.Application.Services.SysUser;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.QualityGate
{
    public class QualityGateService : IQualityGateService
    {
        private readonly ILogger<QualityGateService> _logger;
        private readonly IConfluenceUrlService _confluenceUrlService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ICurrentUserAccessor _currentUserAccessor;
        private readonly ISysUserService _sysUserService;

        public QualityGateService(
            ILogger<QualityGateService> logger,
            IConfluenceUrlService confluenceUrlService,
            IHttpClientFactory httpClientFactory,
            ICurrentUserAccessor currentUserAccessor,
            ISysUserService sysUserService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _confluenceUrlService = confluenceUrlService ?? throw new ArgumentNullException(nameof(confluenceUrlService));
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
            _currentUserAccessor = currentUserAccessor ?? throw new ArgumentNullException(nameof(currentUserAccessor));
            _sysUserService = sysUserService ?? throw new ArgumentNullException(nameof(sysUserService));
        }

        public async Task<QualityGateResponseDTO> CopyQualityGatePagesAsync(QualityGateRequestDTO request)
        {
            try
            {
                _logger.LogInformation("Starting Quality Gate page copy for project: {ProjectTitle}", request.ProjectTitle);

                var currentUserId = _currentUserAccessor.UserId;
                if (string.IsNullOrEmpty(currentUserId))
                {
                    _logger.LogWarning("Unable to get current user ID for Quality Gate operation");
                    return new QualityGateResponseDTO
                    {
                        Success = false,
                        Message = "Unable to identify current user",
                        CopiedPages = new List<CopiedPageInfo>()
                    };
                }

                var currentUser = await _sysUserService.QueryUserByNTAccountAsync(currentUserId);
                if (currentUser == null || string.IsNullOrEmpty(currentUser.DocupediaToken))
                {
                    _logger.LogWarning("User {UserId} does not have a valid Docupedia token", currentUserId);
                    return new QualityGateResponseDTO
                    {
                        Success = false,
                        Message = "Please configure your Docupedia token in your profile",
                        CopiedPages = new List<CopiedPageInfo>()
                    };
                }

                var userToken = currentUser.DocupediaToken;
                var sourceUrl = ChatbotSettings.DataGeneration.QualityGate.SourceUrl;
                var targetUrl = ChatbotSettings.DataGeneration.QualityGate.TargetUrl;

                var sourcePageInfo = await _confluenceUrlService.ResolvePageInfoFromUrlAsync(sourceUrl, userToken);
                var targetPageInfo = await _confluenceUrlService.ResolvePageInfoFromUrlAsync(targetUrl, userToken);

                _logger.LogInformation("Source Page ID: {SourcePageId}, Target Page ID: {TargetPageId}",
                    sourcePageInfo.PageId, targetPageInfo.PageId);

                var sourceBaseUrl = _confluenceUrlService.ExtractBaseUrlFromUrl(sourceUrl);
                var childPages = await GetChildPagesAsync(sourcePageInfo.PageId, userToken, sourceBaseUrl);

                _logger.LogInformation("Found {Count} child pages to copy", childPages.Count);

                var copiedPages = new List<CopiedPageInfo>();
                var targetBaseUrl = _confluenceUrlService.ExtractBaseUrlFromUrl(targetUrl);

                var mainPageResult = await CopyPageAsync(
                    sourcePageInfo.PageId,
                    sourcePageInfo.Title,
                    targetPageInfo.PageId,
                    request.ProjectTitle,
                    userToken,
                    sourceBaseUrl,
                    targetBaseUrl);

                if (mainPageResult != null)
                {
                    copiedPages.Add(mainPageResult);
                }

                foreach (var childPage in childPages)
                {
                    var childResult = await CopyPageAsync(
                        childPage.Id,
                        childPage.Title,
                        mainPageResult?.NewPageId ?? targetPageInfo.PageId,
                        request.ProjectTitle,
                        userToken,
                        sourceBaseUrl,
                        targetBaseUrl);

                    if (childResult != null)
                    {
                        copiedPages.Add(childResult);
                    }
                }

                var response = new QualityGateResponseDTO
                {
                    Success = true,
                    Message = $"Succsss {copiedPages.Count} pages",
                    CopiedPages = copiedPages
                };

                _logger.LogInformation("Quality Gate page copy completed successfully for project: {ProjectTitle}", request.ProjectTitle);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy Quality Gate pages for project: {ProjectTitle}", request.ProjectTitle);

                return new QualityGateResponseDTO
                {
                    Success = false,
                    Message = $"Create failed: {ex.Message}",
                    CopiedPages = new List<CopiedPageInfo>()
                };
            }
        }

        private async Task<List<ChildPageInfo>> GetChildPagesAsync(string pageId, string userToken, string baseUrl)
        {
            var apiUrl = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.GetChildPages, pageId)}";

            using var httpClient = _httpClientFactory.CreateClient("ConfluenceClient");
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", userToken);

            try
            {
                var response = await httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                var responseJson = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var result = JsonSerializer.Deserialize<ChildPagesResult>(responseJson, options);

                return result?.Results ?? new List<ChildPageInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get child pages for page ID: {PageId}", pageId);
                return new List<ChildPageInfo>();
            }
        }

        private async Task<CopiedPageInfo?> CopyPageAsync(string sourcePageId, string originalTitle, string parentPageId,
            string projectTitle, string userToken, string sourceBaseUrl, string targetBaseUrl)
        {
            try
            {
                var sourceContent = await GetPageContentAsync(sourcePageId, userToken, sourceBaseUrl);
                if (sourceContent == null)
                {
                    _logger.LogWarning("Failed to get content for source page: {PageId}", sourcePageId);
                    return null;
                }

                var newTitle = $"{projectTitle} {originalTitle}";

                var newPageId = await CreatePageAsync(newTitle, sourceContent, parentPageId, userToken, targetBaseUrl);
                if (string.IsNullOrEmpty(newPageId))
                {
                    _logger.LogWarning("Failed to create new page for: {Title}", newTitle);
                    return null;
                }

                var newUrl = $"{targetBaseUrl}/pages/viewpage.action?pageId={newPageId}";

                return new CopiedPageInfo
                {
                    OriginalTitle = originalTitle,
                    NewTitle = newTitle,
                    NewUrl = newUrl,
                    NewPageId = newPageId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy page: {SourcePageId}", sourcePageId);
                return null;
            }
        }

        private async Task<string?> GetPageContentAsync(string pageId, string userToken, string baseUrl)
        {
            var apiUrl = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.GetContentbyId, pageId)}?expand=body.storage";

            using var httpClient = _httpClientFactory.CreateClient("ConfluenceClient");
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", userToken);

            try
            {
                var response = await httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                var responseJson = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var pageData = JsonSerializer.Deserialize<PageContentResult>(responseJson, options);

                return pageData?.Body?.Storage?.Value ?? "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get page content for page ID: {PageId}", pageId);
                return null;
            }
        }

        private async Task<string?> CreatePageAsync(string title, string content, string parentPageId, string userToken, string baseUrl)
        {
            var apiUrl = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{ChatbotSettings.Docupedia.API.CreatePage}";

            var targetUrl = ChatbotSettings.DataGeneration.QualityGate.TargetUrl;
            var spaceKey = ExtractSpaceKeyFromUrl(targetUrl);

            var pageData = new
            {
                type = "page",
                title = title,
                ancestors = new[] { new { id = parentPageId } },
                space = new { key = spaceKey },
                body = new
                {
                    storage = new
                    {
                        value = content,
                        representation = "storage"
                    }
                }
            };

            using var httpClient = _httpClientFactory.CreateClient("ConfluenceClient");
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", userToken);

            try
            {
                var json = JsonSerializer.Serialize(pageData);
                var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync(apiUrl, httpContent);
                response.EnsureSuccessStatusCode();

                var responseJson = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var result = JsonSerializer.Deserialize<CreatePageResult>(responseJson, options);

                return result?.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create page: {Title}", title);
                return null;
            }
        }

        private string ExtractSpaceKeyFromUrl(string url)
        {
            try
            {
                var uri = new Uri(url);
                var segments = uri.Segments;

                for (int i = 0; i < segments.Length; i++)
                {
                    if (segments[i].Equals("display/", StringComparison.OrdinalIgnoreCase) && i + 1 < segments.Length)
                    {
                        var spaceKey = segments[i + 1].TrimEnd('/');
                        return spaceKey;
                    }
                }

                _logger.LogWarning("Could not extract space key from URL: {Url}, using default BCIESWCN", url);
                return "BCIESWCN";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to extract space key from URL: {Url}, using default BCIESWCN", url);
                return "BCIESWCN";
            }
        }
    }
}
