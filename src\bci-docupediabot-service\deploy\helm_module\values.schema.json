{"$schema": "https://json-schema.org/draft-07/schema#", "description": "Check http://json-schema.org/understanding-json-schema/ for a good reference", "type": "object", "title": "Values for Module Display Name chart", "$defs": {"nexeedModuleEnabled": {"properties": {"global": {"properties": {"modules": {"properties": {"ModuleName": {"properties": {"enabled": {"const": true}}}}}}}}}}, "if": {"$ref": "#/$defs/nexeedModuleEnabled"}, "then": {"properties": {"global": {"type": "object", "properties": {"modules": {"type": "object", "properties": {"ModuleName": {"type": "object", "required": ["databaseEncryptionSecret"], "properties": {"databases": {"type": "object", "propertyNames": {"pattern": "^(ModuleName)$"}}, "enablePortalRegistration": {"type": "boolean"}, "enableMessagingIntegration": {"type": "boolean"}, "databaseEncryptionSecret": {"type": "string"}}}}}}}}}}