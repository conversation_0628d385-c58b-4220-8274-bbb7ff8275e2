﻿
using BCI.DocupediaBot.Infrastructure.Entities;
using BCI.DocupediaBot.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Infrastructure.Domain
{





  [ExcludeFromCodeCoverage]
  public class DomainService<T> : IDomainService<T> where T : class, IEntity
  {
    private readonly IEntityRepository<T> _repository;

    protected DomainService(IEntityRepository<T> repository) => _repository = repository;

    public IEntityRepository<T> Repository => _repository;

    public virtual async Task CreateAsync(T entity)
    {
      await _repository.CreateAsync(entity);
    }

    public virtual async Task UpdateAsync(T entity)
    {
      await _repository.UpdateAsync(entity);
    }

    public virtual async Task DeleteAsync(T entity)
    {
      await _repository.DeleteAsync(entity);
    }

    public virtual Task<T> GetAsync(object id)
    {
      return _repository.GetAsync(id);
    }

    public virtual Task<List<T>> GetAllAsync()
    {
      return _repository.GetAllAsync();
    }

    public virtual Task<List<T>> QueryAsync(Expression<Func<T, bool>> expression)
    {
      return _repository.QueryAsync(expression);
    }

    public Task<long> CountAsync(Expression<Func<T, bool>> expression)
    {
      return _repository.CountAsync(expression);
    }

    public Task<(List<T> Items, long TotalCount)> PageAsync(int? pageNumber, int? pageSize, Expression<Func<T, bool>> expression)
    {
      return _repository.PageAsync(pageNumber, pageSize, expression);
    }
    public virtual async Task CreateRangeAsync(IList<T> entities)
    {
      await _repository.CreateRangeAsync(entities);
    }

    public virtual async Task UpdateRangeAsync(IList<T> entities)
    {
      await _repository.UpdateRangeAsync(entities);
    }

    public virtual async Task DeleteRangeAsync(IList<T> entities)
    {
      await _repository.DeleteRangeAsync(entities);
    }
  }
}
