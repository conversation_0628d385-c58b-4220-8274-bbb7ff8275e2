@import "@bci-web-core/common-styles/sass/bci/bci-variables";

.group-table {
  width: 100%;
  margin-top: $grid-size * 2;
}

mat-header-cell,
mat-cell {
  padding: 8px 16px;
}


.mat-column-select {
  width: 60px;
  min-width: 60px;
  max-width: 60px;
}

.mat-column-name {
  width: 25%;
  min-width: 150px;
}

.mat-column-size {
  width: 10%;
  min-width: 80px;
}

.mat-column-currentUsers {
  width: 10%;
  min-width: 80px;
}

.text-center {
  text-align: center !important;
}

.mat-column-users {
  width: 35%;
  min-width: 200px;
}

.actions-header,
.actions-cell {
  width: 20%;
  min-width: 120px;
  max-width: 150px;
  justify-content: flex-end;
  padding-right: 8px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
}

.action-btn {
  width: 40px;
  height: 40px;

  &.view {
    color: #0080bc;
  }

  &.edit {
    color: #666;
  }

  &.delete {
    color: #d32f2f;
  }
}

.user-count {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: #e8f5e8;
  color: #2e7d32;

  &.full {
    background-color: #fff3e0;
    color: #f57c00;
  }
}

.users-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.user-tag {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #666;
}

.more-users {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $grid-size * 4;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $grid-size * 4;
  color: #666;
}

mat-row {
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }
}

mat-cell.actions-cell {
  overflow: visible;
}


@media screen and (max-width: 900px) {
  .group-table {
    .mat-column-users {
      display: none;
    }
  }
}

@media screen and (max-width: 600px) {
  .group-table {
    .mat-column-size,
    .mat-column-currentUsers {
      display: none;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .actions-header,
  .actions-cell {
    width: 50px;
    min-width: 50px;
    max-width: 50px;
  }
}
