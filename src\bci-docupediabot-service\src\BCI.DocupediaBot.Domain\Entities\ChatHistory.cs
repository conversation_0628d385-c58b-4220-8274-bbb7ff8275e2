﻿using BCI.DocupediaBot.Infrastructure.Entities;
using System;

namespace BCI.DocupediaBot.Domain.Entities
{
  public class ChatHistory : Entity
	{
		public string Question { get; set; } = default!;
		public Guid CollectionId { get; set; } = default!;
		public string TransformedQuestion { get; set; } = default!;
		public string Prompt { get; set; } = default!;
		public string Answer { get; set; } = default!;
	}
}
