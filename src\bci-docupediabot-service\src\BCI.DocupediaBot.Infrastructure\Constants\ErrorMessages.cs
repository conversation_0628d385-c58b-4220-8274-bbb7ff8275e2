﻿namespace BCI.DocupediaBot.Infrastructure.Constants
{
  public static class ErrorMessages
  {
    public const string UserMessageEmpty = "User message cannot be null or empty.";
    public const string CollectionDataEmpty = "Collection data cannot be null.";
    public const string CollectionIdEmpty = "Collection ID cannot be empty.";
    public const string PageDataEmpty = "Page data cannot be null.";
    public const string PageIdEmpty = "Page ID cannot be empty.";
    public const string PageOrCollectionIdEmpty = "Page ID or Collection ID cannot be empty.";
    public const string InternalServerError = "An error occurred while processing the request.";
    public const string UserIdEmpty = "User ID cannot be empty.";
    public const string GroupIdEmpty = "Group ID cannot be empty.";
    public const string UserDataEmpty = "User data cannot be null.";
    public const string InvalidUserUpdateRequest = "User ID in URL does not match the ID in the request body.";
    public const string InvalidUserGroupAssignment = "Invalid user-group assignment request.";
    public const string UserNotFound = "User not found.";
    public const string GroupDataEmpty = "Group data cannot be null.";
    public const string InvalidGroupUpdateRequest = "Group ID in URL does not match the ID in the request body.";
    public const string GroupNotFound = "Group not found.";
    public const string QualityGateDataEmpty = "Quality gate request data cannot be null.";
    public const string InvalidRequestData = "Invalid request data.";
  }
}