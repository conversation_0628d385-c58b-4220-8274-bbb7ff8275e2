{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-css.js"], "sourcesContent": ["(function (Prism) {\n  var string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n  Prism.languages.css = {\n    'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n    'atrule': {\n      pattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n      inside: {\n        'rule': /^@[\\w-]+/,\n        'selector-function-argument': {\n          pattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n          lookbehind: true,\n          alias: 'selector'\n        },\n        'keyword': {\n          pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n          lookbehind: true\n        }\n        // See rest below\n      }\n    },\n    'url': {\n      // https://drafts.csswg.org/css-values-3/#urls\n      pattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n      greedy: true,\n      inside: {\n        'function': /^url/i,\n        'punctuation': /^\\(|\\)$/,\n        'string': {\n          pattern: RegExp('^' + string.source + '$'),\n          alias: 'url'\n        }\n      }\n    },\n    'selector': {\n      pattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n      lookbehind: true\n    },\n    'string': {\n      pattern: string,\n      greedy: true\n    },\n    'property': {\n      pattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n      lookbehind: true\n    },\n    'important': /!important\\b/i,\n    'function': {\n      pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n      lookbehind: true\n    },\n    'punctuation': /[(){};:,]/\n  };\n  Prism.languages.css['atrule'].inside.rest = Prism.languages.css;\n  var markup = Prism.languages.markup;\n  if (markup) {\n    markup.tag.addInlined('style', 'css');\n    markup.tag.addAttribute('style', 'css');\n  }\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAChB,MAAI,SAAS;AACb,EAAAA,OAAM,UAAU,MAAM;AAAA,IACpB,WAAW;AAAA,IACX,UAAU;AAAA,MACR,SAAS,OAAO,eAAe,sBAAsB,SAAS,MAAM,OAAO,SAAS,QAAQ,kBAAkB,MAAM;AAAA,MACpH,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,8BAA8B;AAAA,UAC5B,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA;AAAA,MAEF;AAAA,IACF;AAAA,IACA,OAAO;AAAA;AAAA,MAEL,SAAS,OAAO,iBAAiB,OAAO,SAAS,MAAM,8BAA8B,SAAS,QAAQ,GAAG;AAAA,MACzG,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,UAAU;AAAA,UACR,SAAS,OAAO,MAAM,OAAO,SAAS,GAAG;AAAA,UACzC,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV,SAAS,OAAO,sDAAuD,OAAO,SAAS,eAAe;AAAA,MACtG,YAAY;AAAA,IACd;AAAA,IACA,UAAU;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,eAAe;AAAA,EACjB;AACA,EAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,OAAOA,OAAM,UAAU;AAC5D,MAAI,SAASA,OAAM,UAAU;AAC7B,MAAI,QAAQ;AACV,WAAO,IAAI,WAAW,SAAS,KAAK;AACpC,WAAO,IAAI,aAAa,SAAS,KAAK;AAAA,EACxC;AACF,GAAG,KAAK;", "names": ["Prism"]}