/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */
import { Injectable } from '@angular/core';
import { NotificationService } from '@bci-web-core/core';
import { environment } from 'src/environments/environment';
import { Notification } from './../models/notification.model';
import { IframeIntegrationLibrary } from '@bci-portal/iframe-integration';
import { NotificationType } from '@shared/enums/notification-type';

@Injectable({
  providedIn: 'root',
})
export class NotificationProvider {
  constructor(private notificationService: NotificationService) {}

  showNotification(notification: Notification): void {
    const isPortal = environment.portal;
    switch (notification.type) {
      case NotificationType.Success:
        if (isPortal) {
          IframeIntegrationLibrary.showMessage(notification.text, 'success', notification.messageDetails);
        } else {
          this.notificationService.success(
            notification.text,
            undefined,
            notification.duration,
            notification.messageDetails
          );
        }
        break;
      case NotificationType.Danger:
        if (isPortal) {
          IframeIntegrationLibrary.showMessage(notification.text, 'error', notification.messageDetails);
        } else {
          this.notificationService.error(
            notification.text,
            undefined,
            notification.duration,
            notification.messageDetails
          );
        }
        break;
      case NotificationType.Warning:
        if (isPortal) {
          IframeIntegrationLibrary.showMessage(notification.text, 'warning', notification.messageDetails);
        } else {
          this.notificationService.warning(
            notification.text,
            undefined,
            notification.duration,
            notification.messageDetails
          );
        }
        break;
      case NotificationType.Info:
        if (isPortal) {
          IframeIntegrationLibrary.showMessage(notification.text, 'info', notification.messageDetails);
        } else {
          this.notificationService.information(
            notification.text,
            undefined,
            notification.duration,
            notification.messageDetails
          );
        }
        break;
    }
  }
}
