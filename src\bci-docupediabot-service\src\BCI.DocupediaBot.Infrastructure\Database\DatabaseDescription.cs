﻿using Bosch.Foundation.Database.EfHelpers.Abstractions;
using System.Diagnostics.CodeAnalysis;

namespace BCI.DocupediaBot.Infrastructure.Database
{
  [ExcludeFromCodeCoverage]
  public class DatabaseDescription
  {
    public DatabaseDescription(DatasourceTypes type, string connectionString)
    {
      this.Type = type;
      this.ConnectionString = connectionString;
    }

    public DatabaseDescription(string dbProviderName, string connectionString)
    {
      dbProviderName = dbProviderName.ToUpper();
      DatasourceTypes databaseType = DatasourceTypes.MSSQL;
      switch (dbProviderName)
      {
        case "MSSQL":
          databaseType = DatasourceTypes.MSSQL;
          break;
        case "POSTGRES":
          databaseType = DatasourceTypes.POSTGRES;
          break;
        case "SQLITE":
          databaseType = DatasourceTypes.SQLITE;
          break;
        default:
          break;
      }

      this.Type = databaseType;
      this.ConnectionString = connectionString;
    }
    public DatasourceTypes Type { get; }

    public string ConnectionString { get; }
  }
}
