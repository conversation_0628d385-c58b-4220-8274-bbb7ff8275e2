
.language-switcher {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 24px;

  button {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}


.page-header {
  margin-bottom: 32px;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #333;
  }

  .page-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 24px;
  }

  .action-buttons {
    display: flex;
    gap: 16px;

    button {
      mat-icon {
        margin-right: 8px;
      }
    }
  }
}


.section-card {
  margin-bottom: 32px;

  mat-card-header {
    margin-bottom: 16px;

    mat-card-title {
      font-size: 1.5rem;
      font-weight: 600;
    }

    mat-card-subtitle {
      color: #666;
      margin-top: 8px;
    }
  }
}


.stats-row {
  display: flex;
  justify-content: space-around;
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #eee;

  .stat-item {
    text-align: center;

    strong {
      display: block;
      font-size: 1.8rem;
      font-weight: 700;
      color: #3f51b5;
      margin-bottom: 4px;
    }

    span {
      color: #666;
      font-size: 0.9rem;
    }
  }
}


.unique-features {
  margin-top: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3f51b5;

  h4 {
    margin: 0 0 16px 0;
    color: #3f51b5;
    font-weight: 600;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 8px;
      line-height: 1.5;

      strong {
        color: #333;
      }
    }
  }
}


.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;

  .feature-item {
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: #fafafa;

    .feature-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;

      .feature-icon {
        font-size: 24px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      h4 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
      }
    }

    p {
      margin: 0 0 12px 0;
      color: #666;
      line-height: 1.5;
    }

    .tech-details {
      small {
        color: #888;
        font-style: italic;
        line-height: 1.4;
      }
    }
  }
}


.tech-stack-table {
  .tech-table {
    width: 100%;

    .layer-description {
      font-size: 0.85rem;
      color: #666;
      margin-top: 4px;
      font-style: italic;
    }

    .tech-tags {
      .tech-chip {
        display: inline-block;
        margin: 2px 4px;
        padding: 4px 8px;
        background: #e3f2fd;
        border-radius: 12px;
        font-size: 0.8rem;
        color: #1976d2;
        border: 1px solid #bbdefb;
      }
    }
  }
}


.architecture-highlights {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #eee;

  h4 {
    color: #3f51b5;
    margin-bottom: 20px;
    font-weight: 600;
  }

  .highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .highlight-item {
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 3px solid #3f51b5;

      strong {
        display: block;
        color: #333;
        margin-bottom: 8px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
      }
    }
  }
}


.integrations-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;

  .integration-compact-item {
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    .integration-compact-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;

      .integration-icon {
        font-size: 24px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .integration-compact-info {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        h4 {
          margin: 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: #333;
        }

        .status-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 12px;
          color: white;
          font-size: 0.75rem;
          font-weight: 500;
        }
      }
    }

    .integration-description {
      margin: 0 0 12px 0;
      color: #666;
      font-size: 0.9rem;
    }

    .feature-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .feature-tag {
        display: inline-block;
        padding: 4px 8px;
        background: #e8f5e8;
        color: #2e7d32;
        border-radius: 12px;
        font-size: 0.75rem;
        border: 1px solid #c8e6c9;
      }
    }
  }
}


.metrics-table {
  .performance-table {
    width: 100%;

    .metric-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }

    .improvement-badge {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #4caf50;

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}


.comparison-table {
  overflow-x: auto;

  .comparison-mat-table {
    width: 100%;

    .rating-stars {
      display: flex;
      gap: 2px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}


.scenarios-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;

  .scenario-category {
    h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #3f51b5;
      margin-bottom: 24px;
      font-weight: 600;
      font-size: 1.3rem;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }

    .scenario-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;

      .scenario-item {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        background: #fafafa;

        h5 {
          color: #333;
          margin: 0 0 12px 0;
          font-weight: 600;
          font-size: 1.1rem;
        }

        p {
          margin: 0 0 16px 0;
          color: #666;
          font-size: 0.9rem;
        }

        .scenario-dialog {
          background: white;
          border-radius: 8px;
          padding: 16px;
          border-left: 4px solid #3f51b5;

          .user-question {
            background: #e3f2fd;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 12px;
            font-weight: 500;
            color: #1976d2;
          }

          .system-answer {
            background: #f1f8e9;
            padding: 12px;
            border-radius: 6px;
            color: #388e3c;
            line-height: 1.5;

            strong {
              color: #2e7d32;
            }
          }
        }
      }
    }
  }


  .usage-stats {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;

    h4 {
      color: #3f51b5;
      margin-bottom: 20px;
      font-weight: 600;
      text-align: center;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;

      .stat-item {
        text-align: center;
        padding: 16px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e0e0e0;

        strong {
          display: block;
          font-size: 1.4rem;
          font-weight: 700;
          color: #3f51b5;
          margin-bottom: 4px;
        }

        span {
          color: #666;
          font-size: 0.85rem;
        }
      }
    }
  }
}


.roadmap-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;

  .roadmap-phase {
    h4 {
      color: #3f51b5;
      margin-bottom: 16px;
      font-weight: 600;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 12px;

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
          margin-top: 2px;
          color: #3f51b5;
        }
      }
    }
  }
}


.value-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;

  .value-category {
    h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #333;
      margin-bottom: 16px;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: 8px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        strong {
          color: #3f51b5;
        }
      }
    }
  }
}


.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;

  .contact-item {
    text-align: center;

    h4 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: #333;
      margin-bottom: 12px;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }

    p {
      margin: 4px 0;
      color: #666;

      &:first-of-type {
        font-weight: 500;
        color: #333;
      }
    }
  }
}


.footer-section {
  text-align: center;
  margin-top: 48px;
  padding: 24px 0;
  border-top: 1px solid #eee;

  p {
    margin: 8px 0;
    color: #666;

    &.copyright {
      font-size: 0.9rem;
      color: #999;
    }
  }
}


@media (max-width: 768px) {
  .page-header {
    .action-buttons {
      flex-direction: column;

      button {
        width: 100%;
      }
    }
  }

  .stats-row {
    flex-direction: column;
    gap: 16px;
  }

  .features-grid,
  .roadmap-grid,
  .value-grid,
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .scenarios-grid {
    .scenario-category {
      .scenario-examples {
        grid-template-columns: 1fr;

        .scenario-item {
          .scenario-dialog {
            .user-question,
            .system-answer {
              font-size: 0.85rem;
            }
          }
        }
      }
    }
  }
}


.tech-flow-container {
  .flow-section {
    margin-bottom: 32px;

    h4 {
      color: #3f51b5;
      margin-bottom: 20px;
      font-weight: 600;
      font-size: 1.2rem;
    }

    .flow-steps-horizontal {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e0e0e0;

      .flow-step-compact {
        flex: 1;
        text-align: center;
        padding: 16px 12px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .step-number {
          width: 28px;
          height: 28px;
          background: #3f51b5;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1rem;
          margin: 0 auto 8px auto;
        }

        h5 {
          margin: 0 0 8px 0;
          color: #333;
          font-weight: 600;
          font-size: 1rem;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 0.8rem;
          line-height: 1.3;
        }
      }

      .flow-arrow {
        color: #3f51b5;
        font-size: 1.5rem;
        font-weight: bold;
        flex-shrink: 0;
      }
    }
  }
}


.special-features {
  .feature-showcase {
    margin-bottom: 40px;
    padding-bottom: 32px;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
    }

    h4 {
      color: #3f51b5;
      margin-bottom: 20px;
      font-weight: 600;
      font-size: 1.2rem;
    }

    .feature-demo {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px;
      align-items: start;

      .demo-description {
        p {
          margin: 0 0 16px 0;
          color: #666;
          line-height: 1.5;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            color: #555;
            line-height: 1.4;

            strong {
              color: #333;
            }
          }
        }
      }

      .demo-example {
        .summary-card {
          background: #f8f9fa;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;

          h5 {
            margin: 0 0 12px 0;
            color: #333;
            font-weight: 600;
          }

          .summary-stats {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .stat {
              font-size: 0.85rem;
              color: #555;
              padding: 4px 0;
            }
          }
        }

        .duplicate-detection {
          background: #fff3e0;
          border: 1px solid #ffcc02;
          border-radius: 8px;
          padding: 16px;

          h5 {
            margin: 0 0 12px 0;
            color: #e65100;
            font-weight: 600;
          }

          .duplicate-item {
            .similarity-score {
              background: #ff9800;
              color: white;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 0.8rem;
              font-weight: 500;
              display: inline-block;
              margin-bottom: 8px;
            }

            .doc-pair {
              display: flex;
              flex-direction: column;
              gap: 4px;
              margin-bottom: 8px;

              span {
                font-size: 0.85rem;
                color: #555;
              }
            }

            .suggestion {
              font-size: 0.85rem;
              color: #2e7d32;
              font-style: italic;
            }
          }
        }

        .knowledge-graph {
          background: #e8f5e8;
          border: 1px solid #4caf50;
          border-radius: 8px;
          padding: 16px;

          h5 {
            margin: 0 0 12px 0;
            color: #2e7d32;
            font-weight: 600;
          }

          .graph-nodes {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;

            .node {
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 0.8rem;
              font-weight: 500;

              &.primary {
                background: #4caf50;
                color: white;
              }

              &:not(.primary):not(.missing) {
                background: #c8e6c9;
                color: #2e7d32;
              }

              &.missing {
                background: #ffebee;
                color: #c62828;
                border: 1px dashed #f44336;
              }
            }
          }
        }
      }
    }
  }
}


@media print {
  .page-header .action-buttons {
    display: none;
  }

  .section-card {
    break-inside: avoid;
    margin-bottom: 24px;
  }
}


@media (max-width: 768px) {
  .tech-flow-container {
    .flow-section {
      .flow-steps-horizontal {
        flex-direction: column;
        gap: 12px;

        .flow-step-compact {
          min-height: auto;
          padding: 12px;
        }

        .flow-arrow {
          transform: rotate(90deg);
          font-size: 1.2rem;
        }
      }
    }
  }

  .special-features {
    .feature-showcase {
      .feature-demo {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }
  }
}
