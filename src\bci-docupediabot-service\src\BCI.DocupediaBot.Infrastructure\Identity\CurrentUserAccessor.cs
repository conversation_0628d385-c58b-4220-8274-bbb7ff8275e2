﻿using BCI.DocupediaBot.Infrastructure.Abstractions;
using Microsoft.AspNetCore.Http;
using System.Linq;

namespace BCI.DocupediaBot.Infrastructure.Identity
{
  public class CurrentUserAccessor : ICurrentUserAccessor
  {
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CurrentUserAccessor(IHttpContextAccessor httpContextAccessor)
    {
      _httpContextAccessor = httpContextAccessor;
    }

    public string UserId => GetClaimValue("userid");
    public string UserName => GetClaimValue("username");
    public string Mail => GetClaimValue("mail");
    public string Department => GetClaimValue("department");
    public string GivenName => GetClaimValue("givenname");
    public string SurName => GetClaimValue("surname");

    private string GetClaimValue(string claimType)
    {
      return _httpContextAccessor.HttpContext?.User.Claims?.FirstOrDefault(x => x.Type == claimType)?.Value ?? string.Empty;
    }

  }
}