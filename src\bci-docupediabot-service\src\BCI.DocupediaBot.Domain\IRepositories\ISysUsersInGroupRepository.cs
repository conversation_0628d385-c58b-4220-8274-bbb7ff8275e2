﻿using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Domain.IRepositories
{
  public interface ISysUsersInGroupRepository : IEntityRepository<SysUsersInGroup>
  {

    Task<Dictionary<Guid, List<Guid>>> GetGroupIdsByUserIdsBatchAsync(IEnumerable<Guid> userIds);
    Task<Dictionary<Guid, List<Guid>>> GetUserIdsByGroupIdsBatchAsync(IEnumerable<Guid> groupIds);
    Task<List<Guid>> GetGroupIdsByUserIdAsync(Guid userId);
    Task<List<Guid>> GetUserIdsByGroupIdAsync(Guid groupId);
    Task<bool> ExistsMappingAsync(Guid userId, Guid groupId);
  }
}
