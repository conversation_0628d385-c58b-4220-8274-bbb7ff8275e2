﻿using BCI.DocupediaBot.Infrastructure.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Infrastructure.IRepositories
{
  public interface IRepository
  {
  }

  public interface IEntityRepository<T> : IRepository where T : IEntity
  {
    Task CreateAsync(T entity);

    Task UpdateAsync(T entity);

    Task DeleteAsync(T entity);

    Task<List<T>> GetAllAsync(bool noTracing = true);

    Task<T> GetAsync(object id);

    Task<List<T>> QueryAsync(Expression<Func<T, bool>> expression, bool noTracing = true);

    Task<long> CountAsync(Expression<Func<T, bool>> expression, bool noTracing = true);

    Task<(List<T> Items, long TotalCount)> PageAsync(int? pageNumber, int? pageSize, Expression<Func<T, bool>> expression, bool noTracing = true);

    Task CreateRangeAsync(IList<T> entities);
    Task UpdateRangeAsync(IList<T> entities);
    Task DeleteRangeAsync(IList<T> entities);
  }
}
