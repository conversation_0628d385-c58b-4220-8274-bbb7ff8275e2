import { Injectable } from '@angular/core';
import { SidebarNavItem } from '@bci-web-core/core';
import { PermissionService } from './permission.service';

@Injectable({
  providedIn: 'root'
})
export class NavigationFilterService {

  constructor(private permissionService: PermissionService) {}

  /**
   * 根据用户权限过滤导航项目
   */
  filterNavigationItems(navigationItems: SidebarNavItem[]): SidebarNavItem[] {
    return navigationItems.filter(item => this.shouldShowNavigationItem(item));
  }

  /**
   * 检查是否应该显示导航项目
   */
  private shouldShowNavigationItem(item: SidebarNavItem): boolean {
    if (item.title === 'System') {
      return this.permissionService.canAccessSystemMenu();
    }


    return true;
  }
}
