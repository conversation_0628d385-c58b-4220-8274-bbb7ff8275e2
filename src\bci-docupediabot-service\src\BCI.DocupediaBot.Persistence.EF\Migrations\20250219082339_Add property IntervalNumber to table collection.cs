﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{

    public partial class AddpropertyIntervalNumbertotablecollection : Migration
    {

        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "UpdateCustomizeNum",
                table: "Collection",
                newName: "IntervalNumber");
        }


        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IntervalNumber",
                table: "Collection",
                newName: "UpdateCustomizeNum");
        }
    }
}
