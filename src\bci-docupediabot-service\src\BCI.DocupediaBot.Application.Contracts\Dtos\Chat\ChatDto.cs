﻿using BCI.DocupediaBot.Domain.Enums;
using System;
using System.Text.Json.Serialization;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Chat
{
  public class ChatDTO
  {
    public required ChatModel ChatModel { get; set; }

    public required EmbeddingModel EmbeddingModel { get; set; }

    public required string UserMessage { get; set; }

    public required Guid CollectionId { get; set; }

    public string Context { get; set; }
  }

  public class ContextItem
  {
    [JsonPropertyName("type")]
    public string Type { get; set; }

    [JsonPropertyName("content")]
    public string Content { get; set; }
  }
}
