﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class SysGroupConfiguration : IEntityTypeConfiguration<SysGroup>
  {
    public void Configure(EntityTypeBuilder<SysGroup> entityBuilder)
    {
      entityBuilder.ToTable(nameof(SysGroup));


      entityBuilder.HasIndex(x => x.Name);
      entityBuilder.HasIndex(x => x.Size);


      entityBuilder.HasIndex(x => new { x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.Creator, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.CreationTime, x.TenantId, x.IsDeleted });
		}
	}
}
