﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Chat;
using BCI.DocupediaBot.Application.Services.Chat;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Infrastructure.Constants;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.UIService.Controllers
{
  [Authorize]
  [Route("api/chat")]
	[ApiController]
	public class ChatController : ControllerBase
	{
		private readonly IChatService _chatService;
		private readonly ILogger<ChatController> _logger;

		public ChatController(IChatService chatService, ILogger<ChatController> logger)
		{
			_chatService = chatService ?? throw new ArgumentNullException(nameof(chatService));
			_logger = logger ?? throw new ArgumentNullException(nameof(logger));
		}

		[HttpPost("ask")]
		[Produces("application/json")]
		public async Task<IActionResult> AskQuestionAsync([FromBody] ChatDTO chat)
		{
			if (chat == null || string.IsNullOrEmpty(chat.UserMessage))
			{
				_logger.LogWarning("Invalid chat request: {Message}", ErrorMessages.UserMessageEmpty);
				return BadRequest(ApiResponse<string>.Error(ErrorMessages.UserMessageEmpty));
			}

			try
			{
				string result = await _chatService.AskQuestionAsync(chat);
				return Ok(ApiResponse<string>.Ok(result));
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to process chat request: {Message}", chat.UserMessage);
				return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
			}
		}
	}
}