<bci-modal-component
  title="Assign Groups"
  [closeIcon]="true"
  (closeHandler)="close()">
  <p class="modal-subheading">
    <span *ngIf="mode === 'single'">Assign or remove groups for user: <strong>{{ users[0].userName }}</strong></span>
    <span *ngIf="mode === 'batch'">Assign or remove groups for <strong>{{ users.length }}</strong> selected users</span>
  </p>

  <div class="groups-container">
    <h6>Available Groups</h6>
    <div *ngIf="loading" class="loading-container">
      <mat-spinner diameter="30"></mat-spinner>
    </div>

    <div *ngIf="!loading" class="groups-list">
      <div *ngFor="let group of allGroups" class="group-item">
        <mat-checkbox
          [checked]="isGroupAssigned(group.id)"
          (change)="toggleGroupAssignment(group, $event.checked)">
          <div class="group-info">
            <span class="group-name">{{ group.name }}</span>
            <span class="group-details">({{ group.users.length }}/{{ group.size }} users)</span>
          </div>
        </mat-checkbox>
      </div>
    </div>

    <div *ngIf="!loading && allGroups.length === 0" class="no-groups">
      No groups available.
    </div>
  </div>

  <div class="subpage-actions">
    <button type="button" bciPrimaryButton
            [disabled]="saving"
            (click)="save()">
      {{ saving ? 'Saving...' : 'Save Changes' }}
    </button>
    <button type="button" bciSecondaryButton
            (click)="close()">
      Cancel
    </button>
  </div>
</bci-modal-component>
