﻿using BCI.DocupediaBot.Infrastructure.Configuration;
using System.Collections.Generic;
using System;
using System.Text;
using System.Linq;
using FastText.NetWrapper;
using System.IO;


namespace BCI.DocupediaBot.Infrastructure.Extensions
{
  public static class PromptUtility
  {
    public static string BuildChatPrompt(string question, string knowledgeBase, string context, string originalQuestion)
    {
      string lang = DetectLanguage(originalQuestion);

      var sb = new StringBuilder();

      sb.AppendLine("=== 严格遵守下面的规则，仅输出最终结果，不展示推理过程 ===");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine("规则1：涉及计算时，完整检索知识库数据，包含表格的所有行和列，并需要展示计算过程。");
      sb.AppendLine("规则2：对于这些标记，[Image], [Table], [Code], [List], [Panel]，需要严格按照以下格式转换成Html的标签：");
      sb.AppendLine("- [Image]：转换为 <img> 标签，使用 alt 属性判断和问题的相关性，如果无法确定，默认有关联。" +
        "比如：[IMAGE: /confluence/download/attachments/5742069620/image-2025-5-19_15-10-26.png?version=1&amp;modificationDate=1747638626000&amp;api=v2 | ALT: ] " +
        "需要转换成 <img src='/confluence/download/attachments/5742069620/image-2025-5-19_15-10-26.png?version=1&amp;modificationDate=1747638626000&amp;api=v2' />");
      sb.AppendLine("- [Table]：转换为 <table><tr><th><td> 标签，去除所有多余换行符。");
      sb.AppendLine("- [Code]：转换为 <pre><code class=\"language-{type}\"> 标签，标注语言类型。");
      sb.AppendLine("- [List]：转换为 <ul><li> 标签。");
      sb.AppendLine("- [Panel]：转换为 <div class=\"panel {type}\"> 标签，{type} 根据元数据 type 属性（如 info, warning）确定，使用 title 或 content 属性判断和问题的相关性，如果无法确定，默认有关联。");
      sb.AppendLine("规则3：回答中请尽量包含相关的图片，表格，列表。注意回答的格式，需要比较高的可读性。");
      sb.AppendLine("规则4：所有包含图片格式的的，如包含(png, jpeg,webp)的链接都认为是图片，转换为 <img> 标签，使用 alt 属性判断和问题的相关性，如果无法确定，默认有关联。");
      sb.AppendLine("-------------------------------------");

      sb.AppendLine("=== 下面是提供给你的内容 ===");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine($"用户问题：{question}");
      sb.AppendLine($"历史对话：{context}");
      sb.AppendLine($"知识库（[Metadata] 里面的内容）：{knowledgeBase}");
      sb.AppendLine("-------------------------------------");

      sb.AppendLine($"=== 下面是你的输出格式，都使用 {lang} 回答 ===");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine("首先请简单描述问题和你的理解，然后尽可能详细的，完全基于提供的知识库回答问题，结构化输出你的回答。" +
        "如果知识库里面没有明确的内容，禁止使用通用知识来回答，也不要猜测性的回答，直接回复：“当前选择的知识库不包含相关内容，请确认是否选择了正确的 Collection”。" +
        "特别注意：如果问题中包含资料，信息，文档，模板等关键词，请优先回答相关的图片和链接。" +
        "特别注意：最后检查你回答的文字部分是否和问题相关，修正回答，保证准确性。");
      sb.AppendLine("最后根据提供的[Title], [Url], [Score]，显示 [Score] 最高的3条记录，每条记录都要换行显示，每条记录标题不可以相同，示例如下：");
      sb.AppendLine("相关知识库： <br>");
      sb.AppendLine("[Title: {title}] - [Url: {url}] - [Score: {score}]");
      sb.AppendLine("-------------------------------------");

      return sb.ToString();
    }

    private static string DetectLanguage(string question)
    {
      using (var fastText = new FastTextWrapper())
      {
        string modelPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Lang", "lid.176.bin");
        fastText.LoadModel(modelPath);

        var result = fastText.PredictSingle(question);
        string language = result.Label.Replace("__label__", "");
        return MapFastTextLanguage(language);
      }
    }

    private static string MapFastTextLanguage(string fastTextLang)
    {
      var langMap = new Dictionary<string, string>
      {
          { "zh", "中文" },
          { "ja", "日语" },
          { "ko", "韩语" },
          { "de", "德语" },
          { "en", "英文" },
          { "fr", "法语" },
          { "es", "西班牙语" },
          { "ru", "俄语" },
          { "it", "意大利语" },
          { "pt", "葡萄牙语" },
          { "ar", "阿拉伯语" },
          { "hi", "印地语" },
          { "bn", "孟加拉语" },
          { "vi", "越南语" },
          { "th", "泰语" }
      };
      return langMap.TryGetValue(fastTextLang, out var lang) ? lang : "中文";
    }

    public static string BuildDuplicatesPrompt(string duplicates)
    {
      throw new NotImplementedException();
    }

    public static string BuildRecentDocumentsPrompt(string recentDocs)
    {
      throw new NotImplementedException();
    }

    public static string BuildSummaryPrompt(string summaries)
    {
      var sb = new StringBuilder();


      sb.AppendLine("=== 规则说明 ===");
      sb.AppendLine("严格遵循以下规则生成回答，仅输出最终结果，不展示推理过程。");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine("规则1：全面分析输入的总结内容，识别所有条目，整理重复项，补全缺失信息，确保逻辑清晰且不遗漏关键细节。");
      sb.AppendLine("规则2：回答需基于输入内容，生成结构化的总结，按分类或步骤组织，语言专业但通俗易懂，避免无关内容。");
      sb.AppendLine("规则3：严禁使用 markdown 代码块标记。");
      sb.AppendLine("-------------------------------------");


      sb.AppendLine("=== 输入内容 ===");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine($"用户提供的总结内容：{summaries}");
      sb.AppendLine("-------------------------------------");


      sb.AppendLine("=== 输出格式 ===");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine("分析用户提供的总结，识别核心主题，整理重复或零散条目，补全缺失的上下文或步骤，生成一个逻辑清晰、结构化的总结。");
      sb.AppendLine("回答字数控制在 1500 字以内，除非明确要求更长，确保简洁高效。");
      sb.AppendLine("-------------------------------------");

      return sb.ToString();
    }

    public static string BuildTransformPrompt(string rawQuestion, string historyQuestion, QueryOptions options)
    {
      var sb = new StringBuilder();


      sb.AppendLine("=== 处理步骤 ===");
      sb.AppendLine("严格遵循以下步骤生成回答，仅输出最终结果，不展示推理过程。");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine("步骤1：判断上下文关系：分析历史问题和当前问题是否语义关联（合并后语义是否通顺）。若有关联，合并当前问题和历史问题；若无，仅处理当前问题。");
      if (options.UseSpelling)
        sb.AppendLine("步骤2：纠正拼写错误：对中文处理错别字，对英文处理拼写错误，确保语义不变，输出纠正后的结果。");
      if (options.UseSimple)
        sb.AppendLine("步骤3：简化语句：去除语气词、冗余词，将疑问句转为简单句，确保语义准确，输出处理后的结果。");
      if (options.UseRewrite)
        sb.AppendLine("步骤4：转化为陈述句：识别语义，将问题转为简洁陈述式查询，去除疑问形式及句末的'?'和';'，输出转化后的结果。");
      if (options.UseMultiLanguage)
        sb.AppendLine("步骤5：翻译处理：判断主要语言，若为中文则翻译成英文，若为英文则翻译成中文，确保翻译自然且语义一致，输出翻译结果。");
      sb.AppendLine("步骤6：提取关键词：提取核心关键词，进行同义词扩展，保留5个最相关关键词（中英文各占一半，若语言单一则仅输出该语言），输出关键词列表。");
      sb.AppendLine("-------------------------------------");


      sb.AppendLine("=== 输入内容 ===");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine($"当前问题：{rawQuestion}");
      sb.AppendLine($"历史问题：{historyQuestion}");
      sb.AppendLine("-------------------------------------");


      sb.AppendLine("=== 输出格式 ===");
      sb.AppendLine("-------------------------------------");
      sb.AppendLine("按步骤顺序输出每次处理后的结果，每步用'步骤X:'开头。");
      sb.AppendLine("最后一行用'优化结果:'开头，用'|'连接以下内容：步骤1的结果(True/False)、陈述句版本、翻译版本、关键词列表。示例：");
      sb.AppendLine("优化结果:True|为供应商申请笔记本电脑并确认其配置|Apply for a laptop for the supplier and confirm its configuration|申请, 笔记本, 供应商, apply, laptop");
      sb.AppendLine("-------------------------------------");

      return sb.ToString();
    }

    public static List<string> ExtractQuestions(string transformedQuestion)
    {
      const string prefix = "优化结果:";
      if (!transformedQuestion.Contains(prefix))
        throw new ArgumentException("Transformed question format is invalid.");

      return transformedQuestion.Split(prefix)[1]
              .Split("|", StringSplitOptions.RemoveEmptyEntries)
              .Select(q => q.Trim())
              .Where(q => !string.IsNullOrEmpty(q))
              .ToList();
    }
  }
}
