﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Collection;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Application.Services.SysUsersInGroup;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Qdrant.Client;
using Qdrant.Client.Grpc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Collection
{
  public class CollectionService : ICollectionService
	{
		private readonly ICollectionRepository _collectionRepository;
		private readonly IPageRepository _pageRepository;
		private readonly IPagesInCollectionService _pagesInCollectionService;
		private readonly IMapper _mapper;
		private readonly QdrantClient _qdrantClient;
		private readonly ILogger<CollectionService> _logger;
		private readonly ISysUserRepository _sysUserRepository;
		private readonly ISysGroupRepository _sysGroupRepository;
		private readonly ISysUsersInGroupService _sysUsersInGroupService;
		private readonly ICurrentUserAccessor _currentUserAccessor;

		public CollectionService(
				ICollectionRepository collectionRepository,
				IPageRepository pageRepository,
				IPagesInCollectionService pagesInCollectionService,
				IMapper mapper,
				QdrantClient qdrantClient,
				ILogger<CollectionService> logger,
				ISysUserRepository sysUserRepository,
				ISysGroupRepository sysGroupRepository,
				ISysUsersInGroupService sysUsersInGroupService,
				ICurrentUserAccessor currentUserAccessor)
		{
			_collectionRepository = collectionRepository ?? throw new ArgumentNullException(nameof(collectionRepository));
			_pageRepository = pageRepository ?? throw new ArgumentNullException(nameof(pageRepository));
			_pagesInCollectionService = pagesInCollectionService ?? throw new ArgumentNullException(nameof(pagesInCollectionService));
			_mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
			_qdrantClient = qdrantClient ?? throw new ArgumentNullException(nameof(qdrantClient));
			_logger = logger ?? throw new ArgumentNullException(nameof(logger));
			_sysUserRepository = sysUserRepository ?? throw new ArgumentNullException(nameof(sysUserRepository));
			_sysGroupRepository = sysGroupRepository ?? throw new ArgumentNullException(nameof(sysGroupRepository));
			_sysUsersInGroupService = sysUsersInGroupService ?? throw new ArgumentNullException(nameof(sysUsersInGroupService));
			_currentUserAccessor = currentUserAccessor ?? throw new ArgumentNullException(nameof(currentUserAccessor));
		}

    public async Task<ResponseResult> AddCollectionAsync(CollectionAddDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("AddCollectionAsync received null DTO.");
        return new ResponseResult { IsSuccess = false, Msg = "Collection data cannot be null." };
      }

      _logger.LogInformation("Adding new collection with name: {Name}", dto.Name);

      var collection = _mapper.Map<Domain.Entities.Collection>(dto);
      try
      {
        await _collectionRepository.CreateAsync(collection);
      }
      catch (Exception ee)
      {
        throw;
      }


      try
      {
        string providerName = dto.EmbeddingModel.ToString();

        var embeddingSettings = ChatbotSettings.GetModel<EmbeddingSettings>(providerName);
        if (embeddingSettings == null || string.IsNullOrWhiteSpace(embeddingSettings.Active))
        {
          _logger.LogError("No active embedding settings found for provider: {ProviderName}", providerName);
          return new ResponseResult { IsSuccess = false, Msg = "Embedding configuration not found." };
        }

        var activeDeployment = embeddingSettings.Deployments[embeddingSettings.Active];
        if (activeDeployment == null)
        {
          _logger.LogError("Active embedding deployment '{Active}' not found for provider: {ProviderName}", embeddingSettings.Active, providerName);
          return new ResponseResult { IsSuccess = false, Msg = "Active embedding deployment not found." };
        }

        int dimensions = activeDeployment.Dimensions;

        await _qdrantClient.CreateCollectionAsync(
            collectionName: collection.Id.ToString(),
            vectorsConfig: new VectorParams { Size = (ulong)dimensions, Distance = Distance.Cosine });
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to create Qdrant collection for ID: {CollectionId}", collection.Id);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to create vector collection: {ex.Message}" };
      }

      _logger.LogInformation("Collection {CollectionId} added successfully.", collection.Id);
      return new ResponseResult { IsSuccess = true, Msg = "Collection added successfully." };
    }

    public async Task<ResponseResult> DeleteCollectionByCollectionIdAsync(Guid collectionId)
		{
			_logger.LogInformation("Deleting collection with ID: {CollectionId}", collectionId);

			var collection = await _collectionRepository.GetAsync(collectionId);
			if (collection == null)
			{
				_logger.LogWarning("Collection not found for ID: {CollectionId}", collectionId);
				return new ResponseResult { IsSuccess = false, Msg = "Collection not found." };
			}

			var deleteResult = await _pagesInCollectionService.DeleteMappingsByCollectionIdAsync(collectionId);
			if (!deleteResult.IsSuccess)
			{
				_logger.LogWarning("Failed to delete page mappings for collection ID: {CollectionId}. Error: {Error}", collectionId, deleteResult.Msg);
				return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete page mappings: {deleteResult.Msg}" };
			}

			await _collectionRepository.DeleteAsync(collection);

			try
			{
				await _qdrantClient.DeleteCollectionAsync(collectionName: collectionId.ToString());
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to delete Qdrant collection for ID: {CollectionId}", collectionId);
				return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete vector collection: {ex.Message}" };
			}

			_logger.LogInformation("Collection {CollectionId} and its page mappings deleted successfully.", collectionId);
			return new ResponseResult { IsSuccess = true, Msg = "Collection and its page mappings deleted successfully." };
		}

		public async Task<List<CollectionResponseDTO>> QueryCollectionsAsync()
		{
			_logger.LogInformation("Querying collections for current user.");


			var currentUserId = _currentUserAccessor?.UserId ?? string.Empty;


			if (string.IsNullOrEmpty(currentUserId))
			{
				_logger.LogWarning("Unable to get current user ID, returning empty collection list.");
				return new List<CollectionResponseDTO>();
			}


			var currentUser = await _sysUserRepository.QueryAsync(u => u.UserNTAccount == currentUserId);
			var currentUserEntity = currentUser.FirstOrDefault();

			if (currentUserEntity == null)
			{
				_logger.LogWarning("Current user not found in database: {UserId}", currentUserId);
				return new List<CollectionResponseDTO>();
			}

			var currentUserGroupIds = await _sysUsersInGroupService.QueryGroupIdsByUserIdAsync(currentUserEntity.Id);

			var collections = await _collectionRepository.GetAllAsync();

			if (!collections.Any())
			{
				_logger.LogInformation("No collections found in database.");
				return new List<CollectionResponseDTO>();
			}


			var allGroups = await _sysGroupRepository.GetAllAsync();
			var systemGroup = allGroups.FirstOrDefault(g => g.Name.ToLower() == "system");
			bool isSystemUser = systemGroup != null && currentUserGroupIds.Contains(systemGroup.Id);


			if (isSystemUser)
			{
				_logger.LogInformation("User {UserId} is in System group, returning all collections.", currentUserId);
				var allCollectionDtos = _mapper.Map<List<CollectionResponseDTO>>(collections);

				foreach (var dto in allCollectionDtos)
				{
					dto.IsEmbedding = await CheckCollectionEmbeddingAsync(dto.Id);
				}

				_logger.LogInformation("Retrieved {Count} collections for System user {UserId}.", allCollectionDtos.Count, currentUserId);
				return allCollectionDtos;
			}

			var creatorNTAccounts = collections.Select(c => c.Creator).Distinct().ToList();
			var allCreators = await _sysUserRepository.QueryAsync(u => creatorNTAccounts.Contains(u.UserNTAccount));
			var creatorDict = allCreators.ToDictionary(u => u.UserNTAccount, u => u);


			var creatorIds = allCreators.Select(u => u.Id).ToList();
			var creatorGroupsDict = await _sysUsersInGroupService.QueryGroupIdsByUserIdsBatchAsync(creatorIds);


			var filteredCollections = new List<Domain.Entities.Collection>();

			foreach (var collection in collections)
			{

				bool isCreatedByCurrentUser = collection.Creator == currentUserId;

				if (isCreatedByCurrentUser)
				{
					filteredCollections.Add(collection);
					_logger.LogDebug("Collection {CollectionId} added - created by current user {UserId}", collection.Id, currentUserId);
					continue;
				}


				if (!creatorDict.TryGetValue(collection.Creator, out var creatorUser))
				{
					_logger.LogWarning("Creator not found for collection {CollectionId}: {Creator}", collection.Id, collection.Creator);
					continue;
				}

				if (!creatorGroupsDict.TryGetValue(creatorUser.Id, out var creatorGroupIds))
				{
					_logger.LogWarning("No groups found for creator {CreatorId} of collection {CollectionId}", creatorUser.Id, collection.Id);
					continue;
				}

				var hasCommonGroup = currentUserGroupIds.Any(groupId => creatorGroupIds.Contains(groupId));

				if (hasCommonGroup)
				{
					filteredCollections.Add(collection);
					_logger.LogDebug("Collection {CollectionId} added - common group access for user {UserId}", collection.Id, currentUserId);
				}
			}

			var collectionDtos = _mapper.Map<List<CollectionResponseDTO>>(filteredCollections);


			foreach (var dto in collectionDtos)
			{
				dto.IsEmbedding = await CheckCollectionEmbeddingAsync(dto.Id);
			}

			_logger.LogInformation("Retrieved {Count} collections after group filtering (from {TotalCount} total) for user {UserId}.",
				collectionDtos.Count, collections.Count, currentUserId);
			return collectionDtos;
		}

		public async Task<CollectionResponseDTO?> QueryCollectionWithPagesAsync(Guid collectionId)
		{
			_logger.LogInformation("Querying collection with pages for ID: {CollectionId}", collectionId);

			var collection = await _collectionRepository.GetAsync(collectionId);
			if (collection == null)
			{
				_logger.LogWarning("Collection not found for ID: {CollectionId}", collectionId);
				return null;
			}

			var pageIds = await _pagesInCollectionService.QueryPageIdsByCollectionIdAsync(collectionId);
			if (pageIds == null || !pageIds.Any())
			{
				_logger.LogInformation("No pages found for collection ID: {CollectionId}", collectionId);
				var emptyDto = _mapper.Map<CollectionResponseDTO>(collection);
				emptyDto.Pages = new List<PageResponseDTO>();
				return emptyDto;
			}


			var pages = await _pageRepository.QueryAsync(p => pageIds.Contains(p.Id));
			var pageDtos = pages.Select(p => new PageResponseDTO
			{
				Id = p.Id,
				Title = p.Title,
				Url = p.Url,
				IsIncludeChild = p.IsIncludeChild,
				SourceId = p.SourceId,
				ModificationTime = p.ModificationTime
			}).ToList();

			var collectionDto = _mapper.Map<CollectionResponseDTO>(collection);
			collectionDto.Pages = pageDtos;

			_logger.LogInformation("Retrieved collection {CollectionId} with {PageCount} pages.", collectionId, pageDtos.Count);
			return collectionDto;
		}

		public async Task<ResponseResult> UpdateCollectionAsync(CollectionUpdateDTO dto)
		{
			if (dto == null)
			{
				_logger.LogWarning("UpdateCollectionAsync received null DTO.");
				return new ResponseResult { IsSuccess = false, Msg = "Collection data cannot be null." };
			}

			_logger.LogInformation("Updating collection with ID: {CollectionId}", dto.Id);

			var collection = await _collectionRepository.GetAsync(dto.Id);
			if (collection == null)
			{
				_logger.LogWarning("Collection not found for ID: {CollectionId}", dto.Id);
				return new ResponseResult { IsSuccess = false, Msg = $"Collection with ID {dto.Id} not found." };
			}

			_mapper.Map(dto, collection);
			await _collectionRepository.UpdateAsync(collection);

			_logger.LogInformation("Collection {CollectionId} updated successfully.", dto.Id);
			return new ResponseResult { IsSuccess = true, Msg = "Collection updated successfully." };
		}

		public async Task<ResponseResult> UpdateCollectionModificationTimeAsync(Guid collectionId)
		{
			_logger.LogInformation("Updating modification time for collection with ID: {CollectionId}", collectionId);

			var collection = await _collectionRepository.GetAsync(collectionId);
			if (collection == null)
			{
				_logger.LogWarning("Collection not found for ID: {CollectionId}", collectionId);
				return new ResponseResult { IsSuccess = false, Msg = $"Collection with ID {collectionId} not found." };
			}

			collection.ModificationTime = DateTime.UtcNow;
			await _collectionRepository.UpdateAsync(collection);

			_logger.LogInformation("Collection {CollectionId} modification time updated successfully.", collectionId);
			return new ResponseResult { IsSuccess = true, Msg = "Collection modification time updated successfully." };
		}

		private async Task<bool> CheckCollectionEmbeddingAsync(Guid collectionId)
		{
			var pageMappings = await _pagesInCollectionService.QueryMappingsByCollectionIdAsync(collectionId);
			return pageMappings != null && pageMappings.Any(mapping => mapping.IsEmbedding);
		}
	}
}