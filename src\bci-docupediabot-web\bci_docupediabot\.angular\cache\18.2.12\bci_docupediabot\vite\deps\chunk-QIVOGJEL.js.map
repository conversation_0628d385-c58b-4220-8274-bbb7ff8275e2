{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/bci-overlay-2df7d606.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { r as registerInstance, h as h$3, H as Host } from './index-93dc8059.js';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t$2 = globalThis,\n  e$7 = t$2.ShadowRoot && (void 0 === t$2.ShadyCSS || t$2.ShadyCSS.nativeShadow) && \"adoptedStyleSheets\" in Document.prototype && \"replace\" in CSSStyleSheet.prototype,\n  s$2 = Symbol(),\n  o$8 = new WeakMap();\nclass n$5 {\n  constructor(t, e, o) {\n    if (this._$cssResult$ = !0, o !== s$2) throw Error(\"CSSResult is not constructable. Use `unsafeCSS` or `css` instead.\");\n    this.cssText = t, this.t = e;\n  }\n  get styleSheet() {\n    let t = this.o;\n    const s = this.t;\n    if (e$7 && void 0 === t) {\n      const e = void 0 !== s && 1 === s.length;\n      e && (t = o$8.get(s)), void 0 === t && ((this.o = t = new CSSStyleSheet()).replaceSync(this.cssText), e && o$8.set(s, t));\n    }\n    return t;\n  }\n  toString() {\n    return this.cssText;\n  }\n}\nconst r$6 = t => new n$5(\"string\" == typeof t ? t : t + \"\", void 0, s$2),\n  i$5 = (t, ...e) => {\n    const o = 1 === t.length ? t[0] : e.reduce((e, s, o) => e + (t => {\n      if (!0 === t._$cssResult$) return t.cssText;\n      if (\"number\" == typeof t) return t;\n      throw Error(\"Value passed to 'css' function must be a 'css' function result: \" + t + \". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.\");\n    })(s) + t[o + 1], t[0]);\n    return new n$5(o, t, s$2);\n  },\n  S$1 = (s, o) => {\n    if (e$7) s.adoptedStyleSheets = o.map(t => t instanceof CSSStyleSheet ? t : t.styleSheet);else for (const e of o) {\n      const o = document.createElement(\"style\"),\n        n = t$2.litNonce;\n      void 0 !== n && o.setAttribute(\"nonce\", n), o.textContent = e.cssText, s.appendChild(o);\n    }\n  },\n  c$5 = e$7 ? t => t : t => t instanceof CSSStyleSheet ? (t => {\n    let e = \"\";\n    for (const s of t.cssRules) e += s.cssText;\n    return r$6(e);\n  })(t) : t;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst {\n    is: i$4,\n    defineProperty: e$6,\n    getOwnPropertyDescriptor: r$5,\n    getOwnPropertyNames: h$2,\n    getOwnPropertySymbols: o$7,\n    getPrototypeOf: n$4\n  } = Object,\n  a$1 = globalThis,\n  c$4 = a$1.trustedTypes,\n  l$1 = c$4 ? c$4.emptyScript : \"\",\n  p$4 = a$1.reactiveElementPolyfillSupport,\n  d$2 = (t, s) => t,\n  u$1 = {\n    toAttribute(t, s) {\n      switch (s) {\n        case Boolean:\n          t = t ? l$1 : null;\n          break;\n        case Object:\n        case Array:\n          t = null == t ? t : JSON.stringify(t);\n      }\n      return t;\n    },\n    fromAttribute(t, s) {\n      let i = t;\n      switch (s) {\n        case Boolean:\n          i = null !== t;\n          break;\n        case Number:\n          i = null === t ? null : Number(t);\n          break;\n        case Object:\n        case Array:\n          try {\n            i = JSON.parse(t);\n          } catch (t) {\n            i = null;\n          }\n      }\n      return i;\n    }\n  },\n  f$2 = (t, s) => !i$4(t, s),\n  y$1 = {\n    attribute: !0,\n    type: String,\n    converter: u$1,\n    reflect: !1,\n    hasChanged: f$2\n  };\nSymbol.metadata ??= Symbol(\"metadata\"), a$1.litPropertyMetadata ??= new WeakMap();\nclass b$1 extends HTMLElement {\n  static addInitializer(t) {\n    this._$Ei(), (this.l ??= []).push(t);\n  }\n  static get observedAttributes() {\n    return this.finalize(), this._$Eh && [...this._$Eh.keys()];\n  }\n  static createProperty(t, s = y$1) {\n    if (s.state && (s.attribute = !1), this._$Ei(), this.elementProperties.set(t, s), !s.noAccessor) {\n      const i = Symbol(),\n        r = this.getPropertyDescriptor(t, i, s);\n      void 0 !== r && e$6(this.prototype, t, r);\n    }\n  }\n  static getPropertyDescriptor(t, s, i) {\n    const {\n      get: e,\n      set: h\n    } = r$5(this.prototype, t) ?? {\n      get() {\n        return this[s];\n      },\n      set(t) {\n        this[s] = t;\n      }\n    };\n    return {\n      get() {\n        return e?.call(this);\n      },\n      set(s) {\n        const r = e?.call(this);\n        h.call(this, s), this.requestUpdate(t, r, i);\n      },\n      configurable: !0,\n      enumerable: !0\n    };\n  }\n  static getPropertyOptions(t) {\n    return this.elementProperties.get(t) ?? y$1;\n  }\n  static _$Ei() {\n    if (this.hasOwnProperty(d$2(\"elementProperties\"))) return;\n    const t = n$4(this);\n    t.finalize(), void 0 !== t.l && (this.l = [...t.l]), this.elementProperties = new Map(t.elementProperties);\n  }\n  static finalize() {\n    if (this.hasOwnProperty(d$2(\"finalized\"))) return;\n    if (this.finalized = !0, this._$Ei(), this.hasOwnProperty(d$2(\"properties\"))) {\n      const t = this.properties,\n        s = [...h$2(t), ...o$7(t)];\n      for (const i of s) this.createProperty(i, t[i]);\n    }\n    const t = this[Symbol.metadata];\n    if (null !== t) {\n      const s = litPropertyMetadata.get(t);\n      if (void 0 !== s) for (const [t, i] of s) this.elementProperties.set(t, i);\n    }\n    this._$Eh = new Map();\n    for (const [t, s] of this.elementProperties) {\n      const i = this._$Eu(t, s);\n      void 0 !== i && this._$Eh.set(i, t);\n    }\n    this.elementStyles = this.finalizeStyles(this.styles);\n  }\n  static finalizeStyles(s) {\n    const i = [];\n    if (Array.isArray(s)) {\n      const e = new Set(s.flat(1 / 0).reverse());\n      for (const s of e) i.unshift(c$5(s));\n    } else void 0 !== s && i.push(c$5(s));\n    return i;\n  }\n  static _$Eu(t, s) {\n    const i = s.attribute;\n    return !1 === i ? void 0 : \"string\" == typeof i ? i : \"string\" == typeof t ? t.toLowerCase() : void 0;\n  }\n  constructor() {\n    super(), this._$Ep = void 0, this.isUpdatePending = !1, this.hasUpdated = !1, this._$Em = null, this._$Ev();\n  }\n  _$Ev() {\n    this._$ES = new Promise(t => this.enableUpdating = t), this._$AL = new Map(), this._$E_(), this.requestUpdate(), this.constructor.l?.forEach(t => t(this));\n  }\n  addController(t) {\n    (this._$EO ??= new Set()).add(t), void 0 !== this.renderRoot && this.isConnected && t.hostConnected?.();\n  }\n  removeController(t) {\n    this._$EO?.delete(t);\n  }\n  _$E_() {\n    const t = new Map(),\n      s = this.constructor.elementProperties;\n    for (const i of s.keys()) this.hasOwnProperty(i) && (t.set(i, this[i]), delete this[i]);\n    t.size > 0 && (this._$Ep = t);\n  }\n  createRenderRoot() {\n    const t = this.shadowRoot ?? this.attachShadow(this.constructor.shadowRootOptions);\n    return S$1(t, this.constructor.elementStyles), t;\n  }\n  connectedCallback() {\n    this.renderRoot ??= this.createRenderRoot(), this.enableUpdating(!0), this._$EO?.forEach(t => t.hostConnected?.());\n  }\n  enableUpdating(t) {}\n  disconnectedCallback() {\n    this._$EO?.forEach(t => t.hostDisconnected?.());\n  }\n  attributeChangedCallback(t, s, i) {\n    this._$AK(t, i);\n  }\n  _$EC(t, s) {\n    const i = this.constructor.elementProperties.get(t),\n      e = this.constructor._$Eu(t, i);\n    if (void 0 !== e && !0 === i.reflect) {\n      const r = (void 0 !== i.converter?.toAttribute ? i.converter : u$1).toAttribute(s, i.type);\n      this._$Em = t, null == r ? this.removeAttribute(e) : this.setAttribute(e, r), this._$Em = null;\n    }\n  }\n  _$AK(t, s) {\n    const i = this.constructor,\n      e = i._$Eh.get(t);\n    if (void 0 !== e && this._$Em !== e) {\n      const t = i.getPropertyOptions(e),\n        r = \"function\" == typeof t.converter ? {\n          fromAttribute: t.converter\n        } : void 0 !== t.converter?.fromAttribute ? t.converter : u$1;\n      this._$Em = e, this[e] = r.fromAttribute(s, t.type), this._$Em = null;\n    }\n  }\n  requestUpdate(t, s, i) {\n    if (void 0 !== t) {\n      if (i ??= this.constructor.getPropertyOptions(t), !(i.hasChanged ?? f$2)(this[t], s)) return;\n      this.P(t, s, i);\n    }\n    !1 === this.isUpdatePending && (this._$ES = this._$ET());\n  }\n  P(t, s, i) {\n    this._$AL.has(t) || this._$AL.set(t, s), !0 === i.reflect && this._$Em !== t && (this._$Ej ??= new Set()).add(t);\n  }\n  async _$ET() {\n    this.isUpdatePending = !0;\n    try {\n      await this._$ES;\n    } catch (t) {\n      Promise.reject(t);\n    }\n    const t = this.scheduleUpdate();\n    return null != t && (await t), !this.isUpdatePending;\n  }\n  scheduleUpdate() {\n    return this.performUpdate();\n  }\n  performUpdate() {\n    if (!this.isUpdatePending) return;\n    if (!this.hasUpdated) {\n      if (this.renderRoot ??= this.createRenderRoot(), this._$Ep) {\n        for (const [t, s] of this._$Ep) this[t] = s;\n        this._$Ep = void 0;\n      }\n      const t = this.constructor.elementProperties;\n      if (t.size > 0) for (const [s, i] of t) !0 !== i.wrapped || this._$AL.has(s) || void 0 === this[s] || this.P(s, this[s], i);\n    }\n    let t = !1;\n    const s = this._$AL;\n    try {\n      t = this.shouldUpdate(s), t ? (this.willUpdate(s), this._$EO?.forEach(t => t.hostUpdate?.()), this.update(s)) : this._$EU();\n    } catch (s) {\n      throw t = !1, this._$EU(), s;\n    }\n    t && this._$AE(s);\n  }\n  willUpdate(t) {}\n  _$AE(t) {\n    this._$EO?.forEach(t => t.hostUpdated?.()), this.hasUpdated || (this.hasUpdated = !0, this.firstUpdated(t)), this.updated(t);\n  }\n  _$EU() {\n    this._$AL = new Map(), this.isUpdatePending = !1;\n  }\n  get updateComplete() {\n    return this.getUpdateComplete();\n  }\n  getUpdateComplete() {\n    return this._$ES;\n  }\n  shouldUpdate(t) {\n    return !0;\n  }\n  update(t) {\n    this._$Ej &&= this._$Ej.forEach(t => this._$EC(t, this[t])), this._$EU();\n  }\n  updated(t) {}\n  firstUpdated(t) {}\n}\nb$1.elementStyles = [], b$1.shadowRootOptions = {\n  mode: \"open\"\n}, b$1[d$2(\"elementProperties\")] = new Map(), b$1[d$2(\"finalized\")] = new Map(), p$4?.({\n  ReactiveElement: b$1\n}), (a$1.reactiveElementVersions ??= []).push(\"2.0.4\");\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t$1 = globalThis,\n  i$3 = t$1.trustedTypes,\n  s$1 = i$3 ? i$3.createPolicy(\"lit-html\", {\n    createHTML: t => t\n  }) : void 0,\n  e$5 = \"$lit$\",\n  h$1 = `lit$${Math.random().toFixed(9).slice(2)}$`,\n  o$6 = \"?\" + h$1,\n  n$3 = `<${o$6}>`,\n  r$4 = document,\n  l = () => r$4.createComment(\"\"),\n  c$3 = t => null === t || \"object\" != typeof t && \"function\" != typeof t,\n  a = Array.isArray,\n  u = t => a(t) || \"function\" == typeof t?.[Symbol.iterator],\n  d$1 = \"[ \\t\\n\\f\\r]\",\n  f$1 = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g,\n  v = /-->/g,\n  _ = />/g,\n  m = RegExp(`>|${d$1}(?:([^\\\\s\"'>=/]+)(${d$1}*=${d$1}*(?:[^ \\t\\n\\f\\r\"'\\`<>=]|(\"|')|))|$)`, \"g\"),\n  p$3 = /'/g,\n  g$2 = /\"/g,\n  $ = /^(?:script|style|textarea|title)$/i,\n  y = t => (i, ...s) => ({\n    _$litType$: t,\n    strings: i,\n    values: s\n  }),\n  x = y(1),\n  w$1 = Symbol.for(\"lit-noChange\"),\n  T$2 = Symbol.for(\"lit-nothing\"),\n  A = new WeakMap(),\n  E$1 = r$4.createTreeWalker(r$4, 129);\nfunction C$2(t, i) {\n  if (!Array.isArray(t) || !t.hasOwnProperty(\"raw\")) throw Error(\"invalid template strings array\");\n  return void 0 !== s$1 ? s$1.createHTML(i) : i;\n}\nconst P = (t, i) => {\n  const s = t.length - 1,\n    o = [];\n  let r,\n    l = 2 === i ? \"<svg>\" : \"\",\n    c = f$1;\n  for (let i = 0; i < s; i++) {\n    const s = t[i];\n    let a,\n      u,\n      d = -1,\n      y = 0;\n    for (; y < s.length && (c.lastIndex = y, u = c.exec(s), null !== u);) y = c.lastIndex, c === f$1 ? \"!--\" === u[1] ? c = v : void 0 !== u[1] ? c = _ : void 0 !== u[2] ? ($.test(u[2]) && (r = RegExp(\"</\" + u[2], \"g\")), c = m) : void 0 !== u[3] && (c = m) : c === m ? \">\" === u[0] ? (c = r ?? f$1, d = -1) : void 0 === u[1] ? d = -2 : (d = c.lastIndex - u[2].length, a = u[1], c = void 0 === u[3] ? m : '\"' === u[3] ? g$2 : p$3) : c === g$2 || c === p$3 ? c = m : c === v || c === _ ? c = f$1 : (c = m, r = void 0);\n    const x = c === m && t[i + 1].startsWith(\"/>\") ? \" \" : \"\";\n    l += c === f$1 ? s + n$3 : d >= 0 ? (o.push(a), s.slice(0, d) + e$5 + s.slice(d) + h$1 + x) : s + h$1 + (-2 === d ? i : x);\n  }\n  return [C$2(t, l + (t[s] || \"<?>\") + (2 === i ? \"</svg>\" : \"\")), o];\n};\nclass V {\n  constructor({\n    strings: t,\n    _$litType$: s\n  }, n) {\n    let r;\n    this.parts = [];\n    let c = 0,\n      a = 0;\n    const u = t.length - 1,\n      d = this.parts,\n      [f, v] = P(t, s);\n    if (this.el = V.createElement(f, n), E$1.currentNode = this.el.content, 2 === s) {\n      const t = this.el.content.firstChild;\n      t.replaceWith(...t.childNodes);\n    }\n    for (; null !== (r = E$1.nextNode()) && d.length < u;) {\n      if (1 === r.nodeType) {\n        if (r.hasAttributes()) for (const t of r.getAttributeNames()) if (t.endsWith(e$5)) {\n          const i = v[a++],\n            s = r.getAttribute(t).split(h$1),\n            e = /([.?@])?(.*)/.exec(i);\n          d.push({\n            type: 1,\n            index: c,\n            name: e[2],\n            strings: s,\n            ctor: \".\" === e[1] ? k : \"?\" === e[1] ? H : \"@\" === e[1] ? I : R\n          }), r.removeAttribute(t);\n        } else t.startsWith(h$1) && (d.push({\n          type: 6,\n          index: c\n        }), r.removeAttribute(t));\n        if ($.test(r.tagName)) {\n          const t = r.textContent.split(h$1),\n            s = t.length - 1;\n          if (s > 0) {\n            r.textContent = i$3 ? i$3.emptyScript : \"\";\n            for (let i = 0; i < s; i++) r.append(t[i], l()), E$1.nextNode(), d.push({\n              type: 2,\n              index: ++c\n            });\n            r.append(t[s], l());\n          }\n        }\n      } else if (8 === r.nodeType) if (r.data === o$6) d.push({\n        type: 2,\n        index: c\n      });else {\n        let t = -1;\n        for (; -1 !== (t = r.data.indexOf(h$1, t + 1));) d.push({\n          type: 7,\n          index: c\n        }), t += h$1.length - 1;\n      }\n      c++;\n    }\n  }\n  static createElement(t, i) {\n    const s = r$4.createElement(\"template\");\n    return s.innerHTML = t, s;\n  }\n}\nfunction N(t, i, s = t, e) {\n  if (i === w$1) return i;\n  let h = void 0 !== e ? s._$Co?.[e] : s._$Cl;\n  const o = c$3(i) ? void 0 : i._$litDirective$;\n  return h?.constructor !== o && (h?._$AO?.(!1), void 0 === o ? h = void 0 : (h = new o(t), h._$AT(t, s, e)), void 0 !== e ? (s._$Co ??= [])[e] = h : s._$Cl = h), void 0 !== h && (i = N(t, h._$AS(t, i.values), h, e)), i;\n}\nclass S {\n  constructor(t, i) {\n    this._$AV = [], this._$AN = void 0, this._$AD = t, this._$AM = i;\n  }\n  get parentNode() {\n    return this._$AM.parentNode;\n  }\n  get _$AU() {\n    return this._$AM._$AU;\n  }\n  u(t) {\n    const {\n        el: {\n          content: i\n        },\n        parts: s\n      } = this._$AD,\n      e = (t?.creationScope ?? r$4).importNode(i, !0);\n    E$1.currentNode = e;\n    let h = E$1.nextNode(),\n      o = 0,\n      n = 0,\n      l = s[0];\n    for (; void 0 !== l;) {\n      if (o === l.index) {\n        let i;\n        2 === l.type ? i = new M(h, h.nextSibling, this, t) : 1 === l.type ? i = new l.ctor(h, l.name, l.strings, this, t) : 6 === l.type && (i = new L(h, this, t)), this._$AV.push(i), l = s[++n];\n      }\n      o !== l?.index && (h = E$1.nextNode(), o++);\n    }\n    return E$1.currentNode = r$4, e;\n  }\n  p(t) {\n    let i = 0;\n    for (const s of this._$AV) void 0 !== s && (void 0 !== s.strings ? (s._$AI(t, s, i), i += s.strings.length - 2) : s._$AI(t[i])), i++;\n  }\n}\nclass M {\n  get _$AU() {\n    return this._$AM?._$AU ?? this._$Cv;\n  }\n  constructor(t, i, s, e) {\n    this.type = 2, this._$AH = T$2, this._$AN = void 0, this._$AA = t, this._$AB = i, this._$AM = s, this.options = e, this._$Cv = e?.isConnected ?? !0;\n  }\n  get parentNode() {\n    let t = this._$AA.parentNode;\n    const i = this._$AM;\n    return void 0 !== i && 11 === t?.nodeType && (t = i.parentNode), t;\n  }\n  get startNode() {\n    return this._$AA;\n  }\n  get endNode() {\n    return this._$AB;\n  }\n  _$AI(t, i = this) {\n    t = N(this, t, i), c$3(t) ? t === T$2 || null == t || \"\" === t ? (this._$AH !== T$2 && this._$AR(), this._$AH = T$2) : t !== this._$AH && t !== w$1 && this._(t) : void 0 !== t._$litType$ ? this.$(t) : void 0 !== t.nodeType ? this.T(t) : u(t) ? this.k(t) : this._(t);\n  }\n  S(t) {\n    return this._$AA.parentNode.insertBefore(t, this._$AB);\n  }\n  T(t) {\n    this._$AH !== t && (this._$AR(), this._$AH = this.S(t));\n  }\n  _(t) {\n    this._$AH !== T$2 && c$3(this._$AH) ? this._$AA.nextSibling.data = t : this.T(r$4.createTextNode(t)), this._$AH = t;\n  }\n  $(t) {\n    const {\n        values: i,\n        _$litType$: s\n      } = t,\n      e = \"number\" == typeof s ? this._$AC(t) : (void 0 === s.el && (s.el = V.createElement(C$2(s.h, s.h[0]), this.options)), s);\n    if (this._$AH?._$AD === e) this._$AH.p(i);else {\n      const t = new S(e, this),\n        s = t.u(this.options);\n      t.p(i), this.T(s), this._$AH = t;\n    }\n  }\n  _$AC(t) {\n    let i = A.get(t.strings);\n    return void 0 === i && A.set(t.strings, i = new V(t)), i;\n  }\n  k(t) {\n    a(this._$AH) || (this._$AH = [], this._$AR());\n    const i = this._$AH;\n    let s,\n      e = 0;\n    for (const h of t) e === i.length ? i.push(s = new M(this.S(l()), this.S(l()), this, this.options)) : s = i[e], s._$AI(h), e++;\n    e < i.length && (this._$AR(s && s._$AB.nextSibling, e), i.length = e);\n  }\n  _$AR(t = this._$AA.nextSibling, i) {\n    for (this._$AP?.(!1, !0, i); t && t !== this._$AB;) {\n      const i = t.nextSibling;\n      t.remove(), t = i;\n    }\n  }\n  setConnected(t) {\n    void 0 === this._$AM && (this._$Cv = t, this._$AP?.(t));\n  }\n}\nclass R {\n  get tagName() {\n    return this.element.tagName;\n  }\n  get _$AU() {\n    return this._$AM._$AU;\n  }\n  constructor(t, i, s, e, h) {\n    this.type = 1, this._$AH = T$2, this._$AN = void 0, this.element = t, this.name = i, this._$AM = e, this.options = h, s.length > 2 || \"\" !== s[0] || \"\" !== s[1] ? (this._$AH = Array(s.length - 1).fill(new String()), this.strings = s) : this._$AH = T$2;\n  }\n  _$AI(t, i = this, s, e) {\n    const h = this.strings;\n    let o = !1;\n    if (void 0 === h) t = N(this, t, i, 0), o = !c$3(t) || t !== this._$AH && t !== w$1, o && (this._$AH = t);else {\n      const e = t;\n      let n, r;\n      for (t = h[0], n = 0; n < h.length - 1; n++) r = N(this, e[s + n], i, n), r === w$1 && (r = this._$AH[n]), o ||= !c$3(r) || r !== this._$AH[n], r === T$2 ? t = T$2 : t !== T$2 && (t += (r ?? \"\") + h[n + 1]), this._$AH[n] = r;\n    }\n    o && !e && this.j(t);\n  }\n  j(t) {\n    t === T$2 ? this.element.removeAttribute(this.name) : this.element.setAttribute(this.name, t ?? \"\");\n  }\n}\nclass k extends R {\n  constructor() {\n    super(...arguments), this.type = 3;\n  }\n  j(t) {\n    this.element[this.name] = t === T$2 ? void 0 : t;\n  }\n}\nclass H extends R {\n  constructor() {\n    super(...arguments), this.type = 4;\n  }\n  j(t) {\n    this.element.toggleAttribute(this.name, !!t && t !== T$2);\n  }\n}\nclass I extends R {\n  constructor(t, i, s, e, h) {\n    super(t, i, s, e, h), this.type = 5;\n  }\n  _$AI(t, i = this) {\n    if ((t = N(this, t, i, 0) ?? T$2) === w$1) return;\n    const s = this._$AH,\n      e = t === T$2 && s !== T$2 || t.capture !== s.capture || t.once !== s.once || t.passive !== s.passive,\n      h = t !== T$2 && (s === T$2 || e);\n    e && this.element.removeEventListener(this.name, this, s), h && this.element.addEventListener(this.name, this, t), this._$AH = t;\n  }\n  handleEvent(t) {\n    \"function\" == typeof this._$AH ? this._$AH.call(this.options?.host ?? this.element, t) : this._$AH.handleEvent(t);\n  }\n}\nclass L {\n  constructor(t, i, s) {\n    this.element = t, this.type = 6, this._$AN = void 0, this._$AM = i, this.options = s;\n  }\n  get _$AU() {\n    return this._$AM._$AU;\n  }\n  _$AI(t) {\n    N(this, t);\n  }\n}\nconst Z = t$1.litHtmlPolyfillSupport;\nZ?.(V, M), (t$1.litHtmlVersions ??= []).push(\"3.1.3\");\nconst j = (t, i, s) => {\n  const e = s?.renderBefore ?? i;\n  let h = e._$litPart$;\n  if (void 0 === h) {\n    const t = s?.renderBefore ?? null;\n    e._$litPart$ = h = new M(i.insertBefore(l(), t), t, void 0, s ?? {});\n  }\n  return h._$AI(t), h;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nclass s extends b$1 {\n  constructor() {\n    super(...arguments), this.renderOptions = {\n      host: this\n    }, this._$Do = void 0;\n  }\n  createRenderRoot() {\n    const t = super.createRenderRoot();\n    return this.renderOptions.renderBefore ??= t.firstChild, t;\n  }\n  update(t) {\n    const i = this.render();\n    this.hasUpdated || (this.renderOptions.isConnected = this.isConnected), super.update(t), this._$Do = j(i, this.renderRoot, this.renderOptions);\n  }\n  connectedCallback() {\n    super.connectedCallback(), this._$Do?.setConnected(!0);\n  }\n  disconnectedCallback() {\n    super.disconnectedCallback(), this._$Do?.setConnected(!1);\n  }\n  render() {\n    return w$1;\n  }\n}\ns._$litElement$ = !0, s[\"finalized\"] = !0, globalThis.litElementHydrateSupport?.({\n  LitElement: s\n});\nconst r$3 = globalThis.litElementPolyfillSupport;\nr$3?.({\n  LitElement: s\n});\n(globalThis.litElementVersions ??= []).push(\"4.0.5\");\n\n// Generated by genversion.\nconst version = '0.42.3';\nconst c$2 = new Set(),\n  g$1 = () => {\n    const s = document.documentElement.dir === \"rtl\" ? document.documentElement.dir : \"ltr\";\n    c$2.forEach(o => {\n      o.setAttribute(\"dir\", s);\n    });\n  },\n  w = new MutationObserver(g$1);\nw.observe(document.documentElement, {\n  attributes: !0,\n  attributeFilter: [\"dir\"]\n});\nconst p$2 = s => typeof s.startManagingContentDirection != \"undefined\" || s.tagName === \"SP-THEME\";\nfunction SpectrumMixin(s) {\n  class o extends s {\n    get isLTR() {\n      return this.dir === \"ltr\";\n    }\n    hasVisibleFocusInTree() {\n      const n = ((r = document) => {\n        var l;\n        let t = r.activeElement;\n        for (; t != null && t.shadowRoot && t.shadowRoot.activeElement;) t = t.shadowRoot.activeElement;\n        const a = t ? [t] : [];\n        for (; t;) {\n          const i = t.assignedSlot || t.parentElement || ((l = t.getRootNode()) == null ? void 0 : l.host);\n          i && a.push(i), t = i;\n        }\n        return a;\n      })(this.getRootNode())[0];\n      if (!n) return !1;\n      try {\n        return n.matches(\":focus-visible\") || n.matches(\".focus-visible\");\n      } catch (r) {\n        return n.matches(\".focus-visible\");\n      }\n    }\n    connectedCallback() {\n      if (!this.hasAttribute(\"dir\")) {\n        let e = this.assignedSlot || this.parentNode;\n        for (; e !== document.documentElement && !p$2(e);) e = e.assignedSlot || e.parentNode || e.host;\n        if (this.dir = e.dir === \"rtl\" ? e.dir : this.dir || \"ltr\", e === document.documentElement) c$2.add(this);else {\n          const {\n            localName: n\n          } = e;\n          n.search(\"-\") > -1 && !customElements.get(n) ? customElements.whenDefined(n).then(() => {\n            e.startManagingContentDirection(this);\n          }) : e.startManagingContentDirection(this);\n        }\n        this._dirParent = e;\n      }\n      super.connectedCallback();\n    }\n    disconnectedCallback() {\n      super.disconnectedCallback(), this._dirParent && (this._dirParent === document.documentElement ? c$2.delete(this) : this._dirParent.stopManagingContentDirection(this), this.removeAttribute(\"dir\"));\n    }\n  }\n  return o;\n}\nclass SpectrumElement extends SpectrumMixin(s) {}\nSpectrumElement.VERSION = version;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst o$5 = {\n    attribute: !0,\n    type: String,\n    converter: u$1,\n    reflect: !1,\n    hasChanged: f$2\n  },\n  r$2 = (t = o$5, e, r) => {\n    const {\n      kind: n,\n      metadata: i\n    } = r;\n    let s = globalThis.litPropertyMetadata.get(i);\n    if (void 0 === s && globalThis.litPropertyMetadata.set(i, s = new Map()), s.set(r.name, t), \"accessor\" === n) {\n      const {\n        name: o\n      } = r;\n      return {\n        set(r) {\n          const n = e.get.call(this);\n          e.set.call(this, r), this.requestUpdate(o, n, t);\n        },\n        init(e) {\n          return void 0 !== e && this.P(o, void 0, t), e;\n        }\n      };\n    }\n    if (\"setter\" === n) {\n      const {\n        name: o\n      } = r;\n      return function (r) {\n        const n = this[o];\n        e.call(this, r), this.requestUpdate(o, n, t);\n      };\n    }\n    throw Error(\"Unsupported decorator location: \" + n);\n  };\nfunction n$2(t) {\n  return (e, o) => \"object\" == typeof o ? r$2(t, e, o) : ((t, e, o) => {\n    const r = e.hasOwnProperty(o);\n    return e.constructor.createProperty(o, r ? {\n      ...t,\n      wrapped: !0\n    } : t), r ? Object.getOwnPropertyDescriptor(e, o) : void 0;\n  })(t, e, o);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nfunction r$1(r) {\n  return n$2({\n    ...r,\n    state: !0,\n    attribute: !1\n  });\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst e$4 = (e, t, c) => (c.configurable = !0, c.enumerable = !0, Reflect.decorate && \"object\" != typeof t && Object.defineProperty(e, t, c), c);\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nfunction e$3(e, r) {\n  return (n, s, i) => {\n    const o = t => t.renderRoot?.querySelector(e) ?? null;\n    if (r) {\n      const {\n        get: e,\n        set: r\n      } = \"object\" == typeof s ? n : i ?? (() => {\n        const t = Symbol();\n        return {\n          get() {\n            return this[t];\n          },\n          set(e) {\n            this[t] = e;\n          }\n        };\n      })();\n      return e$4(n, s, {\n        get() {\n          let t = e.call(this);\n          return void 0 === t && (t = o(this), (null !== t || this.hasUpdated) && r.call(this, t)), t;\n        }\n      });\n    }\n    return e$4(n, s, {\n      get() {\n        return o(this);\n      }\n    });\n  };\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nfunction o$4(o) {\n  return (e, n) => {\n    const {\n        slot: r,\n        selector: s\n      } = o ?? {},\n      c = \"slot\" + (r ? `[name=${r}]` : \":not([name])\");\n    return e$4(e, n, {\n      get() {\n        const t = this.renderRoot?.querySelector(c),\n          e = t?.assignedElements(o) ?? [];\n        return void 0 === s ? e : e.filter(t => t.matches(s));\n      }\n    });\n  };\n}\nconst elementResolverUpdatedSymbol = Symbol(\"element resolver updated\");\nclass ElementResolutionController {\n  constructor(e, {\n    selector: t\n  } = {\n    selector: \"\"\n  }) {\n    this._element = null;\n    this._selector = \"\";\n    this.mutationCallback = e => {\n      let t = !1;\n      e.forEach(s => {\n        if (!t) {\n          if (s.type === \"childList\") {\n            const r = this.element && [...s.removedNodes].includes(this.element),\n              l = !!this.selector && [...s.addedNodes].some(this.elementIsSelected);\n            t = t || r || l;\n          }\n          if (s.type === \"attributes\") {\n            const r = s.target === this.element,\n              l = !!this.selector && this.elementIsSelected(s.target);\n            t = t || r || l;\n          }\n        }\n      }), t && this.resolveElement();\n    };\n    this.elementIsSelected = e => {\n      var t;\n      return this.selectorIsId ? (e == null ? void 0 : e.id) === this.selectorAsId : (t = e == null ? void 0 : e.matches) == null ? void 0 : t.call(e, this.selector);\n    };\n    this.host = e, this.selector = t, this.observer = new MutationObserver(this.mutationCallback), this.host.addController(this);\n  }\n  get element() {\n    return this._element;\n  }\n  set element(e) {\n    if (e === this.element) return;\n    const t = this.element;\n    this._element = e, this.host.requestUpdate(elementResolverUpdatedSymbol, t);\n  }\n  get selector() {\n    return this._selector;\n  }\n  set selector(e) {\n    e !== this.selector && (this.releaseElement(), this._selector = e, this.resolveElement());\n  }\n  get selectorAsId() {\n    return this.selector.slice(1);\n  }\n  get selectorIsId() {\n    return !!this.selector && this.selector.startsWith(\"#\");\n  }\n  hostConnected() {\n    this.resolveElement(), this.observer.observe(this.host.getRootNode(), {\n      subtree: !0,\n      childList: !0,\n      attributes: !0\n    });\n  }\n  hostDisconnected() {\n    this.releaseElement(), this.observer.disconnect();\n  }\n  resolveElement() {\n    if (!this.selector) {\n      this.releaseElement();\n      return;\n    }\n    const e = this.host.getRootNode();\n    this.element = this.selectorIsId ? e.getElementById(this.selectorAsId) : e.querySelector(this.selector);\n  }\n  releaseElement() {\n    this.element = null;\n  }\n}\n\n/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst o$3 = o => o ?? T$2;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst t = {\n    ATTRIBUTE: 1,\n    CHILD: 2,\n    PROPERTY: 3,\n    BOOLEAN_ATTRIBUTE: 4,\n    EVENT: 5,\n    ELEMENT: 6\n  },\n  e$2 = t => (...e) => ({\n    _$litDirective$: t,\n    values: e\n  });\nclass i$2 {\n  constructor(t) {}\n  get _$AU() {\n    return this._$AM._$AU;\n  }\n  _$AT(t, e, i) {\n    this._$Ct = t, this._$AM = e, this._$Ci = i;\n  }\n  _$AS(t, e) {\n    return this.update(t, e);\n  }\n  update(t, e) {\n    return this.render(...e);\n  }\n}\n\n/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nconst n$1 = \"important\",\n  i$1 = \" !\" + n$1,\n  o$2 = e$2(class extends i$2 {\n    constructor(t$1) {\n      if (super(t$1), t$1.type !== t.ATTRIBUTE || \"style\" !== t$1.name || t$1.strings?.length > 2) throw Error(\"The `styleMap` directive must be used in the `style` attribute and must be the only part in the attribute.\");\n    }\n    render(t) {\n      return Object.keys(t).reduce((e, r) => {\n        const s = t[r];\n        return null == s ? e : e + `${r = r.includes(\"-\") ? r : r.replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g, \"-$&\").toLowerCase()}:${s};`;\n      }, \"\");\n    }\n    update(e, [r]) {\n      const {\n        style: s\n      } = e.element;\n      if (void 0 === this.ft) return this.ft = new Set(Object.keys(r)), this.render(r);\n      for (const t of this.ft) null == r[t] && (this.ft.delete(t), t.includes(\"-\") ? s.removeProperty(t) : s[t] = null);\n      for (const t in r) {\n        const e = r[t];\n        if (null != e) {\n          this.ft.add(t);\n          const r = \"string\" == typeof e && e.endsWith(i$1);\n          t.includes(\"-\") || r ? s.setProperty(t, r ? e.slice(0, -11) : e, r ? n$1 : \"\") : s[t] = e;\n        }\n      }\n      return w$1;\n    }\n  });\nfunction randomID() {\n  return Array.from(crypto.getRandomValues(new Uint8Array(4)), r => `0${(r & 255).toString(16)}`.slice(-2)).join(\"\");\n}\nfunction T$1(o, i, l = []) {\n  for (let e = 0; e < i.length; ++e) {\n    const n = i[e],\n      r = o[e],\n      t = r.parentElement || r.getRootNode();\n    l[e] && l[e](n), t && t !== r && t.replaceChild(n, r), delete o[e];\n  }\n  return i;\n}\nconst reparentChildren = (o, i, {\n  position: l,\n  prepareCallback: e\n} = {\n  position: \"beforeend\"\n}) => {\n  let {\n    length: n\n  } = o;\n  if (n === 0) return () => o;\n  let r = 1,\n    t = 0;\n  (l === \"afterbegin\" || l === \"afterend\") && (r = -1, t = n - 1);\n  const a = new Array(n),\n    c = new Array(n),\n    p = document.createComment(\"placeholder for reparented element\");\n  do {\n    const d = o[t];\n    e && (c[t] = e(d)), a[t] = p.cloneNode();\n    const m = d.parentElement || d.getRootNode();\n    m && m !== d && m.replaceChild(a[t], d), i.insertAdjacentElement(l, d), t += r;\n  } while (--n > 0);\n  return function () {\n    return T$1(a, o, c);\n  };\n};\nclass OverlayTimer {\n  constructor(e = {}) {\n    this.warmUpDelay = 1e3;\n    this.coolDownDelay = 1e3;\n    this.isWarm = !1;\n    this.timeout = 0;\n    Object.assign(this, e);\n  }\n  async openTimer(e) {\n    if (this.cancelCooldownTimer(), !this.component || e !== this.component) return this.component && (this.close(this.component), this.cancelCooldownTimer()), this.component = e, this.isWarm ? !1 : (this.promise = new Promise(o => {\n      this.resolve = o, this.timeout = window.setTimeout(() => {\n        this.resolve && (this.resolve(!1), this.isWarm = !0);\n      }, this.warmUpDelay);\n    }), this.promise);\n    if (this.promise) return this.promise;\n    throw new Error(\"Inconsistent state\");\n  }\n  close(e) {\n    this.component && this.component === e && (this.resetCooldownTimer(), this.timeout > 0 && (clearTimeout(this.timeout), this.timeout = 0), this.resolve && (this.resolve(!0), delete this.resolve), delete this.promise, delete this.component);\n  }\n  resetCooldownTimer() {\n    this.isWarm && (this.cooldownTimeout && window.clearTimeout(this.cooldownTimeout), this.cooldownTimeout = window.setTimeout(() => {\n      this.isWarm = !1, delete this.cooldownTimeout;\n    }, this.coolDownDelay));\n  }\n  cancelCooldownTimer() {\n    this.cooldownTimeout && window.clearTimeout(this.cooldownTimeout), delete this.cooldownTimeout;\n  }\n}\nconst overlayTimer = new OverlayTimer(),\n  noop = () => {},\n  guaranteedAllTransitionend = (i, v, e) => {\n    const r = new AbortController(),\n      n = new Map(),\n      a = () => {\n        r.abort(), e();\n      };\n    let m, l;\n    const t = requestAnimationFrame(() => {\n        m = requestAnimationFrame(() => {\n          l = requestAnimationFrame(() => {\n            a();\n          });\n        });\n      }),\n      p = o => {\n        o.target === i && (n.set(o.propertyName, n.get(o.propertyName) - 1), n.get(o.propertyName) || n.delete(o.propertyName), n.size === 0 && a());\n      },\n      d = o => {\n        o.target === i && (n.has(o.propertyName) || n.set(o.propertyName, 0), n.set(o.propertyName, n.get(o.propertyName) + 1), cancelAnimationFrame(t), cancelAnimationFrame(m), cancelAnimationFrame(l));\n      };\n    i.addEventListener(\"transitionrun\", d, {\n      signal: r.signal\n    }), i.addEventListener(\"transitionend\", p, {\n      signal: r.signal\n    }), i.addEventListener(\"transitioncancel\", p, {\n      signal: r.signal\n    }), v();\n  };\nfunction nextFrame() {\n  return new Promise(i => requestAnimationFrame(() => i()));\n}\nclass AbstractOverlay extends SpectrumElement {\n  constructor() {\n    super(...arguments);\n    this.dispose = noop;\n    this.offset = 0;\n    this.willPreventClose = !1;\n  }\n  async applyFocus(e, r) {}\n  get delayed() {\n    return !1;\n  }\n  set delayed(e) {}\n  get disabled() {\n    return !1;\n  }\n  set disabled(e) {}\n  get elementResolver() {\n    return this._elementResolver;\n  }\n  set elementResolver(e) {\n    this._elementResolver = e;\n  }\n  async ensureOnDOM(e) {}\n  async makeTransition(e) {\n    return null;\n  }\n  async manageDelay(e) {}\n  async manageDialogOpen() {}\n  async managePopoverOpen() {}\n  managePosition() {}\n  get open() {\n    return !1;\n  }\n  set open(e) {}\n  get placementController() {\n    return this._placementController;\n  }\n  set placementController(e) {\n    this._placementController = e;\n  }\n  requestSlottable() {}\n  returnFocus() {}\n  get state() {\n    return \"closed\";\n  }\n  set state(e) {}\n  manuallyKeepOpen() {}\n  static update() {\n    const e = new CustomEvent(\"sp-update-overlays\", {\n      bubbles: !0,\n      composed: !0,\n      cancelable: !0\n    });\n    document.dispatchEvent(e);\n  }\n  static async open(e, r, n, a) {\n    await import('./sp-overlay-1351366a.js');\n    const m = arguments.length === 2,\n      l = n || e,\n      t = new this();\n    let p = !1;\n    t.dispose = () => {\n      t.addEventListener(\"sp-closed\", () => {\n        p || (d(), p = !0), requestAnimationFrame(() => {\n          t.remove();\n        });\n      }), t.open = !1, t.dispose = noop;\n    };\n    const d = reparentChildren([l], t, {\n      position: \"beforeend\",\n      prepareCallback: s => {\n        const c = s.slot;\n        return s.removeAttribute(\"slot\"), () => {\n          s.slot = c;\n        };\n      }\n    });\n    if (!m && l && a) {\n      const s = e,\n        c = r,\n        u = a;\n      return AbstractOverlay.applyOptions(t, {\n        ...u,\n        delayed: u.delayed || l.hasAttribute(\"delayed\"),\n        trigger: u.virtualTrigger || s,\n        type: c === \"modal\" ? \"modal\" : c === \"hover\" ? \"hint\" : \"auto\"\n      }), s.insertAdjacentElement(\"afterend\", t), await t.updateComplete, t.open = !0, t.dispose;\n    }\n    const y = r;\n    return t.append(l), AbstractOverlay.applyOptions(t, {\n      ...y,\n      delayed: y.delayed || l.hasAttribute(\"delayed\")\n    }), t.updateComplete.then(() => {\n      t.open = !0;\n    }), t;\n  }\n  static applyOptions(e, r) {\n    var n, a;\n    e.delayed = !!r.delayed, e.receivesFocus = (n = r.receivesFocus) != null ? n : \"auto\", e.triggerElement = r.trigger || null, e.type = r.type || \"modal\", e.offset = (a = r.offset) != null ? a : 0, e.placement = r.placement, e.willPreventClose = !!r.notImmediatelyClosable;\n  }\n}\nconst e$1 = [\"button\", \"[focusable]\", \"[href]\", \"input\", \"label\", \"select\", \"textarea\", \"[tabindex]\"],\n  o$1 = ':not([tabindex=\"-1\"])';\nconst userFocusableSelector = e$1.join(`${o$1}, `) + o$1;\nconst firstFocusableIn = e => e.querySelector(userFocusableSelector),\n  firstFocusableSlottedIn = e => e.assignedElements().find(o => o.matches(userFocusableSelector));\nclass VirtualTrigger {\n  constructor(t, i) {\n    this.x = 0;\n    this.y = 0;\n    this.x = t, this.y = i;\n  }\n  updateBoundingClientRect(t, i) {\n    this.x = t, this.y = i, AbstractOverlay.update();\n  }\n  getBoundingClientRect() {\n    return {\n      width: 0,\n      height: 0,\n      top: this.y,\n      right: this.x,\n      y: this.y,\n      x: this.x,\n      bottom: this.y,\n      left: this.x,\n      toJSON() {}\n    };\n  }\n}\nclass BeforetoggleClosedEvent extends Event {\n  constructor() {\n    super(\"beforetoggle\", {\n      bubbles: !1,\n      composed: !1\n    });\n    this.currentState = \"open\";\n    this.newState = \"closed\";\n  }\n}\nclass BeforetoggleOpenEvent extends Event {\n  constructor() {\n    super(\"beforetoggle\", {\n      bubbles: !1,\n      composed: !1\n    });\n    this.currentState = \"closed\";\n    this.newState = \"open\";\n  }\n}\nclass OverlayStateEvent extends Event {\n  constructor(r, l, {\n    publish: o,\n    interaction: s,\n    reason: n\n  }) {\n    super(r, {\n      bubbles: o,\n      composed: o\n    });\n    this.overlay = l;\n    this.detail = {\n      interaction: s,\n      reason: n\n    };\n  }\n}\nfunction n(o) {\n  return typeof window != \"undefined\" && window.navigator != null ? o.test(window.navigator.userAgent) : !1;\n}\nfunction e(o) {\n  return typeof window != \"undefined\" && window.navigator != null ? o.test(window.navigator.platform) : !1;\n}\nfunction isMac() {\n  return e(/^Mac/);\n}\nfunction isIPhone() {\n  return e(/^iPhone/);\n}\nfunction isIPad() {\n  return e(/^iPad/) || isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n  return isIPhone() || isIPad();\n}\nfunction isAndroid() {\n  return n(/Android/);\n}\nfunction OverlayDialog(h) {\n  class p extends h {\n    async manageDialogOpen() {\n      const e = this.open;\n      if (await this.managePosition(), this.open !== e) return;\n      const i = await this.dialogMakeTransition(e);\n      this.open === e && (await this.dialogApplyFocus(e, i));\n    }\n    async dialogMakeTransition(e) {\n      let i = null;\n      const m = (t, s) => async () => {\n          if (t.open = e, !e) {\n            const n = () => {\n              t.removeEventListener(\"close\", n), a(t, s);\n            };\n            t.addEventListener(\"close\", n);\n          }\n          if (s > 0) return;\n          const o = e ? BeforetoggleOpenEvent : BeforetoggleClosedEvent;\n          this.dispatchEvent(new o()), e && (t.matches(userFocusableSelector) && (i = t), i = i || firstFocusableIn(t), i || t.querySelectorAll(\"slot\").forEach(r => {\n            i || (i = firstFocusableSlottedIn(r));\n          }), !(!this.isConnected || this.dialogEl.open) && this.dialogEl.showModal());\n        },\n        a = (t, s) => () => {\n          if (this.open !== e) return;\n          const o = e ? \"sp-opened\" : \"sp-closed\";\n          if (s > 0) {\n            t.dispatchEvent(new OverlayStateEvent(o, this, {\n              interaction: this.type,\n              publish: !1\n            }));\n            return;\n          }\n          if (!this.isConnected || e !== this.open) return;\n          const n = async () => {\n            const r = this.triggerElement instanceof VirtualTrigger;\n            this.dispatchEvent(new OverlayStateEvent(o, this, {\n              interaction: this.type,\n              publish: r\n            })), t.dispatchEvent(new OverlayStateEvent(o, this, {\n              interaction: this.type,\n              publish: !1\n            })), this.triggerElement && !r && this.triggerElement.dispatchEvent(new OverlayStateEvent(o, this, {\n              interaction: this.type,\n              publish: !0\n            })), this.state = e ? \"opened\" : \"closed\", this.returnFocus(), await nextFrame(), await nextFrame(), e === this.open && e === !1 && this.requestSlottable();\n          };\n          !e && this.dialogEl.open ? (this.dialogEl.addEventListener(\"close\", () => {\n            n();\n          }, {\n            once: !0\n          }), this.dialogEl.close()) : n();\n        };\n      return this.elements.forEach((t, s) => {\n        guaranteedAllTransitionend(t, m(t, s), a(t, s));\n      }), i;\n    }\n    async dialogApplyFocus(e, i) {\n      this.applyFocus(e, i);\n    }\n  }\n  return p;\n}\nconst C$1 = CSS.supports(\"(overlay: auto)\");\nfunction f(a) {\n  let c = !1;\n  try {\n    c = a.matches(\":popover-open\");\n  } catch (e) {}\n  let p = !1;\n  try {\n    p = a.matches(\":open\");\n  } catch (e) {}\n  return c || p;\n}\nfunction OverlayPopover(a) {\n  class c extends a {\n    async manageDelay(e) {\n      if (e === !1 || e !== this.open) {\n        overlayTimer.close(this);\n        return;\n      }\n      this.delayed && (await overlayTimer.openTimer(this)) && (this.open = !e);\n    }\n    async shouldHidePopover(e) {\n      if (e && this.open !== e) return;\n      const o = async ({\n        newState: i\n      } = {}) => {\n        i !== \"open\" && (await this.placementController.resetOverlayPosition());\n      };\n      if (!f(this.dialogEl)) {\n        o();\n        return;\n      }\n      this.dialogEl.addEventListener(\"toggle\", o, {\n        once: !0\n      });\n    }\n    async shouldShowPopover(e) {\n      let o = !1;\n      try {\n        o = this.dialogEl.matches(\":popover-open\");\n      } catch (u) {}\n      let i = !1;\n      try {\n        i = this.dialogEl.matches(\":open\");\n      } catch (u) {}\n      e && this.open === e && !o && !i && this.isConnected && (this.dialogEl.showPopover(), await this.managePosition());\n    }\n    async ensureOnDOM(e) {\n      await nextFrame(), C$1 || (await this.shouldHidePopover(e)), await this.shouldShowPopover(e), await nextFrame();\n    }\n    async makeTransition(e) {\n      if (this.open !== e) return null;\n      let o = null;\n      const i = (t, s) => () => {\n          if (t.open = e, s === 0) {\n            const r = e ? BeforetoggleOpenEvent : BeforetoggleClosedEvent;\n            this.dispatchEvent(new r());\n          }\n          if (!e || (t.matches(userFocusableSelector) && (o = t), o = o || firstFocusableIn(t), o)) return;\n          t.querySelectorAll(\"slot\").forEach(r => {\n            o || (o = firstFocusableSlottedIn(r));\n          });\n        },\n        u = (t, s) => async () => {\n          if (this.open !== e) return;\n          const n = e ? \"sp-opened\" : \"sp-closed\";\n          if (s > 0) {\n            t.dispatchEvent(new OverlayStateEvent(n, this, {\n              interaction: this.type,\n              publish: !1\n            }));\n            return;\n          }\n          const r = async () => {\n            if (this.open !== e) return;\n            await nextFrame();\n            const d = this.triggerElement instanceof VirtualTrigger;\n            this.dispatchEvent(new OverlayStateEvent(n, this, {\n              interaction: this.type,\n              publish: d\n            })), t.dispatchEvent(new OverlayStateEvent(n, this, {\n              interaction: this.type,\n              publish: !1\n            })), this.triggerElement && !d && this.triggerElement.dispatchEvent(new OverlayStateEvent(n, this, {\n              interaction: this.type,\n              publish: !0\n            })), this.state = e ? \"opened\" : \"closed\", this.returnFocus(), await nextFrame(), await nextFrame(), e === this.open && e === !1 && this.requestSlottable();\n          };\n          if (this.open !== e) return;\n          const v = f(this.dialogEl);\n          e !== !0 && v && this.isConnected ? (this.dialogEl.addEventListener(\"beforetoggle\", () => {\n            r();\n          }, {\n            once: !0\n          }), this.dialogEl.hidePopover()) : r();\n        };\n      return this.elements.forEach((t, s) => {\n        guaranteedAllTransitionend(t, i(t, s), u(t, s));\n      }), o;\n    }\n  }\n  return c;\n}\nfunction OverlayNoPopover(a) {\n  class m extends a {\n    async managePopoverOpen() {\n      await this.managePosition();\n    }\n    async manageDelay(e) {\n      if (e === !1 || e !== this.open) {\n        overlayTimer.close(this);\n        return;\n      }\n      this.delayed && (await overlayTimer.openTimer(this)) && (this.open = !e);\n    }\n    async ensureOnDOM(e) {}\n    async makeTransition(e) {\n      if (this.open !== e) return null;\n      let o = null;\n      const h = (t, r) => () => {\n          if (e !== this.open) return;\n          if (t.open = e, r === 0) {\n            const i = e ? BeforetoggleOpenEvent : BeforetoggleClosedEvent;\n            this.dispatchEvent(new i());\n          }\n          if (e !== !0 || (t.matches(userFocusableSelector) && (o = t), o = o || firstFocusableIn(t), o)) return;\n          t.querySelectorAll(\"slot\").forEach(i => {\n            o || (o = firstFocusableSlottedIn(i));\n          });\n        },\n        u = (t, r) => async () => {\n          if (this.open !== e) return;\n          const n = e ? \"sp-opened\" : \"sp-closed\";\n          if (t.dispatchEvent(new OverlayStateEvent(n, this, {\n            interaction: this.type\n          })), r > 0) return;\n          const i = this.triggerElement instanceof VirtualTrigger;\n          this.dispatchEvent(new OverlayStateEvent(n, this, {\n            interaction: this.type,\n            publish: i\n          })), this.triggerElement && !i && this.triggerElement.dispatchEvent(new OverlayStateEvent(n, this, {\n            interaction: this.type,\n            publish: !0\n          })), this.state = e ? \"opened\" : \"closed\", this.returnFocus(), await nextFrame(), await nextFrame(), e === this.open && e === !1 && this.requestSlottable();\n        };\n      return this.elements.forEach((t, r) => {\n        guaranteedAllTransitionend(t, h(t, r), u(t, r));\n      }), o;\n    }\n  }\n  return m;\n}\nconst h = \"showPopover\" in document.createElement(\"div\");\nclass c$1 {\n  constructor() {\n    this.root = document.body;\n    this.stack = [];\n    this.handlePointerdown = t => {\n      this.pointerdownPath = t.composedPath(), this.lastOverlay = this.stack.at(-1);\n    };\n    this.handlePointerup = () => {\n      var r;\n      if (!this.stack.length || !((r = this.pointerdownPath) != null && r.length)) return;\n      const t = this.pointerdownPath;\n      this.pointerdownPath = void 0;\n      const e = this.stack.length - 1,\n        s = this.stack.filter((n, i) => !t.find(o => o === n || o === (n == null ? void 0 : n.triggerElement) && (n == null ? void 0 : n.type) === \"hint\" || i === e && n !== this.lastOverlay && n.triggerInteraction === \"longpress\") && !n.shouldPreventClose() && n.type !== \"manual\");\n      s.reverse(), s.forEach(n => {\n        this.closeOverlay(n);\n        let i = n.parentOverlayToForceClose;\n        for (; i;) this.closeOverlay(i), i = i.parentOverlayToForceClose;\n      });\n    };\n    this.handleBeforetoggle = t => {\n      const {\n        target: e,\n        newState: s\n      } = t;\n      s !== \"open\" && this.closeOverlay(e);\n    };\n    this.handleKeydown = t => {\n      if (t.code !== \"Escape\" || !this.stack.length) return;\n      const e = this.stack.at(-1);\n      if ((e == null ? void 0 : e.type) === \"page\") {\n        t.preventDefault();\n        return;\n      }\n      h || (e == null ? void 0 : e.type) !== \"manual\" && e && this.closeOverlay(e);\n    };\n    this.bindEvents();\n  }\n  get document() {\n    return this.root.ownerDocument || document;\n  }\n  bindEvents() {\n    this.document.addEventListener(\"pointerdown\", this.handlePointerdown), this.document.addEventListener(\"pointerup\", this.handlePointerup), this.document.addEventListener(\"keydown\", this.handleKeydown);\n  }\n  closeOverlay(t) {\n    const e = this.stack.indexOf(t);\n    e > -1 && this.stack.splice(e, 1), t.open = !1;\n  }\n  overlaysByTriggerElement(t) {\n    return this.stack.filter(e => e.triggerElement === t);\n  }\n  add(t) {\n    if (this.stack.includes(t)) {\n      const e = this.stack.indexOf(t);\n      e > -1 && (this.stack.splice(e, 1), this.stack.push(t));\n      return;\n    }\n    if (t.type === \"auto\" || t.type === \"modal\" || t.type === \"page\") {\n      const e = \"sp-overlay-query-path\",\n        s = new Event(e, {\n          composed: !0,\n          bubbles: !0\n        });\n      t.addEventListener(e, r => {\n        const n = r.composedPath();\n        this.stack.forEach(i => {\n          !n.find(o => o === i) && i.type !== \"manual\" && this.closeOverlay(i);\n        });\n      }, {\n        once: !0\n      }), t.dispatchEvent(s);\n    } else if (t.type === \"hint\") {\n      if (this.stack.some(s => s.type !== \"manual\" && s.triggerElement && s.triggerElement === t.triggerElement)) {\n        t.open = !1;\n        return;\n      }\n      this.stack.forEach(s => {\n        s.type === \"hint\" && this.closeOverlay(s);\n      });\n    }\n    requestAnimationFrame(() => {\n      this.stack.push(t), t.addEventListener(\"beforetoggle\", this.handleBeforetoggle, {\n        once: !0\n      });\n    });\n  }\n  remove(t) {\n    this.closeOverlay(t);\n  }\n}\nconst overlayStack = new c$1();\n\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition$1 = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip$1 = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      if (!specifiedFallbackPlacements && fallbackAxisSideDirection !== 'none') {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$map$so;\n                const placement = (_overflowsData$map$so = overflowsData.map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$map$so[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: 0,\n    crossAxis: 0,\n    alignmentAxis: null,\n    ...rawValue\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset$1 = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift$1 = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y\n        }\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size$1 = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const overflowAvailableHeight = height - overflow[heightSide];\n      const overflowAvailableWidth = width - overflow[widthSide];\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if (isYAxis) {\n        const maximumClippingWidth = width - overflow.left - overflow.right;\n        availableWidth = alignment || noShift ? min(overflowAvailableWidth, maximumClippingWidth) : maximumClippingWidth;\n      } else {\n        const maximumClippingHeight = height - overflow.top - overflow.bottom;\n        availableHeight = alignment || noShift ? min(overflowAvailableHeight, maximumClippingHeight) : maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  const webkit = isWebKit();\n  const css = getComputedStyle(element);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement && traverseIframes ? getOverflowAncestors(win.frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = currentWin.frameElement;\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = currentWin.frameElement;\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\nconst topLayerSelectors = [':popover-open', ':modal'];\nfunction isTopLayer(element) {\n  return topLayerSelectors.some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y\n  };\n}\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\nfunction getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  return getBoundingClientRect(getDocumentElement(element)).left + getNodeScroll(element).scrollLeft;\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      ...clippingAncestor,\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  const x = rect.left + scroll.scrollLeft - offsets.x;\n  const y = rect.top + scroll.scrollTop - offsets.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  return element.offsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = element.getBoundingClientRect();\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && (nextRefRect.x !== prevRefRect.x || nextRefRect.y !== prevRefRect.y || nextRefRect.width !== prevRefRect.width || nextRefRect.height !== prevRefRect.height)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\nfunction c(o) {\n  if (typeof o == \"undefined\") return 0;\n  const t = window.devicePixelRatio || 1;\n  return Math.round(o * t) / t;\n}\nconst p$1 = 8,\n  C = 100,\n  T = o => {\n    var e;\n    return (e = {\n      left: [\"right\", \"bottom\", \"top\"],\n      \"left-start\": [\"right-start\", \"bottom\", \"top\"],\n      \"left-end\": [\"right-end\", \"bottom\", \"top\"],\n      right: [\"left\", \"bottom\", \"top\"],\n      \"right-start\": [\"left-start\", \"bottom\", \"top\"],\n      \"right-end\": [\"left-end\", \"bottom\", \"top\"],\n      top: [\"bottom\", \"left\", \"right\"],\n      \"top-start\": [\"bottom-start\", \"left\", \"right\"],\n      \"top-end\": [\"bottom-end\", \"left\", \"right\"],\n      bottom: [\"top\", \"left\", \"right\"],\n      \"bottom-start\": [\"top-start\", \"left\", \"right\"],\n      \"bottom-end\": [\"top-end\", \"left\", \"right\"]\n    }[o]) != null ? e : [o];\n  };\nclass PlacementController {\n  constructor(t) {\n    this.originalPlacements = new WeakMap();\n    this.allowPlacementUpdate = !1;\n    this.closeForAncestorUpdate = () => {\n      !this.allowPlacementUpdate && this.options.type !== \"modal\" && this.cleanup && this.target.dispatchEvent(new Event(\"close\", {\n        bubbles: !0\n      })), this.allowPlacementUpdate = !1;\n    };\n    this.updatePlacement = () => {\n      this.computePlacement();\n    };\n    this.resetOverlayPosition = () => {\n      !this.target || !this.options || (this.clearOverlayPosition(), this.computePlacement());\n    };\n    this.host = t, this.host.addController(this);\n  }\n  async placeOverlay(t = this.target, e = this.options) {\n    if (this.target = t, this.options = e, !t || !e) return;\n    const m = autoUpdate(e.trigger, t, this.closeForAncestorUpdate, {\n        ancestorResize: !1,\n        elementResize: !1,\n        layoutShift: !1\n      }),\n      h = autoUpdate(e.trigger, t, this.updatePlacement, {\n        ancestorScroll: !1\n      });\n    this.cleanup = () => {\n      var n;\n      (n = this.host.elements) == null || n.forEach(a => {\n        a.addEventListener(\"sp-closed\", () => {\n          const r = this.originalPlacements.get(a);\n          r && a.setAttribute(\"placement\", r), this.originalPlacements.delete(a);\n        }, {\n          once: !0\n        });\n      }), m(), h();\n    };\n  }\n  async computePlacement() {\n    var g, u;\n    const {\n      options: t,\n      target: e\n    } = this;\n    await (document.fonts ? document.fonts.ready : Promise.resolve());\n    const m = t.trigger instanceof HTMLElement ? flip() : flip({\n        padding: p$1,\n        fallbackPlacements: T(t.placement)\n      }),\n      [h = 0, n = 0] = Array.isArray(t == null ? void 0 : t.offset) ? t.offset : [t.offset, 0],\n      a = (g = this.host.elements.find(i => i.tipElement)) == null ? void 0 : g.tipElement,\n      r = [offset({\n        mainAxis: h,\n        crossAxis: n\n      }), shift({\n        padding: p$1\n      }), m, size({\n        padding: p$1,\n        apply: ({\n          availableWidth: i,\n          availableHeight: d,\n          rects: {\n            floating: x\n          }\n        }) => {\n          const b = Math.max(C, Math.floor(d)),\n            l = x.height;\n          this.initialHeight = this.isConstrained && this.initialHeight || l, this.isConstrained = l < this.initialHeight || b <= l;\n          const O = this.isConstrained ? `${b}px` : \"\";\n          Object.assign(e.style, {\n            maxWidth: `${Math.floor(i)}px`,\n            maxHeight: O\n          });\n        }\n      }), ...(a ? [arrow({\n        element: a,\n        padding: t.tipPadding || p$1\n      })] : [])],\n      {\n        x: P,\n        y: E,\n        placement: s,\n        middlewareData: f\n      } = await computePosition(t.trigger, e, {\n        placement: t.placement,\n        middleware: r,\n        strategy: \"fixed\"\n      });\n    if (Object.assign(e.style, {\n      top: \"0px\",\n      left: \"0px\",\n      translate: `${c(P)}px ${c(E)}px`\n    }), e.setAttribute(\"actual-placement\", s), (u = this.host.elements) == null || u.forEach(i => {\n      this.originalPlacements.has(i) || this.originalPlacements.set(i, i.getAttribute(\"placement\")), i.setAttribute(\"placement\", s);\n    }), a && f.arrow) {\n      const {\n        x: i,\n        y: d\n      } = f.arrow;\n      Object.assign(a.style, {\n        top: s.startsWith(\"right\") || s.startsWith(\"left\") ? \"0px\" : \"\",\n        left: s.startsWith(\"bottom\") || s.startsWith(\"top\") ? \"0px\" : \"\",\n        translate: `${c(i)}px ${c(d)}px`\n      });\n    }\n  }\n  clearOverlayPosition() {\n    this.target && (this.target.style.removeProperty(\"max-height\"), this.target.style.removeProperty(\"max-width\"), this.initialHeight = void 0, this.isConstrained = !1);\n  }\n  hostConnected() {\n    document.addEventListener(\"sp-update-overlays\", this.resetOverlayPosition);\n  }\n  hostUpdated() {\n    var t;\n    this.host.open || ((t = this.cleanup) == null || t.call(this), this.cleanup = void 0);\n  }\n  hostDisconnected() {\n    var t;\n    (t = this.cleanup) == null || t.call(this), this.cleanup = void 0, document.removeEventListener(\"sp-update-overlays\", this.resetOverlayPosition);\n  }\n}\nfunction conditionAttributeWithoutId(t, i, n) {\n  const e = t.getAttribute(i);\n  let r = e ? e.split(/\\s+/) : [];\n  r = r.filter(s => !n.find(o => s === o)), r.length ? t.setAttribute(i, r.join(\" \")) : t.removeAttribute(i);\n}\nfunction conditionAttributeWithId(t, i, n) {\n  const e = Array.isArray(n) ? n : [n],\n    r = t.getAttribute(i),\n    s = r ? r.split(/\\s+/) : [];\n  return e.every(d => s.indexOf(d) > -1) ? () => {} : (s.push(...e), t.setAttribute(i, s.join(\" \")), () => conditionAttributeWithoutId(t, i, e));\n}\nvar InteractionTypes = (r => (r[r.click = 0] = \"click\", r[r.hover = 1] = \"hover\", r[r.longpress = 2] = \"longpress\", r))(InteractionTypes || {});\nclass InteractionController {\n  constructor(e, {\n    overlay: t,\n    isPersistent: r,\n    handleOverlayReady: i\n  }) {\n    this.target = e;\n    this.isPersistent = !1;\n    this.isPersistent = !!r, this.handleOverlayReady = i, this.isPersistent && this.init(), this.overlay = t;\n  }\n  get activelyOpening() {\n    return !1;\n  }\n  get open() {\n    var e, t;\n    return (t = (e = this.overlay) == null ? void 0 : e.open) != null ? t : !1;\n  }\n  set open(e) {\n    if (this.overlay) {\n      this.overlay.open = e;\n      return;\n    }\n    e && (customElements.whenDefined(\"sp-overlay\").then(async () => {\n      const {\n        Overlay: t\n      } = await Promise.resolve().then(function () {\n        return Overlay$1;\n      });\n      this.overlay = new t(), this.overlay.open = !0;\n    }), import('./sp-overlay-1351366a.js'));\n  }\n  get overlay() {\n    return this._overlay;\n  }\n  set overlay(e) {\n    var t;\n    e && this.overlay !== e && (this.overlay && this.overlay.removeController(this), this._overlay = e, this.overlay.addController(this), this.initOverlay(), this.prepareDescription(this.target), (t = this.handleOverlayReady) == null || t.call(this, this.overlay));\n  }\n  prepareDescription(e) {}\n  releaseDescription() {}\n  shouldCompleteOpen() {}\n  init() {}\n  initOverlay() {}\n  abort() {\n    var e;\n    this.releaseDescription(), (e = this.abortController) == null || e.abort();\n  }\n  hostConnected() {\n    this.init();\n  }\n  hostDisconnected() {\n    this.isPersistent || this.abort();\n  }\n}\nconst g = 300;\nconst LONGPRESS_INSTRUCTIONS = {\n  touch: \"Double tap and long press for additional options\",\n  keyboard: \"Press Space or Alt+Down Arrow for additional options\",\n  mouse: \"Click and hold for additional options\"\n};\nclass LongpressController extends InteractionController {\n  constructor() {\n    super(...arguments);\n    this.type = InteractionTypes.longpress;\n    this.longpressState = null;\n    this.releaseDescription = noop;\n    this.handlePointerup = () => {\n      var e;\n      clearTimeout(this.timeout), this.target && (this.longpressState = ((e = this.overlay) == null ? void 0 : e.state) === \"opening\" ? \"pressed\" : null, document.removeEventListener(\"pointerup\", this.handlePointerup), document.removeEventListener(\"pointercancel\", this.handlePointerup));\n    };\n  }\n  get activelyOpening() {\n    return this.longpressState === \"opening\" || this.longpressState === \"pressed\";\n  }\n  handleLongpress() {\n    this.open = !0, this.longpressState = this.longpressState === \"potential\" ? \"opening\" : \"pressed\";\n  }\n  handlePointerdown(e) {\n    !this.target || e.button !== 0 || (this.longpressState = \"potential\", document.addEventListener(\"pointerup\", this.handlePointerup), document.addEventListener(\"pointercancel\", this.handlePointerup), \"holdAffordance\" in this.target) || (this.timeout = setTimeout(() => {\n      this.target && this.target.dispatchEvent(new CustomEvent(\"longpress\", {\n        bubbles: !0,\n        composed: !0,\n        detail: {\n          source: \"pointer\"\n        }\n      }));\n    }, g));\n  }\n  handleKeydown(e) {\n    const {\n      code: t,\n      altKey: o\n    } = e;\n    o && t === \"ArrowDown\" && (e.stopPropagation(), e.stopImmediatePropagation());\n  }\n  handleKeyup(e) {\n    const {\n      code: t,\n      altKey: o\n    } = e;\n    if (t === \"Space\" || o && t === \"ArrowDown\") {\n      if (!this.target) return;\n      e.stopPropagation(), this.target.dispatchEvent(new CustomEvent(\"longpress\", {\n        bubbles: !0,\n        composed: !0,\n        detail: {\n          source: \"keyboard\"\n        }\n      })), setTimeout(() => {\n        this.longpressState = null;\n      });\n    }\n  }\n  prepareDescription(e) {\n    if (this.releaseDescription !== noop || !this.overlay.elements.length) return;\n    const t = document.createElement(\"div\");\n    t.id = `longpress-describedby-descriptor-${randomID()}`;\n    const o = isIOS() || isAndroid() ? \"touch\" : \"keyboard\";\n    t.textContent = LONGPRESS_INSTRUCTIONS[o], t.slot = \"longpress-describedby-descriptor\";\n    const n = e.getRootNode(),\n      s = this.overlay.getRootNode();\n    n === s ? this.overlay.append(t) : (t.hidden = !(\"host\" in n), e.insertAdjacentElement(\"afterend\", t));\n    const i = conditionAttributeWithId(e, \"aria-describedby\", [t.id]);\n    this.releaseDescription = () => {\n      i(), t.remove(), this.releaseDescription = noop;\n    };\n  }\n  shouldCompleteOpen() {\n    this.longpressState = this.longpressState === \"pressed\" ? null : this.longpressState;\n  }\n  init() {\n    var t;\n    (t = this.abortController) == null || t.abort(), this.abortController = new AbortController();\n    const {\n      signal: e\n    } = this.abortController;\n    this.target.addEventListener(\"longpress\", () => this.handleLongpress(), {\n      signal: e\n    }), this.target.addEventListener(\"pointerdown\", o => this.handlePointerdown(o), {\n      signal: e\n    }), this.prepareDescription(this.target), !this.target.holdAffordance && (this.target.addEventListener(\"keydown\", o => this.handleKeydown(o), {\n      signal: e\n    }), this.target.addEventListener(\"keyup\", o => this.handleKeyup(o), {\n      signal: e\n    }));\n  }\n}\nclass ClickController extends InteractionController {\n  constructor() {\n    super(...arguments);\n    this.type = InteractionTypes.click;\n    this.preventNextToggle = !1;\n  }\n  handleClick() {\n    this.preventNextToggle || (this.open = !this.open), this.preventNextToggle = !1;\n  }\n  handlePointerdown() {\n    this.preventNextToggle = this.open;\n  }\n  init() {\n    var t;\n    (t = this.abortController) == null || t.abort(), this.abortController = new AbortController();\n    const {\n      signal: e\n    } = this.abortController;\n    this.target.addEventListener(\"click\", () => this.handleClick(), {\n      signal: e\n    }), this.target.addEventListener(\"pointerdown\", () => this.handlePointerdown(), {\n      signal: e\n    });\n  }\n}\nconst d = 300;\nclass HoverController extends InteractionController {\n  constructor() {\n    super(...arguments);\n    this.type = InteractionTypes.hover;\n    this.elementIds = [];\n    this.focusedin = !1;\n    this.pointerentered = !1;\n  }\n  handleTargetFocusin() {\n    var e;\n    (e = document.activeElement) != null && e.matches(\":focus-visible\") && (this.open = !0, this.focusedin = !0);\n  }\n  handleTargetFocusout() {\n    this.focusedin = !1, !this.pointerentered && (this.open = !1);\n  }\n  handleTargetPointerenter() {\n    var e;\n    this.hoverTimeout && (clearTimeout(this.hoverTimeout), this.hoverTimeout = void 0), !((e = this.overlay) != null && e.disabled) && (this.open = !0, this.pointerentered = !0);\n  }\n  handleTargetPointerleave() {\n    this.doPointerleave();\n  }\n  handleHostPointerenter() {\n    this.hoverTimeout && (clearTimeout(this.hoverTimeout), this.hoverTimeout = void 0);\n  }\n  handleHostPointerleave() {\n    this.doPointerleave();\n  }\n  prepareDescription() {\n    if (!this.overlay.elements.length) return;\n    const e = this.target.getRootNode(),\n      t = this.overlay.elements[0].getRootNode(),\n      r = this.overlay.getRootNode();\n    e === r ? this.prepareOverlayRelativeDescription() : e === t && this.prepareContentRelativeDescription();\n  }\n  prepareOverlayRelativeDescription() {\n    const e = conditionAttributeWithId(this.target, \"aria-describedby\", [this.overlay.id]);\n    this.releaseDescription = () => {\n      e(), this.releaseDescription = noop;\n    };\n  }\n  prepareContentRelativeDescription() {\n    const e = [],\n      t = this.overlay.elements.map(i => (e.push(i.id), i.id || (i.id = `${this.overlay.tagName.toLowerCase()}-helper-${randomID()}`), i.id));\n    this.elementIds = e;\n    const r = conditionAttributeWithId(this.target, \"aria-describedby\", t);\n    this.releaseDescription = () => {\n      r(), this.overlay.elements.map((i, n) => {\n        i.id = this.elementIds[n];\n      }), this.releaseDescription = noop;\n    };\n  }\n  doPointerleave() {\n    this.pointerentered = !1;\n    const e = this.target;\n    this.focusedin && e.matches(\":focus-visible\") || (this.hoverTimeout = setTimeout(() => {\n      this.open = !1;\n    }, d));\n  }\n  init() {\n    var t;\n    (t = this.abortController) == null || t.abort(), this.abortController = new AbortController();\n    const {\n      signal: e\n    } = this.abortController;\n    this.target.addEventListener(\"focusin\", () => this.handleTargetFocusin(), {\n      signal: e\n    }), this.target.addEventListener(\"focusout\", () => this.handleTargetFocusout(), {\n      signal: e\n    }), this.target.addEventListener(\"pointerenter\", () => this.handleTargetPointerenter(), {\n      signal: e\n    }), this.target.addEventListener(\"pointerleave\", () => this.handleTargetPointerleave(), {\n      signal: e\n    }), this.overlay && this.initOverlay();\n  }\n  initOverlay() {\n    if (!this.abortController) return;\n    const {\n      signal: e\n    } = this.abortController;\n    this.overlay.addEventListener(\"pointerenter\", () => this.handleHostPointerenter(), {\n      signal: e\n    }), this.overlay.addEventListener(\"pointerleave\", () => this.handleHostPointerleave(), {\n      signal: e\n    });\n  }\n}\nconst strategies = {\n  click: ClickController,\n  longpress: LongpressController,\n  hover: HoverController\n};\nclass SlottableRequestEvent extends Event {\n  constructor(e, n, t) {\n    super(\"slottable-request\", {\n      bubbles: !1,\n      cancelable: !0,\n      composed: !1\n    }), this.name = e, this.data = n, this.slotName = t !== void 0 ? `${e}.${t}` : e;\n  }\n}\nconst removeSlottableRequest = Symbol(\"remove-slottable-request\");\nconst o = i$5`\n    :host{pointer-events:none;--swc-overlay-animation-distance:var(--spectrum-spacing-100);display:contents}:host(:has(>sp-tooltip)){--swc-overlay-animation-distance:var(--spectrum-tooltip-animation-distance)}.dialog{box-sizing:border-box;--sp-overlay-open:true;background:0 0;border:0;max-width:calc(100vw - 16px);height:auto;max-height:calc(100dvh - 16px);margin:0;padding:0;display:flex;position:fixed;inset:0 auto auto 0;overflow:visible;opacity:1!important}.dialog:not([is-visible]){display:none}.dialog:focus{outline:none}dialog:modal{--mod-popover-filter:var(--spectrum-popover-filter)}:host(:not([open])) .dialog{--sp-overlay-open:false}.dialog::backdrop{display:none}.dialog:before{content:\"\";position:absolute;inset:-999em;pointer-events:auto!important}.dialog:not(.not-immediately-closable):before{display:none}.dialog>div{width:100%}::slotted(*){pointer-events:auto;visibility:visible!important}::slotted(sp-popover){position:static}.dialog:not([actual-placement])[placement*=top]{padding-block:var(--swc-overlay-animation-distance);margin-top:var(--swc-overlay-animation-distance)}.dialog:not([actual-placement])[placement*=right]{padding-inline:var(--swc-overlay-animation-distance);margin-left:calc(-1*var(--swc-overlay-animation-distance))}.dialog:not([actual-placement])[placement*=bottom]{padding-block:var(--swc-overlay-animation-distance);margin-top:calc(-1*var(--swc-overlay-animation-distance))}.dialog:not([actual-placement])[placement*=left]{padding-inline:var(--swc-overlay-animation-distance);margin-left:var(--swc-overlay-animation-distance)}.dialog[actual-placement*=top]{padding-block:var(--swc-overlay-animation-distance);margin-top:var(--swc-overlay-animation-distance)}.dialog[actual-placement*=right]{padding-inline:var(--swc-overlay-animation-distance);margin-left:calc(-1*var(--swc-overlay-animation-distance))}.dialog[actual-placement*=bottom]{padding-block:var(--swc-overlay-animation-distance);margin-top:calc(-1*var(--swc-overlay-animation-distance))}.dialog[actual-placement*=left]{padding-inline:var(--swc-overlay-animation-distance);margin-left:var(--swc-overlay-animation-distance)}slot[name=longpress-describedby-descriptor]{display:none}@supports selector(:open){.dialog{opacity:0}.dialog:open{opacity:1;--mod-popover-filter:var(--spectrum-popover-filter)}}@supports selector(:popover-open){.dialog{opacity:0}.dialog:popover-open{opacity:1;--mod-popover-filter:var(--spectrum-popover-filter)}}@supports (overlay:auto){.dialog{transition:all var(--mod-overlay-animation-duration,var(--spectrum-animation-duration-100,.13s)),translate 0s,display var(--mod-overlay-animation-duration,var(--spectrum-animation-duration-100,.13s));transition-behavior:allow-discrete;display:none}.dialog:popover-open,.dialog:modal{display:flex}}@supports (not selector(:open)) and (not selector(:popover-open)){:host:not([open]) .dialog{pointer-events:none}.dialog[actual-placement]{z-index:calc(var(--swc-overlay-z-index-base,1000) + var(--swc-overlay-open-count))}}\n`;\nvar b = Object.defineProperty;\nvar E = Object.getOwnPropertyDescriptor;\nvar r = (u, a, e, t) => {\n  for (var o = t > 1 ? void 0 : t ? E(a, e) : a, s = u.length - 1, l; s >= 0; s--) (l = u[s]) && (o = (t ? l(a, e, o) : l(o)) || o);\n  return t && o && b(a, e, o), o;\n};\nconst B = \"showPopover\" in document.createElement(\"div\");\nlet p = OverlayDialog(AbstractOverlay);\nB ? p = OverlayPopover(p) : p = OverlayNoPopover(p);\nconst i = class i extends p {\n  constructor() {\n    super(...arguments);\n    this._delayed = !1;\n    this._disabled = !1;\n    this.offset = 0;\n    this._open = !1;\n    this.lastRequestSlottableState = !1;\n    this.receivesFocus = \"auto\";\n    this._state = \"closed\";\n    this.triggerElement = null;\n    this.type = \"auto\";\n    this.wasOpen = !1;\n    this.closeOnFocusOut = e => {\n      if (!e.relatedTarget) return;\n      const t = new Event(\"overlay-relation-query\", {\n        bubbles: !0,\n        composed: !0\n      });\n      e.relatedTarget.addEventListener(t.type, o => {\n        o.composedPath().includes(this) || (this.open = !1);\n      }), e.relatedTarget.dispatchEvent(t);\n    };\n  }\n  get delayed() {\n    var e;\n    return ((e = this.elements.at(-1)) == null ? void 0 : e.hasAttribute(\"delayed\")) || this._delayed;\n  }\n  set delayed(e) {\n    this._delayed = e;\n  }\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(e) {\n    var t;\n    this._disabled = e, e ? ((t = this.strategy) == null || t.abort(), this.wasOpen = this.open, this.open = !1) : (this.bindEvents(), this.open = this.open || this.wasOpen, this.wasOpen = !1);\n  }\n  get hasNonVirtualTrigger() {\n    return !!this.triggerElement && !(this.triggerElement instanceof VirtualTrigger);\n  }\n  get placementController() {\n    return this._placementController || (this._placementController = new PlacementController(this)), this._placementController;\n  }\n  get open() {\n    return this._open;\n  }\n  set open(e) {\n    var t;\n    e && this.disabled || e !== this.open && ((t = this.strategy) != null && t.activelyOpening && !e || (this._open = e, this.open && (i.openCount += 1), this.requestUpdate(\"open\", !this.open), this.open && this.requestSlottable()));\n  }\n  get state() {\n    return this._state;\n  }\n  set state(e) {\n    var o;\n    if (e === this.state) return;\n    const t = this.state;\n    this._state = e, (this.state === \"opened\" || this.state === \"closed\") && ((o = this.strategy) == null || o.shouldCompleteOpen()), this.requestUpdate(\"state\", t);\n  }\n  get elementResolver() {\n    return this._elementResolver || (this._elementResolver = new ElementResolutionController(this)), this._elementResolver;\n  }\n  get usesDialog() {\n    return this.type === \"modal\" || this.type === \"page\";\n  }\n  get popoverValue() {\n    if (\"popover\" in this) switch (this.type) {\n      case \"modal\":\n      case \"page\":\n        return;\n      case \"hint\":\n        return \"manual\";\n      default:\n        return this.type;\n    }\n  }\n  get requiresPosition() {\n    return !(this.type === \"page\" || !this.open || !this.triggerElement || !this.placement && this.type !== \"hint\");\n  }\n  managePosition() {\n    if (!this.requiresPosition || !this.open) return;\n    const e = this.offset || 0,\n      t = this.triggerElement,\n      o = this.placement || \"right\",\n      s = this.tipPadding;\n    this.placementController.placeOverlay(this.dialogEl, {\n      offset: e,\n      placement: o,\n      tipPadding: s,\n      trigger: t,\n      type: this.type\n    });\n  }\n  async managePopoverOpen() {\n    super.managePopoverOpen();\n    const e = this.open;\n    if (this.open !== e || (await this.manageDelay(e), this.open !== e) || (await this.ensureOnDOM(e), this.open !== e)) return;\n    const t = await this.makeTransition(e);\n    this.open === e && (await this.applyFocus(e, t));\n  }\n  async applyFocus(e, t) {\n    if (!(this.receivesFocus === \"false\" || this.type === \"hint\")) {\n      if (await nextFrame(), await nextFrame(), e === this.open && !this.open) {\n        this.hasNonVirtualTrigger && this.contains(this.getRootNode().activeElement) && this.triggerElement.focus();\n        return;\n      }\n      t == null || t.focus();\n    }\n  }\n  returnFocus() {\n    var t;\n    if (this.open || this.type === \"hint\") return;\n    const e = () => {\n      var l, m;\n      const o = [];\n      let s = document.activeElement;\n      for (; (l = s == null ? void 0 : s.shadowRoot) != null && l.activeElement;) s = s.shadowRoot.activeElement;\n      for (; s;) {\n        const h = s.assignedSlot || s.parentElement || ((m = s.getRootNode()) == null ? void 0 : m.host);\n        h && o.push(h), s = h;\n      }\n      return o;\n    };\n    this.receivesFocus !== \"false\" && (t = this.triggerElement) != null && t.focus && (this.contains(this.getRootNode().activeElement) || e().includes(this) || document.activeElement === document.body) && this.triggerElement.focus();\n  }\n  async manageOpen(e) {\n    if (!(!this.isConnected && this.open) && (this.hasUpdated || (await this.updateComplete), this.open ? (overlayStack.add(this), this.willPreventClose && (document.addEventListener(\"pointerup\", () => {\n      this.dialogEl.classList.toggle(\"not-immediately-closable\", !1), this.willPreventClose = !1;\n    }, {\n      once: !0\n    }), this.dialogEl.classList.toggle(\"not-immediately-closable\", !0))) : (e && this.dispose(), overlayStack.remove(this)), this.open && this.state !== \"opened\" ? this.state = \"opening\" : !this.open && this.state !== \"closed\" && (this.state = \"closing\"), this.usesDialog ? this.manageDialogOpen() : this.managePopoverOpen(), this.type === \"auto\")) {\n      const t = this.getRootNode();\n      this.open ? t.addEventListener(\"focusout\", this.closeOnFocusOut, {\n        capture: !0\n      }) : t.removeEventListener(\"focusout\", this.closeOnFocusOut, {\n        capture: !0\n      });\n    }\n  }\n  bindEvents() {\n    var e;\n    (e = this.strategy) == null || e.abort(), this.strategy = void 0, this.hasNonVirtualTrigger && this.triggerInteraction && (this.strategy = new strategies[this.triggerInteraction](this.triggerElement, {\n      overlay: this\n    }));\n  }\n  handleBeforetoggle(e) {\n    e.newState !== \"open\" && this.handleBrowserClose();\n  }\n  handleBrowserClose() {\n    var e;\n    if (!((e = this.strategy) != null && e.activelyOpening)) {\n      this.open = !1;\n      return;\n    }\n    this.manuallyKeepOpen();\n  }\n  manuallyKeepOpen() {\n    this.open = !0, this.placementController.allowPlacementUpdate = !0, this.manageOpen(!1);\n  }\n  handleSlotchange() {\n    var e, t;\n    this.elements.length ? this.hasNonVirtualTrigger && ((t = this.strategy) == null || t.prepareDescription(this.triggerElement)) : (e = this.strategy) == null || e.releaseDescription();\n  }\n  shouldPreventClose() {\n    const e = this.willPreventClose;\n    return this.willPreventClose = !1, e;\n  }\n  requestSlottable() {\n    this.lastRequestSlottableState !== this.open && (this.dispatchEvent(new SlottableRequestEvent(\"overlay-content\", this.open ? {} : removeSlottableRequest)), this.lastRequestSlottableState = this.open);\n  }\n  willUpdate(e) {\n    var o;\n    if (this.hasAttribute(\"id\") || this.setAttribute(\"id\", `${this.tagName.toLowerCase()}-${randomID()}`), e.has(\"open\") && (this.hasUpdated || this.open) && this.manageOpen(e.get(\"open\")), e.has(\"trigger\")) {\n      const [s, l] = ((o = this.trigger) == null ? void 0 : o.split(\"@\")) || [];\n      this.elementResolver.selector = s ? `#${s}` : \"\", this.triggerInteraction = l;\n    }\n    let t = !1;\n    e.has(elementResolverUpdatedSymbol) && (t = this.triggerElement, this.triggerElement = this.elementResolver.element), e.has(\"triggerElement\") && (t = e.get(\"triggerElement\")), t !== !1 && this.bindEvents();\n  }\n  updated(e) {\n    super.updated(e), e.has(\"placement\") && (this.placement ? this.dialogEl.setAttribute(\"actual-placement\", this.placement) : this.dialogEl.removeAttribute(\"actual-placement\"), this.open && typeof e.get(\"placement\") != \"undefined\" && this.placementController.resetOverlayPosition()), e.has(\"state\") && this.state === \"closed\" && typeof e.get(\"state\") != \"undefined\" && this.placementController.clearOverlayPosition();\n  }\n  renderContent() {\n    return x`\n            <slot @slotchange=${this.handleSlotchange}></slot>\n        `;\n  }\n  get dialogStyleMap() {\n    return {\n      \"--swc-overlay-open-count\": i.openCount.toString()\n    };\n  }\n  renderDialog() {\n    return x`\n            <dialog\n                class=\"dialog\"\n                part=\"dialog\"\n                placement=${o$3(this.requiresPosition ? this.placement || \"right\" : void 0)}\n                style=${o$2(this.dialogStyleMap)}\n                @close=${this.handleBrowserClose}\n                @cancel=${this.handleBrowserClose}\n                @beforetoggle=${this.handleBeforetoggle}\n                ?is-visible=${this.state !== \"closed\"}\n            >\n                ${this.renderContent()}\n            </dialog>\n        `;\n  }\n  renderPopover() {\n    return x`\n            <div\n                class=\"dialog\"\n                part=\"dialog\"\n                placement=${o$3(this.requiresPosition ? this.placement || \"right\" : void 0)}\n                popover=${o$3(this.popoverValue)}\n                style=${o$2(this.dialogStyleMap)}\n                @beforetoggle=${this.handleBeforetoggle}\n                @close=${this.handleBrowserClose}\n                ?is-visible=${this.state !== \"closed\"}\n            >\n                ${this.renderContent()}\n            </div>\n        `;\n  }\n  render() {\n    const e = this.type === \"modal\" || this.type === \"page\";\n    return x`\n            ${e ? this.renderDialog() : this.renderPopover()}\n            <slot name=\"longpress-describedby-descriptor\"></slot>\n        `;\n  }\n  connectedCallback() {\n    super.connectedCallback(), this.addEventListener(\"close\", () => {\n      this.open = !1;\n    }), this.hasUpdated && this.bindEvents();\n  }\n  disconnectedCallback() {\n    var e;\n    (e = this.strategy) == null || e.releaseDescription(), this.open = !1, super.disconnectedCallback();\n  }\n};\ni.styles = [o], i.openCount = 1, r([n$2({\n  type: Boolean\n})], i.prototype, \"delayed\", 1), r([e$3(\".dialog\")], i.prototype, \"dialogEl\", 2), r([n$2({\n  type: Boolean\n})], i.prototype, \"disabled\", 1), r([o$4({\n  flatten: !0,\n  selector: ':not([slot=\"longpress-describedby-descriptor\"], slot)'\n})], i.prototype, \"elements\", 2), r([n$2({\n  type: Number\n})], i.prototype, \"offset\", 2), r([n$2({\n  type: Boolean,\n  reflect: !0\n})], i.prototype, \"open\", 1), r([n$2()], i.prototype, \"placement\", 2), r([n$2({\n  attribute: \"receives-focus\"\n})], i.prototype, \"receivesFocus\", 2), r([e$3(\"slot\")], i.prototype, \"slotEl\", 2), r([r$1()], i.prototype, \"state\", 1), r([n$2({\n  type: Number,\n  attribute: \"tip-padding\"\n})], i.prototype, \"tipPadding\", 2), r([n$2()], i.prototype, \"trigger\", 2), r([n$2({\n  attribute: !1\n})], i.prototype, \"triggerElement\", 2), r([n$2({\n  attribute: !1\n})], i.prototype, \"triggerInteraction\", 2), r([n$2()], i.prototype, \"type\", 2);\nlet Overlay = i;\nconst Overlay$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Overlay: Overlay,\n  LONGPRESS_INSTRUCTIONS: LONGPRESS_INSTRUCTIONS\n});\nconst overlayCss = \":host{display:block}\";\nconst BciOverlay = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this._shouldReopen = false;\n    this.config = {\n      placement: 'bottom-start'\n    };\n    this.host = undefined;\n    this.dialog = undefined;\n  }\n  onConfigChange(newConfig, oldConfig) {\n    if (this.close !== undefined && !!oldConfig && newConfig.placement !== oldConfig.placement) {\n      this._shouldReopen = true;\n      this.hide();\n    }\n  }\n  componentDidRender() {\n    if (this._shouldReopen) {\n      this._shouldReopen = false;\n      setTimeout(() => this.show(), 250);\n    }\n  }\n  /**\n   * try to show the overlay and return the current visibility state\n   */\n  async show() {\n    if (this.close === undefined && this.host && this.dialog) {\n      this.close = await Overlay.open(this.dialog, Object.assign(Object.assign({}, this.config), {\n        trigger: this.host,\n        type: 'manual'\n      }));\n      document.body.append(this.close);\n      return true;\n    }\n    return false;\n  }\n  /**\n   * update the overlay, e.g. to fix positioning\n   */\n  async update() {\n    Overlay.update();\n  }\n  /**\n   * try to hide the overlay and return the current visibility state\n   */\n  async hide() {\n    var _a;\n    if (this.close !== undefined) {\n      (_a = this.close) === null || _a === void 0 ? void 0 : _a.dispose();\n      this.close = undefined;\n    }\n    return false;\n  }\n  render() {\n    return h$3(Host, null, h$3(\"template\", null, h$3(\"slot\", null)));\n  }\n  static get watchers() {\n    return {\n      \"config\": [\"onConfigChange\"]\n    };\n  }\n};\nBciOverlay.style = overlayCss;\nexport { BciOverlay as B, Overlay as O };\n\n"], "mappings": ";;;;;;;;;;;;;;AAQA,IAAM,MAAM;AAAZ,IACE,MAAM,IAAI,eAAe,WAAW,IAAI,YAAY,IAAI,SAAS,iBAAiB,wBAAwB,SAAS,aAAa,aAAa,cAAc;AAD7J,IAEE,MAAM,OAAO;AAFf,IAGE,MAAM,oBAAI,QAAQ;AACpB,IAAM,MAAN,MAAU;AAAA,EACR,YAAYA,IAAGC,IAAGC,IAAG;AACnB,QAAI,KAAK,eAAe,MAAIA,OAAM,IAAK,OAAM,MAAM,mEAAmE;AACtH,SAAK,UAAUF,IAAG,KAAK,IAAIC;AAAA,EAC7B;AAAA,EACA,IAAI,aAAa;AACf,QAAID,KAAI,KAAK;AACb,UAAMG,KAAI,KAAK;AACf,QAAI,OAAO,WAAWH,IAAG;AACvB,YAAMC,KAAI,WAAWE,MAAK,MAAMA,GAAE;AAClC,MAAAF,OAAMD,KAAI,IAAI,IAAIG,EAAC,IAAI,WAAWH,QAAO,KAAK,IAAIA,KAAI,IAAI,cAAc,GAAG,YAAY,KAAK,OAAO,GAAGC,MAAK,IAAI,IAAIE,IAAGH,EAAC;AAAA,IACzH;AACA,WAAOA;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAM,MAAM,CAAAA,OAAK,IAAI,IAAI,YAAY,OAAOA,KAAIA,KAAIA,KAAI,IAAI,QAAQ,GAAG;AAAvE,IACE,MAAM,CAACA,OAAMC,OAAM;AACjB,QAAMC,KAAI,MAAMF,GAAE,SAASA,GAAE,CAAC,IAAIC,GAAE,OAAO,CAACA,IAAGE,IAAGD,OAAMD,MAAK,CAAAD,OAAK;AAChE,QAAI,SAAOA,GAAE,aAAc,QAAOA,GAAE;AACpC,QAAI,YAAY,OAAOA,GAAG,QAAOA;AACjC,UAAM,MAAM,qEAAqEA,KAAI,sFAAsF;AAAA,EAC7K,GAAGG,EAAC,IAAIH,GAAEE,KAAI,CAAC,GAAGF,GAAE,CAAC,CAAC;AACtB,SAAO,IAAI,IAAIE,IAAGF,IAAG,GAAG;AAC1B;AARF,IASE,MAAM,CAACG,IAAGD,OAAM;AACd,MAAI,IAAK,CAAAC,GAAE,qBAAqBD,GAAE,IAAI,CAAAF,OAAKA,cAAa,gBAAgBA,KAAIA,GAAE,UAAU;AAAA,MAAO,YAAWC,MAAKC,IAAG;AAChH,UAAMA,KAAI,SAAS,cAAc,OAAO,GACtCE,KAAI,IAAI;AACV,eAAWA,MAAKF,GAAE,aAAa,SAASE,EAAC,GAAGF,GAAE,cAAcD,GAAE,SAASE,GAAE,YAAYD,EAAC;AAAA,EACxF;AACF;AAfF,IAgBE,MAAM,MAAM,CAAAF,OAAKA,KAAI,CAAAA,OAAKA,cAAa,iBAAiB,CAAAA,OAAK;AAC3D,MAAIC,KAAI;AACR,aAAWE,MAAKH,GAAE,SAAU,CAAAC,MAAKE,GAAE;AACnC,SAAO,IAAIF,EAAC;AACd,GAAGD,EAAC,IAAIA;AAOV,IAAM;AAAA,EACF,IAAI;AAAA,EACJ,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,gBAAgB;AAClB,IAAI;AAPN,IAQE,MAAM;AARR,IASE,MAAM,IAAI;AATZ,IAUE,MAAM,MAAM,IAAI,cAAc;AAVhC,IAWE,MAAM,IAAI;AAXZ,IAYE,MAAM,CAACA,IAAGG,OAAMH;AAZlB,IAaE,MAAM;AAAA,EACJ,YAAYA,IAAGG,IAAG;AAChB,YAAQA,IAAG;AAAA,MACT,KAAK;AACH,QAAAH,KAAIA,KAAI,MAAM;AACd;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,QAAAA,KAAI,QAAQA,KAAIA,KAAI,KAAK,UAAUA,EAAC;AAAA,IACxC;AACA,WAAOA;AAAA,EACT;AAAA,EACA,cAAcA,IAAGG,IAAG;AAClB,QAAIE,KAAIL;AACR,YAAQG,IAAG;AAAA,MACT,KAAK;AACH,QAAAE,KAAI,SAASL;AACb;AAAA,MACF,KAAK;AACH,QAAAK,KAAI,SAASL,KAAI,OAAO,OAAOA,EAAC;AAChC;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,YAAI;AACF,UAAAK,KAAI,KAAK,MAAML,EAAC;AAAA,QAClB,SAASA,IAAG;AACV,UAAAK,KAAI;AAAA,QACN;AAAA,IACJ;AACA,WAAOA;AAAA,EACT;AACF;AA5CF,IA6CE,MAAM,CAACL,IAAGG,OAAM,CAAC,IAAIH,IAAGG,EAAC;AA7C3B,IA8CE,MAAM;AAAA,EACJ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AACd;AACF,OAAO,aAAa,OAAO,UAAU,GAAG,IAAI,wBAAwB,oBAAI,QAAQ;AAChF,IAAM,MAAN,cAAkB,YAAY;AAAA,EAC5B,OAAO,eAAeH,IAAG;AACvB,SAAK,KAAK,IAAI,KAAK,MAAM,CAAC,GAAG,KAAKA,EAAC;AAAA,EACrC;AAAA,EACA,WAAW,qBAAqB;AAC9B,WAAO,KAAK,SAAS,GAAG,KAAK,QAAQ,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC;AAAA,EAC3D;AAAA,EACA,OAAO,eAAeA,IAAGG,KAAI,KAAK;AAChC,QAAIA,GAAE,UAAUA,GAAE,YAAY,QAAK,KAAK,KAAK,GAAG,KAAK,kBAAkB,IAAIH,IAAGG,EAAC,GAAG,CAACA,GAAE,YAAY;AAC/F,YAAME,KAAI,OAAO,GACfC,KAAI,KAAK,sBAAsBN,IAAGK,IAAGF,EAAC;AACxC,iBAAWG,MAAK,IAAI,KAAK,WAAWN,IAAGM,EAAC;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,OAAO,sBAAsBN,IAAGG,IAAGE,IAAG;AACpC,UAAM;AAAA,MACJ,KAAKJ;AAAA,MACL,KAAKM;AAAA,IACP,IAAI,IAAI,KAAK,WAAWP,EAAC,KAAK;AAAA,MAC5B,MAAM;AACJ,eAAO,KAAKG,EAAC;AAAA,MACf;AAAA,MACA,IAAIH,IAAG;AACL,aAAKG,EAAC,IAAIH;AAAA,MACZ;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AACJ,eAAOC,IAAG,KAAK,IAAI;AAAA,MACrB;AAAA,MACA,IAAIE,IAAG;AACL,cAAMG,KAAIL,IAAG,KAAK,IAAI;AACtB,QAAAM,GAAE,KAAK,MAAMJ,EAAC,GAAG,KAAK,cAAcH,IAAGM,IAAGD,EAAC;AAAA,MAC7C;AAAA,MACA,cAAc;AAAA,MACd,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO,mBAAmBL,IAAG;AAC3B,WAAO,KAAK,kBAAkB,IAAIA,EAAC,KAAK;AAAA,EAC1C;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,KAAK,eAAe,IAAI,mBAAmB,CAAC,EAAG;AACnD,UAAMA,KAAI,IAAI,IAAI;AAClB,IAAAA,GAAE,SAAS,GAAG,WAAWA,GAAE,MAAM,KAAK,IAAI,CAAC,GAAGA,GAAE,CAAC,IAAI,KAAK,oBAAoB,IAAI,IAAIA,GAAE,iBAAiB;AAAA,EAC3G;AAAA,EACA,OAAO,WAAW;AAChB,QAAI,KAAK,eAAe,IAAI,WAAW,CAAC,EAAG;AAC3C,QAAI,KAAK,YAAY,MAAI,KAAK,KAAK,GAAG,KAAK,eAAe,IAAI,YAAY,CAAC,GAAG;AAC5E,YAAMA,KAAI,KAAK,YACbG,KAAI,CAAC,GAAG,IAAIH,EAAC,GAAG,GAAG,IAAIA,EAAC,CAAC;AAC3B,iBAAWK,MAAKF,GAAG,MAAK,eAAeE,IAAGL,GAAEK,EAAC,CAAC;AAAA,IAChD;AACA,UAAML,KAAI,KAAK,OAAO,QAAQ;AAC9B,QAAI,SAASA,IAAG;AACd,YAAMG,KAAI,oBAAoB,IAAIH,EAAC;AACnC,UAAI,WAAWG,GAAG,YAAW,CAACH,IAAGK,EAAC,KAAKF,GAAG,MAAK,kBAAkB,IAAIH,IAAGK,EAAC;AAAA,IAC3E;AACA,SAAK,OAAO,oBAAI,IAAI;AACpB,eAAW,CAACL,IAAGG,EAAC,KAAK,KAAK,mBAAmB;AAC3C,YAAME,KAAI,KAAK,KAAKL,IAAGG,EAAC;AACxB,iBAAWE,MAAK,KAAK,KAAK,IAAIA,IAAGL,EAAC;AAAA,IACpC;AACA,SAAK,gBAAgB,KAAK,eAAe,KAAK,MAAM;AAAA,EACtD;AAAA,EACA,OAAO,eAAeG,IAAG;AACvB,UAAME,KAAI,CAAC;AACX,QAAI,MAAM,QAAQF,EAAC,GAAG;AACpB,YAAMF,KAAI,IAAI,IAAIE,GAAE,KAAK,IAAI,CAAC,EAAE,QAAQ,CAAC;AACzC,iBAAWA,MAAKF,GAAG,CAAAI,GAAE,QAAQ,IAAIF,EAAC,CAAC;AAAA,IACrC,MAAO,YAAWA,MAAKE,GAAE,KAAK,IAAIF,EAAC,CAAC;AACpC,WAAOE;AAAA,EACT;AAAA,EACA,OAAO,KAAKL,IAAGG,IAAG;AAChB,UAAME,KAAIF,GAAE;AACZ,WAAO,UAAOE,KAAI,SAAS,YAAY,OAAOA,KAAIA,KAAI,YAAY,OAAOL,KAAIA,GAAE,YAAY,IAAI;AAAA,EACjG;AAAA,EACA,cAAc;AACZ,UAAM,GAAG,KAAK,OAAO,QAAQ,KAAK,kBAAkB,OAAI,KAAK,aAAa,OAAI,KAAK,OAAO,MAAM,KAAK,KAAK;AAAA,EAC5G;AAAA,EACA,OAAO;AACL,SAAK,OAAO,IAAI,QAAQ,CAAAA,OAAK,KAAK,iBAAiBA,EAAC,GAAG,KAAK,OAAO,oBAAI,IAAI,GAAG,KAAK,KAAK,GAAG,KAAK,cAAc,GAAG,KAAK,YAAY,GAAG,QAAQ,CAAAA,OAAKA,GAAE,IAAI,CAAC;AAAA,EAC3J;AAAA,EACA,cAAcA,IAAG;AACf,KAAC,KAAK,SAAS,oBAAI,IAAI,GAAG,IAAIA,EAAC,GAAG,WAAW,KAAK,cAAc,KAAK,eAAeA,GAAE,gBAAgB;AAAA,EACxG;AAAA,EACA,iBAAiBA,IAAG;AAClB,SAAK,MAAM,OAAOA,EAAC;AAAA,EACrB;AAAA,EACA,OAAO;AACL,UAAMA,KAAI,oBAAI,IAAI,GAChBG,KAAI,KAAK,YAAY;AACvB,eAAWE,MAAKF,GAAE,KAAK,EAAG,MAAK,eAAeE,EAAC,MAAML,GAAE,IAAIK,IAAG,KAAKA,EAAC,CAAC,GAAG,OAAO,KAAKA,EAAC;AACrF,IAAAL,GAAE,OAAO,MAAM,KAAK,OAAOA;AAAA,EAC7B;AAAA,EACA,mBAAmB;AACjB,UAAMA,KAAI,KAAK,cAAc,KAAK,aAAa,KAAK,YAAY,iBAAiB;AACjF,WAAO,IAAIA,IAAG,KAAK,YAAY,aAAa,GAAGA;AAAA,EACjD;AAAA,EACA,oBAAoB;AAClB,SAAK,eAAe,KAAK,iBAAiB,GAAG,KAAK,eAAe,IAAE,GAAG,KAAK,MAAM,QAAQ,CAAAA,OAAKA,GAAE,gBAAgB,CAAC;AAAA,EACnH;AAAA,EACA,eAAeA,IAAG;AAAA,EAAC;AAAA,EACnB,uBAAuB;AACrB,SAAK,MAAM,QAAQ,CAAAA,OAAKA,GAAE,mBAAmB,CAAC;AAAA,EAChD;AAAA,EACA,yBAAyBA,IAAGG,IAAGE,IAAG;AAChC,SAAK,KAAKL,IAAGK,EAAC;AAAA,EAChB;AAAA,EACA,KAAKL,IAAGG,IAAG;AACT,UAAME,KAAI,KAAK,YAAY,kBAAkB,IAAIL,EAAC,GAChDC,KAAI,KAAK,YAAY,KAAKD,IAAGK,EAAC;AAChC,QAAI,WAAWJ,MAAK,SAAOI,GAAE,SAAS;AACpC,YAAMC,MAAK,WAAWD,GAAE,WAAW,cAAcA,GAAE,YAAY,KAAK,YAAYF,IAAGE,GAAE,IAAI;AACzF,WAAK,OAAOL,IAAG,QAAQM,KAAI,KAAK,gBAAgBL,EAAC,IAAI,KAAK,aAAaA,IAAGK,EAAC,GAAG,KAAK,OAAO;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,KAAKN,IAAGG,IAAG;AACT,UAAME,KAAI,KAAK,aACbJ,KAAII,GAAE,KAAK,IAAIL,EAAC;AAClB,QAAI,WAAWC,MAAK,KAAK,SAASA,IAAG;AACnC,YAAMD,KAAIK,GAAE,mBAAmBJ,EAAC,GAC9BK,KAAI,cAAc,OAAON,GAAE,YAAY;AAAA,QACrC,eAAeA,GAAE;AAAA,MACnB,IAAI,WAAWA,GAAE,WAAW,gBAAgBA,GAAE,YAAY;AAC5D,WAAK,OAAOC,IAAG,KAAKA,EAAC,IAAIK,GAAE,cAAcH,IAAGH,GAAE,IAAI,GAAG,KAAK,OAAO;AAAA,IACnE;AAAA,EACF;AAAA,EACA,cAAcA,IAAGG,IAAGE,IAAG;AACrB,QAAI,WAAWL,IAAG;AAChB,UAAIK,OAAM,KAAK,YAAY,mBAAmBL,EAAC,GAAG,EAAEK,GAAE,cAAc,KAAK,KAAKL,EAAC,GAAGG,EAAC,EAAG;AACtF,WAAK,EAAEH,IAAGG,IAAGE,EAAC;AAAA,IAChB;AACA,cAAO,KAAK,oBAAoB,KAAK,OAAO,KAAK,KAAK;AAAA,EACxD;AAAA,EACA,EAAEL,IAAGG,IAAGE,IAAG;AACT,SAAK,KAAK,IAAIL,EAAC,KAAK,KAAK,KAAK,IAAIA,IAAGG,EAAC,GAAG,SAAOE,GAAE,WAAW,KAAK,SAASL,OAAM,KAAK,SAAS,oBAAI,IAAI,GAAG,IAAIA,EAAC;AAAA,EACjH;AAAA,EACM,OAAO;AAAA;AACX,WAAK,kBAAkB;AACvB,UAAI;AACF,cAAM,KAAK;AAAA,MACb,SAASA,IAAG;AACV,gBAAQ,OAAOA,EAAC;AAAA,MAClB;AACA,YAAMA,KAAI,KAAK,eAAe;AAC9B,aAAO,QAAQA,OAAM,MAAMA,KAAI,CAAC,KAAK;AAAA,IACvC;AAAA;AAAA,EACA,iBAAiB;AACf,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,gBAAiB;AAC3B,QAAI,CAAC,KAAK,YAAY;AACpB,UAAI,KAAK,eAAe,KAAK,iBAAiB,GAAG,KAAK,MAAM;AAC1D,mBAAW,CAACA,IAAGG,EAAC,KAAK,KAAK,KAAM,MAAKH,EAAC,IAAIG;AAC1C,aAAK,OAAO;AAAA,MACd;AACA,YAAMH,KAAI,KAAK,YAAY;AAC3B,UAAIA,GAAE,OAAO,EAAG,YAAW,CAACG,IAAGE,EAAC,KAAKL,GAAG,UAAOK,GAAE,WAAW,KAAK,KAAK,IAAIF,EAAC,KAAK,WAAW,KAAKA,EAAC,KAAK,KAAK,EAAEA,IAAG,KAAKA,EAAC,GAAGE,EAAC;AAAA,IAC5H;AACA,QAAIL,KAAI;AACR,UAAMG,KAAI,KAAK;AACf,QAAI;AACF,MAAAH,KAAI,KAAK,aAAaG,EAAC,GAAGH,MAAK,KAAK,WAAWG,EAAC,GAAG,KAAK,MAAM,QAAQ,CAAAH,OAAKA,GAAE,aAAa,CAAC,GAAG,KAAK,OAAOG,EAAC,KAAK,KAAK,KAAK;AAAA,IAC5H,SAASA,IAAG;AACV,YAAMH,KAAI,OAAI,KAAK,KAAK,GAAGG;AAAA,IAC7B;AACA,IAAAH,MAAK,KAAK,KAAKG,EAAC;AAAA,EAClB;AAAA,EACA,WAAWH,IAAG;AAAA,EAAC;AAAA,EACf,KAAKA,IAAG;AACN,SAAK,MAAM,QAAQ,CAAAA,OAAKA,GAAE,cAAc,CAAC,GAAG,KAAK,eAAe,KAAK,aAAa,MAAI,KAAK,aAAaA,EAAC,IAAI,KAAK,QAAQA,EAAC;AAAA,EAC7H;AAAA,EACA,OAAO;AACL,SAAK,OAAO,oBAAI,IAAI,GAAG,KAAK,kBAAkB;AAAA,EAChD;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,kBAAkB;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAaA,IAAG;AACd,WAAO;AAAA,EACT;AAAA,EACA,OAAOA,IAAG;AACR,SAAK,SAAS,KAAK,KAAK,QAAQ,CAAAA,OAAK,KAAK,KAAKA,IAAG,KAAKA,EAAC,CAAC,CAAC,GAAG,KAAK,KAAK;AAAA,EACzE;AAAA,EACA,QAAQA,IAAG;AAAA,EAAC;AAAA,EACZ,aAAaA,IAAG;AAAA,EAAC;AACnB;AACA,IAAI,gBAAgB,CAAC,GAAG,IAAI,oBAAoB;AAAA,EAC9C,MAAM;AACR,GAAG,IAAI,IAAI,mBAAmB,CAAC,IAAI,oBAAI,IAAI,GAAG,IAAI,IAAI,WAAW,CAAC,IAAI,oBAAI,IAAI,GAAG,MAAM;AAAA,EACrF,iBAAiB;AACnB,CAAC,IAAI,IAAI,4BAA4B,CAAC,GAAG,KAAK,OAAO;AAOrD,IAAM,MAAM;AAAZ,IACE,MAAM,IAAI;AADZ,IAEE,MAAM,MAAM,IAAI,aAAa,YAAY;AAAA,EACvC,YAAY,CAAAA,OAAKA;AACnB,CAAC,IAAI;AAJP,IAKE,MAAM;AALR,IAME,MAAM,OAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;AANhD,IAOE,MAAM,MAAM;AAPd,IAQE,MAAM,IAAI,GAAG;AARf,IASE,MAAM;AATR,IAUE,IAAI,MAAM,IAAI,cAAc,EAAE;AAVhC,IAWE,MAAM,CAAAA,OAAK,SAASA,MAAK,YAAY,OAAOA,MAAK,cAAc,OAAOA;AAXxE,IAYE,IAAI,MAAM;AAZZ,IAaE,IAAI,CAAAA,OAAK,EAAEA,EAAC,KAAK,cAAc,OAAOA,KAAI,OAAO,QAAQ;AAb3D,IAcE,MAAM;AAdR,IAeE,MAAM;AAfR,IAgBE,IAAI;AAhBN,IAiBE,IAAI;AAjBN,IAkBE,IAAI,OAAO,KAAK,GAAG,qBAAqB,GAAG,KAAK,GAAG;AAAA,2BAAuC,GAAG;AAlB/F,IAmBE,MAAM;AAnBR,IAoBE,MAAM;AApBR,IAqBE,IAAI;AArBN,IAsBE,IAAI,CAAAA,OAAK,CAACK,OAAMF,QAAO;AAAA,EACrB,YAAYH;AAAA,EACZ,SAASK;AAAA,EACT,QAAQF;AACV;AA1BF,IA2BE,IAAI,EAAE,CAAC;AA3BT,IA4BE,MAAM,OAAO,IAAI,cAAc;AA5BjC,IA6BE,MAAM,OAAO,IAAI,aAAa;AA7BhC,IA8BE,IAAI,oBAAI,QAAQ;AA9BlB,IA+BE,MAAM,IAAI,iBAAiB,KAAK,GAAG;AACrC,SAAS,IAAIH,IAAGK,IAAG;AACjB,MAAI,CAAC,MAAM,QAAQL,EAAC,KAAK,CAACA,GAAE,eAAe,KAAK,EAAG,OAAM,MAAM,gCAAgC;AAC/F,SAAO,WAAW,MAAM,IAAI,WAAWK,EAAC,IAAIA;AAC9C;AACA,IAAM,IAAI,CAACL,IAAGK,OAAM;AAClB,QAAMF,KAAIH,GAAE,SAAS,GACnBE,KAAI,CAAC;AACP,MAAII,IACFE,KAAI,MAAMH,KAAI,UAAU,IACxBI,KAAI;AACN,WAASJ,KAAI,GAAGA,KAAIF,IAAGE,MAAK;AAC1B,UAAMF,KAAIH,GAAEK,EAAC;AACb,QAAIK,IACFC,IACAC,KAAI,IACJC,KAAI;AACN,WAAOA,KAAIV,GAAE,WAAWM,GAAE,YAAYI,IAAGF,KAAIF,GAAE,KAAKN,EAAC,GAAG,SAASQ,MAAK,CAAAE,KAAIJ,GAAE,WAAWA,OAAM,MAAM,UAAUE,GAAE,CAAC,IAAIF,KAAI,IAAI,WAAWE,GAAE,CAAC,IAAIF,KAAI,IAAI,WAAWE,GAAE,CAAC,KAAK,EAAE,KAAKA,GAAE,CAAC,CAAC,MAAML,KAAI,OAAO,OAAOK,GAAE,CAAC,GAAG,GAAG,IAAIF,KAAI,KAAK,WAAWE,GAAE,CAAC,MAAMF,KAAI,KAAKA,OAAM,IAAI,QAAQE,GAAE,CAAC,KAAKF,KAAIH,MAAK,KAAKM,KAAI,MAAM,WAAWD,GAAE,CAAC,IAAIC,KAAI,MAAMA,KAAIH,GAAE,YAAYE,GAAE,CAAC,EAAE,QAAQD,KAAIC,GAAE,CAAC,GAAGF,KAAI,WAAWE,GAAE,CAAC,IAAI,IAAI,QAAQA,GAAE,CAAC,IAAI,MAAM,OAAOF,OAAM,OAAOA,OAAM,MAAMA,KAAI,IAAIA,OAAM,KAAKA,OAAM,IAAIA,KAAI,OAAOA,KAAI,GAAGH,KAAI;AACxf,UAAMQ,KAAIL,OAAM,KAAKT,GAAEK,KAAI,CAAC,EAAE,WAAW,IAAI,IAAI,MAAM;AACvD,IAAAG,MAAKC,OAAM,MAAMN,KAAI,MAAMS,MAAK,KAAKV,GAAE,KAAKQ,EAAC,GAAGP,GAAE,MAAM,GAAGS,EAAC,IAAI,MAAMT,GAAE,MAAMS,EAAC,IAAI,MAAME,MAAKX,KAAI,OAAO,OAAOS,KAAIP,KAAIS;AAAA,EAC1H;AACA,SAAO,CAAC,IAAId,IAAGQ,MAAKR,GAAEG,EAAC,KAAK,UAAU,MAAME,KAAI,WAAW,GAAG,GAAGH,EAAC;AACpE;AACA,IAAM,IAAN,MAAM,GAAE;AAAA,EACN,YAAY;AAAA,IACV,SAASF;AAAA,IACT,YAAYG;AAAA,EACd,GAAGC,IAAG;AACJ,QAAIE;AACJ,SAAK,QAAQ,CAAC;AACd,QAAIG,KAAI,GACNC,KAAI;AACN,UAAMC,KAAIX,GAAE,SAAS,GACnBY,KAAI,KAAK,OACT,CAACG,IAAGC,EAAC,IAAI,EAAEhB,IAAGG,EAAC;AACjB,QAAI,KAAK,KAAK,GAAE,cAAcY,IAAGX,EAAC,GAAG,IAAI,cAAc,KAAK,GAAG,SAAS,MAAMD,IAAG;AAC/E,YAAMH,KAAI,KAAK,GAAG,QAAQ;AAC1B,MAAAA,GAAE,YAAY,GAAGA,GAAE,UAAU;AAAA,IAC/B;AACA,WAAO,UAAUM,KAAI,IAAI,SAAS,MAAMM,GAAE,SAASD,MAAI;AACrD,UAAI,MAAML,GAAE,UAAU;AACpB,YAAIA,GAAE,cAAc,EAAG,YAAWN,MAAKM,GAAE,kBAAkB,EAAG,KAAIN,GAAE,SAAS,GAAG,GAAG;AACjF,gBAAMK,KAAIW,GAAEN,IAAG,GACbP,KAAIG,GAAE,aAAaN,EAAC,EAAE,MAAM,GAAG,GAC/BC,KAAI,eAAe,KAAKI,EAAC;AAC3B,UAAAO,GAAE,KAAK;AAAA,YACL,MAAM;AAAA,YACN,OAAOH;AAAA,YACP,MAAMR,GAAE,CAAC;AAAA,YACT,SAASE;AAAA,YACT,MAAM,QAAQF,GAAE,CAAC,IAAI,IAAI,QAAQA,GAAE,CAAC,IAAI,IAAI,QAAQA,GAAE,CAAC,IAAI,IAAI;AAAA,UACjE,CAAC,GAAGK,GAAE,gBAAgBN,EAAC;AAAA,QACzB,MAAO,CAAAA,GAAE,WAAW,GAAG,MAAMY,GAAE,KAAK;AAAA,UAClC,MAAM;AAAA,UACN,OAAOH;AAAA,QACT,CAAC,GAAGH,GAAE,gBAAgBN,EAAC;AACvB,YAAI,EAAE,KAAKM,GAAE,OAAO,GAAG;AACrB,gBAAMN,KAAIM,GAAE,YAAY,MAAM,GAAG,GAC/BH,KAAIH,GAAE,SAAS;AACjB,cAAIG,KAAI,GAAG;AACT,YAAAG,GAAE,cAAc,MAAM,IAAI,cAAc;AACxC,qBAASD,KAAI,GAAGA,KAAIF,IAAGE,KAAK,CAAAC,GAAE,OAAON,GAAEK,EAAC,GAAG,EAAE,CAAC,GAAG,IAAI,SAAS,GAAGO,GAAE,KAAK;AAAA,cACtE,MAAM;AAAA,cACN,OAAO,EAAEH;AAAA,YACX,CAAC;AACD,YAAAH,GAAE,OAAON,GAAEG,EAAC,GAAG,EAAE,CAAC;AAAA,UACpB;AAAA,QACF;AAAA,MACF,WAAW,MAAMG,GAAE,SAAU,KAAIA,GAAE,SAAS,IAAK,CAAAM,GAAE,KAAK;AAAA,QACtD,MAAM;AAAA,QACN,OAAOH;AAAA,MACT,CAAC;AAAA,WAAO;AACN,YAAIT,KAAI;AACR,eAAO,QAAQA,KAAIM,GAAE,KAAK,QAAQ,KAAKN,KAAI,CAAC,KAAK,CAAAY,GAAE,KAAK;AAAA,UACtD,MAAM;AAAA,UACN,OAAOH;AAAA,QACT,CAAC,GAAGT,MAAK,IAAI,SAAS;AAAA,MACxB;AACA,MAAAS;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,cAAcT,IAAGK,IAAG;AACzB,UAAMF,KAAI,IAAI,cAAc,UAAU;AACtC,WAAOA,GAAE,YAAYH,IAAGG;AAAA,EAC1B;AACF;AACA,SAAS,EAAEH,IAAGK,IAAGF,KAAIH,IAAGC,IAAG;AACzB,MAAII,OAAM,IAAK,QAAOA;AACtB,MAAIE,KAAI,WAAWN,KAAIE,GAAE,OAAOF,EAAC,IAAIE,GAAE;AACvC,QAAMD,KAAI,IAAIG,EAAC,IAAI,SAASA,GAAE;AAC9B,SAAOE,IAAG,gBAAgBL,OAAMK,IAAG,OAAO,KAAE,GAAG,WAAWL,KAAIK,KAAI,UAAUA,KAAI,IAAIL,GAAEF,EAAC,GAAGO,GAAE,KAAKP,IAAGG,IAAGF,EAAC,IAAI,WAAWA,MAAKE,GAAE,SAAS,CAAC,GAAGF,EAAC,IAAIM,KAAIJ,GAAE,OAAOI,KAAI,WAAWA,OAAMF,KAAI,EAAEL,IAAGO,GAAE,KAAKP,IAAGK,GAAE,MAAM,GAAGE,IAAGN,EAAC,IAAII;AAC1N;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,YAAYL,IAAGK,IAAG;AAChB,SAAK,OAAO,CAAC,GAAG,KAAK,OAAO,QAAQ,KAAK,OAAOL,IAAG,KAAK,OAAOK;AAAA,EACjE;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,EAAEL,IAAG;AACH,UAAM;AAAA,MACF,IAAI;AAAA,QACF,SAASK;AAAA,MACX;AAAA,MACA,OAAOF;AAAA,IACT,IAAI,KAAK,MACTF,MAAKD,IAAG,iBAAiB,KAAK,WAAWK,IAAG,IAAE;AAChD,QAAI,cAAcJ;AAClB,QAAIM,KAAI,IAAI,SAAS,GACnBL,KAAI,GACJE,KAAI,GACJI,KAAIL,GAAE,CAAC;AACT,WAAO,WAAWK,MAAI;AACpB,UAAIN,OAAMM,GAAE,OAAO;AACjB,YAAIH;AACJ,cAAMG,GAAE,OAAOH,KAAI,IAAI,EAAEE,IAAGA,GAAE,aAAa,MAAMP,EAAC,IAAI,MAAMQ,GAAE,OAAOH,KAAI,IAAIG,GAAE,KAAKD,IAAGC,GAAE,MAAMA,GAAE,SAAS,MAAMR,EAAC,IAAI,MAAMQ,GAAE,SAASH,KAAI,IAAI,EAAEE,IAAG,MAAMP,EAAC,IAAI,KAAK,KAAK,KAAKK,EAAC,GAAGG,KAAIL,GAAE,EAAEC,EAAC;AAAA,MAC5L;AACA,MAAAF,OAAMM,IAAG,UAAUD,KAAI,IAAI,SAAS,GAAGL;AAAA,IACzC;AACA,WAAO,IAAI,cAAc,KAAKD;AAAA,EAChC;AAAA,EACA,EAAED,IAAG;AACH,QAAIK,KAAI;AACR,eAAWF,MAAK,KAAK,KAAM,YAAWA,OAAM,WAAWA,GAAE,WAAWA,GAAE,KAAKH,IAAGG,IAAGE,EAAC,GAAGA,MAAKF,GAAE,QAAQ,SAAS,KAAKA,GAAE,KAAKH,GAAEK,EAAC,CAAC,IAAIA;AAAA,EACnI;AACF;AACA,IAAM,IAAN,MAAM,GAAE;AAAA,EACN,IAAI,OAAO;AACT,WAAO,KAAK,MAAM,QAAQ,KAAK;AAAA,EACjC;AAAA,EACA,YAAYL,IAAGK,IAAGF,IAAGF,IAAG;AACtB,SAAK,OAAO,GAAG,KAAK,OAAO,KAAK,KAAK,OAAO,QAAQ,KAAK,OAAOD,IAAG,KAAK,OAAOK,IAAG,KAAK,OAAOF,IAAG,KAAK,UAAUF,IAAG,KAAK,OAAOA,IAAG,eAAe;AAAA,EACnJ;AAAA,EACA,IAAI,aAAa;AACf,QAAID,KAAI,KAAK,KAAK;AAClB,UAAMK,KAAI,KAAK;AACf,WAAO,WAAWA,MAAK,OAAOL,IAAG,aAAaA,KAAIK,GAAE,aAAaL;AAAA,EACnE;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAKA,IAAGK,KAAI,MAAM;AAChB,IAAAL,KAAI,EAAE,MAAMA,IAAGK,EAAC,GAAG,IAAIL,EAAC,IAAIA,OAAM,OAAO,QAAQA,MAAK,OAAOA,MAAK,KAAK,SAAS,OAAO,KAAK,KAAK,GAAG,KAAK,OAAO,OAAOA,OAAM,KAAK,QAAQA,OAAM,OAAO,KAAK,EAAEA,EAAC,IAAI,WAAWA,GAAE,aAAa,KAAK,EAAEA,EAAC,IAAI,WAAWA,GAAE,WAAW,KAAK,EAAEA,EAAC,IAAI,EAAEA,EAAC,IAAI,KAAK,EAAEA,EAAC,IAAI,KAAK,EAAEA,EAAC;AAAA,EAC1Q;AAAA,EACA,EAAEA,IAAG;AACH,WAAO,KAAK,KAAK,WAAW,aAAaA,IAAG,KAAK,IAAI;AAAA,EACvD;AAAA,EACA,EAAEA,IAAG;AACH,SAAK,SAASA,OAAM,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK,EAAEA,EAAC;AAAA,EACvD;AAAA,EACA,EAAEA,IAAG;AACH,SAAK,SAAS,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,YAAY,OAAOA,KAAI,KAAK,EAAE,IAAI,eAAeA,EAAC,CAAC,GAAG,KAAK,OAAOA;AAAA,EACpH;AAAA,EACA,EAAEA,IAAG;AACH,UAAM;AAAA,MACF,QAAQK;AAAA,MACR,YAAYF;AAAA,IACd,IAAIH,IACJC,KAAI,YAAY,OAAOE,KAAI,KAAK,KAAKH,EAAC,KAAK,WAAWG,GAAE,OAAOA,GAAE,KAAK,EAAE,cAAc,IAAIA,GAAE,GAAGA,GAAE,EAAE,CAAC,CAAC,GAAG,KAAK,OAAO,IAAIA;AAC1H,QAAI,KAAK,MAAM,SAASF,GAAG,MAAK,KAAK,EAAEI,EAAC;AAAA,SAAO;AAC7C,YAAML,KAAI,IAAI,EAAEC,IAAG,IAAI,GACrBE,KAAIH,GAAE,EAAE,KAAK,OAAO;AACtB,MAAAA,GAAE,EAAEK,EAAC,GAAG,KAAK,EAAEF,EAAC,GAAG,KAAK,OAAOH;AAAA,IACjC;AAAA,EACF;AAAA,EACA,KAAKA,IAAG;AACN,QAAIK,KAAI,EAAE,IAAIL,GAAE,OAAO;AACvB,WAAO,WAAWK,MAAK,EAAE,IAAIL,GAAE,SAASK,KAAI,IAAI,EAAEL,EAAC,CAAC,GAAGK;AAAA,EACzD;AAAA,EACA,EAAEL,IAAG;AACH,MAAE,KAAK,IAAI,MAAM,KAAK,OAAO,CAAC,GAAG,KAAK,KAAK;AAC3C,UAAMK,KAAI,KAAK;AACf,QAAIF,IACFF,KAAI;AACN,eAAWM,MAAKP,GAAG,CAAAC,OAAMI,GAAE,SAASA,GAAE,KAAKF,KAAI,IAAI,GAAE,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,MAAM,KAAK,OAAO,CAAC,IAAIA,KAAIE,GAAEJ,EAAC,GAAGE,GAAE,KAAKI,EAAC,GAAGN;AAC3H,IAAAA,KAAII,GAAE,WAAW,KAAK,KAAKF,MAAKA,GAAE,KAAK,aAAaF,EAAC,GAAGI,GAAE,SAASJ;AAAA,EACrE;AAAA,EACA,KAAKD,KAAI,KAAK,KAAK,aAAaK,IAAG;AACjC,SAAK,KAAK,OAAO,OAAI,MAAIA,EAAC,GAAGL,MAAKA,OAAM,KAAK,QAAO;AAClD,YAAMK,KAAIL,GAAE;AACZ,MAAAA,GAAE,OAAO,GAAGA,KAAIK;AAAA,IAClB;AAAA,EACF;AAAA,EACA,aAAaL,IAAG;AACd,eAAW,KAAK,SAAS,KAAK,OAAOA,IAAG,KAAK,OAAOA,EAAC;AAAA,EACvD;AACF;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,IAAI,UAAU;AACZ,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,YAAYA,IAAGK,IAAGF,IAAGF,IAAGM,IAAG;AACzB,SAAK,OAAO,GAAG,KAAK,OAAO,KAAK,KAAK,OAAO,QAAQ,KAAK,UAAUP,IAAG,KAAK,OAAOK,IAAG,KAAK,OAAOJ,IAAG,KAAK,UAAUM,IAAGJ,GAAE,SAAS,KAAK,OAAOA,GAAE,CAAC,KAAK,OAAOA,GAAE,CAAC,KAAK,KAAK,OAAO,MAAMA,GAAE,SAAS,CAAC,EAAE,KAAK,IAAI,OAAO,CAAC,GAAG,KAAK,UAAUA,MAAK,KAAK,OAAO;AAAA,EAC1P;AAAA,EACA,KAAKH,IAAGK,KAAI,MAAMF,IAAGF,IAAG;AACtB,UAAMM,KAAI,KAAK;AACf,QAAIL,KAAI;AACR,QAAI,WAAWK,GAAG,CAAAP,KAAI,EAAE,MAAMA,IAAGK,IAAG,CAAC,GAAGH,KAAI,CAAC,IAAIF,EAAC,KAAKA,OAAM,KAAK,QAAQA,OAAM,KAAKE,OAAM,KAAK,OAAOF;AAAA,SAAQ;AAC7G,YAAMC,KAAID;AACV,UAAII,IAAGE;AACP,WAAKN,KAAIO,GAAE,CAAC,GAAGH,KAAI,GAAGA,KAAIG,GAAE,SAAS,GAAGH,KAAK,CAAAE,KAAI,EAAE,MAAML,GAAEE,KAAIC,EAAC,GAAGC,IAAGD,EAAC,GAAGE,OAAM,QAAQA,KAAI,KAAK,KAAKF,EAAC,IAAIF,OAAM,CAAC,IAAII,EAAC,KAAKA,OAAM,KAAK,KAAKF,EAAC,GAAGE,OAAM,MAAMN,KAAI,MAAMA,OAAM,QAAQA,OAAMM,MAAK,MAAMC,GAAEH,KAAI,CAAC,IAAI,KAAK,KAAKA,EAAC,IAAIE;AAAA,IACjO;AACA,IAAAJ,MAAK,CAACD,MAAK,KAAK,EAAED,EAAC;AAAA,EACrB;AAAA,EACA,EAAEA,IAAG;AACH,IAAAA,OAAM,MAAM,KAAK,QAAQ,gBAAgB,KAAK,IAAI,IAAI,KAAK,QAAQ,aAAa,KAAK,MAAMA,MAAK,EAAE;AAAA,EACpG;AACF;AACA,IAAM,IAAN,cAAgB,EAAE;AAAA,EAChB,cAAc;AACZ,UAAM,GAAG,SAAS,GAAG,KAAK,OAAO;AAAA,EACnC;AAAA,EACA,EAAEA,IAAG;AACH,SAAK,QAAQ,KAAK,IAAI,IAAIA,OAAM,MAAM,SAASA;AAAA,EACjD;AACF;AACA,IAAM,IAAN,cAAgB,EAAE;AAAA,EAChB,cAAc;AACZ,UAAM,GAAG,SAAS,GAAG,KAAK,OAAO;AAAA,EACnC;AAAA,EACA,EAAEA,IAAG;AACH,SAAK,QAAQ,gBAAgB,KAAK,MAAM,CAAC,CAACA,MAAKA,OAAM,GAAG;AAAA,EAC1D;AACF;AACA,IAAM,IAAN,cAAgB,EAAE;AAAA,EAChB,YAAYA,IAAGK,IAAGF,IAAGF,IAAGM,IAAG;AACzB,UAAMP,IAAGK,IAAGF,IAAGF,IAAGM,EAAC,GAAG,KAAK,OAAO;AAAA,EACpC;AAAA,EACA,KAAKP,IAAGK,KAAI,MAAM;AAChB,SAAKL,KAAI,EAAE,MAAMA,IAAGK,IAAG,CAAC,KAAK,SAAS,IAAK;AAC3C,UAAMF,KAAI,KAAK,MACbF,KAAID,OAAM,OAAOG,OAAM,OAAOH,GAAE,YAAYG,GAAE,WAAWH,GAAE,SAASG,GAAE,QAAQH,GAAE,YAAYG,GAAE,SAC9FI,KAAIP,OAAM,QAAQG,OAAM,OAAOF;AACjC,IAAAA,MAAK,KAAK,QAAQ,oBAAoB,KAAK,MAAM,MAAME,EAAC,GAAGI,MAAK,KAAK,QAAQ,iBAAiB,KAAK,MAAM,MAAMP,EAAC,GAAG,KAAK,OAAOA;AAAA,EACjI;AAAA,EACA,YAAYA,IAAG;AACb,kBAAc,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,SAAS,QAAQ,KAAK,SAASA,EAAC,IAAI,KAAK,KAAK,YAAYA,EAAC;AAAA,EAClH;AACF;AACA,IAAM,IAAN,MAAQ;AAAA,EACN,YAAYA,IAAGK,IAAGF,IAAG;AACnB,SAAK,UAAUH,IAAG,KAAK,OAAO,GAAG,KAAK,OAAO,QAAQ,KAAK,OAAOK,IAAG,KAAK,UAAUF;AAAA,EACrF;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,KAAKH,IAAG;AACN,MAAE,MAAMA,EAAC;AAAA,EACX;AACF;AACA,IAAM,IAAI,IAAI;AACd,IAAI,GAAG,CAAC,IAAI,IAAI,oBAAoB,CAAC,GAAG,KAAK,OAAO;AACpD,IAAM,IAAI,CAACA,IAAGK,IAAGF,OAAM;AACrB,QAAMF,KAAIE,IAAG,gBAAgBE;AAC7B,MAAIE,KAAIN,GAAE;AACV,MAAI,WAAWM,IAAG;AAChB,UAAMP,KAAIG,IAAG,gBAAgB;AAC7B,IAAAF,GAAE,aAAaM,KAAI,IAAI,EAAEF,GAAE,aAAa,EAAE,GAAGL,EAAC,GAAGA,IAAG,QAAQG,MAAK,CAAC,CAAC;AAAA,EACrE;AACA,SAAOI,GAAE,KAAKP,EAAC,GAAGO;AACpB;AAOA,IAAM,IAAN,cAAgB,IAAI;AAAA,EAClB,cAAc;AACZ,UAAM,GAAG,SAAS,GAAG,KAAK,gBAAgB;AAAA,MACxC,MAAM;AAAA,IACR,GAAG,KAAK,OAAO;AAAA,EACjB;AAAA,EACA,mBAAmB;AACjB,UAAMP,KAAI,MAAM,iBAAiB;AACjC,WAAO,KAAK,cAAc,iBAAiBA,GAAE,YAAYA;AAAA,EAC3D;AAAA,EACA,OAAOA,IAAG;AACR,UAAMK,KAAI,KAAK,OAAO;AACtB,SAAK,eAAe,KAAK,cAAc,cAAc,KAAK,cAAc,MAAM,OAAOL,EAAC,GAAG,KAAK,OAAO,EAAEK,IAAG,KAAK,YAAY,KAAK,aAAa;AAAA,EAC/I;AAAA,EACA,oBAAoB;AAClB,UAAM,kBAAkB,GAAG,KAAK,MAAM,aAAa,IAAE;AAAA,EACvD;AAAA,EACA,uBAAuB;AACrB,UAAM,qBAAqB,GAAG,KAAK,MAAM,aAAa,KAAE;AAAA,EAC1D;AAAA,EACA,SAAS;AACP,WAAO;AAAA,EACT;AACF;AACA,EAAE,gBAAgB,MAAI,EAAE,WAAW,IAAI,MAAI,WAAW,2BAA2B;AAAA,EAC/E,YAAY;AACd,CAAC;AACD,IAAM,MAAM,WAAW;AACvB,MAAM;AAAA,EACJ,YAAY;AACd,CAAC;AAAA,CACA,WAAW,uBAAuB,CAAC,GAAG,KAAK,OAAO;AAGnD,IAAM,UAAU;AAChB,IAAM,MAAM,oBAAI,IAAI;AAApB,IACE,MAAM,MAAM;AACV,QAAMF,KAAI,SAAS,gBAAgB,QAAQ,QAAQ,SAAS,gBAAgB,MAAM;AAClF,MAAI,QAAQ,CAAAD,OAAK;AACf,IAAAA,GAAE,aAAa,OAAOC,EAAC;AAAA,EACzB,CAAC;AACH;AANF,IAOE,IAAI,IAAI,iBAAiB,GAAG;AAC9B,EAAE,QAAQ,SAAS,iBAAiB;AAAA,EAClC,YAAY;AAAA,EACZ,iBAAiB,CAAC,KAAK;AACzB,CAAC;AACD,IAAM,MAAM,CAAAA,OAAK,OAAOA,GAAE,iCAAiC,eAAeA,GAAE,YAAY;AACxF,SAAS,cAAcA,IAAG;AAAA,EACxB,MAAMD,WAAUC,GAAE;AAAA,IAChB,IAAI,QAAQ;AACV,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,IACA,wBAAwB;AACtB,YAAMC,MAAK,CAACE,KAAI,aAAa;AAC3B,YAAIE;AACJ,YAAIR,KAAIM,GAAE;AACV,eAAON,MAAK,QAAQA,GAAE,cAAcA,GAAE,WAAW,gBAAgB,CAAAA,KAAIA,GAAE,WAAW;AAClF,cAAMU,KAAIV,KAAI,CAACA,EAAC,IAAI,CAAC;AACrB,eAAOA,MAAI;AACT,gBAAMK,KAAIL,GAAE,gBAAgBA,GAAE,mBAAmBQ,KAAIR,GAAE,YAAY,MAAM,OAAO,SAASQ,GAAE;AAC3F,UAAAH,MAAKK,GAAE,KAAKL,EAAC,GAAGL,KAAIK;AAAA,QACtB;AACA,eAAOK;AAAA,MACT,GAAG,KAAK,YAAY,CAAC,EAAE,CAAC;AACxB,UAAI,CAACN,GAAG,QAAO;AACf,UAAI;AACF,eAAOA,GAAE,QAAQ,gBAAgB,KAAKA,GAAE,QAAQ,gBAAgB;AAAA,MAClE,SAASE,IAAG;AACV,eAAOF,GAAE,QAAQ,gBAAgB;AAAA,MACnC;AAAA,IACF;AAAA,IACA,oBAAoB;AAClB,UAAI,CAAC,KAAK,aAAa,KAAK,GAAG;AAC7B,YAAIH,KAAI,KAAK,gBAAgB,KAAK;AAClC,eAAOA,OAAM,SAAS,mBAAmB,CAAC,IAAIA,EAAC,IAAI,CAAAA,KAAIA,GAAE,gBAAgBA,GAAE,cAAcA,GAAE;AAC3F,YAAI,KAAK,MAAMA,GAAE,QAAQ,QAAQA,GAAE,MAAM,KAAK,OAAO,OAAOA,OAAM,SAAS,gBAAiB,KAAI,IAAI,IAAI;AAAA,aAAO;AAC7G,gBAAM;AAAA,YACJ,WAAWG;AAAA,UACb,IAAIH;AACJ,UAAAG,GAAE,OAAO,GAAG,IAAI,MAAM,CAAC,eAAe,IAAIA,EAAC,IAAI,eAAe,YAAYA,EAAC,EAAE,KAAK,MAAM;AACtF,YAAAH,GAAE,8BAA8B,IAAI;AAAA,UACtC,CAAC,IAAIA,GAAE,8BAA8B,IAAI;AAAA,QAC3C;AACA,aAAK,aAAaA;AAAA,MACpB;AACA,YAAM,kBAAkB;AAAA,IAC1B;AAAA,IACA,uBAAuB;AACrB,YAAM,qBAAqB,GAAG,KAAK,eAAe,KAAK,eAAe,SAAS,kBAAkB,IAAI,OAAO,IAAI,IAAI,KAAK,WAAW,6BAA6B,IAAI,GAAG,KAAK,gBAAgB,KAAK;AAAA,IACpM;AAAA,EACF;AACA,SAAOC;AACT;AACA,IAAM,kBAAN,cAA8B,cAAc,CAAC,EAAE;AAAC;AAChD,gBAAgB,UAAU;AAO1B,IAAM,MAAM;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AAAA,EACN,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AACd;AANF,IAOE,MAAM,CAACF,KAAI,KAAKC,IAAGK,OAAM;AACvB,QAAM;AAAA,IACJ,MAAMF;AAAA,IACN,UAAUC;AAAA,EACZ,IAAIC;AACJ,MAAIH,KAAI,WAAW,oBAAoB,IAAIE,EAAC;AAC5C,MAAI,WAAWF,MAAK,WAAW,oBAAoB,IAAIE,IAAGF,KAAI,oBAAI,IAAI,CAAC,GAAGA,GAAE,IAAIG,GAAE,MAAMN,EAAC,GAAG,eAAeI,IAAG;AAC5G,UAAM;AAAA,MACJ,MAAMF;AAAA,IACR,IAAII;AACJ,WAAO;AAAA,MACL,IAAIA,IAAG;AACL,cAAMF,KAAIH,GAAE,IAAI,KAAK,IAAI;AACzB,QAAAA,GAAE,IAAI,KAAK,MAAMK,EAAC,GAAG,KAAK,cAAcJ,IAAGE,IAAGJ,EAAC;AAAA,MACjD;AAAA,MACA,KAAKC,IAAG;AACN,eAAO,WAAWA,MAAK,KAAK,EAAEC,IAAG,QAAQF,EAAC,GAAGC;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACA,MAAI,aAAaG,IAAG;AAClB,UAAM;AAAA,MACJ,MAAMF;AAAA,IACR,IAAII;AACJ,WAAO,SAAUA,IAAG;AAClB,YAAMF,KAAI,KAAKF,EAAC;AAChB,MAAAD,GAAE,KAAK,MAAMK,EAAC,GAAG,KAAK,cAAcJ,IAAGE,IAAGJ,EAAC;AAAA,IAC7C;AAAA,EACF;AACA,QAAM,MAAM,qCAAqCI,EAAC;AACpD;AACF,SAAS,IAAIJ,IAAG;AACd,SAAO,CAACC,IAAGC,OAAM,YAAY,OAAOA,KAAI,IAAIF,IAAGC,IAAGC,EAAC,KAAK,CAACF,IAAGC,IAAGC,OAAM;AACnE,UAAMI,KAAIL,GAAE,eAAeC,EAAC;AAC5B,WAAOD,GAAE,YAAY,eAAeC,IAAGI,KAAI,iCACtCN,KADsC;AAAA,MAEzC,SAAS;AAAA,IACX,KAAIA,EAAC,GAAGM,KAAI,OAAO,yBAAyBL,IAAGC,EAAC,IAAI;AAAA,EACtD,GAAGF,IAAGC,IAAGC,EAAC;AACZ;AAOA,SAAS,IAAII,IAAG;AACd,SAAO,IAAI,iCACNA,KADM;AAAA,IAET,OAAO;AAAA,IACP,WAAW;AAAA,EACb,EAAC;AACH;AAOA,IAAM,MAAM,CAACL,IAAGD,IAAGS,QAAOA,GAAE,eAAe,MAAIA,GAAE,aAAa,MAAI,QAAQ,YAAY,YAAY,OAAOT,MAAK,OAAO,eAAeC,IAAGD,IAAGS,EAAC,GAAGA;AAO9I,SAAS,IAAIR,IAAGK,IAAG;AACjB,SAAO,CAACF,IAAGD,IAAGE,OAAM;AAClB,UAAMH,KAAI,CAAAF,OAAKA,GAAE,YAAY,cAAcC,EAAC,KAAK;AACjD,QAAIK,IAAG;AACL,YAAM;AAAA,QACJ,KAAKL;AAAA,QACL,KAAKK;AAAA,MACP,IAAI,YAAY,OAAOH,KAAIC,KAAIC,OAAM,MAAM;AACzC,cAAML,KAAI,OAAO;AACjB,eAAO;AAAA,UACL,MAAM;AACJ,mBAAO,KAAKA,EAAC;AAAA,UACf;AAAA,UACA,IAAIC,IAAG;AACL,iBAAKD,EAAC,IAAIC;AAAA,UACZ;AAAA,QACF;AAAA,MACF,GAAG;AACH,aAAO,IAAIG,IAAGD,IAAG;AAAA,QACf,MAAM;AACJ,cAAIH,KAAIC,GAAE,KAAK,IAAI;AACnB,iBAAO,WAAWD,OAAMA,KAAIE,GAAE,IAAI,IAAI,SAASF,MAAK,KAAK,eAAeM,GAAE,KAAK,MAAMN,EAAC,IAAIA;AAAA,QAC5F;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO,IAAII,IAAGD,IAAG;AAAA,MACf,MAAM;AACJ,eAAOD,GAAE,IAAI;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAOA,SAAS,IAAIA,IAAG;AACd,SAAO,CAACD,IAAGG,OAAM;AACf,UAAM;AAAA,MACF,MAAME;AAAA,MACN,UAAUH;AAAA,IACZ,IAAID,MAAK,CAAC,GACVO,KAAI,UAAUH,KAAI,SAASA,EAAC,MAAM;AACpC,WAAO,IAAIL,IAAGG,IAAG;AAAA,MACf,MAAM;AACJ,cAAMJ,KAAI,KAAK,YAAY,cAAcS,EAAC,GACxCR,KAAID,IAAG,iBAAiBE,EAAC,KAAK,CAAC;AACjC,eAAO,WAAWC,KAAIF,KAAIA,GAAE,OAAO,CAAAD,OAAKA,GAAE,QAAQG,EAAC,CAAC;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,+BAA+B,OAAO,0BAA0B;AACtE,IAAM,8BAAN,MAAkC;AAAA,EAChC,YAAYF,IAAG;AAAA,IACb,UAAUD;AAAA,EACZ,IAAI;AAAA,IACF,UAAU;AAAA,EACZ,GAAG;AACD,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,mBAAmB,CAAAC,OAAK;AAC3B,UAAID,KAAI;AACR,MAAAC,GAAE,QAAQ,CAAAE,OAAK;AACb,YAAI,CAACH,IAAG;AACN,cAAIG,GAAE,SAAS,aAAa;AAC1B,kBAAMG,KAAI,KAAK,WAAW,CAAC,GAAGH,GAAE,YAAY,EAAE,SAAS,KAAK,OAAO,GACjEK,KAAI,CAAC,CAAC,KAAK,YAAY,CAAC,GAAGL,GAAE,UAAU,EAAE,KAAK,KAAK,iBAAiB;AACtE,YAAAH,KAAIA,MAAKM,MAAKE;AAAA,UAChB;AACA,cAAIL,GAAE,SAAS,cAAc;AAC3B,kBAAMG,KAAIH,GAAE,WAAW,KAAK,SAC1BK,KAAI,CAAC,CAAC,KAAK,YAAY,KAAK,kBAAkBL,GAAE,MAAM;AACxD,YAAAH,KAAIA,MAAKM,MAAKE;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC,GAAGR,MAAK,KAAK,eAAe;AAAA,IAC/B;AACA,SAAK,oBAAoB,CAAAC,OAAK;AAC5B,UAAID;AACJ,aAAO,KAAK,gBAAgBC,MAAK,OAAO,SAASA,GAAE,QAAQ,KAAK,gBAAgBD,KAAIC,MAAK,OAAO,SAASA,GAAE,YAAY,OAAO,SAASD,GAAE,KAAKC,IAAG,KAAK,QAAQ;AAAA,IAChK;AACA,SAAK,OAAOA,IAAG,KAAK,WAAWD,IAAG,KAAK,WAAW,IAAI,iBAAiB,KAAK,gBAAgB,GAAG,KAAK,KAAK,cAAc,IAAI;AAAA,EAC7H;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQC,IAAG;AACb,QAAIA,OAAM,KAAK,QAAS;AACxB,UAAMD,KAAI,KAAK;AACf,SAAK,WAAWC,IAAG,KAAK,KAAK,cAAc,8BAA8BD,EAAC;AAAA,EAC5E;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAASC,IAAG;AACd,IAAAA,OAAM,KAAK,aAAa,KAAK,eAAe,GAAG,KAAK,YAAYA,IAAG,KAAK,eAAe;AAAA,EACzF;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,SAAS,MAAM,CAAC;AAAA,EAC9B;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,CAAC,CAAC,KAAK,YAAY,KAAK,SAAS,WAAW,GAAG;AAAA,EACxD;AAAA,EACA,gBAAgB;AACd,SAAK,eAAe,GAAG,KAAK,SAAS,QAAQ,KAAK,KAAK,YAAY,GAAG;AAAA,MACpE,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AACjB,SAAK,eAAe,GAAG,KAAK,SAAS,WAAW;AAAA,EAClD;AAAA,EACA,iBAAiB;AACf,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,eAAe;AACpB;AAAA,IACF;AACA,UAAMA,KAAI,KAAK,KAAK,YAAY;AAChC,SAAK,UAAU,KAAK,eAAeA,GAAE,eAAe,KAAK,YAAY,IAAIA,GAAE,cAAc,KAAK,QAAQ;AAAA,EACxG;AAAA,EACA,iBAAiB;AACf,SAAK,UAAU;AAAA,EACjB;AACF;AAOA,IAAM,MAAM,CAAAC,OAAKA,MAAK;AAOtB,IAAM,IAAI;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,mBAAmB;AAAA,EACnB,OAAO;AAAA,EACP,SAAS;AACX;AAPF,IAQE,MAAM,CAAAF,OAAK,IAAIC,QAAO;AAAA,EACpB,iBAAiBD;AAAA,EACjB,QAAQC;AACV;AACF,IAAM,MAAN,MAAU;AAAA,EACR,YAAYD,IAAG;AAAA,EAAC;AAAA,EAChB,IAAI,OAAO;AACT,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EACA,KAAKA,IAAGC,IAAGI,IAAG;AACZ,SAAK,OAAOL,IAAG,KAAK,OAAOC,IAAG,KAAK,OAAOI;AAAA,EAC5C;AAAA,EACA,KAAKL,IAAGC,IAAG;AACT,WAAO,KAAK,OAAOD,IAAGC,EAAC;AAAA,EACzB;AAAA,EACA,OAAOD,IAAGC,IAAG;AACX,WAAO,KAAK,OAAO,GAAGA,EAAC;AAAA,EACzB;AACF;AAOA,IAAM,MAAM;AAAZ,IACE,MAAM,OAAO;AADf,IAEE,MAAM,IAAI,cAAc,IAAI;AAAA,EAC1B,YAAYgB,MAAK;AACf,QAAI,MAAMA,IAAG,GAAGA,KAAI,SAAS,EAAE,aAAa,YAAYA,KAAI,QAAQA,KAAI,SAAS,SAAS,EAAG,OAAM,MAAM,4GAA4G;AAAA,EACvN;AAAA,EACA,OAAOjB,IAAG;AACR,WAAO,OAAO,KAAKA,EAAC,EAAE,OAAO,CAACC,IAAGK,OAAM;AACrC,YAAMH,KAAIH,GAAEM,EAAC;AACb,aAAO,QAAQH,KAAIF,KAAIA,KAAI,GAAGK,KAAIA,GAAE,SAAS,GAAG,IAAIA,KAAIA,GAAE,QAAQ,qCAAqC,KAAK,EAAE,YAAY,CAAC,IAAIH,EAAC;AAAA,IAClI,GAAG,EAAE;AAAA,EACP;AAAA,EACA,OAAOF,IAAG,CAACK,EAAC,GAAG;AACb,UAAM;AAAA,MACJ,OAAOH;AAAA,IACT,IAAIF,GAAE;AACN,QAAI,WAAW,KAAK,GAAI,QAAO,KAAK,KAAK,IAAI,IAAI,OAAO,KAAKK,EAAC,CAAC,GAAG,KAAK,OAAOA,EAAC;AAC/E,eAAWN,MAAK,KAAK,GAAI,SAAQM,GAAEN,EAAC,MAAM,KAAK,GAAG,OAAOA,EAAC,GAAGA,GAAE,SAAS,GAAG,IAAIG,GAAE,eAAeH,EAAC,IAAIG,GAAEH,EAAC,IAAI;AAC5G,eAAWA,MAAKM,IAAG;AACjB,YAAML,KAAIK,GAAEN,EAAC;AACb,UAAI,QAAQC,IAAG;AACb,aAAK,GAAG,IAAID,EAAC;AACb,cAAMM,KAAI,YAAY,OAAOL,MAAKA,GAAE,SAAS,GAAG;AAChD,QAAAD,GAAE,SAAS,GAAG,KAAKM,KAAIH,GAAE,YAAYH,IAAGM,KAAIL,GAAE,MAAM,GAAG,GAAG,IAAIA,IAAGK,KAAI,MAAM,EAAE,IAAIH,GAAEH,EAAC,IAAIC;AAAA,MAC1F;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF,CAAC;AACH,SAAS,WAAW;AAClB,SAAO,MAAM,KAAK,OAAO,gBAAgB,IAAI,WAAW,CAAC,CAAC,GAAG,CAAAK,OAAK,KAAKA,KAAI,KAAK,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE;AACnH;AACA,SAAS,IAAIJ,IAAGG,IAAGG,KAAI,CAAC,GAAG;AACzB,WAASP,KAAI,GAAGA,KAAII,GAAE,QAAQ,EAAEJ,IAAG;AACjC,UAAMG,KAAIC,GAAEJ,EAAC,GACXK,KAAIJ,GAAED,EAAC,GACPD,KAAIM,GAAE,iBAAiBA,GAAE,YAAY;AACvC,IAAAE,GAAEP,EAAC,KAAKO,GAAEP,EAAC,EAAEG,EAAC,GAAGJ,MAAKA,OAAMM,MAAKN,GAAE,aAAaI,IAAGE,EAAC,GAAG,OAAOJ,GAAED,EAAC;AAAA,EACnE;AACA,SAAOI;AACT;AACA,IAAM,mBAAmB,CAACH,IAAGG,IAAG;AAAA,EAC9B,UAAUG;AAAA,EACV,iBAAiBP;AACnB,IAAI;AAAA,EACF,UAAU;AACZ,MAAM;AACJ,MAAI;AAAA,IACF,QAAQG;AAAA,EACV,IAAIF;AACJ,MAAIE,OAAM,EAAG,QAAO,MAAMF;AAC1B,MAAII,KAAI,GACNN,KAAI;AACN,GAACQ,OAAM,gBAAgBA,OAAM,gBAAgBF,KAAI,IAAIN,KAAII,KAAI;AAC7D,QAAMM,KAAI,IAAI,MAAMN,EAAC,GACnBK,KAAI,IAAI,MAAML,EAAC,GACfc,KAAI,SAAS,cAAc,oCAAoC;AACjE,KAAG;AACD,UAAMN,KAAIV,GAAEF,EAAC;AACb,IAAAC,OAAMQ,GAAET,EAAC,IAAIC,GAAEW,EAAC,IAAIF,GAAEV,EAAC,IAAIkB,GAAE,UAAU;AACvC,UAAMC,KAAIP,GAAE,iBAAiBA,GAAE,YAAY;AAC3C,IAAAO,MAAKA,OAAMP,MAAKO,GAAE,aAAaT,GAAEV,EAAC,GAAGY,EAAC,GAAGP,GAAE,sBAAsBG,IAAGI,EAAC,GAAGZ,MAAKM;AAAA,EAC/E,SAAS,EAAEF,KAAI;AACf,SAAO,WAAY;AACjB,WAAO,IAAIM,IAAGR,IAAGO,EAAC;AAAA,EACpB;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAYR,KAAI,CAAC,GAAG;AAClB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,UAAU;AACf,WAAO,OAAO,MAAMA,EAAC;AAAA,EACvB;AAAA,EACM,UAAUA,IAAG;AAAA;AACjB,UAAI,KAAK,oBAAoB,GAAG,CAAC,KAAK,aAAaA,OAAM,KAAK,UAAW,QAAO,KAAK,cAAc,KAAK,MAAM,KAAK,SAAS,GAAG,KAAK,oBAAoB,IAAI,KAAK,YAAYA,IAAG,KAAK,SAAS,SAAM,KAAK,UAAU,IAAI,QAAQ,CAAAC,OAAK;AAClO,aAAK,UAAUA,IAAG,KAAK,UAAU,OAAO,WAAW,MAAM;AACvD,eAAK,YAAY,KAAK,QAAQ,KAAE,GAAG,KAAK,SAAS;AAAA,QACnD,GAAG,KAAK,WAAW;AAAA,MACrB,CAAC,GAAG,KAAK;AACT,UAAI,KAAK,QAAS,QAAO,KAAK;AAC9B,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACtC;AAAA;AAAA,EACA,MAAMD,IAAG;AACP,SAAK,aAAa,KAAK,cAAcA,OAAM,KAAK,mBAAmB,GAAG,KAAK,UAAU,MAAM,aAAa,KAAK,OAAO,GAAG,KAAK,UAAU,IAAI,KAAK,YAAY,KAAK,QAAQ,IAAE,GAAG,OAAO,KAAK,UAAU,OAAO,KAAK,SAAS,OAAO,KAAK;AAAA,EACtO;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,KAAK,mBAAmB,OAAO,aAAa,KAAK,eAAe,GAAG,KAAK,kBAAkB,OAAO,WAAW,MAAM;AAChI,WAAK,SAAS,OAAI,OAAO,KAAK;AAAA,IAChC,GAAG,KAAK,aAAa;AAAA,EACvB;AAAA,EACA,sBAAsB;AACpB,SAAK,mBAAmB,OAAO,aAAa,KAAK,eAAe,GAAG,OAAO,KAAK;AAAA,EACjF;AACF;AACA,IAAM,eAAe,IAAI,aAAa;AAAtC,IACE,OAAO,MAAM;AAAC;AADhB,IAEE,6BAA6B,CAACI,IAAGW,IAAGf,OAAM;AACxC,QAAMK,KAAI,IAAI,gBAAgB,GAC5BF,KAAI,oBAAI,IAAI,GACZM,KAAI,MAAM;AACR,IAAAJ,GAAE,MAAM,GAAGL,GAAE;AAAA,EACf;AACF,MAAIkB,IAAGX;AACP,QAAMR,KAAI,sBAAsB,MAAM;AAClC,IAAAmB,KAAI,sBAAsB,MAAM;AAC9B,MAAAX,KAAI,sBAAsB,MAAM;AAC9B,QAAAE,GAAE;AAAA,MACJ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GACDQ,KAAI,CAAAhB,OAAK;AACP,IAAAA,GAAE,WAAWG,OAAMD,GAAE,IAAIF,GAAE,cAAcE,GAAE,IAAIF,GAAE,YAAY,IAAI,CAAC,GAAGE,GAAE,IAAIF,GAAE,YAAY,KAAKE,GAAE,OAAOF,GAAE,YAAY,GAAGE,GAAE,SAAS,KAAKM,GAAE;AAAA,EAC5I,GACAE,KAAI,CAAAV,OAAK;AACP,IAAAA,GAAE,WAAWG,OAAMD,GAAE,IAAIF,GAAE,YAAY,KAAKE,GAAE,IAAIF,GAAE,cAAc,CAAC,GAAGE,GAAE,IAAIF,GAAE,cAAcE,GAAE,IAAIF,GAAE,YAAY,IAAI,CAAC,GAAG,qBAAqBF,EAAC,GAAG,qBAAqBmB,EAAC,GAAG,qBAAqBX,EAAC;AAAA,EAClM;AACF,EAAAH,GAAE,iBAAiB,iBAAiBO,IAAG;AAAA,IACrC,QAAQN,GAAE;AAAA,EACZ,CAAC,GAAGD,GAAE,iBAAiB,iBAAiBa,IAAG;AAAA,IACzC,QAAQZ,GAAE;AAAA,EACZ,CAAC,GAAGD,GAAE,iBAAiB,oBAAoBa,IAAG;AAAA,IAC5C,QAAQZ,GAAE;AAAA,EACZ,CAAC,GAAGU,GAAE;AACR;AACF,SAAS,YAAY;AACnB,SAAO,IAAI,QAAQ,CAAAX,OAAK,sBAAsB,MAAMA,GAAE,CAAC,CAAC;AAC1D;AACA,IAAM,kBAAN,MAAM,yBAAwB,gBAAgB;AAAA,EAC5C,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACM,WAAWJ,IAAGK,IAAG;AAAA;AAAA,IAAC;AAAA;AAAA,EACxB,IAAI,UAAU;AACZ,WAAO;AAAA,EACT;AAAA,EACA,IAAI,QAAQL,IAAG;AAAA,EAAC;AAAA,EAChB,IAAI,WAAW;AACb,WAAO;AAAA,EACT;AAAA,EACA,IAAI,SAASA,IAAG;AAAA,EAAC;AAAA,EACjB,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgBA,IAAG;AACrB,SAAK,mBAAmBA;AAAA,EAC1B;AAAA,EACM,YAAYA,IAAG;AAAA;AAAA,IAAC;AAAA;AAAA,EAChB,eAAeA,IAAG;AAAA;AACtB,aAAO;AAAA,IACT;AAAA;AAAA,EACM,YAAYA,IAAG;AAAA;AAAA,IAAC;AAAA;AAAA,EAChB,mBAAmB;AAAA;AAAA,IAAC;AAAA;AAAA,EACpB,oBAAoB;AAAA;AAAA,IAAC;AAAA;AAAA,EAC3B,iBAAiB;AAAA,EAAC;AAAA,EAClB,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAKA,IAAG;AAAA,EAAC;AAAA,EACb,IAAI,sBAAsB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,oBAAoBA,IAAG;AACzB,SAAK,uBAAuBA;AAAA,EAC9B;AAAA,EACA,mBAAmB;AAAA,EAAC;AAAA,EACpB,cAAc;AAAA,EAAC;AAAA,EACf,IAAI,QAAQ;AACV,WAAO;AAAA,EACT;AAAA,EACA,IAAI,MAAMA,IAAG;AAAA,EAAC;AAAA,EACd,mBAAmB;AAAA,EAAC;AAAA,EACpB,OAAO,SAAS;AACd,UAAMA,KAAI,IAAI,YAAY,sBAAsB;AAAA,MAC9C,SAAS;AAAA,MACT,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AACD,aAAS,cAAcA,EAAC;AAAA,EAC1B;AAAA,EACA,OAAa,KAAK,IAAG,IAAG,IAAG,IAAG;AAAA,+CAAZA,IAAGK,IAAGF,IAAGM,IAAG;AAC5B,YAAM,OAAO,mCAA0B;AACvC,YAAMS,KAAI,UAAU,WAAW,GAC7BX,KAAIJ,MAAKH,IACTD,KAAI,IAAI,KAAK;AACf,UAAIkB,KAAI;AACR,MAAAlB,GAAE,UAAU,MAAM;AAChB,QAAAA,GAAE,iBAAiB,aAAa,MAAM;AACpC,UAAAkB,OAAMN,GAAE,GAAGM,KAAI,OAAK,sBAAsB,MAAM;AAC9C,YAAAlB,GAAE,OAAO;AAAA,UACX,CAAC;AAAA,QACH,CAAC,GAAGA,GAAE,OAAO,OAAIA,GAAE,UAAU;AAAA,MAC/B;AACA,YAAMY,KAAI,iBAAiB,CAACJ,EAAC,GAAGR,IAAG;AAAA,QACjC,UAAU;AAAA,QACV,iBAAiB,CAAAG,OAAK;AACpB,gBAAMM,KAAIN,GAAE;AACZ,iBAAOA,GAAE,gBAAgB,MAAM,GAAG,MAAM;AACtC,YAAAA,GAAE,OAAOM;AAAA,UACX;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,CAACU,MAAKX,MAAKE,IAAG;AAChB,cAAMP,KAAIF,IACRQ,KAAIH,IACJK,KAAID;AACN,eAAO,iBAAgB,aAAaV,IAAG,iCAClCW,KADkC;AAAA,UAErC,SAASA,GAAE,WAAWH,GAAE,aAAa,SAAS;AAAA,UAC9C,SAASG,GAAE,kBAAkBR;AAAA,UAC7B,MAAMM,OAAM,UAAU,UAAUA,OAAM,UAAU,SAAS;AAAA,QAC3D,EAAC,GAAGN,GAAE,sBAAsB,YAAYH,EAAC,GAAG,MAAMA,GAAE,gBAAgBA,GAAE,OAAO,MAAIA,GAAE;AAAA,MACrF;AACA,YAAMa,KAAIP;AACV,aAAON,GAAE,OAAOQ,EAAC,GAAG,iBAAgB,aAAaR,IAAG,iCAC/Ca,KAD+C;AAAA,QAElD,SAASA,GAAE,WAAWL,GAAE,aAAa,SAAS;AAAA,MAChD,EAAC,GAAGR,GAAE,eAAe,KAAK,MAAM;AAC9B,QAAAA,GAAE,OAAO;AAAA,MACX,CAAC,GAAGA;AAAA,IACN;AAAA;AAAA,EACA,OAAO,aAAaC,IAAGK,IAAG;AACxB,QAAIF,IAAGM;AACP,IAAAT,GAAE,UAAU,CAAC,CAACK,GAAE,SAASL,GAAE,iBAAiBG,KAAIE,GAAE,kBAAkB,OAAOF,KAAI,QAAQH,GAAE,iBAAiBK,GAAE,WAAW,MAAML,GAAE,OAAOK,GAAE,QAAQ,SAASL,GAAE,UAAUS,KAAIJ,GAAE,WAAW,OAAOI,KAAI,GAAGT,GAAE,YAAYK,GAAE,WAAWL,GAAE,mBAAmB,CAAC,CAACK,GAAE;AAAA,EAC1P;AACF;AACA,IAAM,MAAM,CAAC,UAAU,eAAe,UAAU,SAAS,SAAS,UAAU,YAAY,YAAY;AAApG,IACE,MAAM;AACR,IAAM,wBAAwB,IAAI,KAAK,GAAG,GAAG,IAAI,IAAI;AACrD,IAAM,mBAAmB,CAAAL,OAAKA,GAAE,cAAc,qBAAqB;AAAnE,IACE,0BAA0B,CAAAA,OAAKA,GAAE,iBAAiB,EAAE,KAAK,CAAAC,OAAKA,GAAE,QAAQ,qBAAqB,CAAC;AAChG,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAYF,IAAGK,IAAG;AAChB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAIL,IAAG,KAAK,IAAIK;AAAA,EACvB;AAAA,EACA,yBAAyBL,IAAGK,IAAG;AAC7B,SAAK,IAAIL,IAAG,KAAK,IAAIK,IAAG,gBAAgB,OAAO;AAAA,EACjD;AAAA,EACA,wBAAwB;AACtB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK,KAAK;AAAA,MACV,OAAO,KAAK;AAAA,MACZ,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX,SAAS;AAAA,MAAC;AAAA,IACZ;AAAA,EACF;AACF;AACA,IAAM,0BAAN,cAAsC,MAAM;AAAA,EAC1C,cAAc;AACZ,UAAM,gBAAgB;AAAA,MACpB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EAClB;AACF;AACA,IAAM,wBAAN,cAAoC,MAAM;AAAA,EACxC,cAAc;AACZ,UAAM,gBAAgB;AAAA,MACpB,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EAClB;AACF;AACA,IAAM,oBAAN,cAAgC,MAAM;AAAA,EACpC,YAAYC,IAAGE,IAAG;AAAA,IAChB,SAASN;AAAA,IACT,aAAaC;AAAA,IACb,QAAQC;AAAA,EACV,GAAG;AACD,UAAME,IAAG;AAAA,MACP,SAASJ;AAAA,MACT,UAAUA;AAAA,IACZ,CAAC;AACD,SAAK,UAAUM;AACf,SAAK,SAAS;AAAA,MACZ,aAAaL;AAAA,MACb,QAAQC;AAAA,IACV;AAAA,EACF;AACF;AACA,SAAS,EAAEF,IAAG;AACZ,SAAO,OAAO,UAAU,eAAe,OAAO,aAAa,OAAOA,GAAE,KAAK,OAAO,UAAU,SAAS,IAAI;AACzG;AACA,SAAS,EAAEA,IAAG;AACZ,SAAO,OAAO,UAAU,eAAe,OAAO,aAAa,OAAOA,GAAE,KAAK,OAAO,UAAU,QAAQ,IAAI;AACxG;AACA,SAAS,QAAQ;AACf,SAAO,EAAE,MAAM;AACjB;AACA,SAAS,WAAW;AAClB,SAAO,EAAE,SAAS;AACpB;AACA,SAAS,SAAS;AAChB,SAAO,EAAE,OAAO,KAAK,MAAM,KAAK,UAAU,iBAAiB;AAC7D;AACA,SAAS,QAAQ;AACf,SAAO,SAAS,KAAK,OAAO;AAC9B;AACA,SAAS,YAAY;AACnB,SAAO,EAAE,SAAS;AACpB;AACA,SAAS,cAAcK,IAAG;AAAA,EACxB,MAAMW,WAAUX,GAAE;AAAA,IACV,mBAAmB;AAAA;AACvB,cAAMN,KAAI,KAAK;AACf,YAAI,MAAM,KAAK,eAAe,GAAG,KAAK,SAASA,GAAG;AAClD,cAAMI,KAAI,MAAM,KAAK,qBAAqBJ,EAAC;AAC3C,aAAK,SAASA,OAAM,MAAM,KAAK,iBAAiBA,IAAGI,EAAC;AAAA,MACtD;AAAA;AAAA,IACM,qBAAqBJ,IAAG;AAAA;AAC5B,YAAII,KAAI;AACR,cAAMc,KAAI,CAACnB,IAAGG,OAAM,MAAY;AAC5B,cAAIH,GAAE,OAAOC,IAAG,CAACA,IAAG;AAClB,kBAAMG,KAAI,MAAM;AACd,cAAAJ,GAAE,oBAAoB,SAASI,EAAC,GAAGM,GAAEV,IAAGG,EAAC;AAAA,YAC3C;AACA,YAAAH,GAAE,iBAAiB,SAASI,EAAC;AAAA,UAC/B;AACA,cAAID,KAAI,EAAG;AACX,gBAAMD,KAAID,KAAI,wBAAwB;AACtC,eAAK,cAAc,IAAIC,GAAE,CAAC,GAAGD,OAAMD,GAAE,QAAQ,qBAAqB,MAAMK,KAAIL,KAAIK,KAAIA,MAAK,iBAAiBL,EAAC,GAAGK,MAAKL,GAAE,iBAAiB,MAAM,EAAE,QAAQ,CAAAM,OAAK;AACzJ,YAAAD,OAAMA,KAAI,wBAAwBC,EAAC;AAAA,UACrC,CAAC,GAAG,EAAE,CAAC,KAAK,eAAe,KAAK,SAAS,SAAS,KAAK,SAAS,UAAU;AAAA,QAC5E,IACAI,KAAI,CAACV,IAAGG,OAAM,MAAM;AAClB,cAAI,KAAK,SAASF,GAAG;AACrB,gBAAMC,KAAID,KAAI,cAAc;AAC5B,cAAIE,KAAI,GAAG;AACT,YAAAH,GAAE,cAAc,IAAI,kBAAkBE,IAAG,MAAM;AAAA,cAC7C,aAAa,KAAK;AAAA,cAClB,SAAS;AAAA,YACX,CAAC,CAAC;AACF;AAAA,UACF;AACA,cAAI,CAAC,KAAK,eAAeD,OAAM,KAAK,KAAM;AAC1C,gBAAMG,KAAI,MAAY;AACpB,kBAAME,KAAI,KAAK,0BAA0B;AACzC,iBAAK,cAAc,IAAI,kBAAkBJ,IAAG,MAAM;AAAA,cAChD,aAAa,KAAK;AAAA,cAClB,SAASI;AAAA,YACX,CAAC,CAAC,GAAGN,GAAE,cAAc,IAAI,kBAAkBE,IAAG,MAAM;AAAA,cAClD,aAAa,KAAK;AAAA,cAClB,SAAS;AAAA,YACX,CAAC,CAAC,GAAG,KAAK,kBAAkB,CAACI,MAAK,KAAK,eAAe,cAAc,IAAI,kBAAkBJ,IAAG,MAAM;AAAA,cACjG,aAAa,KAAK;AAAA,cAClB,SAAS;AAAA,YACX,CAAC,CAAC,GAAG,KAAK,QAAQD,KAAI,WAAW,UAAU,KAAK,YAAY,GAAG,MAAM,UAAU,GAAG,MAAM,UAAU,GAAGA,OAAM,KAAK,QAAQA,OAAM,SAAM,KAAK,iBAAiB;AAAA,UAC5J;AACA,WAACA,MAAK,KAAK,SAAS,QAAQ,KAAK,SAAS,iBAAiB,SAAS,MAAM;AACxE,YAAAG,GAAE;AAAA,UACJ,GAAG;AAAA,YACD,MAAM;AAAA,UACR,CAAC,GAAG,KAAK,SAAS,MAAM,KAAKA,GAAE;AAAA,QACjC;AACF,eAAO,KAAK,SAAS,QAAQ,CAACJ,IAAGG,OAAM;AACrC,qCAA2BH,IAAGmB,GAAEnB,IAAGG,EAAC,GAAGO,GAAEV,IAAGG,EAAC,CAAC;AAAA,QAChD,CAAC,GAAGE;AAAA,MACN;AAAA;AAAA,IACM,iBAAiBJ,IAAGI,IAAG;AAAA;AAC3B,aAAK,WAAWJ,IAAGI,EAAC;AAAA,MACtB;AAAA;AAAA,EACF;AACA,SAAOa;AACT;AACA,IAAM,MAAM,IAAI,SAAS,iBAAiB;AAC1C,SAAS,EAAER,IAAG;AACZ,MAAID,KAAI;AACR,MAAI;AACF,IAAAA,KAAIC,GAAE,QAAQ,eAAe;AAAA,EAC/B,SAAST,IAAG;AAAA,EAAC;AACb,MAAIiB,KAAI;AACR,MAAI;AACF,IAAAA,KAAIR,GAAE,QAAQ,OAAO;AAAA,EACvB,SAAST,IAAG;AAAA,EAAC;AACb,SAAOQ,MAAKS;AACd;AACA,SAAS,eAAeR,IAAG;AAAA,EACzB,MAAMD,WAAUC,GAAE;AAAA,IACV,YAAYT,IAAG;AAAA;AACnB,YAAIA,OAAM,SAAMA,OAAM,KAAK,MAAM;AAC/B,uBAAa,MAAM,IAAI;AACvB;AAAA,QACF;AACA,aAAK,YAAY,MAAM,aAAa,UAAU,IAAI,OAAO,KAAK,OAAO,CAACA;AAAA,MACxE;AAAA;AAAA,IACM,kBAAkBA,IAAG;AAAA;AACzB,YAAIA,MAAK,KAAK,SAASA,GAAG;AAC1B,cAAMC,KAAI,IAEC,yCAFM;AAAA,UACf,UAAUG;AAAA,QACZ,IAAI,CAAC,GAAM;AACT,UAAAA,OAAM,WAAW,MAAM,KAAK,oBAAoB,qBAAqB;AAAA,QACvE;AACA,YAAI,CAAC,EAAE,KAAK,QAAQ,GAAG;AACrB,UAAAH,GAAE;AACF;AAAA,QACF;AACA,aAAK,SAAS,iBAAiB,UAAUA,IAAG;AAAA,UAC1C,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA;AAAA,IACM,kBAAkBD,IAAG;AAAA;AACzB,YAAIC,KAAI;AACR,YAAI;AACF,UAAAA,KAAI,KAAK,SAAS,QAAQ,eAAe;AAAA,QAC3C,SAASS,IAAG;AAAA,QAAC;AACb,YAAIN,KAAI;AACR,YAAI;AACF,UAAAA,KAAI,KAAK,SAAS,QAAQ,OAAO;AAAA,QACnC,SAASM,IAAG;AAAA,QAAC;AACb,QAAAV,MAAK,KAAK,SAASA,MAAK,CAACC,MAAK,CAACG,MAAK,KAAK,gBAAgB,KAAK,SAAS,YAAY,GAAG,MAAM,KAAK,eAAe;AAAA,MAClH;AAAA;AAAA,IACM,YAAYJ,IAAG;AAAA;AACnB,cAAM,UAAU,GAAG,QAAQ,MAAM,KAAK,kBAAkBA,EAAC,IAAI,MAAM,KAAK,kBAAkBA,EAAC,GAAG,MAAM,UAAU;AAAA,MAChH;AAAA;AAAA,IACM,eAAeA,IAAG;AAAA;AACtB,YAAI,KAAK,SAASA,GAAG,QAAO;AAC5B,YAAIC,KAAI;AACR,cAAMG,KAAI,CAACL,IAAGG,OAAM,MAAM;AACtB,cAAIH,GAAE,OAAOC,IAAGE,OAAM,GAAG;AACvB,kBAAMG,KAAIL,KAAI,wBAAwB;AACtC,iBAAK,cAAc,IAAIK,GAAE,CAAC;AAAA,UAC5B;AACA,cAAI,CAACL,OAAMD,GAAE,QAAQ,qBAAqB,MAAME,KAAIF,KAAIE,KAAIA,MAAK,iBAAiBF,EAAC,GAAGE,IAAI;AAC1F,UAAAF,GAAE,iBAAiB,MAAM,EAAE,QAAQ,CAAAM,OAAK;AACtC,YAAAJ,OAAMA,KAAI,wBAAwBI,EAAC;AAAA,UACrC,CAAC;AAAA,QACH,GACAK,KAAI,CAACX,IAAGG,OAAM,MAAY;AACxB,cAAI,KAAK,SAASF,GAAG;AACrB,gBAAMG,KAAIH,KAAI,cAAc;AAC5B,cAAIE,KAAI,GAAG;AACT,YAAAH,GAAE,cAAc,IAAI,kBAAkBI,IAAG,MAAM;AAAA,cAC7C,aAAa,KAAK;AAAA,cAClB,SAAS;AAAA,YACX,CAAC,CAAC;AACF;AAAA,UACF;AACA,gBAAME,KAAI,MAAY;AACpB,gBAAI,KAAK,SAASL,GAAG;AACrB,kBAAM,UAAU;AAChB,kBAAMW,KAAI,KAAK,0BAA0B;AACzC,iBAAK,cAAc,IAAI,kBAAkBR,IAAG,MAAM;AAAA,cAChD,aAAa,KAAK;AAAA,cAClB,SAASQ;AAAA,YACX,CAAC,CAAC,GAAGZ,GAAE,cAAc,IAAI,kBAAkBI,IAAG,MAAM;AAAA,cAClD,aAAa,KAAK;AAAA,cAClB,SAAS;AAAA,YACX,CAAC,CAAC,GAAG,KAAK,kBAAkB,CAACQ,MAAK,KAAK,eAAe,cAAc,IAAI,kBAAkBR,IAAG,MAAM;AAAA,cACjG,aAAa,KAAK;AAAA,cAClB,SAAS;AAAA,YACX,CAAC,CAAC,GAAG,KAAK,QAAQH,KAAI,WAAW,UAAU,KAAK,YAAY,GAAG,MAAM,UAAU,GAAG,MAAM,UAAU,GAAGA,OAAM,KAAK,QAAQA,OAAM,SAAM,KAAK,iBAAiB;AAAA,UAC5J;AACA,cAAI,KAAK,SAASA,GAAG;AACrB,gBAAMe,KAAI,EAAE,KAAK,QAAQ;AACzB,UAAAf,OAAM,QAAMe,MAAK,KAAK,eAAe,KAAK,SAAS,iBAAiB,gBAAgB,MAAM;AACxF,YAAAV,GAAE;AAAA,UACJ,GAAG;AAAA,YACD,MAAM;AAAA,UACR,CAAC,GAAG,KAAK,SAAS,YAAY,KAAKA,GAAE;AAAA,QACvC;AACF,eAAO,KAAK,SAAS,QAAQ,CAACN,IAAGG,OAAM;AACrC,qCAA2BH,IAAGK,GAAEL,IAAGG,EAAC,GAAGQ,GAAEX,IAAGG,EAAC,CAAC;AAAA,QAChD,CAAC,GAAGD;AAAA,MACN;AAAA;AAAA,EACF;AACA,SAAOO;AACT;AACA,SAAS,iBAAiBC,IAAG;AAAA,EAC3B,MAAMS,WAAUT,GAAE;AAAA,IACV,oBAAoB;AAAA;AACxB,cAAM,KAAK,eAAe;AAAA,MAC5B;AAAA;AAAA,IACM,YAAYT,IAAG;AAAA;AACnB,YAAIA,OAAM,SAAMA,OAAM,KAAK,MAAM;AAC/B,uBAAa,MAAM,IAAI;AACvB;AAAA,QACF;AACA,aAAK,YAAY,MAAM,aAAa,UAAU,IAAI,OAAO,KAAK,OAAO,CAACA;AAAA,MACxE;AAAA;AAAA,IACM,YAAYA,IAAG;AAAA;AAAA,MAAC;AAAA;AAAA,IAChB,eAAeA,IAAG;AAAA;AACtB,YAAI,KAAK,SAASA,GAAG,QAAO;AAC5B,YAAIC,KAAI;AACR,cAAMK,KAAI,CAACP,IAAGM,OAAM,MAAM;AACtB,cAAIL,OAAM,KAAK,KAAM;AACrB,cAAID,GAAE,OAAOC,IAAGK,OAAM,GAAG;AACvB,kBAAMD,KAAIJ,KAAI,wBAAwB;AACtC,iBAAK,cAAc,IAAII,GAAE,CAAC;AAAA,UAC5B;AACA,cAAIJ,OAAM,SAAOD,GAAE,QAAQ,qBAAqB,MAAME,KAAIF,KAAIE,KAAIA,MAAK,iBAAiBF,EAAC,GAAGE,IAAI;AAChG,UAAAF,GAAE,iBAAiB,MAAM,EAAE,QAAQ,CAAAK,OAAK;AACtC,YAAAH,OAAMA,KAAI,wBAAwBG,EAAC;AAAA,UACrC,CAAC;AAAA,QACH,GACAM,KAAI,CAACX,IAAGM,OAAM,MAAY;AACxB,cAAI,KAAK,SAASL,GAAG;AACrB,gBAAMG,KAAIH,KAAI,cAAc;AAC5B,cAAID,GAAE,cAAc,IAAI,kBAAkBI,IAAG,MAAM;AAAA,YACjD,aAAa,KAAK;AAAA,UACpB,CAAC,CAAC,GAAGE,KAAI,EAAG;AACZ,gBAAMD,KAAI,KAAK,0BAA0B;AACzC,eAAK,cAAc,IAAI,kBAAkBD,IAAG,MAAM;AAAA,YAChD,aAAa,KAAK;AAAA,YAClB,SAASC;AAAA,UACX,CAAC,CAAC,GAAG,KAAK,kBAAkB,CAACA,MAAK,KAAK,eAAe,cAAc,IAAI,kBAAkBD,IAAG,MAAM;AAAA,YACjG,aAAa,KAAK;AAAA,YAClB,SAAS;AAAA,UACX,CAAC,CAAC,GAAG,KAAK,QAAQH,KAAI,WAAW,UAAU,KAAK,YAAY,GAAG,MAAM,UAAU,GAAG,MAAM,UAAU,GAAGA,OAAM,KAAK,QAAQA,OAAM,SAAM,KAAK,iBAAiB;AAAA,QAC5J;AACF,eAAO,KAAK,SAAS,QAAQ,CAACD,IAAGM,OAAM;AACrC,qCAA2BN,IAAGO,GAAEP,IAAGM,EAAC,GAAGK,GAAEX,IAAGM,EAAC,CAAC;AAAA,QAChD,CAAC,GAAGJ;AAAA,MACN;AAAA;AAAA,EACF;AACA,SAAOiB;AACT;AACA,IAAMZ,KAAI,iBAAiB,SAAS,cAAc,KAAK;AACvD,IAAM,MAAN,MAAU;AAAA,EACR,cAAc;AACZ,SAAK,OAAO,SAAS;AACrB,SAAK,QAAQ,CAAC;AACd,SAAK,oBAAoB,CAAAP,OAAK;AAC5B,WAAK,kBAAkBA,GAAE,aAAa,GAAG,KAAK,cAAc,KAAK,MAAM,GAAG,EAAE;AAAA,IAC9E;AACA,SAAK,kBAAkB,MAAM;AAC3B,UAAIM;AACJ,UAAI,CAAC,KAAK,MAAM,UAAU,GAAGA,KAAI,KAAK,oBAAoB,QAAQA,GAAE,QAAS;AAC7E,YAAMN,KAAI,KAAK;AACf,WAAK,kBAAkB;AACvB,YAAMC,KAAI,KAAK,MAAM,SAAS,GAC5BE,KAAI,KAAK,MAAM,OAAO,CAACC,IAAGC,OAAM,CAACL,GAAE,KAAK,CAAAE,OAAKA,OAAME,MAAKF,QAAOE,MAAK,OAAO,SAASA,GAAE,oBAAoBA,MAAK,OAAO,SAASA,GAAE,UAAU,UAAUC,OAAMJ,MAAKG,OAAM,KAAK,eAAeA,GAAE,uBAAuB,WAAW,KAAK,CAACA,GAAE,mBAAmB,KAAKA,GAAE,SAAS,QAAQ;AACnR,MAAAD,GAAE,QAAQ,GAAGA,GAAE,QAAQ,CAAAC,OAAK;AAC1B,aAAK,aAAaA,EAAC;AACnB,YAAIC,KAAID,GAAE;AACV,eAAOC,KAAI,MAAK,aAAaA,EAAC,GAAGA,KAAIA,GAAE;AAAA,MACzC,CAAC;AAAA,IACH;AACA,SAAK,qBAAqB,CAAAL,OAAK;AAC7B,YAAM;AAAA,QACJ,QAAQC;AAAA,QACR,UAAUE;AAAA,MACZ,IAAIH;AACJ,MAAAG,OAAM,UAAU,KAAK,aAAaF,EAAC;AAAA,IACrC;AACA,SAAK,gBAAgB,CAAAD,OAAK;AACxB,UAAIA,GAAE,SAAS,YAAY,CAAC,KAAK,MAAM,OAAQ;AAC/C,YAAMC,KAAI,KAAK,MAAM,GAAG,EAAE;AAC1B,WAAKA,MAAK,OAAO,SAASA,GAAE,UAAU,QAAQ;AAC5C,QAAAD,GAAE,eAAe;AACjB;AAAA,MACF;AACA,MAAAO,OAAMN,MAAK,OAAO,SAASA,GAAE,UAAU,YAAYA,MAAK,KAAK,aAAaA,EAAC;AAAA,IAC7E;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,KAAK,iBAAiB;AAAA,EACpC;AAAA,EACA,aAAa;AACX,SAAK,SAAS,iBAAiB,eAAe,KAAK,iBAAiB,GAAG,KAAK,SAAS,iBAAiB,aAAa,KAAK,eAAe,GAAG,KAAK,SAAS,iBAAiB,WAAW,KAAK,aAAa;AAAA,EACxM;AAAA,EACA,aAAaD,IAAG;AACd,UAAMC,KAAI,KAAK,MAAM,QAAQD,EAAC;AAC9B,IAAAC,KAAI,MAAM,KAAK,MAAM,OAAOA,IAAG,CAAC,GAAGD,GAAE,OAAO;AAAA,EAC9C;AAAA,EACA,yBAAyBA,IAAG;AAC1B,WAAO,KAAK,MAAM,OAAO,CAAAC,OAAKA,GAAE,mBAAmBD,EAAC;AAAA,EACtD;AAAA,EACA,IAAIA,IAAG;AACL,QAAI,KAAK,MAAM,SAASA,EAAC,GAAG;AAC1B,YAAMC,KAAI,KAAK,MAAM,QAAQD,EAAC;AAC9B,MAAAC,KAAI,OAAO,KAAK,MAAM,OAAOA,IAAG,CAAC,GAAG,KAAK,MAAM,KAAKD,EAAC;AACrD;AAAA,IACF;AACA,QAAIA,GAAE,SAAS,UAAUA,GAAE,SAAS,WAAWA,GAAE,SAAS,QAAQ;AAChE,YAAMC,KAAI,yBACRE,KAAI,IAAI,MAAMF,IAAG;AAAA,QACf,UAAU;AAAA,QACV,SAAS;AAAA,MACX,CAAC;AACH,MAAAD,GAAE,iBAAiBC,IAAG,CAAAK,OAAK;AACzB,cAAMF,KAAIE,GAAE,aAAa;AACzB,aAAK,MAAM,QAAQ,CAAAD,OAAK;AACtB,WAACD,GAAE,KAAK,CAAAF,OAAKA,OAAMG,EAAC,KAAKA,GAAE,SAAS,YAAY,KAAK,aAAaA,EAAC;AAAA,QACrE,CAAC;AAAA,MACH,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC,GAAGL,GAAE,cAAcG,EAAC;AAAA,IACvB,WAAWH,GAAE,SAAS,QAAQ;AAC5B,UAAI,KAAK,MAAM,KAAK,CAAAG,OAAKA,GAAE,SAAS,YAAYA,GAAE,kBAAkBA,GAAE,mBAAmBH,GAAE,cAAc,GAAG;AAC1G,QAAAA,GAAE,OAAO;AACT;AAAA,MACF;AACA,WAAK,MAAM,QAAQ,CAAAG,OAAK;AACtB,QAAAA,GAAE,SAAS,UAAU,KAAK,aAAaA,EAAC;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,0BAAsB,MAAM;AAC1B,WAAK,MAAM,KAAKH,EAAC,GAAGA,GAAE,iBAAiB,gBAAgB,KAAK,oBAAoB;AAAA,QAC9E,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAOA,IAAG;AACR,SAAK,aAAaA,EAAC;AAAA,EACrB;AACF;AACA,IAAM,eAAe,IAAI,IAAI;AAM7B,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,CAAAgB,QAAM;AAAA,EACzB,GAAGA;AAAA,EACH,GAAGA;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,KACH;AAEP;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ,GAAAF;AAAA,IACA,GAAAD;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAKA;AAAA,IACL,MAAMC;AAAA,IACN,OAAOA,KAAI;AAAA,IACX,QAAQD,KAAI;AAAA,IACZ,GAAAC;AAAA,IACA,GAAAD;AAAA,EACF;AACF;AACA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,oBAAoB,CAAO,WAAW,UAAU,WAAW;AAC/D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAO;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,MAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF,GAAAN;AAAA,IACA,GAAAD;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAASR,KAAI,GAAGA,KAAI,gBAAgB,QAAQA,MAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgBA,EAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX,GAAAS;AAAA,MACA,GAAAD;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAO;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,IAAAN,KAAI,SAAS,OAAO,QAAQA;AAC5B,IAAAD,KAAI,SAAS,OAAO,QAAQA;AAC5B,qBAAiB,iCACZ,iBADY;AAAA,MAEf,CAAC,IAAI,GAAG,kCACH,eAAe,IAAI,IACnB;AAAA,IAEP;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMO,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC,GAAAN;AAAA,UACA,GAAAD;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,MAAAR,KAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL,GAAAS;AAAA,IACA,GAAAD;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,SAAe,eAAe,OAAO,SAAS;AAAA;AAC5C,QAAI;AACJ,QAAI,YAAY,QAAQ;AACtB,gBAAU,CAAC;AAAA,IACb;AACA,UAAM;AAAA,MACJ,GAAAC;AAAA,MACA,GAAAD;AAAA,MACA,UAAAO;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ,WAAW;AAAA,MACX,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,UAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,UAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,MACzE,WAAW,wBAAwB,MAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,MAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,mBAAmB,MAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,MAChS;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AACF,UAAM,OAAO,mBAAmB,aAAa;AAAA,MAC3C,GAAAN;AAAA,MACA,GAAAD;AAAA,MACA,OAAO,MAAM,SAAS;AAAA,MACtB,QAAQ,MAAM,SAAS;AAAA,IACzB,IAAI,MAAM;AACV,UAAM,eAAe,MAAOO,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,UAAM,eAAe,MAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,MAAO,MAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,MACvL,GAAG;AAAA,MACH,GAAG;AAAA,IACL,IAAI;AAAA,MACF,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,UAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,MAC/K;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,IAAI,IAAI;AACT,WAAO;AAAA,MACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,MACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,MACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,MAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,IAClG;AAAA,EACF;AAAA;AAOA,IAAM,UAAU,cAAY;AAAA,EAC1B,MAAM;AAAA,EACN;AAAA,EACM,GAAG,OAAO;AAAA;AACd,YAAM;AAAA,QACJ,GAAAN;AAAA,QACA,GAAAD;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAO;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AAEJ,YAAM;AAAA,QACJ;AAAA,QACA,UAAU;AAAA,MACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,UAAI,WAAW,MAAM;AACnB,eAAO,CAAC;AAAA,MACV;AACA,YAAM,gBAAgB,iBAAiB,OAAO;AAC9C,YAAM,SAAS;AAAA,QACb,GAAAN;AAAA,QACA,GAAAD;AAAA,MACF;AACA,YAAM,OAAO,iBAAiB,SAAS;AACvC,YAAM,SAAS,cAAc,IAAI;AACjC,YAAM,kBAAkB,MAAMO,UAAS,cAAc,OAAO;AAC5D,YAAM,UAAU,SAAS;AACzB,YAAM,UAAU,UAAU,QAAQ;AAClC,YAAM,UAAU,UAAU,WAAW;AACrC,YAAM,aAAa,UAAU,iBAAiB;AAC9C,YAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,YAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,YAAM,oBAAoB,MAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,UAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,UAAI,CAAC,cAAc,EAAE,MAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,qBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,MACrE;AACA,YAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,YAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,YAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,YAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,YAAM,QAAQ;AACd,YAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,YAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,YAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,YAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,YAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,aAAO;AAAA,QACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,QACvB,MAAM;AAAA,UACJ,CAAC,IAAI,GAAGC;AAAA,UACR,cAAc,SAASA,UAAS;AAAA,WAC5B,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,QAEF,OAAO;AAAA,MACT;AAAA,IACF;AAAA;AACF;AAQA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACM,GAAG,OAAO;AAAA;AACd,YAAI,uBAAuB;AAC3B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,UAAAF;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAQIG,MAAA,SAAS,SAAS,KAAK,GAPzB;AAAA,oBAAU,gBAAgB;AAAA,UAC1B,WAAW,iBAAiB;AAAA,UAC5B,oBAAoB;AAAA,UACpB,mBAAmB;AAAA,UACnB,4BAA4B;AAAA,UAC5B,gBAAgB;AAAA,QAjgExB,IAmgEUA,KADC,kCACDA,KADC;AAAA,UANH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAQF,aAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,iBAAO,CAAC;AAAA,QACV;AACA,cAAM,OAAO,QAAQ,SAAS;AAC9B,cAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,cAAM,MAAM,MAAOH,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,cAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAI,CAAC,+BAA+B,8BAA8B,QAAQ;AACxE,6BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,QACvH;AACA,cAAM,aAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,cAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,cAAM,YAAY,CAAC;AACnB,YAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,YAAI,eAAe;AACjB,oBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,QAC/B;AACA,YAAI,gBAAgB;AAClB,gBAAM,QAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,oBAAU,KAAK,SAAS,MAAM,CAAC,CAAC,GAAG,SAAS,MAAM,CAAC,CAAC,CAAC;AAAA,QACvD;AACA,wBAAgB,CAAC,GAAG,eAAe;AAAA,UACjC;AAAA,UACA;AAAA,QACF,CAAC;AAGD,YAAI,CAAC,UAAU,MAAM,CAAAI,UAAQA,SAAQ,CAAC,GAAG;AACvC,cAAI,uBAAuB;AAC3B,gBAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,gBAAM,gBAAgB,WAAW,SAAS;AAC1C,cAAI,eAAe;AAEjB,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAIA,cAAI,kBAAkB,wBAAwB,cAAc,OAAO,CAAAZ,OAAKA,GAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAACF,IAAGe,OAAMf,GAAE,UAAU,CAAC,IAAIe,GAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,cAAI,CAAC,gBAAgB;AACnB,oBAAQ,kBAAkB;AAAA,cACxB,KAAK,WACH;AACE,oBAAI;AACJ,sBAAMC,cAAa,wBAAwB,cAAc,IAAI,CAAAd,OAAK,CAACA,GAAE,WAAWA,GAAE,UAAU,OAAO,CAAAe,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAACjB,IAAGe,OAAMf,GAAE,CAAC,IAAIe,GAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB,CAAC;AACtP,oBAAIC,YAAW;AACb,mCAAiBA;AAAA,gBACnB;AACA;AAAA,cACF;AAAA,cACF,KAAK;AACH,iCAAiB;AACjB;AAAA,YACJ;AAAA,UACF;AACA,cAAI,cAAc,gBAAgB;AAChC,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,CAAC;AAAA,MACV;AAAA;AAAA,EACF;AACF;AAKA,SAAe,qBAAqB,OAAO,SAAS;AAAA;AAClD,UAAM;AAAA,MACJ;AAAA,MACA,UAAAN;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,MAAM,MAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,UAAM,OAAO,QAAQ,SAAS;AAC9B,UAAM,YAAY,aAAa,SAAS;AACxC,UAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,UAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,UAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,UAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,OAAO,aAAa,WAAW;AAAA,MACjC,UAAU;AAAA,MACV,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,IAAI;AAAA,MACF,UAAU;AAAA,MACV,WAAW;AAAA,MACX,eAAe;AAAA,OACZ;AAEL,QAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,kBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,IACzD;AACA,WAAO,aAAa;AAAA,MAClB,GAAG,YAAY;AAAA,MACf,GAAG,WAAW;AAAA,IAChB,IAAI;AAAA,MACF,GAAG,WAAW;AAAA,MACd,GAAG,YAAY;AAAA,IACjB;AAAA,EACF;AAAA;AASA,IAAM,WAAW,SAAU,SAAS;AAClC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACM,GAAG,OAAO;AAAA;AACd,YAAI,uBAAuB;AAC3B,cAAM;AAAA,UACJ,GAAAN;AAAA,UACA,GAAAD;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,YAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,iBAAO,CAAC;AAAA,QACV;AACA,eAAO;AAAA,UACL,GAAGC,KAAI,WAAW;AAAA,UAClB,GAAGD,KAAI,WAAW;AAAA,UAClB,MAAM,iCACD,aADC;AAAA,YAEJ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA,EACF;AACF;AAOA,IAAM,UAAU,SAAU,SAAS;AACjC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACM,GAAG,OAAO;AAAA;AACd,cAAM;AAAA,UACJ,GAAAC;AAAA,UACA,GAAAD;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAgBIU,MAAA,SAAS,SAAS,KAAK,GAfzB;AAAA,oBAAU,gBAAgB;AAAA,UAC1B,WAAW,iBAAiB;AAAA,UAC5B,UAAU;AAAA,YACR,IAAI,UAAQ;AACV,kBAAI;AAAA,gBACF,GAAAT;AAAA,gBACA,GAAAD;AAAA,cACF,IAAI;AACJ,qBAAO;AAAA,gBACL,GAAAC;AAAA,gBACA,GAAAD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QA5sER,IA8sEUU,KADC,kCACDA,KADC;AAAA,UAdH;AAAA,UACA;AAAA,UACA;AAAA;AAcF,cAAM,SAAS;AAAA,UACb,GAAAT;AAAA,UACA,GAAAD;AAAA,QACF;AACA,cAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,cAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,cAAM,WAAW,gBAAgB,SAAS;AAC1C,YAAI,gBAAgB,OAAO,QAAQ;AACnC,YAAI,iBAAiB,OAAO,SAAS;AACrC,YAAI,eAAe;AACjB,gBAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,gBAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,gBAAMe,OAAM,gBAAgB,SAAS,OAAO;AAC5C,gBAAMP,OAAM,gBAAgB,SAAS,OAAO;AAC5C,0BAAgB,MAAMO,MAAK,eAAeP,IAAG;AAAA,QAC/C;AACA,YAAI,gBAAgB;AAClB,gBAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,gBAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,gBAAMO,OAAM,iBAAiB,SAAS,OAAO;AAC7C,gBAAMP,OAAM,iBAAiB,SAAS,OAAO;AAC7C,2BAAiB,MAAMO,MAAK,gBAAgBP,IAAG;AAAA,QACjD;AACA,cAAM,gBAAgB,QAAQ,GAAG,iCAC5B,QAD4B;AAAA,UAE/B,CAAC,QAAQ,GAAG;AAAA,UACZ,CAAC,SAAS,GAAG;AAAA,QACf,EAAC;AACD,eAAO,iCACF,gBADE;AAAA,UAEL,MAAM;AAAA,YACJ,GAAG,cAAc,IAAIP;AAAA,YACrB,GAAG,cAAc,IAAID;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,EACF;AACF;AAQA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACM,GAAG,OAAO;AAAA;AACd,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,UAAAO;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAGIG,MAAA,SAAS,SAAS,KAAK,GAFzB;AAAA,kBAAQ,MAAM;AAAA,UAAC;AAAA,QA3wEvB,IA6wEUA,KADC,kCACDA,KADC;AAAA,UADH;AAAA;AAGF,cAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,cAAM,OAAO,QAAQ,SAAS;AAC9B,cAAM,YAAY,aAAa,SAAS;AACxC,cAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM;AACV,YAAI;AACJ,YAAI;AACJ,YAAI,SAAS,SAAS,SAAS,UAAU;AACvC,uBAAa;AACb,sBAAY,gBAAgB,MAAOH,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,KAAM,UAAU,SAAS,SAAS;AAAA,QACzI,OAAO;AACL,sBAAY;AACZ,uBAAa,cAAc,QAAQ,QAAQ;AAAA,QAC7C;AACA,cAAM,0BAA0B,SAAS,SAAS,UAAU;AAC5D,cAAM,yBAAyB,QAAQ,SAAS,SAAS;AACzD,cAAM,UAAU,CAAC,MAAM,eAAe;AACtC,YAAI,kBAAkB;AACtB,YAAI,iBAAiB;AACrB,YAAI,SAAS;AACX,gBAAM,uBAAuB,QAAQ,SAAS,OAAO,SAAS;AAC9D,2BAAiB,aAAa,UAAU,IAAI,wBAAwB,oBAAoB,IAAI;AAAA,QAC9F,OAAO;AACL,gBAAM,wBAAwB,SAAS,SAAS,MAAM,SAAS;AAC/D,4BAAkB,aAAa,UAAU,IAAI,yBAAyB,qBAAqB,IAAI;AAAA,QACjG;AACA,YAAI,WAAW,CAAC,WAAW;AACzB,gBAAM,OAAO,IAAI,SAAS,MAAM,CAAC;AACjC,gBAAM,OAAO,IAAI,SAAS,OAAO,CAAC;AAClC,gBAAM,OAAO,IAAI,SAAS,KAAK,CAAC;AAChC,gBAAM,OAAO,IAAI,SAAS,QAAQ,CAAC;AACnC,cAAI,SAAS;AACX,6BAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,UAC1G,OAAO;AACL,8BAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,KAAK,SAAS,MAAM;AAAA,UAC5G;AAAA,QACF;AACA,cAAM,MAAM,iCACP,QADO;AAAA,UAEV;AAAA,UACA;AAAA,QACF,EAAC;AACD,cAAM,iBAAiB,MAAMA,UAAS,cAAc,SAAS,QAAQ;AACrE,YAAI,UAAU,eAAe,SAAS,WAAW,eAAe,QAAQ;AACtE,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,eAAO,CAAC;AAAA,MACV;AAAA;AAAA,EACF;AACF;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAE3B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,iBAAiB,OAAO;AAGpC,SAAO,IAAI,cAAc,UAAU,IAAI,gBAAgB,WAAW,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACnc;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,IAAI,gBAAgB,kBAAkB,qBAAqB,IAAI,YAAY,IAAI,CAAC,CAAC;AAAA,EACtM;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,iBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AACA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,GAAAS;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAIf,MAAKe,KAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAIhB,MAAKgB,KAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAACf,MAAK,CAAC,OAAO,SAASA,EAAC,GAAG;AAC7B,IAAAA,KAAI;AAAA,EACN;AACA,MAAI,CAACD,MAAK,CAAC,OAAO,SAASA,EAAC,GAAG;AAC7B,IAAAA,KAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL,GAAAC;AAAA,IACA,GAAAD;AAAA,EACF;AACF;AACA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAIC,MAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAID,MAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,WAAW;AAC/B,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAM,iBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,MAAAC,MAAK,YAAY;AACjB,MAAAD,MAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,MAAAC,MAAK;AACL,MAAAD,MAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,WAAW;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA,GAAAC;AAAA,IACA,GAAAD;AAAA,EACF,CAAC;AACH;AACA,IAAM,oBAAoB,CAAC,iBAAiB,QAAQ;AACpD,SAAS,WAAW,SAAS;AAC3B,SAAO,kBAAkB,KAAK,cAAY;AACxC,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAASZ,IAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ;AAAA,IAC5D,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ;AAAA,EAC7D;AACF;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AACA,SAAS,oBAAoB,SAAS;AAGpC,SAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO,cAAc,OAAO,EAAE;AAC1F;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAIa,KAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAMD,KAAI,CAAC,OAAO;AAClB,MAAI,iBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,IAAAC,MAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAA;AAAA,IACA,GAAAD;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAIC,KAAI;AACR,MAAID,KAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,MAAAC,KAAI,eAAe;AACnB,MAAAD,KAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAC;AAAA,IACA,GAAAD;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAMC,KAAI,OAAO,MAAM;AACvB,QAAMD,KAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAAC;AAAA,IACA,GAAAD;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO,iCACF,mBADE;AAAA,MAEL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,IACxC;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiB,iBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgB,iBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AACA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAC1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AACA,QAAMC,KAAI,KAAK,OAAO,OAAO,aAAa,QAAQ;AAClD,QAAMD,KAAI,KAAK,MAAM,OAAO,YAAY,QAAQ;AAChD,SAAO;AAAA,IACL,GAAAC;AAAA,IACA,GAAAD;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AACA,SAAS,mBAAmB,SAAS;AACnC,SAAO,iBAAiB,OAAO,EAAE,aAAa;AAChD;AACA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAK,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,SAAO,QAAQ;AACjB;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AACA,IAAM,kBAAkB,SAAgB,MAAM;AAAA;AAC5C,UAAM,oBAAoB,KAAK,mBAAmB;AAClD,UAAM,kBAAkB,KAAK;AAC7B,UAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,WAAO;AAAA,MACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,MAC9G,UAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAO,mBAAmB;AAAA,QAC1B,QAAQ,mBAAmB;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AACA,SAAS,MAAM,SAAS;AACtB,SAAO,iBAAiB,OAAO,EAAE,cAAc;AACjD;AACA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,QAAQ,sBAAsB;AAClC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe,iCACxC,UADwC;AAAA;AAAA,QAG3C,MAAM,KAAK;AAAA,MACb,EAAC;AAAA,IACH,SAASZ,IAAG;AACV,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,gBAAgB,YAAY,MAAM,YAAY,KAAK,YAAY,MAAM,YAAY,KAAK,YAAY,UAAU,YAAY,SAAS,YAAY,WAAW,YAAY,SAAS;AAC/K,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AASA,IAAM,SAAS;AAOf,IAAM,QAAQ;AAQd,IAAM,OAAO;AAQb,IAAM,OAAO;AAOb,IAAM,QAAQ;AAMd,IAAM,kBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,KACG;AAEL,QAAM,oBAAoB,iCACrB,cAAc,WADO;AAAA,IAExB,IAAI;AAAA,EACN;AACA,SAAO,kBAAkB,WAAW,UAAU,iCACzC,gBADyC;AAAA,IAE5C,UAAU;AAAA,EACZ,EAAC;AACH;AACA,SAAS,EAAEC,IAAG;AACZ,MAAI,OAAOA,MAAK,YAAa,QAAO;AACpC,QAAMF,KAAI,OAAO,oBAAoB;AACrC,SAAO,KAAK,MAAME,KAAIF,EAAC,IAAIA;AAC7B;AACA,IAAM,MAAM;AAAZ,IACE,IAAI;AADN,IAEE,IAAI,CAAAE,OAAK;AACP,MAAID;AACJ,UAAQA,KAAI;AAAA,IACV,MAAM,CAAC,SAAS,UAAU,KAAK;AAAA,IAC/B,cAAc,CAAC,eAAe,UAAU,KAAK;AAAA,IAC7C,YAAY,CAAC,aAAa,UAAU,KAAK;AAAA,IACzC,OAAO,CAAC,QAAQ,UAAU,KAAK;AAAA,IAC/B,eAAe,CAAC,cAAc,UAAU,KAAK;AAAA,IAC7C,aAAa,CAAC,YAAY,UAAU,KAAK;AAAA,IACzC,KAAK,CAAC,UAAU,QAAQ,OAAO;AAAA,IAC/B,aAAa,CAAC,gBAAgB,QAAQ,OAAO;AAAA,IAC7C,WAAW,CAAC,cAAc,QAAQ,OAAO;AAAA,IACzC,QAAQ,CAAC,OAAO,QAAQ,OAAO;AAAA,IAC/B,gBAAgB,CAAC,aAAa,QAAQ,OAAO;AAAA,IAC7C,cAAc,CAAC,WAAW,QAAQ,OAAO;AAAA,EAC3C,EAAEC,EAAC,MAAM,OAAOD,KAAI,CAACC,EAAC;AACxB;AACF,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAYF,IAAG;AACb,SAAK,qBAAqB,oBAAI,QAAQ;AACtC,SAAK,uBAAuB;AAC5B,SAAK,yBAAyB,MAAM;AAClC,OAAC,KAAK,wBAAwB,KAAK,QAAQ,SAAS,WAAW,KAAK,WAAW,KAAK,OAAO,cAAc,IAAI,MAAM,SAAS;AAAA,QAC1H,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,KAAK,uBAAuB;AAAA,IACnC;AACA,SAAK,kBAAkB,MAAM;AAC3B,WAAK,iBAAiB;AAAA,IACxB;AACA,SAAK,uBAAuB,MAAM;AAChC,OAAC,KAAK,UAAU,CAAC,KAAK,YAAY,KAAK,qBAAqB,GAAG,KAAK,iBAAiB;AAAA,IACvF;AACA,SAAK,OAAOA,IAAG,KAAK,KAAK,cAAc,IAAI;AAAA,EAC7C;AAAA,EACM,eAAgD;AAAA,+CAAnCA,KAAI,KAAK,QAAQC,KAAI,KAAK,SAAS;AACpD,UAAI,KAAK,SAASD,IAAG,KAAK,UAAUC,IAAG,CAACD,MAAK,CAACC,GAAG;AACjD,YAAMkB,KAAI,WAAWlB,GAAE,SAASD,IAAG,KAAK,wBAAwB;AAAA,QAC5D,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,aAAa;AAAA,MACf,CAAC,GACDO,KAAI,WAAWN,GAAE,SAASD,IAAG,KAAK,iBAAiB;AAAA,QACjD,gBAAgB;AAAA,MAClB,CAAC;AACH,WAAK,UAAU,MAAM;AACnB,YAAII;AACJ,SAACA,KAAI,KAAK,KAAK,aAAa,QAAQA,GAAE,QAAQ,CAAAM,OAAK;AACjD,UAAAA,GAAE,iBAAiB,aAAa,MAAM;AACpC,kBAAMJ,KAAI,KAAK,mBAAmB,IAAII,EAAC;AACvC,YAAAJ,MAAKI,GAAE,aAAa,aAAaJ,EAAC,GAAG,KAAK,mBAAmB,OAAOI,EAAC;AAAA,UACvE,GAAG;AAAA,YACD,MAAM;AAAA,UACR,CAAC;AAAA,QACH,CAAC,GAAGS,GAAE,GAAGZ,GAAE;AAAA,MACb;AAAA,IACF;AAAA;AAAA,EACM,mBAAmB;AAAA;AACvB,UAAIuB,IAAGnB;AACP,YAAM;AAAA,QACJ,SAASX;AAAA,QACT,QAAQC;AAAA,MACV,IAAI;AACJ,YAAO,SAAS,QAAQ,SAAS,MAAM,QAAQ,QAAQ,QAAQ;AAC/D,YAAMkB,KAAInB,GAAE,mBAAmB,cAAc,KAAK,IAAI,KAAK;AAAA,QACvD,SAAS;AAAA,QACT,oBAAoB,EAAEA,GAAE,SAAS;AAAA,MACnC,CAAC,GACD,CAACO,KAAI,GAAGH,KAAI,CAAC,IAAI,MAAM,QAAQJ,MAAK,OAAO,SAASA,GAAE,MAAM,IAAIA,GAAE,SAAS,CAACA,GAAE,QAAQ,CAAC,GACvFU,MAAKoB,KAAI,KAAK,KAAK,SAAS,KAAK,CAAAzB,OAAKA,GAAE,UAAU,MAAM,OAAO,SAASyB,GAAE,YAC1ExB,KAAI,CAAC,OAAO;AAAA,QACV,UAAUC;AAAA,QACV,WAAWH;AAAA,MACb,CAAC,GAAG,MAAM;AAAA,QACR,SAAS;AAAA,MACX,CAAC,GAAGe,IAAG,KAAK;AAAA,QACV,SAAS;AAAA,QACT,OAAO,CAAC;AAAA,UACN,gBAAgBd;AAAA,UAChB,iBAAiBO;AAAA,UACjB,OAAO;AAAA,YACL,UAAUE;AAAA,UACZ;AAAA,QACF,MAAM;AACJ,gBAAMW,KAAI,KAAK,IAAI,GAAG,KAAK,MAAMb,EAAC,CAAC,GACjCJ,KAAIM,GAAE;AACR,eAAK,gBAAgB,KAAK,iBAAiB,KAAK,iBAAiBN,IAAG,KAAK,gBAAgBA,KAAI,KAAK,iBAAiBiB,MAAKjB;AACxH,gBAAM,IAAI,KAAK,gBAAgB,GAAGiB,EAAC,OAAO;AAC1C,iBAAO,OAAOxB,GAAE,OAAO;AAAA,YACrB,UAAU,GAAG,KAAK,MAAMI,EAAC,CAAC;AAAA,YAC1B,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF,CAAC,GAAG,GAAIK,KAAI,CAAC,MAAM;AAAA,QACjB,SAASA;AAAA,QACT,SAASV,GAAE,cAAc;AAAA,MAC3B,CAAC,CAAC,IAAI,CAAC,CAAE,GACT;AAAA,QACE,GAAG+B;AAAA,QACH,GAAGC;AAAA,QACH,WAAW7B;AAAA,QACX,gBAAgBY;AAAA,MAClB,IAAI,MAAM,gBAAgBf,GAAE,SAASC,IAAG;AAAA,QACtC,WAAWD,GAAE;AAAA,QACb,YAAYM;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC;AACH,UAAI,OAAO,OAAOL,GAAE,OAAO;AAAA,QACzB,KAAK;AAAA,QACL,MAAM;AAAA,QACN,WAAW,GAAG,EAAE8B,EAAC,CAAC,MAAM,EAAEC,EAAC,CAAC;AAAA,MAC9B,CAAC,GAAG/B,GAAE,aAAa,oBAAoBE,EAAC,IAAIQ,KAAI,KAAK,KAAK,aAAa,QAAQA,GAAE,QAAQ,CAAAN,OAAK;AAC5F,aAAK,mBAAmB,IAAIA,EAAC,KAAK,KAAK,mBAAmB,IAAIA,IAAGA,GAAE,aAAa,WAAW,CAAC,GAAGA,GAAE,aAAa,aAAaF,EAAC;AAAA,MAC9H,CAAC,GAAGO,MAAKK,GAAE,OAAO;AAChB,cAAM;AAAA,UACJ,GAAGV;AAAA,UACH,GAAGO;AAAA,QACL,IAAIG,GAAE;AACN,eAAO,OAAOL,GAAE,OAAO;AAAA,UACrB,KAAKP,GAAE,WAAW,OAAO,KAAKA,GAAE,WAAW,MAAM,IAAI,QAAQ;AAAA,UAC7D,MAAMA,GAAE,WAAW,QAAQ,KAAKA,GAAE,WAAW,KAAK,IAAI,QAAQ;AAAA,UAC9D,WAAW,GAAG,EAAEE,EAAC,CAAC,MAAM,EAAEO,EAAC,CAAC;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,EACA,uBAAuB;AACrB,SAAK,WAAW,KAAK,OAAO,MAAM,eAAe,YAAY,GAAG,KAAK,OAAO,MAAM,eAAe,WAAW,GAAG,KAAK,gBAAgB,QAAQ,KAAK,gBAAgB;AAAA,EACnK;AAAA,EACA,gBAAgB;AACd,aAAS,iBAAiB,sBAAsB,KAAK,oBAAoB;AAAA,EAC3E;AAAA,EACA,cAAc;AACZ,QAAIZ;AACJ,SAAK,KAAK,UAAUA,KAAI,KAAK,YAAY,QAAQA,GAAE,KAAK,IAAI,GAAG,KAAK,UAAU;AAAA,EAChF;AAAA,EACA,mBAAmB;AACjB,QAAIA;AACJ,KAACA,KAAI,KAAK,YAAY,QAAQA,GAAE,KAAK,IAAI,GAAG,KAAK,UAAU,QAAQ,SAAS,oBAAoB,sBAAsB,KAAK,oBAAoB;AAAA,EACjJ;AACF;AACA,SAAS,4BAA4BA,IAAGK,IAAGD,IAAG;AAC5C,QAAMH,KAAID,GAAE,aAAaK,EAAC;AAC1B,MAAIC,KAAIL,KAAIA,GAAE,MAAM,KAAK,IAAI,CAAC;AAC9B,EAAAK,KAAIA,GAAE,OAAO,CAAAH,OAAK,CAACC,GAAE,KAAK,CAAAF,OAAKC,OAAMD,EAAC,CAAC,GAAGI,GAAE,SAASN,GAAE,aAAaK,IAAGC,GAAE,KAAK,GAAG,CAAC,IAAIN,GAAE,gBAAgBK,EAAC;AAC3G;AACA,SAAS,yBAAyBL,IAAGK,IAAGD,IAAG;AACzC,QAAMH,KAAI,MAAM,QAAQG,EAAC,IAAIA,KAAI,CAACA,EAAC,GACjCE,KAAIN,GAAE,aAAaK,EAAC,GACpBF,KAAIG,KAAIA,GAAE,MAAM,KAAK,IAAI,CAAC;AAC5B,SAAOL,GAAE,MAAM,CAAAW,OAAKT,GAAE,QAAQS,EAAC,IAAI,EAAE,IAAI,MAAM;AAAA,EAAC,KAAKT,GAAE,KAAK,GAAGF,EAAC,GAAGD,GAAE,aAAaK,IAAGF,GAAE,KAAK,GAAG,CAAC,GAAG,MAAM,4BAA4BH,IAAGK,IAAGJ,EAAC;AAC9I;AACA,IAAI,oBAAoB,CAAAK,QAAMA,GAAEA,GAAE,QAAQ,CAAC,IAAI,SAASA,GAAEA,GAAE,QAAQ,CAAC,IAAI,SAASA,GAAEA,GAAE,YAAY,CAAC,IAAI,aAAaA,KAAI,oBAAoB,CAAC,CAAC;AAC9I,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAYL,IAAG;AAAA,IACb,SAASD;AAAA,IACT,cAAcM;AAAA,IACd,oBAAoBD;AAAA,EACtB,GAAG;AACD,SAAK,SAASJ;AACd,SAAK,eAAe;AACpB,SAAK,eAAe,CAAC,CAACK,IAAG,KAAK,qBAAqBD,IAAG,KAAK,gBAAgB,KAAK,KAAK,GAAG,KAAK,UAAUL;AAAA,EACzG;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,OAAO;AACT,QAAIC,IAAGD;AACP,YAAQA,MAAKC,KAAI,KAAK,YAAY,OAAO,SAASA,GAAE,SAAS,OAAOD,KAAI;AAAA,EAC1E;AAAA,EACA,IAAI,KAAKC,IAAG;AACV,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,OAAOA;AACpB;AAAA,IACF;AACA,IAAAA,OAAM,eAAe,YAAY,YAAY,EAAE,KAAK,MAAY;AAC9D,YAAM;AAAA,QACJ,SAASD;AAAA,MACX,IAAI,MAAM,QAAQ,QAAQ,EAAE,KAAK,WAAY;AAC3C,eAAO;AAAA,MACT,CAAC;AACD,WAAK,UAAU,IAAIA,GAAE,GAAG,KAAK,QAAQ,OAAO;AAAA,IAC9C,EAAC,GAAG,OAAO,mCAA0B;AAAA,EACvC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQC,IAAG;AACb,QAAID;AACJ,IAAAC,MAAK,KAAK,YAAYA,OAAM,KAAK,WAAW,KAAK,QAAQ,iBAAiB,IAAI,GAAG,KAAK,WAAWA,IAAG,KAAK,QAAQ,cAAc,IAAI,GAAG,KAAK,YAAY,GAAG,KAAK,mBAAmB,KAAK,MAAM,IAAID,KAAI,KAAK,uBAAuB,QAAQA,GAAE,KAAK,MAAM,KAAK,OAAO;AAAA,EACpQ;AAAA,EACA,mBAAmBC,IAAG;AAAA,EAAC;AAAA,EACvB,qBAAqB;AAAA,EAAC;AAAA,EACtB,qBAAqB;AAAA,EAAC;AAAA,EACtB,OAAO;AAAA,EAAC;AAAA,EACR,cAAc;AAAA,EAAC;AAAA,EACf,QAAQ;AACN,QAAIA;AACJ,SAAK,mBAAmB,IAAIA,KAAI,KAAK,oBAAoB,QAAQA,GAAE,MAAM;AAAA,EAC3E;AAAA,EACA,gBAAgB;AACd,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,mBAAmB;AACjB,SAAK,gBAAgB,KAAK,MAAM;AAAA,EAClC;AACF;AACA,IAAM,IAAI;AACV,IAAM,yBAAyB;AAAA,EAC7B,OAAO;AAAA,EACP,UAAU;AAAA,EACV,OAAO;AACT;AACA,IAAM,sBAAN,cAAkC,sBAAsB;AAAA,EACtD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO,iBAAiB;AAC7B,SAAK,iBAAiB;AACtB,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,MAAM;AAC3B,UAAIA;AACJ,mBAAa,KAAK,OAAO,GAAG,KAAK,WAAW,KAAK,mBAAmBA,KAAI,KAAK,YAAY,OAAO,SAASA,GAAE,WAAW,YAAY,YAAY,MAAM,SAAS,oBAAoB,aAAa,KAAK,eAAe,GAAG,SAAS,oBAAoB,iBAAiB,KAAK,eAAe;AAAA,IACzR;AAAA,EACF;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,mBAAmB,aAAa,KAAK,mBAAmB;AAAA,EACtE;AAAA,EACA,kBAAkB;AAChB,SAAK,OAAO,MAAI,KAAK,iBAAiB,KAAK,mBAAmB,cAAc,YAAY;AAAA,EAC1F;AAAA,EACA,kBAAkBA,IAAG;AACnB,KAAC,KAAK,UAAUA,GAAE,WAAW,MAAM,KAAK,iBAAiB,aAAa,SAAS,iBAAiB,aAAa,KAAK,eAAe,GAAG,SAAS,iBAAiB,iBAAiB,KAAK,eAAe,GAAG,oBAAoB,KAAK,YAAY,KAAK,UAAU,WAAW,MAAM;AACzQ,WAAK,UAAU,KAAK,OAAO,cAAc,IAAI,YAAY,aAAa;AAAA,QACpE,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,GAAG,CAAC;AAAA,EACN;AAAA,EACA,cAAcA,IAAG;AACf,UAAM;AAAA,MACJ,MAAMD;AAAA,MACN,QAAQE;AAAA,IACV,IAAID;AACJ,IAAAC,MAAKF,OAAM,gBAAgBC,GAAE,gBAAgB,GAAGA,GAAE,yBAAyB;AAAA,EAC7E;AAAA,EACA,YAAYA,IAAG;AACb,UAAM;AAAA,MACJ,MAAMD;AAAA,MACN,QAAQE;AAAA,IACV,IAAID;AACJ,QAAID,OAAM,WAAWE,MAAKF,OAAM,aAAa;AAC3C,UAAI,CAAC,KAAK,OAAQ;AAClB,MAAAC,GAAE,gBAAgB,GAAG,KAAK,OAAO,cAAc,IAAI,YAAY,aAAa;AAAA,QAC1E,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF,CAAC,CAAC,GAAG,WAAW,MAAM;AACpB,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,mBAAmBA,IAAG;AACpB,QAAI,KAAK,uBAAuB,QAAQ,CAAC,KAAK,QAAQ,SAAS,OAAQ;AACvE,UAAMD,KAAI,SAAS,cAAc,KAAK;AACtC,IAAAA,GAAE,KAAK,oCAAoC,SAAS,CAAC;AACrD,UAAME,KAAI,MAAM,KAAK,UAAU,IAAI,UAAU;AAC7C,IAAAF,GAAE,cAAc,uBAAuBE,EAAC,GAAGF,GAAE,OAAO;AACpD,UAAMI,KAAIH,GAAE,YAAY,GACtBE,KAAI,KAAK,QAAQ,YAAY;AAC/B,IAAAC,OAAMD,KAAI,KAAK,QAAQ,OAAOH,EAAC,KAAKA,GAAE,SAAS,EAAE,UAAUI,KAAIH,GAAE,sBAAsB,YAAYD,EAAC;AACpG,UAAMK,KAAI,yBAAyBJ,IAAG,oBAAoB,CAACD,GAAE,EAAE,CAAC;AAChE,SAAK,qBAAqB,MAAM;AAC9B,MAAAK,GAAE,GAAGL,GAAE,OAAO,GAAG,KAAK,qBAAqB;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,iBAAiB,KAAK,mBAAmB,YAAY,OAAO,KAAK;AAAA,EACxE;AAAA,EACA,OAAO;AACL,QAAIA;AACJ,KAACA,KAAI,KAAK,oBAAoB,QAAQA,GAAE,MAAM,GAAG,KAAK,kBAAkB,IAAI,gBAAgB;AAC5F,UAAM;AAAA,MACJ,QAAQC;AAAA,IACV,IAAI,KAAK;AACT,SAAK,OAAO,iBAAiB,aAAa,MAAM,KAAK,gBAAgB,GAAG;AAAA,MACtE,QAAQA;AAAA,IACV,CAAC,GAAG,KAAK,OAAO,iBAAiB,eAAe,CAAAC,OAAK,KAAK,kBAAkBA,EAAC,GAAG;AAAA,MAC9E,QAAQD;AAAA,IACV,CAAC,GAAG,KAAK,mBAAmB,KAAK,MAAM,GAAG,CAAC,KAAK,OAAO,mBAAmB,KAAK,OAAO,iBAAiB,WAAW,CAAAC,OAAK,KAAK,cAAcA,EAAC,GAAG;AAAA,MAC5I,QAAQD;AAAA,IACV,CAAC,GAAG,KAAK,OAAO,iBAAiB,SAAS,CAAAC,OAAK,KAAK,YAAYA,EAAC,GAAG;AAAA,MAClE,QAAQD;AAAA,IACV,CAAC;AAAA,EACH;AACF;AACA,IAAM,kBAAN,cAA8B,sBAAsB;AAAA,EAClD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO,iBAAiB;AAC7B,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,sBAAsB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,oBAAoB;AAAA,EAC/E;AAAA,EACA,oBAAoB;AAClB,SAAK,oBAAoB,KAAK;AAAA,EAChC;AAAA,EACA,OAAO;AACL,QAAID;AACJ,KAACA,KAAI,KAAK,oBAAoB,QAAQA,GAAE,MAAM,GAAG,KAAK,kBAAkB,IAAI,gBAAgB;AAC5F,UAAM;AAAA,MACJ,QAAQC;AAAA,IACV,IAAI,KAAK;AACT,SAAK,OAAO,iBAAiB,SAAS,MAAM,KAAK,YAAY,GAAG;AAAA,MAC9D,QAAQA;AAAA,IACV,CAAC,GAAG,KAAK,OAAO,iBAAiB,eAAe,MAAM,KAAK,kBAAkB,GAAG;AAAA,MAC9E,QAAQA;AAAA,IACV,CAAC;AAAA,EACH;AACF;AACA,IAAM,IAAI;AACV,IAAM,kBAAN,cAA8B,sBAAsB;AAAA,EAClD,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,OAAO,iBAAiB;AAC7B,SAAK,aAAa,CAAC;AACnB,SAAK,YAAY;AACjB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,sBAAsB;AACpB,QAAIA;AACJ,KAACA,KAAI,SAAS,kBAAkB,QAAQA,GAAE,QAAQ,gBAAgB,MAAM,KAAK,OAAO,MAAI,KAAK,YAAY;AAAA,EAC3G;AAAA,EACA,uBAAuB;AACrB,SAAK,YAAY,OAAI,CAAC,KAAK,mBAAmB,KAAK,OAAO;AAAA,EAC5D;AAAA,EACA,2BAA2B;AACzB,QAAIA;AACJ,SAAK,iBAAiB,aAAa,KAAK,YAAY,GAAG,KAAK,eAAe,SAAS,GAAGA,KAAI,KAAK,YAAY,QAAQA,GAAE,cAAc,KAAK,OAAO,MAAI,KAAK,iBAAiB;AAAA,EAC5K;AAAA,EACA,2BAA2B;AACzB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,yBAAyB;AACvB,SAAK,iBAAiB,aAAa,KAAK,YAAY,GAAG,KAAK,eAAe;AAAA,EAC7E;AAAA,EACA,yBAAyB;AACvB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,QAAQ,SAAS,OAAQ;AACnC,UAAMA,KAAI,KAAK,OAAO,YAAY,GAChCD,KAAI,KAAK,QAAQ,SAAS,CAAC,EAAE,YAAY,GACzCM,KAAI,KAAK,QAAQ,YAAY;AAC/B,IAAAL,OAAMK,KAAI,KAAK,kCAAkC,IAAIL,OAAMD,MAAK,KAAK,kCAAkC;AAAA,EACzG;AAAA,EACA,oCAAoC;AAClC,UAAMC,KAAI,yBAAyB,KAAK,QAAQ,oBAAoB,CAAC,KAAK,QAAQ,EAAE,CAAC;AACrF,SAAK,qBAAqB,MAAM;AAC9B,MAAAA,GAAE,GAAG,KAAK,qBAAqB;AAAA,IACjC;AAAA,EACF;AAAA,EACA,oCAAoC;AAClC,UAAMA,KAAI,CAAC,GACTD,KAAI,KAAK,QAAQ,SAAS,IAAI,CAAAK,QAAMJ,GAAE,KAAKI,GAAE,EAAE,GAAGA,GAAE,OAAOA,GAAE,KAAK,GAAG,KAAK,QAAQ,QAAQ,YAAY,CAAC,WAAW,SAAS,CAAC,KAAKA,GAAE,GAAG;AACxI,SAAK,aAAaJ;AAClB,UAAMK,KAAI,yBAAyB,KAAK,QAAQ,oBAAoBN,EAAC;AACrE,SAAK,qBAAqB,MAAM;AAC9B,MAAAM,GAAE,GAAG,KAAK,QAAQ,SAAS,IAAI,CAACD,IAAGD,OAAM;AACvC,QAAAC,GAAE,KAAK,KAAK,WAAWD,EAAC;AAAA,MAC1B,CAAC,GAAG,KAAK,qBAAqB;AAAA,IAChC;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,SAAK,iBAAiB;AACtB,UAAMH,KAAI,KAAK;AACf,SAAK,aAAaA,GAAE,QAAQ,gBAAgB,MAAM,KAAK,eAAe,WAAW,MAAM;AACrF,WAAK,OAAO;AAAA,IACd,GAAG,CAAC;AAAA,EACN;AAAA,EACA,OAAO;AACL,QAAID;AACJ,KAACA,KAAI,KAAK,oBAAoB,QAAQA,GAAE,MAAM,GAAG,KAAK,kBAAkB,IAAI,gBAAgB;AAC5F,UAAM;AAAA,MACJ,QAAQC;AAAA,IACV,IAAI,KAAK;AACT,SAAK,OAAO,iBAAiB,WAAW,MAAM,KAAK,oBAAoB,GAAG;AAAA,MACxE,QAAQA;AAAA,IACV,CAAC,GAAG,KAAK,OAAO,iBAAiB,YAAY,MAAM,KAAK,qBAAqB,GAAG;AAAA,MAC9E,QAAQA;AAAA,IACV,CAAC,GAAG,KAAK,OAAO,iBAAiB,gBAAgB,MAAM,KAAK,yBAAyB,GAAG;AAAA,MACtF,QAAQA;AAAA,IACV,CAAC,GAAG,KAAK,OAAO,iBAAiB,gBAAgB,MAAM,KAAK,yBAAyB,GAAG;AAAA,MACtF,QAAQA;AAAA,IACV,CAAC,GAAG,KAAK,WAAW,KAAK,YAAY;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,gBAAiB;AAC3B,UAAM;AAAA,MACJ,QAAQA;AAAA,IACV,IAAI,KAAK;AACT,SAAK,QAAQ,iBAAiB,gBAAgB,MAAM,KAAK,uBAAuB,GAAG;AAAA,MACjF,QAAQA;AAAA,IACV,CAAC,GAAG,KAAK,QAAQ,iBAAiB,gBAAgB,MAAM,KAAK,uBAAuB,GAAG;AAAA,MACrF,QAAQA;AAAA,IACV,CAAC;AAAA,EACH;AACF;AACA,IAAM,aAAa;AAAA,EACjB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AACT;AACA,IAAM,wBAAN,cAAoC,MAAM;AAAA,EACxC,YAAYA,IAAGG,IAAGJ,IAAG;AACnB,UAAM,qBAAqB;AAAA,MACzB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,KAAK,OAAOC,IAAG,KAAK,OAAOG,IAAG,KAAK,WAAWJ,OAAM,SAAS,GAAGC,EAAC,IAAID,EAAC,KAAKC;AAAA,EACjF;AACF;AACA,IAAM,yBAAyB,OAAO,0BAA0B;AAChE,IAAM,IAAI;AAAA;AAAA;AAGV,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAACU,IAAGD,IAAGT,IAAGD,OAAM;AACtB,WAASE,KAAIF,KAAI,IAAI,SAASA,KAAI,EAAEU,IAAGT,EAAC,IAAIS,IAAGP,KAAIQ,GAAE,SAAS,GAAGH,IAAGL,MAAK,GAAGA,KAAK,EAACK,KAAIG,GAAER,EAAC,OAAOD,MAAKF,KAAIQ,GAAEE,IAAGT,IAAGC,EAAC,IAAIM,GAAEN,EAAC,MAAMA;AAC/H,SAAOF,MAAKE,MAAK,EAAEQ,IAAGT,IAAGC,EAAC,GAAGA;AAC/B;AACA,IAAM,IAAI,iBAAiB,SAAS,cAAc,KAAK;AACvD,IAAI,IAAI,cAAc,eAAe;AACrC,IAAI,IAAI,eAAe,CAAC,IAAI,IAAI,iBAAiB,CAAC;AAClD,IAAM,IAAI,MAAMG,WAAU,EAAE;AAAA,EAC1B,cAAc;AACZ,UAAM,GAAG,SAAS;AAClB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,4BAA4B;AACjC,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,kBAAkB,CAAAJ,OAAK;AAC1B,UAAI,CAACA,GAAE,cAAe;AACtB,YAAMD,KAAI,IAAI,MAAM,0BAA0B;AAAA,QAC5C,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AACD,MAAAC,GAAE,cAAc,iBAAiBD,GAAE,MAAM,CAAAE,OAAK;AAC5C,QAAAA,GAAE,aAAa,EAAE,SAAS,IAAI,MAAM,KAAK,OAAO;AAAA,MAClD,CAAC,GAAGD,GAAE,cAAc,cAAcD,EAAC;AAAA,IACrC;AAAA,EACF;AAAA,EACA,IAAI,UAAU;AACZ,QAAIC;AACJ,aAASA,KAAI,KAAK,SAAS,GAAG,EAAE,MAAM,OAAO,SAASA,GAAE,aAAa,SAAS,MAAM,KAAK;AAAA,EAC3F;AAAA,EACA,IAAI,QAAQA,IAAG;AACb,SAAK,WAAWA;AAAA,EAClB;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAASA,IAAG;AACd,QAAID;AACJ,SAAK,YAAYC,IAAGA,OAAMD,KAAI,KAAK,aAAa,QAAQA,GAAE,MAAM,GAAG,KAAK,UAAU,KAAK,MAAM,KAAK,OAAO,UAAO,KAAK,WAAW,GAAG,KAAK,OAAO,KAAK,QAAQ,KAAK,SAAS,KAAK,UAAU;AAAA,EAC3L;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,CAAC,CAAC,KAAK,kBAAkB,EAAE,KAAK,0BAA0B;AAAA,EACnE;AAAA,EACA,IAAI,sBAAsB;AACxB,WAAO,KAAK,yBAAyB,KAAK,uBAAuB,IAAI,oBAAoB,IAAI,IAAI,KAAK;AAAA,EACxG;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAKC,IAAG;AACV,QAAID;AACJ,IAAAC,MAAK,KAAK,YAAYA,OAAM,KAAK,UAAUD,KAAI,KAAK,aAAa,QAAQA,GAAE,mBAAmB,CAACC,OAAM,KAAK,QAAQA,IAAG,KAAK,SAASI,GAAE,aAAa,IAAI,KAAK,cAAc,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,iBAAiB;AAAA,EACnO;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAMJ,IAAG;AACX,QAAIC;AACJ,QAAID,OAAM,KAAK,MAAO;AACtB,UAAMD,KAAI,KAAK;AACf,SAAK,SAASC,KAAI,KAAK,UAAU,YAAY,KAAK,UAAU,eAAeC,KAAI,KAAK,aAAa,QAAQA,GAAE,mBAAmB,IAAI,KAAK,cAAc,SAASF,EAAC;AAAA,EACjK;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,qBAAqB,KAAK,mBAAmB,IAAI,4BAA4B,IAAI,IAAI,KAAK;AAAA,EACxG;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,EAChD;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,aAAa,KAAM,SAAQ,KAAK,MAAM;AAAA,MACxC,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO,KAAK;AAAA,IAChB;AAAA,EACF;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,EAAE,KAAK,SAAS,UAAU,CAAC,KAAK,QAAQ,CAAC,KAAK,kBAAkB,CAAC,KAAK,aAAa,KAAK,SAAS;AAAA,EAC1G;AAAA,EACA,iBAAiB;AACf,QAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,KAAM;AAC1C,UAAMC,KAAI,KAAK,UAAU,GACvBD,KAAI,KAAK,gBACTE,KAAI,KAAK,aAAa,SACtBC,KAAI,KAAK;AACX,SAAK,oBAAoB,aAAa,KAAK,UAAU;AAAA,MACnD,QAAQF;AAAA,MACR,WAAWC;AAAA,MACX,YAAYC;AAAA,MACZ,SAASH;AAAA,MACT,MAAM,KAAK;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACM,oBAAoB;AAAA;AACxB,iBAAAK,GAAA,iBAAM,0BAAN,IAAwB;AACxB,YAAMJ,KAAI,KAAK;AACf,UAAI,KAAK,SAASA,OAAM,MAAM,KAAK,YAAYA,EAAC,GAAG,KAAK,SAASA,QAAO,MAAM,KAAK,YAAYA,EAAC,GAAG,KAAK,SAASA,IAAI;AACrH,YAAMD,KAAI,MAAM,KAAK,eAAeC,EAAC;AACrC,WAAK,SAASA,OAAM,MAAM,KAAK,WAAWA,IAAGD,EAAC;AAAA,IAChD;AAAA;AAAA,EACM,WAAWC,IAAGD,IAAG;AAAA;AACrB,UAAI,EAAE,KAAK,kBAAkB,WAAW,KAAK,SAAS,SAAS;AAC7D,YAAI,MAAM,UAAU,GAAG,MAAM,UAAU,GAAGC,OAAM,KAAK,QAAQ,CAAC,KAAK,MAAM;AACvE,eAAK,wBAAwB,KAAK,SAAS,KAAK,YAAY,EAAE,aAAa,KAAK,KAAK,eAAe,MAAM;AAC1G;AAAA,QACF;AACA,QAAAD,MAAK,QAAQA,GAAE,MAAM;AAAA,MACvB;AAAA,IACF;AAAA;AAAA,EACA,cAAc;AACZ,QAAIA;AACJ,QAAI,KAAK,QAAQ,KAAK,SAAS,OAAQ;AACvC,UAAMC,KAAI,MAAM;AACd,UAAIO,IAAGW;AACP,YAAMjB,KAAI,CAAC;AACX,UAAIC,KAAI,SAAS;AACjB,cAAQK,KAAIL,MAAK,OAAO,SAASA,GAAE,eAAe,QAAQK,GAAE,gBAAgB,CAAAL,KAAIA,GAAE,WAAW;AAC7F,aAAOA,MAAI;AACT,cAAMI,KAAIJ,GAAE,gBAAgBA,GAAE,mBAAmBgB,KAAIhB,GAAE,YAAY,MAAM,OAAO,SAASgB,GAAE;AAC3F,QAAAZ,MAAKL,GAAE,KAAKK,EAAC,GAAGJ,KAAII;AAAA,MACtB;AACA,aAAOL;AAAA,IACT;AACA,SAAK,kBAAkB,YAAYF,KAAI,KAAK,mBAAmB,QAAQA,GAAE,UAAU,KAAK,SAAS,KAAK,YAAY,EAAE,aAAa,KAAKC,GAAE,EAAE,SAAS,IAAI,KAAK,SAAS,kBAAkB,SAAS,SAAS,KAAK,eAAe,MAAM;AAAA,EACrO;AAAA,EACM,WAAWA,IAAG;AAAA;AAClB,UAAI,EAAE,CAAC,KAAK,eAAe,KAAK,UAAU,KAAK,eAAe,MAAM,KAAK,iBAAiB,KAAK,QAAQ,aAAa,IAAI,IAAI,GAAG,KAAK,qBAAqB,SAAS,iBAAiB,aAAa,MAAM;AACpM,aAAK,SAAS,UAAU,OAAO,4BAA4B,KAAE,GAAG,KAAK,mBAAmB;AAAA,MAC1F,GAAG;AAAA,QACD,MAAM;AAAA,MACR,CAAC,GAAG,KAAK,SAAS,UAAU,OAAO,4BAA4B,IAAE,OAAOA,MAAK,KAAK,QAAQ,GAAG,aAAa,OAAO,IAAI,IAAI,KAAK,QAAQ,KAAK,UAAU,WAAW,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,KAAK,UAAU,aAAa,KAAK,QAAQ,YAAY,KAAK,aAAa,KAAK,iBAAiB,IAAI,KAAK,kBAAkB,GAAG,KAAK,SAAS,SAAS;AACvV,cAAMD,KAAI,KAAK,YAAY;AAC3B,aAAK,OAAOA,GAAE,iBAAiB,YAAY,KAAK,iBAAiB;AAAA,UAC/D,SAAS;AAAA,QACX,CAAC,IAAIA,GAAE,oBAAoB,YAAY,KAAK,iBAAiB;AAAA,UAC3D,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,EACA,aAAa;AACX,QAAIC;AACJ,KAACA,KAAI,KAAK,aAAa,QAAQA,GAAE,MAAM,GAAG,KAAK,WAAW,QAAQ,KAAK,wBAAwB,KAAK,uBAAuB,KAAK,WAAW,IAAI,WAAW,KAAK,kBAAkB,EAAE,KAAK,gBAAgB;AAAA,MACtM,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EACA,mBAAmBA,IAAG;AACpB,IAAAA,GAAE,aAAa,UAAU,KAAK,mBAAmB;AAAA,EACnD;AAAA,EACA,qBAAqB;AACnB,QAAIA;AACJ,QAAI,GAAGA,KAAI,KAAK,aAAa,QAAQA,GAAE,kBAAkB;AACvD,WAAK,OAAO;AACZ;AAAA,IACF;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,mBAAmB;AACjB,SAAK,OAAO,MAAI,KAAK,oBAAoB,uBAAuB,MAAI,KAAK,WAAW,KAAE;AAAA,EACxF;AAAA,EACA,mBAAmB;AACjB,QAAIA,IAAGD;AACP,SAAK,SAAS,SAAS,KAAK,0BAA0BA,KAAI,KAAK,aAAa,QAAQA,GAAE,mBAAmB,KAAK,cAAc,MAAMC,KAAI,KAAK,aAAa,QAAQA,GAAE,mBAAmB;AAAA,EACvL;AAAA,EACA,qBAAqB;AACnB,UAAMA,KAAI,KAAK;AACf,WAAO,KAAK,mBAAmB,OAAIA;AAAA,EACrC;AAAA,EACA,mBAAmB;AACjB,SAAK,8BAA8B,KAAK,SAAS,KAAK,cAAc,IAAI,sBAAsB,mBAAmB,KAAK,OAAO,CAAC,IAAI,sBAAsB,CAAC,GAAG,KAAK,4BAA4B,KAAK;AAAA,EACpM;AAAA,EACA,WAAWA,IAAG;AACZ,QAAIC;AACJ,QAAI,KAAK,aAAa,IAAI,KAAK,KAAK,aAAa,MAAM,GAAG,KAAK,QAAQ,YAAY,CAAC,IAAI,SAAS,CAAC,EAAE,GAAGD,GAAE,IAAI,MAAM,MAAM,KAAK,cAAc,KAAK,SAAS,KAAK,WAAWA,GAAE,IAAI,MAAM,CAAC,GAAGA,GAAE,IAAI,SAAS,GAAG;AAC1M,YAAM,CAACE,IAAGK,EAAC,MAAMN,KAAI,KAAK,YAAY,OAAO,SAASA,GAAE,MAAM,GAAG,MAAM,CAAC;AACxE,WAAK,gBAAgB,WAAWC,KAAI,IAAIA,EAAC,KAAK,IAAI,KAAK,qBAAqBK;AAAA,IAC9E;AACA,QAAIR,KAAI;AACR,IAAAC,GAAE,IAAI,4BAA4B,MAAMD,KAAI,KAAK,gBAAgB,KAAK,iBAAiB,KAAK,gBAAgB,UAAUC,GAAE,IAAI,gBAAgB,MAAMD,KAAIC,GAAE,IAAI,gBAAgB,IAAID,OAAM,SAAM,KAAK,WAAW;AAAA,EAC9M;AAAA,EACA,QAAQC,IAAG;AACT,UAAM,QAAQA,EAAC,GAAGA,GAAE,IAAI,WAAW,MAAM,KAAK,YAAY,KAAK,SAAS,aAAa,oBAAoB,KAAK,SAAS,IAAI,KAAK,SAAS,gBAAgB,kBAAkB,GAAG,KAAK,QAAQ,OAAOA,GAAE,IAAI,WAAW,KAAK,eAAe,KAAK,oBAAoB,qBAAqB,IAAIA,GAAE,IAAI,OAAO,KAAK,KAAK,UAAU,YAAY,OAAOA,GAAE,IAAI,OAAO,KAAK,eAAe,KAAK,oBAAoB,qBAAqB;AAAA,EAC9Z;AAAA,EACA,gBAAgB;AACd,WAAO;AAAA,gCACqB,KAAK,gBAAgB;AAAA;AAAA,EAEnD;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO;AAAA,MACL,4BAA4BI,GAAE,UAAU,SAAS;AAAA,IACnD;AAAA,EACF;AAAA,EACA,eAAe;AACb,WAAO;AAAA;AAAA;AAAA;AAAA,4BAIiB,IAAI,KAAK,mBAAmB,KAAK,aAAa,UAAU,MAAM,CAAC;AAAA,wBACnE,IAAI,KAAK,cAAc,CAAC;AAAA,yBACvB,KAAK,kBAAkB;AAAA,0BACtB,KAAK,kBAAkB;AAAA,gCACjB,KAAK,kBAAkB;AAAA,8BACzB,KAAK,UAAU,QAAQ;AAAA;AAAA,kBAEnC,KAAK,cAAc,CAAC;AAAA;AAAA;AAAA,EAGpC;AAAA,EACA,gBAAgB;AACd,WAAO;AAAA;AAAA;AAAA;AAAA,4BAIiB,IAAI,KAAK,mBAAmB,KAAK,aAAa,UAAU,MAAM,CAAC;AAAA,0BACjE,IAAI,KAAK,YAAY,CAAC;AAAA,wBACxB,IAAI,KAAK,cAAc,CAAC;AAAA,gCAChB,KAAK,kBAAkB;AAAA,yBAC9B,KAAK,kBAAkB;AAAA,8BAClB,KAAK,UAAU,QAAQ;AAAA;AAAA,kBAEnC,KAAK,cAAc,CAAC;AAAA;AAAA;AAAA,EAGpC;AAAA,EACA,SAAS;AACP,UAAMJ,KAAI,KAAK,SAAS,WAAW,KAAK,SAAS;AACjD,WAAO;AAAA,cACGA,KAAI,KAAK,aAAa,IAAI,KAAK,cAAc,CAAC;AAAA;AAAA;AAAA,EAG1D;AAAA,EACA,oBAAoB;AAClB,UAAM,kBAAkB,GAAG,KAAK,iBAAiB,SAAS,MAAM;AAC9D,WAAK,OAAO;AAAA,IACd,CAAC,GAAG,KAAK,cAAc,KAAK,WAAW;AAAA,EACzC;AAAA,EACA,uBAAuB;AACrB,QAAIA;AACJ,KAACA,KAAI,KAAK,aAAa,QAAQA,GAAE,mBAAmB,GAAG,KAAK,OAAO,OAAI,MAAM,qBAAqB;AAAA,EACpG;AACF;AACA,EAAE,SAAS,CAAC,CAAC,GAAG,EAAE,YAAY,GAAG,EAAE,CAAC,IAAI;AAAA,EACtC,MAAM;AACR,CAAC,CAAC,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,SAAS,CAAC,GAAG,EAAE,WAAW,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EACvF,MAAM;AACR,CAAC,CAAC,GAAG,EAAE,WAAW,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EACvC,SAAS;AAAA,EACT,UAAU;AACZ,CAAC,CAAC,GAAG,EAAE,WAAW,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EACvC,MAAM;AACR,CAAC,CAAC,GAAG,EAAE,WAAW,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EACrC,MAAM;AAAA,EACN,SAAS;AACX,CAAC,CAAC,GAAG,EAAE,WAAW,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,aAAa,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EAC5E,WAAW;AACb,CAAC,CAAC,GAAG,EAAE,WAAW,iBAAiB,CAAC,GAAG,EAAE,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE,WAAW,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,SAAS,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EAC7H,MAAM;AAAA,EACN,WAAW;AACb,CAAC,CAAC,GAAG,EAAE,WAAW,cAAc,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EAChF,WAAW;AACb,CAAC,CAAC,GAAG,EAAE,WAAW,kBAAkB,CAAC,GAAG,EAAE,CAAC,IAAI;AAAA,EAC7C,WAAW;AACb,CAAC,CAAC,GAAG,EAAE,WAAW,sBAAsB,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,QAAQ,CAAC;AAC7E,IAAI,UAAU;AACd,IAAM,YAAyB,OAAO,OAAO;AAAA,EAC3C,WAAW;AAAA,EACX;AAAA,EACA;AACF,CAAC;AACD,IAAM,aAAa;AACnB,IAAM,aAAa,MAAM;AAAA,EACvB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,gBAAgB;AACrB,SAAK,SAAS;AAAA,MACZ,WAAW;AAAA,IACb;AACA,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,eAAe,WAAW,WAAW;AACnC,QAAI,KAAK,UAAU,UAAa,CAAC,CAAC,aAAa,UAAU,cAAc,UAAU,WAAW;AAC1F,WAAK,gBAAgB;AACrB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,eAAe;AACtB,WAAK,gBAAgB;AACrB,iBAAW,MAAM,KAAK,KAAK,GAAG,GAAG;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIM,OAAO;AAAA;AACX,UAAI,KAAK,UAAU,UAAa,KAAK,QAAQ,KAAK,QAAQ;AACxD,aAAK,QAAQ,MAAM,QAAQ,KAAK,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,MAAM,GAAG;AAAA,UACzF,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,QACR,CAAC,CAAC;AACF,iBAAS,KAAK,OAAO,KAAK,KAAK;AAC/B,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,SAAS;AAAA;AACb,cAAQ,OAAO;AAAA,IACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,OAAO;AAAA;AACX,UAAI;AACJ,UAAI,KAAK,UAAU,QAAW;AAC5B,SAAC,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAClE,aAAK,QAAQ;AAAA,MACf;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,SAAS;AACP,WAAO,EAAI,MAAM,MAAM,EAAI,YAAY,MAAM,EAAI,QAAQ,IAAI,CAAC,CAAC;AAAA,EACjE;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,UAAU,CAAC,gBAAgB;AAAA,IAC7B;AAAA,EACF;AACF;AACA,WAAW,QAAQ;", "names": ["t", "e", "o", "s", "n", "i", "r", "h", "l", "c", "a", "u", "d", "y", "x", "f", "v", "t$1", "p", "m", "platform", "max", "offset", "_a", "side", "b", "placement", "overflow", "min", "$", "g", "P", "E"]}