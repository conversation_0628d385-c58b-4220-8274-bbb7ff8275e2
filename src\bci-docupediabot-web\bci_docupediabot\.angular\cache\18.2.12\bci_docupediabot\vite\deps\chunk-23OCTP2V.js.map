{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/overlay-handler-210e45aa.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nclass WidthObserver {\n  constructor(element, callbacks, considerPaddings = true) {\n    this.element = element;\n    this.callbacks = callbacks;\n    this.considerPaddings = considerPaddings;\n    this.breakpointVP1 = 480;\n    this.breakpointVP2 = 768;\n    this.containerPaddingForPhone = 14;\n    this.containerPaddingForTablet = 16;\n    this.desktopBreakpoint = this.breakpointVP2 - 2 * this.containerPaddingForTablet;\n    this.tabletBreakpoint = this.breakpointVP1 - 2 * this.containerPaddingForPhone;\n    this.resizeObserver = new ResizeObserver(() => this.updateViewport());\n    this.isObserving = false;\n  }\n  connect() {\n    this.isObserving = true;\n    this.resizeObserver.observe(this.element);\n    this.updateViewport();\n  }\n  disconnect() {\n    this.isObserving = false;\n    this.resizeObserver.unobserve(this.element);\n  }\n  isConnected() {\n    return this.isObserving;\n  }\n  get currentViewport() {\n    return this._currentViewport;\n  }\n  updateViewport() {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    const availableWidth = this.element.clientWidth;\n    // it is 0 if the overlay is not visible\n    if (availableWidth === 0) {\n      return;\n    }\n    const showVP1 = availableWidth <= (this.considerPaddings ? this.tabletBreakpoint : this.breakpointVP1);\n    const showVP2 = availableWidth <= (this.considerPaddings ? this.desktopBreakpoint : this.breakpointVP2);\n    if (showVP1) {\n      if (this._currentViewport !== 'vp1') {\n        (_b = (_a = this.callbacks).onVp1) === null || _b === void 0 ? void 0 : _b.call(_a);\n        this._currentViewport = 'vp1';\n      }\n    } else if (showVP2) {\n      if (this._currentViewport !== 'vp2') {\n        (_d = (_c = this.callbacks).onVp2) === null || _d === void 0 ? void 0 : _d.call(_c);\n        this._currentViewport = 'vp2';\n      }\n    } else {\n      if (this._currentViewport !== 'vp3') {\n        (_f = (_e = this.callbacks).onVp3) === null || _f === void 0 ? void 0 : _f.call(_e);\n        this._currentViewport = 'vp3';\n      }\n    }\n    (_h = (_g = this.callbacks).onChange) === null || _h === void 0 ? void 0 : _h.call(_g, availableWidth);\n  }\n}\nclass OverlayHandler {\n  constructor(_fullwidth = false, overlay = undefined, host = undefined, dialog = undefined) {\n    this._fullwidth = _fullwidth;\n    this.visible = false;\n    this.isFocussed = false;\n    // event to trigger when an overlay is opened\n    this.overlayOpenedEvent = new CustomEvent('OverlayOpenedEvent');\n    this.overlay = overlay;\n    this.host = host;\n    this.dialog = dialog;\n  }\n  get overlay() {\n    return this._overlay;\n  }\n  set overlay(overlay) {\n    this._overlay = overlay;\n    if (this._overlay) {\n      this._overlay.host = this.host;\n      this._overlay.dialog = this.dialog;\n    }\n  }\n  get host() {\n    return this._host;\n  }\n  set host(host) {\n    var _a, _b;\n    if (this._host === host) {\n      return;\n    }\n    this._host = host;\n    const isConnected = (_b = (_a = this.widthObserver) === null || _a === void 0 ? void 0 : _a.isConnected()) !== null && _b !== void 0 ? _b : false;\n    if (this.widthObserver) {\n      this.widthObserver.disconnect();\n    }\n    if (this._fullwidth && host !== undefined) {\n      this.widthObserver = new WidthObserver(host, {\n        onChange: width => this.updateOverlayWidth(width)\n      });\n      if (isConnected) {\n        this.widthObserver.connect();\n      }\n    }\n    if (this.overlay) {\n      this.overlay.host = host;\n    }\n  }\n  get dialog() {\n    return this._dialog;\n  }\n  set dialog(dialog) {\n    this._dialog = dialog;\n    if (this.overlay) {\n      this.overlay.dialog = dialog;\n    }\n  }\n  isReady() {\n    return this.host !== undefined && this.overlay !== undefined && this.dialog !== undefined;\n  }\n  isHidden() {\n    return this.isReady() && !this.visible;\n  }\n  isVisible() {\n    return this.isReady() && this.visible;\n  }\n  async show() {\n    var _a;\n    if (this.isHidden()) {\n      // if another overlay is open, this event triggers it to close itself\n      document.dispatchEvent(this.overlayOpenedEvent);\n      (_a = this.widthObserver) === null || _a === void 0 ? void 0 : _a.connect();\n      await this.handleVisibilityChanged(() => this.overlay.show());\n      this.setupFocusObserver();\n      this.setupScrollObserver();\n      this.setupMutationObserver();\n      document.addEventListener('OverlayOpenedEvent', () => {\n        this.hide();\n      });\n    }\n  }\n  async hide() {\n    var _a;\n    (_a = this.widthObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n    if (this.isVisible()) {\n      this.removeFocusObserver();\n      this.removeScrollObserver();\n      this.removeMutationObserver();\n      document.removeEventListener('OverlayOpenedEvent', () => {});\n      return this.handleVisibilityChanged(() => this.overlay.hide());\n    }\n  }\n  updateOverlayWidth(width) {\n    this.dialog.style.width = `${width}px`;\n    this.updateOverlay();\n  }\n  async handleVisibilityChanged(action) {\n    var _a;\n    const oldVisibility = this.visible;\n    this.visible = await action();\n    if (oldVisibility !== this.visible) {\n      (_a = this.visibilityChangeObserver) === null || _a === void 0 ? void 0 : _a.call(this, this.visible);\n    }\n  }\n  setupFocusObserver() {\n    document.addEventListener('focusin', event => this.handleFocus(event));\n  }\n  removeFocusObserver() {\n    document.removeEventListener('focusin', event => this.handleFocus(event));\n  }\n  handleFocus(event) {\n    var _a;\n    const oldFocusState = this.isFocussed;\n    this.isFocussed = event.relatedTarget === this.dialog.parentNode || event.target === this.dialog.parentNode;\n    if (oldFocusState != this.isFocussed) {\n      (_a = this.focusChangeObserver) === null || _a === void 0 ? void 0 : _a.call(this, this.isFocussed);\n    }\n  }\n  setupScrollObserver() {\n    window.addEventListener('scroll', () => this.updateOverlay());\n    window.addEventListener('wheel', () => this.updateOverlay());\n  }\n  removeScrollObserver() {\n    window.removeEventListener('scroll', () => this.updateOverlay());\n    window.removeEventListener('wheel', () => this.updateOverlay());\n  }\n  setupMutationObserver() {\n    this.resizeObserver = new ResizeObserver(entries => {\n      entries.forEach(_ => {\n        this.updateOverlay();\n      });\n    });\n    this.resizeObserver.observe(this.dialog);\n  }\n  removeMutationObserver() {\n    var _a;\n    (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n  }\n  updateOverlay() {\n    window.requestAnimationFrame(() => this.overlay.update());\n  }\n}\nexport { OverlayHandler as O, WidthObserver as W };\n\n"], "mappings": ";;;;;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YAAY,SAAS,WAAW,mBAAmB,MAAM;AACvD,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,2BAA2B;AAChC,SAAK,4BAA4B;AACjC,SAAK,oBAAoB,KAAK,gBAAgB,IAAI,KAAK;AACvD,SAAK,mBAAmB,KAAK,gBAAgB,IAAI,KAAK;AACtD,SAAK,iBAAiB,IAAI,eAAe,MAAM,KAAK,eAAe,CAAC;AACpE,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,UAAU;AACR,SAAK,cAAc;AACnB,SAAK,eAAe,QAAQ,KAAK,OAAO;AACxC,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,aAAa;AACX,SAAK,cAAc;AACnB,SAAK,eAAe,UAAU,KAAK,OAAO;AAAA,EAC5C;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB;AACf,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAChC,UAAM,iBAAiB,KAAK,QAAQ;AAEpC,QAAI,mBAAmB,GAAG;AACxB;AAAA,IACF;AACA,UAAM,UAAU,mBAAmB,KAAK,mBAAmB,KAAK,mBAAmB,KAAK;AACxF,UAAM,UAAU,mBAAmB,KAAK,mBAAmB,KAAK,oBAAoB,KAAK;AACzF,QAAI,SAAS;AACX,UAAI,KAAK,qBAAqB,OAAO;AACnC,SAAC,MAAM,KAAK,KAAK,WAAW,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAClF,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,WAAW,SAAS;AAClB,UAAI,KAAK,qBAAqB,OAAO;AACnC,SAAC,MAAM,KAAK,KAAK,WAAW,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAClF,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,UAAI,KAAK,qBAAqB,OAAO;AACnC,SAAC,MAAM,KAAK,KAAK,WAAW,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE;AAClF,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AACA,KAAC,MAAM,KAAK,KAAK,WAAW,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,cAAc;AAAA,EACvG;AACF;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,aAAa,OAAO,UAAU,QAAW,OAAO,QAAW,SAAS,QAAW;AACzF,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,aAAa;AAElB,SAAK,qBAAqB,IAAI,YAAY,oBAAoB;AAC9D,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,OAAO,KAAK;AAC1B,WAAK,SAAS,SAAS,KAAK;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,MAAM;AACb,QAAI,IAAI;AACR,QAAI,KAAK,UAAU,MAAM;AACvB;AAAA,IACF;AACA,SAAK,QAAQ;AACb,UAAM,eAAe,MAAM,KAAK,KAAK,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC5I,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,WAAW;AAAA,IAChC;AACA,QAAI,KAAK,cAAc,SAAS,QAAW;AACzC,WAAK,gBAAgB,IAAI,cAAc,MAAM;AAAA,QAC3C,UAAU,WAAS,KAAK,mBAAmB,KAAK;AAAA,MAClD,CAAC;AACD,UAAI,aAAa;AACf,aAAK,cAAc,QAAQ;AAAA,MAC7B;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,OAAO;AAAA,IACtB;AAAA,EACF;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,SAAK,UAAU;AACf,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,SAAS;AAAA,IACxB;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,KAAK,SAAS,UAAa,KAAK,YAAY,UAAa,KAAK,WAAW;AAAA,EAClF;AAAA,EACA,WAAW;AACT,WAAO,KAAK,QAAQ,KAAK,CAAC,KAAK;AAAA,EACjC;AAAA,EACA,YAAY;AACV,WAAO,KAAK,QAAQ,KAAK,KAAK;AAAA,EAChC;AAAA,EACM,OAAO;AAAA;AACX,UAAI;AACJ,UAAI,KAAK,SAAS,GAAG;AAEnB,iBAAS,cAAc,KAAK,kBAAkB;AAC9C,SAAC,KAAK,KAAK,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAC1E,cAAM,KAAK,wBAAwB,MAAM,KAAK,QAAQ,KAAK,CAAC;AAC5D,aAAK,mBAAmB;AACxB,aAAK,oBAAoB;AACzB,aAAK,sBAAsB;AAC3B,iBAAS,iBAAiB,sBAAsB,MAAM;AACpD,eAAK,KAAK;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,EACM,OAAO;AAAA;AACX,UAAI;AACJ,OAAC,KAAK,KAAK,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAC7E,UAAI,KAAK,UAAU,GAAG;AACpB,aAAK,oBAAoB;AACzB,aAAK,qBAAqB;AAC1B,aAAK,uBAAuB;AAC5B,iBAAS,oBAAoB,sBAAsB,MAAM;AAAA,QAAC,CAAC;AAC3D,eAAO,KAAK,wBAAwB,MAAM,KAAK,QAAQ,KAAK,CAAC;AAAA,MAC/D;AAAA,IACF;AAAA;AAAA,EACA,mBAAmB,OAAO;AACxB,SAAK,OAAO,MAAM,QAAQ,GAAG,KAAK;AAClC,SAAK,cAAc;AAAA,EACrB;AAAA,EACM,wBAAwB,QAAQ;AAAA;AACpC,UAAI;AACJ,YAAM,gBAAgB,KAAK;AAC3B,WAAK,UAAU,MAAM,OAAO;AAC5B,UAAI,kBAAkB,KAAK,SAAS;AAClC,SAAC,KAAK,KAAK,8BAA8B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,MAAM,KAAK,OAAO;AAAA,MACtG;AAAA,IACF;AAAA;AAAA,EACA,qBAAqB;AACnB,aAAS,iBAAiB,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC;AAAA,EACvE;AAAA,EACA,sBAAsB;AACpB,aAAS,oBAAoB,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC;AAAA,EAC1E;AAAA,EACA,YAAY,OAAO;AACjB,QAAI;AACJ,UAAM,gBAAgB,KAAK;AAC3B,SAAK,aAAa,MAAM,kBAAkB,KAAK,OAAO,cAAc,MAAM,WAAW,KAAK,OAAO;AACjG,QAAI,iBAAiB,KAAK,YAAY;AACpC,OAAC,KAAK,KAAK,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,MAAM,KAAK,UAAU;AAAA,IACpG;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,WAAO,iBAAiB,UAAU,MAAM,KAAK,cAAc,CAAC;AAC5D,WAAO,iBAAiB,SAAS,MAAM,KAAK,cAAc,CAAC;AAAA,EAC7D;AAAA,EACA,uBAAuB;AACrB,WAAO,oBAAoB,UAAU,MAAM,KAAK,cAAc,CAAC;AAC/D,WAAO,oBAAoB,SAAS,MAAM,KAAK,cAAc,CAAC;AAAA,EAChE;AAAA,EACA,wBAAwB;AACtB,SAAK,iBAAiB,IAAI,eAAe,aAAW;AAClD,cAAQ,QAAQ,OAAK;AACnB,aAAK,cAAc;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,MAAM;AAAA,EACzC;AAAA,EACA,yBAAyB;AACvB,QAAI;AACJ,KAAC,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AAAA,EAChF;AAAA,EACA,gBAAgB;AACd,WAAO,sBAAsB,MAAM,KAAK,QAAQ,OAAO,CAAC;AAAA,EAC1D;AACF;", "names": []}