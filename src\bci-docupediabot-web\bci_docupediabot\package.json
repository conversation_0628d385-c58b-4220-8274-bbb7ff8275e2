{"name": "bci-docupediabot", "version": "0.0.0", "scripts": {"start": "ng serve --configuration development -o", "build": "ng build --configuration production", "build:app": "ng build bci-docupediabot --configuration production", "build:portal": "ng build bci-docupediabot --configuration portal", "e2e": "ng e2e --webdriver-update=false", "e2e:ci": "ng e2e --webdriver-update=false --protractor-config=./e2e/protractor-ci.conf.js", "lint:app": "ng lint bci-docupediabot", "ng": "ng", "pree2e": "webdriver-manager update --ignore_ssl=true --gecko false", "start:subpath": "ng serve --port 4202 --serve-path=/test/ --base-href=/test/", "test": "ng test", "test:app": "ng test bci-docupediabot --browsers=Chrome", "test:headless": "npm run test:headless:app", "test:headless:app": "ng test bci-docupediabot --watch=false --code-coverage --browsers=ChromeHeadlessNoSandbox", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/cdk": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/material": "^18.2.12", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@bci-portal/iframe-integration": "5.17.0", "@bci-web-core/auth": "18.1.2", "@bci-web-core/common-styles": "8.1.2", "@bci-web-core/core": "18.1.2", "@bci-web-core/material-theme": "18.1.1", "@bci-web-core/web-components": "11.1.1", "@juggle/resize-observer": "^3.3.1", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "angular-auth-oidc-client": "18.0.1", "filesize": "^10.0.6", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10", "prismjs": "^1.29.0"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.10", "@angular/cli": "^18.2.0", "@angular/compiler-cli": "^18.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2", "@types/prismjs": "^1.26.4"}}