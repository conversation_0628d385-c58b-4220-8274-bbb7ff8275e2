/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import {
  APP_INITIALIZER,
  CUSTOM_ELEMENTS_SCHEMA,
  NgModule,
} from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { BciCoreModule } from '@bci-web-core/core';
import {
  HTTP_INTERCEPTORS,
  HttpClientModule,
  HttpClient,
} from '@angular/common/http';
import { SharedModule } from '@shared/shared.module';
import { environment } from '@env/environment';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpService } from '@shared/services';
import {
  ErrorInterceptor,
  LoaderInterceptor,
} from '@shared/services/interceptor.service';
import { firstValueFrom } from 'rxjs';
import { AuthService } from '@shared/services/auth.service';
import { AuthGuard } from '@shared/guards/auth.guard';
import { AuthenticationInterceptor } from '@shared/services/authentication.interceptor';
import { LoginComponent } from '@shared/components/login/login.component';
import { PolicyComponent } from '@shared/components/login/policy/policy.component';
import { UserProfileComponent } from '@shared/components/user-profile/user-profile.component';


export function httpLoaderFactory(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

export function appInitializerFactory(
  translate: TranslateService
): () => Promise<any> {
  translate.setDefaultLang('en');
  return () => {
    translate.setDefaultLang('en');
    return firstValueFrom(translate.use('en'));
  };
}

@NgModule({
  declarations: [AppComponent, LoginComponent, PolicyComponent, UserProfileComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    SharedModule,
    BrowserAnimationsModule,
    BciCoreModule.forRoot({
      prod_environment: environment.production,
      core_config_url: '/assets/config/config.json',
    }),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: httpLoaderFactory,
        deps: [HttpClient],
      },
      defaultLanguage: 'en',
    }),
  ],
  providers: [
    HttpService,
    { provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptor, multi: true },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ErrorInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthenticationInterceptor,
      multi: true,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService],
      multi: true,
    },
    AuthService,
    AuthGuard,
  ],
  bootstrap: [AppComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}
