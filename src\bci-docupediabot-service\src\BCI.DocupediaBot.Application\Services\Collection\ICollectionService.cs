﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Collection;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace BCI.DocupediaBot.Application.Services.Collection
{
  public interface ICollectionService
  {
    Task<CollectionResponseDTO?> QueryCollectionWithPagesAsync(Guid collectionId);
		Task<List<CollectionResponseDTO>> QueryCollectionsAsync();
		Task<ResponseResult> DeleteCollectionByCollectionIdAsync(Guid collectionId);
		Task<ResponseResult> UpdateCollectionAsync(CollectionUpdateDTO dto);
		Task<ResponseResult> AddCollectionAsync(CollectionAddDTO dto);
		Task<ResponseResult> UpdateCollectionModificationTimeAsync(Guid collectionId);
	}
}
