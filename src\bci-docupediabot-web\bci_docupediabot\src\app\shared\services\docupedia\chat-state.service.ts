import { Injectable } from '@angular/core';
import { environment } from '@env/environment';
import { BehaviorSubject } from 'rxjs';

export interface ChatMessage {
  type: 'user' | 'bot';
  content: string;
  formattedContent?: string;
}

@Injectable({
  providedIn: 'root',
})
export class ChatStateService {
  private messagesSubject = new BehaviorSubject<ChatMessage[]>(this.loadFromStorage('chatMessages', []));
  messages$ = this.messagesSubject.asObservable();

  private selectedModelSubject = new BehaviorSubject<string>(this.loadFromStorage('selectedModel', ''));
  selectedModel$ = this.selectedModelSubject.asObservable();

  private selectedCollectionSubject = new BehaviorSubject<string>(this.loadFromStorage('selectedCollection', ''));
  selectedCollection$ = this.selectedCollectionSubject.asObservable();

  private hasInteractedSubject = new BehaviorSubject<boolean>(this.loadFromStorage('hasInteracted', false));
  hasInteracted$ = this.hasInteractedSubject.asObservable();

  private loadFromStorage<T>(key: string, defaultValue: T): T {
    try {
      const stored = localStorage.getItem(key);
      if (stored === null || stored === undefined) {
        return defaultValue;
      }


      if (key === 'chatMessages') {
        return JSON.parse(stored) as T;
      } else if (key === 'hasInteracted') {
        return (stored === 'true') as T;
      } else {

        return stored as T;
      }
    } catch (error) {
      console.warn(`Failed to load ${key} from localStorage:`, error);
      return defaultValue;
    }
  }

  public getMessages(): ChatMessage[] {
    return this.messagesSubject.value;
  }

  public getSelectedModel(): string {
    return this.selectedModelSubject.value;
  }

  public getSelectedCollection(): string {
    return this.selectedCollectionSubject.value;
  }

  public getHasInteracted(): boolean {
    return this.hasInteractedSubject.value;
  }

  setMessages(messages: ChatMessage[]): void {
    this.messagesSubject.next(messages);
    this.saveToStorage('chatMessages', messages);
  }

  addMessage(message: ChatMessage): void {
    const currentMessages = this.messagesSubject.value;
    let updatedMessages = [...currentMessages, message];

    if (updatedMessages.length > environment.maxMessages) {
      updatedMessages = updatedMessages.slice(-environment.maxMessages);
    }
    this.messagesSubject.next(updatedMessages);
    this.saveToStorage('chatMessages', updatedMessages);
  }

  setSelectedModel(model: string): void {
    this.selectedModelSubject.next(model);
    this.saveToStorage('selectedModel', model);
  }

  setSelectedCollection(collection: string): void {
    this.selectedCollectionSubject.next(collection);
    this.saveToStorage('selectedCollection', collection);
  }

  setHasInteracted(hasInteracted: boolean): void {
    this.hasInteractedSubject.next(hasInteracted);
    this.saveToStorage('hasInteracted', hasInteracted);
  }

  private saveToStorage<T>(key: string, value: T): void {
    try {
      if (key === 'chatMessages') {
        localStorage.setItem(key, JSON.stringify(value));
      } else {
        localStorage.setItem(key, String(value));
      }
    } catch (error) {
      console.warn(`Failed to save ${key} to localStorage:`, error);
    }
  }

  clearState(): void {
    this.messagesSubject.next([]);
    this.hasInteractedSubject.next(false);
    localStorage.removeItem('chatMessages');
    localStorage.removeItem('hasInteracted');

  }
}
