﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{
    /// <inheritdoc />
    public partial class Addindex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_SysUsersInGroup_GroupId",
                table: "SysUsersInGroup",
                column: "GroupId");

            migrationBuilder.CreateIndex(
                name: "IX_SysUsersInGroup_GroupId_TenantId_IsDeleted",
                table: "SysUsersInGroup",
                columns: new[] { "GroupId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysUsersInGroup_TenantId_IsDeleted",
                table: "SysUsersInGroup",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysUsersInGroup_UserId",
                table: "SysUsersInGroup",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SysUsersInGroup_UserId_GroupId",
                table: "SysUsersInGroup",
                columns: new[] { "UserId", "GroupId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SysUsersInGroup_UserId_TenantId_IsDeleted",
                table: "SysUsersInGroup",
                columns: new[] { "UserId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_CreationTime_TenantId_IsDeleted",
                table: "SysUser",
                columns: new[] { "CreationTime", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_Creator_TenantId_IsDeleted",
                table: "SysUser",
                columns: new[] { "Creator", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_Department",
                table: "SysUser",
                column: "Department");

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_FavCollecitonId",
                table: "SysUser",
                column: "FavCollecitonId");

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_Mail",
                table: "SysUser",
                column: "Mail");

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_Status",
                table: "SysUser",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_TenantId_IsDeleted",
                table: "SysUser",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysUser_UserNTAccount",
                table: "SysUser",
                column: "UserNTAccount",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SysGroup_CreationTime_TenantId_IsDeleted",
                table: "SysGroup",
                columns: new[] { "CreationTime", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysGroup_Creator_TenantId_IsDeleted",
                table: "SysGroup",
                columns: new[] { "Creator", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_SysGroup_Name",
                table: "SysGroup",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_SysGroup_Size",
                table: "SysGroup",
                column: "Size");

            migrationBuilder.CreateIndex(
                name: "IX_SysGroup_TenantId_IsDeleted",
                table: "SysGroup",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_CollectionId",
                table: "PagesInCollection",
                column: "CollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_CollectionId_IsEmbedding",
                table: "PagesInCollection",
                columns: new[] { "CollectionId", "IsEmbedding" });

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_CollectionId_PageId",
                table: "PagesInCollection",
                columns: new[] { "CollectionId", "PageId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_CollectionId_TenantId_IsDeleted",
                table: "PagesInCollection",
                columns: new[] { "CollectionId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_IsEmbedding",
                table: "PagesInCollection",
                column: "IsEmbedding");

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_PageId",
                table: "PagesInCollection",
                column: "PageId");

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_PageId_TenantId_IsDeleted",
                table: "PagesInCollection",
                columns: new[] { "PageId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_PagesInCollection_TenantId_IsDeleted",
                table: "PagesInCollection",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Page_CreationTime_TenantId_IsDeleted",
                table: "Page",
                columns: new[] { "CreationTime", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Page_Creator_TenantId_IsDeleted",
                table: "Page",
                columns: new[] { "Creator", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Page_IsIncludeChild",
                table: "Page",
                column: "IsIncludeChild");

            migrationBuilder.CreateIndex(
                name: "IX_Page_TenantId_IsDeleted",
                table: "Page",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Page_Title",
                table: "Page",
                column: "Title");

            migrationBuilder.CreateIndex(
                name: "IX_Page_Url",
                table: "Page",
                column: "Url");

            migrationBuilder.CreateIndex(
                name: "IX_ContentsInPage_ContentId",
                table: "ContentsInPage",
                column: "ContentId");

            migrationBuilder.CreateIndex(
                name: "IX_ContentsInPage_ContentId_TenantId_IsDeleted",
                table: "ContentsInPage",
                columns: new[] { "ContentId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_ContentsInPage_PageId",
                table: "ContentsInPage",
                column: "PageId");

            migrationBuilder.CreateIndex(
                name: "IX_ContentsInPage_PageId_ContentId",
                table: "ContentsInPage",
                columns: new[] { "PageId", "ContentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ContentsInPage_PageId_TenantId_IsDeleted",
                table: "ContentsInPage",
                columns: new[] { "PageId", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_ContentsInPage_TenantId_IsDeleted",
                table: "ContentsInPage",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Content_CreationTime_TenantId_IsDeleted",
                table: "Content",
                columns: new[] { "CreationTime", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Content_Creator_TenantId_IsDeleted",
                table: "Content",
                columns: new[] { "Creator", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Content_EmbeddingVersionNo",
                table: "Content",
                column: "EmbeddingVersionNo");

            migrationBuilder.CreateIndex(
                name: "IX_Content_SourceModificationTime",
                table: "Content",
                column: "SourceModificationTime");

            migrationBuilder.CreateIndex(
                name: "IX_Content_TenantId_IsDeleted",
                table: "Content",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Content_Title",
                table: "Content",
                column: "Title");

            migrationBuilder.CreateIndex(
                name: "IX_Content_VersionNo",
                table: "Content",
                column: "VersionNo");

            migrationBuilder.CreateIndex(
                name: "IX_Content_VersionNo_TenantId_IsDeleted",
                table: "Content",
                columns: new[] { "VersionNo", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Collection_CreationTime_TenantId_IsDeleted",
                table: "Collection",
                columns: new[] { "CreationTime", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Collection_Creator_TenantId_IsDeleted",
                table: "Collection",
                columns: new[] { "Creator", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Collection_EmbeddingModel",
                table: "Collection",
                column: "EmbeddingModel");

            migrationBuilder.CreateIndex(
                name: "IX_Collection_IsAutomaticUpdate",
                table: "Collection",
                column: "IsAutomaticUpdate");

            migrationBuilder.CreateIndex(
                name: "IX_Collection_Name",
                table: "Collection",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Collection_Status",
                table: "Collection",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Collection_Status_TenantId_IsDeleted",
                table: "Collection",
                columns: new[] { "Status", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_Collection_TenantId_IsDeleted",
                table: "Collection",
                columns: new[] { "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_ChatHistory_CollectionId",
                table: "ChatHistory",
                column: "CollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_ChatHistory_CollectionId_CreationTime_TenantId_IsDeleted",
                table: "ChatHistory",
                columns: new[] { "CollectionId", "CreationTime", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_ChatHistory_CreationTime_TenantId_IsDeleted",
                table: "ChatHistory",
                columns: new[] { "CreationTime", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_ChatHistory_Creator_TenantId_IsDeleted",
                table: "ChatHistory",
                columns: new[] { "Creator", "TenantId", "IsDeleted" });

            migrationBuilder.CreateIndex(
                name: "IX_ChatHistory_Question",
                table: "ChatHistory",
                column: "Question");

            migrationBuilder.CreateIndex(
                name: "IX_ChatHistory_TenantId_IsDeleted",
                table: "ChatHistory",
                columns: new[] { "TenantId", "IsDeleted" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SysUsersInGroup_GroupId",
                table: "SysUsersInGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysUsersInGroup_GroupId_TenantId_IsDeleted",
                table: "SysUsersInGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysUsersInGroup_TenantId_IsDeleted",
                table: "SysUsersInGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysUsersInGroup_UserId",
                table: "SysUsersInGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysUsersInGroup_UserId_GroupId",
                table: "SysUsersInGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysUsersInGroup_UserId_TenantId_IsDeleted",
                table: "SysUsersInGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_CreationTime_TenantId_IsDeleted",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_Creator_TenantId_IsDeleted",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_Department",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_FavCollecitonId",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_Mail",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_Status",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_TenantId_IsDeleted",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysUser_UserNTAccount",
                table: "SysUser");

            migrationBuilder.DropIndex(
                name: "IX_SysGroup_CreationTime_TenantId_IsDeleted",
                table: "SysGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysGroup_Creator_TenantId_IsDeleted",
                table: "SysGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysGroup_Name",
                table: "SysGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysGroup_Size",
                table: "SysGroup");

            migrationBuilder.DropIndex(
                name: "IX_SysGroup_TenantId_IsDeleted",
                table: "SysGroup");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_CollectionId",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_CollectionId_IsEmbedding",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_CollectionId_PageId",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_CollectionId_TenantId_IsDeleted",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_IsEmbedding",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_PageId",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_PageId_TenantId_IsDeleted",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_PagesInCollection_TenantId_IsDeleted",
                table: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_Page_CreationTime_TenantId_IsDeleted",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Page_Creator_TenantId_IsDeleted",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Page_IsIncludeChild",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Page_TenantId_IsDeleted",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Page_Title",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Page_Url",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_ContentsInPage_ContentId",
                table: "ContentsInPage");

            migrationBuilder.DropIndex(
                name: "IX_ContentsInPage_ContentId_TenantId_IsDeleted",
                table: "ContentsInPage");

            migrationBuilder.DropIndex(
                name: "IX_ContentsInPage_PageId",
                table: "ContentsInPage");

            migrationBuilder.DropIndex(
                name: "IX_ContentsInPage_PageId_ContentId",
                table: "ContentsInPage");

            migrationBuilder.DropIndex(
                name: "IX_ContentsInPage_PageId_TenantId_IsDeleted",
                table: "ContentsInPage");

            migrationBuilder.DropIndex(
                name: "IX_ContentsInPage_TenantId_IsDeleted",
                table: "ContentsInPage");

            migrationBuilder.DropIndex(
                name: "IX_Content_CreationTime_TenantId_IsDeleted",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Content_Creator_TenantId_IsDeleted",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Content_EmbeddingVersionNo",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Content_SourceModificationTime",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Content_TenantId_IsDeleted",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Content_Title",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Content_VersionNo",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Content_VersionNo_TenantId_IsDeleted",
                table: "Content");

            migrationBuilder.DropIndex(
                name: "IX_Collection_CreationTime_TenantId_IsDeleted",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_Collection_Creator_TenantId_IsDeleted",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_Collection_EmbeddingModel",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_Collection_IsAutomaticUpdate",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_Collection_Name",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_Collection_Status",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_Collection_Status_TenantId_IsDeleted",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_Collection_TenantId_IsDeleted",
                table: "Collection");

            migrationBuilder.DropIndex(
                name: "IX_ChatHistory_CollectionId",
                table: "ChatHistory");

            migrationBuilder.DropIndex(
                name: "IX_ChatHistory_CollectionId_CreationTime_TenantId_IsDeleted",
                table: "ChatHistory");

            migrationBuilder.DropIndex(
                name: "IX_ChatHistory_CreationTime_TenantId_IsDeleted",
                table: "ChatHistory");

            migrationBuilder.DropIndex(
                name: "IX_ChatHistory_Creator_TenantId_IsDeleted",
                table: "ChatHistory");

            migrationBuilder.DropIndex(
                name: "IX_ChatHistory_Question",
                table: "ChatHistory");

            migrationBuilder.DropIndex(
                name: "IX_ChatHistory_TenantId_IsDeleted",
                table: "ChatHistory");
        }
    }
}
