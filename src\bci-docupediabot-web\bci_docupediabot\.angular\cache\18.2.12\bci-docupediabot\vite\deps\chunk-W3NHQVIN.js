import {
  MDCRipple
} from "./chunk-CR563APR.js";
import {
  MDCComponent,
  MDCFoundation,
  __assign,
  __extends,
  __values,
  closest,
  matches
} from "./chunk-C3UT3JH2.js";

// node_modules/@bci-web-core/web-components/dist/esm/component-d38f720e.js
function createFocusTrapInstance(surfaceEl, focusTrapFactory, initialFocusEl) {
  return focusTrapFactory(surfaceEl, {
    initialFocusEl
  });
}
function isScrollable(el) {
  return el ? el.scrollHeight > el.offsetHeight : false;
}
function isScrollAtTop(el) {
  return el ? el.scrollTop === 0 : false;
}
function isScrollAtBottom(el) {
  return el ? Math.ceil(el.scrollHeight - el.scrollTop) === el.clientHeight : false;
}
function areTopsMisaligned(els) {
  var tops = /* @__PURE__ */ new Set();
  [].forEach.call(els, function(el) {
    return tops.add(el.offsetTop);
  });
  return tops.size > 1;
}
var FOCUS_SENTINEL_CLASS = "mdc-dom-focus-sentinel";
var FocusTrap = (
  /** @class */
  function() {
    function FocusTrap2(root, options) {
      if (options === void 0) {
        options = {};
      }
      this.root = root;
      this.options = options;
      this.elFocusedBeforeTrapFocus = null;
    }
    FocusTrap2.prototype.trapFocus = function() {
      var focusableEls = this.getFocusableElements(this.root);
      if (focusableEls.length === 0) {
        throw new Error("FocusTrap: Element must have at least one focusable child.");
      }
      this.elFocusedBeforeTrapFocus = document.activeElement instanceof HTMLElement ? document.activeElement : null;
      this.wrapTabFocus(this.root);
      if (!this.options.skipInitialFocus) {
        this.focusInitialElement(focusableEls, this.options.initialFocusEl);
      }
    };
    FocusTrap2.prototype.releaseFocus = function() {
      [].slice.call(this.root.querySelectorAll("." + FOCUS_SENTINEL_CLASS)).forEach(function(sentinelEl) {
        sentinelEl.parentElement.removeChild(sentinelEl);
      });
      if (!this.options.skipRestoreFocus && this.elFocusedBeforeTrapFocus) {
        this.elFocusedBeforeTrapFocus.focus();
      }
    };
    FocusTrap2.prototype.wrapTabFocus = function(el) {
      var _this = this;
      var sentinelStart = this.createSentinel();
      var sentinelEnd = this.createSentinel();
      sentinelStart.addEventListener("focus", function() {
        var focusableEls = _this.getFocusableElements(el);
        if (focusableEls.length > 0) {
          focusableEls[focusableEls.length - 1].focus();
        }
      });
      sentinelEnd.addEventListener("focus", function() {
        var focusableEls = _this.getFocusableElements(el);
        if (focusableEls.length > 0) {
          focusableEls[0].focus();
        }
      });
      el.insertBefore(sentinelStart, el.children[0]);
      el.appendChild(sentinelEnd);
    };
    FocusTrap2.prototype.focusInitialElement = function(focusableEls, initialFocusEl) {
      var focusIndex = 0;
      if (initialFocusEl) {
        focusIndex = Math.max(focusableEls.indexOf(initialFocusEl), 0);
      }
      focusableEls[focusIndex].focus();
    };
    FocusTrap2.prototype.getFocusableElements = function(root) {
      var focusableEls = [].slice.call(root.querySelectorAll("[autofocus], [tabindex], a, input, textarea, select, button"));
      return focusableEls.filter(function(el) {
        var isDisabledOrHidden = el.getAttribute("aria-disabled") === "true" || el.getAttribute("disabled") != null || el.getAttribute("hidden") != null || el.getAttribute("aria-hidden") === "true";
        var isTabbableAndVisible = el.tabIndex >= 0 && el.getBoundingClientRect().width > 0 && !el.classList.contains(FOCUS_SENTINEL_CLASS) && !isDisabledOrHidden;
        var isProgrammaticallyHidden = false;
        if (isTabbableAndVisible) {
          var style = getComputedStyle(el);
          isProgrammaticallyHidden = style.display === "none" || style.visibility === "hidden";
        }
        return isTabbableAndVisible && !isProgrammaticallyHidden;
      });
    };
    FocusTrap2.prototype.createSentinel = function() {
      var sentinel = document.createElement("div");
      sentinel.setAttribute("tabindex", "0");
      sentinel.setAttribute("aria-hidden", "true");
      sentinel.classList.add(FOCUS_SENTINEL_CLASS);
      return sentinel;
    };
    return FocusTrap2;
  }()
);
var AnimationFrame = (
  /** @class */
  function() {
    function AnimationFrame2() {
      this.rafIDs = /* @__PURE__ */ new Map();
    }
    AnimationFrame2.prototype.request = function(key, callback) {
      var _this = this;
      this.cancel(key);
      var frameID = requestAnimationFrame(function(frame) {
        _this.rafIDs.delete(key);
        callback(frame);
      });
      this.rafIDs.set(key, frameID);
    };
    AnimationFrame2.prototype.cancel = function(key) {
      var rafID = this.rafIDs.get(key);
      if (rafID) {
        cancelAnimationFrame(rafID);
        this.rafIDs.delete(key);
      }
    };
    AnimationFrame2.prototype.cancelAll = function() {
      var _this = this;
      this.rafIDs.forEach(function(_, key) {
        _this.cancel(key);
      });
    };
    AnimationFrame2.prototype.getQueue = function() {
      var queue = [];
      this.rafIDs.forEach(function(_, key) {
        queue.push(key);
      });
      return queue;
    };
    return AnimationFrame2;
  }()
);
var cssClasses = {
  CLOSING: "mdc-dialog--closing",
  OPEN: "mdc-dialog--open",
  OPENING: "mdc-dialog--opening",
  SCROLLABLE: "mdc-dialog--scrollable",
  SCROLL_LOCK: "mdc-dialog-scroll-lock",
  STACKED: "mdc-dialog--stacked",
  FULLSCREEN: "mdc-dialog--fullscreen",
  // Class for showing a scroll divider on full-screen dialog header element.
  // Should only be displayed on scrollable content, when the dialog content is
  // scrolled "underneath" the header.
  SCROLL_DIVIDER_HEADER: "mdc-dialog-scroll-divider-header",
  // Class for showing a scroll divider on a full-screen dialog footer element.
  // Should only be displayed on scrolalble content, when the dialog content is
  // obscured "underneath" the footer.
  SCROLL_DIVIDER_FOOTER: "mdc-dialog-scroll-divider-footer",
  // The "surface scrim" is a scrim covering only the surface of a dialog. This
  // is used in situations where a confirmation dialog is shown over an already
  // opened full-screen dialog. On larger screen-sizes, the full-screen dialog
  // is sized as a modal and so in these situations we display a "surface scrim"
  // to prevent a "double scrim" (where the scrim from the secondary
  // confirmation dialog would overlap with the scrim from the full-screen
  // dialog).
  SURFACE_SCRIM_SHOWN: "mdc-dialog__surface-scrim--shown",
  // "Showing" animating class for the surface-scrim.
  SURFACE_SCRIM_SHOWING: "mdc-dialog__surface-scrim--showing",
  // "Hiding" animating class for the surface-scrim.
  SURFACE_SCRIM_HIDING: "mdc-dialog__surface-scrim--hiding",
  // Class to hide a dialog's scrim (used in conjunction with a surface-scrim).
  // Note that we only hide the original scrim rather than removing it entirely
  // to prevent interactions with the content behind this scrim, and to capture
  // scrim clicks.
  SCRIM_HIDDEN: "mdc-dialog__scrim--hidden"
};
var strings$1 = {
  ACTION_ATTRIBUTE: "data-mdc-dialog-action",
  BUTTON_DEFAULT_ATTRIBUTE: "data-mdc-dialog-button-default",
  BUTTON_SELECTOR: ".mdc-dialog__button",
  CLOSED_EVENT: "MDCDialog:closed",
  CLOSE_ACTION: "close",
  CLOSING_EVENT: "MDCDialog:closing",
  CONTAINER_SELECTOR: ".mdc-dialog__container",
  CONTENT_SELECTOR: ".mdc-dialog__content",
  DESTROY_ACTION: "destroy",
  INITIAL_FOCUS_ATTRIBUTE: "data-mdc-dialog-initial-focus",
  OPENED_EVENT: "MDCDialog:opened",
  OPENING_EVENT: "MDCDialog:opening",
  SCRIM_SELECTOR: ".mdc-dialog__scrim",
  SUPPRESS_DEFAULT_PRESS_SELECTOR: ["textarea", ".mdc-menu .mdc-list-item", ".mdc-menu .mdc-deprecated-list-item"].join(", "),
  SURFACE_SELECTOR: ".mdc-dialog__surface"
};
var numbers = {
  DIALOG_ANIMATION_CLOSE_TIME_MS: 75,
  DIALOG_ANIMATION_OPEN_TIME_MS: 150
};
var AnimationKeys;
(function(AnimationKeys2) {
  AnimationKeys2["POLL_SCROLL_POS"] = "poll_scroll_position";
  AnimationKeys2["POLL_LAYOUT_CHANGE"] = "poll_layout_change";
})(AnimationKeys || (AnimationKeys = {}));
var MDCDialogFoundation = (
  /** @class */
  function(_super) {
    __extends(MDCDialogFoundation2, _super);
    function MDCDialogFoundation2(adapter) {
      var _this = _super.call(this, __assign(__assign({}, MDCDialogFoundation2.defaultAdapter), adapter)) || this;
      _this.dialogOpen = false;
      _this.isFullscreen = false;
      _this.animationFrame = 0;
      _this.animationTimer = 0;
      _this.escapeKeyAction = strings$1.CLOSE_ACTION;
      _this.scrimClickAction = strings$1.CLOSE_ACTION;
      _this.autoStackButtons = true;
      _this.areButtonsStacked = false;
      _this.suppressDefaultPressSelector = strings$1.SUPPRESS_DEFAULT_PRESS_SELECTOR;
      _this.animFrame = new AnimationFrame();
      _this.contentScrollHandler = function() {
        _this.handleScrollEvent();
      };
      _this.windowResizeHandler = function() {
        _this.layout();
      };
      _this.windowOrientationChangeHandler = function() {
        _this.layout();
      };
      return _this;
    }
    Object.defineProperty(MDCDialogFoundation2, "cssClasses", {
      get: function() {
        return cssClasses;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCDialogFoundation2, "strings", {
      get: function() {
        return strings$1;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCDialogFoundation2, "numbers", {
      get: function() {
        return numbers;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCDialogFoundation2, "defaultAdapter", {
      get: function() {
        return {
          addBodyClass: function() {
            return void 0;
          },
          addClass: function() {
            return void 0;
          },
          areButtonsStacked: function() {
            return false;
          },
          clickDefaultButton: function() {
            return void 0;
          },
          eventTargetMatches: function() {
            return false;
          },
          getActionFromEvent: function() {
            return "";
          },
          getInitialFocusEl: function() {
            return null;
          },
          hasClass: function() {
            return false;
          },
          isContentScrollable: function() {
            return false;
          },
          notifyClosed: function() {
            return void 0;
          },
          notifyClosing: function() {
            return void 0;
          },
          notifyOpened: function() {
            return void 0;
          },
          notifyOpening: function() {
            return void 0;
          },
          releaseFocus: function() {
            return void 0;
          },
          removeBodyClass: function() {
            return void 0;
          },
          removeClass: function() {
            return void 0;
          },
          reverseButtons: function() {
            return void 0;
          },
          trapFocus: function() {
            return void 0;
          },
          registerContentEventHandler: function() {
            return void 0;
          },
          deregisterContentEventHandler: function() {
            return void 0;
          },
          isScrollableContentAtTop: function() {
            return false;
          },
          isScrollableContentAtBottom: function() {
            return false;
          },
          registerWindowEventHandler: function() {
            return void 0;
          },
          deregisterWindowEventHandler: function() {
            return void 0;
          }
        };
      },
      enumerable: false,
      configurable: true
    });
    MDCDialogFoundation2.prototype.init = function() {
      if (this.adapter.hasClass(cssClasses.STACKED)) {
        this.setAutoStackButtons(false);
      }
      this.isFullscreen = this.adapter.hasClass(cssClasses.FULLSCREEN);
    };
    MDCDialogFoundation2.prototype.destroy = function() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer);
        this.handleAnimationTimerEnd();
      }
      if (this.isFullscreen) {
        this.adapter.deregisterContentEventHandler("scroll", this.contentScrollHandler);
      }
      this.animFrame.cancelAll();
      this.adapter.deregisterWindowEventHandler("resize", this.windowResizeHandler);
      this.adapter.deregisterWindowEventHandler("orientationchange", this.windowOrientationChangeHandler);
    };
    MDCDialogFoundation2.prototype.open = function(dialogOptions) {
      var _this = this;
      this.dialogOpen = true;
      this.adapter.notifyOpening();
      this.adapter.addClass(cssClasses.OPENING);
      if (this.isFullscreen) {
        this.adapter.registerContentEventHandler("scroll", this.contentScrollHandler);
      }
      if (dialogOptions && dialogOptions.isAboveFullscreenDialog) {
        this.adapter.addClass(cssClasses.SCRIM_HIDDEN);
      }
      this.adapter.registerWindowEventHandler("resize", this.windowResizeHandler);
      this.adapter.registerWindowEventHandler("orientationchange", this.windowOrientationChangeHandler);
      this.runNextAnimationFrame(function() {
        _this.adapter.addClass(cssClasses.OPEN);
        _this.adapter.addBodyClass(cssClasses.SCROLL_LOCK);
        _this.layout();
        _this.animationTimer = setTimeout(function() {
          _this.handleAnimationTimerEnd();
          _this.adapter.trapFocus(_this.adapter.getInitialFocusEl());
          _this.adapter.notifyOpened();
        }, numbers.DIALOG_ANIMATION_OPEN_TIME_MS);
      });
    };
    MDCDialogFoundation2.prototype.close = function(action) {
      var _this = this;
      if (action === void 0) {
        action = "";
      }
      if (!this.dialogOpen) {
        return;
      }
      this.dialogOpen = false;
      this.adapter.notifyClosing(action);
      this.adapter.addClass(cssClasses.CLOSING);
      this.adapter.removeClass(cssClasses.OPEN);
      this.adapter.removeBodyClass(cssClasses.SCROLL_LOCK);
      if (this.isFullscreen) {
        this.adapter.deregisterContentEventHandler("scroll", this.contentScrollHandler);
      }
      this.adapter.deregisterWindowEventHandler("resize", this.windowResizeHandler);
      this.adapter.deregisterWindowEventHandler("orientationchange", this.windowOrientationChangeHandler);
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = 0;
      clearTimeout(this.animationTimer);
      this.animationTimer = setTimeout(function() {
        _this.adapter.releaseFocus();
        _this.handleAnimationTimerEnd();
        _this.adapter.notifyClosed(action);
      }, numbers.DIALOG_ANIMATION_CLOSE_TIME_MS);
    };
    MDCDialogFoundation2.prototype.showSurfaceScrim = function() {
      var _this = this;
      this.adapter.addClass(cssClasses.SURFACE_SCRIM_SHOWING);
      this.runNextAnimationFrame(function() {
        _this.adapter.addClass(cssClasses.SURFACE_SCRIM_SHOWN);
      });
    };
    MDCDialogFoundation2.prototype.hideSurfaceScrim = function() {
      this.adapter.removeClass(cssClasses.SURFACE_SCRIM_SHOWN);
      this.adapter.addClass(cssClasses.SURFACE_SCRIM_HIDING);
    };
    MDCDialogFoundation2.prototype.handleSurfaceScrimTransitionEnd = function() {
      this.adapter.removeClass(cssClasses.SURFACE_SCRIM_HIDING);
      this.adapter.removeClass(cssClasses.SURFACE_SCRIM_SHOWING);
    };
    MDCDialogFoundation2.prototype.isOpen = function() {
      return this.dialogOpen;
    };
    MDCDialogFoundation2.prototype.getEscapeKeyAction = function() {
      return this.escapeKeyAction;
    };
    MDCDialogFoundation2.prototype.setEscapeKeyAction = function(action) {
      this.escapeKeyAction = action;
    };
    MDCDialogFoundation2.prototype.getScrimClickAction = function() {
      return this.scrimClickAction;
    };
    MDCDialogFoundation2.prototype.setScrimClickAction = function(action) {
      this.scrimClickAction = action;
    };
    MDCDialogFoundation2.prototype.getAutoStackButtons = function() {
      return this.autoStackButtons;
    };
    MDCDialogFoundation2.prototype.setAutoStackButtons = function(autoStack) {
      this.autoStackButtons = autoStack;
    };
    MDCDialogFoundation2.prototype.getSuppressDefaultPressSelector = function() {
      return this.suppressDefaultPressSelector;
    };
    MDCDialogFoundation2.prototype.setSuppressDefaultPressSelector = function(selector) {
      this.suppressDefaultPressSelector = selector;
    };
    MDCDialogFoundation2.prototype.layout = function() {
      var _this = this;
      this.animFrame.request(AnimationKeys.POLL_LAYOUT_CHANGE, function() {
        _this.layoutInternal();
      });
    };
    MDCDialogFoundation2.prototype.handleClick = function(evt) {
      var isScrim = this.adapter.eventTargetMatches(evt.target, strings$1.SCRIM_SELECTOR);
      if (isScrim && this.scrimClickAction !== "") {
        this.close(this.scrimClickAction);
      } else {
        var action = this.adapter.getActionFromEvent(evt);
        if (action) {
          this.close(action);
        }
      }
    };
    MDCDialogFoundation2.prototype.handleKeydown = function(evt) {
      var isEnter = evt.key === "Enter" || evt.keyCode === 13;
      if (!isEnter) {
        return;
      }
      var action = this.adapter.getActionFromEvent(evt);
      if (action) {
        return;
      }
      var target = evt.composedPath ? evt.composedPath()[0] : evt.target;
      var isDefault = this.suppressDefaultPressSelector ? !this.adapter.eventTargetMatches(target, this.suppressDefaultPressSelector) : true;
      if (isEnter && isDefault) {
        this.adapter.clickDefaultButton();
      }
    };
    MDCDialogFoundation2.prototype.handleDocumentKeydown = function(evt) {
      var isEscape = evt.key === "Escape" || evt.keyCode === 27;
      if (isEscape && this.escapeKeyAction !== "") {
        this.close(this.escapeKeyAction);
      }
    };
    MDCDialogFoundation2.prototype.handleScrollEvent = function() {
      var _this = this;
      this.animFrame.request(AnimationKeys.POLL_SCROLL_POS, function() {
        _this.toggleScrollDividerHeader();
        _this.toggleScrollDividerFooter();
      });
    };
    MDCDialogFoundation2.prototype.layoutInternal = function() {
      if (this.autoStackButtons) {
        this.detectStackedButtons();
      }
      this.toggleScrollableClasses();
    };
    MDCDialogFoundation2.prototype.handleAnimationTimerEnd = function() {
      this.animationTimer = 0;
      this.adapter.removeClass(cssClasses.OPENING);
      this.adapter.removeClass(cssClasses.CLOSING);
    };
    MDCDialogFoundation2.prototype.runNextAnimationFrame = function(callback) {
      var _this = this;
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = requestAnimationFrame(function() {
        _this.animationFrame = 0;
        clearTimeout(_this.animationTimer);
        _this.animationTimer = setTimeout(callback, 0);
      });
    };
    MDCDialogFoundation2.prototype.detectStackedButtons = function() {
      this.adapter.removeClass(cssClasses.STACKED);
      var areButtonsStacked = this.adapter.areButtonsStacked();
      if (areButtonsStacked) {
        this.adapter.addClass(cssClasses.STACKED);
      }
      if (areButtonsStacked !== this.areButtonsStacked) {
        this.adapter.reverseButtons();
        this.areButtonsStacked = areButtonsStacked;
      }
    };
    MDCDialogFoundation2.prototype.toggleScrollableClasses = function() {
      this.adapter.removeClass(cssClasses.SCROLLABLE);
      if (this.adapter.isContentScrollable()) {
        this.adapter.addClass(cssClasses.SCROLLABLE);
        if (this.isFullscreen) {
          this.toggleScrollDividerHeader();
          this.toggleScrollDividerFooter();
        }
      }
    };
    MDCDialogFoundation2.prototype.toggleScrollDividerHeader = function() {
      if (!this.adapter.isScrollableContentAtTop()) {
        this.adapter.addClass(cssClasses.SCROLL_DIVIDER_HEADER);
      } else if (this.adapter.hasClass(cssClasses.SCROLL_DIVIDER_HEADER)) {
        this.adapter.removeClass(cssClasses.SCROLL_DIVIDER_HEADER);
      }
    };
    MDCDialogFoundation2.prototype.toggleScrollDividerFooter = function() {
      if (!this.adapter.isScrollableContentAtBottom()) {
        this.adapter.addClass(cssClasses.SCROLL_DIVIDER_FOOTER);
      } else if (this.adapter.hasClass(cssClasses.SCROLL_DIVIDER_FOOTER)) {
        this.adapter.removeClass(cssClasses.SCROLL_DIVIDER_FOOTER);
      }
    };
    return MDCDialogFoundation2;
  }(MDCFoundation)
);
var strings = MDCDialogFoundation.strings;
var MDCDialog = (
  /** @class */
  function(_super) {
    __extends(MDCDialog2, _super);
    function MDCDialog2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    Object.defineProperty(MDCDialog2.prototype, "isOpen", {
      get: function() {
        return this.foundation.isOpen();
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCDialog2.prototype, "escapeKeyAction", {
      get: function() {
        return this.foundation.getEscapeKeyAction();
      },
      set: function(action) {
        this.foundation.setEscapeKeyAction(action);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCDialog2.prototype, "scrimClickAction", {
      get: function() {
        return this.foundation.getScrimClickAction();
      },
      set: function(action) {
        this.foundation.setScrimClickAction(action);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCDialog2.prototype, "autoStackButtons", {
      get: function() {
        return this.foundation.getAutoStackButtons();
      },
      set: function(autoStack) {
        this.foundation.setAutoStackButtons(autoStack);
      },
      enumerable: false,
      configurable: true
    });
    MDCDialog2.attachTo = function(root) {
      return new MDCDialog2(root);
    };
    MDCDialog2.prototype.initialize = function(focusTrapFactory) {
      var e_1, _a;
      if (focusTrapFactory === void 0) {
        focusTrapFactory = function(el, focusOptions) {
          return new FocusTrap(el, focusOptions);
        };
      }
      var container = this.root.querySelector(strings.CONTAINER_SELECTOR);
      if (!container) {
        throw new Error("Dialog component requires a " + strings.CONTAINER_SELECTOR + " container element");
      }
      this.container = container;
      this.content = this.root.querySelector(strings.CONTENT_SELECTOR);
      this.buttons = [].slice.call(this.root.querySelectorAll(strings.BUTTON_SELECTOR));
      this.defaultButton = this.root.querySelector("[" + strings.BUTTON_DEFAULT_ATTRIBUTE + "]");
      this.focusTrapFactory = focusTrapFactory;
      this.buttonRipples = [];
      try {
        for (var _b = __values(this.buttons), _c = _b.next(); !_c.done; _c = _b.next()) {
          var buttonEl = _c.value;
          this.buttonRipples.push(new MDCRipple(buttonEl));
        }
      } catch (e_1_1) {
        e_1 = {
          error: e_1_1
        };
      } finally {
        try {
          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
        } finally {
          if (e_1) throw e_1.error;
        }
      }
    };
    MDCDialog2.prototype.initialSyncWithDOM = function() {
      var _this = this;
      this.focusTrap = createFocusTrapInstance(this.container, this.focusTrapFactory, this.getInitialFocusEl() || void 0);
      this.handleClick = this.foundation.handleClick.bind(this.foundation);
      this.handleKeydown = this.foundation.handleKeydown.bind(this.foundation);
      this.handleDocumentKeydown = this.foundation.handleDocumentKeydown.bind(this.foundation);
      this.handleOpening = function() {
        document.addEventListener("keydown", _this.handleDocumentKeydown);
      };
      this.handleClosing = function() {
        document.removeEventListener("keydown", _this.handleDocumentKeydown);
      };
      this.listen("click", this.handleClick);
      this.listen("keydown", this.handleKeydown);
      this.listen(strings.OPENING_EVENT, this.handleOpening);
      this.listen(strings.CLOSING_EVENT, this.handleClosing);
    };
    MDCDialog2.prototype.destroy = function() {
      this.unlisten("click", this.handleClick);
      this.unlisten("keydown", this.handleKeydown);
      this.unlisten(strings.OPENING_EVENT, this.handleOpening);
      this.unlisten(strings.CLOSING_EVENT, this.handleClosing);
      this.handleClosing();
      this.buttonRipples.forEach(function(ripple) {
        ripple.destroy();
      });
      _super.prototype.destroy.call(this);
    };
    MDCDialog2.prototype.layout = function() {
      this.foundation.layout();
    };
    MDCDialog2.prototype.open = function() {
      this.foundation.open();
    };
    MDCDialog2.prototype.close = function(action) {
      if (action === void 0) {
        action = "";
      }
      this.foundation.close(action);
    };
    MDCDialog2.prototype.getDefaultFoundation = function() {
      var _this = this;
      var adapter = {
        addBodyClass: function(className) {
          return document.body.classList.add(className);
        },
        addClass: function(className) {
          return _this.root.classList.add(className);
        },
        areButtonsStacked: function() {
          return areTopsMisaligned(_this.buttons);
        },
        clickDefaultButton: function() {
          if (_this.defaultButton && !_this.defaultButton.disabled) {
            _this.defaultButton.click();
          }
        },
        eventTargetMatches: function(target, selector) {
          return target ? matches(target, selector) : false;
        },
        getActionFromEvent: function(evt) {
          if (!evt.target) {
            return "";
          }
          var element = closest(evt.target, "[" + strings.ACTION_ATTRIBUTE + "]");
          return element && element.getAttribute(strings.ACTION_ATTRIBUTE);
        },
        getInitialFocusEl: function() {
          return _this.getInitialFocusEl();
        },
        hasClass: function(className) {
          return _this.root.classList.contains(className);
        },
        isContentScrollable: function() {
          return isScrollable(_this.content);
        },
        notifyClosed: function(action) {
          return _this.emit(strings.CLOSED_EVENT, action ? {
            action
          } : {});
        },
        notifyClosing: function(action) {
          return _this.emit(strings.CLOSING_EVENT, action ? {
            action
          } : {});
        },
        notifyOpened: function() {
          return _this.emit(strings.OPENED_EVENT, {});
        },
        notifyOpening: function() {
          return _this.emit(strings.OPENING_EVENT, {});
        },
        releaseFocus: function() {
          _this.focusTrap.releaseFocus();
        },
        removeBodyClass: function(className) {
          return document.body.classList.remove(className);
        },
        removeClass: function(className) {
          return _this.root.classList.remove(className);
        },
        reverseButtons: function() {
          _this.buttons.reverse();
          _this.buttons.forEach(function(button) {
            button.parentElement.appendChild(button);
          });
        },
        trapFocus: function() {
          _this.focusTrap.trapFocus();
        },
        registerContentEventHandler: function(evt, handler) {
          if (_this.content instanceof HTMLElement) {
            _this.content.addEventListener(evt, handler);
          }
        },
        deregisterContentEventHandler: function(evt, handler) {
          if (_this.content instanceof HTMLElement) {
            _this.content.removeEventListener(evt, handler);
          }
        },
        isScrollableContentAtTop: function() {
          return isScrollAtTop(_this.content);
        },
        isScrollableContentAtBottom: function() {
          return isScrollAtBottom(_this.content);
        },
        registerWindowEventHandler: function(evt, handler) {
          window.addEventListener(evt, handler);
        },
        deregisterWindowEventHandler: function(evt, handler) {
          window.removeEventListener(evt, handler);
        }
      };
      return new MDCDialogFoundation(adapter);
    };
    MDCDialog2.prototype.getInitialFocusEl = function() {
      return this.root.querySelector("[" + strings.INITIAL_FOCUS_ATTRIBUTE + "]");
    };
    return MDCDialog2;
  }(MDCComponent)
);

export {
  MDCDialog
};
/*! Bundled license information:

@bci-web-core/web-components/dist/esm/component-d38f720e.js:
  (**
   * @license
   * Copyright 2016 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   *)
  (**
   * @license
   * Copyright 2020 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   *)
  (**
   * @license
   * Copyright 2017 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   *)
*/
//# sourceMappingURL=chunk-W3NHQVIN.js.map
