using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Domain.IRepositories
{
  public interface ICollectionsInGroupRepository : IEntityRepository<CollectionsInGroup>
  {
    Task<List<Guid>> GetGroupIdsByCollectionIdAsync(Guid collectionId);
    Task<List<Guid>> GetCollectionIdsByGroupIdAsync(Guid groupId);
    Task<List<Guid>> GetCollectionIdsByGroupIdsAsync(IEnumerable<Guid> groupIds);
    Task<bool> ExistsMappingAsync(Guid collectionId, Guid groupId);
    Task DeleteByCollectionIdAsync(Guid collectionId);
    Task DeleteByGroupIdAsync(Guid groupId);
    Task DeleteMappingAsync(Guid collectionId, Guid groupId);
    Task<Dictionary<Guid, List<Guid>>> GetGroupIdsByCollectionIdsBatchAsync(IEnumerable<Guid> collectionIds);
  }
}
