﻿using System.Diagnostics.CodeAnalysis;

namespace BCI.DocupediaBot.Infrastructure.Configuration
{
  [ExcludeFromCodeCoverage]
  public class OidcConfiguration
  {
    public string ServiceUrl { get; set; }
    public string ClientId { get; set; }
    public string ApplicationName { get; set; }
    public string TenantId { get; set; }
    public string PublicClientId
    {
      get => !string.IsNullOrEmpty(ClientId) ? $"{ClientId}-frontend" : null;
    }

    public string ServiceScope
    {
      get => !string.IsNullOrEmpty(ClientId) ? $"aud:{ClientId}" : null;
    }

    public Endpoints Endpoints { get; set; }
  }

  [ExcludeFromCodeCoverage]
  public class Endpoints
  {
    public string Usage { get; set; }
    public string DiscoveryEndpoint { get; set; }
    public string IntrospectionEndpoint { get; set; }
    public string UserInfoEndpoint { get; set; }
  }
}
