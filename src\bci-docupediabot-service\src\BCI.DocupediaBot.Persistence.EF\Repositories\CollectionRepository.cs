﻿using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using BCI.DocupediaBot.Infrastructure.Repositories;

namespace BCI.DocupediaBot.Persistence.EF.Repositories
{
  public class CollectionRepository : EntityRepository<Collection>, ICollectionRepository
	{
    public CollectionRepository(DocupediaBotDbContext dbContext) : base(dbContext)
    {
    }
  }
}
