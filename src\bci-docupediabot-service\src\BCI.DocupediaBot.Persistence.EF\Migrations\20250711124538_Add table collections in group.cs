﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{
    /// <inheritdoc />
    public partial class Addtablecollectionsingroup : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CollectionsInGroups",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    GroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    CollectionId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Creator = table.Column<string>(type: "text", nullable: false),
                    ModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Modifier = table.Column<string>(type: "text", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CollectionsInGroups", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Content_SourceId_VersionNo_EmbeddingVersionNo",
                table: "Content",
                columns: new[] { "SourceId", "VersionNo", "EmbeddingVersionNo" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CollectionsInGroups");

            migrationBuilder.DropIndex(
                name: "IX_Content_SourceId_VersionNo_EmbeddingVersionNo",
                table: "Content");
        }
    }
}
