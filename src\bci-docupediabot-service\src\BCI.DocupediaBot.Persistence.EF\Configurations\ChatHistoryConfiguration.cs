﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class ChatHistoryConfiguration : IEntityTypeConfiguration<ChatHistory>
  {
    public void Configure(EntityTypeBuilder<ChatHistory> entityBuilder)
    {
      entityBuilder.ToTable(nameof(ChatHistory));


      entityBuilder.HasIndex(x => x.CollectionId);
      entityBuilder.HasIndex(x => x.Question);


      entityBuilder.HasIndex(x => new { x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.Creator, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.CollectionId, x.CreationTime, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.CreationTime, x.TenantId, x.Is<PERSON>ele<PERSON> });
		}
	}
}
