﻿namespace BCI.DocupediaBot.Domain.Dtos.SharedDto
{
	public class ApiResponse<T>
	{
		public bool Success { get; set; }
		public T Data { get; set; }
		public string Message { get; set; }

		public ApiResponse(T data, string message = null)
		{
			Success = true;
			Data = data;
			Message = message ?? "Operation completed successfully.";
		}

		public ApiResponse(string errorMessage)
		{
			Success = false;
			Data = default;
			Message = errorMessage;
		}

		public static ApiResponse<T> Ok(T data, string message = null) => new ApiResponse<T>(data, message);
		public static ApiResponse<T> Error(string errorMessage) => new ApiResponse<T>(errorMessage);
	}
}