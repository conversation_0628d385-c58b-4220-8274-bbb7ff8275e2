using System;
using System.Text.Json.Serialization;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Confluence
{
    /// <summary>
    /// Represents basic page information from Confluence API
    /// </summary>
    public class ConfluencePageInfo
    {
        public string PageId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
    }

    /// <summary>
    /// Represents a single page result from Confluence API
    /// </summary>
    public class ConfluencePageResult
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("type")]
        public string Type { get; set; } = string.Empty;

        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// Represents search results from Confluence API
    /// </summary>
    public class ConfluenceSearchResult
    {
        [JsonPropertyName("results")]
        public ConfluencePageResult[] Results { get; set; } = Array.Empty<ConfluencePageResult>();

        [JsonPropertyName("start")]
        public int Start { get; set; }

        [JsonPropertyName("limit")]
        public int Limit { get; set; }

        [JsonPropertyName("size")]
        public int Size { get; set; }
    }

    /// <summary>
    /// Represents detailed page information from Confluence API
    /// </summary>
    public class ConfluencePageDetail
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("title")]
        public string Title { get; set; } = string.Empty;

        [JsonPropertyName("_links")]
        public ConfluenceLinks? Links { get; set; }
    }

    /// <summary>
    /// Represents links section from Confluence API response
    /// </summary>
    public class ConfluenceLinks
    {
        [JsonPropertyName("tinyui")]
        public string? TinyUi { get; set; }

        [JsonPropertyName("webui")]
        public string? WebUi { get; set; }

        [JsonPropertyName("self")]
        public string? Self { get; set; }
    }
}
