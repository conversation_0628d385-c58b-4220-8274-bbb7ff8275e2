﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Collection;
using BCI.DocupediaBot.Application.Contracts.Dtos.Embedding;
using BCI.DocupediaBot.Application.Services.Collection;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Infrastructure.Constants;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.UIService.Controllers
{
  [Authorize]
  [Route("api/collections")]
  [ApiController]
  public class CollectionsController : ControllerBase
  {
    private readonly ICollectionService _collectionService;
    private readonly IPageService _pageService;
    private readonly ILogger<CollectionsController> _logger;

    public CollectionsController(
        ICollectionService collectionService,
        IPageService pageService,
        ILogger<CollectionsController> logger)
    {
      _collectionService = collectionService ?? throw new ArgumentNullException(nameof(collectionService));
      _pageService = pageService ?? throw new ArgumentNullException(nameof(pageService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    [HttpPost]
    public async Task<IActionResult> CreateAsync([FromBody] CollectionAddDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("Invalid collection creation request: {Message}", ErrorMessages.CollectionDataEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.CollectionDataEmpty));
      }

      try
      {
        _logger.LogInformation("Creating collection with data: {@Dto}", dto);
        ResponseResult result = await _collectionService.AddCollectionAsync(dto);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to create collection: {@Dto}", dto);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }

    [HttpDelete("{collectionId}")]
    public async Task<IActionResult> DeleteAsync(Guid collectionId)
    {
      if (collectionId == Guid.Empty)
      {
        _logger.LogWarning("Invalid collection deletion request: {Message}", ErrorMessages.CollectionIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.CollectionIdEmpty));
      }

      try
      {
        _logger.LogInformation("Deleting collection: {CollectionId}", collectionId);
        ResponseResult result = await _collectionService.DeleteCollectionByCollectionIdAsync(collectionId);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete collection: {CollectionId}", collectionId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }

    [HttpGet]
    public async Task<IActionResult> GetAllAsync()
    {
      try
      {
        _logger.LogInformation("Querying all collections");
        var result = await _collectionService.QueryCollectionsAsync();
        return Ok(ApiResponse<List<CollectionResponseDTO>>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query collections");
        return StatusCode(500, ApiResponse<string>.Error(ErrorMessages.InternalServerError));
      }
    }

    [HttpPut("{collectionId}")]
    public async Task<IActionResult> UpdateAsync(Guid collectionId, [FromBody] CollectionUpdateDTO dto)
    {
      if (collectionId == Guid.Empty || dto == null)
      {
        _logger.LogWarning("Invalid collection update request: {Message}", ErrorMessages.CollectionIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.CollectionIdEmpty));
      }

      try
      {
        _logger.LogInformation("Updating collection: {CollectionId}", collectionId);
        ResponseResult result = await _collectionService.UpdateCollectionAsync(dto);
        return Ok(ApiResponse<ResponseResult>.Ok(result));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update collection: {CollectionId}", collectionId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }

    [HttpPost("{collectionId}/contents/update")]
    public async Task<IActionResult> UpdateContentsAsync(Guid collectionId)
    {
      if (collectionId == Guid.Empty)
      {
        _logger.LogWarning("Invalid content update request: {Message}", ErrorMessages.CollectionIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.CollectionIdEmpty));
      }

      try
      {
        _logger.LogInformation("Updating contents for collection: {CollectionId}", collectionId);
        var result = await _collectionService.QueryCollectionWithPagesAsync(collectionId);
        foreach (var page in result.Pages)
        {
          await _pageService.UpdateContentAsync(page, collectionId);
        }
        return Ok(ApiResponse<ResponseResult>.Ok(new ResponseResult { IsSuccess = true, Msg = "Contents updated successfully" }));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update contents for collection: {CollectionId}", collectionId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }

    [HttpPost("{collectionId}/contents/embed")]
    public async Task<IActionResult> EmbedContentsAsync(Guid collectionId, [FromBody] EmbeddingModelRequestDTO request)
    {
      if (collectionId == Guid.Empty)
      {
        _logger.LogWarning("Invalid content embed request: {Message}", ErrorMessages.CollectionIdEmpty);
        return BadRequest(ApiResponse<ResponseResult>.Error(ErrorMessages.CollectionIdEmpty));
      }

      try
      {
        _logger.LogInformation("Embedding contents for collection: {CollectionId} with embedding model: {EmbeddingModel}", collectionId, request.EmbeddingModel);
        var result = await _collectionService.QueryCollectionWithPagesAsync(collectionId);
        foreach (var page in result.Pages)
        {
          await _pageService.EmbedContentAsync(page, collectionId, request.EmbeddingModel);
        }
        return Ok(ApiResponse<ResponseResult>.Ok(new ResponseResult { IsSuccess = true, Msg = "Contents embedded successfully" }));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to embed contents for collection: {CollectionId}", collectionId);
        return StatusCode(500, ApiResponse<ResponseResult>.Error(ErrorMessages.InternalServerError));
      }
    }
  }
}