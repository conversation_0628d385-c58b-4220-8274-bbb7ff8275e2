﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup Condition="'$(MySQL)' == 'True' ">
		<PackageReference Include="MySqlConnector" Version="xxx" />
	</ItemGroup>

	<ItemGroup Condition="'$(PgSQL)' == 'True' ">
		<PackageReference Include="Npgsql" Version="xxx" />
	</ItemGroup>

	<ItemGroup Condition="'$(SQLite)' == 'True' ">
		<PackageReference Include="Microsoft.Data.Sqlite" Version="xxx" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.8" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BCI.DocupediaBot.Application.Contracts\BCI.DocupediaBot.Application.Contracts.csproj" />
		<ProjectReference Include="..\BCI.DocupediaBot.Domain\BCI.DocupediaBot.Domain.csproj" />
		<ProjectReference Include="..\BCI.DocupediaBot.Infrastructure\BCI.DocupediaBot.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Migrations\" />
	</ItemGroup>
</Project>
