﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.VectorDb
{
  public interface IVectorDbService
  {
    public Task ProcessAndStoreTextAsync(ContentResponseDTO dto, Guid collectionId, EmbeddingModel embeddingModel);
    public Task DeletePointsByContentIdAsync(Guid collectionId, Guid contentId);
    public Task<string> ProcessAndSearchTextAsync(List<string> questionList, Guid collectIonId, EmbeddingModel embeddingModel, ChatModel chatModel);
		public Task<ResponseResult> ProcessAndUpdateTextAsync(ContentResponseDTO content, Guid collectionId);
    Task<string> FindDuplicateDocumentsAsync(Guid collectionId);
  }
}
