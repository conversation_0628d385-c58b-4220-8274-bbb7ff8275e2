﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Domain.Enums;
using System;
using System.Collections.Generic;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Collection
{
  public class CollectionResponseDTO
	{
		public Guid Id { get; set; }
		public string Name { get; set; } = string.Empty;
		public string Comment { get; set; } = string.Empty;
		public EmbeddingModel EmbeddingModel { get; set; }
		public int ChunkSize { get; set; }
		public int Status { get; set; }
		public bool IsAutomaticUpdate { get; set; }
		public UpdateType? UpdateType { get; set; }
		public int? IntervalNumber { get; set; }
		public TimeOnly? UpdateTime { get; set; }
		public DateTime? ModificationTime { get; set; }
		public bool IsEmbedding { get; set; }
		public string Creator { get; set; } = string.Empty;
		public List<PageResponseDTO> Pages { get; set; } = new List<PageResponseDTO>();
	}
}