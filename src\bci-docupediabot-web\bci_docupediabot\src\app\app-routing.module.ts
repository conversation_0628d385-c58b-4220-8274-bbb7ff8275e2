import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@shared/guards/auth.guard';
import { AdminGuard } from '@shared/guards/admin.guard';
import { BrochureComponent } from '@shared/components/brochure/brochure.component';
import { BrochureEnComponent } from '@shared/components/brochure-en/brochure-en.component';
import { LoginComponent } from '@shared/components/login/login.component';

const routes: Routes = [
  {
    path: '',
    component: LoginComponent
  },
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'chatbot',
    loadChildren: () => import('./chatbot/chatbot.module').then((m) => m.ChatbotModule),
    canActivate: [AuthGuard],  // Add route guard
    canActivateChild: [AuthGuard]  // Protect child routes
  },
  {
    path: 'system',
    loadChildren: () => import('./system/system.module').then((m) => m.SystemModule),
    canActivate: [AuthGuard, AdminGuard],  // Add route guard and admin permission guard
    canActivateChild: [AuthGuard, AdminGuard]  // Protect child routes
  },
  {
    path: 'brochure',
    component: BrochureComponent,
    canActivate: [AuthGuard]  // Only require basic authentication
  },
  {
    path: 'brochure-en',
    component: BrochureEnComponent,
    canActivate: [AuthGuard]  // Only require basic authentication
  },
  {
    path: 'data-generation',
    loadChildren: () => import('./data-generation/data-generation.module').then((m) => m.DataGenerationModule),
    canActivate: [AuthGuard],  // Add route guard
    canActivateChild: [AuthGuard]  // Protect child routes
  },








  {
    path: '**',
    redirectTo: ''
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: false })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
