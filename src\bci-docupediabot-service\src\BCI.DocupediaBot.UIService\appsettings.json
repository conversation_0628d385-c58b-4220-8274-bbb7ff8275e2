{"DatabaseProvider": "pgsql", "ConnectionStrings": {"PgSql": "Host=*************;Database=DocupediaBot;Username=postgres;Password=***********"}, "ConnectionString": "Encrypt=True;TrustServerCertificate=True;Persist Security Info = False; User ID =sa; Password =Password@2021; Initial Catalog =WidgetEngine; Server =*************", "Logging": {"LogLevel": {"Default": "Trace", "Microsoft.AspNetCore": "Information", "Microsoft.Hosting.Lifetime": "Information", "Elastic.Apm": "Information"}}, "AllowedHosts": "*", "ElasticApm": {"Enabled": false}, "OIDC": {"ServiceUrl": "", "RequireHttps": true, "ClientId": "", "ClientSecret": "", "Acl": {"RefreshInterval": "60"}, "Introspection": {"EnableCaching": true, "CachingDuration": 30}, "NamedHttpClients": {"DefaultTokenEndpoint": "", "Clients": {"MacmaClient": {"Scopes": ["aud:macma"]}, "PortalClient": {"DefaultTokenEndpoint": "", "Scopes": [""]}}}, "Endpoints": {"Usage": "ConfigurationFirst", "DiscoveryEndpoint": "auth/realms/{tenantId}", "IntrospectionEndpoint": "access-management/v1/openid-connect/introspect", "UserInfoEndpoint": "access-management/v1/openid-connect/userinfo"}}, "TENANTCONFIG": {"Tenant0": "7311ea8c-5d48-43fe-acf9-980eedf24b6c"}, "Portal": {"TenantId": "", "InitialDelayInSeconds": 30, "Url": "", "Info": {"Id": "", "Name": "<replace with module name>", "Vendor": "BCI", "Version": "0.0.1", "BaseUrl": "", "SupportedLanguages": ["de", "en", "zh"], "AuthProviderClientId": ""}, "Views": {"TopLevelEntries": [{"Title": "<replace with the name of the first level menu>", "NavigationPriority": 0, "NavigationPath": ["<replace with navigation path>"], "Icon": "bosch-ic-connectivity-it", "ResourceId": "<replace with the resource of the first level menu>", "ResourceType": "view", "Localization": {"Title": {"de": "<replace with the title in German language>", "en": "<replace with the title in English language>", "zh": "<replace with the title in Chinese language>"}, "Tags": {"de": ["<replace with tags>", "<replace with tags>"], "en": ["<replace with tags>", "<replace with tags>"], "zh": ["<replace with tags>", "<replace with tags>"]}}}], "IFrameSubViews": [{"Title": "<replace with the name of the second level menu>", "HtmlFile": "/<replace with navigation path>?tenantId=##tenantId##", "ResourceId": "urn.com.bosch.nexeed.demo", "ResourceType": "view", "NavigationPriority": 1, "NavigationPath": ["<replace with navigation path>", "<replace with navigation path>"], "NavigationRoute": "/<replace with navigation path>", "Localization": {"Title": {"de": "<replace with the title in German language>", "en": "<replace with the title in German language>", "zh": "<replace with the title in German language>"}, "Tags": {"de": ["<replace with tags>", "<replace with tags>"], "en": ["<replace with tags>", "<replace with tags>"], "zh": ["<replace with tags>", "<replace with tags>"]}}}]}, "Docs": {"items": [{"Url": "/<replace with document name>", "Language": "en", "Title": "Backend OSS Disclosure Document", "Format": "pdf", "resourceid": "", "resourcetype": "", "Type": "disclosure-documentation"}, {"Url": "/<replace with document name>", "Language": "en", "Title": "Frontend OSS Disclosure Document", "Format": "pdf", "resourceid": "", "resourcetype": "", "Type": "disclosure-documentation"}]}, "Widgets": {"items": []}}, "AclConfigurations": {"ApplicationInfo": [{"ApplicationName": "<replace with client id>", "SystemId": "7311ea8c-5d48-43fe-acf9-980eedf24b6c"}], "Resources": [{"id": "urn.com.bosch.nexeed.demo", "displayName": "Demo", "type": "view", "description": "views.", "typesOfAccess": "read"}, {"id": "urn.com.bosch.nexeed.demo.action", "displayName": "Demo actions", "type": "api", "description": "Add, modify, delete, and read the demo function.", "typesOfAccess": "read, modify, add, delete"}], "Roles": [{"Id": "Admin", "DisplayName": "Admin", "Description": "Ad<PERSON> is allowed to access everything in the widget engine.", "Permissions": [{"Resourceid": "urn.com.bosch.nexeed.demo", "Resourcetype": "view", "AccessMode": "read"}, {"Resourceid": "urn.com.bosch.nexeed.demo.action", "Resourcetype": "api", "AccessMode": "read, add, modify, delete"}]}]}, "Csp": {"DifferentframeSource": true, "FrameSources": ["<replace with module url>"]}, "SwaggerUI": {"Servers": ["<replace with module url>"], "Enabled": true}, "AlwaysTrustSSL": true, "WithoutNIAS": false, "LdapSettings": {"IsEnableLdapLogin": "true", "LdapUrl": "LDAP://*************", "LdapBURegex": ""}, "JwtTokenOptions": {"Secret": "BCI/ESW1-CN-DOCUPEDIA-BOT-SECRET", "Audience": "BCI/ESW1", "Issuer": "BCI/ESW1", "Expires": 1500, "AccessExpiration": 120, "RefreshExpiration": 1440}}