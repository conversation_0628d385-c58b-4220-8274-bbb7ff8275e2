{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/component-d38f720e.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { _ as __extends, a as __assign, M as MDCFoundation, c as __values, b as MDCComponent, m as matches, g as closest } from './ponyfill-78459bda.js';\nimport { M as MDCRipple } from './component-d69b424e.js';\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nfunction createFocusTrapInstance(surfaceEl, focusTrapFactory, initialFocusEl) {\n  return focusTrapFactory(surfaceEl, {\n    initialFocusEl: initialFocusEl\n  });\n}\nfunction isScrollable(el) {\n  return el ? el.scrollHeight > el.offsetHeight : false;\n}\n/**\n * For scrollable content, returns true if the content has not been scrolled\n * (that is, the scroll content is as the \"top\"). This is used in full-screen\n * dialogs, where the scroll divider is expected only to appear once the\n * content has been scrolled \"underneath\" the header bar.\n */\nfunction isScrollAtTop(el) {\n  return el ? el.scrollTop === 0 : false;\n}\n/**\n * For scrollable content, returns true if the content has been scrolled all the\n * way to the bottom. This is used in full-screen dialogs, where the footer\n * scroll divider is expected only to appear when the content is \"cut-off\" by\n * the footer bar.\n */\nfunction isScrollAtBottom(el) {\n  return el ? Math.ceil(el.scrollHeight - el.scrollTop) === el.clientHeight : false;\n}\nfunction areTopsMisaligned(els) {\n  var tops = new Set();\n  [].forEach.call(els, function (el) {\n    return tops.add(el.offsetTop);\n  });\n  return tops.size > 1;\n}\n\n/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar FOCUS_SENTINEL_CLASS = 'mdc-dom-focus-sentinel';\n/**\n * Utility to trap focus in a given root element, e.g. for modal components such\n * as dialogs. The root should have at least one focusable child element,\n * for setting initial focus when trapping focus.\n * Also tracks the previously focused element, and restores focus to that\n * element when releasing focus.\n */\nvar FocusTrap = /** @class */function () {\n  function FocusTrap(root, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    this.root = root;\n    this.options = options;\n    // Previously focused element before trapping focus.\n    this.elFocusedBeforeTrapFocus = null;\n  }\n  /**\n   * Traps focus in `root`. Also focuses on either `initialFocusEl` if set;\n   * otherwises sets initial focus to the first focusable child element.\n   */\n  FocusTrap.prototype.trapFocus = function () {\n    var focusableEls = this.getFocusableElements(this.root);\n    if (focusableEls.length === 0) {\n      throw new Error('FocusTrap: Element must have at least one focusable child.');\n    }\n    this.elFocusedBeforeTrapFocus = document.activeElement instanceof HTMLElement ? document.activeElement : null;\n    this.wrapTabFocus(this.root);\n    if (!this.options.skipInitialFocus) {\n      this.focusInitialElement(focusableEls, this.options.initialFocusEl);\n    }\n  };\n  /**\n   * Releases focus from `root`. Also restores focus to the previously focused\n   * element.\n   */\n  FocusTrap.prototype.releaseFocus = function () {\n    [].slice.call(this.root.querySelectorAll(\".\" + FOCUS_SENTINEL_CLASS)).forEach(function (sentinelEl) {\n      sentinelEl.parentElement.removeChild(sentinelEl);\n    });\n    if (!this.options.skipRestoreFocus && this.elFocusedBeforeTrapFocus) {\n      this.elFocusedBeforeTrapFocus.focus();\n    }\n  };\n  /**\n   * Wraps tab focus within `el` by adding two hidden sentinel divs which are\n   * used to mark the beginning and the end of the tabbable region. When\n   * focused, these sentinel elements redirect focus to the first/last\n   * children elements of the tabbable region, ensuring that focus is trapped\n   * within that region.\n   */\n  FocusTrap.prototype.wrapTabFocus = function (el) {\n    var _this = this;\n    var sentinelStart = this.createSentinel();\n    var sentinelEnd = this.createSentinel();\n    sentinelStart.addEventListener('focus', function () {\n      var focusableEls = _this.getFocusableElements(el);\n      if (focusableEls.length > 0) {\n        focusableEls[focusableEls.length - 1].focus();\n      }\n    });\n    sentinelEnd.addEventListener('focus', function () {\n      var focusableEls = _this.getFocusableElements(el);\n      if (focusableEls.length > 0) {\n        focusableEls[0].focus();\n      }\n    });\n    el.insertBefore(sentinelStart, el.children[0]);\n    el.appendChild(sentinelEnd);\n  };\n  /**\n   * Focuses on `initialFocusEl` if defined and a child of the root element.\n   * Otherwise, focuses on the first focusable child element of the root.\n   */\n  FocusTrap.prototype.focusInitialElement = function (focusableEls, initialFocusEl) {\n    var focusIndex = 0;\n    if (initialFocusEl) {\n      focusIndex = Math.max(focusableEls.indexOf(initialFocusEl), 0);\n    }\n    focusableEls[focusIndex].focus();\n  };\n  FocusTrap.prototype.getFocusableElements = function (root) {\n    var focusableEls = [].slice.call(root.querySelectorAll('[autofocus], [tabindex], a, input, textarea, select, button'));\n    return focusableEls.filter(function (el) {\n      var isDisabledOrHidden = el.getAttribute('aria-disabled') === 'true' || el.getAttribute('disabled') != null || el.getAttribute('hidden') != null || el.getAttribute('aria-hidden') === 'true';\n      var isTabbableAndVisible = el.tabIndex >= 0 && el.getBoundingClientRect().width > 0 && !el.classList.contains(FOCUS_SENTINEL_CLASS) && !isDisabledOrHidden;\n      var isProgrammaticallyHidden = false;\n      if (isTabbableAndVisible) {\n        var style = getComputedStyle(el);\n        isProgrammaticallyHidden = style.display === 'none' || style.visibility === 'hidden';\n      }\n      return isTabbableAndVisible && !isProgrammaticallyHidden;\n    });\n  };\n  FocusTrap.prototype.createSentinel = function () {\n    var sentinel = document.createElement('div');\n    sentinel.setAttribute('tabindex', '0');\n    // Don't announce in screen readers.\n    sentinel.setAttribute('aria-hidden', 'true');\n    sentinel.classList.add(FOCUS_SENTINEL_CLASS);\n    return sentinel;\n  };\n  return FocusTrap;\n}();\n\n/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * AnimationFrame provides a user-friendly abstraction around requesting\n * and canceling animation frames.\n */\nvar AnimationFrame = /** @class */function () {\n  function AnimationFrame() {\n    this.rafIDs = new Map();\n  }\n  /**\n   * Requests an animation frame. Cancels any existing frame with the same key.\n   * @param {string} key The key for this callback.\n   * @param {FrameRequestCallback} callback The callback to be executed.\n   */\n  AnimationFrame.prototype.request = function (key, callback) {\n    var _this = this;\n    this.cancel(key);\n    var frameID = requestAnimationFrame(function (frame) {\n      _this.rafIDs.delete(key);\n      // Callback must come *after* the key is deleted so that nested calls to\n      // request with the same key are not deleted.\n      callback(frame);\n    });\n    this.rafIDs.set(key, frameID);\n  };\n  /**\n   * Cancels a queued callback with the given key.\n   * @param {string} key The key for this callback.\n   */\n  AnimationFrame.prototype.cancel = function (key) {\n    var rafID = this.rafIDs.get(key);\n    if (rafID) {\n      cancelAnimationFrame(rafID);\n      this.rafIDs.delete(key);\n    }\n  };\n  /**\n   * Cancels all queued callback.\n   */\n  AnimationFrame.prototype.cancelAll = function () {\n    var _this = this;\n    // Need to use forEach because it's the only iteration method supported\n    // by IE11. Suppress the underscore because we don't need it.\n    // tslint:disable-next-line:enforce-name-casing\n    this.rafIDs.forEach(function (_, key) {\n      _this.cancel(key);\n    });\n  };\n  /**\n   * Returns the queue of unexecuted callback keys.\n   */\n  AnimationFrame.prototype.getQueue = function () {\n    var queue = [];\n    // Need to use forEach because it's the only iteration method supported\n    // by IE11. Suppress the underscore because we don't need it.\n    // tslint:disable-next-line:enforce-name-casing\n    this.rafIDs.forEach(function (_, key) {\n      queue.push(key);\n    });\n    return queue;\n  };\n  return AnimationFrame;\n}();\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses = {\n  CLOSING: 'mdc-dialog--closing',\n  OPEN: 'mdc-dialog--open',\n  OPENING: 'mdc-dialog--opening',\n  SCROLLABLE: 'mdc-dialog--scrollable',\n  SCROLL_LOCK: 'mdc-dialog-scroll-lock',\n  STACKED: 'mdc-dialog--stacked',\n  FULLSCREEN: 'mdc-dialog--fullscreen',\n  // Class for showing a scroll divider on full-screen dialog header element.\n  // Should only be displayed on scrollable content, when the dialog content is\n  // scrolled \"underneath\" the header.\n  SCROLL_DIVIDER_HEADER: 'mdc-dialog-scroll-divider-header',\n  // Class for showing a scroll divider on a full-screen dialog footer element.\n  // Should only be displayed on scrolalble content, when the dialog content is\n  // obscured \"underneath\" the footer.\n  SCROLL_DIVIDER_FOOTER: 'mdc-dialog-scroll-divider-footer',\n  // The \"surface scrim\" is a scrim covering only the surface of a dialog. This\n  // is used in situations where a confirmation dialog is shown over an already\n  // opened full-screen dialog. On larger screen-sizes, the full-screen dialog\n  // is sized as a modal and so in these situations we display a \"surface scrim\"\n  // to prevent a \"double scrim\" (where the scrim from the secondary\n  // confirmation dialog would overlap with the scrim from the full-screen\n  // dialog).\n  SURFACE_SCRIM_SHOWN: 'mdc-dialog__surface-scrim--shown',\n  // \"Showing\" animating class for the surface-scrim.\n  SURFACE_SCRIM_SHOWING: 'mdc-dialog__surface-scrim--showing',\n  // \"Hiding\" animating class for the surface-scrim.\n  SURFACE_SCRIM_HIDING: 'mdc-dialog__surface-scrim--hiding',\n  // Class to hide a dialog's scrim (used in conjunction with a surface-scrim).\n  // Note that we only hide the original scrim rather than removing it entirely\n  // to prevent interactions with the content behind this scrim, and to capture\n  // scrim clicks.\n  SCRIM_HIDDEN: 'mdc-dialog__scrim--hidden'\n};\nvar strings$1 = {\n  ACTION_ATTRIBUTE: 'data-mdc-dialog-action',\n  BUTTON_DEFAULT_ATTRIBUTE: 'data-mdc-dialog-button-default',\n  BUTTON_SELECTOR: '.mdc-dialog__button',\n  CLOSED_EVENT: 'MDCDialog:closed',\n  CLOSE_ACTION: 'close',\n  CLOSING_EVENT: 'MDCDialog:closing',\n  CONTAINER_SELECTOR: '.mdc-dialog__container',\n  CONTENT_SELECTOR: '.mdc-dialog__content',\n  DESTROY_ACTION: 'destroy',\n  INITIAL_FOCUS_ATTRIBUTE: 'data-mdc-dialog-initial-focus',\n  OPENED_EVENT: 'MDCDialog:opened',\n  OPENING_EVENT: 'MDCDialog:opening',\n  SCRIM_SELECTOR: '.mdc-dialog__scrim',\n  SUPPRESS_DEFAULT_PRESS_SELECTOR: ['textarea', '.mdc-menu .mdc-list-item', '.mdc-menu .mdc-deprecated-list-item'].join(', '),\n  SURFACE_SELECTOR: '.mdc-dialog__surface'\n};\nvar numbers = {\n  DIALOG_ANIMATION_CLOSE_TIME_MS: 75,\n  DIALOG_ANIMATION_OPEN_TIME_MS: 150\n};\n\n/**\n * @license\n * Copyright 2017 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar AnimationKeys;\n(function (AnimationKeys) {\n  AnimationKeys[\"POLL_SCROLL_POS\"] = \"poll_scroll_position\";\n  AnimationKeys[\"POLL_LAYOUT_CHANGE\"] = \"poll_layout_change\";\n})(AnimationKeys || (AnimationKeys = {}));\nvar MDCDialogFoundation = /** @class */function (_super) {\n  __extends(MDCDialogFoundation, _super);\n  function MDCDialogFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCDialogFoundation.defaultAdapter), adapter)) || this;\n    _this.dialogOpen = false;\n    _this.isFullscreen = false;\n    _this.animationFrame = 0;\n    _this.animationTimer = 0;\n    _this.escapeKeyAction = strings$1.CLOSE_ACTION;\n    _this.scrimClickAction = strings$1.CLOSE_ACTION;\n    _this.autoStackButtons = true;\n    _this.areButtonsStacked = false;\n    _this.suppressDefaultPressSelector = strings$1.SUPPRESS_DEFAULT_PRESS_SELECTOR;\n    _this.animFrame = new AnimationFrame();\n    _this.contentScrollHandler = function () {\n      _this.handleScrollEvent();\n    };\n    _this.windowResizeHandler = function () {\n      _this.layout();\n    };\n    _this.windowOrientationChangeHandler = function () {\n      _this.layout();\n    };\n    return _this;\n  }\n  Object.defineProperty(MDCDialogFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCDialogFoundation, \"strings\", {\n    get: function () {\n      return strings$1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCDialogFoundation, \"numbers\", {\n    get: function () {\n      return numbers;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCDialogFoundation, \"defaultAdapter\", {\n    get: function () {\n      return {\n        addBodyClass: function () {\n          return undefined;\n        },\n        addClass: function () {\n          return undefined;\n        },\n        areButtonsStacked: function () {\n          return false;\n        },\n        clickDefaultButton: function () {\n          return undefined;\n        },\n        eventTargetMatches: function () {\n          return false;\n        },\n        getActionFromEvent: function () {\n          return '';\n        },\n        getInitialFocusEl: function () {\n          return null;\n        },\n        hasClass: function () {\n          return false;\n        },\n        isContentScrollable: function () {\n          return false;\n        },\n        notifyClosed: function () {\n          return undefined;\n        },\n        notifyClosing: function () {\n          return undefined;\n        },\n        notifyOpened: function () {\n          return undefined;\n        },\n        notifyOpening: function () {\n          return undefined;\n        },\n        releaseFocus: function () {\n          return undefined;\n        },\n        removeBodyClass: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        reverseButtons: function () {\n          return undefined;\n        },\n        trapFocus: function () {\n          return undefined;\n        },\n        registerContentEventHandler: function () {\n          return undefined;\n        },\n        deregisterContentEventHandler: function () {\n          return undefined;\n        },\n        isScrollableContentAtTop: function () {\n          return false;\n        },\n        isScrollableContentAtBottom: function () {\n          return false;\n        },\n        registerWindowEventHandler: function () {\n          return undefined;\n        },\n        deregisterWindowEventHandler: function () {\n          return undefined;\n        }\n      };\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCDialogFoundation.prototype.init = function () {\n    if (this.adapter.hasClass(cssClasses.STACKED)) {\n      this.setAutoStackButtons(false);\n    }\n    this.isFullscreen = this.adapter.hasClass(cssClasses.FULLSCREEN);\n  };\n  MDCDialogFoundation.prototype.destroy = function () {\n    if (this.animationTimer) {\n      clearTimeout(this.animationTimer);\n      this.handleAnimationTimerEnd();\n    }\n    if (this.isFullscreen) {\n      this.adapter.deregisterContentEventHandler('scroll', this.contentScrollHandler);\n    }\n    this.animFrame.cancelAll();\n    this.adapter.deregisterWindowEventHandler('resize', this.windowResizeHandler);\n    this.adapter.deregisterWindowEventHandler('orientationchange', this.windowOrientationChangeHandler);\n  };\n  MDCDialogFoundation.prototype.open = function (dialogOptions) {\n    var _this = this;\n    this.dialogOpen = true;\n    this.adapter.notifyOpening();\n    this.adapter.addClass(cssClasses.OPENING);\n    if (this.isFullscreen) {\n      // A scroll event listener is registered even if the dialog is not\n      // scrollable on open, since the window resize event, or orientation\n      // change may make the dialog scrollable after it is opened.\n      this.adapter.registerContentEventHandler('scroll', this.contentScrollHandler);\n    }\n    if (dialogOptions && dialogOptions.isAboveFullscreenDialog) {\n      this.adapter.addClass(cssClasses.SCRIM_HIDDEN);\n    }\n    this.adapter.registerWindowEventHandler('resize', this.windowResizeHandler);\n    this.adapter.registerWindowEventHandler('orientationchange', this.windowOrientationChangeHandler);\n    // Wait a frame once display is no longer \"none\", to establish basis for\n    // animation\n    this.runNextAnimationFrame(function () {\n      _this.adapter.addClass(cssClasses.OPEN);\n      _this.adapter.addBodyClass(cssClasses.SCROLL_LOCK);\n      _this.layout();\n      _this.animationTimer = setTimeout(function () {\n        _this.handleAnimationTimerEnd();\n        _this.adapter.trapFocus(_this.adapter.getInitialFocusEl());\n        _this.adapter.notifyOpened();\n      }, numbers.DIALOG_ANIMATION_OPEN_TIME_MS);\n    });\n  };\n  MDCDialogFoundation.prototype.close = function (action) {\n    var _this = this;\n    if (action === void 0) {\n      action = '';\n    }\n    if (!this.dialogOpen) {\n      // Avoid redundant close calls (and events), e.g. from keydown on elements\n      // that inherently emit click\n      return;\n    }\n    this.dialogOpen = false;\n    this.adapter.notifyClosing(action);\n    this.adapter.addClass(cssClasses.CLOSING);\n    this.adapter.removeClass(cssClasses.OPEN);\n    this.adapter.removeBodyClass(cssClasses.SCROLL_LOCK);\n    if (this.isFullscreen) {\n      this.adapter.deregisterContentEventHandler('scroll', this.contentScrollHandler);\n    }\n    this.adapter.deregisterWindowEventHandler('resize', this.windowResizeHandler);\n    this.adapter.deregisterWindowEventHandler('orientationchange', this.windowOrientationChangeHandler);\n    cancelAnimationFrame(this.animationFrame);\n    this.animationFrame = 0;\n    clearTimeout(this.animationTimer);\n    this.animationTimer = setTimeout(function () {\n      _this.adapter.releaseFocus();\n      _this.handleAnimationTimerEnd();\n      _this.adapter.notifyClosed(action);\n    }, numbers.DIALOG_ANIMATION_CLOSE_TIME_MS);\n  };\n  /**\n   * Used only in instances of showing a secondary dialog over a full-screen\n   * dialog. Shows the \"surface scrim\" displayed over the full-screen dialog.\n   */\n  MDCDialogFoundation.prototype.showSurfaceScrim = function () {\n    var _this = this;\n    this.adapter.addClass(cssClasses.SURFACE_SCRIM_SHOWING);\n    this.runNextAnimationFrame(function () {\n      _this.adapter.addClass(cssClasses.SURFACE_SCRIM_SHOWN);\n    });\n  };\n  /**\n   * Used only in instances of showing a secondary dialog over a full-screen\n   * dialog. Hides the \"surface scrim\" displayed over the full-screen dialog.\n   */\n  MDCDialogFoundation.prototype.hideSurfaceScrim = function () {\n    this.adapter.removeClass(cssClasses.SURFACE_SCRIM_SHOWN);\n    this.adapter.addClass(cssClasses.SURFACE_SCRIM_HIDING);\n  };\n  /**\n   * Handles `transitionend` event triggered when surface scrim animation is\n   * finished.\n   */\n  MDCDialogFoundation.prototype.handleSurfaceScrimTransitionEnd = function () {\n    this.adapter.removeClass(cssClasses.SURFACE_SCRIM_HIDING);\n    this.adapter.removeClass(cssClasses.SURFACE_SCRIM_SHOWING);\n  };\n  MDCDialogFoundation.prototype.isOpen = function () {\n    return this.dialogOpen;\n  };\n  MDCDialogFoundation.prototype.getEscapeKeyAction = function () {\n    return this.escapeKeyAction;\n  };\n  MDCDialogFoundation.prototype.setEscapeKeyAction = function (action) {\n    this.escapeKeyAction = action;\n  };\n  MDCDialogFoundation.prototype.getScrimClickAction = function () {\n    return this.scrimClickAction;\n  };\n  MDCDialogFoundation.prototype.setScrimClickAction = function (action) {\n    this.scrimClickAction = action;\n  };\n  MDCDialogFoundation.prototype.getAutoStackButtons = function () {\n    return this.autoStackButtons;\n  };\n  MDCDialogFoundation.prototype.setAutoStackButtons = function (autoStack) {\n    this.autoStackButtons = autoStack;\n  };\n  MDCDialogFoundation.prototype.getSuppressDefaultPressSelector = function () {\n    return this.suppressDefaultPressSelector;\n  };\n  MDCDialogFoundation.prototype.setSuppressDefaultPressSelector = function (selector) {\n    this.suppressDefaultPressSelector = selector;\n  };\n  MDCDialogFoundation.prototype.layout = function () {\n    var _this = this;\n    this.animFrame.request(AnimationKeys.POLL_LAYOUT_CHANGE, function () {\n      _this.layoutInternal();\n    });\n  };\n  /** Handles click on the dialog root element. */\n  MDCDialogFoundation.prototype.handleClick = function (evt) {\n    var isScrim = this.adapter.eventTargetMatches(evt.target, strings$1.SCRIM_SELECTOR);\n    // Check for scrim click first since it doesn't require querying ancestors.\n    if (isScrim && this.scrimClickAction !== '') {\n      this.close(this.scrimClickAction);\n    } else {\n      var action = this.adapter.getActionFromEvent(evt);\n      if (action) {\n        this.close(action);\n      }\n    }\n  };\n  /** Handles keydown on the dialog root element. */\n  MDCDialogFoundation.prototype.handleKeydown = function (evt) {\n    var isEnter = evt.key === 'Enter' || evt.keyCode === 13;\n    if (!isEnter) {\n      return;\n    }\n    var action = this.adapter.getActionFromEvent(evt);\n    if (action) {\n      // Action button callback is handled in `handleClick`,\n      // since space/enter keydowns on buttons trigger click events.\n      return;\n    }\n    // `composedPath` is used here, when available, to account for use cases\n    // where a target meant to suppress the default press behaviour\n    // may exist in a shadow root.\n    // For example, a textarea inside a web component:\n    // <mwc-dialog>\n    //   <horizontal-layout>\n    //     #shadow-root (open)\n    //       <mwc-textarea>\n    //         #shadow-root (open)\n    //           <textarea></textarea>\n    //       </mwc-textarea>\n    //   </horizontal-layout>\n    // </mwc-dialog>\n    var target = evt.composedPath ? evt.composedPath()[0] : evt.target;\n    var isDefault = this.suppressDefaultPressSelector ? !this.adapter.eventTargetMatches(target, this.suppressDefaultPressSelector) : true;\n    if (isEnter && isDefault) {\n      this.adapter.clickDefaultButton();\n    }\n  };\n  /** Handles keydown on the document. */\n  MDCDialogFoundation.prototype.handleDocumentKeydown = function (evt) {\n    var isEscape = evt.key === 'Escape' || evt.keyCode === 27;\n    if (isEscape && this.escapeKeyAction !== '') {\n      this.close(this.escapeKeyAction);\n    }\n  };\n  /**\n   * Handles scroll event on the dialog's content element -- showing a scroll\n   * divider on the header or footer based on the scroll position. This handler\n   * should only be registered on full-screen dialogs with scrollable content.\n   */\n  MDCDialogFoundation.prototype.handleScrollEvent = function () {\n    var _this = this;\n    // Since scroll events can fire at a high rate, we throttle these events by\n    // using requestAnimationFrame.\n    this.animFrame.request(AnimationKeys.POLL_SCROLL_POS, function () {\n      _this.toggleScrollDividerHeader();\n      _this.toggleScrollDividerFooter();\n    });\n  };\n  MDCDialogFoundation.prototype.layoutInternal = function () {\n    if (this.autoStackButtons) {\n      this.detectStackedButtons();\n    }\n    this.toggleScrollableClasses();\n  };\n  MDCDialogFoundation.prototype.handleAnimationTimerEnd = function () {\n    this.animationTimer = 0;\n    this.adapter.removeClass(cssClasses.OPENING);\n    this.adapter.removeClass(cssClasses.CLOSING);\n  };\n  /**\n   * Runs the given logic on the next animation frame, using setTimeout to\n   * factor in Firefox reflow behavior.\n   */\n  MDCDialogFoundation.prototype.runNextAnimationFrame = function (callback) {\n    var _this = this;\n    cancelAnimationFrame(this.animationFrame);\n    this.animationFrame = requestAnimationFrame(function () {\n      _this.animationFrame = 0;\n      clearTimeout(_this.animationTimer);\n      _this.animationTimer = setTimeout(callback, 0);\n    });\n  };\n  MDCDialogFoundation.prototype.detectStackedButtons = function () {\n    // Remove the class first to let us measure the buttons' natural positions.\n    this.adapter.removeClass(cssClasses.STACKED);\n    var areButtonsStacked = this.adapter.areButtonsStacked();\n    if (areButtonsStacked) {\n      this.adapter.addClass(cssClasses.STACKED);\n    }\n    if (areButtonsStacked !== this.areButtonsStacked) {\n      this.adapter.reverseButtons();\n      this.areButtonsStacked = areButtonsStacked;\n    }\n  };\n  MDCDialogFoundation.prototype.toggleScrollableClasses = function () {\n    // Remove the class first to let us measure the natural height of the\n    // content.\n    this.adapter.removeClass(cssClasses.SCROLLABLE);\n    if (this.adapter.isContentScrollable()) {\n      this.adapter.addClass(cssClasses.SCROLLABLE);\n      if (this.isFullscreen) {\n        // If dialog is full-screen and scrollable, check if a scroll divider\n        // should be shown.\n        this.toggleScrollDividerHeader();\n        this.toggleScrollDividerFooter();\n      }\n    }\n  };\n  MDCDialogFoundation.prototype.toggleScrollDividerHeader = function () {\n    if (!this.adapter.isScrollableContentAtTop()) {\n      this.adapter.addClass(cssClasses.SCROLL_DIVIDER_HEADER);\n    } else if (this.adapter.hasClass(cssClasses.SCROLL_DIVIDER_HEADER)) {\n      this.adapter.removeClass(cssClasses.SCROLL_DIVIDER_HEADER);\n    }\n  };\n  MDCDialogFoundation.prototype.toggleScrollDividerFooter = function () {\n    if (!this.adapter.isScrollableContentAtBottom()) {\n      this.adapter.addClass(cssClasses.SCROLL_DIVIDER_FOOTER);\n    } else if (this.adapter.hasClass(cssClasses.SCROLL_DIVIDER_FOOTER)) {\n      this.adapter.removeClass(cssClasses.SCROLL_DIVIDER_FOOTER);\n    }\n  };\n  return MDCDialogFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2017 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar strings = MDCDialogFoundation.strings;\nvar MDCDialog = /** @class */function (_super) {\n  __extends(MDCDialog, _super);\n  function MDCDialog() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(MDCDialog.prototype, \"isOpen\", {\n    get: function () {\n      return this.foundation.isOpen();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCDialog.prototype, \"escapeKeyAction\", {\n    get: function () {\n      return this.foundation.getEscapeKeyAction();\n    },\n    set: function (action) {\n      this.foundation.setEscapeKeyAction(action);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCDialog.prototype, \"scrimClickAction\", {\n    get: function () {\n      return this.foundation.getScrimClickAction();\n    },\n    set: function (action) {\n      this.foundation.setScrimClickAction(action);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCDialog.prototype, \"autoStackButtons\", {\n    get: function () {\n      return this.foundation.getAutoStackButtons();\n    },\n    set: function (autoStack) {\n      this.foundation.setAutoStackButtons(autoStack);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCDialog.attachTo = function (root) {\n    return new MDCDialog(root);\n  };\n  MDCDialog.prototype.initialize = function (focusTrapFactory) {\n    var e_1, _a;\n    if (focusTrapFactory === void 0) {\n      focusTrapFactory = function (el, focusOptions) {\n        return new FocusTrap(el, focusOptions);\n      };\n    }\n    var container = this.root.querySelector(strings.CONTAINER_SELECTOR);\n    if (!container) {\n      throw new Error(\"Dialog component requires a \" + strings.CONTAINER_SELECTOR + \" container element\");\n    }\n    this.container = container;\n    this.content = this.root.querySelector(strings.CONTENT_SELECTOR);\n    this.buttons = [].slice.call(this.root.querySelectorAll(strings.BUTTON_SELECTOR));\n    this.defaultButton = this.root.querySelector(\"[\" + strings.BUTTON_DEFAULT_ATTRIBUTE + \"]\");\n    this.focusTrapFactory = focusTrapFactory;\n    this.buttonRipples = [];\n    try {\n      for (var _b = __values(this.buttons), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var buttonEl = _c.value;\n        this.buttonRipples.push(new MDCRipple(buttonEl));\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  };\n  MDCDialog.prototype.initialSyncWithDOM = function () {\n    var _this = this;\n    this.focusTrap = createFocusTrapInstance(this.container, this.focusTrapFactory, this.getInitialFocusEl() || undefined);\n    this.handleClick = this.foundation.handleClick.bind(this.foundation);\n    this.handleKeydown = this.foundation.handleKeydown.bind(this.foundation);\n    this.handleDocumentKeydown = this.foundation.handleDocumentKeydown.bind(this.foundation);\n    // this.handleLayout = this.layout.bind(this);\n    this.handleOpening = function () {\n      document.addEventListener('keydown', _this.handleDocumentKeydown);\n    };\n    this.handleClosing = function () {\n      document.removeEventListener('keydown', _this.handleDocumentKeydown);\n    };\n    this.listen('click', this.handleClick);\n    this.listen('keydown', this.handleKeydown);\n    this.listen(strings.OPENING_EVENT, this.handleOpening);\n    this.listen(strings.CLOSING_EVENT, this.handleClosing);\n  };\n  MDCDialog.prototype.destroy = function () {\n    this.unlisten('click', this.handleClick);\n    this.unlisten('keydown', this.handleKeydown);\n    this.unlisten(strings.OPENING_EVENT, this.handleOpening);\n    this.unlisten(strings.CLOSING_EVENT, this.handleClosing);\n    this.handleClosing();\n    this.buttonRipples.forEach(function (ripple) {\n      ripple.destroy();\n    });\n    _super.prototype.destroy.call(this);\n  };\n  MDCDialog.prototype.layout = function () {\n    this.foundation.layout();\n  };\n  MDCDialog.prototype.open = function () {\n    this.foundation.open();\n  };\n  MDCDialog.prototype.close = function (action) {\n    if (action === void 0) {\n      action = '';\n    }\n    this.foundation.close(action);\n  };\n  MDCDialog.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    var adapter = {\n      addBodyClass: function (className) {\n        return document.body.classList.add(className);\n      },\n      addClass: function (className) {\n        return _this.root.classList.add(className);\n      },\n      areButtonsStacked: function () {\n        return areTopsMisaligned(_this.buttons);\n      },\n      clickDefaultButton: function () {\n        if (_this.defaultButton && !_this.defaultButton.disabled) {\n          _this.defaultButton.click();\n        }\n      },\n      eventTargetMatches: function (target, selector) {\n        return target ? matches(target, selector) : false;\n      },\n      getActionFromEvent: function (evt) {\n        if (!evt.target) {\n          return '';\n        }\n        var element = closest(evt.target, \"[\" + strings.ACTION_ATTRIBUTE + \"]\");\n        return element && element.getAttribute(strings.ACTION_ATTRIBUTE);\n      },\n      getInitialFocusEl: function () {\n        return _this.getInitialFocusEl();\n      },\n      hasClass: function (className) {\n        return _this.root.classList.contains(className);\n      },\n      isContentScrollable: function () {\n        return isScrollable(_this.content);\n      },\n      notifyClosed: function (action) {\n        return _this.emit(strings.CLOSED_EVENT, action ? {\n          action: action\n        } : {});\n      },\n      notifyClosing: function (action) {\n        return _this.emit(strings.CLOSING_EVENT, action ? {\n          action: action\n        } : {});\n      },\n      notifyOpened: function () {\n        return _this.emit(strings.OPENED_EVENT, {});\n      },\n      notifyOpening: function () {\n        return _this.emit(strings.OPENING_EVENT, {});\n      },\n      releaseFocus: function () {\n        _this.focusTrap.releaseFocus();\n      },\n      removeBodyClass: function (className) {\n        return document.body.classList.remove(className);\n      },\n      removeClass: function (className) {\n        return _this.root.classList.remove(className);\n      },\n      reverseButtons: function () {\n        _this.buttons.reverse();\n        _this.buttons.forEach(function (button) {\n          button.parentElement.appendChild(button);\n        });\n      },\n      trapFocus: function () {\n        _this.focusTrap.trapFocus();\n      },\n      registerContentEventHandler: function (evt, handler) {\n        if (_this.content instanceof HTMLElement) {\n          _this.content.addEventListener(evt, handler);\n        }\n      },\n      deregisterContentEventHandler: function (evt, handler) {\n        if (_this.content instanceof HTMLElement) {\n          _this.content.removeEventListener(evt, handler);\n        }\n      },\n      isScrollableContentAtTop: function () {\n        return isScrollAtTop(_this.content);\n      },\n      isScrollableContentAtBottom: function () {\n        return isScrollAtBottom(_this.content);\n      },\n      registerWindowEventHandler: function (evt, handler) {\n        window.addEventListener(evt, handler);\n      },\n      deregisterWindowEventHandler: function (evt, handler) {\n        window.removeEventListener(evt, handler);\n      }\n    };\n    return new MDCDialogFoundation(adapter);\n  };\n  MDCDialog.prototype.getInitialFocusEl = function () {\n    return this.root.querySelector(\"[\" + strings.INITIAL_FOCUS_ATTRIBUTE + \"]\");\n  };\n  return MDCDialog;\n}(MDCComponent);\nexport { MDCDialog as M };\n\n"], "mappings": ";;;;;;;;;;;;;;AA0BA,SAAS,wBAAwB,WAAW,kBAAkB,gBAAgB;AAC5E,SAAO,iBAAiB,WAAW;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,KAAK,GAAG,eAAe,GAAG,eAAe;AAClD;AAOA,SAAS,cAAc,IAAI;AACzB,SAAO,KAAK,GAAG,cAAc,IAAI;AACnC;AAOA,SAAS,iBAAiB,IAAI;AAC5B,SAAO,KAAK,KAAK,KAAK,GAAG,eAAe,GAAG,SAAS,MAAM,GAAG,eAAe;AAC9E;AACA,SAAS,kBAAkB,KAAK;AAC9B,MAAI,OAAO,oBAAI,IAAI;AACnB,GAAC,EAAE,QAAQ,KAAK,KAAK,SAAU,IAAI;AACjC,WAAO,KAAK,IAAI,GAAG,SAAS;AAAA,EAC9B,CAAC;AACD,SAAO,KAAK,OAAO;AACrB;AAwBA,IAAI,uBAAuB;AAQ3B,IAAI;AAAA;AAAA,EAAyB,WAAY;AACvC,aAASA,WAAU,MAAM,SAAS;AAChC,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,WAAK,OAAO;AACZ,WAAK,UAAU;AAEf,WAAK,2BAA2B;AAAA,IAClC;AAKA,IAAAA,WAAU,UAAU,YAAY,WAAY;AAC1C,UAAI,eAAe,KAAK,qBAAqB,KAAK,IAAI;AACtD,UAAI,aAAa,WAAW,GAAG;AAC7B,cAAM,IAAI,MAAM,4DAA4D;AAAA,MAC9E;AACA,WAAK,2BAA2B,SAAS,yBAAyB,cAAc,SAAS,gBAAgB;AACzG,WAAK,aAAa,KAAK,IAAI;AAC3B,UAAI,CAAC,KAAK,QAAQ,kBAAkB;AAClC,aAAK,oBAAoB,cAAc,KAAK,QAAQ,cAAc;AAAA,MACpE;AAAA,IACF;AAKA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC7C,OAAC,EAAE,MAAM,KAAK,KAAK,KAAK,iBAAiB,MAAM,oBAAoB,CAAC,EAAE,QAAQ,SAAU,YAAY;AAClG,mBAAW,cAAc,YAAY,UAAU;AAAA,MACjD,CAAC;AACD,UAAI,CAAC,KAAK,QAAQ,oBAAoB,KAAK,0BAA0B;AACnE,aAAK,yBAAyB,MAAM;AAAA,MACtC;AAAA,IACF;AAQA,IAAAA,WAAU,UAAU,eAAe,SAAU,IAAI;AAC/C,UAAI,QAAQ;AACZ,UAAI,gBAAgB,KAAK,eAAe;AACxC,UAAI,cAAc,KAAK,eAAe;AACtC,oBAAc,iBAAiB,SAAS,WAAY;AAClD,YAAI,eAAe,MAAM,qBAAqB,EAAE;AAChD,YAAI,aAAa,SAAS,GAAG;AAC3B,uBAAa,aAAa,SAAS,CAAC,EAAE,MAAM;AAAA,QAC9C;AAAA,MACF,CAAC;AACD,kBAAY,iBAAiB,SAAS,WAAY;AAChD,YAAI,eAAe,MAAM,qBAAqB,EAAE;AAChD,YAAI,aAAa,SAAS,GAAG;AAC3B,uBAAa,CAAC,EAAE,MAAM;AAAA,QACxB;AAAA,MACF,CAAC;AACD,SAAG,aAAa,eAAe,GAAG,SAAS,CAAC,CAAC;AAC7C,SAAG,YAAY,WAAW;AAAA,IAC5B;AAKA,IAAAA,WAAU,UAAU,sBAAsB,SAAU,cAAc,gBAAgB;AAChF,UAAI,aAAa;AACjB,UAAI,gBAAgB;AAClB,qBAAa,KAAK,IAAI,aAAa,QAAQ,cAAc,GAAG,CAAC;AAAA,MAC/D;AACA,mBAAa,UAAU,EAAE,MAAM;AAAA,IACjC;AACA,IAAAA,WAAU,UAAU,uBAAuB,SAAU,MAAM;AACzD,UAAI,eAAe,CAAC,EAAE,MAAM,KAAK,KAAK,iBAAiB,6DAA6D,CAAC;AACrH,aAAO,aAAa,OAAO,SAAU,IAAI;AACvC,YAAI,qBAAqB,GAAG,aAAa,eAAe,MAAM,UAAU,GAAG,aAAa,UAAU,KAAK,QAAQ,GAAG,aAAa,QAAQ,KAAK,QAAQ,GAAG,aAAa,aAAa,MAAM;AACvL,YAAI,uBAAuB,GAAG,YAAY,KAAK,GAAG,sBAAsB,EAAE,QAAQ,KAAK,CAAC,GAAG,UAAU,SAAS,oBAAoB,KAAK,CAAC;AACxI,YAAI,2BAA2B;AAC/B,YAAI,sBAAsB;AACxB,cAAI,QAAQ,iBAAiB,EAAE;AAC/B,qCAA2B,MAAM,YAAY,UAAU,MAAM,eAAe;AAAA,QAC9E;AACA,eAAO,wBAAwB,CAAC;AAAA,MAClC,CAAC;AAAA,IACH;AACA,IAAAA,WAAU,UAAU,iBAAiB,WAAY;AAC/C,UAAI,WAAW,SAAS,cAAc,KAAK;AAC3C,eAAS,aAAa,YAAY,GAAG;AAErC,eAAS,aAAa,eAAe,MAAM;AAC3C,eAAS,UAAU,IAAI,oBAAoB;AAC3C,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AA4BF,IAAI;AAAA;AAAA,EAA8B,WAAY;AAC5C,aAASC,kBAAiB;AACxB,WAAK,SAAS,oBAAI,IAAI;AAAA,IACxB;AAMA,IAAAA,gBAAe,UAAU,UAAU,SAAU,KAAK,UAAU;AAC1D,UAAI,QAAQ;AACZ,WAAK,OAAO,GAAG;AACf,UAAI,UAAU,sBAAsB,SAAU,OAAO;AACnD,cAAM,OAAO,OAAO,GAAG;AAGvB,iBAAS,KAAK;AAAA,MAChB,CAAC;AACD,WAAK,OAAO,IAAI,KAAK,OAAO;AAAA,IAC9B;AAKA,IAAAA,gBAAe,UAAU,SAAS,SAAU,KAAK;AAC/C,UAAI,QAAQ,KAAK,OAAO,IAAI,GAAG;AAC/B,UAAI,OAAO;AACT,6BAAqB,KAAK;AAC1B,aAAK,OAAO,OAAO,GAAG;AAAA,MACxB;AAAA,IACF;AAIA,IAAAA,gBAAe,UAAU,YAAY,WAAY;AAC/C,UAAI,QAAQ;AAIZ,WAAK,OAAO,QAAQ,SAAU,GAAG,KAAK;AACpC,cAAM,OAAO,GAAG;AAAA,MAClB,CAAC;AAAA,IACH;AAIA,IAAAA,gBAAe,UAAU,WAAW,WAAY;AAC9C,UAAI,QAAQ,CAAC;AAIb,WAAK,OAAO,QAAQ,SAAU,GAAG,KAAK;AACpC,cAAM,KAAK,GAAG;AAAA,MAChB,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAwBF,IAAI,aAAa;AAAA,EACf,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AAAA;AAAA;AAAA;AAAA,EAIZ,uBAAuB;AAAA;AAAA;AAAA;AAAA,EAIvB,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvB,qBAAqB;AAAA;AAAA,EAErB,uBAAuB;AAAA;AAAA,EAEvB,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,cAAc;AAChB;AACA,IAAI,YAAY;AAAA,EACd,kBAAkB;AAAA,EAClB,0BAA0B;AAAA,EAC1B,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,yBAAyB;AAAA,EACzB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iCAAiC,CAAC,YAAY,4BAA4B,qCAAqC,EAAE,KAAK,IAAI;AAAA,EAC1H,kBAAkB;AACpB;AACA,IAAI,UAAU;AAAA,EACZ,gCAAgC;AAAA,EAChC,+BAA+B;AACjC;AAwBA,IAAI;AAAA,CACH,SAAUC,gBAAe;AACxB,EAAAA,eAAc,iBAAiB,IAAI;AACnC,EAAAA,eAAc,oBAAoB,IAAI;AACxC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACvD,cAAUC,sBAAqB,MAAM;AACrC,aAASA,qBAAoB,SAAS;AACpC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,qBAAoB,cAAc,GAAG,OAAO,CAAC,KAAK;AACtG,YAAM,aAAa;AACnB,YAAM,eAAe;AACrB,YAAM,iBAAiB;AACvB,YAAM,iBAAiB;AACvB,YAAM,kBAAkB,UAAU;AAClC,YAAM,mBAAmB,UAAU;AACnC,YAAM,mBAAmB;AACzB,YAAM,oBAAoB;AAC1B,YAAM,+BAA+B,UAAU;AAC/C,YAAM,YAAY,IAAI,eAAe;AACrC,YAAM,uBAAuB,WAAY;AACvC,cAAM,kBAAkB;AAAA,MAC1B;AACA,YAAM,sBAAsB,WAAY;AACtC,cAAM,OAAO;AAAA,MACf;AACA,YAAM,iCAAiC,WAAY;AACjD,cAAM,OAAO;AAAA,MACf;AACA,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,sBAAqB,cAAc;AAAA,MACvD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,WAAW;AAAA,MACpD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,WAAW;AAAA,MACpD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,kBAAkB;AAAA,MAC3D,KAAK,WAAY;AACf,eAAO;AAAA,UACL,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,mBAAmB,WAAY;AAC7B,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,UACT;AAAA,UACA,mBAAmB,WAAY;AAC7B,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,eAAe,WAAY;AACzB,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,eAAe,WAAY;AACzB,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,gBAAgB,WAAY;AAC1B,mBAAO;AAAA,UACT;AAAA,UACA,WAAW,WAAY;AACrB,mBAAO;AAAA,UACT;AAAA,UACA,6BAA6B,WAAY;AACvC,mBAAO;AAAA,UACT;AAAA,UACA,+BAA+B,WAAY;AACzC,mBAAO;AAAA,UACT;AAAA,UACA,0BAA0B,WAAY;AACpC,mBAAO;AAAA,UACT;AAAA,UACA,6BAA6B,WAAY;AACvC,mBAAO;AAAA,UACT;AAAA,UACA,4BAA4B,WAAY;AACtC,mBAAO;AAAA,UACT;AAAA,UACA,8BAA8B,WAAY;AACxC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,qBAAoB,UAAU,OAAO,WAAY;AAC/C,UAAI,KAAK,QAAQ,SAAS,WAAW,OAAO,GAAG;AAC7C,aAAK,oBAAoB,KAAK;AAAA,MAChC;AACA,WAAK,eAAe,KAAK,QAAQ,SAAS,WAAW,UAAU;AAAA,IACjE;AACA,IAAAA,qBAAoB,UAAU,UAAU,WAAY;AAClD,UAAI,KAAK,gBAAgB;AACvB,qBAAa,KAAK,cAAc;AAChC,aAAK,wBAAwB;AAAA,MAC/B;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,QAAQ,8BAA8B,UAAU,KAAK,oBAAoB;AAAA,MAChF;AACA,WAAK,UAAU,UAAU;AACzB,WAAK,QAAQ,6BAA6B,UAAU,KAAK,mBAAmB;AAC5E,WAAK,QAAQ,6BAA6B,qBAAqB,KAAK,8BAA8B;AAAA,IACpG;AACA,IAAAA,qBAAoB,UAAU,OAAO,SAAU,eAAe;AAC5D,UAAI,QAAQ;AACZ,WAAK,aAAa;AAClB,WAAK,QAAQ,cAAc;AAC3B,WAAK,QAAQ,SAAS,WAAW,OAAO;AACxC,UAAI,KAAK,cAAc;AAIrB,aAAK,QAAQ,4BAA4B,UAAU,KAAK,oBAAoB;AAAA,MAC9E;AACA,UAAI,iBAAiB,cAAc,yBAAyB;AAC1D,aAAK,QAAQ,SAAS,WAAW,YAAY;AAAA,MAC/C;AACA,WAAK,QAAQ,2BAA2B,UAAU,KAAK,mBAAmB;AAC1E,WAAK,QAAQ,2BAA2B,qBAAqB,KAAK,8BAA8B;AAGhG,WAAK,sBAAsB,WAAY;AACrC,cAAM,QAAQ,SAAS,WAAW,IAAI;AACtC,cAAM,QAAQ,aAAa,WAAW,WAAW;AACjD,cAAM,OAAO;AACb,cAAM,iBAAiB,WAAW,WAAY;AAC5C,gBAAM,wBAAwB;AAC9B,gBAAM,QAAQ,UAAU,MAAM,QAAQ,kBAAkB,CAAC;AACzD,gBAAM,QAAQ,aAAa;AAAA,QAC7B,GAAG,QAAQ,6BAA6B;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,IAAAA,qBAAoB,UAAU,QAAQ,SAAU,QAAQ;AACtD,UAAI,QAAQ;AACZ,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,UAAI,CAAC,KAAK,YAAY;AAGpB;AAAA,MACF;AACA,WAAK,aAAa;AAClB,WAAK,QAAQ,cAAc,MAAM;AACjC,WAAK,QAAQ,SAAS,WAAW,OAAO;AACxC,WAAK,QAAQ,YAAY,WAAW,IAAI;AACxC,WAAK,QAAQ,gBAAgB,WAAW,WAAW;AACnD,UAAI,KAAK,cAAc;AACrB,aAAK,QAAQ,8BAA8B,UAAU,KAAK,oBAAoB;AAAA,MAChF;AACA,WAAK,QAAQ,6BAA6B,UAAU,KAAK,mBAAmB;AAC5E,WAAK,QAAQ,6BAA6B,qBAAqB,KAAK,8BAA8B;AAClG,2BAAqB,KAAK,cAAc;AACxC,WAAK,iBAAiB;AACtB,mBAAa,KAAK,cAAc;AAChC,WAAK,iBAAiB,WAAW,WAAY;AAC3C,cAAM,QAAQ,aAAa;AAC3B,cAAM,wBAAwB;AAC9B,cAAM,QAAQ,aAAa,MAAM;AAAA,MACnC,GAAG,QAAQ,8BAA8B;AAAA,IAC3C;AAKA,IAAAA,qBAAoB,UAAU,mBAAmB,WAAY;AAC3D,UAAI,QAAQ;AACZ,WAAK,QAAQ,SAAS,WAAW,qBAAqB;AACtD,WAAK,sBAAsB,WAAY;AACrC,cAAM,QAAQ,SAAS,WAAW,mBAAmB;AAAA,MACvD,CAAC;AAAA,IACH;AAKA,IAAAA,qBAAoB,UAAU,mBAAmB,WAAY;AAC3D,WAAK,QAAQ,YAAY,WAAW,mBAAmB;AACvD,WAAK,QAAQ,SAAS,WAAW,oBAAoB;AAAA,IACvD;AAKA,IAAAA,qBAAoB,UAAU,kCAAkC,WAAY;AAC1E,WAAK,QAAQ,YAAY,WAAW,oBAAoB;AACxD,WAAK,QAAQ,YAAY,WAAW,qBAAqB;AAAA,IAC3D;AACA,IAAAA,qBAAoB,UAAU,SAAS,WAAY;AACjD,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,qBAAqB,WAAY;AAC7D,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,qBAAqB,SAAU,QAAQ;AACnE,WAAK,kBAAkB;AAAA,IACzB;AACA,IAAAA,qBAAoB,UAAU,sBAAsB,WAAY;AAC9D,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,sBAAsB,SAAU,QAAQ;AACpE,WAAK,mBAAmB;AAAA,IAC1B;AACA,IAAAA,qBAAoB,UAAU,sBAAsB,WAAY;AAC9D,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,sBAAsB,SAAU,WAAW;AACvE,WAAK,mBAAmB;AAAA,IAC1B;AACA,IAAAA,qBAAoB,UAAU,kCAAkC,WAAY;AAC1E,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,kCAAkC,SAAU,UAAU;AAClF,WAAK,+BAA+B;AAAA,IACtC;AACA,IAAAA,qBAAoB,UAAU,SAAS,WAAY;AACjD,UAAI,QAAQ;AACZ,WAAK,UAAU,QAAQ,cAAc,oBAAoB,WAAY;AACnE,cAAM,eAAe;AAAA,MACvB,CAAC;AAAA,IACH;AAEA,IAAAA,qBAAoB,UAAU,cAAc,SAAU,KAAK;AACzD,UAAI,UAAU,KAAK,QAAQ,mBAAmB,IAAI,QAAQ,UAAU,cAAc;AAElF,UAAI,WAAW,KAAK,qBAAqB,IAAI;AAC3C,aAAK,MAAM,KAAK,gBAAgB;AAAA,MAClC,OAAO;AACL,YAAI,SAAS,KAAK,QAAQ,mBAAmB,GAAG;AAChD,YAAI,QAAQ;AACV,eAAK,MAAM,MAAM;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEA,IAAAA,qBAAoB,UAAU,gBAAgB,SAAU,KAAK;AAC3D,UAAI,UAAU,IAAI,QAAQ,WAAW,IAAI,YAAY;AACrD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,SAAS,KAAK,QAAQ,mBAAmB,GAAG;AAChD,UAAI,QAAQ;AAGV;AAAA,MACF;AAcA,UAAI,SAAS,IAAI,eAAe,IAAI,aAAa,EAAE,CAAC,IAAI,IAAI;AAC5D,UAAI,YAAY,KAAK,+BAA+B,CAAC,KAAK,QAAQ,mBAAmB,QAAQ,KAAK,4BAA4B,IAAI;AAClI,UAAI,WAAW,WAAW;AACxB,aAAK,QAAQ,mBAAmB;AAAA,MAClC;AAAA,IACF;AAEA,IAAAA,qBAAoB,UAAU,wBAAwB,SAAU,KAAK;AACnE,UAAI,WAAW,IAAI,QAAQ,YAAY,IAAI,YAAY;AACvD,UAAI,YAAY,KAAK,oBAAoB,IAAI;AAC3C,aAAK,MAAM,KAAK,eAAe;AAAA,MACjC;AAAA,IACF;AAMA,IAAAA,qBAAoB,UAAU,oBAAoB,WAAY;AAC5D,UAAI,QAAQ;AAGZ,WAAK,UAAU,QAAQ,cAAc,iBAAiB,WAAY;AAChE,cAAM,0BAA0B;AAChC,cAAM,0BAA0B;AAAA,MAClC,CAAC;AAAA,IACH;AACA,IAAAA,qBAAoB,UAAU,iBAAiB,WAAY;AACzD,UAAI,KAAK,kBAAkB;AACzB,aAAK,qBAAqB;AAAA,MAC5B;AACA,WAAK,wBAAwB;AAAA,IAC/B;AACA,IAAAA,qBAAoB,UAAU,0BAA0B,WAAY;AAClE,WAAK,iBAAiB;AACtB,WAAK,QAAQ,YAAY,WAAW,OAAO;AAC3C,WAAK,QAAQ,YAAY,WAAW,OAAO;AAAA,IAC7C;AAKA,IAAAA,qBAAoB,UAAU,wBAAwB,SAAU,UAAU;AACxE,UAAI,QAAQ;AACZ,2BAAqB,KAAK,cAAc;AACxC,WAAK,iBAAiB,sBAAsB,WAAY;AACtD,cAAM,iBAAiB;AACvB,qBAAa,MAAM,cAAc;AACjC,cAAM,iBAAiB,WAAW,UAAU,CAAC;AAAA,MAC/C,CAAC;AAAA,IACH;AACA,IAAAA,qBAAoB,UAAU,uBAAuB,WAAY;AAE/D,WAAK,QAAQ,YAAY,WAAW,OAAO;AAC3C,UAAI,oBAAoB,KAAK,QAAQ,kBAAkB;AACvD,UAAI,mBAAmB;AACrB,aAAK,QAAQ,SAAS,WAAW,OAAO;AAAA,MAC1C;AACA,UAAI,sBAAsB,KAAK,mBAAmB;AAChD,aAAK,QAAQ,eAAe;AAC5B,aAAK,oBAAoB;AAAA,MAC3B;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,0BAA0B,WAAY;AAGlE,WAAK,QAAQ,YAAY,WAAW,UAAU;AAC9C,UAAI,KAAK,QAAQ,oBAAoB,GAAG;AACtC,aAAK,QAAQ,SAAS,WAAW,UAAU;AAC3C,YAAI,KAAK,cAAc;AAGrB,eAAK,0BAA0B;AAC/B,eAAK,0BAA0B;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,4BAA4B,WAAY;AACpE,UAAI,CAAC,KAAK,QAAQ,yBAAyB,GAAG;AAC5C,aAAK,QAAQ,SAAS,WAAW,qBAAqB;AAAA,MACxD,WAAW,KAAK,QAAQ,SAAS,WAAW,qBAAqB,GAAG;AAClE,aAAK,QAAQ,YAAY,WAAW,qBAAqB;AAAA,MAC3D;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,4BAA4B,WAAY;AACpE,UAAI,CAAC,KAAK,QAAQ,4BAA4B,GAAG;AAC/C,aAAK,QAAQ,SAAS,WAAW,qBAAqB;AAAA,MACxD,WAAW,KAAK,QAAQ,SAAS,WAAW,qBAAqB,GAAG;AAClE,aAAK,QAAQ,YAAY,WAAW,qBAAqB;AAAA,MAC3D;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI,UAAU,oBAAoB;AAClC,IAAI;AAAA;AAAA,EAAyB,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,aAAY;AACnB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,WAAO,eAAeA,WAAU,WAAW,UAAU;AAAA,MACnD,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,OAAO;AAAA,MAChC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,mBAAmB;AAAA,MAC5D,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,mBAAmB;AAAA,MAC5C;AAAA,MACA,KAAK,SAAU,QAAQ;AACrB,aAAK,WAAW,mBAAmB,MAAM;AAAA,MAC3C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,oBAAoB;AAAA,MAC7D,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,oBAAoB;AAAA,MAC7C;AAAA,MACA,KAAK,SAAU,QAAQ;AACrB,aAAK,WAAW,oBAAoB,MAAM;AAAA,MAC5C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,oBAAoB;AAAA,MAC7D,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,oBAAoB;AAAA,MAC7C;AAAA,MACA,KAAK,SAAU,WAAW;AACxB,aAAK,WAAW,oBAAoB,SAAS;AAAA,MAC/C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,WAAU,WAAW,SAAU,MAAM;AACnC,aAAO,IAAIA,WAAU,IAAI;AAAA,IAC3B;AACA,IAAAA,WAAU,UAAU,aAAa,SAAU,kBAAkB;AAC3D,UAAI,KAAK;AACT,UAAI,qBAAqB,QAAQ;AAC/B,2BAAmB,SAAU,IAAI,cAAc;AAC7C,iBAAO,IAAI,UAAU,IAAI,YAAY;AAAA,QACvC;AAAA,MACF;AACA,UAAI,YAAY,KAAK,KAAK,cAAc,QAAQ,kBAAkB;AAClE,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,iCAAiC,QAAQ,qBAAqB,oBAAoB;AAAA,MACpG;AACA,WAAK,YAAY;AACjB,WAAK,UAAU,KAAK,KAAK,cAAc,QAAQ,gBAAgB;AAC/D,WAAK,UAAU,CAAC,EAAE,MAAM,KAAK,KAAK,KAAK,iBAAiB,QAAQ,eAAe,CAAC;AAChF,WAAK,gBAAgB,KAAK,KAAK,cAAc,MAAM,QAAQ,2BAA2B,GAAG;AACzF,WAAK,mBAAmB;AACxB,WAAK,gBAAgB,CAAC;AACtB,UAAI;AACF,iBAAS,KAAK,SAAS,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,KAAK,GAAG;AAC9E,cAAI,WAAW,GAAG;AAClB,eAAK,cAAc,KAAK,IAAI,UAAU,QAAQ,CAAC;AAAA,QACjD;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG,QAAS,IAAG,KAAK,EAAE;AAAA,QACpD,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,IAAAA,WAAU,UAAU,qBAAqB,WAAY;AACnD,UAAI,QAAQ;AACZ,WAAK,YAAY,wBAAwB,KAAK,WAAW,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,MAAS;AACrH,WAAK,cAAc,KAAK,WAAW,YAAY,KAAK,KAAK,UAAU;AACnE,WAAK,gBAAgB,KAAK,WAAW,cAAc,KAAK,KAAK,UAAU;AACvE,WAAK,wBAAwB,KAAK,WAAW,sBAAsB,KAAK,KAAK,UAAU;AAEvF,WAAK,gBAAgB,WAAY;AAC/B,iBAAS,iBAAiB,WAAW,MAAM,qBAAqB;AAAA,MAClE;AACA,WAAK,gBAAgB,WAAY;AAC/B,iBAAS,oBAAoB,WAAW,MAAM,qBAAqB;AAAA,MACrE;AACA,WAAK,OAAO,SAAS,KAAK,WAAW;AACrC,WAAK,OAAO,WAAW,KAAK,aAAa;AACzC,WAAK,OAAO,QAAQ,eAAe,KAAK,aAAa;AACrD,WAAK,OAAO,QAAQ,eAAe,KAAK,aAAa;AAAA,IACvD;AACA,IAAAA,WAAU,UAAU,UAAU,WAAY;AACxC,WAAK,SAAS,SAAS,KAAK,WAAW;AACvC,WAAK,SAAS,WAAW,KAAK,aAAa;AAC3C,WAAK,SAAS,QAAQ,eAAe,KAAK,aAAa;AACvD,WAAK,SAAS,QAAQ,eAAe,KAAK,aAAa;AACvD,WAAK,cAAc;AACnB,WAAK,cAAc,QAAQ,SAAU,QAAQ;AAC3C,eAAO,QAAQ;AAAA,MACjB,CAAC;AACD,aAAO,UAAU,QAAQ,KAAK,IAAI;AAAA,IACpC;AACA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACvC,WAAK,WAAW,OAAO;AAAA,IACzB;AACA,IAAAA,WAAU,UAAU,OAAO,WAAY;AACrC,WAAK,WAAW,KAAK;AAAA,IACvB;AACA,IAAAA,WAAU,UAAU,QAAQ,SAAU,QAAQ;AAC5C,UAAI,WAAW,QAAQ;AACrB,iBAAS;AAAA,MACX;AACA,WAAK,WAAW,MAAM,MAAM;AAAA,IAC9B;AACA,IAAAA,WAAU,UAAU,uBAAuB,WAAY;AACrD,UAAI,QAAQ;AAGZ,UAAI,UAAU;AAAA,QACZ,cAAc,SAAU,WAAW;AACjC,iBAAO,SAAS,KAAK,UAAU,IAAI,SAAS;AAAA,QAC9C;AAAA,QACA,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,mBAAmB,WAAY;AAC7B,iBAAO,kBAAkB,MAAM,OAAO;AAAA,QACxC;AAAA,QACA,oBAAoB,WAAY;AAC9B,cAAI,MAAM,iBAAiB,CAAC,MAAM,cAAc,UAAU;AACxD,kBAAM,cAAc,MAAM;AAAA,UAC5B;AAAA,QACF;AAAA,QACA,oBAAoB,SAAU,QAAQ,UAAU;AAC9C,iBAAO,SAAS,QAAQ,QAAQ,QAAQ,IAAI;AAAA,QAC9C;AAAA,QACA,oBAAoB,SAAU,KAAK;AACjC,cAAI,CAAC,IAAI,QAAQ;AACf,mBAAO;AAAA,UACT;AACA,cAAI,UAAU,QAAQ,IAAI,QAAQ,MAAM,QAAQ,mBAAmB,GAAG;AACtE,iBAAO,WAAW,QAAQ,aAAa,QAAQ,gBAAgB;AAAA,QACjE;AAAA,QACA,mBAAmB,WAAY;AAC7B,iBAAO,MAAM,kBAAkB;AAAA,QACjC;AAAA,QACA,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,SAAS,SAAS;AAAA,QAChD;AAAA,QACA,qBAAqB,WAAY;AAC/B,iBAAO,aAAa,MAAM,OAAO;AAAA,QACnC;AAAA,QACA,cAAc,SAAU,QAAQ;AAC9B,iBAAO,MAAM,KAAK,QAAQ,cAAc,SAAS;AAAA,YAC/C;AAAA,UACF,IAAI,CAAC,CAAC;AAAA,QACR;AAAA,QACA,eAAe,SAAU,QAAQ;AAC/B,iBAAO,MAAM,KAAK,QAAQ,eAAe,SAAS;AAAA,YAChD;AAAA,UACF,IAAI,CAAC,CAAC;AAAA,QACR;AAAA,QACA,cAAc,WAAY;AACxB,iBAAO,MAAM,KAAK,QAAQ,cAAc,CAAC,CAAC;AAAA,QAC5C;AAAA,QACA,eAAe,WAAY;AACzB,iBAAO,MAAM,KAAK,QAAQ,eAAe,CAAC,CAAC;AAAA,QAC7C;AAAA,QACA,cAAc,WAAY;AACxB,gBAAM,UAAU,aAAa;AAAA,QAC/B;AAAA,QACA,iBAAiB,SAAU,WAAW;AACpC,iBAAO,SAAS,KAAK,UAAU,OAAO,SAAS;AAAA,QACjD;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,iBAAO,MAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QAC9C;AAAA,QACA,gBAAgB,WAAY;AAC1B,gBAAM,QAAQ,QAAQ;AACtB,gBAAM,QAAQ,QAAQ,SAAU,QAAQ;AACtC,mBAAO,cAAc,YAAY,MAAM;AAAA,UACzC,CAAC;AAAA,QACH;AAAA,QACA,WAAW,WAAY;AACrB,gBAAM,UAAU,UAAU;AAAA,QAC5B;AAAA,QACA,6BAA6B,SAAU,KAAK,SAAS;AACnD,cAAI,MAAM,mBAAmB,aAAa;AACxC,kBAAM,QAAQ,iBAAiB,KAAK,OAAO;AAAA,UAC7C;AAAA,QACF;AAAA,QACA,+BAA+B,SAAU,KAAK,SAAS;AACrD,cAAI,MAAM,mBAAmB,aAAa;AACxC,kBAAM,QAAQ,oBAAoB,KAAK,OAAO;AAAA,UAChD;AAAA,QACF;AAAA,QACA,0BAA0B,WAAY;AACpC,iBAAO,cAAc,MAAM,OAAO;AAAA,QACpC;AAAA,QACA,6BAA6B,WAAY;AACvC,iBAAO,iBAAiB,MAAM,OAAO;AAAA,QACvC;AAAA,QACA,4BAA4B,SAAU,KAAK,SAAS;AAClD,iBAAO,iBAAiB,KAAK,OAAO;AAAA,QACtC;AAAA,QACA,8BAA8B,SAAU,KAAK,SAAS;AACpD,iBAAO,oBAAoB,KAAK,OAAO;AAAA,QACzC;AAAA,MACF;AACA,aAAO,IAAI,oBAAoB,OAAO;AAAA,IACxC;AACA,IAAAA,WAAU,UAAU,oBAAoB,WAAY;AAClD,aAAO,KAAK,KAAK,cAAc,MAAM,QAAQ,0BAA0B,GAAG;AAAA,IAC5E;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;", "names": ["FocusTrap", "AnimationFrame", "AnimationKeys", "MDCDialogFoundation", "MDCDialog"]}