﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{

    public partial class Addmaptables : Migration
    {

        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Page_CollectionId_SourceId",
                table: "Page");

            migrationBuilder.DropColumn(
                name: "CollectionId",
                table: "Page");

            migrationBuilder.DropColumn(
                name: "PageId",
                table: "Content");

            migrationBuilder.CreateTable(
                name: "ContentsInPage",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PageId = table.Column<Guid>(type: "uuid", nullable: false),
                    ContentId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreationTime = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    ModificationTime = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContentsInPage", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PagesInCollection",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CollectionId = table.Column<Guid>(type: "uuid", nullable: false),
                    PageId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreationTime = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    ModificationTime = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PagesInCollection", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Page_SourceId",
                table: "Page",
                column: "SourceId",
                unique: true);
        }


        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ContentsInPage");

            migrationBuilder.DropTable(
                name: "PagesInCollection");

            migrationBuilder.DropIndex(
                name: "IX_Page_SourceId",
                table: "Page");

            migrationBuilder.AddColumn<Guid>(
                name: "CollectionId",
                table: "Page",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<Guid>(
                name: "PageId",
                table: "Content",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_Page_CollectionId_SourceId",
                table: "Page",
                columns: new[] { "CollectionId", "SourceId" },
                unique: true);
        }
    }
}
