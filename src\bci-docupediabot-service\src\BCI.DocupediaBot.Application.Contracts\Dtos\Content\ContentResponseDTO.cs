﻿using System;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Content
{
	public class ContentResponseDTO
	{
		public Guid Id { get; set; }

		public string Title { get; set; } = string.Empty;

		public string? Url { get; set; }

		public int VersionNo { get; set; }

		public int EmbeddingVersionNo { get; set; }

		public string SourceId { get; set; } = string.Empty;

		public string? Path { get; set; }

		public DateTimeOffset? SourceModificationTime { get; set; }

		public string? OriginContent { get; set; }

		public string? ProcessedContent { get; set; }

    public string? SummarizedContent { get; set; }
  }
}