{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "yarn", "analytics": false}, "newProjectRoot": "projects", "projects": {"bci-docupediabot": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "\\\\SGPVMC0533\\WebApp\\DocupediaBot\\service\\wwwroot", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "./node_modules/@bci-web-core/common-styles/img", "output": "/assets/img/"}], "styles": ["src/styles.scss", "src/app/chatbot/docupedia/components/docupedia-bot/docupedia-bot.component.scss", "src/app/chatbot/deepdoc/components/deepdoc-bot/deepdoc-bot.component.scss"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "3MB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "20kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "portal": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.portal.ts"}], "budgets": [{"type": "initial", "maximumWarning": "3MB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "20kB"}], "outputHashing": "all"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "bci-docupediabot:build:production"}, "development": {"buildTarget": "bci-docupediabot:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "./node_modules/@bci-web-core/common-styles/img", "output": "/assets/img/"}], "styles": ["src/styles.scss", "src/app/chatbot/docupedia/components/docupedia-bot/docupedia-bot.component.scss", "src/app/chatbot/deepdoc/components/deepdoc-bot/deepdoc-bot.component.scss"]}}}}}}