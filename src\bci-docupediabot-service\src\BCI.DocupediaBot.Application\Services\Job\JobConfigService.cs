﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Collection;
using BCI.DocupediaBot.Domain.Enums;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  public class JobConfigService : IJobConfigService
  {
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<JobConfigService> _logger;
    private readonly IMapper _mapper;

    public JobConfigService(
        IServiceProvider serviceProvider,
        ILogger<JobConfigService> logger = null,
        IMapper mapper = null)
    {
      _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
      _logger = logger;
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    }


    public async Task<IEnumerable<JobConfig>> GetJobConfigsAsync()
    {
      using (var scope = _serviceProvider.CreateScope())
      {

        var dbContextOptions = scope.ServiceProvider.GetRequiredService<DbContextOptions<DocupediaBotDbContext>>();


        using var dbContext = new DocupediaBotDbContext(dbContextOptions, null, null);

        _logger?.LogInformation("Querying all collections from database.");
        var collections = await dbContext.Collections.ToListAsync();

        var collectionDtos = _mapper.Map<List<CollectionResponseDTO>>(collections);

        var jobConfigs = new List<JobConfig>();

        foreach (var collection in collectionDtos.Where(c => c.IsAutomaticUpdate))
        {
          if (!collection.UpdateType.HasValue || !collection.UpdateTime.HasValue)
            continue;

          var jobConfig = new JobConfig
          {
            JobName = $"UpdateCollection_{collection.Id}",
            JobGroup = "CollectionUpdateJobs",
            JobType = typeof(UpdateCollectionJob).AssemblyQualifiedName,
            JobData = new Dictionary<string, object>
            {
              { "CollectionId", collection.Id },
              { "EmbeddingModel", collection.EmbeddingModel },
            }
          };

          var schedule = new JobScheduleConfig
          {
            Hour = collection.UpdateTime.Value.Hour,
            Minute = collection.UpdateTime.Value.Minute
          };

          switch (collection.UpdateType)
          {
            case UpdateType.Daily:
              schedule.ScheduleType = ScheduleType.Daily;
              schedule.IntervalInMinutes = 24 * 60;
              break;
            case UpdateType.Weekly:
              schedule.ScheduleType = ScheduleType.Weekly;
              schedule.DayOfWeek = DayOfWeek.Monday;
              schedule.IntervalInMinutes = 7 * 24 * 60;
              break;
            case UpdateType.BiWeekly:
              schedule.ScheduleType = ScheduleType.Weekly;
              schedule.DayOfWeek = DayOfWeek.Monday;
              schedule.IntervalInMinutes = 2 * 7 * 24 * 60;
              break;
            case UpdateType.Monthly:
              schedule.ScheduleType = ScheduleType.Daily;
              schedule.IntervalInMinutes = 30 * 24 * 60;
              break;
            case UpdateType.ByDay:
              schedule.ScheduleType = ScheduleType.Daily;
              schedule.IntervalInMinutes = collection.IntervalNumber * 24 * 60;
              break;
            case UpdateType.ByWeek:
              schedule.ScheduleType = ScheduleType.Weekly;
              schedule.DayOfWeek = DayOfWeek.Monday;
              schedule.IntervalInMinutes = collection.IntervalNumber * 7 * 24 * 60;
              break;
            case UpdateType.ByMonth:
              schedule.ScheduleType = ScheduleType.Daily;
              schedule.IntervalInMinutes = (collection.IntervalNumber ?? 1) * 30 * 24 * 60;
              break;
          }

          jobConfig.Schedule = schedule;
          jobConfigs.Add(jobConfig);
        }

        return jobConfigs;
      }
    }
  }
}