/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

export interface OidcConfiguration {
  serviceUrl: string;
  clientId: string;
  applicationName: string;
  tenantId: string;
  publicClientId: string;
  serviceScope: string;
  endpoints: Endpoints;
}

interface Endpoints {
  usage: string;
  discoveryEndpoint: string;
  introspectionEndpoint: string;
  userInfoEndpoint: string;
}
