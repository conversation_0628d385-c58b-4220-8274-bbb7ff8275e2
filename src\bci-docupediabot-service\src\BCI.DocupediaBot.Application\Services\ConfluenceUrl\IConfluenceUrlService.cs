using System.Threading.Tasks;
using BCI.DocupediaBot.Application.Contracts.Dtos.Confluence;

namespace BCI.DocupediaBot.Application.Services.ConfluenceUrl
{
    public interface IConfluenceUrlService
    {
        Task<ConfluencePageInfo> ResolvePageInfoFromUrlAsync(string url, string userToken);
        bool IsValidConfluenceUrl(string url);
        string GetUrlType(string url);
        string ExtractBaseUrlFromUrl(string url);
    }
}
