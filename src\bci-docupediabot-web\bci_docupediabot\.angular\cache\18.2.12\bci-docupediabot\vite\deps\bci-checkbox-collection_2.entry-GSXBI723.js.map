{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/bci-checkbox-collection_2.entry.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { r as registerInstance, c as createEvent, h, H as Host, a as getElement } from './index-93dc8059.js';\nimport { C as CollectionType } from './checkbox-collection.interface-4f1b87dc.js';\nimport { s as setLocale, t as translate } from './utils-636fa948.js';\nimport { _ as __extends, a as __assign, M as MDCFoundation, m as matches, c as __values, b as MDCComponent } from './ponyfill-78459bda.js';\nimport { a as getCorrectEventName } from './util-40cc7805.js';\nimport { M as MDCRipple, b as applyPassive, a as MDCRippleFoundation } from './component-d69b424e.js';\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses = {\n  ANIM_CHECKED_INDETERMINATE: 'mdc-checkbox--anim-checked-indeterminate',\n  ANIM_CHECKED_UNCHECKED: 'mdc-checkbox--anim-checked-unchecked',\n  ANIM_INDETERMINATE_CHECKED: 'mdc-checkbox--anim-indeterminate-checked',\n  ANIM_INDETERMINATE_UNCHECKED: 'mdc-checkbox--anim-indeterminate-unchecked',\n  ANIM_UNCHECKED_CHECKED: 'mdc-checkbox--anim-unchecked-checked',\n  ANIM_UNCHECKED_INDETERMINATE: 'mdc-checkbox--anim-unchecked-indeterminate',\n  BACKGROUND: 'mdc-checkbox__background',\n  CHECKED: 'mdc-checkbox--checked',\n  CHECKMARK: 'mdc-checkbox__checkmark',\n  CHECKMARK_PATH: 'mdc-checkbox__checkmark-path',\n  DISABLED: 'mdc-checkbox--disabled',\n  INDETERMINATE: 'mdc-checkbox--indeterminate',\n  MIXEDMARK: 'mdc-checkbox__mixedmark',\n  NATIVE_CONTROL: 'mdc-checkbox__native-control',\n  ROOT: 'mdc-checkbox',\n  SELECTED: 'mdc-checkbox--selected',\n  UPGRADED: 'mdc-checkbox--upgraded'\n};\nvar strings = {\n  ARIA_CHECKED_ATTR: 'aria-checked',\n  ARIA_CHECKED_INDETERMINATE_VALUE: 'mixed',\n  DATA_INDETERMINATE_ATTR: 'data-indeterminate',\n  NATIVE_CONTROL_SELECTOR: '.mdc-checkbox__native-control',\n  TRANSITION_STATE_CHECKED: 'checked',\n  TRANSITION_STATE_INDETERMINATE: 'indeterminate',\n  TRANSITION_STATE_INIT: 'init',\n  TRANSITION_STATE_UNCHECKED: 'unchecked'\n};\nvar numbers = {\n  ANIM_END_LATCH_MS: 250\n};\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCCheckboxFoundation = /** @class */function (_super) {\n  __extends(MDCCheckboxFoundation, _super);\n  function MDCCheckboxFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCCheckboxFoundation.defaultAdapter), adapter)) || this;\n    _this.currentCheckState = strings.TRANSITION_STATE_INIT;\n    _this.currentAnimationClass = '';\n    _this.animEndLatchTimer = 0;\n    _this.enableAnimationEndHandler = false;\n    return _this;\n  }\n  Object.defineProperty(MDCCheckboxFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCCheckboxFoundation, \"strings\", {\n    get: function () {\n      return strings;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCCheckboxFoundation, \"numbers\", {\n    get: function () {\n      return numbers;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCCheckboxFoundation, \"defaultAdapter\", {\n    get: function () {\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        forceLayout: function () {\n          return undefined;\n        },\n        hasNativeControl: function () {\n          return false;\n        },\n        isAttachedToDOM: function () {\n          return false;\n        },\n        isChecked: function () {\n          return false;\n        },\n        isIndeterminate: function () {\n          return false;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        removeNativeControlAttr: function () {\n          return undefined;\n        },\n        setNativeControlAttr: function () {\n          return undefined;\n        },\n        setNativeControlDisabled: function () {\n          return undefined;\n        }\n      };\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCCheckboxFoundation.prototype.init = function () {\n    this.currentCheckState = this.determineCheckState();\n    this.updateAriaChecked();\n    this.adapter.addClass(cssClasses.UPGRADED);\n  };\n  MDCCheckboxFoundation.prototype.destroy = function () {\n    clearTimeout(this.animEndLatchTimer);\n  };\n  MDCCheckboxFoundation.prototype.setDisabled = function (disabled) {\n    this.adapter.setNativeControlDisabled(disabled);\n    if (disabled) {\n      this.adapter.addClass(cssClasses.DISABLED);\n    } else {\n      this.adapter.removeClass(cssClasses.DISABLED);\n    }\n  };\n  /**\n   * Handles the animationend event for the checkbox\n   */\n  MDCCheckboxFoundation.prototype.handleAnimationEnd = function () {\n    var _this = this;\n    if (!this.enableAnimationEndHandler) {\n      return;\n    }\n    clearTimeout(this.animEndLatchTimer);\n    this.animEndLatchTimer = setTimeout(function () {\n      _this.adapter.removeClass(_this.currentAnimationClass);\n      _this.enableAnimationEndHandler = false;\n    }, numbers.ANIM_END_LATCH_MS);\n  };\n  /**\n   * Handles the change event for the checkbox\n   */\n  MDCCheckboxFoundation.prototype.handleChange = function () {\n    this.transitionCheckState();\n  };\n  MDCCheckboxFoundation.prototype.transitionCheckState = function () {\n    if (!this.adapter.hasNativeControl()) {\n      return;\n    }\n    var oldState = this.currentCheckState;\n    var newState = this.determineCheckState();\n    if (oldState === newState) {\n      return;\n    }\n    this.updateAriaChecked();\n    var TRANSITION_STATE_UNCHECKED = strings.TRANSITION_STATE_UNCHECKED;\n    var SELECTED = cssClasses.SELECTED;\n    if (newState === TRANSITION_STATE_UNCHECKED) {\n      this.adapter.removeClass(SELECTED);\n    } else {\n      this.adapter.addClass(SELECTED);\n    }\n    // Check to ensure that there isn't a previously existing animation class, in case for example\n    // the user interacted with the checkbox before the animation was finished.\n    if (this.currentAnimationClass.length > 0) {\n      clearTimeout(this.animEndLatchTimer);\n      this.adapter.forceLayout();\n      this.adapter.removeClass(this.currentAnimationClass);\n    }\n    this.currentAnimationClass = this.getTransitionAnimationClass(oldState, newState);\n    this.currentCheckState = newState;\n    // Check for parentNode so that animations are only run when the element is attached\n    // to the DOM.\n    if (this.adapter.isAttachedToDOM() && this.currentAnimationClass.length > 0) {\n      this.adapter.addClass(this.currentAnimationClass);\n      this.enableAnimationEndHandler = true;\n    }\n  };\n  MDCCheckboxFoundation.prototype.determineCheckState = function () {\n    var TRANSITION_STATE_INDETERMINATE = strings.TRANSITION_STATE_INDETERMINATE,\n      TRANSITION_STATE_CHECKED = strings.TRANSITION_STATE_CHECKED,\n      TRANSITION_STATE_UNCHECKED = strings.TRANSITION_STATE_UNCHECKED;\n    if (this.adapter.isIndeterminate()) {\n      return TRANSITION_STATE_INDETERMINATE;\n    }\n    return this.adapter.isChecked() ? TRANSITION_STATE_CHECKED : TRANSITION_STATE_UNCHECKED;\n  };\n  MDCCheckboxFoundation.prototype.getTransitionAnimationClass = function (oldState, newState) {\n    var TRANSITION_STATE_INIT = strings.TRANSITION_STATE_INIT,\n      TRANSITION_STATE_CHECKED = strings.TRANSITION_STATE_CHECKED,\n      TRANSITION_STATE_UNCHECKED = strings.TRANSITION_STATE_UNCHECKED;\n    var _a = MDCCheckboxFoundation.cssClasses,\n      ANIM_UNCHECKED_CHECKED = _a.ANIM_UNCHECKED_CHECKED,\n      ANIM_UNCHECKED_INDETERMINATE = _a.ANIM_UNCHECKED_INDETERMINATE,\n      ANIM_CHECKED_UNCHECKED = _a.ANIM_CHECKED_UNCHECKED,\n      ANIM_CHECKED_INDETERMINATE = _a.ANIM_CHECKED_INDETERMINATE,\n      ANIM_INDETERMINATE_CHECKED = _a.ANIM_INDETERMINATE_CHECKED,\n      ANIM_INDETERMINATE_UNCHECKED = _a.ANIM_INDETERMINATE_UNCHECKED;\n    switch (oldState) {\n      case TRANSITION_STATE_INIT:\n        if (newState === TRANSITION_STATE_UNCHECKED) {\n          return '';\n        }\n        return newState === TRANSITION_STATE_CHECKED ? ANIM_INDETERMINATE_CHECKED : ANIM_INDETERMINATE_UNCHECKED;\n      case TRANSITION_STATE_UNCHECKED:\n        return newState === TRANSITION_STATE_CHECKED ? ANIM_UNCHECKED_CHECKED : ANIM_UNCHECKED_INDETERMINATE;\n      case TRANSITION_STATE_CHECKED:\n        return newState === TRANSITION_STATE_UNCHECKED ? ANIM_CHECKED_UNCHECKED : ANIM_CHECKED_INDETERMINATE;\n      default:\n        // TRANSITION_STATE_INDETERMINATE\n        return newState === TRANSITION_STATE_CHECKED ? ANIM_INDETERMINATE_CHECKED : ANIM_INDETERMINATE_UNCHECKED;\n    }\n  };\n  MDCCheckboxFoundation.prototype.updateAriaChecked = function () {\n    // Ensure aria-checked is set to mixed if checkbox is in indeterminate state.\n    if (this.adapter.isIndeterminate()) {\n      this.adapter.setNativeControlAttr(strings.ARIA_CHECKED_ATTR, strings.ARIA_CHECKED_INDETERMINATE_VALUE);\n    } else {\n      // The on/off state does not need to keep track of aria-checked, since\n      // the screenreader uses the checked property on the checkbox element.\n      this.adapter.removeNativeControlAttr(strings.ARIA_CHECKED_ATTR);\n    }\n  };\n  return MDCCheckboxFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar CB_PROTO_PROPS = ['checked', 'indeterminate'];\nvar MDCCheckbox = /** @class */function (_super) {\n  __extends(MDCCheckbox, _super);\n  function MDCCheckbox() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.rippleSurface = _this.createRipple();\n    return _this;\n  }\n  MDCCheckbox.attachTo = function (root) {\n    return new MDCCheckbox(root);\n  };\n  Object.defineProperty(MDCCheckbox.prototype, \"ripple\", {\n    get: function () {\n      return this.rippleSurface;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCCheckbox.prototype, \"checked\", {\n    get: function () {\n      return this.getNativeControl().checked;\n    },\n    set: function (checked) {\n      this.getNativeControl().checked = checked;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCCheckbox.prototype, \"indeterminate\", {\n    get: function () {\n      return this.getNativeControl().indeterminate;\n    },\n    set: function (indeterminate) {\n      this.getNativeControl().indeterminate = indeterminate;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCCheckbox.prototype, \"disabled\", {\n    get: function () {\n      return this.getNativeControl().disabled;\n    },\n    set: function (disabled) {\n      this.foundation.setDisabled(disabled);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCCheckbox.prototype, \"value\", {\n    get: function () {\n      return this.getNativeControl().value;\n    },\n    set: function (value) {\n      this.getNativeControl().value = value;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCCheckbox.prototype.initialize = function () {\n    var DATA_INDETERMINATE_ATTR = strings.DATA_INDETERMINATE_ATTR;\n    this.getNativeControl().indeterminate = this.getNativeControl().getAttribute(DATA_INDETERMINATE_ATTR) === 'true';\n    this.getNativeControl().removeAttribute(DATA_INDETERMINATE_ATTR);\n  };\n  MDCCheckbox.prototype.initialSyncWithDOM = function () {\n    var _this = this;\n    this.handleChange = function () {\n      _this.foundation.handleChange();\n    };\n    this.handleAnimationEnd = function () {\n      _this.foundation.handleAnimationEnd();\n    };\n    this.getNativeControl().addEventListener('change', this.handleChange);\n    this.listen(getCorrectEventName(window, 'animationend'), this.handleAnimationEnd);\n    this.installPropertyChangeHooks();\n  };\n  MDCCheckbox.prototype.destroy = function () {\n    this.rippleSurface.destroy();\n    this.getNativeControl().removeEventListener('change', this.handleChange);\n    this.unlisten(getCorrectEventName(window, 'animationend'), this.handleAnimationEnd);\n    this.uninstallPropertyChangeHooks();\n    _super.prototype.destroy.call(this);\n  };\n  MDCCheckbox.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    var adapter = {\n      addClass: function (className) {\n        return _this.root.classList.add(className);\n      },\n      forceLayout: function () {\n        return _this.root.offsetWidth;\n      },\n      hasNativeControl: function () {\n        return !!_this.getNativeControl();\n      },\n      isAttachedToDOM: function () {\n        return Boolean(_this.root.parentNode);\n      },\n      isChecked: function () {\n        return _this.checked;\n      },\n      isIndeterminate: function () {\n        return _this.indeterminate;\n      },\n      removeClass: function (className) {\n        _this.root.classList.remove(className);\n      },\n      removeNativeControlAttr: function (attr) {\n        _this.getNativeControl().removeAttribute(attr);\n      },\n      setNativeControlAttr: function (attr, value) {\n        _this.getNativeControl().setAttribute(attr, value);\n      },\n      setNativeControlDisabled: function (disabled) {\n        _this.getNativeControl().disabled = disabled;\n      }\n    };\n    return new MDCCheckboxFoundation(adapter);\n  };\n  MDCCheckbox.prototype.createRipple = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    var adapter = __assign(__assign({}, MDCRipple.createAdapter(this)), {\n      deregisterInteractionHandler: function (evtType, handler) {\n        _this.getNativeControl().removeEventListener(evtType, handler, applyPassive());\n      },\n      isSurfaceActive: function () {\n        return matches(_this.getNativeControl(), ':active');\n      },\n      isUnbounded: function () {\n        return true;\n      },\n      registerInteractionHandler: function (evtType, handler) {\n        _this.getNativeControl().addEventListener(evtType, handler, applyPassive());\n      }\n    });\n    return new MDCRipple(this.root, new MDCRippleFoundation(adapter));\n  };\n  MDCCheckbox.prototype.installPropertyChangeHooks = function () {\n    var e_1, _a;\n    var _this = this;\n    var nativeCb = this.getNativeControl();\n    var cbProto = Object.getPrototypeOf(nativeCb);\n    var _loop_1 = function (controlState) {\n      var desc = Object.getOwnPropertyDescriptor(cbProto, controlState);\n      // We have to check for this descriptor, since some browsers (Safari) don't support its return.\n      // See: https://bugs.webkit.org/show_bug.cgi?id=49739\n      if (!validDescriptor(desc)) {\n        return {\n          value: void 0\n        };\n      }\n      // Type cast is needed for compatibility with Closure Compiler.\n      var nativeGetter = desc.get;\n      var nativeCbDesc = {\n        configurable: desc.configurable,\n        enumerable: desc.enumerable,\n        get: nativeGetter,\n        set: function (state) {\n          desc.set.call(nativeCb, state);\n          _this.foundation.handleChange();\n        }\n      };\n      Object.defineProperty(nativeCb, controlState, nativeCbDesc);\n    };\n    try {\n      for (var CB_PROTO_PROPS_1 = __values(CB_PROTO_PROPS), CB_PROTO_PROPS_1_1 = CB_PROTO_PROPS_1.next(); !CB_PROTO_PROPS_1_1.done; CB_PROTO_PROPS_1_1 = CB_PROTO_PROPS_1.next()) {\n        var controlState = CB_PROTO_PROPS_1_1.value;\n        var state_1 = _loop_1(controlState);\n        if (typeof state_1 === \"object\") return state_1.value;\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (CB_PROTO_PROPS_1_1 && !CB_PROTO_PROPS_1_1.done && (_a = CB_PROTO_PROPS_1.return)) _a.call(CB_PROTO_PROPS_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  };\n  MDCCheckbox.prototype.uninstallPropertyChangeHooks = function () {\n    var e_2, _a;\n    var nativeCb = this.getNativeControl();\n    var cbProto = Object.getPrototypeOf(nativeCb);\n    try {\n      for (var CB_PROTO_PROPS_2 = __values(CB_PROTO_PROPS), CB_PROTO_PROPS_2_1 = CB_PROTO_PROPS_2.next(); !CB_PROTO_PROPS_2_1.done; CB_PROTO_PROPS_2_1 = CB_PROTO_PROPS_2.next()) {\n        var controlState = CB_PROTO_PROPS_2_1.value;\n        var desc = Object.getOwnPropertyDescriptor(cbProto, controlState);\n        if (!validDescriptor(desc)) {\n          return;\n        }\n        Object.defineProperty(nativeCb, controlState, desc);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (CB_PROTO_PROPS_2_1 && !CB_PROTO_PROPS_2_1.done && (_a = CB_PROTO_PROPS_2.return)) _a.call(CB_PROTO_PROPS_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n  };\n  MDCCheckbox.prototype.getNativeControl = function () {\n    var NATIVE_CONTROL_SELECTOR = strings.NATIVE_CONTROL_SELECTOR;\n    var el = this.root.querySelector(NATIVE_CONTROL_SELECTOR);\n    if (!el) {\n      throw new Error(\"Checkbox component requires a \" + NATIVE_CONTROL_SELECTOR + \" element\");\n    }\n    return el;\n  };\n  return MDCCheckbox;\n}(MDCComponent);\nfunction validDescriptor(inputPropDesc) {\n  return !!inputPropDesc && typeof inputPropDesc.set === 'function';\n}\nconst checkboxCollectionCss = \"/*!\\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */.mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.875rem;font-size:var(--mdc-typography-body2-font-size, 0.875rem);line-height:1.25rem;line-height:var(--mdc-typography-body2-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:0.0178571429em;letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:inherit;text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-body2-text-transform, inherit);color:rgba(0, 0, 0, 0.87);color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87));display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0;}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px;}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto;}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0;}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0;}.mdc-checkbox{padding:calc((40px - 18px) / 2);padding:calc((var(--mdc-checkbox-ripple-size, 40px) - 18px) / 2);margin:calc((40px - 40px) / 2);margin:calc((var(--mdc-checkbox-touch-target-size, 40px) - 40px) / 2)}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}.mdc-checkbox:hover .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-ripple-surface--hover .mdc-checkbox__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__ripple::before,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-checkbox:not(.mdc-ripple-upgraded) .mdc-checkbox__ripple::after{transition:opacity 150ms linear}.mdc-checkbox:not(.mdc-ripple-upgraded):active .mdc-checkbox__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-checkbox--selected .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-checkbox--selected .mdc-checkbox__ripple::after{background-color:#018786;background-color:var(--mdc-ripple-color, var(--mdc-theme-secondary, #018786))}.mdc-checkbox.mdc-checkbox--selected:hover .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-checkbox--selected.mdc-ripple-surface--hover .mdc-checkbox__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-checkbox.mdc-checkbox--selected.mdc-ripple-upgraded--background-focused .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-checkbox--selected:not(.mdc-ripple-upgraded):focus .mdc-checkbox__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-checkbox.mdc-checkbox--selected:not(.mdc-ripple-upgraded) .mdc-checkbox__ripple::after{transition:opacity 150ms linear}.mdc-checkbox.mdc-checkbox--selected:not(.mdc-ripple-upgraded):active .mdc-checkbox__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-checkbox--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-ripple-upgraded--background-focused.mdc-checkbox--selected .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-ripple-upgraded--background-focused.mdc-checkbox--selected .mdc-checkbox__ripple::after{background-color:#018786;background-color:var(--mdc-ripple-color, var(--mdc-theme-secondary, #018786))}.mdc-checkbox .mdc-checkbox__background{top:calc((40px - 18px) / 2);top:calc((var(--mdc-checkbox-ripple-size, 40px) - 18px) / 2);left:calc((40px - 18px) / 2);left:calc((var(--mdc-checkbox-ripple-size, 40px) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((40px - 40px) / 2);top:calc((40px - var(--mdc-checkbox-touch-target-size, 40px)) / 2);right:calc((40px - 40px) / 2);right:calc((40px - var(--mdc-checkbox-touch-target-size, 40px)) / 2);left:calc((40px - 40px) / 2);left:calc((40px - var(--mdc-checkbox-touch-target-size, 40px)) / 2);width:40px;width:var(--mdc-checkbox-touch-target-size, 40px);height:40px;height:var(--mdc-checkbox-touch-target-size, 40px)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:rgba(0, 0, 0, 0.54);border-color:var(--mdc-checkbox-unchecked-color, rgba(0, 0, 0, 0.54));background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:#018786;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786));background-color:#018786;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786))}@keyframes mdc-checkbox-fade-in-background-8A000000FF01878600000000FF018786{0%{border-color:rgba(0, 0, 0, 0.54);border-color:var(--mdc-checkbox-unchecked-color, rgba(0, 0, 0, 0.54));background-color:transparent}50%{border-color:#018786;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786));background-color:#018786;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786))}}@keyframes mdc-checkbox-fade-out-background-8A000000FF01878600000000FF018786{0%,80%{border-color:#018786;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786));background-color:#018786;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786))}100%{border-color:rgba(0, 0, 0, 0.54);border-color:var(--mdc-checkbox-unchecked-color, rgba(0, 0, 0, 0.54));background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FF01878600000000FF018786}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FF01878600000000FF018786}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:rgba(0, 0, 0, 0.38);border-color:var(--mdc-checkbox-disabled-color, rgba(0, 0, 0, 0.38));background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:rgba(0, 0, 0, 0.38);background-color:var(--mdc-checkbox-disabled-color, rgba(0, 0, 0, 0.38))}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:#fff;color:var(--mdc-checkbox-ink-color, #fff)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:#fff;border-color:var(--mdc-checkbox-ink-color, #fff)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:#fff;color:var(--mdc-checkbox-ink-color, #fff)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:#fff;border-color:var(--mdc-checkbox-ink-color, #fff)}.mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid transparent;border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid transparent;border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:transparent;pointer-events:none;will-change:background-color, border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1), background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((48px - 40px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size, 48px) - var(--mdc-checkbox-state-layer-size, 40px)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((40px - 48px) / 2);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 48px)) / 2);right:calc((40px - 48px) / 2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 48px)) / 2);left:calc((40px - 48px) / 2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 48px)) / 2);width:48px;width:var(--mdc-checkbox-state-layer-size, 48px);height:48px;height:var(--mdc-checkbox-state-layer-size, 48px)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}@keyframes mdc-ripple-fg-radius-in{from{animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transform:translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1)}to{transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}}@keyframes mdc-ripple-fg-opacity-in{from{animation-timing-function:linear;opacity:0}to{opacity:var(--mdc-ripple-fg-opacity, 0)}}@keyframes mdc-ripple-fg-opacity-out{from{animation-timing-function:linear;opacity:var(--mdc-ripple-fg-opacity, 0)}to{opacity:0}}.mdc-checkbox{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-checkbox .mdc-checkbox__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-checkbox .mdc-checkbox__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-checkbox.mdc-ripple-upgraded--unbounded .mdc-checkbox__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-checkbox.mdc-ripple-upgraded--foreground-activation .mdc-checkbox__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-checkbox.mdc-ripple-upgraded--foreground-deactivation .mdc-checkbox__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{top:calc(50% - 50%);left:calc(50% - 50%);width:100%;height:100%}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::after{top:var(--mdc-ripple-top, calc(50% - 50%));left:var(--mdc-ripple-left, calc(50% - 50%));width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-checkbox{z-index:0}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{z-index:-1;z-index:var(--mdc-ripple-z-index, -1)}.mdc-checkbox__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:GrayText;border-color:var(--mdc-checkbox-disabled-unselected-icon-color, GrayText);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:GrayText;background-color:GrayText;background-color:var(--mdc-checkbox-disabled-selected-icon-color, GrayText)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:ButtonText;color:var(--mdc-checkbox-selected-checkmark-color, ButtonText)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:ButtonText;border-color:var(--mdc-checkbox-selected-checkmark-color, ButtonText)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:ButtonFace;color:var(--mdc-checkbox-disabled-selected-checkmark-color, ButtonFace)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:ButtonFace;border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, ButtonFace)}}/*!\\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */@font-face{font-family:var(--bci-global-font-family);font-weight:400;src:url(\\\"..//fonts/BoschSans-Regular.eot\\\");src:url(\\\"..//fonts/BoschSans-Regular.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Regular.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Regular.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Regular.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Regular.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:300;src:url(\\\"..//fonts/BoschSans-Light.eot\\\");src:url(\\\"..//fonts/BoschSans-Light.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Light.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Light.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Light.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-LightItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-LightItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-LightItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Light.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:500;src:url(\\\"..//fonts/BoschSans-Medium.eot\\\");src:url(\\\"..//fonts/BoschSans-Medium.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Medium.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Medium.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Medium.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Medium.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:700;src:url(\\\"..//fonts/BoschSans-Bold.eot\\\");src:url(\\\"..//fonts/BoschSans-Bold.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Bold.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Bold.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Bold.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Bold.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:900;src:url(\\\"..//fonts/BoschSans-Black.eot\\\");src:url(\\\"..//fonts/BoschSans-Black.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Black.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Black.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Black.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Black.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url(\\\"..//fonts/Bosch-Icon.eot?mh5qa9\\\");src:url(\\\"..//fonts/Bosch-Icon.eot?mh5qa9#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/Bosch-Icon.ttf?mh5qa9\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/Bosch-Icon.woff?mh5qa9\\\") format(\\\"woff\\\"), url(\\\"..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon\\\") format(\\\"svg\\\")}@font-face{font-family:\\\"Bosch-Sans-Condensed\\\";font-weight:300;src:url(\\\"..//fonts/BoschSansCond-Regular.otf\\\"), url(\\\"..//fonts/BoschSansCondensed-Regular.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSansCondensed-RegularItalic.ttf\\\") format(\\\"truetype\\\")}@font-face{font-family:\\\"Bosch-Sans-Condensed\\\";font-weight:700;src:url(\\\"..//fonts/BoschSansCond-Bold.otf\\\"), url(\\\"..//fonts/BoschSansCondensed-Bold.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSansCondensed-BoldItalic.ttf\\\") format(\\\"truetype\\\")}/*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */body{margin:0;display:flex;flex-direction:column}main,*{font-family:var(--bci-global-font-family)}::selection,::-moz-selection{background-color:#007bc0}a::-moz-selection{color:#ffffff}.lead{margin-bottom:24px;font-size:18px;font-weight:300;line-height:1.4}@media (min-width: 768px){.lead{font-size:24px}}.checkbox-item{width:100%;margin:20px 16px}.checkbox-item>label{font-weight:400;padding-left:8px;font-size:16px;width:100%}.checkbox-item>label:hover{cursor:pointer}.mdc-checkbox{padding:calc((0 - 18px) / 2);padding:calc((var(--mdc-checkbox-ripple-size, 0) - 18px) / 2);padding:0;height:initial;width:initial;flex-basis:24px}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:#d0d4d8;border-color:var(--mdc-checkbox-unchecked-color, #d0d4d8);background-color:#eff1f2}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:#005587;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587));background-color:#005587;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587))}@keyframes mdc-checkbox-fade-in-background-********************************{0%{border-color:#d0d4d8;border-color:var(--mdc-checkbox-unchecked-color, #d0d4d8);background-color:#eff1f2}50%{border-color:#005587;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587));background-color:#005587;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587))}}@keyframes mdc-checkbox-fade-out-background-********************************{0%,80%{border-color:#005587;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587));background-color:#005587;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587))}100%{border-color:#d0d4d8;border-color:var(--mdc-checkbox-unchecked-color, #d0d4d8);background-color:#eff1f2}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-********************************}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-********************************}.mdc-checkbox .mdc-checkbox__background{top:calc((0 - 18px) / 2);top:calc((var(--mdc-checkbox-ripple-size, 0) - 18px) / 2);left:calc((0 - 18px) / 2);left:calc((var(--mdc-checkbox-ripple-size, 0) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{position:relative;height:24px;width:24px;min-width:24px}.mdc-checkbox .mdc-checkbox__background{border-radius:0;top:0;left:0;width:24px;height:24px}\";\nconst CheckboxCollection = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.multiItemSelected = createEvent(this, \"multiItemSelected\", 7);\n    this.singleItemSelected = createEvent(this, \"singleItemSelected\", 7);\n    this.multiSelectionList = [];\n    this.type = undefined;\n    this.list = [];\n  }\n  async componentWillLoad() {\n    await setLocale();\n  }\n  async reset() {\n    this.el.shadowRoot.querySelectorAll(`input[type='checkbox']`).forEach(item => item['checked'] = false);\n    this.multiSelectionList = [];\n  }\n  async setDefaultSelection() {\n    if (this.type === CollectionType.Multi) {\n      this.multiSelectionList = this.list.reduce((accumulator, currentItem, index) => {\n        if (currentItem.label) {\n          accumulator.push(currentItem.label);\n          const checkbox = new MDCCheckbox(this.el.shadowRoot.getElementById(`multi-checkbox-${index}`).parentElement);\n          checkbox.checked = currentItem.selected;\n        }\n        return accumulator;\n      }, []);\n      this.multiItemSelected.emit(this.multiSelectionList);\n    } else {\n      const index = this.list.findIndex(item => item.selected);\n      const checkbox = new MDCCheckbox(this.el.shadowRoot.getElementById(`single-checkbox-${index}`).parentElement);\n      checkbox.checked = true;\n      this.singleItemSelected.emit(this.list[index].label);\n    }\n  }\n  multiItemSelectedHandler(event, filter) {\n    if (event.target['checked']) {\n      this.multiSelectionList.push(filter.label);\n    } else {\n      const i = this.multiSelectionList.findIndex(j => j === filter.label);\n      if (i > -1) {\n        this.multiSelectionList.splice(i, 1);\n      }\n    }\n    this.multiItemSelected.emit(this.multiSelectionList);\n  }\n  singleItemSelectHandler(event, filter) {\n    if (event.target['checked']) {\n      this.singleItemSelected.emit(filter.label);\n    }\n    // Unchecking other checkboxes in the collection\n    this.el.shadowRoot.querySelectorAll('.single').forEach(item => item['checked'] = false);\n    event.target['checked'] = true;\n  }\n  selectHandler(event, item, multipleSelection) {\n    if (multipleSelection) {\n      return this.multiItemSelectedHandler(event, item);\n    }\n    this.singleItemSelectHandler(event, item);\n  }\n  renderCheckbox(item, index, multipleSelection) {\n    return h(\"div\", {\n      \"data-test\": \"webcore.webcomponents.checkbox-collection.checkbox-item\",\n      class: \"mdc-form-field checkbox-item\",\n      part: multipleSelection ? '' : 'single-checkbox-item'\n    }, h(\"div\", {\n      class: \"mdc-checkbox\"\n    }, h(\"input\", {\n      \"data-test\": \"webcore.webcomponents.checkbox-collection.checkbox-item.multiple-selection\",\n      type: \"checkbox\",\n      class: 'mdc-checkbox__native-control ' + (multipleSelection ? 'multi' : 'single'),\n      id: `${multipleSelection ? 'multi' : 'single'}-checkbox-${index}`,\n      onClick: event => this.selectHandler(event, item, multipleSelection)\n    }), h(\"div\", {\n      class: \"mdc-checkbox__background\"\n    }, h(\"svg\", {\n      class: \"mdc-checkbox__checkmark\",\n      viewBox: \"0 0 24 24\"\n    }, h(\"path\", {\n      class: \"mdc-checkbox__checkmark-path\",\n      fill: \"none\",\n      d: \"M1.73,12.91 8.1,19.28 22.79,4.59\"\n    })), h(\"div\", {\n      class: \"mdc-checkbox__mixedmark\"\n    })), h(\"div\", {\n      class: \"mdc-checkbox__ripple\"\n    })), h(\"label\", {\n      htmlFor: `${multipleSelection ? 'multi' : 'single'}-checkbox-${index}`\n    }, translate(item.label)));\n  }\n  render() {\n    const multipleSelection = this.type === CollectionType.Multi;\n    return h(Host, null, this.list.map((item, index) => this.renderCheckbox(item, index, multipleSelection)));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nCheckboxCollection.style = checkboxCollectionCss;\nconst checkboxInputCss = \".mdc-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.875rem;font-size:var(--mdc-typography-body2-font-size, 0.875rem);line-height:1.25rem;line-height:var(--mdc-typography-body2-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:0.0178571429em;letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:inherit;text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-body2-text-transform, inherit);color:rgba(0, 0, 0, 0.87);color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87));display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0;}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px;}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto;}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0;}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0;}.mdc-checkbox{padding:calc((40px - 18px) / 2);padding:calc((var(--mdc-checkbox-ripple-size, 40px) - 18px) / 2);margin:calc((40px - 40px) / 2);margin:calc((var(--mdc-checkbox-touch-target-size, 40px) - 40px) / 2)}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}.mdc-checkbox:hover .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-ripple-surface--hover .mdc-checkbox__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__ripple::before,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-checkbox:not(.mdc-ripple-upgraded) .mdc-checkbox__ripple::after{transition:opacity 150ms linear}.mdc-checkbox:not(.mdc-ripple-upgraded):active .mdc-checkbox__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-checkbox--selected .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-checkbox--selected .mdc-checkbox__ripple::after{background-color:#018786;background-color:var(--mdc-ripple-color, var(--mdc-theme-secondary, #018786))}.mdc-checkbox.mdc-checkbox--selected:hover .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-checkbox--selected.mdc-ripple-surface--hover .mdc-checkbox__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-checkbox.mdc-checkbox--selected.mdc-ripple-upgraded--background-focused .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-checkbox--selected:not(.mdc-ripple-upgraded):focus .mdc-checkbox__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-checkbox.mdc-checkbox--selected:not(.mdc-ripple-upgraded) .mdc-checkbox__ripple::after{transition:opacity 150ms linear}.mdc-checkbox.mdc-checkbox--selected:not(.mdc-ripple-upgraded):active .mdc-checkbox__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-checkbox--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-checkbox.mdc-ripple-upgraded--background-focused.mdc-checkbox--selected .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-ripple-upgraded--background-focused.mdc-checkbox--selected .mdc-checkbox__ripple::after{background-color:#018786;background-color:var(--mdc-ripple-color, var(--mdc-theme-secondary, #018786))}.mdc-checkbox .mdc-checkbox__background{top:calc((40px - 18px) / 2);top:calc((var(--mdc-checkbox-ripple-size, 40px) - 18px) / 2);left:calc((40px - 18px) / 2);left:calc((var(--mdc-checkbox-ripple-size, 40px) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((40px - 40px) / 2);top:calc((40px - var(--mdc-checkbox-touch-target-size, 40px)) / 2);right:calc((40px - 40px) / 2);right:calc((40px - var(--mdc-checkbox-touch-target-size, 40px)) / 2);left:calc((40px - 40px) / 2);left:calc((40px - var(--mdc-checkbox-touch-target-size, 40px)) / 2);width:40px;width:var(--mdc-checkbox-touch-target-size, 40px);height:40px;height:var(--mdc-checkbox-touch-target-size, 40px)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:rgba(0, 0, 0, 0.54);border-color:var(--mdc-checkbox-unchecked-color, rgba(0, 0, 0, 0.54));background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:#018786;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786));background-color:#018786;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786))}@keyframes mdc-checkbox-fade-in-background-8A000000FF01878600000000FF018786{0%{border-color:rgba(0, 0, 0, 0.54);border-color:var(--mdc-checkbox-unchecked-color, rgba(0, 0, 0, 0.54));background-color:transparent}50%{border-color:#018786;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786));background-color:#018786;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786))}}@keyframes mdc-checkbox-fade-out-background-8A000000FF01878600000000FF018786{0%,80%{border-color:#018786;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786));background-color:#018786;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #018786))}100%{border-color:rgba(0, 0, 0, 0.54);border-color:var(--mdc-checkbox-unchecked-color, rgba(0, 0, 0, 0.54));background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FF01878600000000FF018786}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FF01878600000000FF018786}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:rgba(0, 0, 0, 0.38);border-color:var(--mdc-checkbox-disabled-color, rgba(0, 0, 0, 0.38));background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:rgba(0, 0, 0, 0.38);background-color:var(--mdc-checkbox-disabled-color, rgba(0, 0, 0, 0.38))}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:#fff;color:var(--mdc-checkbox-ink-color, #fff)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:#fff;border-color:var(--mdc-checkbox-ink-color, #fff)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:#fff;color:var(--mdc-checkbox-ink-color, #fff)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:#fff;border-color:var(--mdc-checkbox-ink-color, #fff)}.mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid transparent;border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid transparent;border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:transparent;pointer-events:none;will-change:background-color, border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1), background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((48px - 40px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size, 48px) - var(--mdc-checkbox-state-layer-size, 40px)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((40px - 48px) / 2);top:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 48px)) / 2);right:calc((40px - 48px) / 2);right:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 48px)) / 2);left:calc((40px - 48px) / 2);left:calc((var(--mdc-checkbox-state-layer-size, 40px) - var(--mdc-checkbox-state-layer-size, 48px)) / 2);width:48px;width:var(--mdc-checkbox-state-layer-size, 48px);height:48px;height:var(--mdc-checkbox-state-layer-size, 48px)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1), transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1), transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}@keyframes mdc-ripple-fg-radius-in{from{animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transform:translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1)}to{transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}}@keyframes mdc-ripple-fg-opacity-in{from{animation-timing-function:linear;opacity:0}to{opacity:var(--mdc-ripple-fg-opacity, 0)}}@keyframes mdc-ripple-fg-opacity-out{from{animation-timing-function:linear;opacity:var(--mdc-ripple-fg-opacity, 0)}to{opacity:0}}.mdc-checkbox{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-checkbox .mdc-checkbox__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-checkbox .mdc-checkbox__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-checkbox.mdc-ripple-upgraded--unbounded .mdc-checkbox__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-checkbox.mdc-ripple-upgraded--foreground-activation .mdc-checkbox__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-checkbox.mdc-ripple-upgraded--foreground-deactivation .mdc-checkbox__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{top:calc(50% - 50%);left:calc(50% - 50%);width:100%;height:100%}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::before,.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::after{top:var(--mdc-ripple-top, calc(50% - 50%));left:var(--mdc-ripple-left, calc(50% - 50%));width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-checkbox.mdc-ripple-upgraded .mdc-checkbox__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-checkbox{z-index:0}.mdc-checkbox .mdc-checkbox__ripple::before,.mdc-checkbox .mdc-checkbox__ripple::after{z-index:-1;z-index:var(--mdc-ripple-z-index, -1)}.mdc-checkbox__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:GrayText;border-color:var(--mdc-checkbox-disabled-unselected-icon-color, GrayText);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:GrayText;background-color:GrayText;background-color:var(--mdc-checkbox-disabled-selected-icon-color, GrayText)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:ButtonText;color:var(--mdc-checkbox-selected-checkmark-color, ButtonText)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:ButtonText;border-color:var(--mdc-checkbox-selected-checkmark-color, ButtonText)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:ButtonFace;color:var(--mdc-checkbox-disabled-selected-checkmark-color, ButtonFace)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:ButtonFace;border-color:var(--mdc-checkbox-disabled-selected-checkmark-color, ButtonFace)}}/*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */@font-face{font-family:\\\"Bosch-Sans\\\";font-weight:400;src:url(\\\"..//fonts/BoschSans-Regular.eot\\\");src:url(\\\"..//fonts/BoschSans-Regular.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Regular.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Regular.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Regular.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Regular.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:\\\"Bosch-Sans\\\";font-weight:300;src:url(\\\"..//fonts/BoschSans-Light.eot\\\");src:url(\\\"..//fonts/BoschSans-Light.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Light.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Light.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Light.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-LightItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-LightItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-LightItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Light.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:\\\"Bosch-Sans\\\";font-weight:500;src:url(\\\"..//fonts/BoschSans-Medium.eot\\\");src:url(\\\"..//fonts/BoschSans-Medium.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Medium.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Medium.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Medium.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Medium.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:\\\"Bosch-Sans\\\";font-weight:700;src:url(\\\"..//fonts/BoschSans-Bold.eot\\\");src:url(\\\"..//fonts/BoschSans-Bold.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Bold.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Bold.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Bold.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Bold.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:\\\"Bosch-Sans\\\";font-weight:900;src:url(\\\"..//fonts/BoschSans-Black.eot\\\");src:url(\\\"..//fonts/BoschSans-Black.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Black.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Black.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Black.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Black.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url(\\\"..//fonts/Bosch-Icon.eot?mh5qa9\\\");src:url(\\\"..//fonts/Bosch-Icon.eot?mh5qa9#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/Bosch-Icon.ttf?mh5qa9\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/Bosch-Icon.woff?mh5qa9\\\") format(\\\"woff\\\"), url(\\\"..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon\\\") format(\\\"svg\\\")}@font-face{font-family:\\\"Bosch-Sans-Condensed\\\";font-weight:300;src:url(\\\"..//fonts/BoschSansCond-Regular.otf\\\"), url(\\\"..//fonts/BoschSansCondensed-Regular.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSansCondensed-RegularItalic.ttf\\\") format(\\\"truetype\\\")}@font-face{font-family:\\\"Bosch-Sans-Condensed\\\";font-weight:700;src:url(\\\"..//fonts/BoschSansCond-Bold.otf\\\"), url(\\\"..//fonts/BoschSansCondensed-Bold.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSansCondensed-BoldItalic.ttf\\\") format(\\\"truetype\\\")}/*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */.checkbox-input{width:24px}.mdc-checkbox{padding:calc((0 - 18px) / 2);padding:calc((var(--mdc-checkbox-ripple-size, 0) - 18px) / 2);padding:0;height:initial;width:initial;flex-basis:24px}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:#d0d4d8;border-color:var(--mdc-checkbox-unchecked-color, #d0d4d8);background-color:#eff1f2}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:#005587;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587));background-color:#005587;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587))}@keyframes mdc-checkbox-fade-in-background-********************************{0%{border-color:#d0d4d8;border-color:var(--mdc-checkbox-unchecked-color, #d0d4d8);background-color:#eff1f2}50%{border-color:#005587;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587));background-color:#005587;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587))}}@keyframes mdc-checkbox-fade-out-background-********************************{0%,80%{border-color:#005587;border-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587));background-color:#005587;background-color:var(--mdc-checkbox-checked-color, var(--mdc-theme-secondary, #005587))}100%{border-color:#d0d4d8;border-color:var(--mdc-checkbox-unchecked-color, #d0d4d8);background-color:#eff1f2}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-********************************}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-********************************}.mdc-checkbox .mdc-checkbox__background{top:calc((0 - 18px) / 2);top:calc((var(--mdc-checkbox-ripple-size, 0) - 18px) / 2);left:calc((0 - 18px) / 2);left:calc((var(--mdc-checkbox-ripple-size, 0) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{position:relative;height:24px;width:24px;min-width:24px}.mdc-checkbox .mdc-checkbox__background{border-radius:0;top:0;left:0;width:24px;height:24px}\";\nconst CheckboxInput = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.checkedChange = createEvent(this, \"checkedChange\", 7);\n    this.indicationChange = createEvent(this, \"indicationChange\", 7);\n    this.checkboxId = undefined;\n    this.checked = undefined;\n    this.indeterminate = undefined;\n    this.indication = undefined;\n    this.preventDefaultOnClickAction = false;\n  }\n  get internalIndication() {\n    return this.indication ? {\n      checked: this.indication === 'checked',\n      indeterminate: this.indication === 'indeterminate'\n    } : {\n      checked: this.checked,\n      indeterminate: this.indeterminate\n    };\n  }\n  onStateChange(newState) {\n    this.indication = newState;\n  }\n  render() {\n    const {\n      checked,\n      indeterminate\n    } = this.internalIndication;\n    return h(\"div\", {\n      class: \"mdc-form-field checkbox-input\"\n    }, h(\"div\", {\n      class: \"mdc-checkbox\"\n    }, h(\"input\", {\n      \"data-test\": \"webcore.webcomponents.checkbox-input.checkbox-input-event\",\n      type: \"checkbox\",\n      class: \"mdc-checkbox__native-control\",\n      id: this.checkboxId,\n      checked: checked,\n      indeterminate: indeterminate,\n      onClick: event => this.handleClick(event)\n    }), h(\"div\", {\n      class: \"mdc-checkbox__background\"\n    }, h(\"svg\", {\n      class: \"mdc-checkbox__checkmark\",\n      viewBox: \"0 0 24 24\"\n    }, h(\"path\", {\n      class: \"mdc-checkbox__checkmark-path\",\n      fill: \"none\",\n      d: \"M1.73,12.91 8.1,19.28 22.79,4.59\"\n    })), h(\"div\", {\n      class: \"mdc-checkbox__mixedmark\"\n    })), h(\"div\", {\n      class: \"mdc-checkbox__ripple\"\n    })), h(\"label\", {\n      htmlFor: this.checkboxId\n    }, h(\"slot\", null)));\n  }\n  handleClick(event) {\n    if (this.preventDefaultOnClickAction) {\n      event.preventDefault();\n    }\n    event.stopPropagation();\n    const checked = event.target.checked;\n    this.checkedChange.emit(checked);\n    this.indicationChange.emit(checked ? 'checked' : 'unchecked');\n  }\n  static get watchers() {\n    return {\n      \"indication\": [\"onStateChange\"]\n    };\n  }\n};\nCheckboxInput.style = checkboxInputCss;\nexport { CheckboxCollection as bci_checkbox_collection, CheckboxInput as checkbox_input };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAI,aAAa;AAAA,EACf,4BAA4B;AAAA,EAC5B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AACZ;AACA,IAAI,UAAU;AAAA,EACZ,mBAAmB;AAAA,EACnB,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,uBAAuB;AAAA,EACvB,4BAA4B;AAC9B;AACA,IAAI,UAAU;AAAA,EACZ,mBAAmB;AACrB;AAwBA,IAAI;AAAA;AAAA,EAAqC,SAAU,QAAQ;AACzD,cAAUA,wBAAuB,MAAM;AACvC,aAASA,uBAAsB,SAAS;AACtC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,uBAAsB,cAAc,GAAG,OAAO,CAAC,KAAK;AACxG,YAAM,oBAAoB,QAAQ;AAClC,YAAM,wBAAwB;AAC9B,YAAM,oBAAoB;AAC1B,YAAM,4BAA4B;AAClC,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,wBAAuB,cAAc;AAAA,MACzD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,wBAAuB,WAAW;AAAA,MACtD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,wBAAuB,WAAW;AAAA,MACtD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,wBAAuB,kBAAkB;AAAA,MAC7D,KAAK,WAAY;AACf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,WAAW,WAAY;AACrB,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,yBAAyB,WAAY;AACnC,mBAAO;AAAA,UACT;AAAA,UACA,sBAAsB,WAAY;AAChC,mBAAO;AAAA,UACT;AAAA,UACA,0BAA0B,WAAY;AACpC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,uBAAsB,UAAU,OAAO,WAAY;AACjD,WAAK,oBAAoB,KAAK,oBAAoB;AAClD,WAAK,kBAAkB;AACvB,WAAK,QAAQ,SAAS,WAAW,QAAQ;AAAA,IAC3C;AACA,IAAAA,uBAAsB,UAAU,UAAU,WAAY;AACpD,mBAAa,KAAK,iBAAiB;AAAA,IACrC;AACA,IAAAA,uBAAsB,UAAU,cAAc,SAAU,UAAU;AAChE,WAAK,QAAQ,yBAAyB,QAAQ;AAC9C,UAAI,UAAU;AACZ,aAAK,QAAQ,SAAS,WAAW,QAAQ;AAAA,MAC3C,OAAO;AACL,aAAK,QAAQ,YAAY,WAAW,QAAQ;AAAA,MAC9C;AAAA,IACF;AAIA,IAAAA,uBAAsB,UAAU,qBAAqB,WAAY;AAC/D,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,2BAA2B;AACnC;AAAA,MACF;AACA,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB,WAAW,WAAY;AAC9C,cAAM,QAAQ,YAAY,MAAM,qBAAqB;AACrD,cAAM,4BAA4B;AAAA,MACpC,GAAG,QAAQ,iBAAiB;AAAA,IAC9B;AAIA,IAAAA,uBAAsB,UAAU,eAAe,WAAY;AACzD,WAAK,qBAAqB;AAAA,IAC5B;AACA,IAAAA,uBAAsB,UAAU,uBAAuB,WAAY;AACjE,UAAI,CAAC,KAAK,QAAQ,iBAAiB,GAAG;AACpC;AAAA,MACF;AACA,UAAI,WAAW,KAAK;AACpB,UAAI,WAAW,KAAK,oBAAoB;AACxC,UAAI,aAAa,UAAU;AACzB;AAAA,MACF;AACA,WAAK,kBAAkB;AACvB,UAAI,6BAA6B,QAAQ;AACzC,UAAI,WAAW,WAAW;AAC1B,UAAI,aAAa,4BAA4B;AAC3C,aAAK,QAAQ,YAAY,QAAQ;AAAA,MACnC,OAAO;AACL,aAAK,QAAQ,SAAS,QAAQ;AAAA,MAChC;AAGA,UAAI,KAAK,sBAAsB,SAAS,GAAG;AACzC,qBAAa,KAAK,iBAAiB;AACnC,aAAK,QAAQ,YAAY;AACzB,aAAK,QAAQ,YAAY,KAAK,qBAAqB;AAAA,MACrD;AACA,WAAK,wBAAwB,KAAK,4BAA4B,UAAU,QAAQ;AAChF,WAAK,oBAAoB;AAGzB,UAAI,KAAK,QAAQ,gBAAgB,KAAK,KAAK,sBAAsB,SAAS,GAAG;AAC3E,aAAK,QAAQ,SAAS,KAAK,qBAAqB;AAChD,aAAK,4BAA4B;AAAA,MACnC;AAAA,IACF;AACA,IAAAA,uBAAsB,UAAU,sBAAsB,WAAY;AAChE,UAAI,iCAAiC,QAAQ,gCAC3C,2BAA2B,QAAQ,0BACnC,6BAA6B,QAAQ;AACvC,UAAI,KAAK,QAAQ,gBAAgB,GAAG;AAClC,eAAO;AAAA,MACT;AACA,aAAO,KAAK,QAAQ,UAAU,IAAI,2BAA2B;AAAA,IAC/D;AACA,IAAAA,uBAAsB,UAAU,8BAA8B,SAAU,UAAU,UAAU;AAC1F,UAAI,wBAAwB,QAAQ,uBAClC,2BAA2B,QAAQ,0BACnC,6BAA6B,QAAQ;AACvC,UAAI,KAAKA,uBAAsB,YAC7B,yBAAyB,GAAG,wBAC5B,+BAA+B,GAAG,8BAClC,yBAAyB,GAAG,wBAC5B,6BAA6B,GAAG,4BAChC,6BAA6B,GAAG,4BAChC,+BAA+B,GAAG;AACpC,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,cAAI,aAAa,4BAA4B;AAC3C,mBAAO;AAAA,UACT;AACA,iBAAO,aAAa,2BAA2B,6BAA6B;AAAA,QAC9E,KAAK;AACH,iBAAO,aAAa,2BAA2B,yBAAyB;AAAA,QAC1E,KAAK;AACH,iBAAO,aAAa,6BAA6B,yBAAyB;AAAA,QAC5E;AAEE,iBAAO,aAAa,2BAA2B,6BAA6B;AAAA,MAChF;AAAA,IACF;AACA,IAAAA,uBAAsB,UAAU,oBAAoB,WAAY;AAE9D,UAAI,KAAK,QAAQ,gBAAgB,GAAG;AAClC,aAAK,QAAQ,qBAAqB,QAAQ,mBAAmB,QAAQ,gCAAgC;AAAA,MACvG,OAAO;AAGL,aAAK,QAAQ,wBAAwB,QAAQ,iBAAiB;AAAA,MAChE;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI,iBAAiB,CAAC,WAAW,eAAe;AAChD,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,eAAc;AACrB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,gBAAgB,MAAM,aAAa;AACzC,aAAO;AAAA,IACT;AACA,IAAAA,aAAY,WAAW,SAAU,MAAM;AACrC,aAAO,IAAIA,aAAY,IAAI;AAAA,IAC7B;AACA,WAAO,eAAeA,aAAY,WAAW,UAAU;AAAA,MACrD,KAAK,WAAY;AACf,eAAO,KAAK;AAAA,MACd;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,aAAY,WAAW,WAAW;AAAA,MACtD,KAAK,WAAY;AACf,eAAO,KAAK,iBAAiB,EAAE;AAAA,MACjC;AAAA,MACA,KAAK,SAAU,SAAS;AACtB,aAAK,iBAAiB,EAAE,UAAU;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,aAAY,WAAW,iBAAiB;AAAA,MAC5D,KAAK,WAAY;AACf,eAAO,KAAK,iBAAiB,EAAE;AAAA,MACjC;AAAA,MACA,KAAK,SAAU,eAAe;AAC5B,aAAK,iBAAiB,EAAE,gBAAgB;AAAA,MAC1C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,aAAY,WAAW,YAAY;AAAA,MACvD,KAAK,WAAY;AACf,eAAO,KAAK,iBAAiB,EAAE;AAAA,MACjC;AAAA,MACA,KAAK,SAAU,UAAU;AACvB,aAAK,WAAW,YAAY,QAAQ;AAAA,MACtC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,aAAY,WAAW,SAAS;AAAA,MACpD,KAAK,WAAY;AACf,eAAO,KAAK,iBAAiB,EAAE;AAAA,MACjC;AAAA,MACA,KAAK,SAAU,OAAO;AACpB,aAAK,iBAAiB,EAAE,QAAQ;AAAA,MAClC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,aAAY,UAAU,aAAa,WAAY;AAC7C,UAAI,0BAA0B,QAAQ;AACtC,WAAK,iBAAiB,EAAE,gBAAgB,KAAK,iBAAiB,EAAE,aAAa,uBAAuB,MAAM;AAC1G,WAAK,iBAAiB,EAAE,gBAAgB,uBAAuB;AAAA,IACjE;AACA,IAAAA,aAAY,UAAU,qBAAqB,WAAY;AACrD,UAAI,QAAQ;AACZ,WAAK,eAAe,WAAY;AAC9B,cAAM,WAAW,aAAa;AAAA,MAChC;AACA,WAAK,qBAAqB,WAAY;AACpC,cAAM,WAAW,mBAAmB;AAAA,MACtC;AACA,WAAK,iBAAiB,EAAE,iBAAiB,UAAU,KAAK,YAAY;AACpE,WAAK,OAAO,oBAAoB,QAAQ,cAAc,GAAG,KAAK,kBAAkB;AAChF,WAAK,2BAA2B;AAAA,IAClC;AACA,IAAAA,aAAY,UAAU,UAAU,WAAY;AAC1C,WAAK,cAAc,QAAQ;AAC3B,WAAK,iBAAiB,EAAE,oBAAoB,UAAU,KAAK,YAAY;AACvE,WAAK,SAAS,oBAAoB,QAAQ,cAAc,GAAG,KAAK,kBAAkB;AAClF,WAAK,6BAA6B;AAClC,aAAO,UAAU,QAAQ,KAAK,IAAI;AAAA,IACpC;AACA,IAAAA,aAAY,UAAU,uBAAuB,WAAY;AACvD,UAAI,QAAQ;AAGZ,UAAI,UAAU;AAAA,QACZ,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,aAAa,WAAY;AACvB,iBAAO,MAAM,KAAK;AAAA,QACpB;AAAA,QACA,kBAAkB,WAAY;AAC5B,iBAAO,CAAC,CAAC,MAAM,iBAAiB;AAAA,QAClC;AAAA,QACA,iBAAiB,WAAY;AAC3B,iBAAO,QAAQ,MAAM,KAAK,UAAU;AAAA,QACtC;AAAA,QACA,WAAW,WAAY;AACrB,iBAAO,MAAM;AAAA,QACf;AAAA,QACA,iBAAiB,WAAY;AAC3B,iBAAO,MAAM;AAAA,QACf;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,gBAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QACvC;AAAA,QACA,yBAAyB,SAAU,MAAM;AACvC,gBAAM,iBAAiB,EAAE,gBAAgB,IAAI;AAAA,QAC/C;AAAA,QACA,sBAAsB,SAAU,MAAM,OAAO;AAC3C,gBAAM,iBAAiB,EAAE,aAAa,MAAM,KAAK;AAAA,QACnD;AAAA,QACA,0BAA0B,SAAU,UAAU;AAC5C,gBAAM,iBAAiB,EAAE,WAAW;AAAA,QACtC;AAAA,MACF;AACA,aAAO,IAAI,sBAAsB,OAAO;AAAA,IAC1C;AACA,IAAAA,aAAY,UAAU,eAAe,WAAY;AAC/C,UAAI,QAAQ;AAGZ,UAAI,UAAU,SAAS,SAAS,CAAC,GAAG,UAAU,cAAc,IAAI,CAAC,GAAG;AAAA,QAClE,8BAA8B,SAAU,SAAS,SAAS;AACxD,gBAAM,iBAAiB,EAAE,oBAAoB,SAAS,SAAS,aAAa,CAAC;AAAA,QAC/E;AAAA,QACA,iBAAiB,WAAY;AAC3B,iBAAO,QAAQ,MAAM,iBAAiB,GAAG,SAAS;AAAA,QACpD;AAAA,QACA,aAAa,WAAY;AACvB,iBAAO;AAAA,QACT;AAAA,QACA,4BAA4B,SAAU,SAAS,SAAS;AACtD,gBAAM,iBAAiB,EAAE,iBAAiB,SAAS,SAAS,aAAa,CAAC;AAAA,QAC5E;AAAA,MACF,CAAC;AACD,aAAO,IAAI,UAAU,KAAK,MAAM,IAAI,oBAAoB,OAAO,CAAC;AAAA,IAClE;AACA,IAAAA,aAAY,UAAU,6BAA6B,WAAY;AAC7D,UAAI,KAAK;AACT,UAAI,QAAQ;AACZ,UAAI,WAAW,KAAK,iBAAiB;AACrC,UAAI,UAAU,OAAO,eAAe,QAAQ;AAC5C,UAAI,UAAU,SAAUC,eAAc;AACpC,YAAI,OAAO,OAAO,yBAAyB,SAASA,aAAY;AAGhE,YAAI,CAAC,gBAAgB,IAAI,GAAG;AAC1B,iBAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AAEA,YAAI,eAAe,KAAK;AACxB,YAAI,eAAe;AAAA,UACjB,cAAc,KAAK;AAAA,UACnB,YAAY,KAAK;AAAA,UACjB,KAAK;AAAA,UACL,KAAK,SAAU,OAAO;AACpB,iBAAK,IAAI,KAAK,UAAU,KAAK;AAC7B,kBAAM,WAAW,aAAa;AAAA,UAChC;AAAA,QACF;AACA,eAAO,eAAe,UAAUA,eAAc,YAAY;AAAA,MAC5D;AACA,UAAI;AACF,iBAAS,mBAAmB,SAAS,cAAc,GAAG,qBAAqB,iBAAiB,KAAK,GAAG,CAAC,mBAAmB,MAAM,qBAAqB,iBAAiB,KAAK,GAAG;AAC1K,cAAI,eAAe,mBAAmB;AACtC,cAAI,UAAU,QAAQ,YAAY;AAClC,cAAI,OAAO,YAAY,SAAU,QAAO,QAAQ;AAAA,QAClD;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,sBAAsB,CAAC,mBAAmB,SAAS,KAAK,iBAAiB,QAAS,IAAG,KAAK,gBAAgB;AAAA,QAChH,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,IAAAD,aAAY,UAAU,+BAA+B,WAAY;AAC/D,UAAI,KAAK;AACT,UAAI,WAAW,KAAK,iBAAiB;AACrC,UAAI,UAAU,OAAO,eAAe,QAAQ;AAC5C,UAAI;AACF,iBAAS,mBAAmB,SAAS,cAAc,GAAG,qBAAqB,iBAAiB,KAAK,GAAG,CAAC,mBAAmB,MAAM,qBAAqB,iBAAiB,KAAK,GAAG;AAC1K,cAAI,eAAe,mBAAmB;AACtC,cAAI,OAAO,OAAO,yBAAyB,SAAS,YAAY;AAChE,cAAI,CAAC,gBAAgB,IAAI,GAAG;AAC1B;AAAA,UACF;AACA,iBAAO,eAAe,UAAU,cAAc,IAAI;AAAA,QACpD;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,sBAAsB,CAAC,mBAAmB,SAAS,KAAK,iBAAiB,QAAS,IAAG,KAAK,gBAAgB;AAAA,QAChH,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,IAAAA,aAAY,UAAU,mBAAmB,WAAY;AACnD,UAAI,0BAA0B,QAAQ;AACtC,UAAI,KAAK,KAAK,KAAK,cAAc,uBAAuB;AACxD,UAAI,CAAC,IAAI;AACP,cAAM,IAAI,MAAM,mCAAmC,0BAA0B,UAAU;AAAA,MACzF;AACA,aAAO;AAAA,IACT;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AACd,SAAS,gBAAgB,eAAe;AACtC,SAAO,CAAC,CAAC,iBAAiB,OAAO,cAAc,QAAQ;AACzD;AACA,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB,MAAM;AAAA,EAC/B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,SAAK,qBAAqB,YAAY,MAAM,sBAAsB,CAAC;AACnE,SAAK,qBAAqB,CAAC;AAC3B,SAAK,OAAO;AACZ,SAAK,OAAO,CAAC;AAAA,EACf;AAAA,EACM,oBAAoB;AAAA;AACxB,YAAM,UAAU;AAAA,IAClB;AAAA;AAAA,EACM,QAAQ;AAAA;AACZ,WAAK,GAAG,WAAW,iBAAiB,wBAAwB,EAAE,QAAQ,UAAQ,KAAK,SAAS,IAAI,KAAK;AACrG,WAAK,qBAAqB,CAAC;AAAA,IAC7B;AAAA;AAAA,EACM,sBAAsB;AAAA;AAC1B,UAAI,KAAK,SAAS,eAAe,OAAO;AACtC,aAAK,qBAAqB,KAAK,KAAK,OAAO,CAAC,aAAa,aAAa,UAAU;AAC9E,cAAI,YAAY,OAAO;AACrB,wBAAY,KAAK,YAAY,KAAK;AAClC,kBAAM,WAAW,IAAI,YAAY,KAAK,GAAG,WAAW,eAAe,kBAAkB,KAAK,EAAE,EAAE,aAAa;AAC3G,qBAAS,UAAU,YAAY;AAAA,UACjC;AACA,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AACL,aAAK,kBAAkB,KAAK,KAAK,kBAAkB;AAAA,MACrD,OAAO;AACL,cAAM,QAAQ,KAAK,KAAK,UAAU,UAAQ,KAAK,QAAQ;AACvD,cAAM,WAAW,IAAI,YAAY,KAAK,GAAG,WAAW,eAAe,mBAAmB,KAAK,EAAE,EAAE,aAAa;AAC5G,iBAAS,UAAU;AACnB,aAAK,mBAAmB,KAAK,KAAK,KAAK,KAAK,EAAE,KAAK;AAAA,MACrD;AAAA,IACF;AAAA;AAAA,EACA,yBAAyB,OAAO,QAAQ;AACtC,QAAI,MAAM,OAAO,SAAS,GAAG;AAC3B,WAAK,mBAAmB,KAAK,OAAO,KAAK;AAAA,IAC3C,OAAO;AACL,YAAM,IAAI,KAAK,mBAAmB,UAAU,OAAK,MAAM,OAAO,KAAK;AACnE,UAAI,IAAI,IAAI;AACV,aAAK,mBAAmB,OAAO,GAAG,CAAC;AAAA,MACrC;AAAA,IACF;AACA,SAAK,kBAAkB,KAAK,KAAK,kBAAkB;AAAA,EACrD;AAAA,EACA,wBAAwB,OAAO,QAAQ;AACrC,QAAI,MAAM,OAAO,SAAS,GAAG;AAC3B,WAAK,mBAAmB,KAAK,OAAO,KAAK;AAAA,IAC3C;AAEA,SAAK,GAAG,WAAW,iBAAiB,SAAS,EAAE,QAAQ,UAAQ,KAAK,SAAS,IAAI,KAAK;AACtF,UAAM,OAAO,SAAS,IAAI;AAAA,EAC5B;AAAA,EACA,cAAc,OAAO,MAAM,mBAAmB;AAC5C,QAAI,mBAAmB;AACrB,aAAO,KAAK,yBAAyB,OAAO,IAAI;AAAA,IAClD;AACA,SAAK,wBAAwB,OAAO,IAAI;AAAA,EAC1C;AAAA,EACA,eAAe,MAAM,OAAO,mBAAmB;AAC7C,WAAO,EAAE,OAAO;AAAA,MACd,aAAa;AAAA,MACb,OAAO;AAAA,MACP,MAAM,oBAAoB,KAAK;AAAA,IACjC,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,IACT,GAAG,EAAE,SAAS;AAAA,MACZ,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO,mCAAmC,oBAAoB,UAAU;AAAA,MACxE,IAAI,GAAG,oBAAoB,UAAU,QAAQ,aAAa,KAAK;AAAA,MAC/D,SAAS,WAAS,KAAK,cAAc,OAAO,MAAM,iBAAiB;AAAA,IACrE,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG,EAAE,QAAQ;AAAA,MACX,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,IACL,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACZ,OAAO;AAAA,IACT,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACZ,OAAO;AAAA,IACT,CAAC,CAAC,GAAG,EAAE,SAAS;AAAA,MACd,SAAS,GAAG,oBAAoB,UAAU,QAAQ,aAAa,KAAK;AAAA,IACtE,GAAG,UAAU,KAAK,KAAK,CAAC,CAAC;AAAA,EAC3B;AAAA,EACA,SAAS;AACP,UAAM,oBAAoB,KAAK,SAAS,eAAe;AACvD,WAAO,EAAE,MAAM,MAAM,KAAK,KAAK,IAAI,CAAC,MAAM,UAAU,KAAK,eAAe,MAAM,OAAO,iBAAiB,CAAC,CAAC;AAAA,EAC1G;AAAA,EACA,IAAI,KAAK;AACP,WAAO,WAAW,IAAI;AAAA,EACxB;AACF;AACA,mBAAmB,QAAQ;AAC3B,IAAM,mBAAmB;AACzB,IAAM,gBAAgB,MAAM;AAAA,EAC1B,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,gBAAgB,YAAY,MAAM,iBAAiB,CAAC;AACzD,SAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,8BAA8B;AAAA,EACrC;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,aAAa;AAAA,MACvB,SAAS,KAAK,eAAe;AAAA,MAC7B,eAAe,KAAK,eAAe;AAAA,IACrC,IAAI;AAAA,MACF,SAAS,KAAK;AAAA,MACd,eAAe,KAAK;AAAA,IACtB;AAAA,EACF;AAAA,EACA,cAAc,UAAU;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,IACT,GAAG,EAAE,SAAS;AAAA,MACZ,aAAa;AAAA,MACb,MAAM;AAAA,MACN,OAAO;AAAA,MACP,IAAI,KAAK;AAAA,MACT;AAAA,MACA;AAAA,MACA,SAAS,WAAS,KAAK,YAAY,KAAK;AAAA,IAC1C,CAAC,GAAG,EAAE,OAAO;AAAA,MACX,OAAO;AAAA,IACT,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG,EAAE,QAAQ;AAAA,MACX,OAAO;AAAA,MACP,MAAM;AAAA,MACN,GAAG;AAAA,IACL,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACZ,OAAO;AAAA,IACT,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACZ,OAAO;AAAA,IACT,CAAC,CAAC,GAAG,EAAE,SAAS;AAAA,MACd,SAAS,KAAK;AAAA,IAChB,GAAG,EAAE,QAAQ,IAAI,CAAC,CAAC;AAAA,EACrB;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,KAAK,6BAA6B;AACpC,YAAM,eAAe;AAAA,IACvB;AACA,UAAM,gBAAgB;AACtB,UAAM,UAAU,MAAM,OAAO;AAC7B,SAAK,cAAc,KAAK,OAAO;AAC/B,SAAK,iBAAiB,KAAK,UAAU,YAAY,WAAW;AAAA,EAC9D;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,cAAc,CAAC,eAAe;AAAA,IAChC;AAAA,EACF;AACF;AACA,cAAc,QAAQ;", "names": ["MDCCheckboxFoundation", "MDCCheckbox", "controlState"]}