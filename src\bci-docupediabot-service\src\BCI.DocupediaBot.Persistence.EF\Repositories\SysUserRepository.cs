﻿using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using BCI.DocupediaBot.Infrastructure.Repositories;

namespace BCI.DocupediaBot.Persistence.EF.Repositories
{
  public class SysUserRepository : EntityRepository<SysUser>, ISysUserRepository
  {
    public SysUserRepository(DocupediaBotDbContext dbContext) : base(dbContext)
    {
    }
  }
}
