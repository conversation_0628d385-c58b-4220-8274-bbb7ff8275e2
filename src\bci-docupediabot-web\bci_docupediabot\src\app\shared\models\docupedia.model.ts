import { ChatModel, EmbeddingModel, UpdateType } from "@shared/enums";

export interface Collection {
  id: string;
  name: string;
  comment: string;
  embeddingModel: EmbeddingModel;
  chunkSize: number;
  status: number;
  isAutomaticUpdate: boolean;
  updateType?: UpdateType | null;
  intervalNumber?: number | null;
  updateTime?: Date;
  modificationTime?: Date;
  pages: Page[];
  isExpend: boolean;
  isEmbedding: boolean;
  creator: string;
  groupIds?: string[];
}

export interface Page {
  id?: string;
  collectionId: string;
  title: string;
  url: string;
  isIncludeChild: boolean;
  sourceId?: string;
  isEmbedding?: boolean;
  contentNumber?: number;
  versionNo?: number;
  embeddingVersionNo?: number;
  modificationTime?: Date;

  urlType?: string;
  urlData?: string;
  userToken?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

export interface ChatCompletionDto {
  userMessage: string;
  collectionId: string;
  chatModel: ChatModel;
  embeddingModel: EmbeddingModel;
}

