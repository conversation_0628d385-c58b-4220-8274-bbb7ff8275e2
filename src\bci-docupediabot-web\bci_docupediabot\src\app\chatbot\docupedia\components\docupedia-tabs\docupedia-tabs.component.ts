import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NavigationEnd, Router, ActivatedRoute  } from '@angular/router';
import { filter, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-docupedia-tabs',
  templateUrl: './docupedia-tabs.component.html',
  styleUrl: './docupedia-tabs.component.scss'
})

export class DocupediaTabsComponent implements OnInit, OnDestroy {

  constructor(private router: Router, private readonly activatedRoute: ActivatedRoute) {}

  activeLink: string = '';

  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.activeLink = this.activatedRoute.firstChild?.snapshot.url[0]?.path || '';
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd), 
        takeUntil(this.destroy$) 
      )
      .subscribe((event: NavigationEnd) => {
        this.activeLink = this.activatedRoute.firstChild?.snapshot.url[0]?.path || '';
      });

  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

}

