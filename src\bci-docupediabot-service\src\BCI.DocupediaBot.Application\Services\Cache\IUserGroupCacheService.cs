using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Cache
{



  public interface IUserGroupCacheService
  {



    Task<List<Guid>?> GetUserGroupsAsync(Guid userId);




    Task SetUserGroupsAsync(Guid userId, List<Guid> groupIds, TimeSpan? expiration = null);




    Task<List<Guid>?> GetGroupUsersAsync(Guid groupId);




    Task SetGroupUsersAsync(Guid groupId, List<Guid> userIds, TimeSpan? expiration = null);




    Task<Dictionary<Guid, List<Guid>>> GetUserGroupsBatchAsync(IEnumerable<Guid> userIds);




    Task SetUserGroupsBatchAsync(Dictionary<Guid, List<Guid>> userGroups, TimeSpan? expiration = null);




    Task<Dictionary<Guid, List<Guid>>> GetGroupUsersBatchAsync(IEnumerable<Guid> groupIds);




    Task SetGroupUsersBatchAsync(Dictionary<Guid, List<Guid>> groupUsers, TimeSpan? expiration = null);




    Task<bool?> IsUserInGroupAsync(Guid userId, Guid groupId);




    Task SetUserInGroupAsync(Guid userId, Guid groupId, bool isInGroup, TimeSpan? expiration = null);




    Task ClearUserCacheAsync(Guid userId);




    Task ClearGroupCacheAsync(Guid groupId);




    Task ClearAllCacheAsync();
  }
}
