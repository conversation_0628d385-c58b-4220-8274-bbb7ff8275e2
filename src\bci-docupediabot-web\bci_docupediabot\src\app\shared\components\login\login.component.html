<div class="bg-img-container">
  <div class="container">
    <div class="form">
      <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
        <h2 class="form-title" style="margin: 0;">GENAI in BCI 2025</h2>
        <img src="./assets/img/chatbot.jpg" alt="Bot Icon" style="width: 100px; height: 100px;">
      </div>

      <form #loginForm="ngForm" (ngSubmit)="login()" novalidate>
        <div *ngIf="errorMsg" class="alert alert-danger">
          <span class="alert-icon Bosch-Ic Bosch-Ic-do-not-disturb"></span>
          <div class="alert-content">{{ errorMsg }}</div>
        </div>

        <mat-form-field class="full-width">
          <mat-label>User Name</mat-label>
          <input
            matInput
            name="username"
            [(ngModel)]="model.UserName"
            #username="ngModel"
            required
            placeholder="Enter your user name"
          />
          <mat-error *ngIf="loginForm.submitted && username.invalid">
            User name is required
          </mat-error>
        </mat-form-field>

        <mat-form-field class="full-width">
          <mat-label>Password</mat-label>
          <input
            type="password"
            matInput
            name="password"
            [(ngModel)]="model.Password"
            #password="ngModel"
            required
            placeholder="Enter your password"
          />
          <mat-error *ngIf="loginForm.submitted && password.invalid">
            Password is required
          </mat-error>
        </mat-form-field>

        <div class="form-actions">
          <div class="checkbox-wrapper">
            <mat-checkbox
              name="isReadAgree"
              [(ngModel)]="model.isReadAgree"
              #isReadAgree="ngModel"
            >
              Accept
            </mat-checkbox>
          </div>
          <a class="policy-link" (click)="openPolicy()">Private Policy</a>
          <div class="button-wrapper">
            <button
              mat-raised-button
              color="primary"
              type="submit"
              [disabled]="loading"
            >
              LOGIN
              <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
