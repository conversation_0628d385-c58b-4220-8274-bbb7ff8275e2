﻿using Azure;
using Azure.AI.OpenAI;
using BCI.DocupediaBot.Application.Contracts.Dtos.Embedding;
using BCI.DocupediaBot.Domain.Enums;
using BCI.DocupediaBot.Infrastructure.Configuration;
using BCI.DocupediaBot.Application.Utilities;
using Microsoft.Extensions.Logging;
using OpenAI.Chat;
using OpenAI.Embeddings;
using SharpToken;
using System;
using System.ClientModel.Primitives;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.LLM
{
  public class LLMService : ILLMService
	{

		private readonly ILogger<LLMService> _logger;
		private readonly IHttpClientFactory _httpClientFactory;

		public LLMService(
				IHttpClientFactory httpClientFactory,
				ILogger<LLMService> logger)
		{

			_logger = logger ?? throw new ArgumentNullException(nameof(logger));
			_httpClientFactory = httpClientFactory;

		}

		public async Task<float[][]> GetEmbeddingAsync(EmbeddingModel model, IList<string> contents)
		{
			if (contents.Count == 0)
			{
				_logger.LogWarning("GetEmbeddingAsync received null or empty content.");
				return null;
			}

			_logger.LogInformation("Generating embedding for model: {Model}", model);

			switch (model)
			{
				case EmbeddingModel.Azure:
					return await GetAzureEmbeddingAsync(contents);
				case EmbeddingModel.Ollama:
          return await GetOllamaEmbeddingAsync(contents);
        default:
					_logger.LogError("Unknown embedding model: {Model}", model);
					throw new ArgumentException($"Unsupported embedding model: {model}", nameof(model));
			}
		}

    private async Task<float[][]> GetOllamaEmbeddingAsync(IList<string> contents)
    {
      var provider = ChatbotSettings.GetModel<ModelProviderSettings>("Ollama");
      var embedding = ChatbotSettings.GetModel<EmbeddingSettings>("Ollama");

      if (provider == null || embedding == null)
      {
        _logger.LogWarning("Ollama provider or embedding settings not found in configuration.");
        return null;
      }

      var activeDeployment = embedding.Deployments[embedding.Active];
      if (activeDeployment == null)
      {
        _logger.LogWarning("Active embedding deployment '{Active}' not found for Ollama.", embedding.Active);
        return null;
      }

      try
      {
        var httpClient = _httpClientFactory.CreateClient("OllamaClient");
        var vectors = new float[contents.Count][];
        for (int i = 0; i < contents.Count; i++)
        {
          var requestBody = new
          {
            model = activeDeployment.Model.Replace("@",":"), // e.g., "bge-m3@latest"
            prompt = contents[i]
          };

          var jsonRequest = JsonSerializer.Serialize(requestBody);
          var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");
          var response = await httpClient.PostAsync("/api/embeddings", content);
          response.EnsureSuccessStatusCode();

          var jsonResponse = await response.Content.ReadAsStringAsync();
          var embeddingResponse = JsonSerializer.Deserialize<OllamaEmbeddingResponse>(jsonResponse);

          if (embeddingResponse?.Embedding == null || embeddingResponse.Embedding.Length == 0)
          {
            _logger.LogWarning("No embedding returned for content index {Index} from Ollama API.", i);
            return null;
          }

          vectors[i] = embeddingResponse.Embedding;
        }

        _logger.LogInformation("Successfully retrieved {Count} embeddings from Ollama.", contents.Count);
        return vectors;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to retrieve embeddings from Ollama API.");
        return null;
      }
    }

    private async Task<float[][]> GetAzureEmbeddingAsync(IList<string> contents)
    {
      var provider = ChatbotSettings.GetModel<ModelProviderSettings>("azure");
      var embedding = ChatbotSettings.GetModel<EmbeddingSettings>("azure");

      if (provider == null || embedding == null)
      {
        _logger.LogWarning("Azure provider or embedding settings not found in configuration.");
        return Array.Empty<float[]>();
      }

      var deployment = embedding.Deployments[embedding.Active];
      if (deployment == null)
      {
        _logger.LogWarning("Active embedding deployment '{Active}' not found for Azure.", embedding.Active);
        return Array.Empty<float[]>();
      }

      IList<string> newContents = new List<string>();
      var chunkMapping = new List<(int OriginalIndex, List<int> ChunkIndices)>();

      for (int originalIndex = 0; originalIndex < contents.Count; originalIndex++)
      {
        var content = contents[originalIndex];
        int tokenSize = LLMTokenUtility.CountTokens(content);
        var currentChunkIndices = new List<int>();

        if (tokenSize <= deployment.MaxTokens)
        {
          newContents.Add(content);
          currentChunkIndices.Add(newContents.Count - 1);
        }
        else
        {
          int chunkCount = (int)Math.Ceiling((double)tokenSize / deployment.MaxTokens);
          int charPerChunk = (int)Math.Ceiling((double)content.Length / chunkCount);

          for (int i = 0; i < content.Length; i += charPerChunk)
          {
            int length = Math.Min(charPerChunk, content.Length - i);
            string subChunk = content.Substring(i, length);
            newContents.Add(subChunk);
            currentChunkIndices.Add(newContents.Count - 1);
          }
        }

        chunkMapping.Add((originalIndex, currentChunkIndices));
      }

      try
      {
        AzureOpenAIClient azureClient = CreateAzureOpenAIClient(provider);
        EmbeddingClient embeddingClient = azureClient.GetEmbeddingClient(deployment.DeploymentName);
        OpenAIEmbeddingCollection response = await embeddingClient.GenerateEmbeddingsAsync(newContents);

        if (response != null && response.Count > 0)
        {
          var vectors = new float[contents.Count][];

          foreach (var mapping in chunkMapping)
          {
            int originalIndex = mapping.OriginalIndex;
            var chunkIndices = mapping.ChunkIndices;

            var chunkEmbeddings = chunkIndices
                .Select(idx => response[idx].ToFloats().ToArray())
                .ToList();

            vectors[originalIndex] = AverageEmbeddings(chunkEmbeddings);
          }

          _logger.LogInformation("Successfully retrieved and merged embeddings from Azure.");
          return vectors;
        }

        _logger.LogWarning("No embeddings returned from Azure API.");
        return Array.Empty<float[]>();
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to retrieve embeddings from Azure API.");
        return Array.Empty<float[]>();
      }
    }


    private float[] AverageEmbeddings(IList<float[]> embeddings)
    {
      if (embeddings.Count == 0)
        return Array.Empty<float>();

      int dimension = embeddings[0].Length;
      var result = new float[dimension];

      for (int i = 0; i < dimension; i++)
      {

        result[i] = embeddings.Average(emb => emb[i]);
      }

      return result;
    }



    private AzureOpenAIClient CreateAzureOpenAIClient(ModelProviderSettings provider)
    {
      var httpClient = _httpClientFactory.CreateClient("AzureClient");

      AzureOpenAIClientOptions options = new AzureOpenAIClientOptions();
      options.Transport = new HttpClientPipelineTransport(httpClient);

      AzureOpenAIClient azureClient = new(
          endpoint: new Uri(provider.Endpoint),
          credential: new AzureKeyCredential(provider.ApiKey),
          options: options
      );
      return azureClient;
    }

    public async Task<string> GetCompletionAsync(string prompt, ChatModel model)
    {
      if (string.IsNullOrEmpty(prompt))
      {
        _logger.LogWarning("GetCompletionAsync received null or empty prompt.");
        return string.Empty;
      }

      _logger.LogInformation("Generating completion for model: {Model}", model);

      switch (model)
      {
        case ChatModel.Azure:
          return await GetAzureCompletionAsync(prompt);
        case ChatModel.Ollama:
          return await GetOllamaCompletionAsync(prompt);
        default:
          _logger.LogError("Unknown chat model: {Model}", model);
          throw new ArgumentException($"Unsupported chat model: {model}", nameof(model));
      }
    }

    private async Task<string> GetAzureCompletionAsync(string prompt)
    {
      var provider = ChatbotSettings.GetModel<ModelProviderSettings>("azure");
      var completion = ChatbotSettings.GetModel<CompletionSettings>("azure");

      if (provider == null || completion == null)
      {
        _logger.LogWarning("Azure provider or completion settings not found in configuration.");
        return string.Empty;
      }

      var deployment = completion.Deployments[completion.Active];
      if (deployment == null)
      {
        _logger.LogWarning("Active completion deployment '{Active}' not found for Azure.", completion.Active);
        return string.Empty;
      }

      try
      {
        AzureOpenAIClient azureClient = CreateAzureOpenAIClient(provider);
        ChatClient chatClient = azureClient.GetChatClient(deployment.DeploymentName);

        var requestOptions = new ChatCompletionOptions()
        {
          MaxOutputTokenCount = deployment.MaxOutputTokens,
          Temperature = (float)deployment.Temperature,
          TopP = (float)deployment.TopP,
        };

        List<ChatMessage> messages = new()
        {
            new SystemChatMessage("You are a professional assistant of BCI knowledge base."),
            new UserChatMessage(prompt)
        };

        var response = await chatClient.CompleteChatAsync(messages, requestOptions);
        var result = response.Value.Content.FirstOrDefault()?.Text;

        if (string.IsNullOrEmpty(result))
        {
          _logger.LogWarning("No completion returned from Azure API.");
          return string.Empty;
        }

        _logger.LogInformation("Successfully retrieved completion from Azure.");
        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to retrieve completion from Azure API.");
        return string.Empty;
      }
    }

    private async Task<string> GetOllamaCompletionAsync(string prompt)
    {
      var provider = ChatbotSettings.GetModel<ModelProviderSettings>("Ollama");
      var completion = ChatbotSettings.GetModel<CompletionSettings>("Ollama");

      if (provider == null || completion == null)
      {
        _logger.LogWarning("Ollama provider or completion settings not found in configuration.");
        return string.Empty;
      }

      var deployment = completion.Deployments[completion.Active];
      if (deployment == null)
      {
        _logger.LogWarning("Active completion deployment '{Active}' not found for Ollama.", completion.Active);
        return string.Empty;
      }

      try
      {
        var httpClient = _httpClientFactory.CreateClient("OllamaClient");
        var requestBody = new
        {
          model = deployment.Model.Replace("@", ":"),
          messages = new[]
            {
                new { role = "system", content = "You are a professional assistant of BCI knowledge base." },
                new { role = "user", content = prompt }
            },
          temperature = deployment.Temperature,
          top_p = deployment.TopP,
          max_tokens = deployment.MaxOutputTokens,
          stream = false
        };

        var jsonRequest = JsonSerializer.Serialize(requestBody);
        var content = new StringContent(jsonRequest, Encoding.UTF8, "application/json");

        var response = await httpClient.PostAsync("/api/chat", content);
        response.EnsureSuccessStatusCode();

        var jsonResponse = await response.Content.ReadAsStringAsync();
        var completionResponse = JsonSerializer.Deserialize<OllamaChatResponse>(jsonResponse);

        var result = completionResponse?.Message?.Content;
        if (string.IsNullOrEmpty(result))
        {
          _logger.LogWarning("No completion returned from Ollama API.");
          return string.Empty;
        }

        _logger.LogInformation("Successfully retrieved completion from Ollama.");
        return result;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to retrieve completion from Ollama API.");
        return string.Empty;
      }
    }
  }
}