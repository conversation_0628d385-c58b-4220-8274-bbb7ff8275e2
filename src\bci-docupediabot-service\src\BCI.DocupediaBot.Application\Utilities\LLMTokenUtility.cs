using BCI.DocupediaBot.Domain.Enums;
using BCI.DocupediaBot.Infrastructure.Configuration;
using SharpToken;
using System;
using System.Text;

namespace BCI.DocupediaBot.Application.Utilities
{
  public static class LLMTokenUtility
  {
    public static int CountTokens(string text)
    {
      if (string.IsNullOrEmpty(text))
        return 0;

      var encoding = GptEncoding.GetEncodingForModel("gpt-4");
      var count = encoding.CountTokens(text);

      return count;
    }

    public static int GetMaxInputTokensForModel(ChatModel chatModel)
    {
      var modelSettings = ChatbotSettings.Model;

      switch (chatModel)
      {
        case ChatModel.Azure:
          var azureProvider = modelSettings.Providers["Azure"];
          var azureDeployment = azureProvider.Completion.Deployments[azureProvider.Completion.Active];
          return azureDeployment.MaxInputTokens;

        case ChatModel.Ollama:
          var ollamaProvider = modelSettings.Providers["Ollama"];
          var ollamaDeployment = ollamaProvider.Completion.Deployments[ollamaProvider.Completion.Active];
          return ollamaDeployment.MaxInputTokens;

        default:
          throw new ArgumentException($"Unsupported chat model: {chatModel}", nameof(chatModel));
      }
    }

    public static int GetMaxOutputTokensForModel(ChatModel chatModel)
    {
      var modelSettings = ChatbotSettings.Model;

      switch (chatModel)
      {
        case ChatModel.Azure:
          var azureProvider = modelSettings.Providers["Azure"];
          var azureDeployment = azureProvider.Completion.Deployments[azureProvider.Completion.Active];
          return azureDeployment.MaxOutputTokens;

        case ChatModel.Ollama:
          var ollamaProvider = modelSettings.Providers["Ollama"];
          var ollamaDeployment = ollamaProvider.Completion.Deployments[ollamaProvider.Completion.Active];
          return ollamaDeployment.MaxOutputTokens;

        default:
          throw new ArgumentException($"Unsupported chat model: {chatModel}", nameof(chatModel));
      }
    }

    public static int GetAllowedInputTokens(ChatModel chatModel)
    {
      int maxInputTokens = GetMaxInputTokensForModel(chatModel);
      double reserveRatio = ChatbotSettings.QueryOptions.InputTokenReserveRatio;

      return (int)(maxInputTokens * reserveRatio);
    }

    public static bool ExceedsTokenLimit(string text, int maxTokens)
    {
      return CountTokens(text) > maxTokens;
    }

    public static string SummarizeContent(string content, int targetTokens)
    {
      if (string.IsNullOrEmpty(content))
        return content;

      int currentTokens = CountTokens(content);
      if (currentTokens <= targetTokens)
        return content;


      var paragraphs = content.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
      var result = new StringBuilder();
      int accumulatedTokens = 0;

      foreach (var paragraph in paragraphs)
      {
        int paragraphTokens = CountTokens(paragraph);

        if (accumulatedTokens + paragraphTokens <= targetTokens)
        {
          result.AppendLine(paragraph);
          accumulatedTokens += paragraphTokens;
        }
        else
        {

          if (accumulatedTokens == 0 && paragraphTokens > targetTokens)
          {
            string truncated = TruncateToTokenLimit(paragraph, targetTokens);
            result.AppendLine(truncated);
          }
          break;
        }
      }

      return result.ToString().Trim();
    }

    /// <summary>

    /// </summary>



    private static string TruncateToTokenLimit(string text, int maxTokens)
    {
      if (string.IsNullOrEmpty(text))
        return text;



      int estimatedChars = (int)(maxTokens * 1.5);

      if (text.Length <= estimatedChars)
        return text;


      string truncated = text.Substring(0, Math.Min(estimatedChars, text.Length));


      while (CountTokens(truncated) > maxTokens && truncated.Length > 0)
      {
        truncated = truncated.Substring(0, (int)(truncated.Length * 0.9));
      }

      return truncated + "...";
    }
  }
}
