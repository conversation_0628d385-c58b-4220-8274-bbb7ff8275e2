﻿using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using BCI.DocupediaBot.Infrastructure.Repositories;

namespace BCI.DocupediaBot.Persistence.EF.Repositories
{
  public class ChatHistoryRepository : EntityRepository<ChatHistory>, IChatHistoryRepository
  {
    public ChatHistoryRepository(DocupediaBotDbContext dbContext) : base(dbContext)
    {
    }
  }
}
