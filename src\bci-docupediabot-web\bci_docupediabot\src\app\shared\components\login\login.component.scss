@import "@bci-web-core/common-styles/sass/bci/bci-variables";

:host {
  display: flex;
  flex: 1;
  min-height: 100vh;
}

.bg-img-container {
  flex: 1;
  min-height: 100vh;
  background: url('/assets/img/supergraphic_dark_blue.svg') no-repeat center center fixed, #0080bc;
  background-size: cover;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  width: 30%;
  min-width: 400px;
  max-width: 500px;
  margin: 0 auto;
}

.form {
  padding: 2.5rem;
  background: #fff;
  color: #666;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-title {
  color: #0080bc;
  text-align: center;
  margin: 0 0 2rem;
  font-size: 1.2rem;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  font-weight: 600;
}

.full-width {
  width: 100%;
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1.5rem;
  width: 100%;
  gap: 10px;
}

.checkbox-wrapper {
  flex: 1;
  text-align: left;
  display: flex;
  align-items: center;
}

.checkbox-wrapper mat-checkbox {
  width: auto;
  padding: 0;
  margin: 0;
  position: relative;
  z-index: 1;
}

.form-actions .policy-link {
  flex: 1;
  text-align: center;
  color: #0080bc;
  text-decoration: underline;
  cursor: pointer;
  position: relative;
  z-index: 2;
  font-size: 0.8rem;
}

.form-actions .policy-link:hover {
  color: darken(#0080bc, 10%);
}

.button-wrapper {
  flex: 1;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.button-wrapper button {
  width: auto;
  min-width: 120px;
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  position: relative;
  z-index: 1;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  font-size: 18px;
  font-weight: bolder;
}

.alert {
  margin-bottom: 1rem;
}

.form-actions * {
  box-sizing: border-box;
}

mat-checkbox .mat-checkbox-inner-container {
  width: auto;
  min-width: 20px;
  margin: 0;
}

mat-checkbox .mat-checkbox-label {
  padding-right: 8px;
}
