import { Component, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup } from '@angular/forms';
import { AuthService } from '@shared/services/auth.service';
import { SysUserUpdate, SysUserResponseDTO } from '@shared/models/system.model';
import { Collection } from '@shared/models/docupedia.model';
import { Observable } from 'rxjs';
import { SysUserService } from '@shared/services/system/sys-user.service';
import { CollectionService } from '@shared/services/docupedia/collection.service';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss']
})
export class UserProfileComponent implements OnInit {
  userProfileForm!: FormGroup;
  submitDisabled = true;
  collections: Collection[] = [];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private sysUserService: SysUserService,
    private collectionService: CollectionService,
    private dialogRef: MatDialogRef<UserProfileComponent>
  ) {}

  ngOnInit(): void {
    const currentUser = this.authService.getCurrentUser();


    this.userProfileForm = this.fb.group({
      id: [{ value: currentUser?.id || '', disabled: true }],
      userNTAccount: [{ value: currentUser?.userNTAccount || '', disabled: true }],
      userName: [{ value: currentUser?.userName || '', disabled: true }],
      givenName: [{ value: currentUser?.givenName || '', disabled: true }],
      sn: [{ value: currentUser?.sn || '', disabled: true }],
      mail: [{ value: currentUser?.mail || '', disabled: true }],
      department: [{ value: currentUser?.department || '', disabled: true }],
      favCollecitonId: [currentUser?.favCollecitonId || ''],
      status: [currentUser?.status || 1]
    });


    this.collectionService.getCollections().subscribe({
      next: (collections) => {

        this.collections = collections.filter(collection =>
          collection.status === 1 && collection.isEmbedding === true
        );
      },
      error: (error) => {
        console.error('Failed to load collections', error);
      }
    });


    this.userProfileForm.valueChanges.subscribe(() => this.updateSubmitDisabled());
    this.updateSubmitDisabled();
  }

  private updateSubmitDisabled(): void {
    this.submitDisabled = this.userProfileForm.invalid;
  }

  save(): void {
    if (this.submitDisabled) return;

    const updatedUser: SysUserUpdate = {
      id: this.userProfileForm.get('id')?.value,
      userNTAccount: this.userProfileForm.get('userNTAccount')?.value,
      userName: this.userProfileForm.get('userName')?.value,
      givenName: this.userProfileForm.get('givenName')?.value,
      sn: this.userProfileForm.get('sn')?.value,
      mail: this.userProfileForm.get('mail')?.value,
      department: this.userProfileForm.get('department')?.value,
      favCollecitonId: this.userProfileForm.get('favCollecitonId')?.value,
      docupediaToken: this.authService.getCurrentUser()?.docupediaToken || '',
      status: this.userProfileForm.get('status')?.value
    };

    this.sysUserService.updateUser(updatedUser).subscribe({
      next: () => {

        const updatedUserDTO: SysUserResponseDTO = {
          ...this.authService.getCurrentUser()!,
          favCollecitonId: updatedUser.favCollecitonId
        };
        this.authService.updateCurrentUser(updatedUserDTO);
        this.dialogRef.close(updatedUser);
      },
      error: (error) => {
        console.error('Failed to update user profile', error);
      }
    });
  }

  close(): void {
    this.dialogRef.close();
  }
}
