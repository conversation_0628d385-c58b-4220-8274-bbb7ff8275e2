﻿using BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup;
using System;
using System.Collections.Generic;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.SysUser
{
  public class SysUserResponseDTO
  {
    public Guid Id { get; set; }
    public string UserNTAccount { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string GivenName { get; set; } = string.Empty;
    public string SN { get; set; } = string.Empty;
    public string Mail { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public Guid FavCollecitonId { get; set; }
    public string DocupediaToken { get; set; } = string.Empty;
    public int Status { get; set; }
    public List<SysGroupResponseDTO> Groups { get; set; } = new List<SysGroupResponseDTO>();
  }
}