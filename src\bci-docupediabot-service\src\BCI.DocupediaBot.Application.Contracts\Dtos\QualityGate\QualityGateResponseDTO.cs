using System.Collections.Generic;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.QualityGate
{
    public class QualityGateResponseDTO
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<CopiedPageInfo> CopiedPages { get; set; } = new List<CopiedPageInfo>();
    }

    public class CopiedPageInfo
    {
        public string OriginalTitle { get; set; } = string.Empty;
        public string NewTitle { get; set; } = string.Empty;
        public string NewUrl { get; set; } = string.Empty;
        public string NewPageId { get; set; } = string.Empty;
    }

    // Helper classes for JSON deserialization
    public class ChildPagesResult
    {
        public List<ChildPageInfo> Results { get; set; } = new List<ChildPageInfo>();
    }

    public class ChildPageInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
    }

    public class PageContentResult
    {
        public PageBody? Body { get; set; }
    }

    public class PageBody
    {
        public PageStorage? Storage { get; set; }
    }

    public class PageStorage
    {
        public string Value { get; set; } = string.Empty;
    }

    public class CreatePageResult
    {
        public string Id { get; set; } = string.Empty;
    }
}
