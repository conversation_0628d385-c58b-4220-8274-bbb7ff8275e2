# Placeholder
1. ModuleName
Unique name under a whole IAS deployment. The ***module_name*** of directory should also be matched with the name in your release pipeline.
2. context-path
A suffix to add to namespaces generated for this module. If there is only one IAS release in a cluster, the namespace will be just this value.
3. Module Display Name
The display name of the module in MACMA
4. HELM_Folder_Name
It will reuse the parameters ModuleName

# How To
Replace the placeholders in the files under ***deploy*** folder with your customized values one by one
# Image name in pipelines
1. Service image name in the values.yaml is defined with value ModuleName/application:1.0.0-dev, so please make this value matchs the definition in pipelines.
2. Helm image name will combine the value ModuleName in the Chart.yaml file. e.g. if you set Project Name with 'example' in task BCI Helm Push, the full path of Helm iamge will be 'example/helmchart/ModuleName'
# Roles and Resources
You should update two files under folder 'roles-and-resources' according to your definitions. The default role which is preseted in the values.yaml will be also influenced after your update.
# Example cmd
*dotnet new BCI.DocupediaBot.Service --name MyNewProject --HELM_Module_Name MyNewProject --HELM_Context_Path ste --HELM_Module_Display_Name MyNewProject_Demo --HELM_Folder_Name MyNewProject*