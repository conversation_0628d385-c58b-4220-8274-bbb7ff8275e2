﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class ContentsInPageConfiguration : IEntityTypeConfiguration<ContentsInPage>
  {
    public void Configure(EntityTypeBuilder<ContentsInPage> entityBuilder)
    {
      entityBuilder.ToTable(nameof(ContentsInPage));


      entityBuilder.HasIndex(x => x.PageId);
      entityBuilder.HasIndex(x => x.ContentId);
      entityBuilder.HasIndex(x => new { x.PageId, x.ContentId }).IsUnique();


      entityBuilder.HasIndex(x => new { x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.PageId, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.ContentId, x.TenantId, x.Is<PERSON>eleted });
    }
  }
}
