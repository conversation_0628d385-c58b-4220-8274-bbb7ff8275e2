{
  "DatabaseProvider": "pgsql",
  "ConnectionStrings": {
    //"Oracle": "DATA SOURCE=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=ORCLPDB1)));;MIN POOL SIZE=3;USER ID=****;PASSWORD=****;VALIDATE CONNECTION=True;HA EVENTS=True",
    //"MSSQL": "Encrypt=True;TrustServerCertificate=True;Server=SZH-C-0032V\\SQLEXPRESS;Database=DocupediaBot;Trusted_Connection=SSPI",
    //"PgSql": "Host=localhost;Database=DocupediaBot;Username=postgres;Password=***********",
    "PgSql": "Host=*************;Database=DocupediaBot;Username=postgres;Password=***********"
  },
  "ConnectionString": "Encrypt=True;TrustServerCertificate=True;Persist Security Info = False; User ID =sa; Password =Password@2021; Initial Catalog =WidgetEngine; Server =*************",
  "BasePath": "",
  "ElasticApm": {
    "Enabled": false
  },
  "OIDC": {
    "ServiceUrl": "https://si0vmc4403.de.bosch.com/iam",
    "RequireHttps": true,
    "ClientId": "ooidie4hxuiv1eukjlhlzesm9",
    "ClientSecret": "WidgetDemo11",

    "Acl": {
      "RefreshInterval": "60"
    },
    "Introspection": {
      "EnableCaching": false,
      "CachingDuration": 30
    },
    "NamedHttpClients": {
      "DefaultTokenEndpoint": "https://si0vmc4403.de.bosch.com/iam/auth/realms/7311ea8c-5d48-43fe-acf9-980eedf24b6c/protocol/openid-connect/token",
      "Clients": {
        "MacmaClient": {
          "Scopes": [ "aud:macma" ]
        },
        "PortalClient": {
          "Scopes": [ "aud:1k1ryinq4orxuznr9exs8zq39" ]
        },
        "UserManagementClient": {
          "Scopes": [ "aud:macma" ]
        }
      }
    }
  },
  "TENANTCONFIG": {
    "Tenant0": "7311ea8c-5d48-43fe-acf9-980eedf24b6c"
  },
  "Portal": {
    "TenantId": "7311ea8c-5d48-43fe-acf9-980eedf24b6c",
    "Url": "https://si0vmc4403.de.bosch.com",
    "Info": {
      "Id": "ooidie4hxuiv1eukjlhlzesm9",
      "Name": "Widget Builder",
      "Vendor": "ESW",
      "Version": "4.7.0",
      "BaseUrl": "http://localhost:4200",
      "SupportedLanguages": [ "de", "en", "zh" ],
      "AuthProviderClientId": "ooidie4hxuiv1eukjlhlzesm9"
    },
    "Views": {
      "TopLevelEntries": [
        {
          "Title": "Widget Builder",
          "ResourceId": "urn.com.bosch.nexeed.bmlp.widgeteng",
          "ResourceType": "view",
          "NavigationPriority": 1,
          "NavigationPath": [ "widgetbuilderdemo" ],
          "Icon": "bosch-ic-core-data",
          "Localization": {
            "Title": {
              "de": "Widget Builder",
              "en": "Widget Builder",
              "zh": "Widget Builder"
            },
            "Tags": {
              "de": [ "Widget Builder", "Widget Builder" ],
              "en": [ "Widget Builder", "Widget Builder" ],
              "zh": [ "Widget Builder", "Widget Builder" ]
            }
          }
        }
      ],
      "IFrameSubViews": [
        {
          "Title": "Widget Market",
          "HtmlFile": "/widget-market?tenantId=##tenantId##",
          "ResourceId": "urn.com.bosch.nexeed.bmlp.widgeteng.widget.market",
          "ResourceType": "view",
          "NavigationPriority": 1,
          "NavigationPath": [ "widgetbuilderdemo", "widget-market" ],
          "NavigationRoute": "/widget-market",
          "Localization": {
            "Title": {
              "de": "Widget Market",
              "en": "Widget Market",
              "zh": "Widget Market"
            },
            "Tags": {
              "de": [ "Widget Builder", "Widget Market" ],
              "en": [ "Widget Builder", "Widget Market" ],
              "zh": [ "Widget Builder", "Widget Market" ]
            }
          }
        },
        {
          "Title": "Datasource Configuration",
          "HtmlFile": "/datasource-config?tenantId=##tenantId##",
          "ResourceId": "urn.com.bosch.nexeed.bmlp.widgeteng.datasource.configuration",
          "ResourceType": "view",
          "NavigationPriority": 2,
          "NavigationPath": [ "widgetbuilderdemo", "datasource-config" ],
          "NavigationRoute": "/datasource-config",
          "Localization": {
            "Title": {
              "de": "Datasource Configuration",
              "en": "Datasource Configuration",
              "zh": "Datasource Configuration"
            },
            "Tags": {
              "de": [ "Widget Builder", "Datasource Configuration" ],
              "en": [ "Widget Builder", "Datasource Configuration" ],
              "zh": [ "Widget Builder", "Datasource Configuration" ]
            }
          }
        }
      ]
    },
    "Docs": {
      "items": [
      ]
    },
    "Widgets": {
      "items": [
        {
          "id": "09232d7e-9627-4ada-b145-bd5c7eab942e",
          "catalog": {
            "title": "widget demo1",
            "categoryId": "cat_others",
            "description": "Displays the demo widget.",
            "imageUrl": "ui/assets/img/blank-widget.png"
          },
          "resourceDynamic": false,
          "resourceId": "urn.com.bosch.nexeed.bmlp.widgeteng",
          "resourceType": "view",
          "widgetType": "IFrame",
          "configuration": {
            "availableWidths": [
              3,
              6,
              9,
              12
            ],
            "availableHeights": [
              2,
              3,
              4,
              5,
              6,
              7
            ]
          },
          "settings": {
            "widgetUrl": "http://localhost:5395/rendering/widgets/NumberText?widgetid=372b6744-a318-40a6-8d93-1aee42408b40&_tenantid=7311ea8c-5d48-43fe-acf9-980eedf24b6c&_basepath=&_name=APOPLN_NT",
            "widgetConfigUrl": ""
          },
          "localization": {
            "catalogTitle": {
              "de": "widget demo1",
              "en": "widget demo1"
            },
            "catalogDescription": {
              "de": "Widget Demo.",
              "en": "Widget Demo."
            }
          }
        },
        {
          "id": "f92f9503-88f8-4322-6b88-fda524e2cb18",
          "catalog": {
            "title": "widget demo2",
            "categoryId": "cat_others",
            "description": "Displays the demo widget.",
            "imageUrl": "ui/assets/img/blank-widget.png"
          },
          "resourceOwingTenant": "7311ea8c-5d48-43fe-acf9-980eedf24b6c",
          "resourceDynamic": true,
          "resourceId": "urn.com.bosch.nexeed.bmlp.widgeteng",
          "resourceType": "view",
          "widgetType": "IFrame",
          "configuration": {
            "availableWidths": [
              3,
              6,
              9,
              12
            ],
            "availableHeights": [
              2,
              3,
              4,
              5,
              6,
              7
            ]
          },
          "settings": {
            "widgetUrl": "http://localhost:5395/rendering/widgets/NumberText?widgetid=372b6744-a318-40a6-8d93-1aee42408b40&_tenantid=7311ea8c-5d48-43fe-acf9-980eedf24b6c&_basepath=&_name=APOPLN_NT",
            "widgetConfigUrl": ""
          },
          "localization": {
            "catalogTitle": {
              "de": "widget demo2",
              "en": "widget demo2"
            },
            "catalogDescription": {
              "de": "Widget Demo.",
              "en": "Widget Demo."
            }
          }
        },
        {
          "id": "56ad9094-0740-4bd5-54ed-4531be5a4881",
          "catalog": {
            "title": "widget demo3",
            "categoryId": "cat_others",
            "description": "Displays the demo widget.",
            "imageUrl": "ui/assets/img/blank-widget.png"
          },
          "resourceOwingTenant": "7311ea8c-5d48-43fe-acf9-980eedf24b6c",
          "resourceDynamic": true,
          "resourceId": "urn.com.bosch.nexeed.bmlp.widgeteng",
          "resourceType": "view",
          "widgetType": "IFrame",
          "configuration": {
            "availableWidths": [
              3,
              6,
              9,
              12
            ],
            "availableHeights": [
              2,
              3,
              4,
              5,
              6,
              7
            ]
          },
          "settings": {
            "widgetUrl": "http://localhost:5395/rendering/widgets/NumberText?widgetid=372b6744-a318-40a6-8d93-1aee42408b40&_tenantid=7311ea8c-5d48-43fe-acf9-980eedf24b6c&_basepath=&_name=APOPLN_NT",
            "widgetConfigUrl": ""
          },
          "localization": {
            "catalogTitle": {
              "de": "widget demo3",
              "en": "widget demo3"
            },
            "catalogDescription": {
              "de": "Widget Demo.",
              "en": "Widget Demo."
            }
          }
        }
      ]
    }
  },
  "NexeedMdmService": {
    "Url": "https://si0vmc4403.de.bosch.com/mdm/equipment-management"
  },
  "SwaggerUI": {
    "Servers": [
      "http://localhost:5395"
    ],
    "Enabled": true
  },
  "Csp": {
    "Sources": [
      "https:",
      "http:"
    ]
  },
  "AllowedHosts": "*",
  "Logging": {
    "LogLevel": {
      "Default": "Trace",
      "Microsoft.AspNetCore": "Information",
      "Microsoft.Hosting.Lifetime": "Information",
      "Elastic.Apm": "Information"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Information",
        "Elastic.Apm": "Information"
      }
    },
    "LogDirectory": "C:/DocupediaBot/Log"
  },
  "RenderingTokenExpiredSeconds": 30,
  "LdapSettings": {
    "IsEnableLdapLogin": "true",
    "LdapUrl": "LDAP://*************",
    "LdapBURegex": ""
  }
}