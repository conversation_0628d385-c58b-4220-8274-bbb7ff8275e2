# Docupedia RAG 系统发布日志

以下是 Docupedia RAG 系统的开发历程和关键里程碑，记录了从功能开发到部署优化的完整过程。日志基于开发记录整理，涵盖核心功能实现、问题修复和性能提升。

## 2025年2月11日 - 2025年4月15日

### 2025年2月11日
- **Qdrant 部署**：
  - 在 Linux 服务器上安装 Qdrant，调试 API 接口。
  - 解决 Docker 代理问题，修复 6334 端口 gRPC 错误（非 gRPC 或 Http2 问题，取消代理后解决）。
- **Docupedia 集合管理**：
  - 实现通过 PageID 和 Content API 获取页面内容。
  - 解决部分页面权限问题，添加权限配置（Personal Access Tokens）。
  - 识别无 PageID 和无权限页面的处理需求，计划用户管理功能。

### 2025年2月12日 - 2月14日
- **部署**：
  - 将应用部署到 0533 服务器，解决外部 API 调用代理问题。
  - 修复单页向量化 Bug，将应用配置为服务随系统启动。
  - 解决外部访问和页面刷新问题，启用 HTTPS。
- **集合管理**：
  - 新增 Collection 表，重构 Collection 与 Page 的关系，支持多对多映射。
  - 实现 Collection 的 CRUD 操作（添加、列表、删除、编辑）。

### 2025年2月15日 - 2月16日
- **页面管理**：
  - 新增 Page 相关 Controller 和 Service，实现页面添加、编辑、删除。
  - 支持子页面自动抓取（`isIncludeChild`），优化多层级内容处理。
- **问答优化**：
  - 实现前端 `sendMessage()`，优化问答前关键字处理。
- **代码重构**：
  - 优化文件和方法命名，规范参数类型，提升代码可读性。

### 2025年2月17日 - 2月24日
- **部署**：
  - 修复 `baseUrl` 问题，完成部署手册，正式将应用部署为 Docupedia 服务。
- **集合管理**：
  - 更新数据库字段，完成 Collection 添加、列表和删除功能。

### 2025年3月6日 - 3月19日
- **页面与内容管理**：
  - 拆分页面和内容逻辑，新增 Page 和 Content 的 DTO，更新数据库字段。
  - 完成 Page 添加、编辑、删除功能，支持多层级内容抓取。
  - 解决子层级内容抓取问题，优化内容添加逻辑。
- **HTML 预处理**：
  - 实现 Content 的 HTML 预处理，添加元数据标记，提升向量化准确性。

### 2025年3月20日 - 3月26日
- **内容管理**：
  - 优化 HTML 预处理逻辑，基于段落、元数据、重叠和 Token 数重写 Chunk 逻辑。
  - 新增 Map 表，维护 Collection 与 Page 的多对多关系，提升内容复用性能。
- **向量化**：
  - 实现基于 Collection 和 Page 的向量化，添加版本校验（`EmbeddingVersionNo`、`VersionNo`）。
  - 优化前端 Import 页面 UI，显示 Content 数量和向量化状态。

### 2025年3月27日 - 3月30日
- **问答优化**：
  - 优化查询和召回逻辑，添加可配置参数，支持多文档和大文档测试。
  - 修复知识库预处理 Bug（连续图片仅处理最后一个）。
  - 优化前端显示，支持图片、链接、代码格式化和元数据展示。
  - 解决切换 Tab 和刷新页面时的对话框状态恢复问题，新增 Reset 按钮。

### 2025年3月31日 - 4月3日
- **内容管理**：
  - 实现 Content 手动更新，基于 VersionNo 对比确保数据一致性。
  - 添加自动更新机制（`isAutomaticUpdate`），支持定时增量索引。
- **模型配置**：
  - 配置本地 Ollama 和 GPU 环境，支持模型切换（Embedding 和 Completion）。

### 2025年4月4日 - 4月9日
- **代码重构**：
  - 优化前端 UI（Toast 通知）、后端 Controller 和异常处理。
  - 集成 Serilog，按级别和日期保存日志，提升调试效率。
- **集合管理**：
  - 修复 Collection 时间选择 Bug，优化添加逻辑，新增 Collection Action（总结、最近更新、重复检测）。
- **用户体验**：
  - 美化 Chat 元素，支持自动滚动、打字机效果和上下文去重。
  - 优化页面状态控制，动态显示 Collection 和 Page 状态。

### 2025年4月10日 - 4月15日
- **模型切换**：
  - 实现 Embedding 和 Completion 模型切换，添加维度一致性校验。
- **特殊功能**：
  - 实现 Collection 总结、最近更新文档列表和重复内容检测（30% 完成）。
  - 支持更多代码类型（Plane、有序/无序列表），优化 HTML 预处理。
- **部署**：
  - 完成 0533 服务器部署，解决 Proxy 问题，优化提示词和多文章测试。
- **Bug 修复与增强**：
  - 取消 Chunk 字段，统一通过配置文件获取。
  - 修复 Page 添加后 Content 数量未实时更新的问题。

---

## 未完成功能与计划
- **用户管理**（0% 完成）：
  - 计划开发用户管理模块，维护 Token 和权限。
  - 调研通过 API 生成用户 Token 的方式。
- **特殊功能**（30% 完成）：
  - 继续完善 Collection 总结、最近更新和重复检测功能。
- **自动清理**：
  - 计划通过 Job 实现 Content 自动清理，减少手动维护。

---

## 总结
Docupedia RAG 系统从 2025 年 2 月启动开发，历经功能实现、部署优化和性能提升，已具备智能检索、问答、集合管理和内容分析的核心能力。系统通过多对多关系优化、HTML 预处理和本地部署，显著提升了性能和安全性。未来将聚焦用户管理、多模态支持和更高级的 AI 洞察，持续为企业知识管理赋能。
