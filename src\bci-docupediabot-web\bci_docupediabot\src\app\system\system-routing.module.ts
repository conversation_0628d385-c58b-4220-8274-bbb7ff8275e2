import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SysUserListComponent } from './components/sys-user-list/sys-user-list.component';
import { SysGroupListComponent } from './components/sys-group-list/sys-group-list.component';
import { SystemTabsComponent } from './components/system-tabs/system-tabs.component';

const routes: Routes = [
  {
    path: '',
    component: SystemTabsComponent,
    children: [
      {
        path: '',
        redirectTo: 'users',
        pathMatch: 'full'
      },
      {
        path: 'users',
        component: SysUserListComponent
      },
      {
        path: 'groups',
        component: SysGroupListComponent
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SystemRoutingModule { }
