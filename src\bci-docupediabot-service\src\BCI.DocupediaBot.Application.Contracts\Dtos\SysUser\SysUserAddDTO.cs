﻿using System;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.SysUser
{
  public class SysUserAddDTO
  {
    public string UserNTAccount { get; set; } = default!;
    public string UserName { get; set; } = default!;
    public string GivenName { get; set; } = default!;
    public string SN { get; set; } = default!;
    public string Mail { get; set; } = default!;
    public string Department { get; set; } = default!;
    public Guid FavCollecitonId { get; set; } = default!;
    public string DocupediaToken { get; set; } = default!;
    public int Status { get; set; } = default!;
  }
}