{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/_commonjsHelpers-ba3f0406.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\nfunction getDefaultExportFromCjs(x) {\n  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\nfunction createCommonjsModule(fn, basedir, module) {\n  return module = {\n    path: basedir,\n    exports: {},\n    require: function (path, base) {\n      return commonjsRequire();\n    }\n  }, fn(module, module.exports), module.exports;\n}\nfunction commonjsRequire() {\n  throw new Error('Dynamic requires are not currently supported by @rollup/plugin-commonjs');\n}\nexport { commonjsGlobal as a, createCommonjsModule as c, getDefaultExportFromCjs as g };\n\n"], "mappings": ";AACA,IAAI,iBAAiB,OAAO,eAAe,cAAc,aAAa,OAAO,WAAW,cAAc,SAAS,OAAO,WAAW,cAAc,SAAS,OAAO,SAAS,cAAc,OAAO,CAAC;AAC9L,SAAS,wBAAwB,GAAG;AAClC,SAAO,KAAK,EAAE,cAAc,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,IAAI,EAAE,SAAS,IAAI;AAClG;AACA,SAAS,qBAAqB,IAAI,SAAS,QAAQ;AACjD,SAAO,SAAS;AAAA,IACd,MAAM;AAAA,IACN,SAAS,CAAC;AAAA,IACV,SAAS,SAAU,MAAM,MAAM;AAC7B,aAAO,gBAAgB;AAAA,IACzB;AAAA,EACF,GAAG,GAAG,QAAQ,OAAO,OAAO,GAAG,OAAO;AACxC;AACA,SAAS,kBAAkB;AACzB,QAAM,IAAI,MAAM,yEAAyE;AAC3F;", "names": []}