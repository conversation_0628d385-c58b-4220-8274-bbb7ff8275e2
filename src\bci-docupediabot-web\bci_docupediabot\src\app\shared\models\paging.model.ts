/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */
export class Paging<TItem> {
  constructor(
    public pageIndex: number = 0,
    public pageSize?: number,
    public totalCount: number = 0,
    public totalPages: number = 0,
    public items?: TItem[],
    public pageSizeOptions?: number[]
  ) {}
}

export class FilterablePaging<TItem> extends Paging<TItem> {
  constructor(public filterText?: string) {
    super();
  }
}
