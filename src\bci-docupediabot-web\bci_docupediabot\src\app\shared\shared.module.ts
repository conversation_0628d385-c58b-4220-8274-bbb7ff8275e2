/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from './material.module';
import { WebCoreModule } from './web-core.module';
import { TranslateModule } from '@ngx-translate/core';
import { BCI_SHARED_INTL_PROVIDER } from '@bci-web-core/core';
import { SafeUrlPipe } from './pipes';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrochureComponent } from './components/brochure/brochure.component';
import { BrochureEnComponent } from './components/brochure-en/brochure-en.component';

@NgModule({
  declarations: [BrochureComponent, BrochureEnComponent],
  imports: [
    CommonModule,
    MaterialModule,
    WebCoreModule,
    SafeUrlPipe,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  exports: [
    MaterialModule,
    WebCoreModule,
    TranslateModule,
    SafeUrlPipe,
    FormsModule,
    ReactiveFormsModule,
    BrochureComponent,
    BrochureEnComponent,
  ],
  providers: [BCI_SHARED_INTL_PROVIDER],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SharedModule {}
