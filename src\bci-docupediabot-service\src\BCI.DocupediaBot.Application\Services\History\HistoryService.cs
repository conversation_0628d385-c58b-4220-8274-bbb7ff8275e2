﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Chat;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.History
{
  public class HistoryService : IHistoryService
  {
    private readonly IChatHistoryRepository _chatHistoryRepository;
    private readonly ILogger<HistoryService> _logger;
    private readonly IMapper _mapper;

    public HistoryService(
        ILogger<HistoryService> logger, IMapper mapper, IChatHistoryRepository chatHistoryRepository)
    {

      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _mapper = mapper;
      _chatHistoryRepository = chatHistoryRepository;
    }

    public async Task<ResponseResult> AddChatHistoryAsync(ChatHistoryAddDTO dto)
    {
      try
      {
        var chatHistory = _mapper.Map<Domain.Entities.ChatHistory>(dto);
        await _chatHistoryRepository.CreateAsync(chatHistory);
      }
      catch (Exception e)
      {
        _logger.LogError("Create history failed: " + e.Message);
      }
      return new ResponseResult { IsSuccess = true, Msg = "Chat history added successfully." };
    }
  }
}