import {
  Over<PERSON><PERSON><PERSON><PERSON>
} from "./chunk-23OCTP2V.js";
import {
  createCommonjsModule,
  getDefaultExportFromCjs
} from "./chunk-NW6JIBKM.js";
import {
  DEFAULT_LOCALE,
  LOCALE_MAP,
  addDays,
  createDate,
  format,
  getDaysInMonth,
  getTimezoneOffsetInMilliseconds,
  getUTCISOWeek,
  getUTCWeek,
  getUTCWeekYear,
  isHideSeconds,
  isProtectedDayOfYearToken,
  isProtectedWeekYearToken,
  isSameDay,
  isValid,
  listenToLanguageChange,
  locale$9,
  longFormatters,
  requiredArgs,
  setLocale,
  setMonth,
  startOfDay,
  startOfUTCISOWeek,
  startOfUTCWeek,
  state,
  subMilliseconds,
  throwProtectedError,
  timeFormatHasMeridiem,
  toDate,
  toInteger,
  translate
} from "./chunk-PHADP4AA.js";
import {
  Host,
  createEvent,
  getElement,
  h,
  registerInstance
} from "./chunk-DC5VRE3P.js";
import {
  __async
} from "./chunk-Y4T55RDF.js";

// node_modules/@bci-web-core/web-components/dist/esm/bci-datepicker_9.entry.js
function startOfWeek(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale = options.locale;
  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = date.getDay();
  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;
  date.setDate(date.getDate() - diff);
  date.setHours(0, 0, 0, 0);
  return date;
}
function max(dirtyDatesArray) {
  requiredArgs(1, arguments);
  var datesArray;
  if (dirtyDatesArray && typeof dirtyDatesArray.forEach === "function") {
    datesArray = dirtyDatesArray;
  } else if (typeof dirtyDatesArray === "object" && dirtyDatesArray !== null) {
    datesArray = Array.prototype.slice.call(dirtyDatesArray);
  } else {
    return /* @__PURE__ */ new Date(NaN);
  }
  var result;
  datesArray.forEach(function(dirtyDate) {
    var currentDate = toDate(dirtyDate);
    if (result === void 0 || result < currentDate || isNaN(Number(currentDate))) {
      result = currentDate;
    }
  });
  return result || /* @__PURE__ */ new Date(NaN);
}
function min(dirtyDatesArray) {
  requiredArgs(1, arguments);
  var datesArray;
  if (dirtyDatesArray && typeof dirtyDatesArray.forEach === "function") {
    datesArray = dirtyDatesArray;
  } else if (typeof dirtyDatesArray === "object" && dirtyDatesArray !== null) {
    datesArray = Array.prototype.slice.call(dirtyDatesArray);
  } else {
    return /* @__PURE__ */ new Date(NaN);
  }
  var result;
  datesArray.forEach(function(dirtyDate) {
    var currentDate = toDate(dirtyDate);
    if (result === void 0 || result > currentDate || isNaN(currentDate.getDate())) {
      result = currentDate;
    }
  });
  return result || /* @__PURE__ */ new Date(NaN);
}
function clamp(date, _ref) {
  var start = _ref.start, end = _ref.end;
  requiredArgs(2, arguments);
  return min([max([date, start]), end]);
}
function endOfDay(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  date.setHours(23, 59, 59, 999);
  return date;
}
function endOfMonth(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var month = date.getMonth();
  date.setFullYear(date.getFullYear(), month + 1, 0);
  date.setHours(23, 59, 59, 999);
  return date;
}
function endOfWeek(dirtyDate, dirtyOptions) {
  requiredArgs(1, arguments);
  var options = dirtyOptions || {};
  var locale = options.locale;
  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = date.getDay();
  var diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);
  date.setDate(date.getDate() + diff);
  date.setHours(23, 59, 59, 999);
  return date;
}
function assign(target, dirtyObject) {
  if (target == null) {
    throw new TypeError("assign requires that input parameter not be null or undefined");
  }
  dirtyObject = dirtyObject || {};
  for (var property in dirtyObject) {
    if (Object.prototype.hasOwnProperty.call(dirtyObject, property)) {
      target[property] = dirtyObject[property];
    }
  }
  return target;
}
function fromUnixTime(dirtyUnixTime) {
  requiredArgs(1, arguments);
  var unixTime = toInteger(dirtyUnixTime);
  return toDate(unixTime * 1e3);
}
function getDate(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var dayOfMonth = date.getDate();
  return dayOfMonth;
}
function getISODay(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var day = date.getDay();
  if (day === 0) {
    day = 7;
  }
  return day;
}
function getMonth(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var month = date.getMonth();
  return month;
}
function getTime(dirtyDate) {
  requiredArgs(1, arguments);
  var date = toDate(dirtyDate);
  var timestamp = date.getTime();
  return timestamp;
}
function getUnixTime(dirtyDate) {
  requiredArgs(1, arguments);
  return Math.floor(getTime(dirtyDate) / 1e3);
}
function getYear(dirtyDate) {
  requiredArgs(1, arguments);
  return toDate(dirtyDate).getFullYear();
}
function isAfter(dirtyDate, dirtyDateToCompare) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var dateToCompare = toDate(dirtyDateToCompare);
  return date.getTime() > dateToCompare.getTime();
}
function isBefore(dirtyDate, dirtyDateToCompare) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var dateToCompare = toDate(dirtyDateToCompare);
  return date.getTime() < dateToCompare.getTime();
}
function setUTCDay(dirtyDate, dirtyDay, dirtyOptions) {
  requiredArgs(2, arguments);
  var options = dirtyOptions || {};
  var locale = options.locale;
  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = toInteger(dirtyDay);
  var currentDay = date.getUTCDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}
function setUTCISODay(dirtyDate, dirtyDay) {
  requiredArgs(2, arguments);
  var day = toInteger(dirtyDay);
  if (day % 7 === 0) {
    day = day - 7;
  }
  var weekStartsOn = 1;
  var date = toDate(dirtyDate);
  var currentDay = date.getUTCDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}
function setUTCISOWeek(dirtyDate, dirtyISOWeek) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var isoWeek = toInteger(dirtyISOWeek);
  var diff = getUTCISOWeek(date) - isoWeek;
  date.setUTCDate(date.getUTCDate() - diff * 7);
  return date;
}
function setUTCWeek(dirtyDate, dirtyWeek, options) {
  requiredArgs(2, arguments);
  var date = toDate(dirtyDate);
  var week = toInteger(dirtyWeek);
  var diff = getUTCWeek(date, options) - week;
  date.setUTCDate(date.getUTCDate() - diff * 7);
  return date;
}
var MILLISECONDS_IN_HOUR$2 = 36e5;
var MILLISECONDS_IN_MINUTE$2 = 6e4;
var MILLISECONDS_IN_SECOND = 1e3;
var numericPatterns = {
  month: /^(1[0-2]|0?\d)/,
  // 0 to 12
  date: /^(3[0-1]|[0-2]?\d)/,
  // 0 to 31
  dayOfYear: /^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,
  // 0 to 366
  week: /^(5[0-3]|[0-4]?\d)/,
  // 0 to 53
  hour23h: /^(2[0-3]|[0-1]?\d)/,
  // 0 to 23
  hour24h: /^(2[0-4]|[0-1]?\d)/,
  // 0 to 24
  hour11h: /^(1[0-1]|0?\d)/,
  // 0 to 11
  hour12h: /^(1[0-2]|0?\d)/,
  // 0 to 12
  minute: /^[0-5]?\d/,
  // 0 to 59
  second: /^[0-5]?\d/,
  // 0 to 59
  singleDigit: /^\d/,
  // 0 to 9
  twoDigits: /^\d{1,2}/,
  // 0 to 99
  threeDigits: /^\d{1,3}/,
  // 0 to 999
  fourDigits: /^\d{1,4}/,
  // 0 to 9999
  anyDigitsSigned: /^-?\d+/,
  singleDigitSigned: /^-?\d/,
  // 0 to 9, -0 to -9
  twoDigitsSigned: /^-?\d{1,2}/,
  // 0 to 99, -0 to -99
  threeDigitsSigned: /^-?\d{1,3}/,
  // 0 to 999, -0 to -999
  fourDigitsSigned: /^-?\d{1,4}/
  // 0 to 9999, -0 to -9999
};
var timezonePatterns = {
  basicOptionalMinutes: /^([+-])(\d{2})(\d{2})?|Z/,
  basic: /^([+-])(\d{2})(\d{2})|Z/,
  basicOptionalSeconds: /^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,
  extended: /^([+-])(\d{2}):(\d{2})|Z/,
  extendedOptionalSeconds: /^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/
};
function parseNumericPattern(pattern, string, valueCallback) {
  var matchResult = string.match(pattern);
  if (!matchResult) {
    return null;
  }
  var value = parseInt(matchResult[0], 10);
  return {
    value: valueCallback ? valueCallback(value) : value,
    rest: string.slice(matchResult[0].length)
  };
}
function parseTimezonePattern(pattern, string) {
  var matchResult = string.match(pattern);
  if (!matchResult) {
    return null;
  }
  if (matchResult[0] === "Z") {
    return {
      value: 0,
      rest: string.slice(1)
    };
  }
  var sign = matchResult[1] === "+" ? 1 : -1;
  var hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;
  var minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;
  var seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;
  return {
    value: sign * (hours * MILLISECONDS_IN_HOUR$2 + minutes * MILLISECONDS_IN_MINUTE$2 + seconds * MILLISECONDS_IN_SECOND),
    rest: string.slice(matchResult[0].length)
  };
}
function parseAnyDigitsSigned(string, valueCallback) {
  return parseNumericPattern(numericPatterns.anyDigitsSigned, string, valueCallback);
}
function parseNDigits(n, string, valueCallback) {
  switch (n) {
    case 1:
      return parseNumericPattern(numericPatterns.singleDigit, string, valueCallback);
    case 2:
      return parseNumericPattern(numericPatterns.twoDigits, string, valueCallback);
    case 3:
      return parseNumericPattern(numericPatterns.threeDigits, string, valueCallback);
    case 4:
      return parseNumericPattern(numericPatterns.fourDigits, string, valueCallback);
    default:
      return parseNumericPattern(new RegExp("^\\d{1," + n + "}"), string, valueCallback);
  }
}
function parseNDigitsSigned(n, string, valueCallback) {
  switch (n) {
    case 1:
      return parseNumericPattern(numericPatterns.singleDigitSigned, string, valueCallback);
    case 2:
      return parseNumericPattern(numericPatterns.twoDigitsSigned, string, valueCallback);
    case 3:
      return parseNumericPattern(numericPatterns.threeDigitsSigned, string, valueCallback);
    case 4:
      return parseNumericPattern(numericPatterns.fourDigitsSigned, string, valueCallback);
    default:
      return parseNumericPattern(new RegExp("^-?\\d{1," + n + "}"), string, valueCallback);
  }
}
function dayPeriodEnumToHours(enumValue) {
  switch (enumValue) {
    case "morning":
      return 4;
    case "evening":
      return 17;
    case "pm":
    case "noon":
    case "afternoon":
      return 12;
    case "am":
    case "midnight":
    case "night":
    default:
      return 0;
  }
}
function normalizeTwoDigitYear(twoDigitYear, currentYear) {
  var isCommonEra = currentYear > 0;
  var absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;
  var result;
  if (absCurrentYear <= 50) {
    result = twoDigitYear || 100;
  } else {
    var rangeEnd = absCurrentYear + 50;
    var rangeEndCentury = Math.floor(rangeEnd / 100) * 100;
    var isPreviousCentury = twoDigitYear >= rangeEnd % 100;
    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);
  }
  return isCommonEra ? result : 1 - result;
}
var DAYS_IN_MONTH$1 = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var DAYS_IN_MONTH_LEAP_YEAR$1 = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
function isLeapYearIndex$1(year) {
  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;
}
var parsers = {
  // Era
  G: {
    priority: 140,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "G":
        case "GG":
        case "GGG":
          return match.era(string, {
            width: "abbreviated"
          }) || match.era(string, {
            width: "narrow"
          });
        case "GGGGG":
          return match.era(string, {
            width: "narrow"
          });
        case "GGGG":
        default:
          return match.era(string, {
            width: "wide"
          }) || match.era(string, {
            width: "abbreviated"
          }) || match.era(string, {
            width: "narrow"
          });
      }
    },
    set: function(date, flags, value, _options) {
      flags.era = value;
      date.setUTCFullYear(value, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["R", "u", "t", "T"]
  },
  // Year
  y: {
    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns
    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |
    // |----------|-------|----|-------|-------|-------|
    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |
    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |
    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |
    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |
    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |
    priority: 130,
    parse: function(string, token, match, _options) {
      var valueCallback = function(year) {
        return {
          year,
          isTwoDigitYear: token === "yy"
        };
      };
      switch (token) {
        case "y":
          return parseNDigits(4, string, valueCallback);
        case "yo":
          return match.ordinalNumber(string, {
            unit: "year",
            valueCallback
          });
        default:
          return parseNDigits(token.length, string, valueCallback);
      }
    },
    validate: function(_date, value, _options) {
      return value.isTwoDigitYear || value.year > 0;
    },
    set: function(date, flags, value, _options) {
      var currentYear = date.getUTCFullYear();
      if (value.isTwoDigitYear) {
        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);
        date.setUTCFullYear(normalizedTwoDigitYear, 0, 1);
        date.setUTCHours(0, 0, 0, 0);
        return date;
      }
      var year = !("era" in flags) || flags.era === 1 ? value.year : 1 - value.year;
      date.setUTCFullYear(year, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "u", "w", "I", "i", "e", "c", "t", "T"]
  },
  // Local week-numbering year
  Y: {
    priority: 130,
    parse: function(string, token, match, _options) {
      var valueCallback = function(year) {
        return {
          year,
          isTwoDigitYear: token === "YY"
        };
      };
      switch (token) {
        case "Y":
          return parseNDigits(4, string, valueCallback);
        case "Yo":
          return match.ordinalNumber(string, {
            unit: "year",
            valueCallback
          });
        default:
          return parseNDigits(token.length, string, valueCallback);
      }
    },
    validate: function(_date, value, _options) {
      return value.isTwoDigitYear || value.year > 0;
    },
    set: function(date, flags, value, options) {
      var currentYear = getUTCWeekYear(date, options);
      if (value.isTwoDigitYear) {
        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);
        date.setUTCFullYear(normalizedTwoDigitYear, 0, options.firstWeekContainsDate);
        date.setUTCHours(0, 0, 0, 0);
        return startOfUTCWeek(date, options);
      }
      var year = !("era" in flags) || flags.era === 1 ? value.year : 1 - value.year;
      date.setUTCFullYear(year, 0, options.firstWeekContainsDate);
      date.setUTCHours(0, 0, 0, 0);
      return startOfUTCWeek(date, options);
    },
    incompatibleTokens: ["y", "R", "u", "Q", "q", "M", "L", "I", "d", "D", "i", "t", "T"]
  },
  // ISO week-numbering year
  R: {
    priority: 130,
    parse: function(string, token, _match, _options) {
      if (token === "R") {
        return parseNDigitsSigned(4, string);
      }
      return parseNDigitsSigned(token.length, string);
    },
    set: function(_date, _flags, value, _options) {
      var firstWeekOfYear = /* @__PURE__ */ new Date(0);
      firstWeekOfYear.setUTCFullYear(value, 0, 4);
      firstWeekOfYear.setUTCHours(0, 0, 0, 0);
      return startOfUTCISOWeek(firstWeekOfYear);
    },
    incompatibleTokens: ["G", "y", "Y", "u", "Q", "q", "M", "L", "w", "d", "D", "e", "c", "t", "T"]
  },
  // Extended year
  u: {
    priority: 130,
    parse: function(string, token, _match, _options) {
      if (token === "u") {
        return parseNDigitsSigned(4, string);
      }
      return parseNDigitsSigned(token.length, string);
    },
    set: function(date, _flags, value, _options) {
      date.setUTCFullYear(value, 0, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["G", "y", "Y", "R", "w", "I", "i", "e", "c", "t", "T"]
  },
  // Quarter
  Q: {
    priority: 120,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "Q":
        case "QQ":
          return parseNDigits(token.length, string);
        case "Qo":
          return match.ordinalNumber(string, {
            unit: "quarter"
          });
        case "QQQ":
          return match.quarter(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.quarter(string, {
            width: "narrow",
            context: "formatting"
          });
        case "QQQQQ":
          return match.quarter(string, {
            width: "narrow",
            context: "formatting"
          });
        case "QQQQ":
        default:
          return match.quarter(string, {
            width: "wide",
            context: "formatting"
          }) || match.quarter(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.quarter(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 4;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth((value - 1) * 3, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "M", "L", "w", "I", "d", "D", "i", "e", "c", "t", "T"]
  },
  // Stand-alone quarter
  q: {
    priority: 120,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "q":
        case "qq":
          return parseNDigits(token.length, string);
        case "qo":
          return match.ordinalNumber(string, {
            unit: "quarter"
          });
        case "qqq":
          return match.quarter(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match.quarter(string, {
            width: "narrow",
            context: "standalone"
          });
        case "qqqqq":
          return match.quarter(string, {
            width: "narrow",
            context: "standalone"
          });
        case "qqqq":
        default:
          return match.quarter(string, {
            width: "wide",
            context: "standalone"
          }) || match.quarter(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match.quarter(string, {
            width: "narrow",
            context: "standalone"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 4;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth((value - 1) * 3, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "Q", "M", "L", "w", "I", "d", "D", "i", "e", "c", "t", "T"]
  },
  // Month
  M: {
    priority: 110,
    parse: function(string, token, match, _options) {
      var valueCallback = function(value) {
        return value - 1;
      };
      switch (token) {
        case "M":
          return parseNumericPattern(numericPatterns.month, string, valueCallback);
        case "MM":
          return parseNDigits(2, string, valueCallback);
        case "Mo":
          return match.ordinalNumber(string, {
            unit: "month",
            valueCallback
          });
        case "MMM":
          return match.month(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.month(string, {
            width: "narrow",
            context: "formatting"
          });
        case "MMMMM":
          return match.month(string, {
            width: "narrow",
            context: "formatting"
          });
        case "MMMM":
        default:
          return match.month(string, {
            width: "wide",
            context: "formatting"
          }) || match.month(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.month(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth(value, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "L", "w", "I", "D", "i", "e", "c", "t", "T"]
  },
  // Stand-alone month
  L: {
    priority: 110,
    parse: function(string, token, match, _options) {
      var valueCallback = function(value) {
        return value - 1;
      };
      switch (token) {
        case "L":
          return parseNumericPattern(numericPatterns.month, string, valueCallback);
        case "LL":
          return parseNDigits(2, string, valueCallback);
        case "Lo":
          return match.ordinalNumber(string, {
            unit: "month",
            valueCallback
          });
        case "LLL":
          return match.month(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match.month(string, {
            width: "narrow",
            context: "standalone"
          });
        case "LLLLL":
          return match.month(string, {
            width: "narrow",
            context: "standalone"
          });
        case "LLLL":
        default:
          return match.month(string, {
            width: "wide",
            context: "standalone"
          }) || match.month(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match.month(string, {
            width: "narrow",
            context: "standalone"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth(value, 1);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "M", "w", "I", "D", "i", "e", "c", "t", "T"]
  },
  // Local week of year
  w: {
    priority: 100,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "w":
          return parseNumericPattern(numericPatterns.week, string);
        case "wo":
          return match.ordinalNumber(string, {
            unit: "week"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 53;
    },
    set: function(date, _flags, value, options) {
      return startOfUTCWeek(setUTCWeek(date, value, options), options);
    },
    incompatibleTokens: ["y", "R", "u", "q", "Q", "M", "L", "I", "d", "D", "i", "t", "T"]
  },
  // ISO week of year
  I: {
    priority: 100,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "I":
          return parseNumericPattern(numericPatterns.week, string);
        case "Io":
          return match.ordinalNumber(string, {
            unit: "week"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 53;
    },
    set: function(date, _flags, value, options) {
      return startOfUTCISOWeek(setUTCISOWeek(date, value, options), options);
    },
    incompatibleTokens: ["y", "Y", "u", "q", "Q", "M", "L", "w", "d", "D", "e", "c", "t", "T"]
  },
  // Day of the month
  d: {
    priority: 90,
    subPriority: 1,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "d":
          return parseNumericPattern(numericPatterns.date, string);
        case "do":
          return match.ordinalNumber(string, {
            unit: "date"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(date, value, _options) {
      var year = date.getUTCFullYear();
      var isLeapYear = isLeapYearIndex$1(year);
      var month = date.getUTCMonth();
      if (isLeapYear) {
        return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR$1[month];
      } else {
        return value >= 1 && value <= DAYS_IN_MONTH$1[month];
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCDate(value);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "w", "I", "D", "i", "e", "c", "t", "T"]
  },
  // Day of year
  D: {
    priority: 90,
    subPriority: 1,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "D":
        case "DD":
          return parseNumericPattern(numericPatterns.dayOfYear, string);
        case "Do":
          return match.ordinalNumber(string, {
            unit: "date"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(date, value, _options) {
      var year = date.getUTCFullYear();
      var isLeapYear = isLeapYearIndex$1(year);
      if (isLeapYear) {
        return value >= 1 && value <= 366;
      } else {
        return value >= 1 && value <= 365;
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMonth(0, value);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["Y", "R", "q", "Q", "M", "L", "w", "I", "d", "E", "i", "e", "c", "t", "T"]
  },
  // Day of week
  E: {
    priority: 90,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "E":
        case "EE":
        case "EEE":
          return match.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.day(string, {
            width: "short",
            context: "formatting"
          }) || match.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "EEEEE":
          return match.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "EEEEEE":
          return match.day(string, {
            width: "short",
            context: "formatting"
          }) || match.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "EEEE":
        default:
          return match.day(string, {
            width: "wide",
            context: "formatting"
          }) || match.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.day(string, {
            width: "short",
            context: "formatting"
          }) || match.day(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function(date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["D", "i", "e", "c", "t", "T"]
  },
  // Local day of week
  e: {
    priority: 90,
    parse: function(string, token, match, options) {
      var valueCallback = function(value) {
        var wholeWeekDays = Math.floor((value - 1) / 7) * 7;
        return (value + options.weekStartsOn + 6) % 7 + wholeWeekDays;
      };
      switch (token) {
        case "e":
        case "ee":
          return parseNDigits(token.length, string, valueCallback);
        case "eo":
          return match.ordinalNumber(string, {
            unit: "day",
            valueCallback
          });
        case "eee":
          return match.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.day(string, {
            width: "short",
            context: "formatting"
          }) || match.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "eeeee":
          return match.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "eeeeee":
          return match.day(string, {
            width: "short",
            context: "formatting"
          }) || match.day(string, {
            width: "narrow",
            context: "formatting"
          });
        case "eeee":
        default:
          return match.day(string, {
            width: "wide",
            context: "formatting"
          }) || match.day(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.day(string, {
            width: "short",
            context: "formatting"
          }) || match.day(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function(date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["y", "R", "u", "q", "Q", "M", "L", "I", "d", "D", "E", "i", "c", "t", "T"]
  },
  // Stand-alone local day of week
  c: {
    priority: 90,
    parse: function(string, token, match, options) {
      var valueCallback = function(value) {
        var wholeWeekDays = Math.floor((value - 1) / 7) * 7;
        return (value + options.weekStartsOn + 6) % 7 + wholeWeekDays;
      };
      switch (token) {
        case "c":
        case "cc":
          return parseNDigits(token.length, string, valueCallback);
        case "co":
          return match.ordinalNumber(string, {
            unit: "day",
            valueCallback
          });
        case "ccc":
          return match.day(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match.day(string, {
            width: "short",
            context: "standalone"
          }) || match.day(string, {
            width: "narrow",
            context: "standalone"
          });
        case "ccccc":
          return match.day(string, {
            width: "narrow",
            context: "standalone"
          });
        case "cccccc":
          return match.day(string, {
            width: "short",
            context: "standalone"
          }) || match.day(string, {
            width: "narrow",
            context: "standalone"
          });
        case "cccc":
        default:
          return match.day(string, {
            width: "wide",
            context: "standalone"
          }) || match.day(string, {
            width: "abbreviated",
            context: "standalone"
          }) || match.day(string, {
            width: "short",
            context: "standalone"
          }) || match.day(string, {
            width: "narrow",
            context: "standalone"
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 6;
    },
    set: function(date, _flags, value, options) {
      date = setUTCDay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["y", "R", "u", "q", "Q", "M", "L", "I", "d", "D", "E", "i", "e", "t", "T"]
  },
  // ISO day of week
  i: {
    priority: 90,
    parse: function(string, token, match, _options) {
      var valueCallback = function(value) {
        if (value === 0) {
          return 7;
        }
        return value;
      };
      switch (token) {
        case "i":
        case "ii":
          return parseNDigits(token.length, string);
        case "io":
          return match.ordinalNumber(string, {
            unit: "day"
          });
        case "iii":
          return match.day(string, {
            width: "abbreviated",
            context: "formatting",
            valueCallback
          }) || match.day(string, {
            width: "short",
            context: "formatting",
            valueCallback
          }) || match.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
        case "iiiii":
          return match.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
        case "iiiiii":
          return match.day(string, {
            width: "short",
            context: "formatting",
            valueCallback
          }) || match.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
        case "iiii":
        default:
          return match.day(string, {
            width: "wide",
            context: "formatting",
            valueCallback
          }) || match.day(string, {
            width: "abbreviated",
            context: "formatting",
            valueCallback
          }) || match.day(string, {
            width: "short",
            context: "formatting",
            valueCallback
          }) || match.day(string, {
            width: "narrow",
            context: "formatting",
            valueCallback
          });
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 7;
    },
    set: function(date, _flags, value, options) {
      date = setUTCISODay(date, value, options);
      date.setUTCHours(0, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["y", "Y", "u", "q", "Q", "M", "L", "w", "d", "D", "E", "e", "c", "t", "T"]
  },
  // AM or PM
  a: {
    priority: 80,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "a":
        case "aa":
        case "aaa":
          return match.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "aaaaa":
          return match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "aaaa":
        default:
          return match.dayPeriod(string, {
            width: "wide",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["b", "B", "H", "k", "t", "T"]
  },
  // AM, PM, midnight
  b: {
    priority: 80,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "b":
        case "bb":
        case "bbb":
          return match.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "bbbbb":
          return match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "bbbb":
        default:
          return match.dayPeriod(string, {
            width: "wide",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "B", "H", "k", "t", "T"]
  },
  // in the morning, in the afternoon, in the evening, at night
  B: {
    priority: 80,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "B":
        case "BB":
        case "BBB":
          return match.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "BBBBB":
          return match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
        case "BBBB":
        default:
          return match.dayPeriod(string, {
            width: "wide",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "abbreviated",
            context: "formatting"
          }) || match.dayPeriod(string, {
            width: "narrow",
            context: "formatting"
          });
      }
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "b", "t", "T"]
  },
  // Hour [1-12]
  h: {
    priority: 70,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "h":
          return parseNumericPattern(numericPatterns.hour12h, string);
        case "ho":
          return match.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 12;
    },
    set: function(date, _flags, value, _options) {
      var isPM = date.getUTCHours() >= 12;
      if (isPM && value < 12) {
        date.setUTCHours(value + 12, 0, 0, 0);
      } else if (!isPM && value === 12) {
        date.setUTCHours(0, 0, 0, 0);
      } else {
        date.setUTCHours(value, 0, 0, 0);
      }
      return date;
    },
    incompatibleTokens: ["H", "K", "k", "t", "T"]
  },
  // Hour [0-23]
  H: {
    priority: 70,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "H":
          return parseNumericPattern(numericPatterns.hour23h, string);
        case "Ho":
          return match.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 23;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCHours(value, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "b", "h", "K", "k", "t", "T"]
  },
  // Hour [0-11]
  K: {
    priority: 70,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "K":
          return parseNumericPattern(numericPatterns.hour11h, string);
        case "Ko":
          return match.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 11;
    },
    set: function(date, _flags, value, _options) {
      var isPM = date.getUTCHours() >= 12;
      if (isPM && value < 12) {
        date.setUTCHours(value + 12, 0, 0, 0);
      } else {
        date.setUTCHours(value, 0, 0, 0);
      }
      return date;
    },
    incompatibleTokens: ["h", "H", "k", "t", "T"]
  },
  // Hour [1-24]
  k: {
    priority: 70,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "k":
          return parseNumericPattern(numericPatterns.hour24h, string);
        case "ko":
          return match.ordinalNumber(string, {
            unit: "hour"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 1 && value <= 24;
    },
    set: function(date, _flags, value, _options) {
      var hours = value <= 24 ? value % 24 : value;
      date.setUTCHours(hours, 0, 0, 0);
      return date;
    },
    incompatibleTokens: ["a", "b", "h", "H", "K", "t", "T"]
  },
  // Minute
  m: {
    priority: 60,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "m":
          return parseNumericPattern(numericPatterns.minute, string);
        case "mo":
          return match.ordinalNumber(string, {
            unit: "minute"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 59;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMinutes(value, 0, 0);
      return date;
    },
    incompatibleTokens: ["t", "T"]
  },
  // Second
  s: {
    priority: 50,
    parse: function(string, token, match, _options) {
      switch (token) {
        case "s":
          return parseNumericPattern(numericPatterns.second, string);
        case "so":
          return match.ordinalNumber(string, {
            unit: "second"
          });
        default:
          return parseNDigits(token.length, string);
      }
    },
    validate: function(_date, value, _options) {
      return value >= 0 && value <= 59;
    },
    set: function(date, _flags, value, _options) {
      date.setUTCSeconds(value, 0);
      return date;
    },
    incompatibleTokens: ["t", "T"]
  },
  // Fraction of second
  S: {
    priority: 30,
    parse: function(string, token, _match, _options) {
      var valueCallback = function(value) {
        return Math.floor(value * Math.pow(10, -token.length + 3));
      };
      return parseNDigits(token.length, string, valueCallback);
    },
    set: function(date, _flags, value, _options) {
      date.setUTCMilliseconds(value);
      return date;
    },
    incompatibleTokens: ["t", "T"]
  },
  // Timezone (ISO-8601. +00:00 is `'Z'`)
  X: {
    priority: 10,
    parse: function(string, token, _match, _options) {
      switch (token) {
        case "X":
          return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, string);
        case "XX":
          return parseTimezonePattern(timezonePatterns.basic, string);
        case "XXXX":
          return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, string);
        case "XXXXX":
          return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, string);
        case "XXX":
        default:
          return parseTimezonePattern(timezonePatterns.extended, string);
      }
    },
    set: function(date, flags, value, _options) {
      if (flags.timestampIsSet) {
        return date;
      }
      return new Date(date.getTime() - value);
    },
    incompatibleTokens: ["t", "T", "x"]
  },
  // Timezone (ISO-8601)
  x: {
    priority: 10,
    parse: function(string, token, _match, _options) {
      switch (token) {
        case "x":
          return parseTimezonePattern(timezonePatterns.basicOptionalMinutes, string);
        case "xx":
          return parseTimezonePattern(timezonePatterns.basic, string);
        case "xxxx":
          return parseTimezonePattern(timezonePatterns.basicOptionalSeconds, string);
        case "xxxxx":
          return parseTimezonePattern(timezonePatterns.extendedOptionalSeconds, string);
        case "xxx":
        default:
          return parseTimezonePattern(timezonePatterns.extended, string);
      }
    },
    set: function(date, flags, value, _options) {
      if (flags.timestampIsSet) {
        return date;
      }
      return new Date(date.getTime() - value);
    },
    incompatibleTokens: ["t", "T", "X"]
  },
  // Seconds timestamp
  t: {
    priority: 40,
    parse: function(string, _token, _match, _options) {
      return parseAnyDigitsSigned(string);
    },
    set: function(_date, _flags, value, _options) {
      return [new Date(value * 1e3), {
        timestampIsSet: true
      }];
    },
    incompatibleTokens: "*"
  },
  // Milliseconds timestamp
  T: {
    priority: 20,
    parse: function(string, _token, _match, _options) {
      return parseAnyDigitsSigned(string);
    },
    set: function(_date, _flags, value, _options) {
      return [new Date(value), {
        timestampIsSet: true
      }];
    },
    incompatibleTokens: "*"
  }
};
var TIMEZONE_UNIT_PRIORITY = 10;
var formattingTokensRegExp = /[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;
var longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;
var escapedStringRegExp = /^'([^]*?)'?$/;
var doubleQuoteRegExp = /''/g;
var notWhitespaceRegExp = /\S/;
var unescapedLatinCharacterRegExp = /[a-zA-Z]/;
function parse(dirtyDateString, dirtyFormatString, dirtyReferenceDate, dirtyOptions) {
  requiredArgs(3, arguments);
  var dateString = String(dirtyDateString);
  var formatString = String(dirtyFormatString);
  var options = dirtyOptions || {};
  var locale$1 = options.locale || locale$9;
  if (!locale$1.match) {
    throw new RangeError("locale must contain match property");
  }
  var localeFirstWeekContainsDate = locale$1.options && locale$1.options.firstWeekContainsDate;
  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);
  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);
  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {
    throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");
  }
  var localeWeekStartsOn = locale$1.options && locale$1.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  if (formatString === "") {
    if (dateString === "") {
      return toDate(dirtyReferenceDate);
    } else {
      return /* @__PURE__ */ new Date(NaN);
    }
  }
  var subFnOptions = {
    firstWeekContainsDate,
    weekStartsOn,
    locale: locale$1
  };
  var setters = [{
    priority: TIMEZONE_UNIT_PRIORITY,
    subPriority: -1,
    set: dateToSystemTimezone,
    index: 0
  }];
  var i;
  var tokens = formatString.match(longFormattingTokensRegExp).map(function(substring) {
    var firstCharacter2 = substring[0];
    if (firstCharacter2 === "p" || firstCharacter2 === "P") {
      var longFormatter = longFormatters[firstCharacter2];
      return longFormatter(substring, locale$1.formatLong, subFnOptions);
    }
    return substring;
  }).join("").match(formattingTokensRegExp);
  var usedTokens = [];
  for (i = 0; i < tokens.length; i++) {
    var token = tokens[i];
    if (!options.useAdditionalWeekYearTokens && isProtectedWeekYearToken(token)) {
      throwProtectedError(token, formatString, dirtyDateString);
    }
    if (!options.useAdditionalDayOfYearTokens && isProtectedDayOfYearToken(token)) {
      throwProtectedError(token, formatString, dirtyDateString);
    }
    var firstCharacter = token[0];
    var parser = parsers[firstCharacter];
    if (parser) {
      var incompatibleTokens = parser.incompatibleTokens;
      if (Array.isArray(incompatibleTokens)) {
        var incompatibleToken = void 0;
        for (var _i = 0; _i < usedTokens.length; _i++) {
          var usedToken = usedTokens[_i].token;
          if (incompatibleTokens.indexOf(usedToken) !== -1 || usedToken === firstCharacter) {
            incompatibleToken = usedTokens[_i];
            break;
          }
        }
        if (incompatibleToken) {
          throw new RangeError("The format string mustn't contain `".concat(incompatibleToken.fullToken, "` and `").concat(token, "` at the same time"));
        }
      } else if (parser.incompatibleTokens === "*" && usedTokens.length) {
        throw new RangeError("The format string mustn't contain `".concat(token, "` and any other token at the same time"));
      }
      usedTokens.push({
        token: firstCharacter,
        fullToken: token
      });
      var parseResult = parser.parse(dateString, token, locale$1.match, subFnOptions);
      if (!parseResult) {
        return /* @__PURE__ */ new Date(NaN);
      }
      setters.push({
        priority: parser.priority,
        subPriority: parser.subPriority || 0,
        set: parser.set,
        validate: parser.validate,
        value: parseResult.value,
        index: setters.length
      });
      dateString = parseResult.rest;
    } else {
      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {
        throw new RangeError("Format string contains an unescaped latin alphabet character `" + firstCharacter + "`");
      }
      if (token === "''") {
        token = "'";
      } else if (firstCharacter === "'") {
        token = cleanEscapedString(token);
      }
      if (dateString.indexOf(token) === 0) {
        dateString = dateString.slice(token.length);
      } else {
        return /* @__PURE__ */ new Date(NaN);
      }
    }
  }
  if (dateString.length > 0 && notWhitespaceRegExp.test(dateString)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  var uniquePrioritySetters = setters.map(function(setter2) {
    return setter2.priority;
  }).sort(function(a, b) {
    return b - a;
  }).filter(function(priority, index, array) {
    return array.indexOf(priority) === index;
  }).map(function(priority) {
    return setters.filter(function(setter2) {
      return setter2.priority === priority;
    }).sort(function(a, b) {
      return b.subPriority - a.subPriority;
    });
  }).map(function(setterArray) {
    return setterArray[0];
  });
  var date = toDate(dirtyReferenceDate);
  if (isNaN(date)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));
  var flags = {};
  for (i = 0; i < uniquePrioritySetters.length; i++) {
    var setter = uniquePrioritySetters[i];
    if (setter.validate && !setter.validate(utcDate, setter.value, subFnOptions)) {
      return /* @__PURE__ */ new Date(NaN);
    }
    var result = setter.set(utcDate, flags, setter.value, subFnOptions);
    if (result[0]) {
      utcDate = result[0];
      assign(flags, result[1]);
    } else {
      utcDate = result;
    }
  }
  return utcDate;
}
function dateToSystemTimezone(date, flags) {
  if (flags.timestampIsSet) {
    return date;
  }
  var convertedDate = /* @__PURE__ */ new Date(0);
  convertedDate.setFullYear(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());
  convertedDate.setHours(date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds(), date.getUTCMilliseconds());
  return convertedDate;
}
function cleanEscapedString(input) {
  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, "'");
}
function isSameMonth(dirtyDateLeft, dirtyDateRight) {
  requiredArgs(2, arguments);
  var dateLeft = toDate(dirtyDateLeft);
  var dateRight = toDate(dirtyDateRight);
  return dateLeft.getFullYear() === dateRight.getFullYear() && dateLeft.getMonth() === dateRight.getMonth();
}
function isSameYear(dirtyDateLeft, dirtyDateRight) {
  requiredArgs(2, arguments);
  var dateLeft = toDate(dirtyDateLeft);
  var dateRight = toDate(dirtyDateRight);
  return dateLeft.getFullYear() === dateRight.getFullYear();
}
function isWithinInterval(dirtyDate, interval) {
  requiredArgs(2, arguments);
  var time = toDate(dirtyDate).getTime();
  var startTime = toDate(interval.start).getTime();
  var endTime = toDate(interval.end).getTime();
  if (!(startTime <= endTime)) {
    throw new RangeError("Invalid interval");
  }
  return time >= startTime && time <= endTime;
}
function setDay(dirtyDate, dirtyDay, dirtyOptions) {
  requiredArgs(2, arguments);
  var options = dirtyOptions || {};
  var locale = options.locale;
  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;
  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);
  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn);
  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {
    throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");
  }
  var date = toDate(dirtyDate);
  var day = toInteger(dirtyDay);
  var currentDay = date.getDay();
  var remainder = day % 7;
  var dayIndex = (remainder + 7) % 7;
  var delta = 7 - weekStartsOn;
  var diff = day < 0 || day > 6 ? day - (currentDay + delta) % 7 : (dayIndex + delta) % 7 - (currentDay + delta) % 7;
  return addDays(date, diff);
}
var monthHeaderCss = '/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.bosch-ic,.Bosch-Ic{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic:before,.Bosch-Ic:before{font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium,.Bosch-Ic-Medium{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium:before,.Bosch-Ic-Medium:before{font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-clickable,.Bosch-Ic-Clickable{cursor:pointer}.bosch-ic-clickable:disabled,.bosch-ic-clickable.disabled,.Bosch-Ic-Clickable:disabled,.Bosch-Ic-Clickable.disabled{color:#a4abb3;cursor:not-allowed}.bosch-ic-down:before{content:"\\e147"}.bosch-ic-forward-right:before{content:"\\e181"}.bosch-ic-back-left:before{content:"\\e0a0"}.bci-core-month-header{display:flex;justify-content:space-between;align-items:center;color:#000000;text-align:center;border-bottom:1px solid #8a9097}.bci-core-month-header .month-header{display:flex;justify-content:center;align-items:center;height:48px;min-width:2em;font-size:16px;font-weight:400;margin-left:14px;color:#000000;cursor:pointer;outline:none}.bci-core-month-header .month-header:hover,.bci-core-month-header .month-header:focus{color:#007bc0}.bci-core-month-header .month-header:active{color:#00629a}.bci-core-month-header .month-header p{margin:0}.bci-core-month-header .month-header .open-arrow{margin-left:4px}.bci-core-month-header .month-header .open-arrow>i{display:inline-block;width:24px;aspect-ratio:1}.bci-core-month-header .month-header.opened{color:#007bc0}.bci-core-month-header .month-header.opened:hover,.bci-core-month-header .month-header.opened:focus{color:#00629a}.bci-core-month-header .month-header.opened .open-arrow{transform:rotate(180deg)}.bci-core-month-header div{display:flex}.bci-core-month-header span:not(.open-arrow){display:grid;place-items:center;width:48px;aspect-ratio:1;color:#000000;outline:none}.bci-core-month-header span:not(.open-arrow):not(.disabled){cursor:pointer}.bci-core-month-header span:not(.open-arrow):not(.disabled):hover,.bci-core-month-header span:not(.open-arrow):not(.disabled):focus{color:#007bc0}.bci-core-month-header span:not(.open-arrow):not(.disabled):active{color:#00629a}.bci-core-month-header span:not(.open-arrow).disabled{pointer-events:none;color:#c1c7cc}';
var MonthHeader = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.arrowClicked = createEvent(this, "arrowClicked", 7);
    this.toggleShowYearMonthSelection = createEvent(this, "toggleShowYearMonthSelection", 7);
    this.year = void 0;
    this.month = void 0;
    this.yearMonthSelectionStep = void 0;
    this.displayedMonth = void 0;
    this.displayedYear = void 0;
  }
  handleMonthChange(newValue) {
    this.setFormattedMonth(newValue);
  }
  handleYearChange(newValue) {
    this.displayedYear = newValue;
  }
  componentWillLoad() {
    this.setFormattedMonth(this.month);
    this.displayedYear = this.year;
  }
  arrowClickHandler(arrowDirection) {
    this.arrowClicked.emit(arrowDirection);
  }
  handleKeyboardClick(event) {
    if (event.key !== "Enter" && event.key !== " " || event.defaultPrevented) return;
    event.target.click();
  }
  render() {
    return h("div", {
      class: "bci-core-month-header"
    }, h("div", {
      "data-test": "webcore.webcomponents.month-header.yearMonthSelection",
      class: {
        "month-header": true,
        opened: !!this.yearMonthSelectionStep
      },
      tabIndex: 0,
      role: "button",
      "aria-label": `${!!this.yearMonthSelectionStep ? "Close" : "Open"} the Year/Month Selection`,
      onKeyDown: (ev) => this.handleKeyboardClick(ev),
      onClick: () => {
        this.toggleShowYearMonthSelection.emit({
          newState: !!this.yearMonthSelectionStep ? false : true
        });
      }
    }, h("p", null, `${this.displayedMonth} ${this.displayedYear}` || "Month Header"), h("span", {
      class: "open-arrow"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-down"
    }))), h("div", null, h("span", {
      class: {
        "left-arrow": true,
        disabled: this.yearMonthSelectionStep === "month"
      },
      role: "button",
      tabIndex: 0,
      "aria-label": `Show previous ${!!this.yearMonthSelectionStep ? "20 years" : "month"}`,
      onKeyDown: (ev) => this.handleKeyboardClick(ev),
      onClick: () => this.arrowClickHandler({
        direction: "prev"
      })
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-back-left",
      "data-test": "webcore.webcomponents.month-header.month-change-handler.span"
    })), h("span", {
      class: {
        "right-arrow": true,
        disabled: this.yearMonthSelectionStep === "month"
      },
      role: "button",
      tabIndex: 0,
      "aria-label": `Show next ${!!this.yearMonthSelectionStep ? "20 years" : "month"}`,
      onKeyDown: (ev) => this.handleKeyboardClick(ev),
      onClick: () => this.arrowClickHandler({
        direction: "next"
      })
    }, h("i", {
      "data-test": "webcore.webcomponents.bci-core-month-header-month-change-header.next",
      class: "bosch-ic bosch-ic-clickable bosch-ic-forward-right"
    }))));
  }
  setFormattedMonth(value) {
    const locale = {
      locale: LOCALE_MAP[state.language || DEFAULT_LOCALE.language]
    };
    this.displayedMonth = format(setMonth(/* @__PURE__ */ new Date(), value), "MMMM", locale);
  }
  static get watchers() {
    return {
      "month": ["handleMonthChange"],
      "year": ["handleYearChange"]
    };
  }
};
MonthHeader.style = monthHeaderCss;
var weekHeaderCss = "/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */:host .bci-core-datepicker-week-container{display:flex;justify-content:space-between;padding:15px 5px 10px 5px}:host .bci-core-datepicker-week-container .bci-core-datepicker-week-header{font-size:12px;width:40px;line-height:18px;font-weight:700}";
var WeekHeader = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.sundayFirst = void 0;
  }
  getWeekDays() {
    const locale = {
      locale: LOCALE_MAP[state.language || DEFAULT_LOCALE.language]
    };
    const weekdays = [];
    for (let i = 0; i < 7; i++) {
      weekdays.push(format(setDay(/* @__PURE__ */ new Date(), i), "EEE", locale));
    }
    return weekdays;
  }
  render() {
    const weekDays = this.getWeekDays();
    if (!this.sundayFirst) {
      const sunday = weekDays.shift();
      weekDays.push(sunday);
    }
    return h("div", {
      "data-test": "webcore.webcomponents.week-header.bci-week-header.day",
      class: "bci-core-datepicker-week-container"
    }, weekDays.map((day) => h("th", {
      class: "bci-core-datepicker-week-header"
    }, h("span", null, day))));
  }
};
WeekHeader.style = weekHeaderCss;
var weekdaysCss = '/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.bci-core-weekdays-table{width:calc(100% - 10px);border-spacing:0;border-collapse:collapse;margin:0 5px 5px 5px}.bci-core-weekdays-table td{position:relative;height:0;line-height:0;text-align:center;outline:0;cursor:pointer;padding-top:8px;padding-bottom:32px}.bci-core-weekdays-table td .bci-core-date{color:#000000;font-size:16px;font-weight:400;text-align:center;display:flex;line-height:1;border-width:1px;border-style:solid;border-color:transparent;border-radius:50%;width:32px;height:32px;position:absolute;left:4px;top:4px;align-items:center;justify-content:center;box-sizing:border-box}.bci-core-weekdays-table td.bci-core-disabled{cursor:default}.bci-core-weekdays-table td.bci-core-disabled .bci-core-date{background:transparent;color:#c1c7cc}.bci-core-weekdays-table td.bci-core-disabled.bci-core-today .bci-core-date{border:1px solid #c1c7cc}.bci-core-weekdays-table td.bci-core-disabled.bci-core-selected .bci-core-date{background-color:#c1c7cc;color:#8a9097}.bci-core-weekdays-table td.bci-core-disabled.bci-core-selected.bci-core-range{border-radius:0px}.bci-core-weekdays-table td.bci-core-disabled.bci-core-selected.bci-core-range .bci-core-date{background-color:transparent}.bci-core-weekdays-table td.bci-core-disabled.bci-core-selected.bci-core-range.bci-core-first-date-range::before{content:"";display:block;width:41px;height:32px;position:absolute;left:4px;top:4px;background-color:#c1c7cc;border-radius:16px 0 0 16px}.bci-core-weekdays-table td.bci-core-disabled.bci-core-selected.bci-core-range.bci-core-last-date-range::before{content:"";display:block;width:41px;height:32px;position:absolute;left:4px;top:4px;background-color:#c1c7cc;width:32px;border-radius:0 16px 16px 0}.bci-core-weekdays-table td.bci-core-disabled.bci-core-selected.bci-core-range:not(.bci-core-first-date-range):not(.bci-core-last-date-range)::before{content:"";display:block;width:41px;height:32px;position:absolute;left:4px;top:4px;background-color:#c1c7cc;border-radius:0}.bci-core-weekdays-table td.bci-core-week-days{cursor:pointer}.bci-core-weekdays-table td.bci-core-week-days .bci-core-date:hover{background-color:#e0e2e5}.bci-core-weekdays-table td.bci-core-week-days .bci-core-date:active{background-color:#c1c7cc}.bci-core-weekdays-table td.bci-core-week-days.bci-core-today .bci-core-date{background:transparent;border:1px solid #000000;color:#000000}.bci-core-weekdays-table td.bci-core-week-days.bci-core-today .bci-core-date:hover{background-color:#e0e2e5}.bci-core-weekdays-table td.bci-core-week-days.bci-core-today .bci-core-date:active{background-color:#c1c7cc}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected .bci-core-date{background-color:#007bc0;color:#ffffff}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected .bci-core-date:hover{background-color:#00629a}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected .bci-core-date:active{background-color:#004975}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range{border-radius:0px}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range .bci-core-date{background-color:transparent}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range .bci-core-date:hover{background-color:#00629a}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range .bci-core-date:active{background-color:#004975}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range.bci-core-first-date-range::before{content:"";display:block;width:41px;height:32px;position:absolute;left:4px;top:4px;background-color:#007bc0;border-radius:16px 0 0 16px}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range.bci-core-last-date-range::before{content:"";display:block;width:41px;height:32px;position:absolute;left:4px;top:4px;background-color:#007bc0;width:32px;border-radius:0 16px 16px 0}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range:not(.bci-core-first-date-range):not(.bci-core-last-date-range)::before{content:"";display:block;width:41px;height:32px;position:absolute;left:4px;top:4px;background-color:#007bc0;border-radius:0}.bci-core-weekdays-table td.bci-core-week-days.bci-core-selected.bci-core-range:not(.bci-core-first-date-range):not(.bci-core-last-date-range).bci-core-last-week-day::before{width:32px}.bci-core-weekdays-table .bci-core-empty{visibility:hidden}.bci-core-weekdays-table .bci-core-empty:hover{cursor:not-allowed}';
var Weekdays = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.singleDateSelected = createEvent(this, "singleDateSelected", 7);
    this.rangeDateSelected = createEvent(this, "rangeDateSelected", 7);
    this.datesObj = void 0;
    this.daysInMonth = void 0;
    this.lastDayOfMonth = void 0;
    this.month = void 0;
    this.rangeDate = void 0;
    this.dateRestrictionObj = void 0;
    this.offset = void 0;
    this.selectedDate = void 0;
    this.year = void 0;
    this.todaysDate = void 0;
    this.formatDate = void 0;
    this.timeZone = void 0;
  }
  singleDateSelectedHandler(input) {
    this.singleDateSelected.emit(input);
  }
  rangeDateSelectedHandler(input) {
    this.rangeDateSelected.emit(input);
  }
  render() {
    const cells = [];
    const offset = this.offset % 7;
    for (let blank = 0; blank < offset; blank++) {
      cells.push(this.emptyDateCell());
    }
    for (let day = 1; day <= this.daysInMonth; day++) {
      let selected;
      let selectedRange;
      let selectedFirstDateRange;
      let selectedLastDateRange;
      const maxDate = this.dateRestrictionObj.maximumDate;
      const minDate = this.dateRestrictionObj.minimumDate;
      const currentDate = this.timeZone === void 0 ? createDate(/* @__PURE__ */ new Date(), this.year, this.month, day) : createDate(new Date((/* @__PURE__ */ new Date()).toLocaleString("en-US", {
        timeZone: this.timeZone
      })), this.year, this.month, day);
      const todaysDate = isSameDay(currentDate, parse(this.todaysDate, this.formatDate, /* @__PURE__ */ new Date()));
      if ((!isSameDay(minDate, currentDate) && isAfter(minDate, currentDate) || !isSameDay(maxDate, currentDate) && isBefore(maxDate, currentDate)) && this.datesObj.startDate && this.datesObj.endDate) {
        const startDate = parse(this.datesObj.startDate, this.formatDate, /* @__PURE__ */ new Date());
        const endDate = parse(this.datesObj.endDate, this.formatDate, /* @__PURE__ */ new Date());
        if (isAfter(currentDate, startDate) && isBefore(currentDate, endDate)) {
          selected = true;
          selectedRange = true;
        } else {
          selected = false;
          selectedRange = false;
        }
        if (isSameDay(currentDate, startDate)) {
          selectedFirstDateRange = true;
          selected = true;
        }
        if (isSameDay(currentDate, endDate)) {
          selectedLastDateRange = true;
          selected = true;
          selectedRange = true;
        }
        cells.push(h("td", {
          class: {
            "bci-core-disabled": true,
            "bci-core-today": todaysDate,
            "bci-core-selected": selected,
            "bci-core-range": selectedRange,
            "bci-core-first-date-range": selectedFirstDateRange,
            "bci-core-last-date-range": selectedLastDateRange
          }
        }, h("div", {
          class: "bci-core-date"
        }, day)));
        continue;
      }
      if (this.datesObj.startDate && this.datesObj.endDate) {
        const startDate = parse(this.datesObj.startDate, this.formatDate, /* @__PURE__ */ new Date());
        const endDate = parse(this.datesObj.endDate, this.formatDate, /* @__PURE__ */ new Date());
        if (isAfter(endDate, startDate) && isWithinInterval(currentDate, {
          start: startDate,
          end: endDate
        })) {
          selected = true;
          selectedRange = true;
        } else {
          selected = false;
        }
        if (isSameDay(currentDate, startDate)) {
          selectedFirstDateRange = true;
          selected = true;
        }
        if (isSameDay(currentDate, endDate)) {
          selectedLastDateRange = true;
          selected = true;
          selectedRange = true;
        }
        if (isSameDay(currentDate, startDate) && isSameDay(startDate, endDate)) {
          selected = true;
          selectedFirstDateRange = false;
          selectedLastDateRange = false;
          selectedRange = false;
        }
      } else {
        if (this.datesObj.startDate || this.selectedDate) {
          const selectedDate = parse(this.datesObj.startDate || this.selectedDate, this.formatDate, /* @__PURE__ */ new Date());
          selected = getDate(selectedDate) === day && getMonth(selectedDate) === this.month && getYear(selectedDate) === this.year;
        }
      }
      const classes = {
        "bci-core-week-days": true,
        "bci-core-selected": selected,
        "bci-core-range": selectedRange,
        "bci-core-first-date-range": selectedFirstDateRange,
        "bci-core-last-date-range": selectedLastDateRange,
        "bci-core-today": selected ? false : todaysDate,
        "bci-core-last-week-day": (cells.length + 1) % 7 === 0
      };
      cells.push(h("td", {
        "data-test": "webcore.webcomponents.weekdays.bci-weekdays.selected-day",
        class: classes,
        onClick: () => this.rangeDate ? this.rangeDateSelectedHandler({
          selectedDay: day
        }) : this.singleDateSelectedHandler({
          selectedDay: day
        })
      }, h("div", {
        class: "bci-core-date"
      }, day)));
    }
    while (cells.length % 7 !== 0) {
      cells.push(this.emptyDateCell());
    }
    const table = cells.reduce((acc, curr, index) => {
      const weekNumber = Math.floor(index / 7);
      acc[weekNumber].push(curr);
      return acc;
    }, [[], [], [], [], [], []]);
    return h("table", {
      class: "bci-core-weekdays-table"
    }, h("tbody", {
      class: "bci-core-week-days-container"
    }, table.sort().map((row, index) => h("tr", {
      key: index
    }, row.map((cell) => cell)))));
  }
  emptyDateCell() {
    return h("td", {
      class: "bci-core-empty"
    }, h("div", {
      class: "bci-core-date"
    }));
  }
};
Weekdays.style = weekdaysCss;
var datepickerCss = '/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */@font-face{font-family:var(--bci-global-font-family);font-weight:400;src:url("..//fonts/BoschSans-Regular.eot");src:url("..//fonts/BoschSans-Regular.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Regular.woff2") format("woff2"), url("..//fonts/BoschSans-Regular.woff") format("woff"), url("..//fonts/BoschSans-Regular.ttf") format("truetype"), url("..//fonts/BoschSans-RegularItalic.woff2") format("woff2"), url("..//fonts/BoschSans-RegularItalic.woff") format("woff"), url("..//fonts/BoschSans-RegularItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Regular.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:300;src:url("..//fonts/BoschSans-Light.eot");src:url("..//fonts/BoschSans-Light.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Light.woff2") format("woff2"), url("..//fonts/BoschSans-Light.woff") format("woff"), url("..//fonts/BoschSans-Light.ttf") format("truetype"), url("..//fonts/BoschSans-LightItalic.woff2") format("woff2"), url("..//fonts/BoschSans-LightItalic.woff") format("woff"), url("..//fonts/BoschSans-LightItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Light.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:500;src:url("..//fonts/BoschSans-Medium.eot");src:url("..//fonts/BoschSans-Medium.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Medium.woff2") format("woff2"), url("..//fonts/BoschSans-Medium.woff") format("woff"), url("..//fonts/BoschSans-Medium.ttf") format("truetype"), url("..//fonts/BoschSans-MediumItalic.woff2") format("woff2"), url("..//fonts/BoschSans-MediumItalic.woff") format("woff"), url("..//fonts/BoschSans-MediumItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Medium.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:700;src:url("..//fonts/BoschSans-Bold.eot");src:url("..//fonts/BoschSans-Bold.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Bold.woff2") format("woff2"), url("..//fonts/BoschSans-Bold.woff") format("woff"), url("..//fonts/BoschSans-Bold.ttf") format("truetype"), url("..//fonts/BoschSans-BoldItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BoldItalic.woff") format("woff"), url("..//fonts/BoschSans-BoldItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Bold.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:900;src:url("..//fonts/BoschSans-Black.eot");src:url("..//fonts/BoschSans-Black.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Black.woff2") format("woff2"), url("..//fonts/BoschSans-Black.woff") format("woff"), url("..//fonts/BoschSans-Black.ttf") format("truetype"), url("..//fonts/BoschSans-BlackItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BlackItalic.woff") format("woff"), url("..//fonts/BoschSans-BlackItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Black.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Ic";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url("..//fonts/Bosch-Icon.eot?mh5qa9");src:url("..//fonts/Bosch-Icon.eot?mh5qa9#iefix") format("embedded-opentype"), url("..//fonts/Bosch-Icon.ttf?mh5qa9") format("truetype"), url("..//fonts/Bosch-Icon.woff?mh5qa9") format("woff"), url("..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon") format("svg")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:300;src:url("..//fonts/BoschSansCond-Regular.otf"), url("..//fonts/BoschSansCondensed-Regular.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-RegularItalic.ttf") format("truetype")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:700;src:url("..//fonts/BoschSansCond-Bold.otf"), url("..//fonts/BoschSansCondensed-Bold.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-BoldItalic.ttf") format("truetype")}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */body{margin:0;display:flex;flex-direction:column}main,*{font-family:var(--bci-global-font-family)}::selection,::-moz-selection{background-color:#007bc0}a::-moz-selection{color:#ffffff}.lead{margin-bottom:24px;font-size:18px;font-weight:300;line-height:1.4}@media (min-width: 768px){.lead{font-size:24px}}.bci-core-datepicker{display:flex;flex-direction:column;position:relative;font-size:14px;width:292px}.bci-core-datepicker .bci-core-hide{display:none}.bci-core-datepicker .bci-core-datepicker-container{background:#ffffff;overflow-y:auto;overflow-x:hidden;white-space:normal;text-align:center;visibility:visible}.bci-core-datepicker .bci-core-datepicker-container .bci-core-month-container{display:inline-block;width:100%}';
var Datepicker = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.minDate = void 0;
    this.maxDate = void 0;
    this.rangeDate = void 0;
    this.sundayFirst = void 0;
    this.bciDatepickerId = void 0;
    this.formatDate = void 0;
    this.datePickerInputObj = void 0;
    this.timeZone = void 0;
    this.today = void 0;
    this.selectedDate = void 0;
    this.selectedMonth = void 0;
    this.selectedYear = void 0;
    this.datesObj = void 0;
  }
  componentWillLoad() {
    return __async(this, null, function* () {
      yield setLocale();
      if (!this.formatDate) {
        this.formatDate = state.dateFormat;
      }
    });
  }
  render() {
    const currentMonth = this.selectedMonth;
    const isoWeekday = getISODay(createDate(/* @__PURE__ */ new Date(), this.selectedYear, currentMonth, 0));
    const offset = this.sundayFirst ? isoWeekday + 1 : isoWeekday;
    const minimumDate = new Date(this.minDate);
    const maximumDate = new Date(this.maxDate);
    const dateRestrictionObj = {
      minimumDate,
      maximumDate
    };
    return h("div", {
      class: "bci-core-datepicker"
    }, h("div", {
      class: "bci-core-datepicker-container"
    }, h("div", {
      class: "bci-core-month-container"
    }, h("bci-week-header", {
      "data-test": "webcore.webcomponents.bci-datepicker.sunday-first",
      "sunday-first": this.sundayFirst
    }), h("bci-weekdays", {
      "data-test": "webcore.webcomponents.bci-datepicker.select-date",
      datesObj: this.datesObj,
      daysInMonth: getDaysInMonth(createDate(/* @__PURE__ */ new Date(), this.selectedYear, currentMonth)),
      lastDayOfMonth: endOfMonth(createDate(/* @__PURE__ */ new Date(), this.selectedYear, this.selectedMonth, 0)),
      dateRestrictionObj,
      month: currentMonth,
      rangeDate: this.rangeDate,
      offset,
      selectedDate: this.selectedDate,
      todaysDate: this.today,
      year: this.selectedYear,
      formatDate: this.formatDate,
      timeZone: this.timeZone
    }))));
  }
};
Datepicker.style = datepickerCss;
var toInteger_1 = createCommonjsModule(function(module, exports) {
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = toInteger3;
  function toInteger3(dirtyNumber) {
    if (dirtyNumber === null || dirtyNumber === true || dirtyNumber === false) {
      return NaN;
    }
    var number = Number(dirtyNumber);
    if (isNaN(number)) {
      return number;
    }
    return number < 0 ? Math.ceil(number) : Math.floor(number);
  }
  module.exports = exports.default;
});
var toInteger2 = getDefaultExportFromCjs(toInteger_1);
var getTimezoneOffsetInMilliseconds_1 = createCommonjsModule(function(module, exports) {
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = getTimezoneOffsetInMilliseconds3;
  function getTimezoneOffsetInMilliseconds3(date) {
    var utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));
    utcDate.setUTCFullYear(date.getFullYear());
    return date.getTime() - utcDate.getTime();
  }
  module.exports = exports.default;
});
var getTimezoneOffsetInMilliseconds2 = getDefaultExportFromCjs(getTimezoneOffsetInMilliseconds_1);
function tzTokenizeDate(date, timeZone) {
  var dtf = getDateTimeFormat(timeZone);
  return dtf.formatToParts ? partsOffset(dtf, date) : hackyOffset(dtf, date);
}
var typeToPos = {
  year: 0,
  month: 1,
  day: 2,
  hour: 3,
  minute: 4,
  second: 5
};
function partsOffset(dtf, date) {
  try {
    var formatted = dtf.formatToParts(date);
    var filled = [];
    for (var i = 0; i < formatted.length; i++) {
      var pos = typeToPos[formatted[i].type];
      if (pos >= 0) {
        filled[pos] = parseInt(formatted[i].value, 10);
      }
    }
    return filled;
  } catch (error) {
    if (error instanceof RangeError) {
      return [NaN];
    }
    throw error;
  }
}
function hackyOffset(dtf, date) {
  var formatted = dtf.format(date).replace(/\u200E/g, "");
  var parsed = /(\d+)\/(\d+)\/(\d+),? (\d+):(\d+):(\d+)/.exec(formatted);
  return [parsed[3], parsed[1], parsed[2], parsed[4], parsed[5], parsed[6]];
}
var dtfCache = {};
function getDateTimeFormat(timeZone) {
  if (!dtfCache[timeZone]) {
    var testDateFormatted = new Intl.DateTimeFormat("en-US", {
      hour12: false,
      timeZone: "America/New_York",
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    }).format(/* @__PURE__ */ new Date("2014-06-25T04:00:00.123Z"));
    var hourCycleSupported = testDateFormatted === "06/25/2014, 00:00:00" || testDateFormatted === "‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00";
    dtfCache[timeZone] = hourCycleSupported ? new Intl.DateTimeFormat("en-US", {
      hour12: false,
      timeZone,
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    }) : new Intl.DateTimeFormat("en-US", {
      hourCycle: "h23",
      timeZone,
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    });
  }
  return dtfCache[timeZone];
}
function newDateUTC(fullYear, month, day, hour, minute, second, millisecond) {
  var utcDate = /* @__PURE__ */ new Date(0);
  utcDate.setUTCFullYear(fullYear, month, day);
  utcDate.setUTCHours(hour, minute, second, millisecond);
  return utcDate;
}
var MILLISECONDS_IN_HOUR$1 = 36e5;
var MILLISECONDS_IN_MINUTE$1 = 6e4;
var patterns$1 = {
  timezone: /([Z+-].*)$/,
  timezoneZ: /^(Z)$/,
  timezoneHH: /^([+-]\d{2})$/,
  timezoneHHMM: /^([+-]\d{2}):?(\d{2})$/
};
function tzParseTimezone(timezoneString, date, isUtcDate) {
  var token;
  var absoluteOffset;
  if (timezoneString === "") {
    return 0;
  }
  token = patterns$1.timezoneZ.exec(timezoneString);
  if (token) {
    return 0;
  }
  var hours;
  token = patterns$1.timezoneHH.exec(timezoneString);
  if (token) {
    hours = parseInt(token[1], 10);
    if (!validateTimezone(hours)) {
      return NaN;
    }
    return -(hours * MILLISECONDS_IN_HOUR$1);
  }
  token = patterns$1.timezoneHHMM.exec(timezoneString);
  if (token) {
    hours = parseInt(token[1], 10);
    var minutes = parseInt(token[2], 10);
    if (!validateTimezone(hours, minutes)) {
      return NaN;
    }
    absoluteOffset = Math.abs(hours) * MILLISECONDS_IN_HOUR$1 + minutes * MILLISECONDS_IN_MINUTE$1;
    return hours > 0 ? -absoluteOffset : absoluteOffset;
  }
  if (isValidTimezoneIANAString(timezoneString)) {
    date = new Date(date || Date.now());
    var utcDate = isUtcDate ? date : toUtcDate(date);
    var offset = calcOffset(utcDate, timezoneString);
    var fixedOffset = isUtcDate ? offset : fixOffset(date, offset, timezoneString);
    return -fixedOffset;
  }
  return NaN;
}
function toUtcDate(date) {
  return newDateUTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
}
function calcOffset(date, timezoneString) {
  var tokens = tzTokenizeDate(date, timezoneString);
  var asUTC = newDateUTC(tokens[0], tokens[1] - 1, tokens[2], tokens[3] % 24, tokens[4], tokens[5], 0).getTime();
  var asTS = date.getTime();
  var over = asTS % 1e3;
  asTS -= over >= 0 ? over : 1e3 + over;
  return asUTC - asTS;
}
function fixOffset(date, offset, timezoneString) {
  var localTS = date.getTime();
  var utcGuess = localTS - offset;
  var o2 = calcOffset(new Date(utcGuess), timezoneString);
  if (offset === o2) {
    return offset;
  }
  utcGuess -= o2 - offset;
  var o3 = calcOffset(new Date(utcGuess), timezoneString);
  if (o2 === o3) {
    return o2;
  }
  return Math.max(o2, o3);
}
function validateTimezone(hours, minutes) {
  return -23 <= hours && hours <= 23 && (minutes == null || 0 <= minutes && minutes <= 59);
}
var validIANATimezoneCache = {};
function isValidTimezoneIANAString(timeZoneString) {
  if (validIANATimezoneCache[timeZoneString]) return true;
  try {
    validIANATimezoneCache[timeZoneString] = true;
    return true;
  } catch (error) {
    return false;
  }
}
var tzPattern = /(Z|[+-]\d{2}(?::?\d{2})?| UTC| [a-zA-Z]+\/[a-zA-Z_]+(?:\/[a-zA-Z_]+)?)$/;
var MILLISECONDS_IN_HOUR = 36e5;
var MILLISECONDS_IN_MINUTE = 6e4;
var DEFAULT_ADDITIONAL_DIGITS = 2;
var patterns = {
  dateTimePattern: /^([0-9W+-]+)(T| )(.*)/,
  datePattern: /^([0-9W+-]+)(.*)/,
  plainTime: /:/,
  // year tokens
  YY: /^(\d{2})$/,
  YYY: [
    /^([+-]\d{2})$/,
    // 0 additional digits
    /^([+-]\d{3})$/,
    // 1 additional digit
    /^([+-]\d{4})$/
    // 2 additional digits
  ],
  YYYY: /^(\d{4})/,
  YYYYY: [
    /^([+-]\d{4})/,
    // 0 additional digits
    /^([+-]\d{5})/,
    // 1 additional digit
    /^([+-]\d{6})/
    // 2 additional digits
  ],
  // date tokens
  MM: /^-(\d{2})$/,
  DDD: /^-?(\d{3})$/,
  MMDD: /^-?(\d{2})-?(\d{2})$/,
  Www: /^-?W(\d{2})$/,
  WwwD: /^-?W(\d{2})-?(\d{1})$/,
  HH: /^(\d{2}([.,]\d*)?)$/,
  HHMM: /^(\d{2}):?(\d{2}([.,]\d*)?)$/,
  HHMMSS: /^(\d{2}):?(\d{2}):?(\d{2}([.,]\d*)?)$/,
  // time zone tokens (to identify the presence of a tz)
  timeZone: tzPattern
};
function toDate2(argument, dirtyOptions) {
  if (arguments.length < 1) {
    throw new TypeError("1 argument required, but only " + arguments.length + " present");
  }
  if (argument === null) {
    return /* @__PURE__ */ new Date(NaN);
  }
  var options = dirtyOptions || {};
  var additionalDigits = options.additionalDigits == null ? DEFAULT_ADDITIONAL_DIGITS : toInteger2(options.additionalDigits);
  if (additionalDigits !== 2 && additionalDigits !== 1 && additionalDigits !== 0) {
    throw new RangeError("additionalDigits must be 0, 1 or 2");
  }
  if (argument instanceof Date || typeof argument === "object" && Object.prototype.toString.call(argument) === "[object Date]") {
    return new Date(argument.getTime());
  } else if (typeof argument === "number" || Object.prototype.toString.call(argument) === "[object Number]") {
    return new Date(argument);
  } else if (!(typeof argument === "string" || Object.prototype.toString.call(argument) === "[object String]")) {
    return /* @__PURE__ */ new Date(NaN);
  }
  var dateStrings = splitDateString(argument);
  var parseYearResult = parseYear(dateStrings.date, additionalDigits);
  var year = parseYearResult.year;
  var restDateString = parseYearResult.restDateString;
  var date = parseDate(restDateString, year);
  if (isNaN(date)) {
    return /* @__PURE__ */ new Date(NaN);
  }
  if (date) {
    var timestamp = date.getTime();
    var time = 0;
    var offset;
    if (dateStrings.time) {
      time = parseTime(dateStrings.time);
      if (isNaN(time)) {
        return /* @__PURE__ */ new Date(NaN);
      }
    }
    if (dateStrings.timeZone || options.timeZone) {
      offset = tzParseTimezone(dateStrings.timeZone || options.timeZone, new Date(timestamp + time));
      if (isNaN(offset)) {
        return /* @__PURE__ */ new Date(NaN);
      }
    } else {
      offset = getTimezoneOffsetInMilliseconds2(new Date(timestamp + time));
      offset = getTimezoneOffsetInMilliseconds2(new Date(timestamp + time + offset));
    }
    return new Date(timestamp + time + offset);
  } else {
    return /* @__PURE__ */ new Date(NaN);
  }
}
function splitDateString(dateString) {
  var dateStrings = {};
  var parts = patterns.dateTimePattern.exec(dateString);
  var timeString;
  if (!parts) {
    parts = patterns.datePattern.exec(dateString);
    if (parts) {
      dateStrings.date = parts[1];
      timeString = parts[2];
    } else {
      dateStrings.date = null;
      timeString = dateString;
    }
  } else {
    dateStrings.date = parts[1];
    timeString = parts[3];
  }
  if (timeString) {
    var token = patterns.timeZone.exec(timeString);
    if (token) {
      dateStrings.time = timeString.replace(token[1], "");
      dateStrings.timeZone = token[1].trim();
    } else {
      dateStrings.time = timeString;
    }
  }
  return dateStrings;
}
function parseYear(dateString, additionalDigits) {
  var patternYYY = patterns.YYY[additionalDigits];
  var patternYYYYY = patterns.YYYYY[additionalDigits];
  var token;
  token = patterns.YYYY.exec(dateString) || patternYYYYY.exec(dateString);
  if (token) {
    var yearString = token[1];
    return {
      year: parseInt(yearString, 10),
      restDateString: dateString.slice(yearString.length)
    };
  }
  token = patterns.YY.exec(dateString) || patternYYY.exec(dateString);
  if (token) {
    var centuryString = token[1];
    return {
      year: parseInt(centuryString, 10) * 100,
      restDateString: dateString.slice(centuryString.length)
    };
  }
  return {
    year: null
  };
}
function parseDate(dateString, year) {
  if (year === null) {
    return null;
  }
  var token;
  var date;
  var month;
  var week;
  if (dateString.length === 0) {
    date = /* @__PURE__ */ new Date(0);
    date.setUTCFullYear(year);
    return date;
  }
  token = patterns.MM.exec(dateString);
  if (token) {
    date = /* @__PURE__ */ new Date(0);
    month = parseInt(token[1], 10) - 1;
    if (!validateDate(year, month)) {
      return /* @__PURE__ */ new Date(NaN);
    }
    date.setUTCFullYear(year, month);
    return date;
  }
  token = patterns.DDD.exec(dateString);
  if (token) {
    date = /* @__PURE__ */ new Date(0);
    var dayOfYear = parseInt(token[1], 10);
    if (!validateDayOfYearDate(year, dayOfYear)) {
      return /* @__PURE__ */ new Date(NaN);
    }
    date.setUTCFullYear(year, 0, dayOfYear);
    return date;
  }
  token = patterns.MMDD.exec(dateString);
  if (token) {
    date = /* @__PURE__ */ new Date(0);
    month = parseInt(token[1], 10) - 1;
    var day = parseInt(token[2], 10);
    if (!validateDate(year, month, day)) {
      return /* @__PURE__ */ new Date(NaN);
    }
    date.setUTCFullYear(year, month, day);
    return date;
  }
  token = patterns.Www.exec(dateString);
  if (token) {
    week = parseInt(token[1], 10) - 1;
    if (!validateWeekDate(year, week)) {
      return /* @__PURE__ */ new Date(NaN);
    }
    return dayOfISOWeekYear(year, week);
  }
  token = patterns.WwwD.exec(dateString);
  if (token) {
    week = parseInt(token[1], 10) - 1;
    var dayOfWeek = parseInt(token[2], 10) - 1;
    if (!validateWeekDate(year, week, dayOfWeek)) {
      return /* @__PURE__ */ new Date(NaN);
    }
    return dayOfISOWeekYear(year, week, dayOfWeek);
  }
  return null;
}
function parseTime(timeString) {
  var token;
  var hours;
  var minutes;
  token = patterns.HH.exec(timeString);
  if (token) {
    hours = parseFloat(token[1].replace(",", "."));
    if (!validateTime(hours)) {
      return NaN;
    }
    return hours % 24 * MILLISECONDS_IN_HOUR;
  }
  token = patterns.HHMM.exec(timeString);
  if (token) {
    hours = parseInt(token[1], 10);
    minutes = parseFloat(token[2].replace(",", "."));
    if (!validateTime(hours, minutes)) {
      return NaN;
    }
    return hours % 24 * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE;
  }
  token = patterns.HHMMSS.exec(timeString);
  if (token) {
    hours = parseInt(token[1], 10);
    minutes = parseInt(token[2], 10);
    var seconds = parseFloat(token[3].replace(",", "."));
    if (!validateTime(hours, minutes, seconds)) {
      return NaN;
    }
    return hours % 24 * MILLISECONDS_IN_HOUR + minutes * MILLISECONDS_IN_MINUTE + seconds * 1e3;
  }
  return null;
}
function dayOfISOWeekYear(isoWeekYear, week, day) {
  week = week || 0;
  day = day || 0;
  var date = /* @__PURE__ */ new Date(0);
  date.setUTCFullYear(isoWeekYear, 0, 4);
  var fourthOfJanuaryDay = date.getUTCDay() || 7;
  var diff = week * 7 + day + 1 - fourthOfJanuaryDay;
  date.setUTCDate(date.getUTCDate() + diff);
  return date;
}
var DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var DAYS_IN_MONTH_LEAP_YEAR = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
function isLeapYearIndex(year) {
  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;
}
function validateDate(year, month, date) {
  if (month < 0 || month > 11) {
    return false;
  }
  if (date != null) {
    if (date < 1) {
      return false;
    }
    var isLeapYear = isLeapYearIndex(year);
    if (isLeapYear && date > DAYS_IN_MONTH_LEAP_YEAR[month]) {
      return false;
    }
    if (!isLeapYear && date > DAYS_IN_MONTH[month]) {
      return false;
    }
  }
  return true;
}
function validateDayOfYearDate(year, dayOfYear) {
  if (dayOfYear < 1) {
    return false;
  }
  var isLeapYear = isLeapYearIndex(year);
  if (isLeapYear && dayOfYear > 366) {
    return false;
  }
  if (!isLeapYear && dayOfYear > 365) {
    return false;
  }
  return true;
}
function validateWeekDate(year, week, day) {
  if (week < 0 || week > 52) {
    return false;
  }
  if (day != null && (day < 0 || day > 6)) {
    return false;
  }
  return true;
}
function validateTime(hours, minutes, seconds) {
  if (hours != null && (hours < 0 || hours >= 25)) {
    return false;
  }
  if (minutes != null && (minutes < 0 || minutes >= 60)) {
    return false;
  }
  if (seconds != null && (seconds < 0 || seconds >= 60)) {
    return false;
  }
  return true;
}
var assign_1 = createCommonjsModule(function(module, exports) {
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = assign2;
  function assign2(target, dirtyObject) {
    if (target == null) {
      throw new TypeError("assign requires that input parameter not be null or undefined");
    }
    dirtyObject = dirtyObject || {};
    for (var property in dirtyObject) {
      if (Object.prototype.hasOwnProperty.call(dirtyObject, property)) {
        target[property] = dirtyObject[property];
      }
    }
    return target;
  }
  module.exports = exports.default;
});
var cloneObject_1 = createCommonjsModule(function(module, exports) {
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = cloneObject2;
  var _index = _interopRequireDefault(assign_1);
  function _interopRequireDefault(obj) {
    return obj && obj.__esModule ? obj : {
      default: obj
    };
  }
  function cloneObject2(dirtyObject) {
    return (0, _index.default)({}, dirtyObject);
  }
  module.exports = exports.default;
});
var cloneObject = getDefaultExportFromCjs(cloneObject_1);
function utcToZonedTime(dirtyDate, timeZone, options) {
  var date = toDate2(dirtyDate, options);
  var offsetMilliseconds = tzParseTimezone(timeZone, date, true);
  var d = new Date(date.getTime() - offsetMilliseconds);
  return new Date(d.getUTCFullYear(), d.getUTCMonth(), d.getUTCDate(), d.getUTCHours(), d.getUTCMinutes(), d.getUTCSeconds(), d.getUTCMilliseconds());
}
function zonedTimeToUtc(date, timeZone, options) {
  if (typeof date === "string" && !date.match(tzPattern)) {
    var extendedOptions = cloneObject(options);
    extendedOptions.timeZone = timeZone;
    return toDate2(date, extendedOptions);
  }
  var d = toDate2(date, options);
  var utc = newDateUTC(d.getFullYear(), d.getMonth(), d.getDate(), d.getHours(), d.getMinutes(), d.getSeconds(), d.getMilliseconds()).getTime();
  var offsetMilliseconds = tzParseTimezone(timeZone, new Date(utc));
  return new Date(utc + offsetMilliseconds);
}
var defaultFactoryConfig = {
  dateFormat: DEFAULT_LOCALE.dateFormat,
  timeFormat: DEFAULT_LOCALE.timeFormat
};
function formattingServiceFactory(config = {}) {
  const {
    dateFormat,
    timeFormat,
    timeZone
  } = Object.assign(Object.assign({}, defaultFactoryConfig), config);
  return new FormattingService(dateFormat, timeFormat, timeZone);
}
var FormattingService = class {
  get locale() {
    var _a;
    return (_a = LOCALE_MAP[state.language]) !== null && _a !== void 0 ? _a : LOCALE_MAP[DEFAULT_LOCALE.language];
  }
  constructor(dateFormat, timeFormat, timeZone) {
    this.dateFormat = dateFormat;
    this.timeFormat = timeFormat;
    this.timeZone = timeZone;
  }
  /**
   * Returns date in any format with utc timezone set by property time-zone or with local browser timezone
   * @param date
   * @returns
   */
  formatDate(date) {
    if (!date) {
      return "";
    }
    if (typeof date === "number") {
      return this.formatNumber(date, this.dateFormat);
    }
    if (typeof date === "string") {
      return format(this.formatDateToDate(date), this.dateFormat);
    }
    return format(date, this.dateFormat);
  }
  formatDateToDate(date) {
    return parse(date, this.dateFormat, /* @__PURE__ */ new Date());
  }
  /**
   * Returns time in any format with utc timezone set by property time-zone or with local browser timezone
   * @param time
   * @returns
   */
  formatTime(time) {
    if (!time) {
      return "";
    }
    if (typeof time === "number") {
      return this.formatNumber(time, this.timeFormat);
    }
    return format(time, this.timeFormat);
  }
  /**
   * Formats a date/time range for display
   * @param {string} [dateStart] - The start date
   * @param {string} [dateEnd] - The end date
   * @param {string} [timeStart] - The start time
   * @param {string} [timeEnd] - The end time
   * @returns {string} The formatted date/time range string
   */
  formatRange(dateStart, dateEnd, timeStart, timeEnd) {
    let value = "";
    if (dateStart) {
      value += dateStart;
    }
    if (dateStart && timeStart) {
      value += " ";
    }
    if (timeStart) {
      value += timeStart;
    }
    if (dateStart && dateEnd && dateEnd !== dateStart || timeStart && timeEnd && timeEnd !== timeStart) {
      value += " – ";
    }
    if (dateEnd && dateEnd !== dateStart) {
      value += dateEnd;
    }
    if (dateEnd && timeEnd && dateEnd !== dateStart) {
      value += " ";
    }
    if (timeEnd && (timeStart !== timeEnd || dateStart !== dateEnd)) {
      value += timeEnd;
    }
    return value;
  }
  formatDateWithOptionalTime(date, time) {
    const dateString = time ? date + " " + time : date;
    const format2 = time ? this.dateFormat + " " + this.timeFormat : this.dateFormat;
    return parse(dateString, format2, /* @__PURE__ */ new Date());
  }
  timestampToZonedTime(timestamp) {
    if (this.timeZone) {
      return utcToZonedTime(fromUnixTime(timestamp), this.timeZone);
    }
    return fromUnixTime(timestamp);
  }
  formatCalendarWeek(date, weekStartsOn, calendarWeekViewMode) {
    if (typeof date === "string") {
      date = this.formatDateToDate(date);
    } else {
      date = new Date(date);
    }
    if (!isValid(date)) {
      return "";
    }
    const calWeek = translate("calendarWeek").replace("{{calendar-week}}", format(date, "w", {
      firstWeekContainsDate: 4,
      weekStartsOn,
      locale: this.locale
    }));
    const fullFormat = "d MMM yyyy";
    if (calendarWeekViewMode === "day") {
      return `${calWeek} | ${format(date, fullFormat, {
        locale: this.locale
      })}`;
    }
    const weekStart = startOfWeek(date, {
      weekStartsOn
    });
    const weekEnd = endOfWeek(date, {
      weekStartsOn
    });
    let startFormat = "d";
    if (weekStart.getFullYear() !== weekEnd.getFullYear()) {
      startFormat = fullFormat;
    } else if (weekStart.getMonth() !== weekEnd.getMonth()) {
      startFormat = "d MMM";
    }
    return `${calWeek} | ${format(weekStart, startFormat, {
      locale: this.locale
    })} – ${format(weekEnd, fullFormat, {
      locale: this.locale
    })}`;
  }
  formatNumber(timestamp, formatting) {
    return format(this.timestampToZonedTime(timestamp / 1e3), formatting);
  }
};
var defaultDateConfig = {
  formatService: formattingServiceFactory(),
  rangeDateTime: false,
  hideSeconds: false,
  minDate: 0,
  maxDate: 0
};
var FORMAT_BLOCKS = {
  dd: /(0[1-9]|[12][0-9]|3[01])/,
  MM: /(0[1-9]|1[0-2])/,
  yy: /([0-9]{2})/,
  d: /([1-9]|[12][0-9]|3[01])/,
  M: /([1-9]|1[0-2])/,
  y: /([12][0-9]{3})/,
  h: /(0?[0-9]|1[0-2])/,
  H: /(0?[0-9]|[1][0-9]|2[0-3])/,
  mm: /([0-5][0-9])/,
  ss: /([0-5][0-9])/,
  " a": /( [AaPp][Mm])/
};
var SUPPORTED_DATE_FORMATS_DEFAULT = ["dd.MM.y", "MM/dd/y", "dd/MM/y", "y-MM-dd", "d.M.y", "M/d/y", "d/M/y", "y-M-d", "dd.MM.yy", "MM/dd/yy", "dd/MM/yy", "yy-MM-dd", "d.M.yy", "M/d/yy", "d/M/yy", "yy-M-d"];
var SUPPORTED_DATE_FORMATS_START_WITH_DAY = ["dd.MM.y", "dd/MM/y", "dd.MM.yy", "dd/MM/yy", "MM/dd/y", "MM/dd/yy", "y-MM-dd", "d.M.y", "d/M/y", "M/d/y", "y-M-d", "yy-MM-dd", "d.M.yy", "d/M/yy", "M/d/yy", "yy-M-d"];
var SUPPORTED_TIME_FORMATS = ["h:mm:ss a", "H:mm:ss", "h:mm a", "H:mm", "h a"];
function dateServiceFactory(config = {}) {
  const {
    hideSeconds,
    rangeDateTime,
    minDate,
    maxDate,
    formatService
  } = Object.assign(Object.assign({}, defaultDateConfig), config);
  return new DateService(hideSeconds, rangeDateTime, minDate, maxDate, formatService);
}
var DateService = class {
  constructor(hideSeconds, rangeDateTime, minDate, maxDate, formatService) {
    this.hideSeconds = hideSeconds;
    this.rangeDateTime = rangeDateTime;
    this.minDate = minDate;
    this.maxDate = maxDate;
    this.formatService = formatService;
  }
  getTodayValue(timeZone = void 0) {
    return timeZone === void 0 ? /* @__PURE__ */ new Date() : new Date((/* @__PURE__ */ new Date()).toLocaleString("en-US", {
      timeZone
    }));
  }
  getDataFromInput(input) {
    const dates = this.getDateFromDateInput(input);
    const times = this.getTimeFromDateInput(input);
    const correctedData = this.correctInputData(Object.assign(Object.assign({}, dates), times));
    return Object.assign(Object.assign({}, correctedData), {
      valid: true
    });
  }
  /**
   * check whether the input contains valid date and time values and that they are also in the correct time order.
   * @param input
   * @returns
   */
  validateInput(input) {
    const dateStart = startOfDay(this.formatService.formatDateToDate(input.dateStart || ""));
    const dateEnd = startOfDay(this.formatService.formatDateToDate(input.dateEnd || ""));
    if (isAfter(dateStart, endOfDay(this.maxDate)) || isBefore(dateStart, startOfDay(this.minDate)) || isAfter(dateEnd, endOfDay(this.maxDate)) || isBefore(dateEnd, startOfDay(this.minDate)) || isAfter(dateStart, dateEnd)) {
      return {
        valid: false,
        timeEnd: null,
        dateEnd: null
      };
    } else {
      return {
        valid: true
      };
    }
  }
  /**
   * Corrects date values, puts them within allowed ranges and swaps values if necessary.
   * @param dateObj dateStart, dateEnd, timeStart, timeEnd
   * @returns corrected dateStart, dateEnd, timeStart, timeEnd
   */
  correctInputData(dateObj) {
    const {
      dateStart,
      dateEnd,
      timeStart,
      timeEnd
    } = dateObj;
    let parsedDateStart, parsedDateEnd;
    if (!!dateStart && !!timeStart && !!timeEnd && !dateEnd) {
      parsedDateStart = this.dateWithinAllowedRange(this.formatService.formatDateWithOptionalTime(dateStart, timeStart));
      parsedDateEnd = this.dateWithinAllowedRange(this.formatService.formatDateWithOptionalTime(dateStart, timeEnd));
      if (isBefore(parsedDateEnd, parsedDateStart)) {
        return Object.assign({}, this.createInputData(parsedDateEnd, parsedDateStart));
      }
    } else if (!dateStart || !dateEnd) {
      if (!dateStart) {
        return dateObj;
      } else if (dateStart && !dateEnd) {
        parsedDateStart = this.formatService.formatDateWithOptionalTime(dateStart, timeStart);
        return Object.assign({}, this.createInputData(parsedDateStart, null));
      }
    } else {
      parsedDateStart = this.dateWithinAllowedRange(this.formatService.formatDateWithOptionalTime(dateStart, timeStart));
      parsedDateEnd = this.dateWithinAllowedRange(this.formatService.formatDateWithOptionalTime(dateEnd, timeEnd));
      if (isBefore(parsedDateEnd, parsedDateStart)) {
        return Object.assign({}, this.createInputData(parsedDateEnd, parsedDateStart));
      }
    }
    return Object.assign({}, this.createInputData(parsedDateStart, parsedDateEnd));
  }
  dateWithinAllowedRange(date) {
    return clamp(date, {
      start: this.formatService.timestampToZonedTime(this.minDate / 1e3),
      end: this.maxDate === 0 ? Infinity : this.formatService.timestampToZonedTime(this.maxDate / 1e3)
    });
  }
  /**
   * Creates valid InputData object from two dates.
   * @param dateStart Date to infer dateStart and timeStart from
   * @param dateEnd Date to infer dateEnd and timeEnd from
   */
  createInputData(dateStart, dateEnd) {
    return {
      dateStart: this.formatService.formatDate(dateStart),
      dateEnd: this.formatService.formatDate(dateEnd) || null,
      timeStart: this.formatService.formatTime(dateStart),
      timeEnd: this.formatService.formatTime(dateEnd) || null
    };
  }
  /**
   * Returns utc timestamp created by timezone from property time-zone or with local browser timezone
   * @param date string parsed with formatDate
   * @param time string in format 00:00
   */
  toSelectionTimestamp(date, time) {
    if (!date || !this.formatService.timeZone) {
      return void 0;
    }
    const dateTime = this.formatService.formatDateWithOptionalTime(date, time);
    return getUnixTime(zonedTimeToUtc(dateTime, this.formatService.timeZone)) * 1e3;
  }
  getDateFromDateInput(input) {
    const matches = this.getPartFromDateInput(input, "dateStart", "dateEnd", this.DATE_FORMATS);
    const result = {
      dateStart: null,
      dateEnd: null
    };
    result.dateStart = matches.dateStart ? this.formatService.formatDate(matches.dateStart) : null;
    result.dateEnd = matches.dateEnd ? this.formatService.formatDate(matches.dateEnd) : null;
    return result;
  }
  getTimeFromDateInput(input) {
    const matches = this.getPartFromDateInput(input, "timeStart", "timeEnd", this.TIME_FORMATS);
    const result = {
      timeStart: null,
      timeEnd: null
    };
    result.timeStart = matches.timeStart ? this.formatService.formatTime(matches.timeStart) : null;
    result.timeEnd = matches.timeEnd ? this.formatService.formatTime(matches.timeEnd) : null;
    return result;
  }
  getPartFromDateInput(input, startKey, endKey, tests) {
    var _a, _b;
    let match;
    let countMatches = 0;
    const dateObj = {
      [startKey]: null,
      [endKey]: null
    };
    const result = {
      [startKey]: null,
      [endKey]: null
    };
    Object.keys(tests).forEach((key) => {
      var _a2;
      const pattern = tests[key];
      while ((match = pattern.exec(input)) !== null) {
        if (match.index === pattern.lastIndex) {
          pattern.lastIndex++;
        }
        if (match[0] !== null && match.index !== ((_a2 = dateObj[startKey]) === null || _a2 === void 0 ? void 0 : _a2.atIndex)) {
          countMatches++;
          if (countMatches >= 3) break;
          if (this.rangeDateTime && countMatches === 2) {
            dateObj[endKey] = {
              match: match[0],
              fromFormat: key,
              atIndex: match.index
            };
          } else {
            if (countMatches >= 2) break;
            dateObj[startKey] = {
              match: match[0],
              fromFormat: key,
              atIndex: match.index
            };
          }
        }
      }
    });
    if (!!dateObj[endKey]) {
      const {
        match: match2,
        fromFormat
      } = dateObj[endKey];
      const normalizedDate = parse(match2, fromFormat, /* @__PURE__ */ new Date());
      result[endKey] = normalizedDate;
    }
    if (!!dateObj[startKey]) {
      const {
        match: match2,
        fromFormat
      } = dateObj[startKey];
      const normalizedDate = parse(match2, fromFormat, /* @__PURE__ */ new Date());
      result[startKey] = normalizedDate;
    }
    if (((_a = dateObj[startKey]) === null || _a === void 0 ? void 0 : _a.atIndex) > ((_b = dateObj[endKey]) === null || _b === void 0 ? void 0 : _b.atIndex)) {
      const temp = result[startKey];
      result[startKey] = result[endKey];
      result[endKey] = temp;
    }
    return result;
  }
  get DATE_FORMATS() {
    var _a, _b;
    const formats = {};
    const DATE_FORMATS = ((_a = this.formatService.dateFormat) === null || _a === void 0 ? void 0 : _a.toLowerCase().startsWith("dd/")) || ((_b = this.formatService.dateFormat) === null || _b === void 0 ? void 0 : _b.toLowerCase().startsWith("d/")) ? SUPPORTED_DATE_FORMATS_START_WITH_DAY : SUPPORTED_DATE_FORMATS_DEFAULT;
    DATE_FORMATS.forEach((format2) => {
      let regex = format2;
      [".", "/", "-"].forEach((symbol) => {
        regex = regex.replace(new RegExp(`[${symbol}]`, "g"), `[${symbol}]`);
      });
      Object.keys(FORMAT_BLOCKS).forEach((block) => {
        regex = regex.replace(new RegExp(block, "g"), FORMAT_BLOCKS[block].source);
      });
      formats[format2] = new RegExp(regex, "g");
    });
    return formats;
  }
  get TIME_FORMATS() {
    const formats = {};
    SUPPORTED_TIME_FORMATS.map((format2) => {
      let regex = format2;
      [":"].forEach((symbol) => {
        regex = regex.replace(new RegExp(`[${symbol}]`, "g"), `[${symbol}]`);
      });
      Object.keys(FORMAT_BLOCKS).forEach((block) => {
        regex = regex.replace(new RegExp(block, "g"), FORMAT_BLOCKS[block].source);
      });
      formats[format2] = new RegExp(regex, "g");
    });
    return formats;
  }
};
var dialogCss = '/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */@font-face{font-family:"Bosch-Sans";font-weight:400;src:url("..//fonts/BoschSans-Regular.eot");src:url("..//fonts/BoschSans-Regular.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Regular.woff2") format("woff2"), url("..//fonts/BoschSans-Regular.woff") format("woff"), url("..//fonts/BoschSans-Regular.ttf") format("truetype"), url("..//fonts/BoschSans-RegularItalic.woff2") format("woff2"), url("..//fonts/BoschSans-RegularItalic.woff") format("woff"), url("..//fonts/BoschSans-RegularItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Regular.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:300;src:url("..//fonts/BoschSans-Light.eot");src:url("..//fonts/BoschSans-Light.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Light.woff2") format("woff2"), url("..//fonts/BoschSans-Light.woff") format("woff"), url("..//fonts/BoschSans-Light.ttf") format("truetype"), url("..//fonts/BoschSans-LightItalic.woff2") format("woff2"), url("..//fonts/BoschSans-LightItalic.woff") format("woff"), url("..//fonts/BoschSans-LightItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Light.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:500;src:url("..//fonts/BoschSans-Medium.eot");src:url("..//fonts/BoschSans-Medium.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Medium.woff2") format("woff2"), url("..//fonts/BoschSans-Medium.woff") format("woff"), url("..//fonts/BoschSans-Medium.ttf") format("truetype"), url("..//fonts/BoschSans-MediumItalic.woff2") format("woff2"), url("..//fonts/BoschSans-MediumItalic.woff") format("woff"), url("..//fonts/BoschSans-MediumItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Medium.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:700;src:url("..//fonts/BoschSans-Bold.eot");src:url("..//fonts/BoschSans-Bold.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Bold.woff2") format("woff2"), url("..//fonts/BoschSans-Bold.woff") format("woff"), url("..//fonts/BoschSans-Bold.ttf") format("truetype"), url("..//fonts/BoschSans-BoldItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BoldItalic.woff") format("woff"), url("..//fonts/BoschSans-BoldItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Bold.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:900;src:url("..//fonts/BoschSans-Black.eot");src:url("..//fonts/BoschSans-Black.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Black.woff2") format("woff2"), url("..//fonts/BoschSans-Black.woff") format("woff"), url("..//fonts/BoschSans-Black.ttf") format("truetype"), url("..//fonts/BoschSans-BlackItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BlackItalic.woff") format("woff"), url("..//fonts/BoschSans-BlackItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Black.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Ic";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url("..//fonts/Bosch-Icon.eot?mh5qa9");src:url("..//fonts/Bosch-Icon.eot?mh5qa9#iefix") format("embedded-opentype"), url("..//fonts/Bosch-Icon.ttf?mh5qa9") format("truetype"), url("..//fonts/Bosch-Icon.woff?mh5qa9") format("woff"), url("..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon") format("svg")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:300;src:url("..//fonts/BoschSansCond-Regular.otf"), url("..//fonts/BoschSansCondensed-Regular.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-RegularItalic.ttf") format("truetype")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:700;src:url("..//fonts/BoschSansCond-Bold.otf"), url("..//fonts/BoschSansCondensed-Bold.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-BoldItalic.ttf") format("truetype")}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.clickable-element{transition:0.4s;cursor:pointer}.clickable-element:active,.clickable-element.active{color:#007bc0}.clickable-element:active:hover,.clickable-element:hover .clickable-element:focus{color:#9dc9ff}.clickable-element:disabled,.clickable-element.disabled{color:#a4abb3}.clickable-element:disabled:active,.clickable-element:disabled.active,.clickable-element:disabled:active:hover,.clickable-element:disabled:hover,.clickable-element.disabled:active,.clickable-element.disabled.active,.clickable-element.disabled:active:hover,.clickable-element.disabled:hover{color:#a4abb3}button.button-primary,button.button-default,input.button-primary,input.button-default{color:#ffffff;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;background-color:#007bc0;background-size:100% 200%;border-width:0;border-color:transparent;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-primary:hover,button.button-primary:focus-visible,button.button-default:hover,button.button-default:focus-visible,input.button-primary:hover,input.button-primary:focus-visible,input.button-default:hover,input.button-default:focus-visible{background-color:#00629a;color:#ffffff;outline:0}button.button-primary:active,button.button-default:active,input.button-primary:active,input.button-default:active{background-color:#004975;color:#ffffff;outline:0}button.button-primary:disabled,button.button-primary.disabled,button.button-default:disabled,button.button-default.disabled,input.button-primary:disabled,input.button-primary.disabled,input.button-default:disabled,input.button-default.disabled{background-color:#c1c7cc;color:#656a6f;background-image:none;cursor:default}button.button-primary:not(.mat-mdc-icon-button):has(mat-icon),button.button-default:not(.mat-mdc-icon-button):has(mat-icon),input.button-primary:not(.mat-mdc-icon-button):has(mat-icon),input.button-default:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-secondary,input.button-secondary{color:#007bc0;background-color:transparent;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 15px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;border-width:1px;border-color:#007bc0;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-secondary:hover,button.button-secondary:focus-visible,input.button-secondary:hover,input.button-secondary:focus-visible{border-color:#004975;outline:0;color:#004975;background-color:#d1e4ff;border-width:1px;border-color:#004975;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-secondary:active,input.button-secondary:active{border-color:#004975;color:#004975;outline:0;background-color:#9dc9ff;border-width:1px;border-color:#004975;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-secondary:disabled,button.button-secondary.disabled,input.button-secondary:disabled,input.button-secondary.disabled{color:#979ea4;border-color:#979ea4;background-color:transparent;cursor:default}button.button-secondary:not(.mat-mdc-icon-button):has(mat-icon),input.button-secondary:not(.mat-mdc-icon-button):has(mat-icon){padding:0 15px 0 13px}button.button-tertiary,input.button-tertiary{color:#007bc0;background-color:transparent;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;border-style:none}button.button-tertiary:hover,button.button-tertiary:focus-visible,input.button-tertiary:hover,input.button-tertiary:focus-visible{outline:0;color:#00629a;background-color:#d1e4ff}button.button-tertiary:active,input.button-tertiary:active{color:#004975;background-color:#9dc9ff;outline:0}button.button-tertiary:disabled,button.button-tertiary.disabled,input.button-tertiary:disabled,input.button-tertiary.disabled{color:#979ea4;background-color:transparent;cursor:default}button.button-tertiary:not(.mat-mdc-icon-button):has(mat-icon),input.button-tertiary:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-integrated,input.button-integrated{color:#000000;background-color:transparent;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;border-style:none}button.button-integrated:hover,button.button-integrated:focus-visible,input.button-integrated:hover,input.button-integrated:focus-visible{color:#007bc0;outline:0}button.button-integrated:active,input.button-integrated:active{color:#00629a;outline:0}button.button-integrated:disabled,button.button-integrated.disabled,input.button-integrated:disabled,input.button-integrated.disabled{color:#979ea4 !important;background-color:transparent;cursor:default}button.button-integrated:not(.mat-mdc-icon-button):has(mat-icon),input.button-integrated:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-link,input.button-link{color:#007bc0;background-color:transparent;border:none;-webkit-box-shadow:none;box-shadow:none;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0 16px 0 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto}button.button-link::after,input.button-link::after{position:absolute;font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:18px;content:"\\e181";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}button.button-link:hover,button.button-link:focus-visible,input.button-link:hover,input.button-link:focus-visible{color:#00629a;text-decoration:underline;outline:0}button.button-link:active,input.button-link:active{color:#004975;text-decoration:underline}button.button-link.disabled,button.button-link[disabled],button.button-link fieldset[disabled],input.button-link.disabled,input.button-link[disabled],input.button-link fieldset[disabled]{color:#c1c7cc;text-decoration:none;-webkit-transition:none;-o-transition:none;transition:none}button.button-link.disabled::after,button.button-link[disabled]::after,button.button-link fieldset[disabled]::after,input.button-link.disabled::after,input.button-link[disabled]::after,input.button-link fieldset[disabled]::after{-webkit-transform:translate(0, 0);-ms-transform:translate(0, 0);-o-transform:translate(0, 0);transform:translate(0, 0)}button.button-link.no-arrow::after,input.button-link.no-arrow::after{content:""}button.button-link:not(.mat-mdc-icon-button):has(mat-icon),input.button-link:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-block,input[type=submit].button-block,input[type=reset].button-block,input[type=button].button-block{display:block;width:100%}/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.bci-core-date-time-picker-container{background:#ffffff;display:flex;flex-flow:column nowrap;outline:none;box-shadow:0px 0px 8px rgba(0, 0, 0, 0.25)}.bci-core-date-time-picker-container .bci-core-date-time-picker-sub-container{display:flex}.bci-core-date-time-picker-container .bci-core-date-time-picker-sub-container.hidden{display:none}.bci-core-date-time-picker-container .bci-core-date-time-picker-sub-container.year-month{height:292px;min-width:292px}.bci-core-date-time-picker-container .bci-core-date-time-picker-sub-container.year-month.has-time{width:584px}.bci-core-date-time-picker-container .bci-core-save{color:#005587;text-align:end;border-top:1px solid #8a9097;font-size:16px;padding:5px 10px 5px 0px;height:29px}.bci-core-date-time-picker-container .bci-core-save .bosch-ic-clickable{position:relative;top:4px}.bci-core-date-time-picker-container .bci-core-datepicker-selection{text-align:right;border-top:1px solid #8a9097;display:flex;justify-content:space-between;align-items:center;padding:0 10px;height:68px}.bci-core-date-time-picker-container .bci-core-datepicker-selection .close{margin-left:auto}@media (max-width: 767px){.bci-core-date-time-picker-sub-container{flex-flow:column nowrap;width:292px !important}}@media (min-width: 768px){.bci-core-date-time-picker-sub-container{flex-flow:row nowrap}.bci-core-date-time-picker-sub-container>bci-datepicker{order:1;flex:1}.bci-core-date-time-picker-sub-container .bci-core-time-picker-container{order:2;flex:1}.bci-core-date-time-picker-sub-container .bci-core-time-picker-container:not(.no-border){border-left:1px solid #8a9097}.bci-core-date-time-picker-sub-container .bci-core-time-picker-container bci-timepicker .bci-core-timepicker-container{padding-right:8px}.bci-core-date-time-picker-sub-container>.bci-core-save{order:3;flex:2}}';
var DateTimeDialog = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.selectionChanged = createEvent(this, "selectionChanged", 7);
    this.dateTimePickerId = void 0;
    this.datePickerInputObj = void 0;
    this.templatesList = void 0;
    this.templatesDisabled = void 0;
    this.hide = void 0;
    this.datePickerConfig = void 0;
    this.timePickerConfig = void 0;
    this.showTemplates = false;
    this.yearMonthSelectionStep = null;
    this.yearSelectionPageOffset = 0;
    this.dateObj = void 0;
    this.datesObj = {
      startDate: null,
      endDate: null
    };
    this.formatService = formattingServiceFactory();
    this.dateService = dateServiceFactory({
      formatService: this.formatService
    });
    this.toggleDialogHandler = void 0;
  }
  emitSelectionChanged(event) {
    this.selectionChanged.emit(event);
  }
  onTemplateUpdate({
    detail: {
      firstEmit,
      timeStamp,
      id
    }
  }) {
    const event = {
      reason: "frame",
      timestampStart: timeStamp.timestampStart,
      timestampEnd: timeStamp.timestampEnd,
      firstEmit,
      id
    };
    this.selectionChanged.emit(event);
  }
  timeSelectedHandler({
    detail: data
  }) {
    var _a;
    (_a = this.templatesElement) === null || _a === void 0 ? void 0 : _a.reset();
    let {
      timeStart,
      timeEnd
    } = this.datePickerInputObj;
    if (data.timepickerId === "timeStart_" + this.dateTimePickerId) {
      timeStart = data.updatedTime;
    }
    if (data.timepickerId === "timeEnd_" + this.dateTimePickerId) {
      timeEnd = data.updatedTime;
    }
    const event = {
      reason: "time",
      timeStart,
      timeEnd
    };
    this.selectionChanged.emit(event);
  }
  showTimeRange() {
    this.showTemplates = false;
  }
  /**
   * Clears internal timer and resets selected template
   */
  close() {
    return __async(this, null, function* () {
      var _a;
      (_a = this.templatesElement) === null || _a === void 0 ? void 0 : _a.reset();
    });
  }
  /**
   * Closes the Year/Month Selection
   */
  closeYearMonthSelection() {
    return __async(this, null, function* () {
      this.yearMonthSelectionStep = null;
    });
  }
  showTemplate() {
    this.showTemplates = true;
  }
  toggleShowYearMonthSelectionHandler(event) {
    this.yearMonthSelectionStep = event.detail.newState ? "year" : null;
    this.yearSelectionPageOffset = 0;
  }
  arrowClickedHandler(event) {
    if (!!this.yearMonthSelectionStep && this.yearMonthSelectionStep === "year") {
      switch (event.detail.direction) {
        case "next":
          this.yearSelectionPageOffset++;
          break;
        case "prev":
          this.yearSelectionPageOffset--;
          break;
      }
      return;
    }
    this.handleMonthChangeOnArrowClick(event.detail);
  }
  handleMonthChangeOnArrowClick(detail) {
    let newYear = this.dateObj.calendarDateYear;
    let newMonth;
    switch (detail.direction) {
      case "next":
        newMonth = (this.dateObj.calendarDateMonth + 1) % 12;
        if (newMonth === 0) {
          newYear = this.dateObj.calendarDateYear + 1;
        }
        this.dateObj = Object.assign(Object.assign({}, this.dateObj), {
          calendarDateMonth: newMonth,
          calendarDateYear: newYear
        });
        break;
      case "prev":
        newMonth = this.dateObj.calendarDateMonth - 1;
        if (newMonth === -1) {
          newMonth = 11;
          newYear = this.dateObj.calendarDateYear - 1;
        }
        this.dateObj = Object.assign(Object.assign({}, this.dateObj), {
          calendarDateMonth: newMonth,
          calendarDateYear: newYear
        });
        break;
    }
  }
  yearMonthSelectedHandler(event) {
    if (!this.yearMonthSelectionStep) return;
    if (this.yearMonthSelectionStep === "year") {
      const newYear = event.detail.selectedYearMonth;
      this.dateObj = Object.assign(Object.assign({}, this.dateObj), {
        calendarDateYear: newYear
      });
      this.yearMonthSelectionStep = "month";
    } else if (this.yearMonthSelectionStep === "month") {
      const newMonth = event.detail.selectedYearMonth;
      this.dateObj = Object.assign(Object.assign({}, this.dateObj), {
        calendarDateMonth: newMonth
      });
      this.yearMonthSelectionStep = null;
    }
  }
  singleDateSelectedHandler(event) {
    var _a;
    (_a = this.templatesElement) === null || _a === void 0 ? void 0 : _a.reset();
    const selectedDay = event.detail.selectedDay;
    const newDate = createDate(/* @__PURE__ */ new Date(), this.dateObj.calendarDateYear, this.dateObj.calendarDateMonth, selectedDay);
    this.dateObj = Object.assign(Object.assign({}, this.dateObj), {
      calendarDate: format(newDate, this.datePickerConfig.formatDate),
      selectedDate: format(newDate, this.datePickerConfig.formatDate)
    });
    const dateSelectedEvent = {
      reason: "date",
      startDate: this.dateObj.selectedDate,
      endDate: null,
      rangeCompleted: false
    };
    this.emitSelectionChanged(dateSelectedEvent);
  }
  rangeDateSelectedHandler(event) {
    var _a;
    (_a = this.templatesElement) === null || _a === void 0 ? void 0 : _a.reset();
    const selectedDay = event.detail.selectedDay;
    const newDate = createDate(/* @__PURE__ */ new Date(), this.dateObj.calendarDateYear, this.dateObj.calendarDateMonth, selectedDay);
    let rangeComplete = false;
    if (this.datesObj.startDate && this.datesObj.endDate && this.datesObj.startDate !== this.datesObj.endDate) {
      this.datesObj = Object.assign(Object.assign({}, this.datesObj), {
        startDate: format(newDate, this.datePickerConfig.formatDate),
        endDate: format(newDate, this.datePickerConfig.formatDate)
      });
      rangeComplete = false;
    } else if (!this.datesObj.startDate) {
      this.datesObj = Object.assign(Object.assign({}, this.datesObj), {
        startDate: format(newDate, this.datePickerConfig.formatDate)
      });
      rangeComplete = false;
    } else if (isBefore(newDate, parse(this.datesObj.startDate, this.datePickerConfig.formatDate, /* @__PURE__ */ new Date()))) {
      this.datesObj = Object.assign(Object.assign({}, this.datesObj), {
        endDate: this.datesObj.startDate,
        startDate: format(newDate, this.datePickerConfig.formatDate)
      });
      rangeComplete = true;
    } else {
      this.datesObj = Object.assign(Object.assign({}, this.datesObj), {
        endDate: format(newDate, this.datePickerConfig.formatDate)
      });
      rangeComplete = true;
    }
    const dateSelectedEvent = Object.assign(Object.assign({}, this.datesObj), {
      reason: "date",
      rangeCompleted: rangeComplete
    });
    this.emitSelectionChanged(dateSelectedEvent);
  }
  userInputDate(newValue) {
    if (!newValue) return;
    if (newValue.dateStart) {
      this.dateObj = Object.assign(Object.assign({}, this.dateObj), {
        calendarDateMonth: getMonth(parse(newValue.dateStart, this.datePickerConfig.formatDate, /* @__PURE__ */ new Date())),
        calendarDateYear: getYear(parse(newValue.dateStart, this.datePickerConfig.formatDate, /* @__PURE__ */ new Date()))
      });
      this.datesObj = Object.assign(Object.assign({}, this.datesObj), {
        startDate: newValue.dateStart,
        endDate: newValue.dateEnd
      });
    } else {
      this.dateObj = Object.assign(Object.assign({}, this.dateObj), {
        calendarDateYear: getYear(utcToZonedTime(/* @__PURE__ */ new Date(), this.datePickerConfig.timeZone)),
        calendarDateMonth: getMonth(utcToZonedTime(/* @__PURE__ */ new Date(), this.datePickerConfig.timeZone)),
        selectedDate: null
      });
      this.datesObj = Object.assign(Object.assign({}, this.datesObj), {
        startDate: null,
        endDate: null
      });
    }
  }
  componentWillLoad() {
    return __async(this, null, function* () {
      this.dateObj = {
        calendarDate: format(utcToZonedTime(/* @__PURE__ */ new Date(), this.datePickerConfig.timeZone), this.datePickerConfig.formatDate),
        calendarDateYear: getYear(utcToZonedTime(/* @__PURE__ */ new Date(), this.datePickerConfig.timeZone)),
        calendarDateMonth: getMonth(utcToZonedTime(/* @__PURE__ */ new Date(), this.datePickerConfig.timeZone)),
        selectedDate: null
      };
    });
  }
  applyTemplate(option, firstEmit) {
    this.onTemplateUpdate({
      detail: {
        timeStamp: option.calculateTemplate(),
        firstEmit,
        id: option.id
      }
    });
  }
  render() {
    var _a, _b, _c, _d;
    return h(Host, {
      id: `overlay_${this.dateTimePickerId}`
    }, h("div", {
      class: "bci-core-date-time-picker-container",
      tabindex: "-1"
    }, !this.showTemplates && this.hide !== "calendar" && h("div", null, h("bci-month-header", {
      "data-test": "webcore.webcomponents.bci-datepicker.year-month",
      year: this.dateObj.calendarDateYear,
      month: this.dateObj.calendarDateMonth,
      yearMonthSelectionStep: this.yearMonthSelectionStep
    }), h("div", {
      class: `bci-core-date-time-picker-sub-container year-month ${!this.yearMonthSelectionStep ? "hidden" : ""} ${this.hide !== "time" ? "has-time" : ""}`
    }, h("bci-year-month", {
      selectionStep: this.yearMonthSelectionStep,
      minDate: this.datePickerConfig.minDate,
      maxDate: this.datePickerConfig.maxDate,
      dateObj: this.dateObj,
      yearSelectionPageOffset: this.yearSelectionPageOffset
    }))), !this.showTemplates && h("div", {
      class: `bci-core-date-time-picker-sub-container ${!!this.yearMonthSelectionStep ? "hidden" : ""} `
    }, this.hide !== "calendar" && h("bci-datepicker", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.dateTimePickerInput",
      bciDatepickerId: this.dateTimePickerId,
      minDate: this.datePickerConfig.minDate,
      maxDate: this.datePickerConfig.maxDate,
      rangeDate: this.datePickerConfig.rangeDateTime,
      sundayFirst: this.datePickerConfig.sundayFirst,
      formatDate: this.datePickerConfig.formatDate,
      datePickerInputObj: this.datePickerInputObj,
      timeZone: this.datePickerConfig.timeZone,
      selectedYear: this.dateObj.calendarDateYear,
      selectedMonth: this.dateObj.calendarDateMonth,
      selectedDate: this.dateObj.selectedDate,
      today: this.formatService.formatDate(this.dateService.getTodayValue(this.datePickerConfig.timeZone)),
      datesObj: this.datesObj
    }), this.hide !== "time" && h("div", {
      class: {
        "bci-core-time-picker-container": true,
        "no-border": this.hide === "calendar"
      }
    }, h("bci-timepicker", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.starttime",
      class: {
        "full-height": !this.timePickerConfig.rangeDateTime
      },
      timeInput: (_a = this.datePickerInputObj) === null || _a === void 0 ? void 0 : _a.timeStart,
      timeFormat: this.timePickerConfig.timeFormat,
      bciTimepickerId: "timeStart_" + this.dateTimePickerId,
      rangeTimePicker: false,
      timeTitle: this.timePickerConfig.rangeDateTime ? translate("startTime") : translate("time")
    }), h("bci-timepicker", {
      "data-test": "datetime-picker.core-datetime-picker.endtime",
      class: {
        hidden: !this.timePickerConfig.rangeDateTime
      },
      timeInput: (_b = this.datePickerInputObj) === null || _b === void 0 ? void 0 : _b.timeEnd,
      timeFormat: this.timePickerConfig.timeFormat,
      bciTimepickerId: "timeEnd_" + this.dateTimePickerId,
      rangeTimePicker: true,
      timeTitle: translate("endTime")
    }))), !this.showTemplates && h("div", {
      class: "bci-core-datepicker-selection"
    }, ((_c = this.templatesList) === null || _c === void 0 ? void 0 : _c.length) > 1 && this.hide !== "calendar" && h("button", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.template",
      class: "button-secondary no-arrow template",
      disabled: this.templatesDisabled,
      onClick: () => this.showTemplate()
    }, translate("templates")), ((_d = this.templatesList) === null || _d === void 0 ? void 0 : _d.length) === 1 && this.hide !== "calendar" && h("button", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.template",
      class: "button-secondary no-arrow template",
      disabled: this.templatesDisabled,
      onClick: () => this.applyTemplate(this.templatesList[0], true)
    }, this.templatesList[0].label), h("button", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.close",
      class: "button-primary no-arrow close",
      onClick: () => this.toggleDialogHandler()
    }, translate("close"))), h("bci-template", {
      templatesList: this.templatesList,
      visible: this.showTemplates && !!this.templatesList,
      ref: (el) => this.templatesElement = el
    })));
  }
  static get watchers() {
    return {
      "datePickerInputObj": ["userInputDate"]
    };
  }
};
DateTimeDialog.style = dialogCss;
var timepickerCss = '/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */@font-face{font-family:var(--bci-global-font-family);font-weight:400;src:url("..//fonts/BoschSans-Regular.eot");src:url("..//fonts/BoschSans-Regular.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Regular.woff2") format("woff2"), url("..//fonts/BoschSans-Regular.woff") format("woff"), url("..//fonts/BoschSans-Regular.ttf") format("truetype"), url("..//fonts/BoschSans-RegularItalic.woff2") format("woff2"), url("..//fonts/BoschSans-RegularItalic.woff") format("woff"), url("..//fonts/BoschSans-RegularItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Regular.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:300;src:url("..//fonts/BoschSans-Light.eot");src:url("..//fonts/BoschSans-Light.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Light.woff2") format("woff2"), url("..//fonts/BoschSans-Light.woff") format("woff"), url("..//fonts/BoschSans-Light.ttf") format("truetype"), url("..//fonts/BoschSans-LightItalic.woff2") format("woff2"), url("..//fonts/BoschSans-LightItalic.woff") format("woff"), url("..//fonts/BoschSans-LightItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Light.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:500;src:url("..//fonts/BoschSans-Medium.eot");src:url("..//fonts/BoschSans-Medium.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Medium.woff2") format("woff2"), url("..//fonts/BoschSans-Medium.woff") format("woff"), url("..//fonts/BoschSans-Medium.ttf") format("truetype"), url("..//fonts/BoschSans-MediumItalic.woff2") format("woff2"), url("..//fonts/BoschSans-MediumItalic.woff") format("woff"), url("..//fonts/BoschSans-MediumItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Medium.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:700;src:url("..//fonts/BoschSans-Bold.eot");src:url("..//fonts/BoschSans-Bold.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Bold.woff2") format("woff2"), url("..//fonts/BoschSans-Bold.woff") format("woff"), url("..//fonts/BoschSans-Bold.ttf") format("truetype"), url("..//fonts/BoschSans-BoldItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BoldItalic.woff") format("woff"), url("..//fonts/BoschSans-BoldItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Bold.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:900;src:url("..//fonts/BoschSans-Black.eot");src:url("..//fonts/BoschSans-Black.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Black.woff2") format("woff2"), url("..//fonts/BoschSans-Black.woff") format("woff"), url("..//fonts/BoschSans-Black.ttf") format("truetype"), url("..//fonts/BoschSans-BlackItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BlackItalic.woff") format("woff"), url("..//fonts/BoschSans-BlackItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Black.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Ic";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url("..//fonts/Bosch-Icon.eot?mh5qa9");src:url("..//fonts/Bosch-Icon.eot?mh5qa9#iefix") format("embedded-opentype"), url("..//fonts/Bosch-Icon.ttf?mh5qa9") format("truetype"), url("..//fonts/Bosch-Icon.woff?mh5qa9") format("woff"), url("..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon") format("svg")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:300;src:url("..//fonts/BoschSansCond-Regular.otf"), url("..//fonts/BoschSansCondensed-Regular.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-RegularItalic.ttf") format("truetype")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:700;src:url("..//fonts/BoschSansCond-Bold.otf"), url("..//fonts/BoschSansCondensed-Bold.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-BoldItalic.ttf") format("truetype")}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */body{margin:0;display:flex;flex-direction:column}main,*{font-family:var(--bci-global-font-family)}::selection,::-moz-selection{background-color:#007bc0}a::-moz-selection{color:#ffffff}.lead{margin-bottom:24px;font-size:18px;font-weight:300;line-height:1.4}@media (min-width: 768px){.lead{font-size:24px}}.bosch-ic,.Bosch-Ic{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic:before,.Bosch-Ic:before{font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium,.Bosch-Ic-Medium{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium:before,.Bosch-Ic-Medium:before{font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-clickable,.Bosch-Ic-Clickable{cursor:pointer}.bosch-ic-clickable:disabled,.bosch-ic-clickable.disabled,.Bosch-Ic-Clickable:disabled,.Bosch-Ic-Clickable.disabled{color:#a4abb3;cursor:not-allowed}.bosch-ic-down:before{content:"\\e147"}.bosch-ic-up:before{content:"\\e296"}bci-timepicker{display:block;display:flex;flex-direction:column;justify-content:stretch}bci-timepicker.full-height{height:100%}bci-timepicker:nth-of-type(2){border-top:1px solid #8a9097}.bci-core-timepicker-title{flex-wrap:nowrap;font-size:12px;line-height:1.5;padding-top:16px;padding-left:10px;font-weight:700}.bci-core-timepicker-container{display:flex;justify-content:center;padding:8px;margin:auto 0}.bci-core-timepicker-container .bosch-ic-clickable{color:#000000}.bci-core-timepicker-container .bosch-ic-clickable:hover{color:#007bc0}.bci-core-timepicker-container .bosch-ic-clickable:active{color:#00629a}.bci-core-input-container{display:flex;flex-direction:column;align-items:center}.bci-core-input-container.divider{align-self:center;padding:0 10px 7px 10px;font-size:16px;font-weight:400}.bci-core-input-container .arrow-button{color:#000000}.bci-core-input-container input::-webkit-outer-spin-button,.bci-core-input-container input::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.bci-core-input-container input{height:48px;width:48px;padding:0px 1px 0px 1px;text-align:center;font-size:16px;line-height:24px;font-weight:400;color:#000000;background-color:#e0e2e5;border:0;border-bottom:1px solid black;outline:none;appearance:textfield}.bci-core-input-container input:hover{background-color:#c1c7cc}.bci-core-input-container input:active{background-color:#a4abb3}.bci-core-input-container input:focus,.bci-core-input-container input:focus-within{background-color:#d1e4ff}.bci-core-input-container.no-seconds input{width:73px}.bci-core-input-container.no-secondsandmerdiam input{width:123px}.hidden{display:none}';
var TimeDirection;
(function(TimeDirection2) {
  TimeDirection2[TimeDirection2["UP"] = 0] = "UP";
  TimeDirection2[TimeDirection2["DOWN"] = 1] = "DOWN";
})(TimeDirection || (TimeDirection = {}));
var MeridiemValue;
(function(MeridiemValue2) {
  MeridiemValue2["AM"] = "AM";
  MeridiemValue2["PM"] = "PM";
})(MeridiemValue || (MeridiemValue = {}));
var Timepicker = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.updatedTime = createEvent(this, "updatedTime", 7);
    this.timeInput = void 0;
    this.timeFormat = void 0;
    this.bciTimepickerId = void 0;
    this.rangeTimePicker = void 0;
    this.timeTitle = void 0;
    this.hideSeconds = void 0;
    this.showMeridiem = true;
    this.hour = void 0;
    this.minutes = void 0;
    this.seconds = void 0;
    this.meridiem = MeridiemValue.AM;
  }
  handleTimeInput(newValue) {
    this.hour = !!newValue ? format(parse(newValue, this.timeFormat, /* @__PURE__ */ new Date()), timeFormatHasMeridiem(this.timeFormat) ? "hh" : "HH") : timeFormatHasMeridiem(this.timeFormat) ? null : null;
    this.hour = this.formatTime(this.hour);
    this.minutes = !!newValue ? format(parse(newValue, this.timeFormat, /* @__PURE__ */ new Date()), "mm") : null;
    this.minutes = this.formatTime(this.minutes);
    this.seconds = !!newValue ? format(parse(newValue, this.timeFormat, /* @__PURE__ */ new Date()), "ss") : null;
    this.seconds = this.formatTime(this.seconds);
    this.showMeridiem = !timeFormatHasMeridiem(this.timeFormat);
    this.hideSeconds = !isHideSeconds(this.timeFormat);
    this.meridiem = (newValue === null || newValue === void 0 ? void 0 : newValue.includes(MeridiemValue.PM)) ? MeridiemValue.PM : MeridiemValue.AM;
  }
  componentDidLoad() {
    if (isNaN(Date.parse(this.timeInput))) {
      this.handleTimeInput(this.timeInput);
    }
  }
  getUpdatedTime() {
    if (this.hour || this.minutes || this.seconds) {
      const newTime = `${!!this.hour ? this.hour : timeFormatHasMeridiem(this.timeFormat) ? "01" : "00"}:${this.minutes || "00"}:${this.seconds || "00"}${timeFormatHasMeridiem(this.timeFormat) ? " " + this.meridiem : ""}`;
      return format(parse(newTime, timeFormatHasMeridiem(this.timeFormat) ? "hh:mm:ss a" : "HH:mm:ss", /* @__PURE__ */ new Date()), this.timeFormat);
    } else {
      return null;
    }
  }
  emitUpdatedTime(isDelete) {
    this.updatedTime.emit({
      updatedTime: isDelete ? null : this.getUpdatedTime(),
      timepickerId: this.bciTimepickerId
    });
  }
  updateTimeOnType(ev, hmsmr, maxValue, minValue = 0) {
    let newTime;
    newTime = ev.target.value;
    if (!newTime || isNaN(newTime) || newTime === "") {
      newTime = null;
    } else {
      newTime = parseInt(newTime, 10);
      newTime = Math.min(newTime, maxValue);
      newTime = Math.max(newTime, minValue);
      newTime = this.formatTime(newTime);
    }
    switch (hmsmr) {
      case "h":
        this.hour = !!newTime ? String(newTime) : newTime;
        this.hourInput.value = `${newTime}`;
        break;
      case "m":
        this.minutes = !!newTime ? String(newTime) : newTime;
        this.minutesInput.value = `${newTime}`;
        break;
      case "s":
        this.seconds = !!newTime ? String(newTime) : newTime;
        this.secondsInput.value = `${newTime}`;
        break;
    }
    if (!newTime && typeof newTime !== "number") {
      this.emitUpdatedTime(true);
    } else {
      this.emitUpdatedTime();
    }
  }
  handleInputType(ev, hmsmr, maxValue, minValue = 0) {
    let newTime = String(ev.target.value);
    const isInputEvent = ev instanceof InputEvent;
    const newTimeAsInt = parseInt(newTime, 10);
    if (isInputEvent) {
      if (newTime.length === 2 && newTimeAsInt >= minValue && newTimeAsInt <= maxValue) {
        this.selectNextInput(hmsmr);
      }
    } else {
      ev.target.select();
    }
    newTime = newTime.replace("-", "");
    if (newTime.length > 2) {
      newTime = newTime.substring(0, 2);
    }
    switch (hmsmr) {
      case "h":
        this.hourInput.value = `${newTime}`;
        break;
      case "m":
        this.minutesInput.value = `${newTime}`;
        break;
      case "s":
        this.secondsInput.value = `${newTime}`;
        break;
    }
  }
  handleKeyPressed(ev, hmsmr, maxValue, minValue = 0) {
    if (ev.key === "Enter") {
      this.updateTimeOnType(ev, hmsmr, maxValue, minValue);
      this.selectNextInput(hmsmr);
    }
  }
  selectNextInput(hmsmr) {
    switch (hmsmr) {
      case "h":
        this.minutesInput.select();
        break;
      case "m":
        this.secondsInput.select();
        break;
      case "s":
        if (timeFormatHasMeridiem(this.timeFormat)) this.meridiemInput.focus();
        break;
    }
  }
  formatTime(value) {
    if (!value && typeof value !== "number") return null;
    return String(value).padStart(2, "0");
  }
  updateTimeOnClick(direction, value, maxValue, minValue = 0) {
    if (!value) {
      value = direction === TimeDirection.UP ? minValue : maxValue;
      return this.formatTime(value);
    }
    value = parseInt(value, 10);
    if (direction === TimeDirection.UP) {
      value = value === maxValue ? minValue : value + 1;
    }
    if (direction === TimeDirection.DOWN) {
      value = value === minValue ? maxValue : value - 1;
    }
    return this.formatTime(value);
  }
  updateMeridiemOnClick() {
    this.meridiem = this.meridiem === MeridiemValue.AM ? MeridiemValue.PM : MeridiemValue.AM;
  }
  render() {
    return h(Host, null, h("div", {
      class: "bci-core-timepicker-title"
    }, this.timeTitle), h("div", {
      class: "bci-core-timepicker-container"
    }, h("div", {
      class: {
        "bci-core-input-container": true,
        rangeTimePicker: this.rangeTimePicker,
        "no-seconds": this.hideSeconds && !this.showMeridiem || !this.hideSeconds && this.timeFormat === "HH:mm:ss",
        "no-secondsandmerdiam": this.hideSeconds && this.showMeridiem
      }
    }, h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-up",
      onClick: () => {
        this.hour = this.updateTimeOnClick(TimeDirection.UP, this.hour, timeFormatHasMeridiem(this.timeFormat) ? 12 : 23, timeFormatHasMeridiem(this.timeFormat) ? 1 : 0);
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.hour-up.span"
    })), h("input", {
      "data-test": "webcore.webcomponents.timepicker.update-time-on-type",
      type: "number",
      placeholder: "HH",
      step: 1,
      min: timeFormatHasMeridiem(this.timeFormat) ? 1 : 0,
      max: timeFormatHasMeridiem(this.timeFormat) ? 12 : 23,
      pattern: "[012][0-9]*",
      "data-hours": "true",
      onFocus: () => this.hourInput.select(),
      onInput: (ev) => this.handleInputType(ev, "h", timeFormatHasMeridiem(this.timeFormat) ? 12 : 23, timeFormatHasMeridiem(this.timeFormat) ? 1 : 0),
      onKeyPress: (ev) => this.handleKeyPressed(ev, "h", timeFormatHasMeridiem(this.timeFormat) ? 12 : 23, timeFormatHasMeridiem(this.timeFormat) ? 1 : 0),
      onBlur: (ev) => this.updateTimeOnType(ev, "h", timeFormatHasMeridiem(this.timeFormat) ? 12 : 23, timeFormatHasMeridiem(this.timeFormat) ? 1 : 0),
      value: this.hour,
      ref: (el) => this.hourInput = el
    }), h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-down",
      onClick: () => {
        this.hour = this.updateTimeOnClick(TimeDirection.DOWN, this.hour, timeFormatHasMeridiem(this.timeFormat) ? 12 : 23, timeFormatHasMeridiem(this.timeFormat) ? 1 : 0);
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.hour-down.span"
    }))), h("div", {
      class: "bci-core-input-container divider"
    }, h("span", null, ":")), h("div", {
      class: {
        "bci-core-input-container": true,
        rangeTimePicker: this.rangeTimePicker,
        "no-seconds": this.hideSeconds && !this.showMeridiem || !this.hideSeconds && this.timeFormat === "HH:mm:ss",
        "no-secondsandmerdiam": this.hideSeconds && this.showMeridiem
      }
    }, h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-up",
      onClick: () => {
        this.minutes = this.updateTimeOnClick(TimeDirection.UP, this.minutes, 59);
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.minute-up.span"
    })), h("input", {
      "data-test": "webcore.webcomponents.timepicker.update-time-on-type-minute",
      min: 0,
      max: 59,
      placeholder: "MM",
      type: "number",
      step: 1,
      pattern: "[0-5][0-9]*",
      "data-minutes": "true",
      onFocus: () => this.minutesInput.select(),
      onInput: (ev) => this.handleInputType(ev, "m", 59),
      onKeyPress: (ev) => this.handleKeyPressed(ev, "m", 59),
      onBlur: (ev) => this.updateTimeOnType(ev, "m", 59),
      value: this.minutes,
      ref: (el) => this.minutesInput = el
    }), h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-down",
      onClick: () => {
        this.minutes = this.updateTimeOnClick(TimeDirection.DOWN, this.minutes, 59);
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.minute-down.span"
    }))), h("div", {
      class: {
        "bci-core-input-container": true,
        divider: true,
        hidden: this.hideSeconds
      }
    }, h("span", null, ":")), h("div", {
      class: {
        "bci-core-input-container": true,
        hidden: this.hideSeconds,
        rangeTimePicker: this.rangeTimePicker,
        "no-seconds": this.hideSeconds && !this.showMeridiem || !this.hideSeconds && this.timeFormat === "HH:mm:ss"
      }
    }, h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-up",
      onClick: () => {
        this.seconds = this.updateTimeOnClick(TimeDirection.UP, this.seconds, 59);
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.second-up.span"
    })), h("input", {
      "data-test": "webcore.webcomponents.timepicker.bci-core-timepicker-seconds-input",
      type: "number",
      placeholder: "SS",
      step: 1,
      min: 0,
      max: 59,
      pattern: "[0-5][0-9]*",
      "data-seconds": "true",
      onFocus: () => this.secondsInput.select(),
      onInput: (ev) => this.handleInputType(ev, "s", 59),
      onKeyPress: (ev) => this.handleKeyPressed(ev, "s", 59),
      onBlur: (ev) => this.updateTimeOnType(ev, "s", 59),
      value: this.seconds,
      ref: (el) => this.secondsInput = el
    }), h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-down",
      onClick: () => {
        this.seconds = this.updateTimeOnClick(TimeDirection.DOWN, this.seconds, 59);
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.second-down.span"
    }))), h("div", {
      class: {
        "bci-core-input-container": true,
        divider: true,
        hidden: this.showMeridiem
      }
    }), h("div", {
      class: {
        "bci-core-input-container": true,
        "no-seconds": this.hideSeconds && !this.showMeridiem,
        hidden: this.showMeridiem,
        rangeTimePicker: this.rangeTimePicker
      }
    }, h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-up",
      onClick: () => {
        this.updateMeridiemOnClick();
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.meridiem-up.span"
    })), h("input", {
      "data-test": "webcore.webcomponents.timepicker.bci-core-timepicker-meridiem-input",
      type: "text",
      readonly: "readonly",
      "data-seconds": "true",
      onKeyDown: (ev) => {
        if (["ArrowDown", "ArrowUp"].includes(ev.key)) {
          this.updateMeridiemOnClick();
          this.emitUpdatedTime();
        }
      },
      value: this.meridiem,
      ref: (el) => this.meridiemInput = el
    }), h("span", {
      class: "arrow-button"
    }, h("i", {
      class: "bosch-ic bosch-ic-clickable bosch-ic-down",
      onClick: () => {
        this.updateMeridiemOnClick();
        this.emitUpdatedTime();
      },
      "data-test": "webcore.webcomponents.timepicker.update-time.meridiem-down.span"
    })))));
  }
  get el() {
    return getElement(this);
  }
  static get watchers() {
    return {
      "timeInput": ["handleTimeInput"]
    };
  }
};
Timepicker.style = timepickerCss;
var datetimePickerCss = '/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */@font-face{font-family:var(--bci-global-font-family);font-weight:400;src:url("..//fonts/BoschSans-Regular.eot");src:url("..//fonts/BoschSans-Regular.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Regular.woff2") format("woff2"), url("..//fonts/BoschSans-Regular.woff") format("woff"), url("..//fonts/BoschSans-Regular.ttf") format("truetype"), url("..//fonts/BoschSans-RegularItalic.woff2") format("woff2"), url("..//fonts/BoschSans-RegularItalic.woff") format("woff"), url("..//fonts/BoschSans-RegularItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Regular.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:300;src:url("..//fonts/BoschSans-Light.eot");src:url("..//fonts/BoschSans-Light.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Light.woff2") format("woff2"), url("..//fonts/BoschSans-Light.woff") format("woff"), url("..//fonts/BoschSans-Light.ttf") format("truetype"), url("..//fonts/BoschSans-LightItalic.woff2") format("woff2"), url("..//fonts/BoschSans-LightItalic.woff") format("woff"), url("..//fonts/BoschSans-LightItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Light.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:500;src:url("..//fonts/BoschSans-Medium.eot");src:url("..//fonts/BoschSans-Medium.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Medium.woff2") format("woff2"), url("..//fonts/BoschSans-Medium.woff") format("woff"), url("..//fonts/BoschSans-Medium.ttf") format("truetype"), url("..//fonts/BoschSans-MediumItalic.woff2") format("woff2"), url("..//fonts/BoschSans-MediumItalic.woff") format("woff"), url("..//fonts/BoschSans-MediumItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Medium.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:700;src:url("..//fonts/BoschSans-Bold.eot");src:url("..//fonts/BoschSans-Bold.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Bold.woff2") format("woff2"), url("..//fonts/BoschSans-Bold.woff") format("woff"), url("..//fonts/BoschSans-Bold.ttf") format("truetype"), url("..//fonts/BoschSans-BoldItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BoldItalic.woff") format("woff"), url("..//fonts/BoschSans-BoldItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Bold.svg#svgFontName") format("svg");}@font-face{font-family:var(--bci-global-font-family);font-weight:900;src:url("..//fonts/BoschSans-Black.eot");src:url("..//fonts/BoschSans-Black.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Black.woff2") format("woff2"), url("..//fonts/BoschSans-Black.woff") format("woff"), url("..//fonts/BoschSans-Black.ttf") format("truetype"), url("..//fonts/BoschSans-BlackItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BlackItalic.woff") format("woff"), url("..//fonts/BoschSans-BlackItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Black.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Ic";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url("..//fonts/Bosch-Icon.eot?mh5qa9");src:url("..//fonts/Bosch-Icon.eot?mh5qa9#iefix") format("embedded-opentype"), url("..//fonts/Bosch-Icon.ttf?mh5qa9") format("truetype"), url("..//fonts/Bosch-Icon.woff?mh5qa9") format("woff"), url("..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon") format("svg")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:300;src:url("..//fonts/BoschSansCond-Regular.otf"), url("..//fonts/BoschSansCondensed-Regular.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-RegularItalic.ttf") format("truetype")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:700;src:url("..//fonts/BoschSansCond-Bold.otf"), url("..//fonts/BoschSansCondensed-Bold.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-BoldItalic.ttf") format("truetype")}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */body{margin:0;display:flex;flex-direction:column}main,*{font-family:var(--bci-global-font-family)}::selection,::-moz-selection{background-color:#007bc0}a::-moz-selection{color:#ffffff}.lead{margin-bottom:24px;font-size:18px;font-weight:300;line-height:1.4}@media (min-width: 768px){.lead{font-size:24px}}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */input{color:#000000;background-color:#e0e2e5;box-shadow:none;width:100%;min-width:160px;box-sizing:border-box;caret-color:#000000;cursor:auto;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;border-width:0px 0px 1px 0px;border-color:#000000;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0;padding:12px 16px 11px 16px}input:hover:enabled,input:hover{background-color:#c1c7cc;box-shadow:none}input:hover:enabled~.label-top,input:hover~.label-top{background-color:#c1c7cc}input:hover:enabled::-moz-placeholder,input:hover::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}input:hover:enabled:-ms-input-placeholder,input:hover:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:hover:enabled::-webkit-input-placeholder,input:hover::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:hover:enabled::placeholder,input:hover::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:hover:enabled label,input:hover label{background-color:#c1c7cc}input:active:enabled,input.active,input:active:enabled:hover,input.active:hover{background-color:#a4abb3;box-shadow:none}input:active:enabled~.label-top,input.active~.label-top,input:active:enabled:hover~.label-top,input.active:hover~.label-top{background-color:#a4abb3}input:active:enabled::-moz-placeholder,input.active::-moz-placeholder,input:active:enabled:hover::-moz-placeholder,input.active:hover::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}input:active:enabled:-ms-input-placeholder,input.active:-ms-input-placeholder,input:active:enabled:hover:-ms-input-placeholder,input.active:hover:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:active:enabled::-webkit-input-placeholder,input.active::-webkit-input-placeholder,input:active:enabled:hover::-webkit-input-placeholder,input.active:hover::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:active:enabled::placeholder,input.active::placeholder,input:active:enabled:hover::placeholder,input.active:hover::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:active:enabled label,input.active label,input:active:enabled:hover label,input.active:hover label{background-color:#a4abb3}input:focus,input:focus:enabled,input:focus:hover,input:focus:enabled:hover{background-color:#d1e4ff;box-shadow:none}input:focus~.label-top,input:focus:enabled~.label-top,input:focus:hover~.label-top,input:focus:enabled:hover~.label-top{background-color:#d1e4ff}input:focus::-moz-placeholder,input:focus:enabled::-moz-placeholder,input:focus:hover::-moz-placeholder,input:focus:enabled:hover::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}input:focus:-ms-input-placeholder,input:focus:enabled:-ms-input-placeholder,input:focus:hover:-ms-input-placeholder,input:focus:enabled:hover:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:focus::-webkit-input-placeholder,input:focus:enabled::-webkit-input-placeholder,input:focus:hover::-webkit-input-placeholder,input:focus:enabled:hover::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:focus::placeholder,input:focus:enabled::placeholder,input:focus:hover::placeholder,input:focus:enabled:hover::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:focus label,input:focus:enabled label,input:focus:hover label,input:focus:enabled:hover label{background-color:#d1e4ff}input:disabled,input:hover:disabled,input.disabled{color:#a4abb3;background-color:#eff1f2;cursor:default;border-width:0px 0px 1px 0px;border-color:#a4abb3;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}input:disabled::-moz-placeholder,input:hover:disabled::-moz-placeholder,input.disabled::-moz-placeholder{color:#a4abb3;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}input:disabled:-ms-input-placeholder,input:hover:disabled:-ms-input-placeholder,input.disabled:-ms-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:disabled::-webkit-input-placeholder,input:hover:disabled::-webkit-input-placeholder,input.disabled::-webkit-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:disabled::placeholder,input:hover:disabled::placeholder,input.disabled::placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input:disabled~.label-top,input:hover:disabled~.label-top,input.disabled~.label-top{color:#a4abb3;cursor:not-allowed}input:disabled label,input:hover:disabled label,input.disabled label{background-color:#eff1f2}input[readonly]{color:#a4abb3;background-color:#eff1f2;cursor:default;border-width:0px 0px 1px 0px;border-color:#000000;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}input[readonly]::-moz-placeholder{color:#a4abb3;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}input[readonly]:-ms-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input[readonly]::-webkit-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input[readonly]::placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input[readonly]~.label-top{color:#a4abb3;cursor:not-allowed}input[readonly] label{background-color:#eff1f2}input:focus{box-shadow:none;outline:0}input::-ms-expand{border:0;background-color:transparent}input::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}input:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}input[type=number]{-moz-appearance:textfield}input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.search-input input{padding-right:44px}.search-input::after{font-family:"Bosch-Ic";content:"\\e235";font-size:24px;color:#000000;display:inline-block;position:relative;right:12px;bottom:14px;margin-left:-24px;z-index:1;pointer-events:none}textarea{color:#000000;background-color:#e0e2e5;box-shadow:none;width:100%;min-width:160px;box-sizing:border-box;caret-color:#000000;cursor:auto;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;border-width:0px 0px 1px 0px;border-color:#000000;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0;padding:12px 16px 11px 16px;border-width:0px 0px 1px 0px;border-color:#000000;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0;min-height:96px;resize:vertical}textarea:hover:enabled,textarea:hover{background-color:#c1c7cc;box-shadow:none}textarea:hover:enabled~.label-top,textarea:hover~.label-top{background-color:#c1c7cc}textarea:hover:enabled::-moz-placeholder,textarea:hover::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}textarea:hover:enabled:-ms-input-placeholder,textarea:hover:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:hover:enabled::-webkit-input-placeholder,textarea:hover::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:hover:enabled::placeholder,textarea:hover::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:hover:enabled label,textarea:hover label{background-color:#c1c7cc}textarea:active:enabled,textarea.active,textarea:active:enabled:hover,textarea.active:hover{background-color:#a4abb3;box-shadow:none}textarea:active:enabled~.label-top,textarea.active~.label-top,textarea:active:enabled:hover~.label-top,textarea.active:hover~.label-top{background-color:#a4abb3}textarea:active:enabled::-moz-placeholder,textarea.active::-moz-placeholder,textarea:active:enabled:hover::-moz-placeholder,textarea.active:hover::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}textarea:active:enabled:-ms-input-placeholder,textarea.active:-ms-input-placeholder,textarea:active:enabled:hover:-ms-input-placeholder,textarea.active:hover:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:active:enabled::-webkit-input-placeholder,textarea.active::-webkit-input-placeholder,textarea:active:enabled:hover::-webkit-input-placeholder,textarea.active:hover::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:active:enabled::placeholder,textarea.active::placeholder,textarea:active:enabled:hover::placeholder,textarea.active:hover::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:active:enabled label,textarea.active label,textarea:active:enabled:hover label,textarea.active:hover label{background-color:#a4abb3}textarea:focus,textarea:focus:enabled,textarea:focus:hover,textarea:focus:enabled:hover{background-color:#d1e4ff;box-shadow:none}textarea:focus~.label-top,textarea:focus:enabled~.label-top,textarea:focus:hover~.label-top,textarea:focus:enabled:hover~.label-top{background-color:#d1e4ff}textarea:focus::-moz-placeholder,textarea:focus:enabled::-moz-placeholder,textarea:focus:hover::-moz-placeholder,textarea:focus:enabled:hover::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}textarea:focus:-ms-input-placeholder,textarea:focus:enabled:-ms-input-placeholder,textarea:focus:hover:-ms-input-placeholder,textarea:focus:enabled:hover:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:focus::-webkit-input-placeholder,textarea:focus:enabled::-webkit-input-placeholder,textarea:focus:hover::-webkit-input-placeholder,textarea:focus:enabled:hover::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:focus::placeholder,textarea:focus:enabled::placeholder,textarea:focus:hover::placeholder,textarea:focus:enabled:hover::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:focus label,textarea:focus:enabled label,textarea:focus:hover label,textarea:focus:enabled:hover label{background-color:#d1e4ff}textarea:disabled,textarea:hover:disabled,textarea.disabled{color:#a4abb3;background-color:#eff1f2;cursor:default;border-width:0px 0px 1px 0px;border-color:#a4abb3;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}textarea:disabled::-moz-placeholder,textarea:hover:disabled::-moz-placeholder,textarea.disabled::-moz-placeholder{color:#a4abb3;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}textarea:disabled:-ms-input-placeholder,textarea:hover:disabled:-ms-input-placeholder,textarea.disabled:-ms-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:disabled::-webkit-input-placeholder,textarea:hover:disabled::-webkit-input-placeholder,textarea.disabled::-webkit-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:disabled::placeholder,textarea:hover:disabled::placeholder,textarea.disabled::placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea:disabled~.label-top,textarea:hover:disabled~.label-top,textarea.disabled~.label-top{color:#a4abb3;cursor:not-allowed}textarea:disabled label,textarea:hover:disabled label,textarea.disabled label{background-color:#eff1f2}textarea[readonly]{color:#a4abb3;background-color:#eff1f2;cursor:default;border-width:0px 0px 1px 0px;border-color:#000000;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}textarea[readonly]::-moz-placeholder{color:#a4abb3;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}textarea[readonly]:-ms-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea[readonly]::-webkit-input-placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea[readonly]::placeholder{color:#a4abb3;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea[readonly]~.label-top{color:#a4abb3;cursor:not-allowed}textarea[readonly] label{background-color:#eff1f2}textarea:focus{box-shadow:none;outline:0}textarea::-ms-expand{border:0;background-color:transparent}textarea::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}textarea:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}textarea[type=number]{-moz-appearance:textfield}textarea::-webkit-outer-spin-button,textarea::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}input.valid,textarea.valid,select.valid{border-color:#00884a}input.warning,textarea.warning,select.warning{border-color:#ffcf00}input.invalid,input.error,textarea.invalid,textarea.error,select.invalid,select.error{border-color:#ed0007}.form-group{position:relative}.form-group input:first-child,.form-group textarea:first-child,.form-group select:first-child,.form-group .dropdown:first-child{padding:20px 16px 3px 16px}.form-group input:first-child:last-child,.form-group textarea:first-child:last-child,.form-group select:first-child:last-child,.form-group .dropdown:first-child:last-child{padding:12px 16px 11px 16px}.form-group.form-field-numeric .bosch-ic-clickable{position:absolute;top:12px;cursor:pointer}.form-group.form-field-numeric .bosch-ic-clickable:first-of-type+.bosch-ic-clickable{right:12px}.form-group.form-field-numeric .bosch-ic-clickable:first-of-type{right:60px}.form-group.form-field-numeric .label-top{width:calc(100% - 112px)}.form-group.form-field-numeric input{padding-right:106px}.form-group.form-field-numeric input:disabled~.bosch-ic-clickable{cursor:default}.form-group.form-field-numeric input:disabled~.bosch-ic-clickable,.form-group.form-field-numeric input:disabled~.bosch-ic-clickable:active,.form-group.form-field-numeric input:disabled~.bosch-ic-clickable:hover{color:#a4abb3}.form-group.form-field-numeric input:nth-last-child(3){padding:12px 16px 11px 16px;padding-right:106px}.form-group div.hint,.form-group div.valid,.form-group div.error,.form-group div.warning,.form-group span.hint,.form-group span.valid,.form-group span.error,.form-group span.warning,.form-group p.hint,.form-group p.valid,.form-group p.error,.form-group p.warning{font-size:12px;padding:8px 16px;font-weight:500}.form-group div.valid,.form-group span.valid,.form-group p.valid{color:#00884a}.form-group div.warning,.form-group span.warning,.form-group p.warning{color:#ffcf00}.form-group div.error,.form-group span.error,.form-group p.error{color:#ed0007}@-moz-document url-prefix(){.form-group{position:relative}.form-group input:first-child,.form-group textarea:first-child,.form-group select:first-child,.form-group .dropdown:first-child{padding:20px 16px 3px 16px}.form-group input:first-child:last-child,.form-group textarea:first-child:last-child,.form-group select:first-child:last-child,.form-group .dropdown:first-child:last-child{padding:12px 16px 11px 16px}.form-group select:first-child{padding-left:13px}.form-group.form-field-numeric .bosch-ic-clickable{position:absolute;top:12px;cursor:pointer}.form-group.form-field-numeric .bosch-ic-clickable:first-of-type+.bosch-ic-clickable{right:12px}.form-group.form-field-numeric .bosch-ic-clickable:first-of-type{right:60px}.form-group.form-field-numeric .label-top{width:calc(100% - 112px)}.form-group.form-field-numeric input{padding-right:106px}.form-group.form-field-numeric input:disabled~.bosch-ic-clickable{cursor:default}.form-group.form-field-numeric input:disabled~.bosch-ic-clickable,.form-group.form-field-numeric input:disabled~.bosch-ic-clickable:active,.form-group.form-field-numeric input:disabled~.bosch-ic-clickable:hover{color:#a4abb3}.form-group.form-field-numeric input:nth-last-child(3){padding:12px 16px 11px 16px;padding-right:106px}}.dark-background input{background-color:#ffffff}.dark-background input:active:enabled,.dark-background input.active,.dark-background input:focus:enabled{background-color:#eff1f2}.dark-background input:active:enabled~.label-top,.dark-background input.active~.label-top,.dark-background input:focus:enabled~.label-top{background-color:#eff1f2}.dark-background textarea{background-color:#ffffff}.dark-background textarea:active:enabled,.dark-background textarea.active,.dark-background textarea:focus:enabled{background-color:#eff1f2}.dark-background textarea:active:enabled~.label-top,.dark-background textarea.active~.label-top,.dark-background textarea:focus:enabled~.label-top{background-color:#eff1f2}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */label{font-family:var(--bci-global-font-family);font-weight:400;font-size:12px;line-height:1.5;color:#000000;display:inline;line-height:18px;padding:0 16px 0 0}label.label-top{font-family:var(--bci-global-font-family);font-weight:400;font-size:12px;line-height:1.5;color:#000000;background-color:#e0e2e5;display:block}label.label-top:last-of-type{position:absolute;left:1px;top:1px;z-index:1;padding:4px 0 0 15px;margin:0;width:calc(100% - 48px);max-width:calc(100% - 48px);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}label.label-top-disabled{color:#a4abb3;cursor:not-allowed}label.label-top-focused{background-color:#d1e4ff}.dark-background label.label-top{background-color:#ffffff}.dark-background label.label-top-focused{background-color:#eff1f2}.bosch-ic,.Bosch-Ic{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic:before,.Bosch-Ic:before{font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium,.Bosch-Ic-Medium{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium:before,.Bosch-Ic-Medium:before{font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-clickable,.Bosch-Ic-Clickable{cursor:pointer}.bosch-ic-clickable:disabled,.bosch-ic-clickable.disabled,.Bosch-Ic-Clickable:disabled,.Bosch-Ic-Clickable.disabled{color:#a4abb3;cursor:not-allowed}.bosch-ic-delete:before{content:"\\e118"}.bosch-ic-close-small:before{content:"\\e6c0"}.bosch-ic-calendar-clock:before{content:"\\e2d1"}.bosch-ic-calendar:before{content:"\\e0c8"}.bosch-ic-clock:before{content:"\\e0ef"}:host{display:block;position:relative}:host .bci-core-datetime-picker{display:inline-block;position:relative;width:100%}:host .bci-core-datetime-picker input{padding-right:50px;height:48px}:host .bci-core-datetime-picker input::-moz-placeholder{color:#000000;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;opacity:0.5;transform:translateX(-0.5px)}:host .bci-core-datetime-picker input:-ms-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}:host .bci-core-datetime-picker input::-webkit-input-placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}:host .bci-core-datetime-picker input::placeholder{color:#000000;opacity:0.5;font-family:var(--bci-global-font-family);font-weight:400;font-size:16px;line-height:1.5;transform:translateX(-0.5px)}:host .bci-core-datetime-picker input:placeholder-shown{text-overflow:ellipsis}:host .bci-core-datetime-picker label{background:transparent}:host .bci-core-datetime-picker input:not(.has-label){padding-top:12px;padding-bottom:12px}:host .bci-core-datetime-picker .input-field{cursor:text;border-bottom:1px solid black !important}:host .bci-core-datetime-picker .label-top{width:fit-content;max-width:calc(100% - 64px);height:16px;pointer-events:none}:host .bci-core-datetime-picker .icon{position:absolute;right:0;top:0;width:48px;aspect-ratio:1;border-bottom:1px solid black;box-sizing:border-box;display:grid;place-items:center;z-index:2;background:#e0e2e5;cursor:pointer}:host .bci-core-datetime-picker .icon:hover{background:#c1c7cc}:host .bci-core-datetime-picker .icon:active{background:#a4abb3}:host .bci-core-datetime-picker input:disabled{cursor:default;pointer-events:none;border-color:#7d8389 !important;color:#7d8389;background:#eff1f2}:host .bci-core-datetime-picker input:disabled:hover+label{background:transparent}:host .bci-core-datetime-picker input:disabled::placeholder{color:#7d8389}:host .bci-core-datetime-picker input:disabled+label{cursor:default}:host .bci-core-datetime-picker input:disabled+label+.icon{pointer-events:none;cursor:default;color:#7d8389;border-color:#7d8389;background:#e0e2e5}:host .bci-core-datetime-picker input:disabled+label+.icon:hover,:host .bci-core-datetime-picker input:disabled+label+.icon:active{background:initial}:host .bci-core-datetime-picker input:disabled+label+.icon span{cursor:default}:host .hidden{display:none}';
var EMPTYDATEPICKERINPUTOBJ = {
  dateStart: null,
  dateEnd: null,
  timeStart: null,
  timeEnd: null,
  timestampStart: null,
  timestampEnd: null,
  valid: true
};
var DatetimePicker = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.newDateSelected = createEvent(this, "newDateSelected", 7);
    this.overlayHandler = new OverlayHandler();
    this.formatService = formattingServiceFactory();
    this.dateService = dateServiceFactory({
      formatService: this.formatService
    });
    this.showTemplates = false;
    this.label = "";
    this.placeholder = "";
    this.dateTimePickerId = void 0;
    this.formatDate = "dd.MM.yyyy";
    this.maxDate = 2e13;
    this.minDate = 0;
    this.hide = void 0;
    this.templatesList = void 0;
    this.templatesDisabled = void 0;
    this.sundayFirst = void 0;
    this.rangeDateTime = void 0;
    this.disabled = void 0;
    this.darkBackground = void 0;
    this.timeFormat = "H:mm";
    this.timeZone = void 0;
    this.dateTime = void 0;
    this.timeStamp = void 0;
    this.calendarWeekViewMode = "off";
    this.required = void 0;
    this.dateTimePickerInputObj = EMPTYDATEPICKERINPUTOBJ;
    this.formatTime = void 0;
    this.inputPlaceholder = void 0;
    this.datePickerConfig = void 0;
    this.timePickerConfig = void 0;
    this.hideSeconds = void 0;
    this.overlayVisible = false;
  }
  componentWillLoad() {
    return __async(this, null, function* () {
      yield setLocale();
      this.setInputPlaceholderText();
      listenToLanguageChange(() => this.setInputPlaceholderText());
      this.timePickerConfig = {
        timeFormat: this.timeFormat,
        rangeDateTime: this.rangeDateTime
      };
      this.datePickerConfig = {
        formatDate: this.formatDate === void 0 ? state.dateFormat : this.formatDate,
        maxDate: this.maxDate,
        minDate: this.minDate,
        rangeDateTime: this.rangeDateTime,
        sundayFirst: this.sundayFirst,
        timeZone: this.timeZone
      };
    });
  }
  componentDidLoad() {
    this.overlayHandler.dialog.onclick = (ev) => {
      ev.stopPropagation();
    };
    this.overlayHandler.host.onclick = (ev) => {
      ev.stopPropagation();
    };
    this.formatService.dateFormat = this.formatDate;
    this.formatService.timeFormat = this.timeFormat;
    this.formatService.timeZone = this.timeZone;
    if (isHideSeconds(this.timeFormat)) {
      this.hideSeconds = false;
      this.dateService.hideSeconds = false;
    } else {
      this.hideSeconds = true;
      this.dateService.hideSeconds = true;
    }
    this.dateService.rangeDateTime = this.rangeDateTime;
    this.dateService.minDate = this.minDate;
    this.dateService.maxDate = this.maxDate;
    const dateTime = this.getStartupDateTime();
    if (dateTime === void 0) {
      return;
    }
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), dateTime);
    this.removeHiddenDateParts();
    this.updateDatePickerInput();
  }
  onDateTimeChanged(dateTime) {
    var _a;
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), dateTime);
    this.removeHiddenDateParts();
    this.selectedTemplateId = null;
    (_a = this.overlayHandler.dialog) === null || _a === void 0 ? void 0 : _a.close();
    this.updateDatePickerInput();
  }
  onTimeStampChanged(timeStamp) {
    var _a;
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), {
      dateStart: this.formatService.formatDate(timeStamp.timestampStart),
      timeStart: this.formatService.formatTime(timeStamp.timestampStart),
      dateEnd: this.formatService.formatDate(timeStamp.timestampEnd),
      timeEnd: this.formatService.formatTime(timeStamp.timestampEnd)
    });
    this.removeHiddenDateParts();
    this.selectedTemplateId = null;
    (_a = this.overlayHandler.dialog) === null || _a === void 0 ? void 0 : _a.close();
    this.updateDatePickerInput();
  }
  onPlaceholderChanged() {
    this.setInputPlaceholderText();
  }
  onCalendarWeekViewModeChanged() {
    this.updateDatePickerInput();
  }
  /**
   * - listens to the 'OverlayOpenedEvent' event which is triggered when another datetimepicker on the page is opened (see overlay component)
   * - if this is the case and the overlay of this datetimepicker is open (which closes through the event) the placeholder text needs to be updated
   * - therefore the placeholder is shown again after the user opens the datetimepicker and closes it through the opening of another datetimepicker on the page
   */
  updatePlaceholderOnOverlayOpenedEvent() {
    var _a;
    this.overlayVisible = false;
    (_a = this.overlayHandler.dialog) === null || _a === void 0 ? void 0 : _a.closeYearMonthSelection();
    if (this.overlayHandler.isVisible()) {
      this.setInputPlaceholderText();
    }
  }
  handleMouseDown(event) {
    this.handleClickOutside(event);
  }
  handleTouchStart(event) {
    this.handleClickOutside(event);
  }
  handleClickOutside(event) {
    var _a, _b, _c;
    if (this.overlayHandler.isHidden()) {
      return;
    }
    if (((_a = this.datepickerRef) === null || _a === void 0 ? void 0 : _a.contains(event.target)) || ((_b = this.overlayHandler.dialog) === null || _b === void 0 ? void 0 : _b.contains(event.target))) {
      return;
    }
    if (this.overlayHandler.isVisible()) {
      this.save();
      this.overlayVisible = false;
      (_c = this.overlayHandler.dialog) === null || _c === void 0 ? void 0 : _c.closeYearMonthSelection();
    }
  }
  handleEscape(ev) {
    if (ev.key !== "Escape") {
      return;
    }
    if (this.overlayHandler.isVisible()) {
      this.save();
    }
    this.dateTimePickerInput.blur();
  }
  handleTab(ev) {
    var _a;
    if (ev.key !== "Tab") {
      return;
    }
    const sourceTarget = ev.composedPath()[0];
    if (this.hide === "time" || sourceTarget.dataset.seconds && (sourceTarget.parentElement && sourceTarget.parentElement.classList.contains("rangeTimePicker") || !this.rangeDateTime) && !ev.shiftKey || this.hideSeconds && !this.rangeDateTime && sourceTarget.dataset.minutes && !ev.shiftKey || this.hideSeconds && sourceTarget.dataset.minutes && sourceTarget.parentElement && sourceTarget.parentElement.classList.contains("rangeTimePicker") || sourceTarget && sourceTarget.classList.contains("input-field") && ev.shiftKey) {
      this.save();
    }
    this.overlayHandler.hide();
    this.overlayVisible = false;
    (_a = this.overlayHandler.dialog) === null || _a === void 0 ? void 0 : _a.closeYearMonthSelection();
  }
  handleEnter(ev) {
    if (ev.key !== "Enter") {
      return;
    }
    if (this.overlayHandler.isVisible()) {
      this.save();
    }
    this.dateTimePickerInput.blur();
  }
  toggle() {
    return __async(this, null, function* () {
      if (!this.overlayHandler.isReady()) {
        return;
      }
      if (this.overlayHandler.isVisible()) {
        this.save();
      } else {
        this.overlayHandler.show();
        this.overlayVisible = true;
      }
      this.setInputPlaceholderText();
    });
  }
  showPickerContainer() {
    this.inputPlaceholder = "";
    this.overlayHandler.show();
    this.overlayVisible = true;
  }
  getStartupDateTime() {
    if (this.timeStamp) {
      return {
        dateStart: this.formatService.formatDate(this.timeStamp.timestampStart),
        timeStart: this.formatService.formatTime(this.timeStamp.timestampStart),
        dateEnd: this.formatService.formatDate(this.timeStamp.timestampEnd),
        timeEnd: this.formatService.formatTime(this.timeStamp.timestampEnd)
      };
    }
    if (this.dateTime) {
      const {
        dateStart,
        dateEnd,
        timeStart,
        timeEnd
      } = this.dateTime;
      return {
        dateStart: dateStart !== null && dateStart !== void 0 ? dateStart : null,
        dateEnd: dateEnd !== null && dateEnd !== void 0 ? dateEnd : null,
        timeStart: timeStart !== null && timeStart !== void 0 ? timeStart : null,
        timeEnd: timeEnd !== null && timeEnd !== void 0 ? timeEnd : null
      };
    }
  }
  updateDatePickerInput() {
    if (this.dateTimePickerInput) {
      this.dateTimePickerInput.value = this.getInputValue();
    }
  }
  /** Function to reset the datepicker's selection */
  resetSelection() {
    return __async(this, null, function* () {
      var _a;
      this.dateTimePickerInputObj = EMPTYDATEPICKERINPUTOBJ;
      this.selectedTemplateId = null;
      (_a = this.overlayHandler.dialog) === null || _a === void 0 ? void 0 : _a.close();
      this.updateDatePickerInput();
      this.newDateSelected.emit(this.dateTimePickerInputObj);
    });
  }
  getInputValue() {
    var _a;
    let returnValue = "";
    if (this.isNull()) {
      return returnValue;
    }
    if (this.showTemplates && this.selectedTemplateId) {
      const foundElement = this.templatesList.find((x) => "templates" in x ? x.templates.find((y) => y.id === this.selectedTemplateId) : x.id === this.selectedTemplateId);
      returnValue = "templates" in foundElement ? (_a = foundElement.templates.find((template) => template.id === this.selectedTemplateId)) === null || _a === void 0 ? void 0 : _a.label : foundElement === null || foundElement === void 0 ? void 0 : foundElement.label;
    }
    if (returnValue === "") {
      const {
        dateStart,
        dateEnd,
        timeStart,
        timeEnd
      } = this.dateTimePickerInputObj;
      returnValue = this.formatService.formatRange(dateStart, dateEnd, timeStart, timeEnd);
    }
    if (!this.overlayVisible && this.calendarWeekViewMode !== "off") {
      returnValue = this.formatService.formatCalendarWeek(this.dateTimePickerInputObj.dateStart, this.sundayFirst ? 0 : 1, this.calendarWeekViewMode);
    }
    return returnValue;
  }
  get openingIcon() {
    return this.hide === "calendar" ? "clock" : this.hide === "time" ? "calendar" : "calendar-clock";
  }
  updateDatePickerComponent(ev) {
    const value = ev.target.value;
    const dateInput = this.dateService.getDataFromInput(value);
    const validityCheck = this.dateService.validateInput(dateInput);
    this.dateTimePickerInputObj = Object.assign(Object.assign(Object.assign({}, this.dateTimePickerInputObj), dateInput), validityCheck);
  }
  /** Corrects data based on configuration */
  correctData() {
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), this.dateService.correctInputData(this.dateTimePickerInputObj));
    this.removeHiddenDateParts();
  }
  removeHiddenDateParts() {
    if (this.hide === "calendar") {
      this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), {
        dateStart: null,
        dateEnd: null
      });
    } else if (this.hide === "time") {
      this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), {
        timeStart: null,
        timeEnd: null
      });
    }
  }
  setInputPlaceholderText() {
    if (this.placeholder) {
      this.inputPlaceholder = this.placeholder;
    } else {
      if (this.hide === "time") {
        this.inputPlaceholder = translate("setDate");
        if (this.rangeDateTime) {
          this.inputPlaceholder += " " + translate("range");
        }
      } else if (this.hide === "calendar") {
        this.inputPlaceholder = translate("setTime");
        if (this.rangeDateTime) {
          this.inputPlaceholder += " " + translate("range");
        }
      } else {
        this.inputPlaceholder = translate("setDateTime");
        if (this.rangeDateTime) {
          this.inputPlaceholder = translate("setRange");
        }
      }
    }
  }
  save() {
    var _a;
    if (!this.overlayHandler.isReady()) {
      return;
    }
    this.overlayHandler.hide();
    this.overlayVisible = false;
    (_a = this.overlayHandler.dialog) === null || _a === void 0 ? void 0 : _a.closeYearMonthSelection();
    if (this.dateTimePickerInput.value.trim().length === 0) {
      this.setInputPlaceholderText();
    }
    if (this.rangeDateTime && !this.dateTimePickerInputObj.dateEnd) {
      this.dateTimePickerInputObj.dateEnd = this.dateTimePickerInputObj.dateStart;
    }
    if (this.hide === "calendar" && !this.dateTimePickerInputObj.timeStart) {
      this.dateTimePickerInputObj.timeStart = this.dateTimePickerInputObj.timeEnd;
    }
    this.correctData();
    const timestampStart = this.dateService.toSelectionTimestamp(this.dateTimePickerInputObj.dateStart, this.dateTimePickerInputObj.timeStart);
    const timestampEnd = this.dateService.toSelectionTimestamp(this.dateTimePickerInputObj.dateEnd, this.dateTimePickerInputObj.timeEnd);
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), {
      timestampStart: timestampStart !== null && timestampStart !== void 0 ? timestampStart : null,
      timestampEnd: timestampEnd !== null && timestampEnd !== void 0 ? timestampEnd : null
    });
    this.updateDatePickerInput();
    if (!this.showTemplates) {
      this.selectedTemplateId = "";
    }
    this.newDateSelected.emit(this.dateTimePickerInputObj);
  }
  isNull() {
    return this.dateTimePickerInputObj.dateStart === null && this.dateTimePickerInputObj.dateEnd === null && this.dateTimePickerInputObj.timeStart === null && this.dateTimePickerInputObj.timeEnd === null;
  }
  handleSelectionChanged(event) {
    event.stopPropagation();
    switch (event.detail.reason) {
      case "date":
        this.showTemplates = false;
        this.handleDateChange(event.detail);
        break;
      case "time":
        this.showTemplates = false;
        this.handleTimeChange(event.detail);
        break;
      case "frame":
        this.showTemplates = true;
        this.handleRangeChange(event.detail);
        break;
    }
    this.updateDatePickerInput();
  }
  handleDateChange({
    startDate: dateStart,
    endDate: dateEnd,
    rangeCompleted
  }) {
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), {
      dateStart,
      dateEnd: dateEnd !== null && dateEnd !== void 0 ? dateEnd : null
    });
    if (this.hide === "time") {
      if (!this.rangeDateTime || this.rangeDateTime && rangeCompleted) {
        this.save();
      }
    }
  }
  handleTimeChange(newTimes) {
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), newTimes);
  }
  handleRangeChange(event) {
    var _a, _b;
    if (event.firstEmit) {
      this.selectedTemplateId = event.id;
    }
    const timestampStart = (_a = this.dateService.toSelectionTimestamp(this.formatService.formatDate(event.timestampStart), this.formatService.formatTime(event.timestampStart))) !== null && _a !== void 0 ? _a : null;
    const timestampEnd = (_b = this.dateService.toSelectionTimestamp(this.formatService.formatDate(event.timestampEnd), this.formatService.formatTime(event.timestampEnd))) !== null && _b !== void 0 ? _b : null;
    this.dateTimePickerInputObj = Object.assign(Object.assign({}, this.dateTimePickerInputObj), {
      dateStart: this.formatService.formatDate(event.timestampStart),
      dateEnd: this.formatService.formatDate(event.timestampEnd),
      timeStart: this.formatService.formatTime(event.timestampStart),
      timeEnd: this.formatService.formatTime(event.timestampEnd),
      timestampStart,
      timestampEnd
    });
    this.removeHiddenDateParts();
    if (this.overlayHandler.isReady()) {
      this.newDateSelected.emit(this.dateTimePickerInputObj);
    }
    if (event.firstEmit) {
      this.hideOverlay();
    }
    this.updateDatePickerInput();
  }
  hideOverlay() {
    this.updateDatePickerInput();
    this.overlayHandler.hide();
    this.overlayVisible = false;
  }
  onFocus() {
    this.showPickerContainer();
    this.updateDatePickerInput();
  }
  render() {
    var _a;
    return h(Host, {
      ref: (el) => this.datepickerRef = el
    }, h("div", {
      class: {
        "bci-core-datetime-picker": true,
        "form-group": true,
        "dark-background": !!this.darkBackground
      },
      id: this.dateTimePickerId,
      ref: (el) => this.overlayHandler.host = el
    }, h("input", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.input-field",
      id: this.dateTimePickerId + "-input",
      class: {
        "input-field": true,
        invalid: !this.dateTimePickerInputObj.valid,
        "has-label": !!this.label
      },
      type: "text",
      ref: (el) => this.dateTimePickerInput = el,
      onFocus: () => this.onFocus(),
      onInput: (event) => this.updateDatePickerComponent(event),
      onBlur: () => this.updateDatePickerInput(),
      placeholder: this.inputPlaceholder,
      disabled: this.disabled,
      value: ""
    }), h("label", {
      class: "label-top"
    }, ((_a = this.label) !== null && _a !== void 0 ? _a : "") + (this.required ? "*" : "")), !this.overlayVisible ? h("div", {
      class: "icon",
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.toggle-picker",
      onClick: () => this.dateTimePickerInput.focus(),
      title: "Open"
    }, h("span", {
      class: `bosch-ic bci-core-calendar-icon bosch-ic-clickable bosch-ic-${this.openingIcon} has-label`
    })) : this.dateTimePickerInput.value.trim() ? h("div", {
      class: "icon",
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.reset-picker",
      onClick: () => __async(this, null, function* () {
        yield this.resetSelection();
        this.dateTimePickerInput.focus();
      }),
      title: "Clear"
    }, h("span", {
      class: "bosch-ic bci-core-calendar-icon bosch-ic-clickable bosch-ic-delete has-label"
    })) : h("div", {
      class: "icon",
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.toggle-picker",
      onClick: () => this.toggle(),
      title: "Close"
    }, h("span", {
      class: "bosch-ic bci-core-calendar-icon bosch-ic-clickable bosch-ic-close-small has-label"
    }))), h("bci-overlay", {
      ref: (el) => this.overlayHandler.overlay = el
    }, h("bci-datetime-dialog", {
      ref: (el) => this.overlayHandler.dialog = el,
      dateTimePickerId: this.dateTimePickerId,
      onSelectionChanged: (timeStamp) => this.handleSelectionChanged(timeStamp),
      toggleDialogHandler: () => this.toggle(),
      hide: this.hide,
      templatesList: this.templatesList,
      datePickerConfig: this.datePickerConfig,
      timePickerConfig: this.timePickerConfig,
      datePickerInputObj: this.dateTimePickerInputObj,
      formatService: this.formatService,
      dateService: this.dateService,
      templatesDisabled: this.templatesDisabled
    })));
  }
  static get watchers() {
    return {
      "dateTime": ["onDateTimeChanged"],
      "timeStamp": ["onTimeStampChanged"],
      "placeholder": ["onPlaceholderChanged"],
      "calendarWeekViewMode": ["onCalendarWeekViewModeChanged"]
    };
  }
};
DatetimePicker.style = datetimePickerCss;
var templateCss = '/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */@font-face{font-family:"Bosch-Sans";font-weight:400;src:url("..//fonts/BoschSans-Regular.eot");src:url("..//fonts/BoschSans-Regular.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Regular.woff2") format("woff2"), url("..//fonts/BoschSans-Regular.woff") format("woff"), url("..//fonts/BoschSans-Regular.ttf") format("truetype"), url("..//fonts/BoschSans-RegularItalic.woff2") format("woff2"), url("..//fonts/BoschSans-RegularItalic.woff") format("woff"), url("..//fonts/BoschSans-RegularItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Regular.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:300;src:url("..//fonts/BoschSans-Light.eot");src:url("..//fonts/BoschSans-Light.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Light.woff2") format("woff2"), url("..//fonts/BoschSans-Light.woff") format("woff"), url("..//fonts/BoschSans-Light.ttf") format("truetype"), url("..//fonts/BoschSans-LightItalic.woff2") format("woff2"), url("..//fonts/BoschSans-LightItalic.woff") format("woff"), url("..//fonts/BoschSans-LightItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Light.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:500;src:url("..//fonts/BoschSans-Medium.eot");src:url("..//fonts/BoschSans-Medium.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Medium.woff2") format("woff2"), url("..//fonts/BoschSans-Medium.woff") format("woff"), url("..//fonts/BoschSans-Medium.ttf") format("truetype"), url("..//fonts/BoschSans-MediumItalic.woff2") format("woff2"), url("..//fonts/BoschSans-MediumItalic.woff") format("woff"), url("..//fonts/BoschSans-MediumItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Medium.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:700;src:url("..//fonts/BoschSans-Bold.eot");src:url("..//fonts/BoschSans-Bold.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Bold.woff2") format("woff2"), url("..//fonts/BoschSans-Bold.woff") format("woff"), url("..//fonts/BoschSans-Bold.ttf") format("truetype"), url("..//fonts/BoschSans-BoldItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BoldItalic.woff") format("woff"), url("..//fonts/BoschSans-BoldItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Bold.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:900;src:url("..//fonts/BoschSans-Black.eot");src:url("..//fonts/BoschSans-Black.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Black.woff2") format("woff2"), url("..//fonts/BoschSans-Black.woff") format("woff"), url("..//fonts/BoschSans-Black.ttf") format("truetype"), url("..//fonts/BoschSans-BlackItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BlackItalic.woff") format("woff"), url("..//fonts/BoschSans-BlackItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Black.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Ic";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url("..//fonts/Bosch-Icon.eot?mh5qa9");src:url("..//fonts/Bosch-Icon.eot?mh5qa9#iefix") format("embedded-opentype"), url("..//fonts/Bosch-Icon.ttf?mh5qa9") format("truetype"), url("..//fonts/Bosch-Icon.woff?mh5qa9") format("woff"), url("..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon") format("svg")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:300;src:url("..//fonts/BoschSansCond-Regular.otf"), url("..//fonts/BoschSansCondensed-Regular.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-RegularItalic.ttf") format("truetype")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:700;src:url("..//fonts/BoschSansCond-Bold.otf"), url("..//fonts/BoschSansCondensed-Bold.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-BoldItalic.ttf") format("truetype")}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */label{font-family:"Bosch-Sans";font-weight:400;font-size:12px;line-height:1.5;color:#000000;display:inline;line-height:18px;padding:0 16px 0 0}label.label-top{font-family:"Bosch-Sans";font-weight:400;font-size:12px;line-height:1.5;color:#000000;background-color:#e0e2e5;display:block}label.label-top:last-of-type{position:absolute;left:1px;top:1px;z-index:1;padding:4px 0 0 15px;margin:0;width:calc(100% - 48px);max-width:calc(100% - 48px);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}label.label-top-disabled{color:#a4abb3;cursor:not-allowed}label.label-top-focused{background-color:#d1e4ff}.dark-background label.label-top{background-color:#ffffff}.dark-background label.label-top-focused{background-color:#eff1f2}/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */:host{display:block}:host .templates-container{display:block;background-color:#ffffff;min-width:292px;flex-shrink:0}:host .templates-container .mdc-list{margin:0;background:#ffffff;outline:none;list-style-type:none;padding:0 !important;overflow-y:auto;overflow-y:overlay;max-height:240px}:host .templates-container .mdc-list::-webkit-scrollbar{width:16px;height:16px;padding-right:2px}:host .templates-container .mdc-list::-webkit-scrollbar-track{background:transparent}:host .templates-container .mdc-list::-webkit-scrollbar-thumb{background:#7d8389;background-clip:padding-box;border:5px solid transparent;min-height:48px}:host .templates-container .mdc-list::-webkit-scrollbar-thumb:hover{background:#43464a;background-clip:padding-box;border:3px solid transparent}:host .templates-container .mdc-list-item{line-height:1.5;padding:12px 16px !important;cursor:pointer;display:flex;align-items:center;overflow:hidden;margin:0 !important;background-color:#ffffff;color:#000000;font-size:16px;text-overflow:ellipsis}:host .templates-container .mdc-list-item::before{content:none !important}:host .templates-container .mdc-list-item:hover{background-color:#e0e2e5}:host .templates-container .mdc-list-item:active{background-color:#c1c7cc}:host .templates-container .mdc-list-item>*{color:inherit;font-size:inherit;line-height:inherit;cursor:inherit;padding:0;margin:0}:host .templates-container .mdc-list-item.selected{line-height:1.5;padding:12px 16px !important;cursor:pointer;display:flex;align-items:center;overflow:hidden;margin:0 !important;background-color:#007bc0;color:#ffffff}:host .templates-container .mdc-list-item.selected::before{content:none !important}:host .templates-container .mdc-list-item.selected:hover{background-color:#00629a}:host .templates-container .mdc-list-item.selected:active{background-color:#004975}:host .templates-container .mdc-list-item.disabled{background:#ffffff;cursor:default;pointer-events:none;color:#a4abb3}:host .templates-container .mdc-list-item .mdc-list-item__text{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}:host .templates-container .optiongroup-label{font-weight:700;font-size:12px;line-height:1.5;padding:12px 16px 4px 16px;width:100%;box-sizing:border-box;user-select:none;overflow:hidden;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical}:host .templates-container .optiongroup-label.disabled,:host .templates-container .optiongroup-label:disabled{color:#a4abb3}:host .templates-container :not(.mdc-list--non-interactive)>:not(.mdc-list-item--disabled).mdc-list-item::after{cursor:pointer}:host .templates-container .mdc-list-item__graphic{margin-right:8px}@media (min-width: 767px){.templates-container{max-width:584px}}@media (max-width: 767px){.templates-container{max-width:292px}}';
var TemplateList = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.showTimeRanges = createEvent(this, "showTimeRanges", 7);
    this.templateUpdate = createEvent(this, "templateUpdate", 7);
    this.templatesList = void 0;
    this.visible = false;
    this.selectedTemplateId = void 0;
  }
  /**
   * Clears internal timer and resets selected template
   */
  reset() {
    return __async(this, null, function* () {
      this.selectedTemplateId = "";
      this.clearTimer();
    });
  }
  clearTimer() {
    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = 0;
    }
  }
  updateTemplate(event, option) {
    event.stopPropagation();
    this.selectedTemplateId = option.id;
    this.clearTimer();
    this.applyTemplate(option, true);
    if (option.liveUpdates && option.refreshInterval && option.refreshInterval > 0) {
      this.timerId = window.setInterval(() => {
        this.applyTemplate(option, false);
      }, option.refreshInterval);
    }
  }
  applyTemplate(option, firstEmit) {
    this.templateUpdate.emit({
      timeStamp: option.calculateTemplate(),
      firstEmit,
      id: this.selectedTemplateId
    });
  }
  render() {
    var _a;
    return this.visible ? h(Host, {
      class: "templates-container"
    }, h("ul", {
      class: "mdc-list",
      role: "listbox"
    }, (_a = this.templatesList) === null || _a === void 0 ? void 0 : _a.map((element) => "id" in element ? h("li", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.selecttemplate",
      class: {
        "mdc-list-item": true,
        selected: this.selectedTemplateId === element.id,
        disabled: element.disabled
      },
      role: "option",
      "aria-label": `Select ${element.label}`,
      title: element.label,
      onClick: (item) => this.updateTemplate(item, element)
    }, h("label", {
      class: "mdc-list-item__text"
    }, element.label)) : (
      // element is TemplateGroup
      element.templates.length > 0 && h("div", {
        "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.optiongroup"
      }, h("span", {
        "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.optiongrouplabel",
        class: {
          "optiongroup-label": true,
          disabled: element.disabled
        }
      }, element.label), element.templates.map((template) => h("li", {
        "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.selecttemplate",
        class: {
          "mdc-list-item": true,
          selected: this.selectedTemplateId === template.id,
          disabled: template.disabled || element.disabled
        },
        role: "option",
        "aria-label": `Select ${template.label}`,
        title: template.label,
        onClick: (item) => this.updateTemplate(item, template)
      }, h("label", {
        class: "mdc-list-item__text"
      }, template.label))))
    ))), h("div", {
      class: "bci-core-datepicker-selection"
    }, h("button", {
      "data-test": "webcore.webcomponents.datetime-picker.core-datetime-picker.timerange",
      class: "button-secondary no-arrow",
      onClick: () => this.showTimeRanges.emit()
    }, translate("calendar")))) : null;
  }
};
TemplateList.style = templateCss;
var yearMonthCss = '/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */@font-face{font-family:"Bosch-Sans";font-weight:400;src:url("..//fonts/BoschSans-Regular.eot");src:url("..//fonts/BoschSans-Regular.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Regular.woff2") format("woff2"), url("..//fonts/BoschSans-Regular.woff") format("woff"), url("..//fonts/BoschSans-Regular.ttf") format("truetype"), url("..//fonts/BoschSans-RegularItalic.woff2") format("woff2"), url("..//fonts/BoschSans-RegularItalic.woff") format("woff"), url("..//fonts/BoschSans-RegularItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Regular.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:300;src:url("..//fonts/BoschSans-Light.eot");src:url("..//fonts/BoschSans-Light.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Light.woff2") format("woff2"), url("..//fonts/BoschSans-Light.woff") format("woff"), url("..//fonts/BoschSans-Light.ttf") format("truetype"), url("..//fonts/BoschSans-LightItalic.woff2") format("woff2"), url("..//fonts/BoschSans-LightItalic.woff") format("woff"), url("..//fonts/BoschSans-LightItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Light.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:500;src:url("..//fonts/BoschSans-Medium.eot");src:url("..//fonts/BoschSans-Medium.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Medium.woff2") format("woff2"), url("..//fonts/BoschSans-Medium.woff") format("woff"), url("..//fonts/BoschSans-Medium.ttf") format("truetype"), url("..//fonts/BoschSans-MediumItalic.woff2") format("woff2"), url("..//fonts/BoschSans-MediumItalic.woff") format("woff"), url("..//fonts/BoschSans-MediumItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Medium.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:700;src:url("..//fonts/BoschSans-Bold.eot");src:url("..//fonts/BoschSans-Bold.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Bold.woff2") format("woff2"), url("..//fonts/BoschSans-Bold.woff") format("woff"), url("..//fonts/BoschSans-Bold.ttf") format("truetype"), url("..//fonts/BoschSans-BoldItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BoldItalic.woff") format("woff"), url("..//fonts/BoschSans-BoldItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Bold.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Sans";font-weight:900;src:url("..//fonts/BoschSans-Black.eot");src:url("..//fonts/BoschSans-Black.eot?#iefix") format("embedded-opentype"), url("..//fonts/BoschSans-Black.woff2") format("woff2"), url("..//fonts/BoschSans-Black.woff") format("woff"), url("..//fonts/BoschSans-Black.ttf") format("truetype"), url("..//fonts/BoschSans-BlackItalic.woff2") format("woff2"), url("..//fonts/BoschSans-BlackItalic.woff") format("woff"), url("..//fonts/BoschSans-BlackItalic.ttf") format("truetype"), url("..//fonts/BoschSans-Black.svg#svgFontName") format("svg");}@font-face{font-family:"Bosch-Ic";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url("..//fonts/Bosch-Icon.eot?mh5qa9");src:url("..//fonts/Bosch-Icon.eot?mh5qa9#iefix") format("embedded-opentype"), url("..//fonts/Bosch-Icon.ttf?mh5qa9") format("truetype"), url("..//fonts/Bosch-Icon.woff?mh5qa9") format("woff"), url("..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon") format("svg")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:300;src:url("..//fonts/BoschSansCond-Regular.otf"), url("..//fonts/BoschSansCondensed-Regular.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-RegularItalic.ttf") format("truetype")}@font-face{font-family:"Bosch-Sans-Condensed";font-weight:700;src:url("..//fonts/BoschSansCond-Bold.otf"), url("..//fonts/BoschSansCondensed-Bold.ttf") format("truetype"), url("..//fonts/BoschSansCondensed-BoldItalic.ttf") format("truetype")}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */.clickable-element{transition:0.4s;cursor:pointer}.clickable-element:active,.clickable-element.active{color:#007bc0}.clickable-element:active:hover,.clickable-element:hover .clickable-element:focus{color:#9dc9ff}.clickable-element:disabled,.clickable-element.disabled{color:#a4abb3}.clickable-element:disabled:active,.clickable-element:disabled.active,.clickable-element:disabled:active:hover,.clickable-element:disabled:hover,.clickable-element.disabled:active,.clickable-element.disabled.active,.clickable-element.disabled:active:hover,.clickable-element.disabled:hover{color:#a4abb3}button.button-primary,button.button-default,input.button-primary,input.button-default{color:#ffffff;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;background-color:#007bc0;background-size:100% 200%;border-width:0;border-color:transparent;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-primary:hover,button.button-primary:focus-visible,button.button-default:hover,button.button-default:focus-visible,input.button-primary:hover,input.button-primary:focus-visible,input.button-default:hover,input.button-default:focus-visible{background-color:#00629a;color:#ffffff;outline:0}button.button-primary:active,button.button-default:active,input.button-primary:active,input.button-default:active{background-color:#004975;color:#ffffff;outline:0}button.button-primary:disabled,button.button-primary.disabled,button.button-default:disabled,button.button-default.disabled,input.button-primary:disabled,input.button-primary.disabled,input.button-default:disabled,input.button-default.disabled{background-color:#c1c7cc;color:#656a6f;background-image:none;cursor:default}button.button-primary:not(.mat-mdc-icon-button):has(mat-icon),button.button-default:not(.mat-mdc-icon-button):has(mat-icon),input.button-primary:not(.mat-mdc-icon-button):has(mat-icon),input.button-default:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-secondary,input.button-secondary{color:#007bc0;background-color:transparent;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 15px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;border-width:1px;border-color:#007bc0;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-secondary:hover,button.button-secondary:focus-visible,input.button-secondary:hover,input.button-secondary:focus-visible{border-color:#004975;outline:0;color:#004975;background-color:#d1e4ff;border-width:1px;border-color:#004975;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-secondary:active,input.button-secondary:active{border-color:#004975;color:#004975;outline:0;background-color:#9dc9ff;border-width:1px;border-color:#004975;border-style:solid;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;border-radius:0}button.button-secondary:disabled,button.button-secondary.disabled,input.button-secondary:disabled,input.button-secondary.disabled{color:#979ea4;border-color:#979ea4;background-color:transparent;cursor:default}button.button-secondary:not(.mat-mdc-icon-button):has(mat-icon),input.button-secondary:not(.mat-mdc-icon-button):has(mat-icon){padding:0 15px 0 13px}button.button-tertiary,input.button-tertiary{color:#007bc0;background-color:transparent;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;border-style:none}button.button-tertiary:hover,button.button-tertiary:focus-visible,input.button-tertiary:hover,input.button-tertiary:focus-visible{outline:0;color:#00629a;background-color:#d1e4ff}button.button-tertiary:active,input.button-tertiary:active{color:#004975;background-color:#9dc9ff;outline:0}button.button-tertiary:disabled,button.button-tertiary.disabled,input.button-tertiary:disabled,input.button-tertiary.disabled{color:#979ea4;background-color:transparent;cursor:default}button.button-tertiary:not(.mat-mdc-icon-button):has(mat-icon),input.button-tertiary:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-integrated,input.button-integrated{color:#000000;background-color:transparent;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0px 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto;border-style:none}button.button-integrated:hover,button.button-integrated:focus-visible,input.button-integrated:hover,input.button-integrated:focus-visible{color:#007bc0;outline:0}button.button-integrated:active,input.button-integrated:active{color:#00629a;outline:0}button.button-integrated:disabled,button.button-integrated.disabled,input.button-integrated:disabled,input.button-integrated.disabled{color:#979ea4 !important;background-color:transparent;cursor:default}button.button-integrated:not(.mat-mdc-icon-button):has(mat-icon),input.button-integrated:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-link,input.button-link{color:#007bc0;background-color:transparent;border:none;-webkit-box-shadow:none;box-shadow:none;font-family:"Bosch-Sans";font-weight:400;font-size:16px;height:48px;padding:0 16px 0 16px;display:inline-flex;justify-content:center;align-items:center;gap:8px;cursor:pointer;width:auto}button.button-link::after,input.button-link::after{position:absolute;font-family:"Bosch-Ic";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:18px;content:"\\e181";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}button.button-link:hover,button.button-link:focus-visible,input.button-link:hover,input.button-link:focus-visible{color:#00629a;text-decoration:underline;outline:0}button.button-link:active,input.button-link:active{color:#004975;text-decoration:underline}button.button-link.disabled,button.button-link[disabled],button.button-link fieldset[disabled],input.button-link.disabled,input.button-link[disabled],input.button-link fieldset[disabled]{color:#c1c7cc;text-decoration:none;-webkit-transition:none;-o-transition:none;transition:none}button.button-link.disabled::after,button.button-link[disabled]::after,button.button-link fieldset[disabled]::after,input.button-link.disabled::after,input.button-link[disabled]::after,input.button-link fieldset[disabled]::after{-webkit-transform:translate(0, 0);-ms-transform:translate(0, 0);-o-transform:translate(0, 0);transform:translate(0, 0)}button.button-link.no-arrow::after,input.button-link.no-arrow::after{content:""}button.button-link:not(.mat-mdc-icon-button):has(mat-icon),input.button-link:not(.mat-mdc-icon-button):has(mat-icon){padding:0px 16px 0px 14px}button.button-block,input[type=submit].button-block,input[type=reset].button-block,input[type=button].button-block{display:block;width:100%}/*!\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n*//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n *//*!\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */bci-year-month{width:100%;height:100%}.year-month-grid{width:100%;height:100%;padding:16px;display:flex;box-sizing:border-box;flex-direction:column;justify-content:space-between}.year-month-grid .row{width:100%;display:flex;align-items:center;justify-content:space-between}.year-month-grid .row .selection-chip{width:60px;height:30px;font-size:16px;background:transparent;display:grid;place-items:center;line-height:1.5;border:none;border-radius:15px;cursor:pointer;outline:none;user-select:none}.year-month-grid .row .selection-chip:hover,.year-month-grid .row .selection-chip:focus{background:#e0e2e5}.year-month-grid .row .selection-chip:active{background:#c1c7cc}.year-month-grid .row .selection-chip.today{border:1px solid #000000}.year-month-grid .row .selection-chip.selected{background:#007bc0;color:white;border:none}.year-month-grid .row .selection-chip.selected:hover,.year-month-grid .row .selection-chip.selected:focus{background:#00629a}.year-month-grid .row .selection-chip.selected:active{background:#004975}.year-month-grid .row .selection-chip:disabled{pointer-events:none;color:#c1c7cc}.year-month-grid .row .selection-chip:disabled.today{border-color:#c1c7cc}.year-month-grid .row .selection-chip:disabled.selected{background:#c1c7cc;color:#8a9097}';
var YEAR_PAGE_SIZE = 20;
var SELECTED_YEAR_SPOT = 9;
var YearMonthSelection = class {
  constructor(hostRef) {
    registerInstance(this, hostRef);
    this.yearMonthSelected = createEvent(this, "yearMonthSelected", 7);
    this.selectionStep = void 0;
    this.yearSelectionPageOffset = void 0;
    this.dateObj = void 0;
    this.minDate = void 0;
    this.maxDate = void 0;
    this.displayedYears = void 0;
  }
  handleSelectionStepChange(newValue, oldValue) {
    if (!oldValue && !!newValue) {
      this.displayedYears = this.getDisplayedYearsFromYear(this.dateObj.calendarDateYear);
    }
  }
  handleSelectionPageChange(newValue) {
    this.displayedYears = this.getDisplayedYearsFromYear(this.dateObj.calendarDateYear + newValue * YEAR_PAGE_SIZE);
  }
  getDisplayedYearsFromYear(year) {
    const yearsBefore = [];
    for (let i = year - SELECTED_YEAR_SPOT; i < year; i++) {
      yearsBefore.push(i);
    }
    const yearsAfter = [];
    for (let i = year + 1; i < year + (YEAR_PAGE_SIZE - SELECTED_YEAR_SPOT); i++) {
      yearsAfter.push(i);
    }
    const years = [...yearsBefore, year, ...yearsAfter];
    return this.createGrid(years, YEAR_PAGE_SIZE, 4);
  }
  get months() {
    const locale = {
      locale: LOCALE_MAP[state.language || DEFAULT_LOCALE.language]
    };
    const months = [];
    for (let i = 0; i < 12; i++) {
      months.push(format(setMonth(/* @__PURE__ */ new Date(), i), "LLL", locale));
    }
    return this.createGrid(months, 12, 3);
  }
  // use generics here because years are numbers and months are strings
  createGrid(array, gridItems, rowSize) {
    const grid = [];
    for (let i = 0; i < gridItems; i += rowSize) {
      const row = array.slice(i, i + rowSize);
      grid.push(row);
    }
    return grid;
  }
  handleMonthClick(monthIndex) {
    this.yearMonthSelected.emit({
      selectedYearMonth: monthIndex
    });
  }
  handleYearClick(year) {
    this.yearMonthSelected.emit({
      selectedYearMonth: year
    });
  }
  /**
   * Return true if the given month is outside of the min and max date of the datepicker
   * @param selectedYear current year
   * @param compareDate date to compare with (min or max date of the datepicker)
   * @param monthIndex current month
   * @param minOrMaxDate compare with min or with max date
   * @returns true or false
   */
  checkIfMonthIsInRange(selectedYear, compareDate, monthIndex, minOrMaxDate) {
    if (selectedYear === compareDate.getFullYear()) {
      return minOrMaxDate === "min" ? monthIndex < compareDate.getMonth() : monthIndex > compareDate.getMonth();
    } else {
      return false;
    }
  }
  render() {
    const selectedYear = this.dateObj.calendarDateYear;
    const selectedMonth = this.dateObj.calendarDateMonth;
    const selectionAsDate = createDate(/* @__PURE__ */ new Date(), selectedYear, selectedMonth);
    return h(Host, null, this.selectionStep === "year" ? h("div", {
      class: "year-month-grid"
    }, this.displayedYears.map((yearRow) => h("div", {
      class: "row"
    }, yearRow.map((year) => {
      const selected = isSameYear(createDate(/* @__PURE__ */ new Date(), year, 0), selectionAsDate);
      const isTodaysDate = isSameYear(createDate(/* @__PURE__ */ new Date(), year, 0), /* @__PURE__ */ new Date());
      const minDate = new Date(this.minDate);
      const maxDate = new Date(this.maxDate);
      const notInRange = year < minDate.getFullYear() || year > maxDate.getFullYear();
      const buttonClasses = {
        "selection-chip": true,
        selected,
        today: isTodaysDate
      };
      return h("button", {
        class: buttonClasses,
        "aria-label": `Select Year ${year}`,
        key: year,
        disabled: notInRange,
        onClick: () => this.handleYearClick(year)
      }, year);
    })))) : this.selectionStep === "month" ? h("div", {
      class: "year-month-grid"
    }, this.months.map((monthRow, rowI) => h("div", {
      class: "row"
    }, monthRow.map((month, monthI) => {
      const monthIndex = rowI * (this.months.length - 1) + monthI;
      const selected = isSameMonth(createDate(/* @__PURE__ */ new Date(), selectedYear, monthIndex), selectionAsDate);
      const isTodaysDate = isSameMonth(createDate(/* @__PURE__ */ new Date(), selectedYear, monthIndex), /* @__PURE__ */ new Date());
      const minDate = new Date(this.minDate);
      const maxDate = new Date(this.maxDate);
      const notInRange = this.checkIfMonthIsInRange(selectedYear, minDate, monthIndex, "min") || this.checkIfMonthIsInRange(selectedYear, maxDate, monthIndex, "max");
      const buttonClasses = {
        "selection-chip": true,
        selected,
        today: isTodaysDate
      };
      return h("button", {
        class: buttonClasses,
        "aria-label": `Select Month ${month}`,
        disabled: notInRange,
        onClick: () => this.handleMonthClick(monthIndex),
        key: month
      }, month);
    })))) : void 0);
  }
  static get watchers() {
    return {
      "selectionStep": ["handleSelectionStepChange"],
      "yearSelectionPageOffset": ["handleSelectionPageChange"]
    };
  }
};
YearMonthSelection.style = yearMonthCss;
export {
  Datepicker as bci_datepicker,
  DateTimeDialog as bci_datetime_dialog,
  DatetimePicker as bci_datetime_picker,
  MonthHeader as bci_month_header,
  TemplateList as bci_template,
  Timepicker as bci_timepicker,
  WeekHeader as bci_week_header,
  Weekdays as bci_weekdays,
  YearMonthSelection as bci_year_month
};
//# sourceMappingURL=bci-datepicker_9.entry-6KL4KAXE.js.map
