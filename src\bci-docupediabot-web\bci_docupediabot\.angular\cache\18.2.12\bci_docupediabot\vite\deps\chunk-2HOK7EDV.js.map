{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/component-655cd5b3.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { _ as __extends, a as __assign, d as __read, f as __spreadArray, M as MDCFoundation, m as matches, g as closest, b as MDCComponent, c as __values } from './ponyfill-78459bda.js';\nimport { g as getCorrectPropertyName } from './util-40cc7805.js';\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar _a, _b;\nvar cssClasses$2 = {\n  LIST_ITEM_ACTIVATED_CLASS: 'mdc-list-item--activated',\n  LIST_ITEM_CLASS: 'mdc-list-item',\n  LIST_ITEM_DISABLED_CLASS: 'mdc-list-item--disabled',\n  LIST_ITEM_SELECTED_CLASS: 'mdc-list-item--selected',\n  LIST_ITEM_TEXT_CLASS: 'mdc-list-item__text',\n  LIST_ITEM_PRIMARY_TEXT_CLASS: 'mdc-list-item__primary-text',\n  ROOT: 'mdc-list'\n};\nvar evolutionClassNameMap = (_a = {}, _a[\"\" + cssClasses$2.LIST_ITEM_ACTIVATED_CLASS] = 'mdc-list-item--activated', _a[\"\" + cssClasses$2.LIST_ITEM_CLASS] = 'mdc-list-item', _a[\"\" + cssClasses$2.LIST_ITEM_DISABLED_CLASS] = 'mdc-list-item--disabled', _a[\"\" + cssClasses$2.LIST_ITEM_SELECTED_CLASS] = 'mdc-list-item--selected', _a[\"\" + cssClasses$2.LIST_ITEM_PRIMARY_TEXT_CLASS] = 'mdc-list-item__primary-text', _a[\"\" + cssClasses$2.ROOT] = 'mdc-list', _a);\nvar deprecatedClassNameMap = (_b = {}, _b[\"\" + cssClasses$2.LIST_ITEM_ACTIVATED_CLASS] = 'mdc-deprecated-list-item--activated', _b[\"\" + cssClasses$2.LIST_ITEM_CLASS] = 'mdc-deprecated-list-item', _b[\"\" + cssClasses$2.LIST_ITEM_DISABLED_CLASS] = 'mdc-deprecated-list-item--disabled', _b[\"\" + cssClasses$2.LIST_ITEM_SELECTED_CLASS] = 'mdc-deprecated-list-item--selected', _b[\"\" + cssClasses$2.LIST_ITEM_TEXT_CLASS] = 'mdc-deprecated-list-item__text', _b[\"\" + cssClasses$2.LIST_ITEM_PRIMARY_TEXT_CLASS] = 'mdc-deprecated-list-item__primary-text', _b[\"\" + cssClasses$2.ROOT] = 'mdc-deprecated-list', _b);\nvar strings$2 = {\n  ACTION_EVENT: 'MDCList:action',\n  SELECTION_CHANGE_EVENT: 'MDCList:selectionChange',\n  ARIA_CHECKED: 'aria-checked',\n  ARIA_CHECKED_CHECKBOX_SELECTOR: '[role=\"checkbox\"][aria-checked=\"true\"]',\n  ARIA_CHECKED_RADIO_SELECTOR: '[role=\"radio\"][aria-checked=\"true\"]',\n  ARIA_CURRENT: 'aria-current',\n  ARIA_DISABLED: 'aria-disabled',\n  ARIA_ORIENTATION: 'aria-orientation',\n  ARIA_ORIENTATION_HORIZONTAL: 'horizontal',\n  ARIA_ROLE_CHECKBOX_SELECTOR: '[role=\"checkbox\"]',\n  ARIA_SELECTED: 'aria-selected',\n  ARIA_INTERACTIVE_ROLES_SELECTOR: '[role=\"listbox\"], [role=\"menu\"]',\n  ARIA_MULTI_SELECTABLE_SELECTOR: '[aria-multiselectable=\"true\"]',\n  CHECKBOX_RADIO_SELECTOR: 'input[type=\"checkbox\"], input[type=\"radio\"]',\n  CHECKBOX_SELECTOR: 'input[type=\"checkbox\"]',\n  CHILD_ELEMENTS_TO_TOGGLE_TABINDEX: \"\\n    .\" + cssClasses$2.LIST_ITEM_CLASS + \" button:not(:disabled),\\n    .\" + cssClasses$2.LIST_ITEM_CLASS + \" a,\\n    .\" + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + \" button:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + \" a\\n  \",\n  DEPRECATED_SELECTOR: '.mdc-deprecated-list',\n  FOCUSABLE_CHILD_ELEMENTS: \"\\n    .\" + cssClasses$2.LIST_ITEM_CLASS + \" button:not(:disabled),\\n    .\" + cssClasses$2.LIST_ITEM_CLASS + \" a,\\n    .\" + cssClasses$2.LIST_ITEM_CLASS + \" input[type=\\\"radio\\\"]:not(:disabled),\\n    .\" + cssClasses$2.LIST_ITEM_CLASS + \" input[type=\\\"checkbox\\\"]:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + \" button:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + \" a,\\n    .\" + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + \" input[type=\\\"radio\\\"]:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + \" input[type=\\\"checkbox\\\"]:not(:disabled)\\n  \",\n  RADIO_SELECTOR: 'input[type=\"radio\"]',\n  SELECTED_ITEM_SELECTOR: '[aria-selected=\"true\"], [aria-current=\"true\"]'\n};\nvar numbers$2 = {\n  UNSET_INDEX: -1,\n  TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS: 300\n};\nvar evolutionAttribute = 'evolution';\n\n/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * KEY provides normalized string values for keys.\n */\nvar KEY = {\n  UNKNOWN: 'Unknown',\n  BACKSPACE: 'Backspace',\n  ENTER: 'Enter',\n  SPACEBAR: 'Spacebar',\n  PAGE_UP: 'PageUp',\n  PAGE_DOWN: 'PageDown',\n  END: 'End',\n  HOME: 'Home',\n  ARROW_LEFT: 'ArrowLeft',\n  ARROW_UP: 'ArrowUp',\n  ARROW_RIGHT: 'ArrowRight',\n  ARROW_DOWN: 'ArrowDown',\n  DELETE: 'Delete',\n  ESCAPE: 'Escape',\n  TAB: 'Tab'\n};\nvar normalizedKeys = new Set();\n// IE11 has no support for new Map with iterable so we need to initialize this\n// by hand.\nnormalizedKeys.add(KEY.BACKSPACE);\nnormalizedKeys.add(KEY.ENTER);\nnormalizedKeys.add(KEY.SPACEBAR);\nnormalizedKeys.add(KEY.PAGE_UP);\nnormalizedKeys.add(KEY.PAGE_DOWN);\nnormalizedKeys.add(KEY.END);\nnormalizedKeys.add(KEY.HOME);\nnormalizedKeys.add(KEY.ARROW_LEFT);\nnormalizedKeys.add(KEY.ARROW_UP);\nnormalizedKeys.add(KEY.ARROW_RIGHT);\nnormalizedKeys.add(KEY.ARROW_DOWN);\nnormalizedKeys.add(KEY.DELETE);\nnormalizedKeys.add(KEY.ESCAPE);\nnormalizedKeys.add(KEY.TAB);\nvar KEY_CODE = {\n  BACKSPACE: 8,\n  ENTER: 13,\n  SPACEBAR: 32,\n  PAGE_UP: 33,\n  PAGE_DOWN: 34,\n  END: 35,\n  HOME: 36,\n  ARROW_LEFT: 37,\n  ARROW_UP: 38,\n  ARROW_RIGHT: 39,\n  ARROW_DOWN: 40,\n  DELETE: 46,\n  ESCAPE: 27,\n  TAB: 9\n};\nvar mappedKeyCodes = new Map();\n// IE11 has no support for new Map with iterable so we need to initialize this\n// by hand.\nmappedKeyCodes.set(KEY_CODE.BACKSPACE, KEY.BACKSPACE);\nmappedKeyCodes.set(KEY_CODE.ENTER, KEY.ENTER);\nmappedKeyCodes.set(KEY_CODE.SPACEBAR, KEY.SPACEBAR);\nmappedKeyCodes.set(KEY_CODE.PAGE_UP, KEY.PAGE_UP);\nmappedKeyCodes.set(KEY_CODE.PAGE_DOWN, KEY.PAGE_DOWN);\nmappedKeyCodes.set(KEY_CODE.END, KEY.END);\nmappedKeyCodes.set(KEY_CODE.HOME, KEY.HOME);\nmappedKeyCodes.set(KEY_CODE.ARROW_LEFT, KEY.ARROW_LEFT);\nmappedKeyCodes.set(KEY_CODE.ARROW_UP, KEY.ARROW_UP);\nmappedKeyCodes.set(KEY_CODE.ARROW_RIGHT, KEY.ARROW_RIGHT);\nmappedKeyCodes.set(KEY_CODE.ARROW_DOWN, KEY.ARROW_DOWN);\nmappedKeyCodes.set(KEY_CODE.DELETE, KEY.DELETE);\nmappedKeyCodes.set(KEY_CODE.ESCAPE, KEY.ESCAPE);\nmappedKeyCodes.set(KEY_CODE.TAB, KEY.TAB);\nvar navigationKeys = new Set();\n// IE11 has no support for new Set with iterable so we need to initialize this\n// by hand.\nnavigationKeys.add(KEY.PAGE_UP);\nnavigationKeys.add(KEY.PAGE_DOWN);\nnavigationKeys.add(KEY.END);\nnavigationKeys.add(KEY.HOME);\nnavigationKeys.add(KEY.ARROW_LEFT);\nnavigationKeys.add(KEY.ARROW_UP);\nnavigationKeys.add(KEY.ARROW_RIGHT);\nnavigationKeys.add(KEY.ARROW_DOWN);\n/**\n * normalizeKey returns the normalized string for a navigational action.\n */\nfunction normalizeKey(evt) {\n  var key = evt.key;\n  // If the event already has a normalized key, return it\n  if (normalizedKeys.has(key)) {\n    return key;\n  }\n  // tslint:disable-next-line:deprecation\n  var mappedKey = mappedKeyCodes.get(evt.keyCode);\n  if (mappedKey) {\n    return mappedKey;\n  }\n  return KEY.UNKNOWN;\n}\n\n/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar ELEMENTS_KEY_ALLOWED_IN = ['input', 'button', 'textarea', 'select'];\n/**\n * Ensures that preventDefault is only called if the containing element\n * doesn't consume the event, and it will cause an unintended scroll.\n *\n * @param evt keyboard event to be prevented.\n */\nvar preventDefaultEvent = function (evt) {\n  var target = evt.target;\n  if (!target) {\n    return;\n  }\n  var tagName = (\"\" + target.tagName).toLowerCase();\n  if (ELEMENTS_KEY_ALLOWED_IN.indexOf(tagName) === -1) {\n    evt.preventDefault();\n  }\n};\n\n/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * Initializes a state object for typeahead. Use the same reference for calls to\n * typeahead functions.\n *\n * @return The current state of the typeahead process. Each state reference\n *     represents a typeahead instance as the reference is typically mutated\n *     in-place.\n */\nfunction initState() {\n  var state = {\n    bufferClearTimeout: 0,\n    currentFirstChar: '',\n    sortedIndexCursor: 0,\n    typeaheadBuffer: ''\n  };\n  return state;\n}\n/**\n * Initializes typeahead state by indexing the current list items by primary\n * text into the sortedIndexByFirstChar data structure.\n *\n * @param listItemCount numer of items in the list\n * @param getPrimaryTextByItemIndex function that returns the primary text at a\n *     given index\n *\n * @return Map that maps the first character of the primary text to the full\n *     list text and it's index\n */\nfunction initSortedIndex(listItemCount, getPrimaryTextByItemIndex) {\n  var sortedIndexByFirstChar = new Map();\n  // Aggregate item text to index mapping\n  for (var i = 0; i < listItemCount; i++) {\n    var primaryText = getPrimaryTextByItemIndex(i).trim();\n    if (!primaryText) {\n      continue;\n    }\n    var firstChar = primaryText[0].toLowerCase();\n    if (!sortedIndexByFirstChar.has(firstChar)) {\n      sortedIndexByFirstChar.set(firstChar, []);\n    }\n    sortedIndexByFirstChar.get(firstChar).push({\n      text: primaryText.toLowerCase(),\n      index: i\n    });\n  }\n  // Sort the mapping\n  // TODO(b/157162694): Investigate replacing forEach with Map.values()\n  sortedIndexByFirstChar.forEach(function (values) {\n    values.sort(function (first, second) {\n      return first.index - second.index;\n    });\n  });\n  return sortedIndexByFirstChar;\n}\n/**\n * Given the next desired character from the user, it attempts to find the next\n * list option matching the buffer. Wraps around if at the end of options.\n *\n * @param opts Options and accessors\n *   - nextChar - the next character to match against items\n *   - sortedIndexByFirstChar - output of `initSortedIndex(...)`\n *   - focusedItemIndex - the index of the currently focused item\n *   - focusItemAtIndex - function that focuses a list item at given index\n *   - skipFocus - whether or not to focus the matched item\n *   - isItemAtIndexDisabled - function that determines whether an item at a\n *        given index is disabled\n * @param state The typeahead state instance. See `initState`.\n *\n * @return The index of the matched item, or -1 if no match.\n */\nfunction matchItem(opts, state) {\n  var nextChar = opts.nextChar,\n    focusItemAtIndex = opts.focusItemAtIndex,\n    sortedIndexByFirstChar = opts.sortedIndexByFirstChar,\n    focusedItemIndex = opts.focusedItemIndex,\n    skipFocus = opts.skipFocus,\n    isItemAtIndexDisabled = opts.isItemAtIndexDisabled;\n  clearTimeout(state.bufferClearTimeout);\n  state.bufferClearTimeout = setTimeout(function () {\n    clearBuffer(state);\n  }, numbers$2.TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS);\n  state.typeaheadBuffer = state.typeaheadBuffer + nextChar;\n  var index;\n  if (state.typeaheadBuffer.length === 1) {\n    index = matchFirstChar(sortedIndexByFirstChar, focusedItemIndex, isItemAtIndexDisabled, state);\n  } else {\n    index = matchAllChars(sortedIndexByFirstChar, isItemAtIndexDisabled, state);\n  }\n  if (index !== -1 && !skipFocus) {\n    focusItemAtIndex(index);\n  }\n  return index;\n}\n/**\n * Matches the user's single input character in the buffer to the\n * next option that begins with such character. Wraps around if at\n * end of options. Returns -1 if no match is found.\n */\nfunction matchFirstChar(sortedIndexByFirstChar, focusedItemIndex, isItemAtIndexDisabled, state) {\n  var firstChar = state.typeaheadBuffer[0];\n  var itemsMatchingFirstChar = sortedIndexByFirstChar.get(firstChar);\n  if (!itemsMatchingFirstChar) {\n    return -1;\n  }\n  // Has the same firstChar been recently matched?\n  // Also, did starting index remain the same between key presses?\n  // If both hold true, simply increment index.\n  if (firstChar === state.currentFirstChar && itemsMatchingFirstChar[state.sortedIndexCursor].index === focusedItemIndex) {\n    state.sortedIndexCursor = (state.sortedIndexCursor + 1) % itemsMatchingFirstChar.length;\n    var newIndex = itemsMatchingFirstChar[state.sortedIndexCursor].index;\n    if (!isItemAtIndexDisabled(newIndex)) {\n      return newIndex;\n    }\n  }\n  // If we're here, it means one of the following happened:\n  // - either firstChar or startingIndex has changed, invalidating the\n  // cursor.\n  // - The next item of typeahead is disabled, so we have to look further.\n  state.currentFirstChar = firstChar;\n  var newCursorPosition = -1;\n  var cursorPosition;\n  // Find the first non-disabled item as a fallback.\n  for (cursorPosition = 0; cursorPosition < itemsMatchingFirstChar.length; cursorPosition++) {\n    if (!isItemAtIndexDisabled(itemsMatchingFirstChar[cursorPosition].index)) {\n      newCursorPosition = cursorPosition;\n      break;\n    }\n  }\n  // Advance cursor to first item matching the firstChar that is positioned\n  // after starting item. Cursor is unchanged from fallback if there's no\n  // such item.\n  for (; cursorPosition < itemsMatchingFirstChar.length; cursorPosition++) {\n    if (itemsMatchingFirstChar[cursorPosition].index > focusedItemIndex && !isItemAtIndexDisabled(itemsMatchingFirstChar[cursorPosition].index)) {\n      newCursorPosition = cursorPosition;\n      break;\n    }\n  }\n  if (newCursorPosition !== -1) {\n    state.sortedIndexCursor = newCursorPosition;\n    return itemsMatchingFirstChar[state.sortedIndexCursor].index;\n  }\n  return -1;\n}\n/**\n * Attempts to find the next item that matches all of the typeahead buffer.\n * Wraps around if at end of options. Returns -1 if no match is found.\n */\nfunction matchAllChars(sortedIndexByFirstChar, isItemAtIndexDisabled, state) {\n  var firstChar = state.typeaheadBuffer[0];\n  var itemsMatchingFirstChar = sortedIndexByFirstChar.get(firstChar);\n  if (!itemsMatchingFirstChar) {\n    return -1;\n  }\n  // Do nothing if text already matches\n  var startingItem = itemsMatchingFirstChar[state.sortedIndexCursor];\n  if (startingItem.text.lastIndexOf(state.typeaheadBuffer, 0) === 0 && !isItemAtIndexDisabled(startingItem.index)) {\n    return startingItem.index;\n  }\n  // Find next item that matches completely; if no match, we'll eventually\n  // loop around to same position\n  var cursorPosition = (state.sortedIndexCursor + 1) % itemsMatchingFirstChar.length;\n  var nextCursorPosition = -1;\n  while (cursorPosition !== state.sortedIndexCursor) {\n    var currentItem = itemsMatchingFirstChar[cursorPosition];\n    var matches = currentItem.text.lastIndexOf(state.typeaheadBuffer, 0) === 0;\n    var isEnabled = !isItemAtIndexDisabled(currentItem.index);\n    if (matches && isEnabled) {\n      nextCursorPosition = cursorPosition;\n      break;\n    }\n    cursorPosition = (cursorPosition + 1) % itemsMatchingFirstChar.length;\n  }\n  if (nextCursorPosition !== -1) {\n    state.sortedIndexCursor = nextCursorPosition;\n    return itemsMatchingFirstChar[state.sortedIndexCursor].index;\n  }\n  return -1;\n}\n/**\n * Whether or not the given typeahead instaance state is currently typing.\n *\n * @param state The typeahead state instance. See `initState`.\n */\nfunction isTypingInProgress(state) {\n  return state.typeaheadBuffer.length > 0;\n}\n/**\n * Clears the typeahaed buffer so that it resets item matching to the first\n * character.\n *\n * @param state The typeahead state instance. See `initState`.\n */\nfunction clearBuffer(state) {\n  state.typeaheadBuffer = '';\n}\n/**\n * Given a keydown event, it calculates whether or not to automatically focus a\n * list item depending on what was typed mimicing the typeahead functionality of\n * a standard <select> element that is open.\n *\n * @param opts Options and accessors\n *   - event - the KeyboardEvent to handle and parse\n *   - sortedIndexByFirstChar - output of `initSortedIndex(...)`\n *   - focusedItemIndex - the index of the currently focused item\n *   - focusItemAtIndex - function that focuses a list item at given index\n *   - isItemAtFocusedIndexDisabled - whether or not the currently focused item\n *      is disabled\n *   - isTargetListItem - whether or not the event target is a list item\n * @param state The typeahead state instance. See `initState`.\n *\n * @returns index of the item matched by the keydown. -1 if not matched.\n */\nfunction handleKeydown(opts, state) {\n  var event = opts.event,\n    isTargetListItem = opts.isTargetListItem,\n    focusedItemIndex = opts.focusedItemIndex,\n    focusItemAtIndex = opts.focusItemAtIndex,\n    sortedIndexByFirstChar = opts.sortedIndexByFirstChar,\n    isItemAtIndexDisabled = opts.isItemAtIndexDisabled;\n  var isArrowLeft = normalizeKey(event) === 'ArrowLeft';\n  var isArrowUp = normalizeKey(event) === 'ArrowUp';\n  var isArrowRight = normalizeKey(event) === 'ArrowRight';\n  var isArrowDown = normalizeKey(event) === 'ArrowDown';\n  var isHome = normalizeKey(event) === 'Home';\n  var isEnd = normalizeKey(event) === 'End';\n  var isEnter = normalizeKey(event) === 'Enter';\n  var isSpace = normalizeKey(event) === 'Spacebar';\n  if (event.altKey || event.ctrlKey || event.metaKey || isArrowLeft || isArrowUp || isArrowRight || isArrowDown || isHome || isEnd || isEnter) {\n    return -1;\n  }\n  var isCharacterKey = !isSpace && event.key.length === 1;\n  if (isCharacterKey) {\n    preventDefaultEvent(event);\n    var matchItemOpts = {\n      focusItemAtIndex: focusItemAtIndex,\n      focusedItemIndex: focusedItemIndex,\n      nextChar: event.key.toLowerCase(),\n      sortedIndexByFirstChar: sortedIndexByFirstChar,\n      skipFocus: false,\n      isItemAtIndexDisabled: isItemAtIndexDisabled\n    };\n    return matchItem(matchItemOpts, state);\n  }\n  if (!isSpace) {\n    return -1;\n  }\n  if (isTargetListItem) {\n    preventDefaultEvent(event);\n  }\n  var typeaheadOnListItem = isTargetListItem && isTypingInProgress(state);\n  if (typeaheadOnListItem) {\n    var matchItemOpts = {\n      focusItemAtIndex: focusItemAtIndex,\n      focusedItemIndex: focusedItemIndex,\n      nextChar: ' ',\n      sortedIndexByFirstChar: sortedIndexByFirstChar,\n      skipFocus: false,\n      isItemAtIndexDisabled: isItemAtIndexDisabled\n    };\n    // space participates in typeahead matching if in rapid typing mode\n    return matchItem(matchItemOpts, state);\n  }\n  return -1;\n}\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nfunction isNumberArray(selectedIndex) {\n  return selectedIndex instanceof Array;\n}\n/** List of modifier keys to consider while handling keyboard events. */\nvar handledModifierKeys = ['Alt', 'Control', 'Meta', 'Shift'];\n/** Checks if the event has the given modifier keys. */\nfunction createModifierChecker(event) {\n  var eventModifiers = new Set(event ? handledModifierKeys.filter(function (m) {\n    return event.getModifierState(m);\n  }) : []);\n  return function (modifiers) {\n    return modifiers.every(function (m) {\n      return eventModifiers.has(m);\n    }) && modifiers.length === eventModifiers.size;\n  };\n}\nvar MDCListFoundation = /** @class */function (_super) {\n  __extends(MDCListFoundation, _super);\n  function MDCListFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCListFoundation.defaultAdapter), adapter)) || this;\n    _this.wrapFocus = false;\n    _this.isVertical = true;\n    _this.isSingleSelectionList = false;\n    _this.areDisabledItemsFocusable = true;\n    _this.selectedIndex = numbers$2.UNSET_INDEX;\n    _this.focusedItemIndex = numbers$2.UNSET_INDEX;\n    _this.useActivatedClass = false;\n    _this.useSelectedAttr = false;\n    _this.ariaCurrentAttrValue = null;\n    _this.isCheckboxList = false;\n    _this.isRadioList = false;\n    _this.lastSelectedIndex = null;\n    _this.hasTypeahead = false;\n    // Transiently holds current typeahead prefix from user.\n    _this.typeaheadState = initState();\n    _this.sortedIndexByFirstChar = new Map();\n    return _this;\n  }\n  Object.defineProperty(MDCListFoundation, \"strings\", {\n    get: function () {\n      return strings$2;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCListFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses$2;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCListFoundation, \"numbers\", {\n    get: function () {\n      return numbers$2;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCListFoundation, \"defaultAdapter\", {\n    get: function () {\n      return {\n        addClassForElementIndex: function () {\n          return undefined;\n        },\n        focusItemAtIndex: function () {\n          return undefined;\n        },\n        getAttributeForElementIndex: function () {\n          return null;\n        },\n        getFocusedElementIndex: function () {\n          return 0;\n        },\n        getListItemCount: function () {\n          return 0;\n        },\n        hasCheckboxAtIndex: function () {\n          return false;\n        },\n        hasRadioAtIndex: function () {\n          return false;\n        },\n        isCheckboxCheckedAtIndex: function () {\n          return false;\n        },\n        isFocusInsideList: function () {\n          return false;\n        },\n        isRootFocused: function () {\n          return false;\n        },\n        listItemAtIndexHasClass: function () {\n          return false;\n        },\n        notifyAction: function () {\n          return undefined;\n        },\n        notifySelectionChange: function () {},\n        removeClassForElementIndex: function () {\n          return undefined;\n        },\n        setAttributeForElementIndex: function () {\n          return undefined;\n        },\n        setCheckedCheckboxOrRadioAtIndex: function () {\n          return undefined;\n        },\n        setTabIndexForListItemChildren: function () {\n          return undefined;\n        },\n        getPrimaryTextAtIndex: function () {\n          return '';\n        }\n      };\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCListFoundation.prototype.layout = function () {\n    if (this.adapter.getListItemCount() === 0) {\n      return;\n    }\n    // TODO(b/172274142): consider all items when determining the list's type.\n    if (this.adapter.hasCheckboxAtIndex(0)) {\n      this.isCheckboxList = true;\n    } else if (this.adapter.hasRadioAtIndex(0)) {\n      this.isRadioList = true;\n    } else {\n      this.maybeInitializeSingleSelection();\n    }\n    if (this.hasTypeahead) {\n      this.sortedIndexByFirstChar = this.typeaheadInitSortedIndex();\n    }\n  };\n  /** Returns the index of the item that was last focused. */\n  MDCListFoundation.prototype.getFocusedItemIndex = function () {\n    return this.focusedItemIndex;\n  };\n  /** Toggles focus wrapping with keyboard navigation. */\n  MDCListFoundation.prototype.setWrapFocus = function (value) {\n    this.wrapFocus = value;\n  };\n  /**\n   * Toggles orientation direction for keyboard navigation (true for vertical,\n   * false for horizontal).\n   */\n  MDCListFoundation.prototype.setVerticalOrientation = function (value) {\n    this.isVertical = value;\n  };\n  /** Toggles single-selection behavior. */\n  MDCListFoundation.prototype.setSingleSelection = function (value) {\n    this.isSingleSelectionList = value;\n    if (value) {\n      this.maybeInitializeSingleSelection();\n      this.selectedIndex = this.getSelectedIndexFromDOM();\n    }\n  };\n  MDCListFoundation.prototype.setDisabledItemsFocusable = function (value) {\n    this.areDisabledItemsFocusable = value;\n  };\n  /**\n   * Automatically determines whether the list is single selection list. If so,\n   * initializes the internal state to match the selected item.\n   */\n  MDCListFoundation.prototype.maybeInitializeSingleSelection = function () {\n    var selectedItemIndex = this.getSelectedIndexFromDOM();\n    if (selectedItemIndex === numbers$2.UNSET_INDEX) return;\n    var hasActivatedClass = this.adapter.listItemAtIndexHasClass(selectedItemIndex, cssClasses$2.LIST_ITEM_ACTIVATED_CLASS);\n    if (hasActivatedClass) {\n      this.setUseActivatedClass(true);\n    }\n    this.isSingleSelectionList = true;\n    this.selectedIndex = selectedItemIndex;\n  };\n  /** @return Index of the first selected item based on the DOM state. */\n  MDCListFoundation.prototype.getSelectedIndexFromDOM = function () {\n    var selectedIndex = numbers$2.UNSET_INDEX;\n    var listItemsCount = this.adapter.getListItemCount();\n    for (var i = 0; i < listItemsCount; i++) {\n      var hasSelectedClass = this.adapter.listItemAtIndexHasClass(i, cssClasses$2.LIST_ITEM_SELECTED_CLASS);\n      var hasActivatedClass = this.adapter.listItemAtIndexHasClass(i, cssClasses$2.LIST_ITEM_ACTIVATED_CLASS);\n      if (!(hasSelectedClass || hasActivatedClass)) {\n        continue;\n      }\n      selectedIndex = i;\n      break;\n    }\n    return selectedIndex;\n  };\n  /**\n   * Sets whether typeahead is enabled on the list.\n   * @param hasTypeahead Whether typeahead is enabled.\n   */\n  MDCListFoundation.prototype.setHasTypeahead = function (hasTypeahead) {\n    this.hasTypeahead = hasTypeahead;\n    if (hasTypeahead) {\n      this.sortedIndexByFirstChar = this.typeaheadInitSortedIndex();\n    }\n  };\n  /**\n   * @return Whether typeahead is currently matching a user-specified prefix.\n   */\n  MDCListFoundation.prototype.isTypeaheadInProgress = function () {\n    return this.hasTypeahead && isTypingInProgress(this.typeaheadState);\n  };\n  /** Toggle use of the \"activated\" CSS class. */\n  MDCListFoundation.prototype.setUseActivatedClass = function (useActivated) {\n    this.useActivatedClass = useActivated;\n  };\n  /**\n   * Toggles use of the selected attribute (true for aria-selected, false for\n   * aria-checked).\n   */\n  MDCListFoundation.prototype.setUseSelectedAttribute = function (useSelected) {\n    this.useSelectedAttr = useSelected;\n  };\n  MDCListFoundation.prototype.getSelectedIndex = function () {\n    return this.selectedIndex;\n  };\n  MDCListFoundation.prototype.setSelectedIndex = function (index, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    if (!this.isIndexValid(index)) {\n      return;\n    }\n    if (this.isCheckboxList) {\n      this.setCheckboxAtIndex(index, options);\n    } else if (this.isRadioList) {\n      this.setRadioAtIndex(index, options);\n    } else {\n      this.setSingleSelectionAtIndex(index, options);\n    }\n  };\n  /**\n   * Focus in handler for the list items.\n   */\n  MDCListFoundation.prototype.handleFocusIn = function (listItemIndex) {\n    if (listItemIndex >= 0) {\n      this.focusedItemIndex = listItemIndex;\n      this.adapter.setAttributeForElementIndex(listItemIndex, 'tabindex', '0');\n      this.adapter.setTabIndexForListItemChildren(listItemIndex, '0');\n    }\n  };\n  /**\n   * Focus out handler for the list items.\n   */\n  MDCListFoundation.prototype.handleFocusOut = function (listItemIndex) {\n    var _this = this;\n    if (listItemIndex >= 0) {\n      this.adapter.setAttributeForElementIndex(listItemIndex, 'tabindex', '-1');\n      this.adapter.setTabIndexForListItemChildren(listItemIndex, '-1');\n    }\n    /**\n     * Between Focusout & Focusin some browsers do not have focus on any\n     * element. Setting a delay to wait till the focus is moved to next element.\n     */\n    setTimeout(function () {\n      if (!_this.adapter.isFocusInsideList()) {\n        _this.setTabindexToFirstSelectedOrFocusedItem();\n      }\n    }, 0);\n  };\n  MDCListFoundation.prototype.isIndexDisabled = function (index) {\n    return this.adapter.listItemAtIndexHasClass(index, cssClasses$2.LIST_ITEM_DISABLED_CLASS);\n  };\n  /**\n   * Key handler for the list.\n   */\n  MDCListFoundation.prototype.handleKeydown = function (event, isRootListItem, listItemIndex) {\n    var _this = this;\n    var _a;\n    var isArrowLeft = normalizeKey(event) === 'ArrowLeft';\n    var isArrowUp = normalizeKey(event) === 'ArrowUp';\n    var isArrowRight = normalizeKey(event) === 'ArrowRight';\n    var isArrowDown = normalizeKey(event) === 'ArrowDown';\n    var isHome = normalizeKey(event) === 'Home';\n    var isEnd = normalizeKey(event) === 'End';\n    var isEnter = normalizeKey(event) === 'Enter';\n    var isSpace = normalizeKey(event) === 'Spacebar';\n    // The keys for forward and back differ based on list orientation.\n    var isForward = this.isVertical && isArrowDown || !this.isVertical && isArrowRight;\n    var isBack = this.isVertical && isArrowUp || !this.isVertical && isArrowLeft;\n    // Have to check both upper and lower case, because having caps lock on\n    // affects the value.\n    var isLetterA = event.key === 'A' || event.key === 'a';\n    var eventHasModifiers = createModifierChecker(event);\n    if (this.adapter.isRootFocused()) {\n      if ((isBack || isEnd) && eventHasModifiers([])) {\n        event.preventDefault();\n        this.focusLastElement();\n      } else if ((isForward || isHome) && eventHasModifiers([])) {\n        event.preventDefault();\n        this.focusFirstElement();\n      } else if (isBack && eventHasModifiers(['Shift']) && this.isCheckboxList) {\n        event.preventDefault();\n        var focusedIndex = this.focusLastElement();\n        if (focusedIndex !== -1) {\n          this.setSelectedIndexOnAction(focusedIndex, false);\n        }\n      } else if (isForward && eventHasModifiers(['Shift']) && this.isCheckboxList) {\n        event.preventDefault();\n        var focusedIndex = this.focusFirstElement();\n        if (focusedIndex !== -1) {\n          this.setSelectedIndexOnAction(focusedIndex, false);\n        }\n      }\n      if (this.hasTypeahead) {\n        var handleKeydownOpts = {\n          event: event,\n          focusItemAtIndex: function (index) {\n            _this.focusItemAtIndex(index);\n          },\n          focusedItemIndex: -1,\n          isTargetListItem: isRootListItem,\n          sortedIndexByFirstChar: this.sortedIndexByFirstChar,\n          isItemAtIndexDisabled: function (index) {\n            return _this.isIndexDisabled(index);\n          }\n        };\n        handleKeydown(handleKeydownOpts, this.typeaheadState);\n      }\n      return;\n    }\n    var currentIndex = this.adapter.getFocusedElementIndex();\n    if (currentIndex === -1) {\n      currentIndex = listItemIndex;\n      if (currentIndex < 0) {\n        // If this event doesn't have a mdc-list-item ancestor from the\n        // current list (not from a sublist), return early.\n        return;\n      }\n    }\n    if (isForward && eventHasModifiers([])) {\n      preventDefaultEvent(event);\n      this.focusNextElement(currentIndex);\n    } else if (isBack && eventHasModifiers([])) {\n      preventDefaultEvent(event);\n      this.focusPrevElement(currentIndex);\n    } else if (isForward && eventHasModifiers(['Shift']) && this.isCheckboxList) {\n      preventDefaultEvent(event);\n      var focusedIndex = this.focusNextElement(currentIndex);\n      if (focusedIndex !== -1) {\n        this.setSelectedIndexOnAction(focusedIndex, false);\n      }\n    } else if (isBack && eventHasModifiers(['Shift']) && this.isCheckboxList) {\n      preventDefaultEvent(event);\n      var focusedIndex = this.focusPrevElement(currentIndex);\n      if (focusedIndex !== -1) {\n        this.setSelectedIndexOnAction(focusedIndex, false);\n      }\n    } else if (isHome && eventHasModifiers([])) {\n      preventDefaultEvent(event);\n      this.focusFirstElement();\n    } else if (isEnd && eventHasModifiers([])) {\n      preventDefaultEvent(event);\n      this.focusLastElement();\n    } else if (isHome && eventHasModifiers(['Control', 'Shift']) && this.isCheckboxList) {\n      preventDefaultEvent(event);\n      if (this.isIndexDisabled(currentIndex)) {\n        return;\n      }\n      this.focusFirstElement();\n      this.toggleCheckboxRange(0, currentIndex, currentIndex);\n    } else if (isEnd && eventHasModifiers(['Control', 'Shift']) && this.isCheckboxList) {\n      preventDefaultEvent(event);\n      if (this.isIndexDisabled(currentIndex)) {\n        return;\n      }\n      this.focusLastElement();\n      this.toggleCheckboxRange(currentIndex, this.adapter.getListItemCount() - 1, currentIndex);\n    } else if (isLetterA && eventHasModifiers(['Control']) && this.isCheckboxList) {\n      event.preventDefault();\n      this.checkboxListToggleAll(this.selectedIndex === numbers$2.UNSET_INDEX ? [] : this.selectedIndex, true);\n    } else if ((isEnter || isSpace) && eventHasModifiers([])) {\n      if (isRootListItem) {\n        // Return early if enter key is pressed on anchor element which triggers\n        // synthetic MouseEvent event.\n        var target = event.target;\n        if (target && target.tagName === 'A' && isEnter) {\n          return;\n        }\n        preventDefaultEvent(event);\n        if (this.isIndexDisabled(currentIndex)) {\n          return;\n        }\n        if (!this.isTypeaheadInProgress()) {\n          if (this.isSelectableList()) {\n            this.setSelectedIndexOnAction(currentIndex, false);\n          }\n          this.adapter.notifyAction(currentIndex);\n        }\n      }\n    } else if ((isEnter || isSpace) && eventHasModifiers(['Shift']) && this.isCheckboxList) {\n      // Return early if enter key is pressed on anchor element which triggers\n      // synthetic MouseEvent event.\n      var target = event.target;\n      if (target && target.tagName === 'A' && isEnter) {\n        return;\n      }\n      preventDefaultEvent(event);\n      if (this.isIndexDisabled(currentIndex)) {\n        return;\n      }\n      if (!this.isTypeaheadInProgress()) {\n        this.toggleCheckboxRange((_a = this.lastSelectedIndex) !== null && _a !== void 0 ? _a : currentIndex, currentIndex, currentIndex);\n        this.adapter.notifyAction(currentIndex);\n      }\n    }\n    if (this.hasTypeahead) {\n      var handleKeydownOpts = {\n        event: event,\n        focusItemAtIndex: function (index) {\n          _this.focusItemAtIndex(index);\n        },\n        focusedItemIndex: this.focusedItemIndex,\n        isTargetListItem: isRootListItem,\n        sortedIndexByFirstChar: this.sortedIndexByFirstChar,\n        isItemAtIndexDisabled: function (index) {\n          return _this.isIndexDisabled(index);\n        }\n      };\n      handleKeydown(handleKeydownOpts, this.typeaheadState);\n    }\n  };\n  /**\n   * Click handler for the list.\n   *\n   * @param index Index for the item that has been clicked.\n   * @param isCheckboxAlreadyUpdatedInAdapter Whether the checkbox for\n   *   the list item has already been updated in the adapter. This attribute\n   *   should be set to `true` when e.g. the click event directly landed on\n   *   the underlying native checkbox element which would cause the checked\n   *   state to be already toggled within `adapter.isCheckboxCheckedAtIndex`.\n   */\n  MDCListFoundation.prototype.handleClick = function (index, isCheckboxAlreadyUpdatedInAdapter, event) {\n    var _a;\n    var eventHasModifiers = createModifierChecker(event);\n    if (index === numbers$2.UNSET_INDEX) {\n      return;\n    }\n    if (this.isIndexDisabled(index)) {\n      return;\n    }\n    if (eventHasModifiers([])) {\n      if (this.isSelectableList()) {\n        this.setSelectedIndexOnAction(index, isCheckboxAlreadyUpdatedInAdapter);\n      }\n      this.adapter.notifyAction(index);\n    } else if (this.isCheckboxList && eventHasModifiers(['Shift'])) {\n      this.toggleCheckboxRange((_a = this.lastSelectedIndex) !== null && _a !== void 0 ? _a : index, index, index);\n      this.adapter.notifyAction(index);\n    }\n  };\n  /**\n   * Focuses the next element on the list.\n   */\n  MDCListFoundation.prototype.focusNextElement = function (index) {\n    var count = this.adapter.getListItemCount();\n    var nextIndex = index;\n    var firstChecked = null;\n    do {\n      nextIndex++;\n      if (nextIndex >= count) {\n        if (this.wrapFocus) {\n          nextIndex = 0;\n        } else {\n          // Return early because last item is already focused.\n          return index;\n        }\n      }\n      if (nextIndex === firstChecked) {\n        return -1;\n      }\n      firstChecked = firstChecked !== null && firstChecked !== void 0 ? firstChecked : nextIndex;\n    } while (!this.areDisabledItemsFocusable && this.isIndexDisabled(nextIndex));\n    this.focusItemAtIndex(nextIndex);\n    return nextIndex;\n  };\n  /**\n   * Focuses the previous element on the list.\n   */\n  MDCListFoundation.prototype.focusPrevElement = function (index) {\n    var count = this.adapter.getListItemCount();\n    var prevIndex = index;\n    var firstChecked = null;\n    do {\n      prevIndex--;\n      if (prevIndex < 0) {\n        if (this.wrapFocus) {\n          prevIndex = count - 1;\n        } else {\n          // Return early because first item is already focused.\n          return index;\n        }\n      }\n      if (prevIndex === firstChecked) {\n        return -1;\n      }\n      firstChecked = firstChecked !== null && firstChecked !== void 0 ? firstChecked : prevIndex;\n    } while (!this.areDisabledItemsFocusable && this.isIndexDisabled(prevIndex));\n    this.focusItemAtIndex(prevIndex);\n    return prevIndex;\n  };\n  MDCListFoundation.prototype.focusFirstElement = function () {\n    // Pass -1 to `focusNextElement`, since it will incremement to 0 and focus\n    // the first element.\n    return this.focusNextElement(-1);\n  };\n  MDCListFoundation.prototype.focusLastElement = function () {\n    // Pass the length of the list to `focusNextElement` since it will decrement\n    // to length - 1 and focus the last element.\n    return this.focusPrevElement(this.adapter.getListItemCount());\n  };\n  MDCListFoundation.prototype.focusInitialElement = function () {\n    var initialIndex = this.getFirstSelectedOrFocusedItemIndex();\n    this.focusItemAtIndex(initialIndex);\n    return initialIndex;\n  };\n  /**\n   * @param itemIndex Index of the list item\n   * @param isEnabled Sets the list item to enabled or disabled.\n   */\n  MDCListFoundation.prototype.setEnabled = function (itemIndex, isEnabled) {\n    if (!this.isIndexValid(itemIndex, false)) {\n      return;\n    }\n    if (isEnabled) {\n      this.adapter.removeClassForElementIndex(itemIndex, cssClasses$2.LIST_ITEM_DISABLED_CLASS);\n      this.adapter.setAttributeForElementIndex(itemIndex, strings$2.ARIA_DISABLED, 'false');\n    } else {\n      this.adapter.addClassForElementIndex(itemIndex, cssClasses$2.LIST_ITEM_DISABLED_CLASS);\n      this.adapter.setAttributeForElementIndex(itemIndex, strings$2.ARIA_DISABLED, 'true');\n    }\n  };\n  MDCListFoundation.prototype.setSingleSelectionAtIndex = function (index, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    if (this.selectedIndex === index && !options.forceUpdate) {\n      return;\n    }\n    var selectedClassName = cssClasses$2.LIST_ITEM_SELECTED_CLASS;\n    if (this.useActivatedClass) {\n      selectedClassName = cssClasses$2.LIST_ITEM_ACTIVATED_CLASS;\n    }\n    if (this.selectedIndex !== numbers$2.UNSET_INDEX) {\n      this.adapter.removeClassForElementIndex(this.selectedIndex, selectedClassName);\n    }\n    this.setAriaForSingleSelectionAtIndex(index);\n    this.setTabindexAtIndex(index);\n    if (index !== numbers$2.UNSET_INDEX) {\n      this.adapter.addClassForElementIndex(index, selectedClassName);\n    }\n    this.selectedIndex = index;\n    // If the selected value has changed through user interaction,\n    // we want to notify the selection change to the adapter.\n    if (options.isUserInteraction && !options.forceUpdate) {\n      this.adapter.notifySelectionChange([index]);\n    }\n  };\n  /**\n   * Sets aria attribute for single selection at given index.\n   */\n  MDCListFoundation.prototype.setAriaForSingleSelectionAtIndex = function (index) {\n    // Detect the presence of aria-current and get the value only during list\n    // initialization when it is in unset state.\n    if (this.selectedIndex === numbers$2.UNSET_INDEX) {\n      this.ariaCurrentAttrValue = this.adapter.getAttributeForElementIndex(index, strings$2.ARIA_CURRENT);\n    }\n    var isAriaCurrent = this.ariaCurrentAttrValue !== null;\n    var ariaAttribute = isAriaCurrent ? strings$2.ARIA_CURRENT : strings$2.ARIA_SELECTED;\n    if (this.selectedIndex !== numbers$2.UNSET_INDEX) {\n      this.adapter.setAttributeForElementIndex(this.selectedIndex, ariaAttribute, 'false');\n    }\n    if (index !== numbers$2.UNSET_INDEX) {\n      var ariaAttributeValue = isAriaCurrent ? this.ariaCurrentAttrValue : 'true';\n      this.adapter.setAttributeForElementIndex(index, ariaAttribute, ariaAttributeValue);\n    }\n  };\n  /**\n   * Returns the attribute to use for indicating selection status.\n   */\n  MDCListFoundation.prototype.getSelectionAttribute = function () {\n    return this.useSelectedAttr ? strings$2.ARIA_SELECTED : strings$2.ARIA_CHECKED;\n  };\n  /**\n   * Toggles radio at give index. Radio doesn't change the checked state if it\n   * is already checked.\n   */\n  MDCListFoundation.prototype.setRadioAtIndex = function (index, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var selectionAttribute = this.getSelectionAttribute();\n    this.adapter.setCheckedCheckboxOrRadioAtIndex(index, true);\n    if (this.selectedIndex === index && !options.forceUpdate) {\n      return;\n    }\n    if (this.selectedIndex !== numbers$2.UNSET_INDEX) {\n      this.adapter.setAttributeForElementIndex(this.selectedIndex, selectionAttribute, 'false');\n    }\n    this.adapter.setAttributeForElementIndex(index, selectionAttribute, 'true');\n    this.selectedIndex = index;\n    // If the selected value has changed through user interaction,\n    // we want to notify the selection change to the adapter.\n    if (options.isUserInteraction && !options.forceUpdate) {\n      this.adapter.notifySelectionChange([index]);\n    }\n  };\n  MDCListFoundation.prototype.setCheckboxAtIndex = function (index, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var currentIndex = this.selectedIndex;\n    // If this update is not triggered by a user interaction, we do not\n    // need to know about the currently selected indices and can avoid\n    // constructing the `Set` for performance reasons.\n    var currentlySelected = options.isUserInteraction ? new Set(currentIndex === numbers$2.UNSET_INDEX ? [] : currentIndex) : null;\n    var selectionAttribute = this.getSelectionAttribute();\n    var changedIndices = [];\n    for (var i = 0; i < this.adapter.getListItemCount(); i++) {\n      var previousIsChecked = currentlySelected === null || currentlySelected === void 0 ? void 0 : currentlySelected.has(i);\n      var newIsChecked = index.indexOf(i) >= 0;\n      // If the selection has changed for this item, we keep track of it\n      // so that we can notify the adapter.\n      if (newIsChecked !== previousIsChecked) {\n        changedIndices.push(i);\n      }\n      this.adapter.setCheckedCheckboxOrRadioAtIndex(i, newIsChecked);\n      this.adapter.setAttributeForElementIndex(i, selectionAttribute, newIsChecked ? 'true' : 'false');\n    }\n    this.selectedIndex = index;\n    // If the selected value has changed through user interaction,\n    // we want to notify the selection change to the adapter.\n    if (options.isUserInteraction && changedIndices.length) {\n      this.adapter.notifySelectionChange(changedIndices);\n    }\n  };\n  /**\n   * Toggles the state of all checkboxes in the given range (inclusive) based on\n   * the state of the checkbox at the `toggleIndex`. To determine whether to set\n   * the given range to checked or unchecked, read the value of the checkbox at\n   * the `toggleIndex` and negate it. Then apply that new checked state to all\n   * checkboxes in the range.\n   * @param fromIndex The start of the range of checkboxes to toggle\n   * @param toIndex The end of the range of checkboxes to toggle\n   * @param toggleIndex The index that will be used to determine the new state\n   *     of the given checkbox range.\n   */\n  MDCListFoundation.prototype.toggleCheckboxRange = function (fromIndex, toIndex, toggleIndex) {\n    this.lastSelectedIndex = toggleIndex;\n    var currentlySelected = new Set(this.selectedIndex === numbers$2.UNSET_INDEX ? [] : this.selectedIndex);\n    var newIsChecked = !(currentlySelected === null || currentlySelected === void 0 ? void 0 : currentlySelected.has(toggleIndex));\n    var _a = __read([fromIndex, toIndex].sort(), 2),\n      startIndex = _a[0],\n      endIndex = _a[1];\n    var selectionAttribute = this.getSelectionAttribute();\n    var changedIndices = [];\n    for (var i = startIndex; i <= endIndex; i++) {\n      if (this.isIndexDisabled(i)) {\n        continue;\n      }\n      var previousIsChecked = currentlySelected.has(i);\n      // If the selection has changed for this item, we keep track of it\n      // so that we can notify the adapter.\n      if (newIsChecked !== previousIsChecked) {\n        changedIndices.push(i);\n        this.adapter.setCheckedCheckboxOrRadioAtIndex(i, newIsChecked);\n        this.adapter.setAttributeForElementIndex(i, selectionAttribute, \"\" + newIsChecked);\n        if (newIsChecked) {\n          currentlySelected.add(i);\n        } else {\n          currentlySelected.delete(i);\n        }\n      }\n    }\n    // If the selected value has changed, update and notify the selection change\n    // to the adapter.\n    if (changedIndices.length) {\n      this.selectedIndex = __spreadArray([], __read(currentlySelected));\n      this.adapter.notifySelectionChange(changedIndices);\n    }\n  };\n  MDCListFoundation.prototype.setTabindexAtIndex = function (index) {\n    if (this.focusedItemIndex === numbers$2.UNSET_INDEX && index !== 0) {\n      // If some list item was selected set first list item's tabindex to -1.\n      // Generally, tabindex is set to 0 on first list item of list that has no\n      // preselected items.\n      this.adapter.setAttributeForElementIndex(0, 'tabindex', '-1');\n    } else if (this.focusedItemIndex >= 0 && this.focusedItemIndex !== index) {\n      this.adapter.setAttributeForElementIndex(this.focusedItemIndex, 'tabindex', '-1');\n    }\n    // Set the previous selection's tabindex to -1. We need this because\n    // in selection menus that are not visible, programmatically setting an\n    // option will not change focus but will change where tabindex should be 0.\n    if (!(this.selectedIndex instanceof Array) && this.selectedIndex !== index) {\n      this.adapter.setAttributeForElementIndex(this.selectedIndex, 'tabindex', '-1');\n    }\n    if (index !== numbers$2.UNSET_INDEX) {\n      this.adapter.setAttributeForElementIndex(index, 'tabindex', '0');\n    }\n  };\n  /**\n   * @return Return true if it is single selectin list, checkbox list or radio\n   *     list.\n   */\n  MDCListFoundation.prototype.isSelectableList = function () {\n    return this.isSingleSelectionList || this.isCheckboxList || this.isRadioList;\n  };\n  MDCListFoundation.prototype.setTabindexToFirstSelectedOrFocusedItem = function () {\n    var targetIndex = this.getFirstSelectedOrFocusedItemIndex();\n    this.setTabindexAtIndex(targetIndex);\n  };\n  MDCListFoundation.prototype.getFirstSelectedOrFocusedItemIndex = function () {\n    // Action lists retain focus on the most recently focused item.\n    if (!this.isSelectableList()) {\n      return Math.max(this.focusedItemIndex, 0);\n    }\n    // Single-selection lists focus the selected item.\n    if (typeof this.selectedIndex === 'number' && this.selectedIndex !== numbers$2.UNSET_INDEX) {\n      return this.selectedIndex;\n    }\n    // Multiple-selection lists focus the first selected item.\n    if (isNumberArray(this.selectedIndex) && this.selectedIndex.length > 0) {\n      return this.selectedIndex.reduce(function (minIndex, currentIndex) {\n        return Math.min(minIndex, currentIndex);\n      });\n    }\n    // Selection lists without a selection focus the first item.\n    return 0;\n  };\n  MDCListFoundation.prototype.isIndexValid = function (index, validateListType) {\n    var _this = this;\n    if (validateListType === void 0) {\n      validateListType = true;\n    }\n    if (index instanceof Array) {\n      if (!this.isCheckboxList && validateListType) {\n        throw new Error('MDCListFoundation: Array of index is only supported for checkbox based list');\n      }\n      if (index.length === 0) {\n        return true;\n      } else {\n        return index.some(function (i) {\n          return _this.isIndexInRange(i);\n        });\n      }\n    } else if (typeof index === 'number') {\n      if (this.isCheckboxList && validateListType) {\n        throw new Error(\"MDCListFoundation: Expected array of index for checkbox based list but got number: \" + index);\n      }\n      return this.isIndexInRange(index) || this.isSingleSelectionList && index === numbers$2.UNSET_INDEX;\n    } else {\n      return false;\n    }\n  };\n  MDCListFoundation.prototype.isIndexInRange = function (index) {\n    var listSize = this.adapter.getListItemCount();\n    return index >= 0 && index < listSize;\n  };\n  /**\n   * Sets selected index on user action, toggles checkboxes in checkbox lists\n   * by default, unless `isCheckboxAlreadyUpdatedInAdapter` is set to `true`.\n   *\n   * In cases where `isCheckboxAlreadyUpdatedInAdapter` is set to `true`, the\n   * UI is just updated to reflect the value returned by the adapter.\n   *\n   * When calling this, make sure user interaction does not toggle disabled\n   * list items.\n   */\n  MDCListFoundation.prototype.setSelectedIndexOnAction = function (index, isCheckboxAlreadyUpdatedInAdapter) {\n    this.lastSelectedIndex = index;\n    if (this.isCheckboxList) {\n      this.toggleCheckboxAtIndex(index, isCheckboxAlreadyUpdatedInAdapter);\n      this.adapter.notifySelectionChange([index]);\n    } else {\n      this.setSelectedIndex(index, {\n        isUserInteraction: true\n      });\n    }\n  };\n  MDCListFoundation.prototype.toggleCheckboxAtIndex = function (index, isCheckboxAlreadyUpdatedInAdapter) {\n    var selectionAttribute = this.getSelectionAttribute();\n    var adapterIsChecked = this.adapter.isCheckboxCheckedAtIndex(index);\n    // By default the checked value from the adapter is toggled unless the\n    // checked state in the adapter has already been updated beforehand.\n    // This can be happen when the underlying native checkbox has already\n    // been updated through the native click event.\n    var newCheckedValue;\n    if (isCheckboxAlreadyUpdatedInAdapter) {\n      newCheckedValue = adapterIsChecked;\n    } else {\n      newCheckedValue = !adapterIsChecked;\n      this.adapter.setCheckedCheckboxOrRadioAtIndex(index, newCheckedValue);\n    }\n    this.adapter.setAttributeForElementIndex(index, selectionAttribute, newCheckedValue ? 'true' : 'false');\n    // If none of the checkbox items are selected and selectedIndex is not\n    // initialized then provide a default value.\n    var selectedIndexes = this.selectedIndex === numbers$2.UNSET_INDEX ? [] : this.selectedIndex.slice();\n    if (newCheckedValue) {\n      selectedIndexes.push(index);\n    } else {\n      selectedIndexes = selectedIndexes.filter(function (i) {\n        return i !== index;\n      });\n    }\n    this.selectedIndex = selectedIndexes;\n  };\n  MDCListFoundation.prototype.focusItemAtIndex = function (index) {\n    this.adapter.focusItemAtIndex(index);\n    this.focusedItemIndex = index;\n  };\n  MDCListFoundation.prototype.checkboxListToggleAll = function (currentlySelectedIndexes, isUserInteraction) {\n    var count = this.adapter.getListItemCount();\n    // If all items are selected, deselect everything.\n    if (currentlySelectedIndexes.length === count) {\n      this.setCheckboxAtIndex([], {\n        isUserInteraction: isUserInteraction\n      });\n    } else {\n      // Otherwise select all enabled options.\n      var allIndexes = [];\n      for (var i = 0; i < count; i++) {\n        if (!this.isIndexDisabled(i) || currentlySelectedIndexes.indexOf(i) > -1) {\n          allIndexes.push(i);\n        }\n      }\n      this.setCheckboxAtIndex(allIndexes, {\n        isUserInteraction: isUserInteraction\n      });\n    }\n  };\n  /**\n   * Given the next desired character from the user, adds it to the typeahead\n   * buffer. Then, attempts to find the next option matching the buffer. Wraps\n   * around if at the end of options.\n   *\n   * @param nextChar The next character to add to the prefix buffer.\n   * @param startingIndex The index from which to start matching. Only relevant\n   *     when starting a new match sequence. To start a new match sequence,\n   *     clear the buffer using `clearTypeaheadBuffer`, or wait for the buffer\n   *     to clear after a set interval defined in list foundation. Defaults to\n   *     the currently focused index.\n   * @return The index of the matched item, or -1 if no match.\n   */\n  MDCListFoundation.prototype.typeaheadMatchItem = function (nextChar, startingIndex, skipFocus) {\n    var _this = this;\n    if (skipFocus === void 0) {\n      skipFocus = false;\n    }\n    var opts = {\n      focusItemAtIndex: function (index) {\n        _this.focusItemAtIndex(index);\n      },\n      focusedItemIndex: startingIndex ? startingIndex : this.focusedItemIndex,\n      nextChar: nextChar,\n      sortedIndexByFirstChar: this.sortedIndexByFirstChar,\n      skipFocus: skipFocus,\n      isItemAtIndexDisabled: function (index) {\n        return _this.isIndexDisabled(index);\n      }\n    };\n    return matchItem(opts, this.typeaheadState);\n  };\n  /**\n   * Initializes the MDCListTextAndIndex data structure by indexing the current\n   * list items by primary text.\n   *\n   * @return The primary texts of all the list items sorted by first character.\n   */\n  MDCListFoundation.prototype.typeaheadInitSortedIndex = function () {\n    return initSortedIndex(this.adapter.getListItemCount(), this.adapter.getPrimaryTextAtIndex);\n  };\n  /**\n   * Clears the typeahead buffer.\n   */\n  MDCListFoundation.prototype.clearTypeaheadBuffer = function () {\n    clearBuffer(this.typeaheadState);\n  };\n  return MDCListFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCList = /** @class */function (_super) {\n  __extends(MDCList, _super);\n  function MDCList() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(MDCList.prototype, \"vertical\", {\n    set: function (value) {\n      this.foundation.setVerticalOrientation(value);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCList.prototype, \"listElements\", {\n    get: function () {\n      return Array.from(this.root.querySelectorAll(\".\" + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS]));\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCList.prototype, \"wrapFocus\", {\n    set: function (value) {\n      this.foundation.setWrapFocus(value);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCList.prototype, \"typeaheadInProgress\", {\n    /**\n     * @return Whether typeahead is currently matching a user-specified prefix.\n     */\n    get: function () {\n      return this.foundation.isTypeaheadInProgress();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCList.prototype, \"hasTypeahead\", {\n    /**\n     * Sets whether typeahead functionality is enabled on the list.\n     * @param hasTypeahead Whether typeahead is enabled.\n     */\n    set: function (hasTypeahead) {\n      this.foundation.setHasTypeahead(hasTypeahead);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCList.prototype, \"singleSelection\", {\n    set: function (isSingleSelectionList) {\n      this.foundation.setSingleSelection(isSingleSelectionList);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCList.prototype, \"disabledItemsFocusable\", {\n    set: function (areDisabledItemsFocusable) {\n      this.foundation.setDisabledItemsFocusable(areDisabledItemsFocusable);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCList.prototype, \"selectedIndex\", {\n    get: function () {\n      return this.foundation.getSelectedIndex();\n    },\n    set: function (index) {\n      this.foundation.setSelectedIndex(index);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCList.attachTo = function (root) {\n    return new MDCList(root);\n  };\n  MDCList.prototype.initialSyncWithDOM = function () {\n    this.isEvolutionEnabled = evolutionAttribute in this.root.dataset;\n    if (this.isEvolutionEnabled) {\n      this.classNameMap = evolutionClassNameMap;\n    } else if (matches(this.root, strings$2.DEPRECATED_SELECTOR)) {\n      this.classNameMap = deprecatedClassNameMap;\n    } else {\n      this.classNameMap = Object.values(cssClasses$2).reduce(function (obj, className) {\n        obj[className] = className;\n        return obj;\n      }, {});\n    }\n    this.handleClick = this.handleClickEvent.bind(this);\n    this.handleKeydown = this.handleKeydownEvent.bind(this);\n    this.focusInEventListener = this.handleFocusInEvent.bind(this);\n    this.focusOutEventListener = this.handleFocusOutEvent.bind(this);\n    this.listen('keydown', this.handleKeydown);\n    this.listen('click', this.handleClick);\n    this.listen('focusin', this.focusInEventListener);\n    this.listen('focusout', this.focusOutEventListener);\n    this.layout();\n    this.initializeListType();\n    this.ensureFocusable();\n  };\n  MDCList.prototype.destroy = function () {\n    this.unlisten('keydown', this.handleKeydown);\n    this.unlisten('click', this.handleClick);\n    this.unlisten('focusin', this.focusInEventListener);\n    this.unlisten('focusout', this.focusOutEventListener);\n  };\n  MDCList.prototype.layout = function () {\n    var direction = this.root.getAttribute(strings$2.ARIA_ORIENTATION);\n    this.vertical = direction !== strings$2.ARIA_ORIENTATION_HORIZONTAL;\n    var itemSelector = \".\" + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + \":not([tabindex])\";\n    var childSelector = strings$2.FOCUSABLE_CHILD_ELEMENTS;\n    // List items need to have at least tabindex=-1 to be focusable.\n    var itemEls = this.root.querySelectorAll(itemSelector);\n    if (itemEls.length) {\n      Array.prototype.forEach.call(itemEls, function (el) {\n        el.setAttribute('tabindex', '-1');\n      });\n    }\n    // Child button/a elements are not tabbable until the list item is focused.\n    var focusableChildEls = this.root.querySelectorAll(childSelector);\n    if (focusableChildEls.length) {\n      Array.prototype.forEach.call(focusableChildEls, function (el) {\n        el.setAttribute('tabindex', '-1');\n      });\n    }\n    if (this.isEvolutionEnabled) {\n      this.foundation.setUseSelectedAttribute(true);\n    }\n    this.foundation.layout();\n  };\n  /**\n   * Extracts the primary text from a list item.\n   * @param item The list item element.\n   * @return The primary text in the element.\n   */\n  MDCList.prototype.getPrimaryText = function (item) {\n    var _a;\n    var primaryText = item.querySelector(\".\" + this.classNameMap[cssClasses$2.LIST_ITEM_PRIMARY_TEXT_CLASS]);\n    if (this.isEvolutionEnabled || primaryText) {\n      return (_a = primaryText === null || primaryText === void 0 ? void 0 : primaryText.textContent) !== null && _a !== void 0 ? _a : '';\n    }\n    var singleLineText = item.querySelector(\".\" + this.classNameMap[cssClasses$2.LIST_ITEM_TEXT_CLASS]);\n    return singleLineText && singleLineText.textContent || '';\n  };\n  /**\n   * Initialize selectedIndex value based on pre-selected list items.\n   */\n  MDCList.prototype.initializeListType = function () {\n    var _this = this;\n    this.isInteractive = matches(this.root, strings$2.ARIA_INTERACTIVE_ROLES_SELECTOR);\n    if (this.isEvolutionEnabled && this.isInteractive) {\n      var selection = Array.from(this.root.querySelectorAll(strings$2.SELECTED_ITEM_SELECTOR), function (listItem) {\n        return _this.listElements.indexOf(listItem);\n      });\n      if (matches(this.root, strings$2.ARIA_MULTI_SELECTABLE_SELECTOR)) {\n        this.selectedIndex = selection;\n      } else if (selection.length > 0) {\n        this.selectedIndex = selection[0];\n      }\n      return;\n    }\n    var checkboxListItems = this.root.querySelectorAll(strings$2.ARIA_ROLE_CHECKBOX_SELECTOR);\n    var radioSelectedListItem = this.root.querySelector(strings$2.ARIA_CHECKED_RADIO_SELECTOR);\n    if (checkboxListItems.length) {\n      var preselectedItems = this.root.querySelectorAll(strings$2.ARIA_CHECKED_CHECKBOX_SELECTOR);\n      this.selectedIndex = Array.from(preselectedItems, function (listItem) {\n        return _this.listElements.indexOf(listItem);\n      });\n    } else if (radioSelectedListItem) {\n      this.selectedIndex = this.listElements.indexOf(radioSelectedListItem);\n    }\n  };\n  /**\n   * Updates the list item at itemIndex to the desired isEnabled state.\n   * @param itemIndex Index of the list item\n   * @param isEnabled Sets the list item to enabled or disabled.\n   */\n  MDCList.prototype.setEnabled = function (itemIndex, isEnabled) {\n    this.foundation.setEnabled(itemIndex, isEnabled);\n  };\n  /**\n   * Given the next desired character from the user, adds it to the typeahead\n   * buffer. Then, attempts to find the next option matching the buffer. Wraps\n   * around if at the end of options.\n   *\n   * @param nextChar The next character to add to the prefix buffer.\n   * @param startingIndex The index from which to start matching. Defaults to\n   *     the currently focused index.\n   * @return The index of the matched item.\n   */\n  MDCList.prototype.typeaheadMatchItem = function (nextChar, startingIndex) {\n    return this.foundation.typeaheadMatchItem(nextChar, startingIndex, /** skipFocus */true);\n  };\n  MDCList.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take\n    // a Partial<MDCFooAdapter>. To ensure we don't accidentally omit any\n    // methods, we need a separate, strongly typed adapter variable.\n    var adapter = {\n      addClassForElementIndex: function (index, className) {\n        var element = _this.listElements[index];\n        if (element) {\n          element.classList.add(_this.classNameMap[className]);\n        }\n      },\n      focusItemAtIndex: function (index) {\n        var element = _this.listElements[index];\n        if (element) {\n          element.focus();\n        }\n      },\n      getAttributeForElementIndex: function (index, attr) {\n        return _this.listElements[index].getAttribute(attr);\n      },\n      getFocusedElementIndex: function () {\n        return _this.listElements.indexOf(document.activeElement);\n      },\n      getListItemCount: function () {\n        return _this.listElements.length;\n      },\n      getPrimaryTextAtIndex: function (index) {\n        return _this.getPrimaryText(_this.listElements[index]);\n      },\n      hasCheckboxAtIndex: function (index) {\n        var listItem = _this.listElements[index];\n        return !!listItem.querySelector(strings$2.CHECKBOX_SELECTOR);\n      },\n      hasRadioAtIndex: function (index) {\n        var listItem = _this.listElements[index];\n        return !!listItem.querySelector(strings$2.RADIO_SELECTOR);\n      },\n      isCheckboxCheckedAtIndex: function (index) {\n        var listItem = _this.listElements[index];\n        var toggleEl = listItem.querySelector(strings$2.CHECKBOX_SELECTOR);\n        return toggleEl.checked;\n      },\n      isFocusInsideList: function () {\n        return _this.root !== document.activeElement && _this.root.contains(document.activeElement);\n      },\n      isRootFocused: function () {\n        return document.activeElement === _this.root;\n      },\n      listItemAtIndexHasClass: function (index, className) {\n        return _this.listElements[index].classList.contains(_this.classNameMap[className]);\n      },\n      notifyAction: function (index) {\n        _this.emit(strings$2.ACTION_EVENT, {\n          index: index\n        }, /** shouldBubble */true);\n      },\n      notifySelectionChange: function (changedIndices) {\n        _this.emit(strings$2.SELECTION_CHANGE_EVENT, {\n          changedIndices: changedIndices\n        }, /** shouldBubble */true);\n      },\n      removeClassForElementIndex: function (index, className) {\n        var element = _this.listElements[index];\n        if (element) {\n          element.classList.remove(_this.classNameMap[className]);\n        }\n      },\n      setAttributeForElementIndex: function (index, attr, value) {\n        var element = _this.listElements[index];\n        if (element) {\n          element.setAttribute(attr, value);\n        }\n      },\n      setCheckedCheckboxOrRadioAtIndex: function (index, isChecked) {\n        var listItem = _this.listElements[index];\n        var toggleEl = listItem.querySelector(strings$2.CHECKBOX_RADIO_SELECTOR);\n        toggleEl.checked = isChecked;\n        var event = document.createEvent('Event');\n        event.initEvent('change', true, true);\n        toggleEl.dispatchEvent(event);\n      },\n      setTabIndexForListItemChildren: function (listItemIndex, tabIndexValue) {\n        var element = _this.listElements[listItemIndex];\n        var selector = strings$2.CHILD_ELEMENTS_TO_TOGGLE_TABINDEX;\n        Array.prototype.forEach.call(element.querySelectorAll(selector), function (el) {\n          el.setAttribute('tabindex', tabIndexValue);\n        });\n      }\n    };\n    return new MDCListFoundation(adapter);\n  };\n  /**\n   * Ensures that at least one item is focusable if the list is interactive and\n   * doesn't specify a suitable tabindex.\n   */\n  MDCList.prototype.ensureFocusable = function () {\n    if (this.isEvolutionEnabled && this.isInteractive) {\n      if (!this.root.querySelector(\".\" + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + \"[tabindex=\\\"0\\\"]\")) {\n        var index = this.initialFocusIndex();\n        if (index !== -1) {\n          this.listElements[index].tabIndex = 0;\n        }\n      }\n    }\n  };\n  MDCList.prototype.initialFocusIndex = function () {\n    if (this.selectedIndex instanceof Array && this.selectedIndex.length > 0) {\n      return this.selectedIndex[0];\n    }\n    if (typeof this.selectedIndex === 'number' && this.selectedIndex !== numbers$2.UNSET_INDEX) {\n      return this.selectedIndex;\n    }\n    var el = this.root.querySelector(\".\" + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + \":not(.\" + this.classNameMap[cssClasses$2.LIST_ITEM_DISABLED_CLASS] + \")\");\n    if (el === null) {\n      return -1;\n    }\n    return this.getListItemIndex(el);\n  };\n  /**\n   * Used to figure out which list item this event is targetting. Or returns -1\n   * if there is no list item\n   */\n  MDCList.prototype.getListItemIndex = function (el) {\n    var nearestParent = closest(el, \".\" + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + \", .\" + this.classNameMap[cssClasses$2.ROOT]);\n    // Get the index of the element if it is a list item.\n    if (nearestParent && matches(nearestParent, \".\" + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS])) {\n      return this.listElements.indexOf(nearestParent);\n    }\n    return -1;\n  };\n  /**\n   * Used to figure out which element was clicked before sending the event to\n   * the foundation.\n   */\n  MDCList.prototype.handleFocusInEvent = function (evt) {\n    var index = this.getListItemIndex(evt.target);\n    this.foundation.handleFocusIn(index);\n  };\n  /**\n   * Used to figure out which element was clicked before sending the event to\n   * the foundation.\n   */\n  MDCList.prototype.handleFocusOutEvent = function (evt) {\n    var index = this.getListItemIndex(evt.target);\n    this.foundation.handleFocusOut(index);\n  };\n  /**\n   * Used to figure out which element was focused when keydown event occurred\n   * before sending the event to the foundation.\n   */\n  MDCList.prototype.handleKeydownEvent = function (evt) {\n    var index = this.getListItemIndex(evt.target);\n    var target = evt.target;\n    this.foundation.handleKeydown(evt, target.classList.contains(this.classNameMap[cssClasses$2.LIST_ITEM_CLASS]), index);\n  };\n  /**\n   * Used to figure out which element was clicked before sending the event to\n   * the foundation.\n   */\n  MDCList.prototype.handleClickEvent = function (evt) {\n    var index = this.getListItemIndex(evt.target);\n    var target = evt.target;\n    // Toggle the checkbox only if it's not the target of the event, or the\n    // checkbox will have 2 change events.\n    var toggleCheckbox = !matches(target, strings$2.CHECKBOX_RADIO_SELECTOR);\n    this.foundation.handleClick(index, toggleCheckbox, evt);\n  };\n  return MDCList;\n}(MDCComponent);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses$1 = {\n  ANCHOR: 'mdc-menu-surface--anchor',\n  ANIMATING_CLOSED: 'mdc-menu-surface--animating-closed',\n  ANIMATING_OPEN: 'mdc-menu-surface--animating-open',\n  FIXED: 'mdc-menu-surface--fixed',\n  IS_OPEN_BELOW: 'mdc-menu-surface--is-open-below',\n  OPEN: 'mdc-menu-surface--open',\n  ROOT: 'mdc-menu-surface'\n};\n// tslint:disable:object-literal-sort-keys\nvar strings$1 = {\n  CLOSED_EVENT: 'MDCMenuSurface:closed',\n  CLOSING_EVENT: 'MDCMenuSurface:closing',\n  OPENED_EVENT: 'MDCMenuSurface:opened',\n  OPENING_EVENT: 'MDCMenuSurface:opening',\n  FOCUSABLE_ELEMENTS: ['button:not(:disabled)', '[href]:not([aria-disabled=\"true\"])', 'input:not(:disabled)', 'select:not(:disabled)', 'textarea:not(:disabled)', '[tabindex]:not([tabindex=\"-1\"]):not([aria-disabled=\"true\"])'].join(', ')\n};\n// tslint:enable:object-literal-sort-keys\nvar numbers$1 = {\n  /** Total duration of menu-surface open animation. */\n  TRANSITION_OPEN_DURATION: 120,\n  /** Total duration of menu-surface close animation. */\n  TRANSITION_CLOSE_DURATION: 75,\n  /**\n   * Margin left to the edge of the viewport when menu-surface is at maximum\n   * possible height. Also used as a viewport margin.\n   */\n  MARGIN_TO_EDGE: 32,\n  /**\n   * Ratio of anchor width to menu-surface width for switching from corner\n   * positioning to center positioning.\n   */\n  ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO: 0.67,\n  /**\n   * Amount of time to wait before restoring focus when closing the menu\n   * surface. This is important because if a touch event triggered the menu\n   * close, and the subsequent mouse event occurs after focus is restored, then\n   * the restored focus would be lost.\n   */\n  TOUCH_EVENT_WAIT_MS: 30\n};\n/**\n * Enum for bits in the {@see Corner) bitmap.\n */\nvar CornerBit;\n(function (CornerBit) {\n  CornerBit[CornerBit[\"BOTTOM\"] = 1] = \"BOTTOM\";\n  CornerBit[CornerBit[\"CENTER\"] = 2] = \"CENTER\";\n  CornerBit[CornerBit[\"RIGHT\"] = 4] = \"RIGHT\";\n  CornerBit[CornerBit[\"FLIP_RTL\"] = 8] = \"FLIP_RTL\";\n})(CornerBit || (CornerBit = {}));\n/**\n * Enum for representing an element corner for positioning the menu-surface.\n *\n * The START constants map to LEFT if element directionality is left\n * to right and RIGHT if the directionality is right to left.\n * Likewise END maps to RIGHT or LEFT depending on the directionality.\n */\nvar Corner;\n(function (Corner) {\n  Corner[Corner[\"TOP_LEFT\"] = 0] = \"TOP_LEFT\";\n  Corner[Corner[\"TOP_RIGHT\"] = 4] = \"TOP_RIGHT\";\n  Corner[Corner[\"BOTTOM_LEFT\"] = 1] = \"BOTTOM_LEFT\";\n  Corner[Corner[\"BOTTOM_RIGHT\"] = 5] = \"BOTTOM_RIGHT\";\n  Corner[Corner[\"TOP_START\"] = 8] = \"TOP_START\";\n  Corner[Corner[\"TOP_END\"] = 12] = \"TOP_END\";\n  Corner[Corner[\"BOTTOM_START\"] = 9] = \"BOTTOM_START\";\n  Corner[Corner[\"BOTTOM_END\"] = 13] = \"BOTTOM_END\";\n})(Corner || (Corner = {}));\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCMenuSurfaceFoundation = /** @class */function (_super) {\n  __extends(MDCMenuSurfaceFoundation, _super);\n  function MDCMenuSurfaceFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCMenuSurfaceFoundation.defaultAdapter), adapter)) || this;\n    _this.isSurfaceOpen = false;\n    _this.isQuickOpen = false;\n    _this.isHoistedElement = false;\n    _this.isFixedPosition = false;\n    _this.isHorizontallyCenteredOnViewport = false;\n    _this.maxHeight = 0;\n    _this.openBottomBias = 0;\n    _this.openAnimationEndTimerId = 0;\n    _this.closeAnimationEndTimerId = 0;\n    _this.animationRequestId = 0;\n    _this.anchorCorner = Corner.TOP_START;\n    /**\n     * Corner of the menu surface to which menu surface is attached to anchor.\n     *\n     *  Anchor corner --->+----------+\n     *                    |  ANCHOR  |\n     *                    +----------+\n     *  Origin corner --->+--------------+\n     *                    |              |\n     *                    |              |\n     *                    | MENU SURFACE |\n     *                    |              |\n     *                    |              |\n     *                    +--------------+\n     */\n    _this.originCorner = Corner.TOP_START;\n    _this.anchorMargin = {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    };\n    _this.position = {\n      x: 0,\n      y: 0\n    };\n    return _this;\n  }\n  Object.defineProperty(MDCMenuSurfaceFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses$1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenuSurfaceFoundation, \"strings\", {\n    get: function () {\n      return strings$1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenuSurfaceFoundation, \"numbers\", {\n    get: function () {\n      return numbers$1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenuSurfaceFoundation, \"Corner\", {\n    get: function () {\n      return Corner;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenuSurfaceFoundation, \"defaultAdapter\", {\n    /**\n     * @see {@link MDCMenuSurfaceAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        hasClass: function () {\n          return false;\n        },\n        hasAnchor: function () {\n          return false;\n        },\n        isElementInContainer: function () {\n          return false;\n        },\n        isFocused: function () {\n          return false;\n        },\n        isRtl: function () {\n          return false;\n        },\n        getInnerDimensions: function () {\n          return {\n            height: 0,\n            width: 0\n          };\n        },\n        getAnchorDimensions: function () {\n          return null;\n        },\n        getWindowDimensions: function () {\n          return {\n            height: 0,\n            width: 0\n          };\n        },\n        getBodyDimensions: function () {\n          return {\n            height: 0,\n            width: 0\n          };\n        },\n        getWindowScroll: function () {\n          return {\n            x: 0,\n            y: 0\n          };\n        },\n        setPosition: function () {\n          return undefined;\n        },\n        setMaxHeight: function () {\n          return undefined;\n        },\n        setTransformOrigin: function () {\n          return undefined;\n        },\n        saveFocus: function () {\n          return undefined;\n        },\n        restoreFocus: function () {\n          return undefined;\n        },\n        notifyClose: function () {\n          return undefined;\n        },\n        notifyClosing: function () {\n          return undefined;\n        },\n        notifyOpen: function () {\n          return undefined;\n        },\n        notifyOpening: function () {\n          return undefined;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCMenuSurfaceFoundation.prototype.init = function () {\n    var _a = MDCMenuSurfaceFoundation.cssClasses,\n      ROOT = _a.ROOT,\n      OPEN = _a.OPEN;\n    if (!this.adapter.hasClass(ROOT)) {\n      throw new Error(ROOT + \" class required in root element.\");\n    }\n    if (this.adapter.hasClass(OPEN)) {\n      this.isSurfaceOpen = true;\n    }\n  };\n  MDCMenuSurfaceFoundation.prototype.destroy = function () {\n    clearTimeout(this.openAnimationEndTimerId);\n    clearTimeout(this.closeAnimationEndTimerId);\n    // Cancel any currently running animations.\n    cancelAnimationFrame(this.animationRequestId);\n  };\n  /**\n   * @param corner Default anchor corner alignment of top-left menu surface\n   *     corner.\n   */\n  MDCMenuSurfaceFoundation.prototype.setAnchorCorner = function (corner) {\n    this.anchorCorner = corner;\n  };\n  /**\n   * Flip menu corner horizontally.\n   */\n  MDCMenuSurfaceFoundation.prototype.flipCornerHorizontally = function () {\n    this.originCorner = this.originCorner ^ CornerBit.RIGHT;\n  };\n  /**\n   * @param margin Set of margin values from anchor.\n   */\n  MDCMenuSurfaceFoundation.prototype.setAnchorMargin = function (margin) {\n    this.anchorMargin.top = margin.top || 0;\n    this.anchorMargin.right = margin.right || 0;\n    this.anchorMargin.bottom = margin.bottom || 0;\n    this.anchorMargin.left = margin.left || 0;\n  };\n  /** Used to indicate if the menu-surface is hoisted to the body. */\n  MDCMenuSurfaceFoundation.prototype.setIsHoisted = function (isHoisted) {\n    this.isHoistedElement = isHoisted;\n  };\n  /**\n   * Used to set the menu-surface calculations based on a fixed position menu.\n   */\n  MDCMenuSurfaceFoundation.prototype.setFixedPosition = function (isFixedPosition) {\n    this.isFixedPosition = isFixedPosition;\n  };\n  /**\n   * @return Returns true if menu is in fixed (`position: fixed`) position.\n   */\n  MDCMenuSurfaceFoundation.prototype.isFixed = function () {\n    return this.isFixedPosition;\n  };\n  /** Sets the menu-surface position on the page. */\n  MDCMenuSurfaceFoundation.prototype.setAbsolutePosition = function (x, y) {\n    this.position.x = this.isFinite(x) ? x : 0;\n    this.position.y = this.isFinite(y) ? y : 0;\n  };\n  /** Sets whether menu-surface should be horizontally centered to viewport. */\n  MDCMenuSurfaceFoundation.prototype.setIsHorizontallyCenteredOnViewport = function (isCentered) {\n    this.isHorizontallyCenteredOnViewport = isCentered;\n  };\n  MDCMenuSurfaceFoundation.prototype.setQuickOpen = function (quickOpen) {\n    this.isQuickOpen = quickOpen;\n  };\n  /**\n   * Sets maximum menu-surface height on open.\n   * @param maxHeight The desired max-height. Set to 0 (default) to\n   *     automatically calculate max height based on available viewport space.\n   */\n  MDCMenuSurfaceFoundation.prototype.setMaxHeight = function (maxHeight) {\n    this.maxHeight = maxHeight;\n  };\n  /**\n   * Set to a positive integer to influence the menu to preferentially open\n   * below the anchor instead of above.\n   * @param bias A value of `x` simulates an extra `x` pixels of available space\n   *     below the menu during positioning calculations.\n   */\n  MDCMenuSurfaceFoundation.prototype.setOpenBottomBias = function (bias) {\n    this.openBottomBias = bias;\n  };\n  MDCMenuSurfaceFoundation.prototype.isOpen = function () {\n    return this.isSurfaceOpen;\n  };\n  /**\n   * Open the menu surface.\n   */\n  MDCMenuSurfaceFoundation.prototype.open = function () {\n    var _this = this;\n    if (this.isSurfaceOpen) {\n      return;\n    }\n    this.adapter.notifyOpening();\n    this.adapter.saveFocus();\n    if (this.isQuickOpen) {\n      this.isSurfaceOpen = true;\n      this.adapter.addClass(MDCMenuSurfaceFoundation.cssClasses.OPEN);\n      this.dimensions = this.adapter.getInnerDimensions();\n      this.autoposition();\n      this.adapter.notifyOpen();\n    } else {\n      this.adapter.addClass(MDCMenuSurfaceFoundation.cssClasses.ANIMATING_OPEN);\n      this.animationRequestId = requestAnimationFrame(function () {\n        _this.dimensions = _this.adapter.getInnerDimensions();\n        _this.autoposition();\n        _this.adapter.addClass(MDCMenuSurfaceFoundation.cssClasses.OPEN);\n        _this.openAnimationEndTimerId = setTimeout(function () {\n          _this.openAnimationEndTimerId = 0;\n          _this.adapter.removeClass(MDCMenuSurfaceFoundation.cssClasses.ANIMATING_OPEN);\n          _this.adapter.notifyOpen();\n        }, numbers$1.TRANSITION_OPEN_DURATION);\n      });\n      this.isSurfaceOpen = true;\n    }\n  };\n  /**\n   * Closes the menu surface.\n   */\n  MDCMenuSurfaceFoundation.prototype.close = function (skipRestoreFocus) {\n    var _this = this;\n    if (skipRestoreFocus === void 0) {\n      skipRestoreFocus = false;\n    }\n    if (!this.isSurfaceOpen) {\n      return;\n    }\n    this.adapter.notifyClosing();\n    if (this.isQuickOpen) {\n      this.isSurfaceOpen = false;\n      if (!skipRestoreFocus) {\n        this.maybeRestoreFocus();\n      }\n      this.adapter.removeClass(MDCMenuSurfaceFoundation.cssClasses.OPEN);\n      this.adapter.removeClass(MDCMenuSurfaceFoundation.cssClasses.IS_OPEN_BELOW);\n      this.adapter.notifyClose();\n      return;\n    }\n    this.adapter.addClass(MDCMenuSurfaceFoundation.cssClasses.ANIMATING_CLOSED);\n    requestAnimationFrame(function () {\n      _this.adapter.removeClass(MDCMenuSurfaceFoundation.cssClasses.OPEN);\n      _this.adapter.removeClass(MDCMenuSurfaceFoundation.cssClasses.IS_OPEN_BELOW);\n      _this.closeAnimationEndTimerId = setTimeout(function () {\n        _this.closeAnimationEndTimerId = 0;\n        _this.adapter.removeClass(MDCMenuSurfaceFoundation.cssClasses.ANIMATING_CLOSED);\n        _this.adapter.notifyClose();\n      }, numbers$1.TRANSITION_CLOSE_DURATION);\n    });\n    this.isSurfaceOpen = false;\n    if (!skipRestoreFocus) {\n      this.maybeRestoreFocus();\n    }\n  };\n  /** Handle clicks and close if not within menu-surface element. */\n  MDCMenuSurfaceFoundation.prototype.handleBodyClick = function (evt) {\n    var el = evt.target;\n    if (this.adapter.isElementInContainer(el)) {\n      return;\n    }\n    this.close();\n  };\n  /** Handle keys that close the surface. */\n  MDCMenuSurfaceFoundation.prototype.handleKeydown = function (evt) {\n    var keyCode = evt.keyCode,\n      key = evt.key;\n    var isEscape = key === 'Escape' || keyCode === 27;\n    if (isEscape) {\n      this.close();\n    }\n  };\n  MDCMenuSurfaceFoundation.prototype.autoposition = function () {\n    var _a;\n    // Compute measurements for autoposition methods reuse.\n    this.measurements = this.getAutoLayoutmeasurements();\n    var corner = this.getoriginCorner();\n    var maxMenuSurfaceHeight = this.getMenuSurfaceMaxHeight(corner);\n    var verticalAlignment = this.hasBit(corner, CornerBit.BOTTOM) ? 'bottom' : 'top';\n    var horizontalAlignment = this.hasBit(corner, CornerBit.RIGHT) ? 'right' : 'left';\n    var horizontalOffset = this.getHorizontalOriginOffset(corner);\n    var verticalOffset = this.getVerticalOriginOffset(corner);\n    var _b = this.measurements,\n      anchorSize = _b.anchorSize,\n      surfaceSize = _b.surfaceSize;\n    var position = (_a = {}, _a[horizontalAlignment] = horizontalOffset, _a[verticalAlignment] = verticalOffset, _a);\n    // Center align when anchor width is comparable or greater than menu\n    // surface, otherwise keep corner.\n    if (anchorSize.width / surfaceSize.width > numbers$1.ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO) {\n      horizontalAlignment = 'center';\n    }\n    // If the menu-surface has been hoisted to the body, it's no longer relative\n    // to the anchor element\n    if (this.isHoistedElement || this.isFixedPosition) {\n      this.adjustPositionForHoistedElement(position);\n    }\n    this.adapter.setTransformOrigin(horizontalAlignment + \" \" + verticalAlignment);\n    this.adapter.setPosition(position);\n    this.adapter.setMaxHeight(maxMenuSurfaceHeight ? maxMenuSurfaceHeight + 'px' : '');\n    // If it is opened from the top then add is-open-below class\n    if (!this.hasBit(corner, CornerBit.BOTTOM)) {\n      this.adapter.addClass(MDCMenuSurfaceFoundation.cssClasses.IS_OPEN_BELOW);\n    }\n  };\n  /**\n   * @return Measurements used to position menu surface popup.\n   */\n  MDCMenuSurfaceFoundation.prototype.getAutoLayoutmeasurements = function () {\n    var anchorRect = this.adapter.getAnchorDimensions();\n    var bodySize = this.adapter.getBodyDimensions();\n    var viewportSize = this.adapter.getWindowDimensions();\n    var windowScroll = this.adapter.getWindowScroll();\n    if (!anchorRect) {\n      // tslint:disable:object-literal-sort-keys Positional properties are more readable when they're grouped together\n      anchorRect = {\n        top: this.position.y,\n        right: this.position.x,\n        bottom: this.position.y,\n        left: this.position.x,\n        width: 0,\n        height: 0\n      };\n      // tslint:enable:object-literal-sort-keys\n    }\n    return {\n      anchorSize: anchorRect,\n      bodySize: bodySize,\n      surfaceSize: this.dimensions,\n      viewportDistance: {\n        // tslint:disable:object-literal-sort-keys Positional properties are more readable when they're grouped together\n        top: anchorRect.top,\n        right: viewportSize.width - anchorRect.right,\n        bottom: viewportSize.height - anchorRect.bottom,\n        left: anchorRect.left\n        // tslint:enable:object-literal-sort-keys\n      },\n      viewportSize: viewportSize,\n      windowScroll: windowScroll\n    };\n  };\n  /**\n   * Computes the corner of the anchor from which to animate and position the\n   * menu surface.\n   *\n   * Only LEFT or RIGHT bit is used to position the menu surface ignoring RTL\n   * context. E.g., menu surface will be positioned from right side on TOP_END.\n   */\n  MDCMenuSurfaceFoundation.prototype.getoriginCorner = function () {\n    var corner = this.originCorner;\n    var _a = this.measurements,\n      viewportDistance = _a.viewportDistance,\n      anchorSize = _a.anchorSize,\n      surfaceSize = _a.surfaceSize;\n    var MARGIN_TO_EDGE = MDCMenuSurfaceFoundation.numbers.MARGIN_TO_EDGE;\n    var isAnchoredToBottom = this.hasBit(this.anchorCorner, CornerBit.BOTTOM);\n    var availableTop;\n    var availableBottom;\n    if (isAnchoredToBottom) {\n      availableTop = viewportDistance.top - MARGIN_TO_EDGE + this.anchorMargin.bottom;\n      availableBottom = viewportDistance.bottom - MARGIN_TO_EDGE - this.anchorMargin.bottom;\n    } else {\n      availableTop = viewportDistance.top - MARGIN_TO_EDGE + this.anchorMargin.top;\n      availableBottom = viewportDistance.bottom - MARGIN_TO_EDGE + anchorSize.height - this.anchorMargin.top;\n    }\n    var isAvailableBottom = availableBottom - surfaceSize.height > 0;\n    if (!isAvailableBottom && availableTop > availableBottom + this.openBottomBias) {\n      // Attach bottom side of surface to the anchor.\n      corner = this.setBit(corner, CornerBit.BOTTOM);\n    }\n    var isRtl = this.adapter.isRtl();\n    var isFlipRtl = this.hasBit(this.anchorCorner, CornerBit.FLIP_RTL);\n    var hasRightBit = this.hasBit(this.anchorCorner, CornerBit.RIGHT) || this.hasBit(corner, CornerBit.RIGHT);\n    // Whether surface attached to right side of anchor element.\n    var isAnchoredToRight = false;\n    // Anchored to start\n    if (isRtl && isFlipRtl) {\n      isAnchoredToRight = !hasRightBit;\n    } else {\n      // Anchored to right\n      isAnchoredToRight = hasRightBit;\n    }\n    var availableLeft;\n    var availableRight;\n    if (isAnchoredToRight) {\n      availableLeft = viewportDistance.left + anchorSize.width + this.anchorMargin.right;\n      availableRight = viewportDistance.right - this.anchorMargin.right;\n    } else {\n      availableLeft = viewportDistance.left + this.anchorMargin.left;\n      availableRight = viewportDistance.right + anchorSize.width - this.anchorMargin.left;\n    }\n    var isAvailableLeft = availableLeft - surfaceSize.width > 0;\n    var isAvailableRight = availableRight - surfaceSize.width > 0;\n    var isOriginCornerAlignedToEnd = this.hasBit(corner, CornerBit.FLIP_RTL) && this.hasBit(corner, CornerBit.RIGHT);\n    if (isAvailableRight && isOriginCornerAlignedToEnd && isRtl || !isAvailableLeft && isOriginCornerAlignedToEnd) {\n      // Attach left side of surface to the anchor.\n      corner = this.unsetBit(corner, CornerBit.RIGHT);\n    } else if (isAvailableLeft && isAnchoredToRight && isRtl || isAvailableLeft && !isAnchoredToRight && hasRightBit || !isAvailableRight && availableLeft >= availableRight) {\n      // Attach right side of surface to the anchor.\n      corner = this.setBit(corner, CornerBit.RIGHT);\n    }\n    return corner;\n  };\n  /**\n   * @param corner Origin corner of the menu surface.\n   * @return Maximum height of the menu surface, based on available space. 0\n   *     indicates should not be set.\n   */\n  MDCMenuSurfaceFoundation.prototype.getMenuSurfaceMaxHeight = function (corner) {\n    if (this.maxHeight > 0) {\n      return this.maxHeight;\n    }\n    var viewportDistance = this.measurements.viewportDistance;\n    var maxHeight = 0;\n    var isBottomAligned = this.hasBit(corner, CornerBit.BOTTOM);\n    var isBottomAnchored = this.hasBit(this.anchorCorner, CornerBit.BOTTOM);\n    var MARGIN_TO_EDGE = MDCMenuSurfaceFoundation.numbers.MARGIN_TO_EDGE;\n    // When maximum height is not specified, it is handled from CSS.\n    if (isBottomAligned) {\n      maxHeight = viewportDistance.top + this.anchorMargin.top - MARGIN_TO_EDGE;\n      if (!isBottomAnchored) {\n        maxHeight += this.measurements.anchorSize.height;\n      }\n    } else {\n      maxHeight = viewportDistance.bottom - this.anchorMargin.bottom + this.measurements.anchorSize.height - MARGIN_TO_EDGE;\n      if (isBottomAnchored) {\n        maxHeight -= this.measurements.anchorSize.height;\n      }\n    }\n    return maxHeight;\n  };\n  /**\n   * @param corner Origin corner of the menu surface.\n   * @return Horizontal offset of menu surface origin corner from corresponding\n   *     anchor corner.\n   */\n  MDCMenuSurfaceFoundation.prototype.getHorizontalOriginOffset = function (corner) {\n    var anchorSize = this.measurements.anchorSize;\n    // isRightAligned corresponds to using the 'right' property on the surface.\n    var isRightAligned = this.hasBit(corner, CornerBit.RIGHT);\n    var avoidHorizontalOverlap = this.hasBit(this.anchorCorner, CornerBit.RIGHT);\n    if (isRightAligned) {\n      var rightOffset = avoidHorizontalOverlap ? anchorSize.width - this.anchorMargin.left : this.anchorMargin.right;\n      // For hoisted or fixed elements, adjust the offset by the difference\n      // between viewport width and body width so when we calculate the right\n      // value (`adjustPositionForHoistedElement`) based on the element\n      // position, the right property is correct.\n      if (this.isHoistedElement || this.isFixedPosition) {\n        return rightOffset - (this.measurements.viewportSize.width - this.measurements.bodySize.width);\n      }\n      return rightOffset;\n    }\n    return avoidHorizontalOverlap ? anchorSize.width - this.anchorMargin.right : this.anchorMargin.left;\n  };\n  /**\n   * @param corner Origin corner of the menu surface.\n   * @return Vertical offset of menu surface origin corner from corresponding\n   *     anchor corner.\n   */\n  MDCMenuSurfaceFoundation.prototype.getVerticalOriginOffset = function (corner) {\n    var anchorSize = this.measurements.anchorSize;\n    var isBottomAligned = this.hasBit(corner, CornerBit.BOTTOM);\n    var avoidVerticalOverlap = this.hasBit(this.anchorCorner, CornerBit.BOTTOM);\n    var y = 0;\n    if (isBottomAligned) {\n      y = avoidVerticalOverlap ? anchorSize.height - this.anchorMargin.top : -this.anchorMargin.bottom;\n    } else {\n      y = avoidVerticalOverlap ? anchorSize.height + this.anchorMargin.bottom : this.anchorMargin.top;\n    }\n    return y;\n  };\n  /**\n   * Calculates the offsets for positioning the menu-surface when the\n   * menu-surface has been hoisted to the body.\n   */\n  MDCMenuSurfaceFoundation.prototype.adjustPositionForHoistedElement = function (position) {\n    var e_1, _a;\n    var _b = this.measurements,\n      windowScroll = _b.windowScroll,\n      viewportDistance = _b.viewportDistance,\n      surfaceSize = _b.surfaceSize,\n      viewportSize = _b.viewportSize;\n    var props = Object.keys(position);\n    try {\n      for (var props_1 = __values(props), props_1_1 = props_1.next(); !props_1_1.done; props_1_1 = props_1.next()) {\n        var prop = props_1_1.value;\n        var value = position[prop] || 0;\n        if (this.isHorizontallyCenteredOnViewport && (prop === 'left' || prop === 'right')) {\n          position[prop] = (viewportSize.width - surfaceSize.width) / 2;\n          continue;\n        }\n        // Hoisted surfaces need to have the anchor elements location on the page\n        // added to the position properties for proper alignment on the body.\n        value += viewportDistance[prop];\n        // Surfaces that are absolutely positioned need to have additional\n        // calculations for scroll and bottom positioning.\n        if (!this.isFixedPosition) {\n          if (prop === 'top') {\n            value += windowScroll.y;\n          } else if (prop === 'bottom') {\n            value -= windowScroll.y;\n          } else if (prop === 'left') {\n            value += windowScroll.x;\n          } else {\n            // prop === 'right'\n            value -= windowScroll.x;\n          }\n        }\n        position[prop] = value;\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (props_1_1 && !props_1_1.done && (_a = props_1.return)) _a.call(props_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  };\n  /**\n   * The last focused element when the menu surface was opened should regain\n   * focus, if the user is focused on or within the menu surface when it is\n   * closed.\n   */\n  MDCMenuSurfaceFoundation.prototype.maybeRestoreFocus = function () {\n    var _this = this;\n    var isRootFocused = this.adapter.isFocused();\n    var ownerDocument = this.adapter.getOwnerDocument ? this.adapter.getOwnerDocument() : document;\n    var childHasFocus = ownerDocument.activeElement && this.adapter.isElementInContainer(ownerDocument.activeElement);\n    if (isRootFocused || childHasFocus) {\n      // Wait before restoring focus when closing the menu surface. This is\n      // important because if a touch event triggered the menu close, and the\n      // subsequent mouse event occurs after focus is restored, then the\n      // restored focus would be lost.\n      setTimeout(function () {\n        _this.adapter.restoreFocus();\n      }, numbers$1.TOUCH_EVENT_WAIT_MS);\n    }\n  };\n  MDCMenuSurfaceFoundation.prototype.hasBit = function (corner, bit) {\n    return Boolean(corner & bit); // tslint:disable-line:no-bitwise\n  };\n  MDCMenuSurfaceFoundation.prototype.setBit = function (corner, bit) {\n    return corner | bit; // tslint:disable-line:no-bitwise\n  };\n  MDCMenuSurfaceFoundation.prototype.unsetBit = function (corner, bit) {\n    return corner ^ bit;\n  };\n  /**\n   * isFinite that doesn't force conversion to number type.\n   * Equivalent to Number.isFinite in ES2015, which is not supported in IE.\n   */\n  MDCMenuSurfaceFoundation.prototype.isFinite = function (num) {\n    return typeof num === 'number' && isFinite(num);\n  };\n  return MDCMenuSurfaceFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCMenuSurface = /** @class */function (_super) {\n  __extends(MDCMenuSurface, _super);\n  function MDCMenuSurface() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCMenuSurface.attachTo = function (root) {\n    return new MDCMenuSurface(root);\n  };\n  MDCMenuSurface.prototype.initialSyncWithDOM = function () {\n    var _this = this;\n    var parentEl = this.root.parentElement;\n    this.anchorElement = parentEl && parentEl.classList.contains(cssClasses$1.ANCHOR) ? parentEl : null;\n    if (this.root.classList.contains(cssClasses$1.FIXED)) {\n      this.setFixedPosition(true);\n    }\n    this.handleKeydown = function (event) {\n      _this.foundation.handleKeydown(event);\n    };\n    this.handleBodyClick = function (event) {\n      _this.foundation.handleBodyClick(event);\n    };\n    // capture so that no race between handleBodyClick and quickOpen when\n    // menusurface opened on button click which registers this listener\n    this.registerBodyClickListener = function () {\n      document.body.addEventListener('click', _this.handleBodyClick, {\n        capture: true\n      });\n    };\n    this.deregisterBodyClickListener = function () {\n      document.body.removeEventListener('click', _this.handleBodyClick, {\n        capture: true\n      });\n    };\n    this.listen('keydown', this.handleKeydown);\n    this.listen(strings$1.OPENED_EVENT, this.registerBodyClickListener);\n    this.listen(strings$1.CLOSED_EVENT, this.deregisterBodyClickListener);\n  };\n  MDCMenuSurface.prototype.destroy = function () {\n    this.unlisten('keydown', this.handleKeydown);\n    this.unlisten(strings$1.OPENED_EVENT, this.registerBodyClickListener);\n    this.unlisten(strings$1.CLOSED_EVENT, this.deregisterBodyClickListener);\n    _super.prototype.destroy.call(this);\n  };\n  MDCMenuSurface.prototype.isOpen = function () {\n    return this.foundation.isOpen();\n  };\n  MDCMenuSurface.prototype.open = function () {\n    this.foundation.open();\n  };\n  MDCMenuSurface.prototype.close = function (skipRestoreFocus) {\n    if (skipRestoreFocus === void 0) {\n      skipRestoreFocus = false;\n    }\n    this.foundation.close(skipRestoreFocus);\n  };\n  Object.defineProperty(MDCMenuSurface.prototype, \"quickOpen\", {\n    set: function (quickOpen) {\n      this.foundation.setQuickOpen(quickOpen);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /** Sets the foundation to use page offsets for an positioning when the menu is hoisted to the body. */\n  MDCMenuSurface.prototype.setIsHoisted = function (isHoisted) {\n    this.foundation.setIsHoisted(isHoisted);\n  };\n  /** Sets the element that the menu-surface is anchored to. */\n  MDCMenuSurface.prototype.setMenuSurfaceAnchorElement = function (element) {\n    this.anchorElement = element;\n  };\n  /** Sets the menu-surface to position: fixed. */\n  MDCMenuSurface.prototype.setFixedPosition = function (isFixed) {\n    if (isFixed) {\n      this.root.classList.add(cssClasses$1.FIXED);\n    } else {\n      this.root.classList.remove(cssClasses$1.FIXED);\n    }\n    this.foundation.setFixedPosition(isFixed);\n  };\n  /** Sets the absolute x/y position to position based on. Requires the menu to be hoisted. */\n  MDCMenuSurface.prototype.setAbsolutePosition = function (x, y) {\n    this.foundation.setAbsolutePosition(x, y);\n    this.setIsHoisted(true);\n  };\n  /**\n   * @param corner Default anchor corner alignment of top-left surface corner.\n   */\n  MDCMenuSurface.prototype.setAnchorCorner = function (corner) {\n    this.foundation.setAnchorCorner(corner);\n  };\n  MDCMenuSurface.prototype.setAnchorMargin = function (margin) {\n    this.foundation.setAnchorMargin(margin);\n  };\n  MDCMenuSurface.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = {\n      addClass: function (className) {\n        return _this.root.classList.add(className);\n      },\n      removeClass: function (className) {\n        return _this.root.classList.remove(className);\n      },\n      hasClass: function (className) {\n        return _this.root.classList.contains(className);\n      },\n      hasAnchor: function () {\n        return !!_this.anchorElement;\n      },\n      notifyClose: function () {\n        return _this.emit(MDCMenuSurfaceFoundation.strings.CLOSED_EVENT, {});\n      },\n      notifyClosing: function () {\n        _this.emit(MDCMenuSurfaceFoundation.strings.CLOSING_EVENT, {});\n      },\n      notifyOpen: function () {\n        return _this.emit(MDCMenuSurfaceFoundation.strings.OPENED_EVENT, {});\n      },\n      notifyOpening: function () {\n        return _this.emit(MDCMenuSurfaceFoundation.strings.OPENING_EVENT, {});\n      },\n      isElementInContainer: function (el) {\n        return _this.root.contains(el);\n      },\n      isRtl: function () {\n        return getComputedStyle(_this.root).getPropertyValue('direction') === 'rtl';\n      },\n      setTransformOrigin: function (origin) {\n        var propertyName = getCorrectPropertyName(window, 'transform') + \"-origin\";\n        _this.root.style.setProperty(propertyName, origin);\n      },\n      isFocused: function () {\n        return document.activeElement === _this.root;\n      },\n      saveFocus: function () {\n        _this.previousFocus = document.activeElement;\n      },\n      restoreFocus: function () {\n        if (_this.root.contains(document.activeElement)) {\n          if (_this.previousFocus && _this.previousFocus.focus) {\n            _this.previousFocus.focus();\n          }\n        }\n      },\n      getInnerDimensions: function () {\n        return {\n          width: _this.root.offsetWidth,\n          height: _this.root.offsetHeight\n        };\n      },\n      getAnchorDimensions: function () {\n        return _this.anchorElement ? _this.anchorElement.getBoundingClientRect() : null;\n      },\n      getWindowDimensions: function () {\n        return {\n          width: window.innerWidth,\n          height: window.innerHeight\n        };\n      },\n      getBodyDimensions: function () {\n        return {\n          width: document.body.clientWidth,\n          height: document.body.clientHeight\n        };\n      },\n      getWindowScroll: function () {\n        return {\n          x: window.pageXOffset,\n          y: window.pageYOffset\n        };\n      },\n      setPosition: function (position) {\n        var rootHTML = _this.root;\n        rootHTML.style.left = 'left' in position ? position.left + \"px\" : '';\n        rootHTML.style.right = 'right' in position ? position.right + \"px\" : '';\n        rootHTML.style.top = 'top' in position ? position.top + \"px\" : '';\n        rootHTML.style.bottom = 'bottom' in position ? position.bottom + \"px\" : '';\n      },\n      setMaxHeight: function (height) {\n        _this.root.style.maxHeight = height;\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n    return new MDCMenuSurfaceFoundation(adapter);\n  };\n  return MDCMenuSurface;\n}(MDCComponent);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses = {\n  MENU_SELECTED_LIST_ITEM: 'mdc-menu-item--selected',\n  MENU_SELECTION_GROUP: 'mdc-menu__selection-group',\n  ROOT: 'mdc-menu'\n};\nvar strings = {\n  ARIA_CHECKED_ATTR: 'aria-checked',\n  ARIA_DISABLED_ATTR: 'aria-disabled',\n  CHECKBOX_SELECTOR: 'input[type=\"checkbox\"]',\n  LIST_SELECTOR: '.mdc-list,.mdc-deprecated-list',\n  SELECTED_EVENT: 'MDCMenu:selected',\n  SKIP_RESTORE_FOCUS: 'data-menu-item-skip-restore-focus'\n};\nvar numbers = {\n  FOCUS_ROOT_INDEX: -1\n};\nvar DefaultFocusState;\n(function (DefaultFocusState) {\n  DefaultFocusState[DefaultFocusState[\"NONE\"] = 0] = \"NONE\";\n  DefaultFocusState[DefaultFocusState[\"LIST_ROOT\"] = 1] = \"LIST_ROOT\";\n  DefaultFocusState[DefaultFocusState[\"FIRST_ITEM\"] = 2] = \"FIRST_ITEM\";\n  DefaultFocusState[DefaultFocusState[\"LAST_ITEM\"] = 3] = \"LAST_ITEM\";\n})(DefaultFocusState || (DefaultFocusState = {}));\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCMenuFoundation = /** @class */function (_super) {\n  __extends(MDCMenuFoundation, _super);\n  function MDCMenuFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCMenuFoundation.defaultAdapter), adapter)) || this;\n    _this.closeAnimationEndTimerId = 0;\n    _this.defaultFocusState = DefaultFocusState.LIST_ROOT;\n    _this.selectedIndex = -1;\n    return _this;\n  }\n  Object.defineProperty(MDCMenuFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenuFoundation, \"strings\", {\n    get: function () {\n      return strings;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenuFoundation, \"numbers\", {\n    get: function () {\n      return numbers;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenuFoundation, \"defaultAdapter\", {\n    /**\n     * @see {@link MDCMenuAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        addClassToElementAtIndex: function () {\n          return undefined;\n        },\n        removeClassFromElementAtIndex: function () {\n          return undefined;\n        },\n        addAttributeToElementAtIndex: function () {\n          return undefined;\n        },\n        removeAttributeFromElementAtIndex: function () {\n          return undefined;\n        },\n        getAttributeFromElementAtIndex: function () {\n          return null;\n        },\n        elementContainsClass: function () {\n          return false;\n        },\n        closeSurface: function () {\n          return undefined;\n        },\n        getElementIndex: function () {\n          return -1;\n        },\n        notifySelected: function () {\n          return undefined;\n        },\n        getMenuItemCount: function () {\n          return 0;\n        },\n        focusItemAtIndex: function () {\n          return undefined;\n        },\n        focusListRoot: function () {\n          return undefined;\n        },\n        getSelectedSiblingOfItemAtIndex: function () {\n          return -1;\n        },\n        isSelectableItemAtIndex: function () {\n          return false;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCMenuFoundation.prototype.destroy = function () {\n    if (this.closeAnimationEndTimerId) {\n      clearTimeout(this.closeAnimationEndTimerId);\n    }\n    this.adapter.closeSurface();\n  };\n  MDCMenuFoundation.prototype.handleKeydown = function (evt) {\n    var key = evt.key,\n      keyCode = evt.keyCode;\n    var isTab = key === 'Tab' || keyCode === 9;\n    if (isTab) {\n      this.adapter.closeSurface( /** skipRestoreFocus */true);\n    }\n  };\n  MDCMenuFoundation.prototype.handleItemAction = function (listItem) {\n    var _this = this;\n    var index = this.adapter.getElementIndex(listItem);\n    if (index < 0) {\n      return;\n    }\n    this.adapter.notifySelected({\n      index: index\n    });\n    var skipRestoreFocus = this.adapter.getAttributeFromElementAtIndex(index, strings.SKIP_RESTORE_FOCUS) === 'true';\n    this.adapter.closeSurface(skipRestoreFocus);\n    // Wait for the menu to close before adding/removing classes that affect styles.\n    this.closeAnimationEndTimerId = setTimeout(function () {\n      // Recompute the index in case the menu contents have changed.\n      var recomputedIndex = _this.adapter.getElementIndex(listItem);\n      if (recomputedIndex >= 0 && _this.adapter.isSelectableItemAtIndex(recomputedIndex)) {\n        _this.setSelectedIndex(recomputedIndex);\n      }\n    }, MDCMenuSurfaceFoundation.numbers.TRANSITION_CLOSE_DURATION);\n  };\n  MDCMenuFoundation.prototype.handleMenuSurfaceOpened = function () {\n    switch (this.defaultFocusState) {\n      case DefaultFocusState.FIRST_ITEM:\n        this.adapter.focusItemAtIndex(0);\n        break;\n      case DefaultFocusState.LAST_ITEM:\n        this.adapter.focusItemAtIndex(this.adapter.getMenuItemCount() - 1);\n        break;\n      case DefaultFocusState.NONE:\n        // Do nothing.\n        break;\n      default:\n        this.adapter.focusListRoot();\n        break;\n    }\n  };\n  /**\n   * Sets default focus state where the menu should focus every time when menu\n   * is opened. Focuses the list root (`DefaultFocusState.LIST_ROOT`) element by\n   * default.\n   */\n  MDCMenuFoundation.prototype.setDefaultFocusState = function (focusState) {\n    this.defaultFocusState = focusState;\n  };\n  /** @return Index of the currently selected list item within the menu. */\n  MDCMenuFoundation.prototype.getSelectedIndex = function () {\n    return this.selectedIndex;\n  };\n  /**\n   * Selects the list item at `index` within the menu.\n   * @param index Index of list item within the menu.\n   */\n  MDCMenuFoundation.prototype.setSelectedIndex = function (index) {\n    this.validatedIndex(index);\n    if (!this.adapter.isSelectableItemAtIndex(index)) {\n      throw new Error('MDCMenuFoundation: No selection group at specified index.');\n    }\n    var prevSelectedIndex = this.adapter.getSelectedSiblingOfItemAtIndex(index);\n    if (prevSelectedIndex >= 0) {\n      this.adapter.removeAttributeFromElementAtIndex(prevSelectedIndex, strings.ARIA_CHECKED_ATTR);\n      this.adapter.removeClassFromElementAtIndex(prevSelectedIndex, cssClasses.MENU_SELECTED_LIST_ITEM);\n    }\n    this.adapter.addClassToElementAtIndex(index, cssClasses.MENU_SELECTED_LIST_ITEM);\n    this.adapter.addAttributeToElementAtIndex(index, strings.ARIA_CHECKED_ATTR, 'true');\n    this.selectedIndex = index;\n  };\n  /**\n   * Sets the enabled state to isEnabled for the menu item at the given index.\n   * @param index Index of the menu item\n   * @param isEnabled The desired enabled state of the menu item.\n   */\n  MDCMenuFoundation.prototype.setEnabled = function (index, isEnabled) {\n    this.validatedIndex(index);\n    if (isEnabled) {\n      this.adapter.removeClassFromElementAtIndex(index, cssClasses$2.LIST_ITEM_DISABLED_CLASS);\n      this.adapter.addAttributeToElementAtIndex(index, strings.ARIA_DISABLED_ATTR, 'false');\n    } else {\n      this.adapter.addClassToElementAtIndex(index, cssClasses$2.LIST_ITEM_DISABLED_CLASS);\n      this.adapter.addAttributeToElementAtIndex(index, strings.ARIA_DISABLED_ATTR, 'true');\n    }\n  };\n  MDCMenuFoundation.prototype.validatedIndex = function (index) {\n    var menuSize = this.adapter.getMenuItemCount();\n    var isIndexInRange = index >= 0 && index < menuSize;\n    if (!isIndexInRange) {\n      throw new Error('MDCMenuFoundation: No list item at specified index.');\n    }\n  };\n  return MDCMenuFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCMenu = /** @class */function (_super) {\n  __extends(MDCMenu, _super);\n  function MDCMenu() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCMenu.attachTo = function (root) {\n    return new MDCMenu(root);\n  };\n  MDCMenu.prototype.initialize = function (menuSurfaceFactory, listFactory) {\n    if (menuSurfaceFactory === void 0) {\n      menuSurfaceFactory = function (el) {\n        return new MDCMenuSurface(el);\n      };\n    }\n    if (listFactory === void 0) {\n      listFactory = function (el) {\n        return new MDCList(el);\n      };\n    }\n    this.menuSurfaceFactory = menuSurfaceFactory;\n    this.listFactory = listFactory;\n  };\n  MDCMenu.prototype.initialSyncWithDOM = function () {\n    var _this = this;\n    this.menuSurface = this.menuSurfaceFactory(this.root);\n    var list = this.root.querySelector(strings.LIST_SELECTOR);\n    if (list) {\n      this.list = this.listFactory(list);\n      this.list.wrapFocus = true;\n    } else {\n      this.list = null;\n    }\n    this.handleKeydown = function (evt) {\n      _this.foundation.handleKeydown(evt);\n    };\n    this.handleItemAction = function (evt) {\n      _this.foundation.handleItemAction(_this.items[evt.detail.index]);\n    };\n    this.handleMenuSurfaceOpened = function () {\n      _this.foundation.handleMenuSurfaceOpened();\n    };\n    this.menuSurface.listen(MDCMenuSurfaceFoundation.strings.OPENED_EVENT, this.handleMenuSurfaceOpened);\n    this.listen('keydown', this.handleKeydown);\n    this.listen(MDCListFoundation.strings.ACTION_EVENT, this.handleItemAction);\n  };\n  MDCMenu.prototype.destroy = function () {\n    if (this.list) {\n      this.list.destroy();\n    }\n    this.menuSurface.destroy();\n    this.menuSurface.unlisten(MDCMenuSurfaceFoundation.strings.OPENED_EVENT, this.handleMenuSurfaceOpened);\n    this.unlisten('keydown', this.handleKeydown);\n    this.unlisten(MDCListFoundation.strings.ACTION_EVENT, this.handleItemAction);\n    _super.prototype.destroy.call(this);\n  };\n  Object.defineProperty(MDCMenu.prototype, \"open\", {\n    get: function () {\n      return this.menuSurface.isOpen();\n    },\n    set: function (value) {\n      if (value) {\n        this.menuSurface.open();\n      } else {\n        this.menuSurface.close();\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenu.prototype, \"wrapFocus\", {\n    get: function () {\n      return this.list ? this.list.wrapFocus : false;\n    },\n    set: function (value) {\n      if (this.list) {\n        this.list.wrapFocus = value;\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenu.prototype, \"hasTypeahead\", {\n    /**\n     * Sets whether the menu has typeahead functionality.\n     * @param value Whether typeahead is enabled.\n     */\n    set: function (value) {\n      if (this.list) {\n        this.list.hasTypeahead = value;\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenu.prototype, \"typeaheadInProgress\", {\n    /**\n     * @return Whether typeahead logic is currently matching some user prefix.\n     */\n    get: function () {\n      return this.list ? this.list.typeaheadInProgress : false;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Given the next desired character from the user, adds it to the typeahead\n   * buffer. Then, attempts to find the next option matching the buffer. Wraps\n   * around if at the end of options.\n   *\n   * @param nextChar The next character to add to the prefix buffer.\n   * @param startingIndex The index from which to start matching. Only relevant\n   *     when starting a new match sequence. To start a new match sequence,\n   *     clear the buffer using `clearTypeaheadBuffer`, or wait for the buffer\n   *     to clear after a set interval defined in list foundation. Defaults to\n   *     the currently focused index.\n   * @return The index of the matched item, or -1 if no match.\n   */\n  MDCMenu.prototype.typeaheadMatchItem = function (nextChar, startingIndex) {\n    if (this.list) {\n      return this.list.typeaheadMatchItem(nextChar, startingIndex);\n    }\n    return -1;\n  };\n  /**\n   * Layout the underlying list element in the case of any dynamic updates\n   * to its structure.\n   */\n  MDCMenu.prototype.layout = function () {\n    if (this.list) {\n      this.list.layout();\n    }\n  };\n  Object.defineProperty(MDCMenu.prototype, \"items\", {\n    /**\n     * Return the items within the menu. Note that this only contains the set of elements within\n     * the items container that are proper list items, and not supplemental / presentational DOM\n     * elements.\n     */\n    get: function () {\n      return this.list ? this.list.listElements : [];\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenu.prototype, \"singleSelection\", {\n    /**\n     * Turns on/off the underlying list's single selection mode. Used mainly\n     * by select menu.\n     *\n     * @param singleSelection Whether to enable single selection mode.\n     */\n    set: function (singleSelection) {\n      if (this.list) {\n        this.list.singleSelection = singleSelection;\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenu.prototype, \"selectedIndex\", {\n    /**\n     * Retrieves the selected index. Only applicable to select menus.\n     * @return The selected index, which is a number for single selection and\n     *     radio lists, and an array of numbers for checkbox lists.\n     */\n    get: function () {\n      return this.list ? this.list.selectedIndex : numbers$2.UNSET_INDEX;\n    },\n    /**\n     * Sets the selected index of the list. Only applicable to select menus.\n     * @param index The selected index, which is a number for single selection and\n     *     radio lists, and an array of numbers for checkbox lists.\n     */\n    set: function (index) {\n      if (this.list) {\n        this.list.selectedIndex = index;\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCMenu.prototype, \"quickOpen\", {\n    set: function (quickOpen) {\n      this.menuSurface.quickOpen = quickOpen;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Sets default focus state where the menu should focus every time when menu\n   * is opened. Focuses the list root (`DefaultFocusState.LIST_ROOT`) element by\n   * default.\n   * @param focusState Default focus state.\n   */\n  MDCMenu.prototype.setDefaultFocusState = function (focusState) {\n    this.foundation.setDefaultFocusState(focusState);\n  };\n  /**\n   * @param corner Default anchor corner alignment of top-left menu corner.\n   */\n  MDCMenu.prototype.setAnchorCorner = function (corner) {\n    this.menuSurface.setAnchorCorner(corner);\n  };\n  MDCMenu.prototype.setAnchorMargin = function (margin) {\n    this.menuSurface.setAnchorMargin(margin);\n  };\n  /**\n   * Sets the list item as the selected row at the specified index.\n   * @param index Index of list item within menu.\n   */\n  MDCMenu.prototype.setSelectedIndex = function (index) {\n    this.foundation.setSelectedIndex(index);\n  };\n  /**\n   * Sets the enabled state to isEnabled for the menu item at the given index.\n   * @param index Index of the menu item\n   * @param isEnabled The desired enabled state of the menu item.\n   */\n  MDCMenu.prototype.setEnabled = function (index, isEnabled) {\n    this.foundation.setEnabled(index, isEnabled);\n  };\n  /**\n   * @return The item within the menu at the index specified.\n   */\n  MDCMenu.prototype.getOptionByIndex = function (index) {\n    var items = this.items;\n    if (index < items.length) {\n      return this.items[index];\n    } else {\n      return null;\n    }\n  };\n  /**\n   * @param index A menu item's index.\n   * @return The primary text within the menu at the index specified.\n   */\n  MDCMenu.prototype.getPrimaryTextAtIndex = function (index) {\n    var item = this.getOptionByIndex(index);\n    if (item && this.list) {\n      return this.list.getPrimaryText(item) || '';\n    }\n    return '';\n  };\n  MDCMenu.prototype.setFixedPosition = function (isFixed) {\n    this.menuSurface.setFixedPosition(isFixed);\n  };\n  MDCMenu.prototype.setIsHoisted = function (isHoisted) {\n    this.menuSurface.setIsHoisted(isHoisted);\n  };\n  MDCMenu.prototype.setAbsolutePosition = function (x, y) {\n    this.menuSurface.setAbsolutePosition(x, y);\n  };\n  /**\n   * Sets the element that the menu-surface is anchored to.\n   */\n  MDCMenu.prototype.setAnchorElement = function (element) {\n    this.menuSurface.anchorElement = element;\n  };\n  MDCMenu.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = {\n      addClassToElementAtIndex: function (index, className) {\n        var list = _this.items;\n        list[index].classList.add(className);\n      },\n      removeClassFromElementAtIndex: function (index, className) {\n        var list = _this.items;\n        list[index].classList.remove(className);\n      },\n      addAttributeToElementAtIndex: function (index, attr, value) {\n        var list = _this.items;\n        list[index].setAttribute(attr, value);\n      },\n      removeAttributeFromElementAtIndex: function (index, attr) {\n        var list = _this.items;\n        list[index].removeAttribute(attr);\n      },\n      getAttributeFromElementAtIndex: function (index, attr) {\n        var list = _this.items;\n        return list[index].getAttribute(attr);\n      },\n      elementContainsClass: function (element, className) {\n        return element.classList.contains(className);\n      },\n      closeSurface: function (skipRestoreFocus) {\n        _this.menuSurface.close(skipRestoreFocus);\n      },\n      getElementIndex: function (element) {\n        return _this.items.indexOf(element);\n      },\n      notifySelected: function (evtData) {\n        _this.emit(strings.SELECTED_EVENT, {\n          index: evtData.index,\n          item: _this.items[evtData.index]\n        });\n      },\n      getMenuItemCount: function () {\n        return _this.items.length;\n      },\n      focusItemAtIndex: function (index) {\n        _this.items[index].focus();\n      },\n      focusListRoot: function () {\n        _this.root.querySelector(strings.LIST_SELECTOR).focus();\n      },\n      isSelectableItemAtIndex: function (index) {\n        return !!closest(_this.items[index], \".\" + cssClasses.MENU_SELECTION_GROUP);\n      },\n      getSelectedSiblingOfItemAtIndex: function (index) {\n        var selectionGroupEl = closest(_this.items[index], \".\" + cssClasses.MENU_SELECTION_GROUP);\n        var selectedItemEl = selectionGroupEl.querySelector(\".\" + cssClasses.MENU_SELECTED_LIST_ITEM);\n        return selectedItemEl ? _this.items.indexOf(selectedItemEl) : -1;\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n    return new MDCMenuFoundation(adapter);\n  };\n  return MDCMenu;\n}(MDCComponent);\nexport { Corner as C, DefaultFocusState as D, KEY as K, MDCMenu as M, strings as a, MDCMenuSurface as b, normalizeKey as n, strings$1 as s };\n\n"], "mappings": ";;;;;;;;;;;;;;;;AA0BA,IAAI;AAAJ,IAAQ;AACR,IAAI,eAAe;AAAA,EACjB,2BAA2B;AAAA,EAC3B,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,MAAM;AACR;AACA,IAAI,yBAAyB,KAAK,CAAC,GAAG,GAAG,KAAK,aAAa,yBAAyB,IAAI,4BAA4B,GAAG,KAAK,aAAa,eAAe,IAAI,iBAAiB,GAAG,KAAK,aAAa,wBAAwB,IAAI,2BAA2B,GAAG,KAAK,aAAa,wBAAwB,IAAI,2BAA2B,GAAG,KAAK,aAAa,4BAA4B,IAAI,+BAA+B,GAAG,KAAK,aAAa,IAAI,IAAI,YAAY;AAClc,IAAI,0BAA0B,KAAK,CAAC,GAAG,GAAG,KAAK,aAAa,yBAAyB,IAAI,uCAAuC,GAAG,KAAK,aAAa,eAAe,IAAI,4BAA4B,GAAG,KAAK,aAAa,wBAAwB,IAAI,sCAAsC,GAAG,KAAK,aAAa,wBAAwB,IAAI,sCAAsC,GAAG,KAAK,aAAa,oBAAoB,IAAI,kCAAkC,GAAG,KAAK,aAAa,4BAA4B,IAAI,0CAA0C,GAAG,KAAK,aAAa,IAAI,IAAI,uBAAuB;AACplB,IAAI,YAAY;AAAA,EACd,cAAc;AAAA,EACd,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,cAAc;AAAA,EACd,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,eAAe;AAAA,EACf,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,mCAAmC,YAAY,aAAa,kBAAkB,mCAAmC,aAAa,kBAAkB,eAAe,uBAAuB,aAAa,eAAe,IAAI,mCAAmC,uBAAuB,aAAa,eAAe,IAAI;AAAA,EAChT,qBAAqB;AAAA,EACrB,0BAA0B,YAAY,aAAa,kBAAkB,mCAAmC,aAAa,kBAAkB,eAAe,aAAa,kBAAkB,gDAAkD,aAAa,kBAAkB,mDAAqD,uBAAuB,aAAa,eAAe,IAAI,mCAAmC,uBAAuB,aAAa,eAAe,IAAI,eAAe,uBAAuB,aAAa,eAAe,IAAI,gDAAkD,uBAAuB,aAAa,eAAe,IAAI;AAAA,EAC3nB,gBAAgB;AAAA,EAChB,wBAAwB;AAC1B;AACA,IAAI,YAAY;AAAA,EACd,aAAa;AAAA,EACb,mCAAmC;AACrC;AACA,IAAI,qBAAqB;AA2BzB,IAAI,MAAM;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,iBAAiB,oBAAI,IAAI;AAG7B,eAAe,IAAI,IAAI,SAAS;AAChC,eAAe,IAAI,IAAI,KAAK;AAC5B,eAAe,IAAI,IAAI,QAAQ;AAC/B,eAAe,IAAI,IAAI,OAAO;AAC9B,eAAe,IAAI,IAAI,SAAS;AAChC,eAAe,IAAI,IAAI,GAAG;AAC1B,eAAe,IAAI,IAAI,IAAI;AAC3B,eAAe,IAAI,IAAI,UAAU;AACjC,eAAe,IAAI,IAAI,QAAQ;AAC/B,eAAe,IAAI,IAAI,WAAW;AAClC,eAAe,IAAI,IAAI,UAAU;AACjC,eAAe,IAAI,IAAI,MAAM;AAC7B,eAAe,IAAI,IAAI,MAAM;AAC7B,eAAe,IAAI,IAAI,GAAG;AAC1B,IAAI,WAAW;AAAA,EACb,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAI,iBAAiB,oBAAI,IAAI;AAG7B,eAAe,IAAI,SAAS,WAAW,IAAI,SAAS;AACpD,eAAe,IAAI,SAAS,OAAO,IAAI,KAAK;AAC5C,eAAe,IAAI,SAAS,UAAU,IAAI,QAAQ;AAClD,eAAe,IAAI,SAAS,SAAS,IAAI,OAAO;AAChD,eAAe,IAAI,SAAS,WAAW,IAAI,SAAS;AACpD,eAAe,IAAI,SAAS,KAAK,IAAI,GAAG;AACxC,eAAe,IAAI,SAAS,MAAM,IAAI,IAAI;AAC1C,eAAe,IAAI,SAAS,YAAY,IAAI,UAAU;AACtD,eAAe,IAAI,SAAS,UAAU,IAAI,QAAQ;AAClD,eAAe,IAAI,SAAS,aAAa,IAAI,WAAW;AACxD,eAAe,IAAI,SAAS,YAAY,IAAI,UAAU;AACtD,eAAe,IAAI,SAAS,QAAQ,IAAI,MAAM;AAC9C,eAAe,IAAI,SAAS,QAAQ,IAAI,MAAM;AAC9C,eAAe,IAAI,SAAS,KAAK,IAAI,GAAG;AACxC,IAAI,iBAAiB,oBAAI,IAAI;AAG7B,eAAe,IAAI,IAAI,OAAO;AAC9B,eAAe,IAAI,IAAI,SAAS;AAChC,eAAe,IAAI,IAAI,GAAG;AAC1B,eAAe,IAAI,IAAI,IAAI;AAC3B,eAAe,IAAI,IAAI,UAAU;AACjC,eAAe,IAAI,IAAI,QAAQ;AAC/B,eAAe,IAAI,IAAI,WAAW;AAClC,eAAe,IAAI,IAAI,UAAU;AAIjC,SAAS,aAAa,KAAK;AACzB,MAAI,MAAM,IAAI;AAEd,MAAI,eAAe,IAAI,GAAG,GAAG;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,eAAe,IAAI,IAAI,OAAO;AAC9C,MAAI,WAAW;AACb,WAAO;AAAA,EACT;AACA,SAAO,IAAI;AACb;AAwBA,IAAI,0BAA0B,CAAC,SAAS,UAAU,YAAY,QAAQ;AAOtE,IAAI,sBAAsB,SAAU,KAAK;AACvC,MAAI,SAAS,IAAI;AACjB,MAAI,CAAC,QAAQ;AACX;AAAA,EACF;AACA,MAAI,WAAW,KAAK,OAAO,SAAS,YAAY;AAChD,MAAI,wBAAwB,QAAQ,OAAO,MAAM,IAAI;AACnD,QAAI,eAAe;AAAA,EACrB;AACF;AAgCA,SAAS,YAAY;AACnB,MAAI,QAAQ;AAAA,IACV,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,EACnB;AACA,SAAO;AACT;AAYA,SAAS,gBAAgB,eAAe,2BAA2B;AACjE,MAAI,yBAAyB,oBAAI,IAAI;AAErC,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,QAAI,cAAc,0BAA0B,CAAC,EAAE,KAAK;AACpD,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,QAAI,YAAY,YAAY,CAAC,EAAE,YAAY;AAC3C,QAAI,CAAC,uBAAuB,IAAI,SAAS,GAAG;AAC1C,6BAAuB,IAAI,WAAW,CAAC,CAAC;AAAA,IAC1C;AACA,2BAAuB,IAAI,SAAS,EAAE,KAAK;AAAA,MACzC,MAAM,YAAY,YAAY;AAAA,MAC9B,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAGA,yBAAuB,QAAQ,SAAU,QAAQ;AAC/C,WAAO,KAAK,SAAU,OAAO,QAAQ;AACnC,aAAO,MAAM,QAAQ,OAAO;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAiBA,SAAS,UAAU,MAAM,OAAO;AAC9B,MAAI,WAAW,KAAK,UAClB,mBAAmB,KAAK,kBACxB,yBAAyB,KAAK,wBAC9B,mBAAmB,KAAK,kBACxB,YAAY,KAAK,WACjB,wBAAwB,KAAK;AAC/B,eAAa,MAAM,kBAAkB;AACrC,QAAM,qBAAqB,WAAW,WAAY;AAChD,gBAAY,KAAK;AAAA,EACnB,GAAG,UAAU,iCAAiC;AAC9C,QAAM,kBAAkB,MAAM,kBAAkB;AAChD,MAAI;AACJ,MAAI,MAAM,gBAAgB,WAAW,GAAG;AACtC,YAAQ,eAAe,wBAAwB,kBAAkB,uBAAuB,KAAK;AAAA,EAC/F,OAAO;AACL,YAAQ,cAAc,wBAAwB,uBAAuB,KAAK;AAAA,EAC5E;AACA,MAAI,UAAU,MAAM,CAAC,WAAW;AAC9B,qBAAiB,KAAK;AAAA,EACxB;AACA,SAAO;AACT;AAMA,SAAS,eAAe,wBAAwB,kBAAkB,uBAAuB,OAAO;AAC9F,MAAI,YAAY,MAAM,gBAAgB,CAAC;AACvC,MAAI,yBAAyB,uBAAuB,IAAI,SAAS;AACjE,MAAI,CAAC,wBAAwB;AAC3B,WAAO;AAAA,EACT;AAIA,MAAI,cAAc,MAAM,oBAAoB,uBAAuB,MAAM,iBAAiB,EAAE,UAAU,kBAAkB;AACtH,UAAM,qBAAqB,MAAM,oBAAoB,KAAK,uBAAuB;AACjF,QAAI,WAAW,uBAAuB,MAAM,iBAAiB,EAAE;AAC/D,QAAI,CAAC,sBAAsB,QAAQ,GAAG;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AAKA,QAAM,mBAAmB;AACzB,MAAI,oBAAoB;AACxB,MAAI;AAEJ,OAAK,iBAAiB,GAAG,iBAAiB,uBAAuB,QAAQ,kBAAkB;AACzF,QAAI,CAAC,sBAAsB,uBAAuB,cAAc,EAAE,KAAK,GAAG;AACxE,0BAAoB;AACpB;AAAA,IACF;AAAA,EACF;AAIA,SAAO,iBAAiB,uBAAuB,QAAQ,kBAAkB;AACvE,QAAI,uBAAuB,cAAc,EAAE,QAAQ,oBAAoB,CAAC,sBAAsB,uBAAuB,cAAc,EAAE,KAAK,GAAG;AAC3I,0BAAoB;AACpB;AAAA,IACF;AAAA,EACF;AACA,MAAI,sBAAsB,IAAI;AAC5B,UAAM,oBAAoB;AAC1B,WAAO,uBAAuB,MAAM,iBAAiB,EAAE;AAAA,EACzD;AACA,SAAO;AACT;AAKA,SAAS,cAAc,wBAAwB,uBAAuB,OAAO;AAC3E,MAAI,YAAY,MAAM,gBAAgB,CAAC;AACvC,MAAI,yBAAyB,uBAAuB,IAAI,SAAS;AACjE,MAAI,CAAC,wBAAwB;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,uBAAuB,MAAM,iBAAiB;AACjE,MAAI,aAAa,KAAK,YAAY,MAAM,iBAAiB,CAAC,MAAM,KAAK,CAAC,sBAAsB,aAAa,KAAK,GAAG;AAC/G,WAAO,aAAa;AAAA,EACtB;AAGA,MAAI,kBAAkB,MAAM,oBAAoB,KAAK,uBAAuB;AAC5E,MAAI,qBAAqB;AACzB,SAAO,mBAAmB,MAAM,mBAAmB;AACjD,QAAI,cAAc,uBAAuB,cAAc;AACvD,QAAIA,WAAU,YAAY,KAAK,YAAY,MAAM,iBAAiB,CAAC,MAAM;AACzE,QAAI,YAAY,CAAC,sBAAsB,YAAY,KAAK;AACxD,QAAIA,YAAW,WAAW;AACxB,2BAAqB;AACrB;AAAA,IACF;AACA,sBAAkB,iBAAiB,KAAK,uBAAuB;AAAA,EACjE;AACA,MAAI,uBAAuB,IAAI;AAC7B,UAAM,oBAAoB;AAC1B,WAAO,uBAAuB,MAAM,iBAAiB,EAAE;AAAA,EACzD;AACA,SAAO;AACT;AAMA,SAAS,mBAAmB,OAAO;AACjC,SAAO,MAAM,gBAAgB,SAAS;AACxC;AAOA,SAAS,YAAY,OAAO;AAC1B,QAAM,kBAAkB;AAC1B;AAkBA,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,QAAQ,KAAK,OACf,mBAAmB,KAAK,kBACxB,mBAAmB,KAAK,kBACxB,mBAAmB,KAAK,kBACxB,yBAAyB,KAAK,wBAC9B,wBAAwB,KAAK;AAC/B,MAAI,cAAc,aAAa,KAAK,MAAM;AAC1C,MAAI,YAAY,aAAa,KAAK,MAAM;AACxC,MAAI,eAAe,aAAa,KAAK,MAAM;AAC3C,MAAI,cAAc,aAAa,KAAK,MAAM;AAC1C,MAAI,SAAS,aAAa,KAAK,MAAM;AACrC,MAAI,QAAQ,aAAa,KAAK,MAAM;AACpC,MAAI,UAAU,aAAa,KAAK,MAAM;AACtC,MAAI,UAAU,aAAa,KAAK,MAAM;AACtC,MAAI,MAAM,UAAU,MAAM,WAAW,MAAM,WAAW,eAAe,aAAa,gBAAgB,eAAe,UAAU,SAAS,SAAS;AAC3I,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,CAAC,WAAW,MAAM,IAAI,WAAW;AACtD,MAAI,gBAAgB;AAClB,wBAAoB,KAAK;AACzB,QAAI,gBAAgB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,UAAU,MAAM,IAAI,YAAY;AAAA,MAChC;AAAA,MACA,WAAW;AAAA,MACX;AAAA,IACF;AACA,WAAO,UAAU,eAAe,KAAK;AAAA,EACvC;AACA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB;AACpB,wBAAoB,KAAK;AAAA,EAC3B;AACA,MAAI,sBAAsB,oBAAoB,mBAAmB,KAAK;AACtE,MAAI,qBAAqB;AACvB,QAAI,gBAAgB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA,WAAW;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU,eAAe,KAAK;AAAA,EACvC;AACA,SAAO;AACT;AAwBA,SAAS,cAAc,eAAe;AACpC,SAAO,yBAAyB;AAClC;AAEA,IAAI,sBAAsB,CAAC,OAAO,WAAW,QAAQ,OAAO;AAE5D,SAAS,sBAAsB,OAAO;AACpC,MAAI,iBAAiB,IAAI,IAAI,QAAQ,oBAAoB,OAAO,SAAU,GAAG;AAC3E,WAAO,MAAM,iBAAiB,CAAC;AAAA,EACjC,CAAC,IAAI,CAAC,CAAC;AACP,SAAO,SAAU,WAAW;AAC1B,WAAO,UAAU,MAAM,SAAU,GAAG;AAClC,aAAO,eAAe,IAAI,CAAC;AAAA,IAC7B,CAAC,KAAK,UAAU,WAAW,eAAe;AAAA,EAC5C;AACF;AACA,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,SAAS;AAClC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,mBAAkB,cAAc,GAAG,OAAO,CAAC,KAAK;AACpG,YAAM,YAAY;AAClB,YAAM,aAAa;AACnB,YAAM,wBAAwB;AAC9B,YAAM,4BAA4B;AAClC,YAAM,gBAAgB,UAAU;AAChC,YAAM,mBAAmB,UAAU;AACnC,YAAM,oBAAoB;AAC1B,YAAM,kBAAkB;AACxB,YAAM,uBAAuB;AAC7B,YAAM,iBAAiB;AACvB,YAAM,cAAc;AACpB,YAAM,oBAAoB;AAC1B,YAAM,eAAe;AAErB,YAAM,iBAAiB,UAAU;AACjC,YAAM,yBAAyB,oBAAI,IAAI;AACvC,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,oBAAmB,WAAW;AAAA,MAClD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,oBAAmB,cAAc;AAAA,MACrD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,oBAAmB,WAAW;AAAA,MAClD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,oBAAmB,kBAAkB;AAAA,MACzD,KAAK,WAAY;AACf,eAAO;AAAA,UACL,yBAAyB,WAAY;AACnC,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,6BAA6B,WAAY;AACvC,mBAAO;AAAA,UACT;AAAA,UACA,wBAAwB,WAAY;AAClC,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,0BAA0B,WAAY;AACpC,mBAAO;AAAA,UACT;AAAA,UACA,mBAAmB,WAAY;AAC7B,mBAAO;AAAA,UACT;AAAA,UACA,eAAe,WAAY;AACzB,mBAAO;AAAA,UACT;AAAA,UACA,yBAAyB,WAAY;AACnC,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,uBAAuB,WAAY;AAAA,UAAC;AAAA,UACpC,4BAA4B,WAAY;AACtC,mBAAO;AAAA,UACT;AAAA,UACA,6BAA6B,WAAY;AACvC,mBAAO;AAAA,UACT;AAAA,UACA,kCAAkC,WAAY;AAC5C,mBAAO;AAAA,UACT;AAAA,UACA,gCAAgC,WAAY;AAC1C,mBAAO;AAAA,UACT;AAAA,UACA,uBAAuB,WAAY;AACjC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,mBAAkB,UAAU,SAAS,WAAY;AAC/C,UAAI,KAAK,QAAQ,iBAAiB,MAAM,GAAG;AACzC;AAAA,MACF;AAEA,UAAI,KAAK,QAAQ,mBAAmB,CAAC,GAAG;AACtC,aAAK,iBAAiB;AAAA,MACxB,WAAW,KAAK,QAAQ,gBAAgB,CAAC,GAAG;AAC1C,aAAK,cAAc;AAAA,MACrB,OAAO;AACL,aAAK,+BAA+B;AAAA,MACtC;AACA,UAAI,KAAK,cAAc;AACrB,aAAK,yBAAyB,KAAK,yBAAyB;AAAA,MAC9D;AAAA,IACF;AAEA,IAAAA,mBAAkB,UAAU,sBAAsB,WAAY;AAC5D,aAAO,KAAK;AAAA,IACd;AAEA,IAAAA,mBAAkB,UAAU,eAAe,SAAU,OAAO;AAC1D,WAAK,YAAY;AAAA,IACnB;AAKA,IAAAA,mBAAkB,UAAU,yBAAyB,SAAU,OAAO;AACpE,WAAK,aAAa;AAAA,IACpB;AAEA,IAAAA,mBAAkB,UAAU,qBAAqB,SAAU,OAAO;AAChE,WAAK,wBAAwB;AAC7B,UAAI,OAAO;AACT,aAAK,+BAA+B;AACpC,aAAK,gBAAgB,KAAK,wBAAwB;AAAA,MACpD;AAAA,IACF;AACA,IAAAA,mBAAkB,UAAU,4BAA4B,SAAU,OAAO;AACvE,WAAK,4BAA4B;AAAA,IACnC;AAKA,IAAAA,mBAAkB,UAAU,iCAAiC,WAAY;AACvE,UAAI,oBAAoB,KAAK,wBAAwB;AACrD,UAAI,sBAAsB,UAAU,YAAa;AACjD,UAAI,oBAAoB,KAAK,QAAQ,wBAAwB,mBAAmB,aAAa,yBAAyB;AACtH,UAAI,mBAAmB;AACrB,aAAK,qBAAqB,IAAI;AAAA,MAChC;AACA,WAAK,wBAAwB;AAC7B,WAAK,gBAAgB;AAAA,IACvB;AAEA,IAAAA,mBAAkB,UAAU,0BAA0B,WAAY;AAChE,UAAI,gBAAgB,UAAU;AAC9B,UAAI,iBAAiB,KAAK,QAAQ,iBAAiB;AACnD,eAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,YAAI,mBAAmB,KAAK,QAAQ,wBAAwB,GAAG,aAAa,wBAAwB;AACpG,YAAI,oBAAoB,KAAK,QAAQ,wBAAwB,GAAG,aAAa,yBAAyB;AACtG,YAAI,EAAE,oBAAoB,oBAAoB;AAC5C;AAAA,QACF;AACA,wBAAgB;AAChB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,cAAc;AACpE,WAAK,eAAe;AACpB,UAAI,cAAc;AAChB,aAAK,yBAAyB,KAAK,yBAAyB;AAAA,MAC9D;AAAA,IACF;AAIA,IAAAA,mBAAkB,UAAU,wBAAwB,WAAY;AAC9D,aAAO,KAAK,gBAAgB,mBAAmB,KAAK,cAAc;AAAA,IACpE;AAEA,IAAAA,mBAAkB,UAAU,uBAAuB,SAAU,cAAc;AACzE,WAAK,oBAAoB;AAAA,IAC3B;AAKA,IAAAA,mBAAkB,UAAU,0BAA0B,SAAU,aAAa;AAC3E,WAAK,kBAAkB;AAAA,IACzB;AACA,IAAAA,mBAAkB,UAAU,mBAAmB,WAAY;AACzD,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,mBAAkB,UAAU,mBAAmB,SAAU,OAAO,SAAS;AACvE,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,UAAI,CAAC,KAAK,aAAa,KAAK,GAAG;AAC7B;AAAA,MACF;AACA,UAAI,KAAK,gBAAgB;AACvB,aAAK,mBAAmB,OAAO,OAAO;AAAA,MACxC,WAAW,KAAK,aAAa;AAC3B,aAAK,gBAAgB,OAAO,OAAO;AAAA,MACrC,OAAO;AACL,aAAK,0BAA0B,OAAO,OAAO;AAAA,MAC/C;AAAA,IACF;AAIA,IAAAA,mBAAkB,UAAU,gBAAgB,SAAU,eAAe;AACnE,UAAI,iBAAiB,GAAG;AACtB,aAAK,mBAAmB;AACxB,aAAK,QAAQ,4BAA4B,eAAe,YAAY,GAAG;AACvE,aAAK,QAAQ,+BAA+B,eAAe,GAAG;AAAA,MAChE;AAAA,IACF;AAIA,IAAAA,mBAAkB,UAAU,iBAAiB,SAAU,eAAe;AACpE,UAAI,QAAQ;AACZ,UAAI,iBAAiB,GAAG;AACtB,aAAK,QAAQ,4BAA4B,eAAe,YAAY,IAAI;AACxE,aAAK,QAAQ,+BAA+B,eAAe,IAAI;AAAA,MACjE;AAKA,iBAAW,WAAY;AACrB,YAAI,CAAC,MAAM,QAAQ,kBAAkB,GAAG;AACtC,gBAAM,wCAAwC;AAAA,QAChD;AAAA,MACF,GAAG,CAAC;AAAA,IACN;AACA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,OAAO;AAC7D,aAAO,KAAK,QAAQ,wBAAwB,OAAO,aAAa,wBAAwB;AAAA,IAC1F;AAIA,IAAAA,mBAAkB,UAAU,gBAAgB,SAAU,OAAO,gBAAgB,eAAe;AAC1F,UAAI,QAAQ;AACZ,UAAIC;AACJ,UAAI,cAAc,aAAa,KAAK,MAAM;AAC1C,UAAI,YAAY,aAAa,KAAK,MAAM;AACxC,UAAI,eAAe,aAAa,KAAK,MAAM;AAC3C,UAAI,cAAc,aAAa,KAAK,MAAM;AAC1C,UAAI,SAAS,aAAa,KAAK,MAAM;AACrC,UAAI,QAAQ,aAAa,KAAK,MAAM;AACpC,UAAI,UAAU,aAAa,KAAK,MAAM;AACtC,UAAI,UAAU,aAAa,KAAK,MAAM;AAEtC,UAAI,YAAY,KAAK,cAAc,eAAe,CAAC,KAAK,cAAc;AACtE,UAAI,SAAS,KAAK,cAAc,aAAa,CAAC,KAAK,cAAc;AAGjE,UAAI,YAAY,MAAM,QAAQ,OAAO,MAAM,QAAQ;AACnD,UAAI,oBAAoB,sBAAsB,KAAK;AACnD,UAAI,KAAK,QAAQ,cAAc,GAAG;AAChC,aAAK,UAAU,UAAU,kBAAkB,CAAC,CAAC,GAAG;AAC9C,gBAAM,eAAe;AACrB,eAAK,iBAAiB;AAAA,QACxB,YAAY,aAAa,WAAW,kBAAkB,CAAC,CAAC,GAAG;AACzD,gBAAM,eAAe;AACrB,eAAK,kBAAkB;AAAA,QACzB,WAAW,UAAU,kBAAkB,CAAC,OAAO,CAAC,KAAK,KAAK,gBAAgB;AACxE,gBAAM,eAAe;AACrB,cAAI,eAAe,KAAK,iBAAiB;AACzC,cAAI,iBAAiB,IAAI;AACvB,iBAAK,yBAAyB,cAAc,KAAK;AAAA,UACnD;AAAA,QACF,WAAW,aAAa,kBAAkB,CAAC,OAAO,CAAC,KAAK,KAAK,gBAAgB;AAC3E,gBAAM,eAAe;AACrB,cAAI,eAAe,KAAK,kBAAkB;AAC1C,cAAI,iBAAiB,IAAI;AACvB,iBAAK,yBAAyB,cAAc,KAAK;AAAA,UACnD;AAAA,QACF;AACA,YAAI,KAAK,cAAc;AACrB,cAAI,oBAAoB;AAAA,YACtB;AAAA,YACA,kBAAkB,SAAU,OAAO;AACjC,oBAAM,iBAAiB,KAAK;AAAA,YAC9B;AAAA,YACA,kBAAkB;AAAA,YAClB,kBAAkB;AAAA,YAClB,wBAAwB,KAAK;AAAA,YAC7B,uBAAuB,SAAU,OAAO;AACtC,qBAAO,MAAM,gBAAgB,KAAK;AAAA,YACpC;AAAA,UACF;AACA,wBAAc,mBAAmB,KAAK,cAAc;AAAA,QACtD;AACA;AAAA,MACF;AACA,UAAI,eAAe,KAAK,QAAQ,uBAAuB;AACvD,UAAI,iBAAiB,IAAI;AACvB,uBAAe;AACf,YAAI,eAAe,GAAG;AAGpB;AAAA,QACF;AAAA,MACF;AACA,UAAI,aAAa,kBAAkB,CAAC,CAAC,GAAG;AACtC,4BAAoB,KAAK;AACzB,aAAK,iBAAiB,YAAY;AAAA,MACpC,WAAW,UAAU,kBAAkB,CAAC,CAAC,GAAG;AAC1C,4BAAoB,KAAK;AACzB,aAAK,iBAAiB,YAAY;AAAA,MACpC,WAAW,aAAa,kBAAkB,CAAC,OAAO,CAAC,KAAK,KAAK,gBAAgB;AAC3E,4BAAoB,KAAK;AACzB,YAAI,eAAe,KAAK,iBAAiB,YAAY;AACrD,YAAI,iBAAiB,IAAI;AACvB,eAAK,yBAAyB,cAAc,KAAK;AAAA,QACnD;AAAA,MACF,WAAW,UAAU,kBAAkB,CAAC,OAAO,CAAC,KAAK,KAAK,gBAAgB;AACxE,4BAAoB,KAAK;AACzB,YAAI,eAAe,KAAK,iBAAiB,YAAY;AACrD,YAAI,iBAAiB,IAAI;AACvB,eAAK,yBAAyB,cAAc,KAAK;AAAA,QACnD;AAAA,MACF,WAAW,UAAU,kBAAkB,CAAC,CAAC,GAAG;AAC1C,4BAAoB,KAAK;AACzB,aAAK,kBAAkB;AAAA,MACzB,WAAW,SAAS,kBAAkB,CAAC,CAAC,GAAG;AACzC,4BAAoB,KAAK;AACzB,aAAK,iBAAiB;AAAA,MACxB,WAAW,UAAU,kBAAkB,CAAC,WAAW,OAAO,CAAC,KAAK,KAAK,gBAAgB;AACnF,4BAAoB,KAAK;AACzB,YAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC;AAAA,QACF;AACA,aAAK,kBAAkB;AACvB,aAAK,oBAAoB,GAAG,cAAc,YAAY;AAAA,MACxD,WAAW,SAAS,kBAAkB,CAAC,WAAW,OAAO,CAAC,KAAK,KAAK,gBAAgB;AAClF,4BAAoB,KAAK;AACzB,YAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC;AAAA,QACF;AACA,aAAK,iBAAiB;AACtB,aAAK,oBAAoB,cAAc,KAAK,QAAQ,iBAAiB,IAAI,GAAG,YAAY;AAAA,MAC1F,WAAW,aAAa,kBAAkB,CAAC,SAAS,CAAC,KAAK,KAAK,gBAAgB;AAC7E,cAAM,eAAe;AACrB,aAAK,sBAAsB,KAAK,kBAAkB,UAAU,cAAc,CAAC,IAAI,KAAK,eAAe,IAAI;AAAA,MACzG,YAAY,WAAW,YAAY,kBAAkB,CAAC,CAAC,GAAG;AACxD,YAAI,gBAAgB;AAGlB,cAAI,SAAS,MAAM;AACnB,cAAI,UAAU,OAAO,YAAY,OAAO,SAAS;AAC/C;AAAA,UACF;AACA,8BAAoB,KAAK;AACzB,cAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC;AAAA,UACF;AACA,cAAI,CAAC,KAAK,sBAAsB,GAAG;AACjC,gBAAI,KAAK,iBAAiB,GAAG;AAC3B,mBAAK,yBAAyB,cAAc,KAAK;AAAA,YACnD;AACA,iBAAK,QAAQ,aAAa,YAAY;AAAA,UACxC;AAAA,QACF;AAAA,MACF,YAAY,WAAW,YAAY,kBAAkB,CAAC,OAAO,CAAC,KAAK,KAAK,gBAAgB;AAGtF,YAAI,SAAS,MAAM;AACnB,YAAI,UAAU,OAAO,YAAY,OAAO,SAAS;AAC/C;AAAA,QACF;AACA,4BAAoB,KAAK;AACzB,YAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC;AAAA,QACF;AACA,YAAI,CAAC,KAAK,sBAAsB,GAAG;AACjC,eAAK,qBAAqBA,MAAK,KAAK,uBAAuB,QAAQA,QAAO,SAASA,MAAK,cAAc,cAAc,YAAY;AAChI,eAAK,QAAQ,aAAa,YAAY;AAAA,QACxC;AAAA,MACF;AACA,UAAI,KAAK,cAAc;AACrB,YAAI,oBAAoB;AAAA,UACtB;AAAA,UACA,kBAAkB,SAAU,OAAO;AACjC,kBAAM,iBAAiB,KAAK;AAAA,UAC9B;AAAA,UACA,kBAAkB,KAAK;AAAA,UACvB,kBAAkB;AAAA,UAClB,wBAAwB,KAAK;AAAA,UAC7B,uBAAuB,SAAU,OAAO;AACtC,mBAAO,MAAM,gBAAgB,KAAK;AAAA,UACpC;AAAA,QACF;AACA,sBAAc,mBAAmB,KAAK,cAAc;AAAA,MACtD;AAAA,IACF;AAWA,IAAAD,mBAAkB,UAAU,cAAc,SAAU,OAAO,mCAAmC,OAAO;AACnG,UAAIC;AACJ,UAAI,oBAAoB,sBAAsB,KAAK;AACnD,UAAI,UAAU,UAAU,aAAa;AACnC;AAAA,MACF;AACA,UAAI,KAAK,gBAAgB,KAAK,GAAG;AAC/B;AAAA,MACF;AACA,UAAI,kBAAkB,CAAC,CAAC,GAAG;AACzB,YAAI,KAAK,iBAAiB,GAAG;AAC3B,eAAK,yBAAyB,OAAO,iCAAiC;AAAA,QACxE;AACA,aAAK,QAAQ,aAAa,KAAK;AAAA,MACjC,WAAW,KAAK,kBAAkB,kBAAkB,CAAC,OAAO,CAAC,GAAG;AAC9D,aAAK,qBAAqBA,MAAK,KAAK,uBAAuB,QAAQA,QAAO,SAASA,MAAK,OAAO,OAAO,KAAK;AAC3G,aAAK,QAAQ,aAAa,KAAK;AAAA,MACjC;AAAA,IACF;AAIA,IAAAD,mBAAkB,UAAU,mBAAmB,SAAU,OAAO;AAC9D,UAAI,QAAQ,KAAK,QAAQ,iBAAiB;AAC1C,UAAI,YAAY;AAChB,UAAI,eAAe;AACnB,SAAG;AACD;AACA,YAAI,aAAa,OAAO;AACtB,cAAI,KAAK,WAAW;AAClB,wBAAY;AAAA,UACd,OAAO;AAEL,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,cAAc,cAAc;AAC9B,iBAAO;AAAA,QACT;AACA,uBAAe,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe;AAAA,MACnF,SAAS,CAAC,KAAK,6BAA6B,KAAK,gBAAgB,SAAS;AAC1E,WAAK,iBAAiB,SAAS;AAC/B,aAAO;AAAA,IACT;AAIA,IAAAA,mBAAkB,UAAU,mBAAmB,SAAU,OAAO;AAC9D,UAAI,QAAQ,KAAK,QAAQ,iBAAiB;AAC1C,UAAI,YAAY;AAChB,UAAI,eAAe;AACnB,SAAG;AACD;AACA,YAAI,YAAY,GAAG;AACjB,cAAI,KAAK,WAAW;AAClB,wBAAY,QAAQ;AAAA,UACtB,OAAO;AAEL,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,cAAc,cAAc;AAC9B,iBAAO;AAAA,QACT;AACA,uBAAe,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe;AAAA,MACnF,SAAS,CAAC,KAAK,6BAA6B,KAAK,gBAAgB,SAAS;AAC1E,WAAK,iBAAiB,SAAS;AAC/B,aAAO;AAAA,IACT;AACA,IAAAA,mBAAkB,UAAU,oBAAoB,WAAY;AAG1D,aAAO,KAAK,iBAAiB,EAAE;AAAA,IACjC;AACA,IAAAA,mBAAkB,UAAU,mBAAmB,WAAY;AAGzD,aAAO,KAAK,iBAAiB,KAAK,QAAQ,iBAAiB,CAAC;AAAA,IAC9D;AACA,IAAAA,mBAAkB,UAAU,sBAAsB,WAAY;AAC5D,UAAI,eAAe,KAAK,mCAAmC;AAC3D,WAAK,iBAAiB,YAAY;AAClC,aAAO;AAAA,IACT;AAKA,IAAAA,mBAAkB,UAAU,aAAa,SAAU,WAAW,WAAW;AACvE,UAAI,CAAC,KAAK,aAAa,WAAW,KAAK,GAAG;AACxC;AAAA,MACF;AACA,UAAI,WAAW;AACb,aAAK,QAAQ,2BAA2B,WAAW,aAAa,wBAAwB;AACxF,aAAK,QAAQ,4BAA4B,WAAW,UAAU,eAAe,OAAO;AAAA,MACtF,OAAO;AACL,aAAK,QAAQ,wBAAwB,WAAW,aAAa,wBAAwB;AACrF,aAAK,QAAQ,4BAA4B,WAAW,UAAU,eAAe,MAAM;AAAA,MACrF;AAAA,IACF;AACA,IAAAA,mBAAkB,UAAU,4BAA4B,SAAU,OAAO,SAAS;AAChF,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,UAAI,KAAK,kBAAkB,SAAS,CAAC,QAAQ,aAAa;AACxD;AAAA,MACF;AACA,UAAI,oBAAoB,aAAa;AACrC,UAAI,KAAK,mBAAmB;AAC1B,4BAAoB,aAAa;AAAA,MACnC;AACA,UAAI,KAAK,kBAAkB,UAAU,aAAa;AAChD,aAAK,QAAQ,2BAA2B,KAAK,eAAe,iBAAiB;AAAA,MAC/E;AACA,WAAK,iCAAiC,KAAK;AAC3C,WAAK,mBAAmB,KAAK;AAC7B,UAAI,UAAU,UAAU,aAAa;AACnC,aAAK,QAAQ,wBAAwB,OAAO,iBAAiB;AAAA,MAC/D;AACA,WAAK,gBAAgB;AAGrB,UAAI,QAAQ,qBAAqB,CAAC,QAAQ,aAAa;AACrD,aAAK,QAAQ,sBAAsB,CAAC,KAAK,CAAC;AAAA,MAC5C;AAAA,IACF;AAIA,IAAAA,mBAAkB,UAAU,mCAAmC,SAAU,OAAO;AAG9E,UAAI,KAAK,kBAAkB,UAAU,aAAa;AAChD,aAAK,uBAAuB,KAAK,QAAQ,4BAA4B,OAAO,UAAU,YAAY;AAAA,MACpG;AACA,UAAI,gBAAgB,KAAK,yBAAyB;AAClD,UAAI,gBAAgB,gBAAgB,UAAU,eAAe,UAAU;AACvE,UAAI,KAAK,kBAAkB,UAAU,aAAa;AAChD,aAAK,QAAQ,4BAA4B,KAAK,eAAe,eAAe,OAAO;AAAA,MACrF;AACA,UAAI,UAAU,UAAU,aAAa;AACnC,YAAI,qBAAqB,gBAAgB,KAAK,uBAAuB;AACrE,aAAK,QAAQ,4BAA4B,OAAO,eAAe,kBAAkB;AAAA,MACnF;AAAA,IACF;AAIA,IAAAA,mBAAkB,UAAU,wBAAwB,WAAY;AAC9D,aAAO,KAAK,kBAAkB,UAAU,gBAAgB,UAAU;AAAA,IACpE;AAKA,IAAAA,mBAAkB,UAAU,kBAAkB,SAAU,OAAO,SAAS;AACtE,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,UAAI,qBAAqB,KAAK,sBAAsB;AACpD,WAAK,QAAQ,iCAAiC,OAAO,IAAI;AACzD,UAAI,KAAK,kBAAkB,SAAS,CAAC,QAAQ,aAAa;AACxD;AAAA,MACF;AACA,UAAI,KAAK,kBAAkB,UAAU,aAAa;AAChD,aAAK,QAAQ,4BAA4B,KAAK,eAAe,oBAAoB,OAAO;AAAA,MAC1F;AACA,WAAK,QAAQ,4BAA4B,OAAO,oBAAoB,MAAM;AAC1E,WAAK,gBAAgB;AAGrB,UAAI,QAAQ,qBAAqB,CAAC,QAAQ,aAAa;AACrD,aAAK,QAAQ,sBAAsB,CAAC,KAAK,CAAC;AAAA,MAC5C;AAAA,IACF;AACA,IAAAA,mBAAkB,UAAU,qBAAqB,SAAU,OAAO,SAAS;AACzE,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,UAAI,eAAe,KAAK;AAIxB,UAAI,oBAAoB,QAAQ,oBAAoB,IAAI,IAAI,iBAAiB,UAAU,cAAc,CAAC,IAAI,YAAY,IAAI;AAC1H,UAAI,qBAAqB,KAAK,sBAAsB;AACpD,UAAI,iBAAiB,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,iBAAiB,GAAG,KAAK;AACxD,YAAI,oBAAoB,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,IAAI,CAAC;AACrH,YAAI,eAAe,MAAM,QAAQ,CAAC,KAAK;AAGvC,YAAI,iBAAiB,mBAAmB;AACtC,yBAAe,KAAK,CAAC;AAAA,QACvB;AACA,aAAK,QAAQ,iCAAiC,GAAG,YAAY;AAC7D,aAAK,QAAQ,4BAA4B,GAAG,oBAAoB,eAAe,SAAS,OAAO;AAAA,MACjG;AACA,WAAK,gBAAgB;AAGrB,UAAI,QAAQ,qBAAqB,eAAe,QAAQ;AACtD,aAAK,QAAQ,sBAAsB,cAAc;AAAA,MACnD;AAAA,IACF;AAYA,IAAAA,mBAAkB,UAAU,sBAAsB,SAAU,WAAW,SAAS,aAAa;AAC3F,WAAK,oBAAoB;AACzB,UAAI,oBAAoB,IAAI,IAAI,KAAK,kBAAkB,UAAU,cAAc,CAAC,IAAI,KAAK,aAAa;AACtG,UAAI,eAAe,EAAE,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,IAAI,WAAW;AAC5H,UAAIC,MAAK,OAAO,CAAC,WAAW,OAAO,EAAE,KAAK,GAAG,CAAC,GAC5C,aAAaA,IAAG,CAAC,GACjB,WAAWA,IAAG,CAAC;AACjB,UAAI,qBAAqB,KAAK,sBAAsB;AACpD,UAAI,iBAAiB,CAAC;AACtB,eAAS,IAAI,YAAY,KAAK,UAAU,KAAK;AAC3C,YAAI,KAAK,gBAAgB,CAAC,GAAG;AAC3B;AAAA,QACF;AACA,YAAI,oBAAoB,kBAAkB,IAAI,CAAC;AAG/C,YAAI,iBAAiB,mBAAmB;AACtC,yBAAe,KAAK,CAAC;AACrB,eAAK,QAAQ,iCAAiC,GAAG,YAAY;AAC7D,eAAK,QAAQ,4BAA4B,GAAG,oBAAoB,KAAK,YAAY;AACjF,cAAI,cAAc;AAChB,8BAAkB,IAAI,CAAC;AAAA,UACzB,OAAO;AACL,8BAAkB,OAAO,CAAC;AAAA,UAC5B;AAAA,QACF;AAAA,MACF;AAGA,UAAI,eAAe,QAAQ;AACzB,aAAK,gBAAgB,cAAc,CAAC,GAAG,OAAO,iBAAiB,CAAC;AAChE,aAAK,QAAQ,sBAAsB,cAAc;AAAA,MACnD;AAAA,IACF;AACA,IAAAD,mBAAkB,UAAU,qBAAqB,SAAU,OAAO;AAChE,UAAI,KAAK,qBAAqB,UAAU,eAAe,UAAU,GAAG;AAIlE,aAAK,QAAQ,4BAA4B,GAAG,YAAY,IAAI;AAAA,MAC9D,WAAW,KAAK,oBAAoB,KAAK,KAAK,qBAAqB,OAAO;AACxE,aAAK,QAAQ,4BAA4B,KAAK,kBAAkB,YAAY,IAAI;AAAA,MAClF;AAIA,UAAI,EAAE,KAAK,yBAAyB,UAAU,KAAK,kBAAkB,OAAO;AAC1E,aAAK,QAAQ,4BAA4B,KAAK,eAAe,YAAY,IAAI;AAAA,MAC/E;AACA,UAAI,UAAU,UAAU,aAAa;AACnC,aAAK,QAAQ,4BAA4B,OAAO,YAAY,GAAG;AAAA,MACjE;AAAA,IACF;AAKA,IAAAA,mBAAkB,UAAU,mBAAmB,WAAY;AACzD,aAAO,KAAK,yBAAyB,KAAK,kBAAkB,KAAK;AAAA,IACnE;AACA,IAAAA,mBAAkB,UAAU,0CAA0C,WAAY;AAChF,UAAI,cAAc,KAAK,mCAAmC;AAC1D,WAAK,mBAAmB,WAAW;AAAA,IACrC;AACA,IAAAA,mBAAkB,UAAU,qCAAqC,WAAY;AAE3E,UAAI,CAAC,KAAK,iBAAiB,GAAG;AAC5B,eAAO,KAAK,IAAI,KAAK,kBAAkB,CAAC;AAAA,MAC1C;AAEA,UAAI,OAAO,KAAK,kBAAkB,YAAY,KAAK,kBAAkB,UAAU,aAAa;AAC1F,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,cAAc,KAAK,aAAa,KAAK,KAAK,cAAc,SAAS,GAAG;AACtE,eAAO,KAAK,cAAc,OAAO,SAAU,UAAU,cAAc;AACjE,iBAAO,KAAK,IAAI,UAAU,YAAY;AAAA,QACxC,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IACT;AACA,IAAAA,mBAAkB,UAAU,eAAe,SAAU,OAAO,kBAAkB;AAC5E,UAAI,QAAQ;AACZ,UAAI,qBAAqB,QAAQ;AAC/B,2BAAmB;AAAA,MACrB;AACA,UAAI,iBAAiB,OAAO;AAC1B,YAAI,CAAC,KAAK,kBAAkB,kBAAkB;AAC5C,gBAAM,IAAI,MAAM,6EAA6E;AAAA,QAC/F;AACA,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,MAAM,KAAK,SAAU,GAAG;AAC7B,mBAAO,MAAM,eAAe,CAAC;AAAA,UAC/B,CAAC;AAAA,QACH;AAAA,MACF,WAAW,OAAO,UAAU,UAAU;AACpC,YAAI,KAAK,kBAAkB,kBAAkB;AAC3C,gBAAM,IAAI,MAAM,wFAAwF,KAAK;AAAA,QAC/G;AACA,eAAO,KAAK,eAAe,KAAK,KAAK,KAAK,yBAAyB,UAAU,UAAU;AAAA,MACzF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACA,IAAAA,mBAAkB,UAAU,iBAAiB,SAAU,OAAO;AAC5D,UAAI,WAAW,KAAK,QAAQ,iBAAiB;AAC7C,aAAO,SAAS,KAAK,QAAQ;AAAA,IAC/B;AAWA,IAAAA,mBAAkB,UAAU,2BAA2B,SAAU,OAAO,mCAAmC;AACzG,WAAK,oBAAoB;AACzB,UAAI,KAAK,gBAAgB;AACvB,aAAK,sBAAsB,OAAO,iCAAiC;AACnE,aAAK,QAAQ,sBAAsB,CAAC,KAAK,CAAC;AAAA,MAC5C,OAAO;AACL,aAAK,iBAAiB,OAAO;AAAA,UAC3B,mBAAmB;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAAA,mBAAkB,UAAU,wBAAwB,SAAU,OAAO,mCAAmC;AACtG,UAAI,qBAAqB,KAAK,sBAAsB;AACpD,UAAI,mBAAmB,KAAK,QAAQ,yBAAyB,KAAK;AAKlE,UAAI;AACJ,UAAI,mCAAmC;AACrC,0BAAkB;AAAA,MACpB,OAAO;AACL,0BAAkB,CAAC;AACnB,aAAK,QAAQ,iCAAiC,OAAO,eAAe;AAAA,MACtE;AACA,WAAK,QAAQ,4BAA4B,OAAO,oBAAoB,kBAAkB,SAAS,OAAO;AAGtG,UAAI,kBAAkB,KAAK,kBAAkB,UAAU,cAAc,CAAC,IAAI,KAAK,cAAc,MAAM;AACnG,UAAI,iBAAiB;AACnB,wBAAgB,KAAK,KAAK;AAAA,MAC5B,OAAO;AACL,0BAAkB,gBAAgB,OAAO,SAAU,GAAG;AACpD,iBAAO,MAAM;AAAA,QACf,CAAC;AAAA,MACH;AACA,WAAK,gBAAgB;AAAA,IACvB;AACA,IAAAA,mBAAkB,UAAU,mBAAmB,SAAU,OAAO;AAC9D,WAAK,QAAQ,iBAAiB,KAAK;AACnC,WAAK,mBAAmB;AAAA,IAC1B;AACA,IAAAA,mBAAkB,UAAU,wBAAwB,SAAU,0BAA0B,mBAAmB;AACzG,UAAI,QAAQ,KAAK,QAAQ,iBAAiB;AAE1C,UAAI,yBAAyB,WAAW,OAAO;AAC7C,aAAK,mBAAmB,CAAC,GAAG;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AAEL,YAAI,aAAa,CAAC;AAClB,iBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,cAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,yBAAyB,QAAQ,CAAC,IAAI,IAAI;AACxE,uBAAW,KAAK,CAAC;AAAA,UACnB;AAAA,QACF;AACA,aAAK,mBAAmB,YAAY;AAAA,UAClC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAcA,IAAAA,mBAAkB,UAAU,qBAAqB,SAAU,UAAU,eAAe,WAAW;AAC7F,UAAI,QAAQ;AACZ,UAAI,cAAc,QAAQ;AACxB,oBAAY;AAAA,MACd;AACA,UAAI,OAAO;AAAA,QACT,kBAAkB,SAAU,OAAO;AACjC,gBAAM,iBAAiB,KAAK;AAAA,QAC9B;AAAA,QACA,kBAAkB,gBAAgB,gBAAgB,KAAK;AAAA,QACvD;AAAA,QACA,wBAAwB,KAAK;AAAA,QAC7B;AAAA,QACA,uBAAuB,SAAU,OAAO;AACtC,iBAAO,MAAM,gBAAgB,KAAK;AAAA,QACpC;AAAA,MACF;AACA,aAAO,UAAU,MAAM,KAAK,cAAc;AAAA,IAC5C;AAOA,IAAAA,mBAAkB,UAAU,2BAA2B,WAAY;AACjE,aAAO,gBAAgB,KAAK,QAAQ,iBAAiB,GAAG,KAAK,QAAQ,qBAAqB;AAAA,IAC5F;AAIA,IAAAA,mBAAkB,UAAU,uBAAuB,WAAY;AAC7D,kBAAY,KAAK,cAAc;AAAA,IACjC;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AAC3C,cAAUE,UAAS,MAAM;AACzB,aAASA,WAAU;AACjB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,WAAO,eAAeA,SAAQ,WAAW,YAAY;AAAA,MACnD,KAAK,SAAU,OAAO;AACpB,aAAK,WAAW,uBAAuB,KAAK;AAAA,MAC9C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,gBAAgB;AAAA,MACvD,KAAK,WAAY;AACf,eAAO,MAAM,KAAK,KAAK,KAAK,iBAAiB,MAAM,KAAK,aAAa,aAAa,eAAe,CAAC,CAAC;AAAA,MACrG;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,aAAa;AAAA,MACpD,KAAK,SAAU,OAAO;AACpB,aAAK,WAAW,aAAa,KAAK;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,uBAAuB;AAAA;AAAA;AAAA;AAAA,MAI9D,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,sBAAsB;AAAA,MAC/C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvD,KAAK,SAAU,cAAc;AAC3B,aAAK,WAAW,gBAAgB,YAAY;AAAA,MAC9C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,mBAAmB;AAAA,MAC1D,KAAK,SAAU,uBAAuB;AACpC,aAAK,WAAW,mBAAmB,qBAAqB;AAAA,MAC1D;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,0BAA0B;AAAA,MACjE,KAAK,SAAU,2BAA2B;AACxC,aAAK,WAAW,0BAA0B,yBAAyB;AAAA,MACrE;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,iBAAiB;AAAA,MACxD,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,iBAAiB;AAAA,MAC1C;AAAA,MACA,KAAK,SAAU,OAAO;AACpB,aAAK,WAAW,iBAAiB,KAAK;AAAA,MACxC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,SAAQ,WAAW,SAAU,MAAM;AACjC,aAAO,IAAIA,SAAQ,IAAI;AAAA,IACzB;AACA,IAAAA,SAAQ,UAAU,qBAAqB,WAAY;AACjD,WAAK,qBAAqB,sBAAsB,KAAK,KAAK;AAC1D,UAAI,KAAK,oBAAoB;AAC3B,aAAK,eAAe;AAAA,MACtB,WAAW,QAAQ,KAAK,MAAM,UAAU,mBAAmB,GAAG;AAC5D,aAAK,eAAe;AAAA,MACtB,OAAO;AACL,aAAK,eAAe,OAAO,OAAO,YAAY,EAAE,OAAO,SAAU,KAAK,WAAW;AAC/E,cAAI,SAAS,IAAI;AACjB,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AAAA,MACP;AACA,WAAK,cAAc,KAAK,iBAAiB,KAAK,IAAI;AAClD,WAAK,gBAAgB,KAAK,mBAAmB,KAAK,IAAI;AACtD,WAAK,uBAAuB,KAAK,mBAAmB,KAAK,IAAI;AAC7D,WAAK,wBAAwB,KAAK,oBAAoB,KAAK,IAAI;AAC/D,WAAK,OAAO,WAAW,KAAK,aAAa;AACzC,WAAK,OAAO,SAAS,KAAK,WAAW;AACrC,WAAK,OAAO,WAAW,KAAK,oBAAoB;AAChD,WAAK,OAAO,YAAY,KAAK,qBAAqB;AAClD,WAAK,OAAO;AACZ,WAAK,mBAAmB;AACxB,WAAK,gBAAgB;AAAA,IACvB;AACA,IAAAA,SAAQ,UAAU,UAAU,WAAY;AACtC,WAAK,SAAS,WAAW,KAAK,aAAa;AAC3C,WAAK,SAAS,SAAS,KAAK,WAAW;AACvC,WAAK,SAAS,WAAW,KAAK,oBAAoB;AAClD,WAAK,SAAS,YAAY,KAAK,qBAAqB;AAAA,IACtD;AACA,IAAAA,SAAQ,UAAU,SAAS,WAAY;AACrC,UAAI,YAAY,KAAK,KAAK,aAAa,UAAU,gBAAgB;AACjE,WAAK,WAAW,cAAc,UAAU;AACxC,UAAI,eAAe,MAAM,KAAK,aAAa,aAAa,eAAe,IAAI;AAC3E,UAAI,gBAAgB,UAAU;AAE9B,UAAI,UAAU,KAAK,KAAK,iBAAiB,YAAY;AACrD,UAAI,QAAQ,QAAQ;AAClB,cAAM,UAAU,QAAQ,KAAK,SAAS,SAAU,IAAI;AAClD,aAAG,aAAa,YAAY,IAAI;AAAA,QAClC,CAAC;AAAA,MACH;AAEA,UAAI,oBAAoB,KAAK,KAAK,iBAAiB,aAAa;AAChE,UAAI,kBAAkB,QAAQ;AAC5B,cAAM,UAAU,QAAQ,KAAK,mBAAmB,SAAU,IAAI;AAC5D,aAAG,aAAa,YAAY,IAAI;AAAA,QAClC,CAAC;AAAA,MACH;AACA,UAAI,KAAK,oBAAoB;AAC3B,aAAK,WAAW,wBAAwB,IAAI;AAAA,MAC9C;AACA,WAAK,WAAW,OAAO;AAAA,IACzB;AAMA,IAAAA,SAAQ,UAAU,iBAAiB,SAAU,MAAM;AACjD,UAAID;AACJ,UAAI,cAAc,KAAK,cAAc,MAAM,KAAK,aAAa,aAAa,4BAA4B,CAAC;AACvG,UAAI,KAAK,sBAAsB,aAAa;AAC1C,gBAAQA,MAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,iBAAiB,QAAQA,QAAO,SAASA,MAAK;AAAA,MACnI;AACA,UAAI,iBAAiB,KAAK,cAAc,MAAM,KAAK,aAAa,aAAa,oBAAoB,CAAC;AAClG,aAAO,kBAAkB,eAAe,eAAe;AAAA,IACzD;AAIA,IAAAC,SAAQ,UAAU,qBAAqB,WAAY;AACjD,UAAI,QAAQ;AACZ,WAAK,gBAAgB,QAAQ,KAAK,MAAM,UAAU,+BAA+B;AACjF,UAAI,KAAK,sBAAsB,KAAK,eAAe;AACjD,YAAI,YAAY,MAAM,KAAK,KAAK,KAAK,iBAAiB,UAAU,sBAAsB,GAAG,SAAU,UAAU;AAC3G,iBAAO,MAAM,aAAa,QAAQ,QAAQ;AAAA,QAC5C,CAAC;AACD,YAAI,QAAQ,KAAK,MAAM,UAAU,8BAA8B,GAAG;AAChE,eAAK,gBAAgB;AAAA,QACvB,WAAW,UAAU,SAAS,GAAG;AAC/B,eAAK,gBAAgB,UAAU,CAAC;AAAA,QAClC;AACA;AAAA,MACF;AACA,UAAI,oBAAoB,KAAK,KAAK,iBAAiB,UAAU,2BAA2B;AACxF,UAAI,wBAAwB,KAAK,KAAK,cAAc,UAAU,2BAA2B;AACzF,UAAI,kBAAkB,QAAQ;AAC5B,YAAI,mBAAmB,KAAK,KAAK,iBAAiB,UAAU,8BAA8B;AAC1F,aAAK,gBAAgB,MAAM,KAAK,kBAAkB,SAAU,UAAU;AACpE,iBAAO,MAAM,aAAa,QAAQ,QAAQ;AAAA,QAC5C,CAAC;AAAA,MACH,WAAW,uBAAuB;AAChC,aAAK,gBAAgB,KAAK,aAAa,QAAQ,qBAAqB;AAAA,MACtE;AAAA,IACF;AAMA,IAAAA,SAAQ,UAAU,aAAa,SAAU,WAAW,WAAW;AAC7D,WAAK,WAAW,WAAW,WAAW,SAAS;AAAA,IACjD;AAWA,IAAAA,SAAQ,UAAU,qBAAqB,SAAU,UAAU,eAAe;AACxE,aAAO,KAAK,WAAW;AAAA,QAAmB;AAAA,QAAU;AAAA;AAAA,QAA+B;AAAA,MAAI;AAAA,IACzF;AACA,IAAAA,SAAQ,UAAU,uBAAuB,WAAY;AACnD,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,yBAAyB,SAAU,OAAO,WAAW;AACnD,cAAI,UAAU,MAAM,aAAa,KAAK;AACtC,cAAI,SAAS;AACX,oBAAQ,UAAU,IAAI,MAAM,aAAa,SAAS,CAAC;AAAA,UACrD;AAAA,QACF;AAAA,QACA,kBAAkB,SAAU,OAAO;AACjC,cAAI,UAAU,MAAM,aAAa,KAAK;AACtC,cAAI,SAAS;AACX,oBAAQ,MAAM;AAAA,UAChB;AAAA,QACF;AAAA,QACA,6BAA6B,SAAU,OAAO,MAAM;AAClD,iBAAO,MAAM,aAAa,KAAK,EAAE,aAAa,IAAI;AAAA,QACpD;AAAA,QACA,wBAAwB,WAAY;AAClC,iBAAO,MAAM,aAAa,QAAQ,SAAS,aAAa;AAAA,QAC1D;AAAA,QACA,kBAAkB,WAAY;AAC5B,iBAAO,MAAM,aAAa;AAAA,QAC5B;AAAA,QACA,uBAAuB,SAAU,OAAO;AACtC,iBAAO,MAAM,eAAe,MAAM,aAAa,KAAK,CAAC;AAAA,QACvD;AAAA,QACA,oBAAoB,SAAU,OAAO;AACnC,cAAI,WAAW,MAAM,aAAa,KAAK;AACvC,iBAAO,CAAC,CAAC,SAAS,cAAc,UAAU,iBAAiB;AAAA,QAC7D;AAAA,QACA,iBAAiB,SAAU,OAAO;AAChC,cAAI,WAAW,MAAM,aAAa,KAAK;AACvC,iBAAO,CAAC,CAAC,SAAS,cAAc,UAAU,cAAc;AAAA,QAC1D;AAAA,QACA,0BAA0B,SAAU,OAAO;AACzC,cAAI,WAAW,MAAM,aAAa,KAAK;AACvC,cAAI,WAAW,SAAS,cAAc,UAAU,iBAAiB;AACjE,iBAAO,SAAS;AAAA,QAClB;AAAA,QACA,mBAAmB,WAAY;AAC7B,iBAAO,MAAM,SAAS,SAAS,iBAAiB,MAAM,KAAK,SAAS,SAAS,aAAa;AAAA,QAC5F;AAAA,QACA,eAAe,WAAY;AACzB,iBAAO,SAAS,kBAAkB,MAAM;AAAA,QAC1C;AAAA,QACA,yBAAyB,SAAU,OAAO,WAAW;AACnD,iBAAO,MAAM,aAAa,KAAK,EAAE,UAAU,SAAS,MAAM,aAAa,SAAS,CAAC;AAAA,QACnF;AAAA,QACA,cAAc,SAAU,OAAO;AAC7B,gBAAM;AAAA,YAAK,UAAU;AAAA,YAAc;AAAA,cACjC;AAAA,YACF;AAAA;AAAA,YAAsB;AAAA,UAAI;AAAA,QAC5B;AAAA,QACA,uBAAuB,SAAU,gBAAgB;AAC/C,gBAAM;AAAA,YAAK,UAAU;AAAA,YAAwB;AAAA,cAC3C;AAAA,YACF;AAAA;AAAA,YAAsB;AAAA,UAAI;AAAA,QAC5B;AAAA,QACA,4BAA4B,SAAU,OAAO,WAAW;AACtD,cAAI,UAAU,MAAM,aAAa,KAAK;AACtC,cAAI,SAAS;AACX,oBAAQ,UAAU,OAAO,MAAM,aAAa,SAAS,CAAC;AAAA,UACxD;AAAA,QACF;AAAA,QACA,6BAA6B,SAAU,OAAO,MAAM,OAAO;AACzD,cAAI,UAAU,MAAM,aAAa,KAAK;AACtC,cAAI,SAAS;AACX,oBAAQ,aAAa,MAAM,KAAK;AAAA,UAClC;AAAA,QACF;AAAA,QACA,kCAAkC,SAAU,OAAO,WAAW;AAC5D,cAAI,WAAW,MAAM,aAAa,KAAK;AACvC,cAAI,WAAW,SAAS,cAAc,UAAU,uBAAuB;AACvE,mBAAS,UAAU;AACnB,cAAI,QAAQ,SAAS,YAAY,OAAO;AACxC,gBAAM,UAAU,UAAU,MAAM,IAAI;AACpC,mBAAS,cAAc,KAAK;AAAA,QAC9B;AAAA,QACA,gCAAgC,SAAU,eAAe,eAAe;AACtE,cAAI,UAAU,MAAM,aAAa,aAAa;AAC9C,cAAI,WAAW,UAAU;AACzB,gBAAM,UAAU,QAAQ,KAAK,QAAQ,iBAAiB,QAAQ,GAAG,SAAU,IAAI;AAC7E,eAAG,aAAa,YAAY,aAAa;AAAA,UAC3C,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO,IAAI,kBAAkB,OAAO;AAAA,IACtC;AAKA,IAAAA,SAAQ,UAAU,kBAAkB,WAAY;AAC9C,UAAI,KAAK,sBAAsB,KAAK,eAAe;AACjD,YAAI,CAAC,KAAK,KAAK,cAAc,MAAM,KAAK,aAAa,aAAa,eAAe,IAAI,gBAAkB,GAAG;AACxG,cAAI,QAAQ,KAAK,kBAAkB;AACnC,cAAI,UAAU,IAAI;AAChB,iBAAK,aAAa,KAAK,EAAE,WAAW;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAAA,SAAQ,UAAU,oBAAoB,WAAY;AAChD,UAAI,KAAK,yBAAyB,SAAS,KAAK,cAAc,SAAS,GAAG;AACxE,eAAO,KAAK,cAAc,CAAC;AAAA,MAC7B;AACA,UAAI,OAAO,KAAK,kBAAkB,YAAY,KAAK,kBAAkB,UAAU,aAAa;AAC1F,eAAO,KAAK;AAAA,MACd;AACA,UAAI,KAAK,KAAK,KAAK,cAAc,MAAM,KAAK,aAAa,aAAa,eAAe,IAAI,WAAW,KAAK,aAAa,aAAa,wBAAwB,IAAI,GAAG;AAClK,UAAI,OAAO,MAAM;AACf,eAAO;AAAA,MACT;AACA,aAAO,KAAK,iBAAiB,EAAE;AAAA,IACjC;AAKA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,IAAI;AACjD,UAAI,gBAAgB,QAAQ,IAAI,MAAM,KAAK,aAAa,aAAa,eAAe,IAAI,QAAQ,KAAK,aAAa,aAAa,IAAI,CAAC;AAEpI,UAAI,iBAAiB,QAAQ,eAAe,MAAM,KAAK,aAAa,aAAa,eAAe,CAAC,GAAG;AAClG,eAAO,KAAK,aAAa,QAAQ,aAAa;AAAA,MAChD;AACA,aAAO;AAAA,IACT;AAKA,IAAAA,SAAQ,UAAU,qBAAqB,SAAU,KAAK;AACpD,UAAI,QAAQ,KAAK,iBAAiB,IAAI,MAAM;AAC5C,WAAK,WAAW,cAAc,KAAK;AAAA,IACrC;AAKA,IAAAA,SAAQ,UAAU,sBAAsB,SAAU,KAAK;AACrD,UAAI,QAAQ,KAAK,iBAAiB,IAAI,MAAM;AAC5C,WAAK,WAAW,eAAe,KAAK;AAAA,IACtC;AAKA,IAAAA,SAAQ,UAAU,qBAAqB,SAAU,KAAK;AACpD,UAAI,QAAQ,KAAK,iBAAiB,IAAI,MAAM;AAC5C,UAAI,SAAS,IAAI;AACjB,WAAK,WAAW,cAAc,KAAK,OAAO,UAAU,SAAS,KAAK,aAAa,aAAa,eAAe,CAAC,GAAG,KAAK;AAAA,IACtH;AAKA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,KAAK;AAClD,UAAI,QAAQ,KAAK,iBAAiB,IAAI,MAAM;AAC5C,UAAI,SAAS,IAAI;AAGjB,UAAI,iBAAiB,CAAC,QAAQ,QAAQ,UAAU,uBAAuB;AACvE,WAAK,WAAW,YAAY,OAAO,gBAAgB,GAAG;AAAA,IACxD;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AAwBd,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,eAAe;AAAA,EACf,MAAM;AAAA,EACN,MAAM;AACR;AAEA,IAAI,YAAY;AAAA,EACd,cAAc;AAAA,EACd,eAAe;AAAA,EACf,cAAc;AAAA,EACd,eAAe;AAAA,EACf,oBAAoB,CAAC,yBAAyB,sCAAsC,wBAAwB,yBAAyB,2BAA2B,6DAA6D,EAAE,KAAK,IAAI;AAC1O;AAEA,IAAI,YAAY;AAAA;AAAA,EAEd,0BAA0B;AAAA;AAAA,EAE1B,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,oCAAoC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpC,qBAAqB;AACvB;AAIA,IAAI;AAAA,CACH,SAAUC,YAAW;AACpB,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,OAAO,IAAI,CAAC,IAAI;AACpC,EAAAA,WAAUA,WAAU,UAAU,IAAI,CAAC,IAAI;AACzC,GAAG,cAAc,YAAY,CAAC,EAAE;AAQhC,IAAI;AAAA,CACH,SAAUC,SAAQ;AACjB,EAAAA,QAAOA,QAAO,UAAU,IAAI,CAAC,IAAI;AACjC,EAAAA,QAAOA,QAAO,WAAW,IAAI,CAAC,IAAI;AAClC,EAAAA,QAAOA,QAAO,aAAa,IAAI,CAAC,IAAI;AACpC,EAAAA,QAAOA,QAAO,cAAc,IAAI,CAAC,IAAI;AACrC,EAAAA,QAAOA,QAAO,WAAW,IAAI,CAAC,IAAI;AAClC,EAAAA,QAAOA,QAAO,SAAS,IAAI,EAAE,IAAI;AACjC,EAAAA,QAAOA,QAAO,cAAc,IAAI,CAAC,IAAI;AACrC,EAAAA,QAAOA,QAAO,YAAY,IAAI,EAAE,IAAI;AACtC,GAAG,WAAW,SAAS,CAAC,EAAE;AAwB1B,IAAI;AAAA;AAAA,EAAwC,SAAU,QAAQ;AAC5D,cAAUC,2BAA0B,MAAM;AAC1C,aAASA,0BAAyB,SAAS;AACzC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,0BAAyB,cAAc,GAAG,OAAO,CAAC,KAAK;AAC3G,YAAM,gBAAgB;AACtB,YAAM,cAAc;AACpB,YAAM,mBAAmB;AACzB,YAAM,kBAAkB;AACxB,YAAM,mCAAmC;AACzC,YAAM,YAAY;AAClB,YAAM,iBAAiB;AACvB,YAAM,0BAA0B;AAChC,YAAM,2BAA2B;AACjC,YAAM,qBAAqB;AAC3B,YAAM,eAAe,OAAO;AAe5B,YAAM,eAAe,OAAO;AAC5B,YAAM,eAAe;AAAA,QACnB,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,MACR;AACA,YAAM,WAAW;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,2BAA0B,cAAc;AAAA,MAC5D,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,2BAA0B,WAAW;AAAA,MACzD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,2BAA0B,WAAW;AAAA,MACzD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,2BAA0B,UAAU;AAAA,MACxD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,2BAA0B,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAIhE,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,WAAW,WAAY;AACrB,mBAAO;AAAA,UACT;AAAA,UACA,sBAAsB,WAAY;AAChC,mBAAO;AAAA,UACT;AAAA,UACA,WAAW,WAAY;AACrB,mBAAO;AAAA,UACT;AAAA,UACA,OAAO,WAAY;AACjB,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,cACL,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,UACT;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,cACL,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,mBAAmB,WAAY;AAC7B,mBAAO;AAAA,cACL,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,UACT;AAAA,UACA,WAAW,WAAY;AACrB,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,eAAe,WAAY;AACzB,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAY;AACtB,mBAAO;AAAA,UACT;AAAA,UACA,eAAe,WAAY;AACzB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,0BAAyB,UAAU,OAAO,WAAY;AACpD,UAAIJ,MAAKI,0BAAyB,YAChC,OAAOJ,IAAG,MACV,OAAOA,IAAG;AACZ,UAAI,CAAC,KAAK,QAAQ,SAAS,IAAI,GAAG;AAChC,cAAM,IAAI,MAAM,OAAO,kCAAkC;AAAA,MAC3D;AACA,UAAI,KAAK,QAAQ,SAAS,IAAI,GAAG;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AACA,IAAAI,0BAAyB,UAAU,UAAU,WAAY;AACvD,mBAAa,KAAK,uBAAuB;AACzC,mBAAa,KAAK,wBAAwB;AAE1C,2BAAqB,KAAK,kBAAkB;AAAA,IAC9C;AAKA,IAAAA,0BAAyB,UAAU,kBAAkB,SAAU,QAAQ;AACrE,WAAK,eAAe;AAAA,IACtB;AAIA,IAAAA,0BAAyB,UAAU,yBAAyB,WAAY;AACtE,WAAK,eAAe,KAAK,eAAe,UAAU;AAAA,IACpD;AAIA,IAAAA,0BAAyB,UAAU,kBAAkB,SAAU,QAAQ;AACrE,WAAK,aAAa,MAAM,OAAO,OAAO;AACtC,WAAK,aAAa,QAAQ,OAAO,SAAS;AAC1C,WAAK,aAAa,SAAS,OAAO,UAAU;AAC5C,WAAK,aAAa,OAAO,OAAO,QAAQ;AAAA,IAC1C;AAEA,IAAAA,0BAAyB,UAAU,eAAe,SAAU,WAAW;AACrE,WAAK,mBAAmB;AAAA,IAC1B;AAIA,IAAAA,0BAAyB,UAAU,mBAAmB,SAAU,iBAAiB;AAC/E,WAAK,kBAAkB;AAAA,IACzB;AAIA,IAAAA,0BAAyB,UAAU,UAAU,WAAY;AACvD,aAAO,KAAK;AAAA,IACd;AAEA,IAAAA,0BAAyB,UAAU,sBAAsB,SAAU,GAAG,GAAG;AACvE,WAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,IAAI;AACzC,WAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,IAAI;AAAA,IAC3C;AAEA,IAAAA,0BAAyB,UAAU,sCAAsC,SAAU,YAAY;AAC7F,WAAK,mCAAmC;AAAA,IAC1C;AACA,IAAAA,0BAAyB,UAAU,eAAe,SAAU,WAAW;AACrE,WAAK,cAAc;AAAA,IACrB;AAMA,IAAAA,0BAAyB,UAAU,eAAe,SAAU,WAAW;AACrE,WAAK,YAAY;AAAA,IACnB;AAOA,IAAAA,0BAAyB,UAAU,oBAAoB,SAAU,MAAM;AACrE,WAAK,iBAAiB;AAAA,IACxB;AACA,IAAAA,0BAAyB,UAAU,SAAS,WAAY;AACtD,aAAO,KAAK;AAAA,IACd;AAIA,IAAAA,0BAAyB,UAAU,OAAO,WAAY;AACpD,UAAI,QAAQ;AACZ,UAAI,KAAK,eAAe;AACtB;AAAA,MACF;AACA,WAAK,QAAQ,cAAc;AAC3B,WAAK,QAAQ,UAAU;AACvB,UAAI,KAAK,aAAa;AACpB,aAAK,gBAAgB;AACrB,aAAK,QAAQ,SAASA,0BAAyB,WAAW,IAAI;AAC9D,aAAK,aAAa,KAAK,QAAQ,mBAAmB;AAClD,aAAK,aAAa;AAClB,aAAK,QAAQ,WAAW;AAAA,MAC1B,OAAO;AACL,aAAK,QAAQ,SAASA,0BAAyB,WAAW,cAAc;AACxE,aAAK,qBAAqB,sBAAsB,WAAY;AAC1D,gBAAM,aAAa,MAAM,QAAQ,mBAAmB;AACpD,gBAAM,aAAa;AACnB,gBAAM,QAAQ,SAASA,0BAAyB,WAAW,IAAI;AAC/D,gBAAM,0BAA0B,WAAW,WAAY;AACrD,kBAAM,0BAA0B;AAChC,kBAAM,QAAQ,YAAYA,0BAAyB,WAAW,cAAc;AAC5E,kBAAM,QAAQ,WAAW;AAAA,UAC3B,GAAG,UAAU,wBAAwB;AAAA,QACvC,CAAC;AACD,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAIA,IAAAA,0BAAyB,UAAU,QAAQ,SAAU,kBAAkB;AACrE,UAAI,QAAQ;AACZ,UAAI,qBAAqB,QAAQ;AAC/B,2BAAmB;AAAA,MACrB;AACA,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AACA,WAAK,QAAQ,cAAc;AAC3B,UAAI,KAAK,aAAa;AACpB,aAAK,gBAAgB;AACrB,YAAI,CAAC,kBAAkB;AACrB,eAAK,kBAAkB;AAAA,QACzB;AACA,aAAK,QAAQ,YAAYA,0BAAyB,WAAW,IAAI;AACjE,aAAK,QAAQ,YAAYA,0BAAyB,WAAW,aAAa;AAC1E,aAAK,QAAQ,YAAY;AACzB;AAAA,MACF;AACA,WAAK,QAAQ,SAASA,0BAAyB,WAAW,gBAAgB;AAC1E,4BAAsB,WAAY;AAChC,cAAM,QAAQ,YAAYA,0BAAyB,WAAW,IAAI;AAClE,cAAM,QAAQ,YAAYA,0BAAyB,WAAW,aAAa;AAC3E,cAAM,2BAA2B,WAAW,WAAY;AACtD,gBAAM,2BAA2B;AACjC,gBAAM,QAAQ,YAAYA,0BAAyB,WAAW,gBAAgB;AAC9E,gBAAM,QAAQ,YAAY;AAAA,QAC5B,GAAG,UAAU,yBAAyB;AAAA,MACxC,CAAC;AACD,WAAK,gBAAgB;AACrB,UAAI,CAAC,kBAAkB;AACrB,aAAK,kBAAkB;AAAA,MACzB;AAAA,IACF;AAEA,IAAAA,0BAAyB,UAAU,kBAAkB,SAAU,KAAK;AAClE,UAAI,KAAK,IAAI;AACb,UAAI,KAAK,QAAQ,qBAAqB,EAAE,GAAG;AACzC;AAAA,MACF;AACA,WAAK,MAAM;AAAA,IACb;AAEA,IAAAA,0BAAyB,UAAU,gBAAgB,SAAU,KAAK;AAChE,UAAI,UAAU,IAAI,SAChB,MAAM,IAAI;AACZ,UAAI,WAAW,QAAQ,YAAY,YAAY;AAC/C,UAAI,UAAU;AACZ,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AACA,IAAAA,0BAAyB,UAAU,eAAe,WAAY;AAC5D,UAAIJ;AAEJ,WAAK,eAAe,KAAK,0BAA0B;AACnD,UAAI,SAAS,KAAK,gBAAgB;AAClC,UAAI,uBAAuB,KAAK,wBAAwB,MAAM;AAC9D,UAAI,oBAAoB,KAAK,OAAO,QAAQ,UAAU,MAAM,IAAI,WAAW;AAC3E,UAAI,sBAAsB,KAAK,OAAO,QAAQ,UAAU,KAAK,IAAI,UAAU;AAC3E,UAAI,mBAAmB,KAAK,0BAA0B,MAAM;AAC5D,UAAI,iBAAiB,KAAK,wBAAwB,MAAM;AACxD,UAAIK,MAAK,KAAK,cACZ,aAAaA,IAAG,YAChB,cAAcA,IAAG;AACnB,UAAI,YAAYL,MAAK,CAAC,GAAGA,IAAG,mBAAmB,IAAI,kBAAkBA,IAAG,iBAAiB,IAAI,gBAAgBA;AAG7G,UAAI,WAAW,QAAQ,YAAY,QAAQ,UAAU,oCAAoC;AACvF,8BAAsB;AAAA,MACxB;AAGA,UAAI,KAAK,oBAAoB,KAAK,iBAAiB;AACjD,aAAK,gCAAgC,QAAQ;AAAA,MAC/C;AACA,WAAK,QAAQ,mBAAmB,sBAAsB,MAAM,iBAAiB;AAC7E,WAAK,QAAQ,YAAY,QAAQ;AACjC,WAAK,QAAQ,aAAa,uBAAuB,uBAAuB,OAAO,EAAE;AAEjF,UAAI,CAAC,KAAK,OAAO,QAAQ,UAAU,MAAM,GAAG;AAC1C,aAAK,QAAQ,SAASI,0BAAyB,WAAW,aAAa;AAAA,MACzE;AAAA,IACF;AAIA,IAAAA,0BAAyB,UAAU,4BAA4B,WAAY;AACzE,UAAI,aAAa,KAAK,QAAQ,oBAAoB;AAClD,UAAI,WAAW,KAAK,QAAQ,kBAAkB;AAC9C,UAAI,eAAe,KAAK,QAAQ,oBAAoB;AACpD,UAAI,eAAe,KAAK,QAAQ,gBAAgB;AAChD,UAAI,CAAC,YAAY;AAEf,qBAAa;AAAA,UACX,KAAK,KAAK,SAAS;AAAA,UACnB,OAAO,KAAK,SAAS;AAAA,UACrB,QAAQ,KAAK,SAAS;AAAA,UACtB,MAAM,KAAK,SAAS;AAAA,UACpB,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MAEF;AACA,aAAO;AAAA,QACL,YAAY;AAAA,QACZ;AAAA,QACA,aAAa,KAAK;AAAA,QAClB,kBAAkB;AAAA;AAAA,UAEhB,KAAK,WAAW;AAAA,UAChB,OAAO,aAAa,QAAQ,WAAW;AAAA,UACvC,QAAQ,aAAa,SAAS,WAAW;AAAA,UACzC,MAAM,WAAW;AAAA;AAAA,QAEnB;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAQA,IAAAA,0BAAyB,UAAU,kBAAkB,WAAY;AAC/D,UAAI,SAAS,KAAK;AAClB,UAAIJ,MAAK,KAAK,cACZ,mBAAmBA,IAAG,kBACtB,aAAaA,IAAG,YAChB,cAAcA,IAAG;AACnB,UAAI,iBAAiBI,0BAAyB,QAAQ;AACtD,UAAI,qBAAqB,KAAK,OAAO,KAAK,cAAc,UAAU,MAAM;AACxE,UAAI;AACJ,UAAI;AACJ,UAAI,oBAAoB;AACtB,uBAAe,iBAAiB,MAAM,iBAAiB,KAAK,aAAa;AACzE,0BAAkB,iBAAiB,SAAS,iBAAiB,KAAK,aAAa;AAAA,MACjF,OAAO;AACL,uBAAe,iBAAiB,MAAM,iBAAiB,KAAK,aAAa;AACzE,0BAAkB,iBAAiB,SAAS,iBAAiB,WAAW,SAAS,KAAK,aAAa;AAAA,MACrG;AACA,UAAI,oBAAoB,kBAAkB,YAAY,SAAS;AAC/D,UAAI,CAAC,qBAAqB,eAAe,kBAAkB,KAAK,gBAAgB;AAE9E,iBAAS,KAAK,OAAO,QAAQ,UAAU,MAAM;AAAA,MAC/C;AACA,UAAI,QAAQ,KAAK,QAAQ,MAAM;AAC/B,UAAI,YAAY,KAAK,OAAO,KAAK,cAAc,UAAU,QAAQ;AACjE,UAAI,cAAc,KAAK,OAAO,KAAK,cAAc,UAAU,KAAK,KAAK,KAAK,OAAO,QAAQ,UAAU,KAAK;AAExG,UAAI,oBAAoB;AAExB,UAAI,SAAS,WAAW;AACtB,4BAAoB,CAAC;AAAA,MACvB,OAAO;AAEL,4BAAoB;AAAA,MACtB;AACA,UAAI;AACJ,UAAI;AACJ,UAAI,mBAAmB;AACrB,wBAAgB,iBAAiB,OAAO,WAAW,QAAQ,KAAK,aAAa;AAC7E,yBAAiB,iBAAiB,QAAQ,KAAK,aAAa;AAAA,MAC9D,OAAO;AACL,wBAAgB,iBAAiB,OAAO,KAAK,aAAa;AAC1D,yBAAiB,iBAAiB,QAAQ,WAAW,QAAQ,KAAK,aAAa;AAAA,MACjF;AACA,UAAI,kBAAkB,gBAAgB,YAAY,QAAQ;AAC1D,UAAI,mBAAmB,iBAAiB,YAAY,QAAQ;AAC5D,UAAI,6BAA6B,KAAK,OAAO,QAAQ,UAAU,QAAQ,KAAK,KAAK,OAAO,QAAQ,UAAU,KAAK;AAC/G,UAAI,oBAAoB,8BAA8B,SAAS,CAAC,mBAAmB,4BAA4B;AAE7G,iBAAS,KAAK,SAAS,QAAQ,UAAU,KAAK;AAAA,MAChD,WAAW,mBAAmB,qBAAqB,SAAS,mBAAmB,CAAC,qBAAqB,eAAe,CAAC,oBAAoB,iBAAiB,gBAAgB;AAExK,iBAAS,KAAK,OAAO,QAAQ,UAAU,KAAK;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAMA,IAAAA,0BAAyB,UAAU,0BAA0B,SAAU,QAAQ;AAC7E,UAAI,KAAK,YAAY,GAAG;AACtB,eAAO,KAAK;AAAA,MACd;AACA,UAAI,mBAAmB,KAAK,aAAa;AACzC,UAAI,YAAY;AAChB,UAAI,kBAAkB,KAAK,OAAO,QAAQ,UAAU,MAAM;AAC1D,UAAI,mBAAmB,KAAK,OAAO,KAAK,cAAc,UAAU,MAAM;AACtE,UAAI,iBAAiBA,0BAAyB,QAAQ;AAEtD,UAAI,iBAAiB;AACnB,oBAAY,iBAAiB,MAAM,KAAK,aAAa,MAAM;AAC3D,YAAI,CAAC,kBAAkB;AACrB,uBAAa,KAAK,aAAa,WAAW;AAAA,QAC5C;AAAA,MACF,OAAO;AACL,oBAAY,iBAAiB,SAAS,KAAK,aAAa,SAAS,KAAK,aAAa,WAAW,SAAS;AACvG,YAAI,kBAAkB;AACpB,uBAAa,KAAK,aAAa,WAAW;AAAA,QAC5C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAMA,IAAAA,0BAAyB,UAAU,4BAA4B,SAAU,QAAQ;AAC/E,UAAI,aAAa,KAAK,aAAa;AAEnC,UAAI,iBAAiB,KAAK,OAAO,QAAQ,UAAU,KAAK;AACxD,UAAI,yBAAyB,KAAK,OAAO,KAAK,cAAc,UAAU,KAAK;AAC3E,UAAI,gBAAgB;AAClB,YAAI,cAAc,yBAAyB,WAAW,QAAQ,KAAK,aAAa,OAAO,KAAK,aAAa;AAKzG,YAAI,KAAK,oBAAoB,KAAK,iBAAiB;AACjD,iBAAO,eAAe,KAAK,aAAa,aAAa,QAAQ,KAAK,aAAa,SAAS;AAAA,QAC1F;AACA,eAAO;AAAA,MACT;AACA,aAAO,yBAAyB,WAAW,QAAQ,KAAK,aAAa,QAAQ,KAAK,aAAa;AAAA,IACjG;AAMA,IAAAA,0BAAyB,UAAU,0BAA0B,SAAU,QAAQ;AAC7E,UAAI,aAAa,KAAK,aAAa;AACnC,UAAI,kBAAkB,KAAK,OAAO,QAAQ,UAAU,MAAM;AAC1D,UAAI,uBAAuB,KAAK,OAAO,KAAK,cAAc,UAAU,MAAM;AAC1E,UAAI,IAAI;AACR,UAAI,iBAAiB;AACnB,YAAI,uBAAuB,WAAW,SAAS,KAAK,aAAa,MAAM,CAAC,KAAK,aAAa;AAAA,MAC5F,OAAO;AACL,YAAI,uBAAuB,WAAW,SAAS,KAAK,aAAa,SAAS,KAAK,aAAa;AAAA,MAC9F;AACA,aAAO;AAAA,IACT;AAKA,IAAAA,0BAAyB,UAAU,kCAAkC,SAAU,UAAU;AACvF,UAAI,KAAKJ;AACT,UAAIK,MAAK,KAAK,cACZ,eAAeA,IAAG,cAClB,mBAAmBA,IAAG,kBACtB,cAAcA,IAAG,aACjB,eAAeA,IAAG;AACpB,UAAI,QAAQ,OAAO,KAAK,QAAQ;AAChC,UAAI;AACF,iBAAS,UAAU,SAAS,KAAK,GAAG,YAAY,QAAQ,KAAK,GAAG,CAAC,UAAU,MAAM,YAAY,QAAQ,KAAK,GAAG;AAC3G,cAAI,OAAO,UAAU;AACrB,cAAI,QAAQ,SAAS,IAAI,KAAK;AAC9B,cAAI,KAAK,qCAAqC,SAAS,UAAU,SAAS,UAAU;AAClF,qBAAS,IAAI,KAAK,aAAa,QAAQ,YAAY,SAAS;AAC5D;AAAA,UACF;AAGA,mBAAS,iBAAiB,IAAI;AAG9B,cAAI,CAAC,KAAK,iBAAiB;AACzB,gBAAI,SAAS,OAAO;AAClB,uBAAS,aAAa;AAAA,YACxB,WAAW,SAAS,UAAU;AAC5B,uBAAS,aAAa;AAAA,YACxB,WAAW,SAAS,QAAQ;AAC1B,uBAAS,aAAa;AAAA,YACxB,OAAO;AAEL,uBAAS,aAAa;AAAA,YACxB;AAAA,UACF;AACA,mBAAS,IAAI,IAAI;AAAA,QACnB;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,aAAa,CAAC,UAAU,SAASL,MAAK,QAAQ,QAAS,CAAAA,IAAG,KAAK,OAAO;AAAA,QAC5E,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAMA,IAAAI,0BAAyB,UAAU,oBAAoB,WAAY;AACjE,UAAI,QAAQ;AACZ,UAAI,gBAAgB,KAAK,QAAQ,UAAU;AAC3C,UAAI,gBAAgB,KAAK,QAAQ,mBAAmB,KAAK,QAAQ,iBAAiB,IAAI;AACtF,UAAI,gBAAgB,cAAc,iBAAiB,KAAK,QAAQ,qBAAqB,cAAc,aAAa;AAChH,UAAI,iBAAiB,eAAe;AAKlC,mBAAW,WAAY;AACrB,gBAAM,QAAQ,aAAa;AAAA,QAC7B,GAAG,UAAU,mBAAmB;AAAA,MAClC;AAAA,IACF;AACA,IAAAA,0BAAyB,UAAU,SAAS,SAAU,QAAQ,KAAK;AACjE,aAAO,QAAQ,SAAS,GAAG;AAAA,IAC7B;AACA,IAAAA,0BAAyB,UAAU,SAAS,SAAU,QAAQ,KAAK;AACjE,aAAO,SAAS;AAAA,IAClB;AACA,IAAAA,0BAAyB,UAAU,WAAW,SAAU,QAAQ,KAAK;AACnE,aAAO,SAAS;AAAA,IAClB;AAKA,IAAAA,0BAAyB,UAAU,WAAW,SAAU,KAAK;AAC3D,aAAO,OAAO,QAAQ,YAAY,SAAS,GAAG;AAAA,IAChD;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAClD,cAAUE,iBAAgB,MAAM;AAChC,aAASA,kBAAiB;AACxB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,gBAAe,WAAW,SAAU,MAAM;AACxC,aAAO,IAAIA,gBAAe,IAAI;AAAA,IAChC;AACA,IAAAA,gBAAe,UAAU,qBAAqB,WAAY;AACxD,UAAI,QAAQ;AACZ,UAAI,WAAW,KAAK,KAAK;AACzB,WAAK,gBAAgB,YAAY,SAAS,UAAU,SAAS,aAAa,MAAM,IAAI,WAAW;AAC/F,UAAI,KAAK,KAAK,UAAU,SAAS,aAAa,KAAK,GAAG;AACpD,aAAK,iBAAiB,IAAI;AAAA,MAC5B;AACA,WAAK,gBAAgB,SAAU,OAAO;AACpC,cAAM,WAAW,cAAc,KAAK;AAAA,MACtC;AACA,WAAK,kBAAkB,SAAU,OAAO;AACtC,cAAM,WAAW,gBAAgB,KAAK;AAAA,MACxC;AAGA,WAAK,4BAA4B,WAAY;AAC3C,iBAAS,KAAK,iBAAiB,SAAS,MAAM,iBAAiB;AAAA,UAC7D,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,WAAK,8BAA8B,WAAY;AAC7C,iBAAS,KAAK,oBAAoB,SAAS,MAAM,iBAAiB;AAAA,UAChE,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,WAAK,OAAO,WAAW,KAAK,aAAa;AACzC,WAAK,OAAO,UAAU,cAAc,KAAK,yBAAyB;AAClE,WAAK,OAAO,UAAU,cAAc,KAAK,2BAA2B;AAAA,IACtE;AACA,IAAAA,gBAAe,UAAU,UAAU,WAAY;AAC7C,WAAK,SAAS,WAAW,KAAK,aAAa;AAC3C,WAAK,SAAS,UAAU,cAAc,KAAK,yBAAyB;AACpE,WAAK,SAAS,UAAU,cAAc,KAAK,2BAA2B;AACtE,aAAO,UAAU,QAAQ,KAAK,IAAI;AAAA,IACpC;AACA,IAAAA,gBAAe,UAAU,SAAS,WAAY;AAC5C,aAAO,KAAK,WAAW,OAAO;AAAA,IAChC;AACA,IAAAA,gBAAe,UAAU,OAAO,WAAY;AAC1C,WAAK,WAAW,KAAK;AAAA,IACvB;AACA,IAAAA,gBAAe,UAAU,QAAQ,SAAU,kBAAkB;AAC3D,UAAI,qBAAqB,QAAQ;AAC/B,2BAAmB;AAAA,MACrB;AACA,WAAK,WAAW,MAAM,gBAAgB;AAAA,IACxC;AACA,WAAO,eAAeA,gBAAe,WAAW,aAAa;AAAA,MAC3D,KAAK,SAAU,WAAW;AACxB,aAAK,WAAW,aAAa,SAAS;AAAA,MACxC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AAED,IAAAA,gBAAe,UAAU,eAAe,SAAU,WAAW;AAC3D,WAAK,WAAW,aAAa,SAAS;AAAA,IACxC;AAEA,IAAAA,gBAAe,UAAU,8BAA8B,SAAU,SAAS;AACxE,WAAK,gBAAgB;AAAA,IACvB;AAEA,IAAAA,gBAAe,UAAU,mBAAmB,SAAU,SAAS;AAC7D,UAAI,SAAS;AACX,aAAK,KAAK,UAAU,IAAI,aAAa,KAAK;AAAA,MAC5C,OAAO;AACL,aAAK,KAAK,UAAU,OAAO,aAAa,KAAK;AAAA,MAC/C;AACA,WAAK,WAAW,iBAAiB,OAAO;AAAA,IAC1C;AAEA,IAAAA,gBAAe,UAAU,sBAAsB,SAAU,GAAG,GAAG;AAC7D,WAAK,WAAW,oBAAoB,GAAG,CAAC;AACxC,WAAK,aAAa,IAAI;AAAA,IACxB;AAIA,IAAAA,gBAAe,UAAU,kBAAkB,SAAU,QAAQ;AAC3D,WAAK,WAAW,gBAAgB,MAAM;AAAA,IACxC;AACA,IAAAA,gBAAe,UAAU,kBAAkB,SAAU,QAAQ;AAC3D,WAAK,WAAW,gBAAgB,MAAM;AAAA,IACxC;AACA,IAAAA,gBAAe,UAAU,uBAAuB,WAAY;AAC1D,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,iBAAO,MAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QAC9C;AAAA,QACA,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,SAAS,SAAS;AAAA,QAChD;AAAA,QACA,WAAW,WAAY;AACrB,iBAAO,CAAC,CAAC,MAAM;AAAA,QACjB;AAAA,QACA,aAAa,WAAY;AACvB,iBAAO,MAAM,KAAK,yBAAyB,QAAQ,cAAc,CAAC,CAAC;AAAA,QACrE;AAAA,QACA,eAAe,WAAY;AACzB,gBAAM,KAAK,yBAAyB,QAAQ,eAAe,CAAC,CAAC;AAAA,QAC/D;AAAA,QACA,YAAY,WAAY;AACtB,iBAAO,MAAM,KAAK,yBAAyB,QAAQ,cAAc,CAAC,CAAC;AAAA,QACrE;AAAA,QACA,eAAe,WAAY;AACzB,iBAAO,MAAM,KAAK,yBAAyB,QAAQ,eAAe,CAAC,CAAC;AAAA,QACtE;AAAA,QACA,sBAAsB,SAAU,IAAI;AAClC,iBAAO,MAAM,KAAK,SAAS,EAAE;AAAA,QAC/B;AAAA,QACA,OAAO,WAAY;AACjB,iBAAO,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,WAAW,MAAM;AAAA,QACxE;AAAA,QACA,oBAAoB,SAAU,QAAQ;AACpC,cAAI,eAAe,uBAAuB,QAAQ,WAAW,IAAI;AACjE,gBAAM,KAAK,MAAM,YAAY,cAAc,MAAM;AAAA,QACnD;AAAA,QACA,WAAW,WAAY;AACrB,iBAAO,SAAS,kBAAkB,MAAM;AAAA,QAC1C;AAAA,QACA,WAAW,WAAY;AACrB,gBAAM,gBAAgB,SAAS;AAAA,QACjC;AAAA,QACA,cAAc,WAAY;AACxB,cAAI,MAAM,KAAK,SAAS,SAAS,aAAa,GAAG;AAC/C,gBAAI,MAAM,iBAAiB,MAAM,cAAc,OAAO;AACpD,oBAAM,cAAc,MAAM;AAAA,YAC5B;AAAA,UACF;AAAA,QACF;AAAA,QACA,oBAAoB,WAAY;AAC9B,iBAAO;AAAA,YACL,OAAO,MAAM,KAAK;AAAA,YAClB,QAAQ,MAAM,KAAK;AAAA,UACrB;AAAA,QACF;AAAA,QACA,qBAAqB,WAAY;AAC/B,iBAAO,MAAM,gBAAgB,MAAM,cAAc,sBAAsB,IAAI;AAAA,QAC7E;AAAA,QACA,qBAAqB,WAAY;AAC/B,iBAAO;AAAA,YACL,OAAO,OAAO;AAAA,YACd,QAAQ,OAAO;AAAA,UACjB;AAAA,QACF;AAAA,QACA,mBAAmB,WAAY;AAC7B,iBAAO;AAAA,YACL,OAAO,SAAS,KAAK;AAAA,YACrB,QAAQ,SAAS,KAAK;AAAA,UACxB;AAAA,QACF;AAAA,QACA,iBAAiB,WAAY;AAC3B,iBAAO;AAAA,YACL,GAAG,OAAO;AAAA,YACV,GAAG,OAAO;AAAA,UACZ;AAAA,QACF;AAAA,QACA,aAAa,SAAU,UAAU;AAC/B,cAAI,WAAW,MAAM;AACrB,mBAAS,MAAM,OAAO,UAAU,WAAW,SAAS,OAAO,OAAO;AAClE,mBAAS,MAAM,QAAQ,WAAW,WAAW,SAAS,QAAQ,OAAO;AACrE,mBAAS,MAAM,MAAM,SAAS,WAAW,SAAS,MAAM,OAAO;AAC/D,mBAAS,MAAM,SAAS,YAAY,WAAW,SAAS,SAAS,OAAO;AAAA,QAC1E;AAAA,QACA,cAAc,SAAU,QAAQ;AAC9B,gBAAM,KAAK,MAAM,YAAY;AAAA,QAC/B;AAAA,MACF;AAEA,aAAO,IAAI,yBAAyB,OAAO;AAAA,IAC7C;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AAwBd,IAAI,aAAa;AAAA,EACf,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,MAAM;AACR;AACA,IAAI,UAAU;AAAA,EACZ,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,oBAAoB;AACtB;AACA,IAAI,UAAU;AAAA,EACZ,kBAAkB;AACpB;AACA,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAC5B,EAAAA,mBAAkBA,mBAAkB,MAAM,IAAI,CAAC,IAAI;AACnD,EAAAA,mBAAkBA,mBAAkB,WAAW,IAAI,CAAC,IAAI;AACxD,EAAAA,mBAAkBA,mBAAkB,YAAY,IAAI,CAAC,IAAI;AACzD,EAAAA,mBAAkBA,mBAAkB,WAAW,IAAI,CAAC,IAAI;AAC1D,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAwBhD,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,SAAS;AAClC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,mBAAkB,cAAc,GAAG,OAAO,CAAC,KAAK;AACpG,YAAM,2BAA2B;AACjC,YAAM,oBAAoB,kBAAkB;AAC5C,YAAM,gBAAgB;AACtB,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,oBAAmB,cAAc;AAAA,MACrD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,oBAAmB,WAAW;AAAA,MAClD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,oBAAmB,WAAW;AAAA,MAClD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,oBAAmB,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAIzD,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,0BAA0B,WAAY;AACpC,mBAAO;AAAA,UACT;AAAA,UACA,+BAA+B,WAAY;AACzC,mBAAO;AAAA,UACT;AAAA,UACA,8BAA8B,WAAY;AACxC,mBAAO;AAAA,UACT;AAAA,UACA,mCAAmC,WAAY;AAC7C,mBAAO;AAAA,UACT;AAAA,UACA,gCAAgC,WAAY;AAC1C,mBAAO;AAAA,UACT;AAAA,UACA,sBAAsB,WAAY;AAChC,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,gBAAgB,WAAY;AAC1B,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,eAAe,WAAY;AACzB,mBAAO;AAAA,UACT;AAAA,UACA,iCAAiC,WAAY;AAC3C,mBAAO;AAAA,UACT;AAAA,UACA,yBAAyB,WAAY;AACnC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,mBAAkB,UAAU,UAAU,WAAY;AAChD,UAAI,KAAK,0BAA0B;AACjC,qBAAa,KAAK,wBAAwB;AAAA,MAC5C;AACA,WAAK,QAAQ,aAAa;AAAA,IAC5B;AACA,IAAAA,mBAAkB,UAAU,gBAAgB,SAAU,KAAK;AACzD,UAAI,MAAM,IAAI,KACZ,UAAU,IAAI;AAChB,UAAI,QAAQ,QAAQ,SAAS,YAAY;AACzC,UAAI,OAAO;AACT,aAAK,QAAQ;AAAA;AAAA,UAAqC;AAAA,QAAI;AAAA,MACxD;AAAA,IACF;AACA,IAAAA,mBAAkB,UAAU,mBAAmB,SAAU,UAAU;AACjE,UAAI,QAAQ;AACZ,UAAI,QAAQ,KAAK,QAAQ,gBAAgB,QAAQ;AACjD,UAAI,QAAQ,GAAG;AACb;AAAA,MACF;AACA,WAAK,QAAQ,eAAe;AAAA,QAC1B;AAAA,MACF,CAAC;AACD,UAAI,mBAAmB,KAAK,QAAQ,+BAA+B,OAAO,QAAQ,kBAAkB,MAAM;AAC1G,WAAK,QAAQ,aAAa,gBAAgB;AAE1C,WAAK,2BAA2B,WAAW,WAAY;AAErD,YAAI,kBAAkB,MAAM,QAAQ,gBAAgB,QAAQ;AAC5D,YAAI,mBAAmB,KAAK,MAAM,QAAQ,wBAAwB,eAAe,GAAG;AAClF,gBAAM,iBAAiB,eAAe;AAAA,QACxC;AAAA,MACF,GAAG,yBAAyB,QAAQ,yBAAyB;AAAA,IAC/D;AACA,IAAAA,mBAAkB,UAAU,0BAA0B,WAAY;AAChE,cAAQ,KAAK,mBAAmB;AAAA,QAC9B,KAAK,kBAAkB;AACrB,eAAK,QAAQ,iBAAiB,CAAC;AAC/B;AAAA,QACF,KAAK,kBAAkB;AACrB,eAAK,QAAQ,iBAAiB,KAAK,QAAQ,iBAAiB,IAAI,CAAC;AACjE;AAAA,QACF,KAAK,kBAAkB;AAErB;AAAA,QACF;AACE,eAAK,QAAQ,cAAc;AAC3B;AAAA,MACJ;AAAA,IACF;AAMA,IAAAA,mBAAkB,UAAU,uBAAuB,SAAU,YAAY;AACvE,WAAK,oBAAoB;AAAA,IAC3B;AAEA,IAAAA,mBAAkB,UAAU,mBAAmB,WAAY;AACzD,aAAO,KAAK;AAAA,IACd;AAKA,IAAAA,mBAAkB,UAAU,mBAAmB,SAAU,OAAO;AAC9D,WAAK,eAAe,KAAK;AACzB,UAAI,CAAC,KAAK,QAAQ,wBAAwB,KAAK,GAAG;AAChD,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AACA,UAAI,oBAAoB,KAAK,QAAQ,gCAAgC,KAAK;AAC1E,UAAI,qBAAqB,GAAG;AAC1B,aAAK,QAAQ,kCAAkC,mBAAmB,QAAQ,iBAAiB;AAC3F,aAAK,QAAQ,8BAA8B,mBAAmB,WAAW,uBAAuB;AAAA,MAClG;AACA,WAAK,QAAQ,yBAAyB,OAAO,WAAW,uBAAuB;AAC/E,WAAK,QAAQ,6BAA6B,OAAO,QAAQ,mBAAmB,MAAM;AAClF,WAAK,gBAAgB;AAAA,IACvB;AAMA,IAAAA,mBAAkB,UAAU,aAAa,SAAU,OAAO,WAAW;AACnE,WAAK,eAAe,KAAK;AACzB,UAAI,WAAW;AACb,aAAK,QAAQ,8BAA8B,OAAO,aAAa,wBAAwB;AACvF,aAAK,QAAQ,6BAA6B,OAAO,QAAQ,oBAAoB,OAAO;AAAA,MACtF,OAAO;AACL,aAAK,QAAQ,yBAAyB,OAAO,aAAa,wBAAwB;AAClF,aAAK,QAAQ,6BAA6B,OAAO,QAAQ,oBAAoB,MAAM;AAAA,MACrF;AAAA,IACF;AACA,IAAAA,mBAAkB,UAAU,iBAAiB,SAAU,OAAO;AAC5D,UAAI,WAAW,KAAK,QAAQ,iBAAiB;AAC7C,UAAI,iBAAiB,SAAS,KAAK,QAAQ;AAC3C,UAAI,CAAC,gBAAgB;AACnB,cAAM,IAAI,MAAM,qDAAqD;AAAA,MACvE;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAAuB,SAAU,QAAQ;AAC3C,cAAUC,UAAS,MAAM;AACzB,aAASA,WAAU;AACjB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,SAAQ,WAAW,SAAU,MAAM;AACjC,aAAO,IAAIA,SAAQ,IAAI;AAAA,IACzB;AACA,IAAAA,SAAQ,UAAU,aAAa,SAAU,oBAAoB,aAAa;AACxE,UAAI,uBAAuB,QAAQ;AACjC,6BAAqB,SAAU,IAAI;AACjC,iBAAO,IAAI,eAAe,EAAE;AAAA,QAC9B;AAAA,MACF;AACA,UAAI,gBAAgB,QAAQ;AAC1B,sBAAc,SAAU,IAAI;AAC1B,iBAAO,IAAI,QAAQ,EAAE;AAAA,QACvB;AAAA,MACF;AACA,WAAK,qBAAqB;AAC1B,WAAK,cAAc;AAAA,IACrB;AACA,IAAAA,SAAQ,UAAU,qBAAqB,WAAY;AACjD,UAAI,QAAQ;AACZ,WAAK,cAAc,KAAK,mBAAmB,KAAK,IAAI;AACpD,UAAI,OAAO,KAAK,KAAK,cAAc,QAAQ,aAAa;AACxD,UAAI,MAAM;AACR,aAAK,OAAO,KAAK,YAAY,IAAI;AACjC,aAAK,KAAK,YAAY;AAAA,MACxB,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AACA,WAAK,gBAAgB,SAAU,KAAK;AAClC,cAAM,WAAW,cAAc,GAAG;AAAA,MACpC;AACA,WAAK,mBAAmB,SAAU,KAAK;AACrC,cAAM,WAAW,iBAAiB,MAAM,MAAM,IAAI,OAAO,KAAK,CAAC;AAAA,MACjE;AACA,WAAK,0BAA0B,WAAY;AACzC,cAAM,WAAW,wBAAwB;AAAA,MAC3C;AACA,WAAK,YAAY,OAAO,yBAAyB,QAAQ,cAAc,KAAK,uBAAuB;AACnG,WAAK,OAAO,WAAW,KAAK,aAAa;AACzC,WAAK,OAAO,kBAAkB,QAAQ,cAAc,KAAK,gBAAgB;AAAA,IAC3E;AACA,IAAAA,SAAQ,UAAU,UAAU,WAAY;AACtC,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,QAAQ;AAAA,MACpB;AACA,WAAK,YAAY,QAAQ;AACzB,WAAK,YAAY,SAAS,yBAAyB,QAAQ,cAAc,KAAK,uBAAuB;AACrG,WAAK,SAAS,WAAW,KAAK,aAAa;AAC3C,WAAK,SAAS,kBAAkB,QAAQ,cAAc,KAAK,gBAAgB;AAC3E,aAAO,UAAU,QAAQ,KAAK,IAAI;AAAA,IACpC;AACA,WAAO,eAAeA,SAAQ,WAAW,QAAQ;AAAA,MAC/C,KAAK,WAAY;AACf,eAAO,KAAK,YAAY,OAAO;AAAA,MACjC;AAAA,MACA,KAAK,SAAU,OAAO;AACpB,YAAI,OAAO;AACT,eAAK,YAAY,KAAK;AAAA,QACxB,OAAO;AACL,eAAK,YAAY,MAAM;AAAA,QACzB;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,aAAa;AAAA,MACpD,KAAK,WAAY;AACf,eAAO,KAAK,OAAO,KAAK,KAAK,YAAY;AAAA,MAC3C;AAAA,MACA,KAAK,SAAU,OAAO;AACpB,YAAI,KAAK,MAAM;AACb,eAAK,KAAK,YAAY;AAAA,QACxB;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKvD,KAAK,SAAU,OAAO;AACpB,YAAI,KAAK,MAAM;AACb,eAAK,KAAK,eAAe;AAAA,QAC3B;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,uBAAuB;AAAA;AAAA;AAAA;AAAA,MAI9D,KAAK,WAAY;AACf,eAAO,KAAK,OAAO,KAAK,KAAK,sBAAsB;AAAA,MACrD;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AAcD,IAAAA,SAAQ,UAAU,qBAAqB,SAAU,UAAU,eAAe;AACxE,UAAI,KAAK,MAAM;AACb,eAAO,KAAK,KAAK,mBAAmB,UAAU,aAAa;AAAA,MAC7D;AACA,aAAO;AAAA,IACT;AAKA,IAAAA,SAAQ,UAAU,SAAS,WAAY;AACrC,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,OAAO;AAAA,MACnB;AAAA,IACF;AACA,WAAO,eAAeA,SAAQ,WAAW,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMhD,KAAK,WAAY;AACf,eAAO,KAAK,OAAO,KAAK,KAAK,eAAe,CAAC;AAAA,MAC/C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO1D,KAAK,SAAU,iBAAiB;AAC9B,YAAI,KAAK,MAAM;AACb,eAAK,KAAK,kBAAkB;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMxD,KAAK,WAAY;AACf,eAAO,KAAK,OAAO,KAAK,KAAK,gBAAgB,UAAU;AAAA,MACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,KAAK,SAAU,OAAO;AACpB,YAAI,KAAK,MAAM;AACb,eAAK,KAAK,gBAAgB;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,SAAQ,WAAW,aAAa;AAAA,MACpD,KAAK,SAAU,WAAW;AACxB,aAAK,YAAY,YAAY;AAAA,MAC/B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AAOD,IAAAA,SAAQ,UAAU,uBAAuB,SAAU,YAAY;AAC7D,WAAK,WAAW,qBAAqB,UAAU;AAAA,IACjD;AAIA,IAAAA,SAAQ,UAAU,kBAAkB,SAAU,QAAQ;AACpD,WAAK,YAAY,gBAAgB,MAAM;AAAA,IACzC;AACA,IAAAA,SAAQ,UAAU,kBAAkB,SAAU,QAAQ;AACpD,WAAK,YAAY,gBAAgB,MAAM;AAAA,IACzC;AAKA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,OAAO;AACpD,WAAK,WAAW,iBAAiB,KAAK;AAAA,IACxC;AAMA,IAAAA,SAAQ,UAAU,aAAa,SAAU,OAAO,WAAW;AACzD,WAAK,WAAW,WAAW,OAAO,SAAS;AAAA,IAC7C;AAIA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,OAAO;AACpD,UAAI,QAAQ,KAAK;AACjB,UAAI,QAAQ,MAAM,QAAQ;AACxB,eAAO,KAAK,MAAM,KAAK;AAAA,MACzB,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAKA,IAAAA,SAAQ,UAAU,wBAAwB,SAAU,OAAO;AACzD,UAAI,OAAO,KAAK,iBAAiB,KAAK;AACtC,UAAI,QAAQ,KAAK,MAAM;AACrB,eAAO,KAAK,KAAK,eAAe,IAAI,KAAK;AAAA,MAC3C;AACA,aAAO;AAAA,IACT;AACA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,SAAS;AACtD,WAAK,YAAY,iBAAiB,OAAO;AAAA,IAC3C;AACA,IAAAA,SAAQ,UAAU,eAAe,SAAU,WAAW;AACpD,WAAK,YAAY,aAAa,SAAS;AAAA,IACzC;AACA,IAAAA,SAAQ,UAAU,sBAAsB,SAAU,GAAG,GAAG;AACtD,WAAK,YAAY,oBAAoB,GAAG,CAAC;AAAA,IAC3C;AAIA,IAAAA,SAAQ,UAAU,mBAAmB,SAAU,SAAS;AACtD,WAAK,YAAY,gBAAgB;AAAA,IACnC;AACA,IAAAA,SAAQ,UAAU,uBAAuB,WAAY;AACnD,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,0BAA0B,SAAU,OAAO,WAAW;AACpD,cAAI,OAAO,MAAM;AACjB,eAAK,KAAK,EAAE,UAAU,IAAI,SAAS;AAAA,QACrC;AAAA,QACA,+BAA+B,SAAU,OAAO,WAAW;AACzD,cAAI,OAAO,MAAM;AACjB,eAAK,KAAK,EAAE,UAAU,OAAO,SAAS;AAAA,QACxC;AAAA,QACA,8BAA8B,SAAU,OAAO,MAAM,OAAO;AAC1D,cAAI,OAAO,MAAM;AACjB,eAAK,KAAK,EAAE,aAAa,MAAM,KAAK;AAAA,QACtC;AAAA,QACA,mCAAmC,SAAU,OAAO,MAAM;AACxD,cAAI,OAAO,MAAM;AACjB,eAAK,KAAK,EAAE,gBAAgB,IAAI;AAAA,QAClC;AAAA,QACA,gCAAgC,SAAU,OAAO,MAAM;AACrD,cAAI,OAAO,MAAM;AACjB,iBAAO,KAAK,KAAK,EAAE,aAAa,IAAI;AAAA,QACtC;AAAA,QACA,sBAAsB,SAAU,SAAS,WAAW;AAClD,iBAAO,QAAQ,UAAU,SAAS,SAAS;AAAA,QAC7C;AAAA,QACA,cAAc,SAAU,kBAAkB;AACxC,gBAAM,YAAY,MAAM,gBAAgB;AAAA,QAC1C;AAAA,QACA,iBAAiB,SAAU,SAAS;AAClC,iBAAO,MAAM,MAAM,QAAQ,OAAO;AAAA,QACpC;AAAA,QACA,gBAAgB,SAAU,SAAS;AACjC,gBAAM,KAAK,QAAQ,gBAAgB;AAAA,YACjC,OAAO,QAAQ;AAAA,YACf,MAAM,MAAM,MAAM,QAAQ,KAAK;AAAA,UACjC,CAAC;AAAA,QACH;AAAA,QACA,kBAAkB,WAAY;AAC5B,iBAAO,MAAM,MAAM;AAAA,QACrB;AAAA,QACA,kBAAkB,SAAU,OAAO;AACjC,gBAAM,MAAM,KAAK,EAAE,MAAM;AAAA,QAC3B;AAAA,QACA,eAAe,WAAY;AACzB,gBAAM,KAAK,cAAc,QAAQ,aAAa,EAAE,MAAM;AAAA,QACxD;AAAA,QACA,yBAAyB,SAAU,OAAO;AACxC,iBAAO,CAAC,CAAC,QAAQ,MAAM,MAAM,KAAK,GAAG,MAAM,WAAW,oBAAoB;AAAA,QAC5E;AAAA,QACA,iCAAiC,SAAU,OAAO;AAChD,cAAI,mBAAmB,QAAQ,MAAM,MAAM,KAAK,GAAG,MAAM,WAAW,oBAAoB;AACxF,cAAI,iBAAiB,iBAAiB,cAAc,MAAM,WAAW,uBAAuB;AAC5F,iBAAO,iBAAiB,MAAM,MAAM,QAAQ,cAAc,IAAI;AAAA,QAChE;AAAA,MACF;AAEA,aAAO,IAAI,kBAAkB,OAAO;AAAA,IACtC;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;", "names": ["matches", "MDCListFoundation", "_a", "MDCList", "CornerBit", "Corner", "MDCMenuSurfaceFoundation", "_b", "MDCMenuSurface", "DefaultFocusState", "MDCMenuFoundation", "MDCMenu"]}