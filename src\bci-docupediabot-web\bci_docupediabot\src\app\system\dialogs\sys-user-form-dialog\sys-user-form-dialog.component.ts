import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SysUserResponseDTO, SysUserAdd, SysUserUpdate } from '@shared/models/system.model';
import { SysUserService } from '@shared/services/system/sys-user.service';
import { Collection } from '@shared/models/docupedia.model';
import { CollectionService } from '@shared/services/docupedia/collection.service';

interface DialogData {
  mode: 'add' | 'edit';
  user?: SysUserResponseDTO;
}

@Component({
  selector: 'app-sys-user-form-dialog',
  templateUrl: './sys-user-form-dialog.component.html',
  styleUrls: ['./sys-user-form-dialog.component.scss']
})
export class SysUserFormDialogComponent implements OnInit {
  userForm: FormGroup;
  loading = false;
  collections: any[] = []; // TODO: Load from collection service if needed

  constructor(
    private fb: FormBuilder,
    private sysUserService: SysUserService,
    private dialogRef: MatDialogRef<SysUserFormDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.userForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.data.mode === 'edit' && this.data.user) {
      this.populateForm(this.data.user);
    }
  }

  get dialogTitle(): string {
    return this.data.mode === 'add' ? 'Add New User' : 'Edit User';
  }

  get dialogSubtitle(): string {
    return this.data.mode === 'add'
      ? 'Enter the user information below.'
      : 'Update the user information below.';
  }

  private createForm(): FormGroup {
    return this.fb.group({
      userNTAccount: ['', [Validators.required]],
      userName: ['', [Validators.required]],
      givenName: [''],
      sn: [''],
      mail: ['', [Validators.required, Validators.email]],
      department: [''],
      favCollecitonId: [null],
      docupediaToken: [''],
      status: [1, [Validators.required]]
    });
  }

  private populateForm(user: SysUserResponseDTO): void {
    this.userForm.patchValue({
      userNTAccount: user.userNTAccount,
      userName: user.userName,
      givenName: user.givenName,
      sn: user.sn,
      mail: user.mail,
      department: user.department,
      favCollecitonId: user.favCollecitonId,
      docupediaToken: user.docupediaToken,
      status: user.status
    });
  }

  save(): void {
    if (this.userForm.valid) {
      this.loading = true;

      if (this.data.mode === 'add') {
        this.addUser();
      } else {
        this.updateUser();
      }
    }
  }

  private addUser(): void {
    const userData: SysUserAdd = this.userForm.value;

    this.sysUserService.addUser(userData).subscribe({
      next: (result) => {
        this.loading = false;
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Error adding user:', error);
        this.loading = false;
      }
    });
  }

  private updateUser(): void {
    if (!this.data.user) return;

    const userData: SysUserUpdate = {
      id: this.data.user.id,
      ...this.userForm.value
    };

    this.sysUserService.updateUser(userData).subscribe({
      next: (result) => {
        this.loading = false;
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Error updating user:', error);
        this.loading = false;
      }
    });
  }

  close(): void {
    this.dialogRef.close(false);
  }
}
