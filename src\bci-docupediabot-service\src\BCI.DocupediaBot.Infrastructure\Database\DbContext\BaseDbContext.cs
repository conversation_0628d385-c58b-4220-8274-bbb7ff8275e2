﻿using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Entity;
using Bosch.Foundation.Abstractions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace BCI.DocupediaBot.Infrastructure.Database
{
  [ExcludeFromCodeCoverage]
  public abstract class BaseDbContext : DbContext
  {
    private readonly ITenantAccessor tenantAccessor;
    private readonly ICurrentUserAccessor currentUserAccessor;

    public DatabaseDescription DatabaseDescpriton { get; }

    protected BaseDbContext(
      DatabaseDescription databaseDescpriton,
      ITenantAccessor tenantAccessor,
      ICurrentUserAccessor currentUserAccessor)
    {
      DatabaseDescpriton = databaseDescpriton;
      this.tenantAccessor = tenantAccessor;
      this.currentUserAccessor = currentUserAccessor;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
      optionsBuilder.AddInterceptors(new DbInterceptor(this.currentUserAccessor?.UserId, this.tenantAccessor?.TenantId));

      base.OnConfiguring(optionsBuilder);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
      AppendGlobalFilters(modelBuilder);

      base.OnModelCreating(modelBuilder);
    }

    #region Global Filter
    private void AppendGlobalFilters(ModelBuilder modelBuilder)
    {
      BindingFlags bindingAttr = BindingFlags.NonPublic | BindingFlags.Instance;
      var softDeletedFilterExpressionMethod = typeof(BaseDbContext).GetMethod(nameof(BaseDbContext.CreateSoftDeleteFilterExp), bindingAttr);
      var tenantIdFilterExpressionMethod = typeof(BaseDbContext).GetMethod(nameof(BaseDbContext.CreateTenantIdFilterExp), bindingAttr);
      var tenandIdAndSoftDeleteFilterExpressionMethod = typeof(BaseDbContext).GetMethod(nameof(BaseDbContext.CreateBothTenantIdAndSoftDeleteFilterExp), bindingAttr);

      var modelBuilderType = typeof(ModelBuilder);
      var modelBuilderEntityMethod = modelBuilderType.GetMethods().First(x => x.Name == nameof(ModelBuilder.Entity));

      var entityTypes = modelBuilder.Model.GetEntityTypes();
      foreach (var entityType in entityTypes)
      {
        var entityMethod = modelBuilderEntityMethod.MakeGenericMethod(entityType.ClrType);
        var entityTypeBuilder = entityMethod.Invoke(modelBuilder, Array.Empty<object>());
        var exp = GenerateGlobalFilterExp(softDeletedFilterExpressionMethod, tenantIdFilterExpressionMethod, tenandIdAndSoftDeleteFilterExpressionMethod, entityType);

        entityTypeBuilder.GetType().GetMethods().First(x => x.Name == "HasQueryFilter").Invoke(entityTypeBuilder, new object[] { exp });
      }
    }

    private object GenerateGlobalFilterExp(MethodInfo softDeletedFilterExpressionMethod, MethodInfo tenantIdFilterExpressionMethod, MethodInfo tenandIdAndSoftDeleteFilterExpressionMethod, IMutableEntityType entityType)
    {
      bool isTenantEntity = false;
      bool isSoftDeleteEntity = false;

      if (entityType.ClrType.IsAssignableTo(typeof(ITenantEntity)))
      {
        isTenantEntity = true;
      }

      if (entityType.ClrType.IsAssignableTo(typeof(ISoftDeleteEntity)))
      {
        isSoftDeleteEntity = true;
      }

      object exp = null;
      if (isTenantEntity && isSoftDeleteEntity)
      {

        exp = tenandIdAndSoftDeleteFilterExpressionMethod.MakeGenericMethod(entityType.ClrType).Invoke(this, Array.Empty<object>());
      }
      else if (isTenantEntity)
      {
        exp = tenantIdFilterExpressionMethod.MakeGenericMethod(entityType.ClrType).Invoke(this, Array.Empty<object>());
      }
      else if (isSoftDeleteEntity)
      {
        exp = softDeletedFilterExpressionMethod.MakeGenericMethod(entityType.ClrType).Invoke(this, Array.Empty<object>());
      }

      return exp;
    }

    private Expression<Func<T, bool>> CreateBothTenantIdAndSoftDeleteFilterExp<T>() where T : ISoftDeleteEntity, ITenantEntity
    {
      Expression<Func<T, bool>> exp = (x) => !x.IsDeleted && x.TenantId == this.tenantAccessor.TenantId;

      return exp;
    }

    private Expression<Func<T, bool>> CreateTenantIdFilterExp<T>() where T : ITenantEntity
    {
      Expression<Func<T, bool>> exp = (x) => x.TenantId == this.tenantAccessor.TenantId;

      return exp;
    }

    private static Expression<Func<T, bool>> CreateSoftDeleteFilterExp<T>() where T : ISoftDeleteEntity
    {
      Expression<Func<T, bool>> exp = (x) => !x.IsDeleted;

      return exp;
    }

    #endregion

  }
}