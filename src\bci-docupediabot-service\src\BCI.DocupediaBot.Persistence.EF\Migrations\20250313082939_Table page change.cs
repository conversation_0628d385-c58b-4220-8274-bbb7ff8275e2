﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{

    public partial class Tablepagechange : Migration
    {

        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Page_Url",
                table: "Page");

            migrationBuilder.CreateIndex(
                name: "IX_Page_CollectionId_SourceId",
                table: "Page",
                columns: new[] { "CollectionId", "SourceId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Content_SourceId",
                table: "Content",
                column: "SourceId",
                unique: true);
        }


        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Page_CollectionId_SourceId",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Content_SourceId",
                table: "Content");

            migrationBuilder.CreateIndex(
                name: "IX_Page_Url",
                table: "Page",
                column: "Url",
                unique: true);
        }
    }
}
