﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Content
{
  public class ContentService : IContentService
	{
		private readonly IContentRepository _contentRepository;
		private readonly IMapper _mapper;
		private readonly IContentsInPageService _contentsInPageService;
		private readonly ILogger<ContentService> _logger;

    public ContentService(
        IContentRepository contentRepository,
        IMapper mapper,
        IContentsInPageService contentsInPageService,
        ILogger<ContentService> logger)
    {
      _contentRepository = contentRepository ?? throw new ArgumentNullException(nameof(contentRepository));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
      _contentsInPageService = contentsInPageService ?? throw new ArgumentNullException(nameof(contentsInPageService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ResponseResult> AddContentsAsync(IEnumerable<ContentAddDTO> dtos, Guid pageId)
		{
			if (dtos == null || !dtos.Any())
			{
				_logger.LogWarning("AddContentsAsync received null or empty DTO list for page ID: {PageId}", pageId);
				return new ResponseResult { IsSuccess = false, Msg = "Content data cannot be null or empty." };
			}

			_logger.LogInformation("Adding contents for page ID: {PageId}", pageId);

			var allExistingContents = await _contentRepository.GetAllAsync();
			var contentIdsToMap = new List<Guid>();
			int newContentCount = 0;

			foreach (var dto in dtos)
			{
				var existingContent = allExistingContents.FirstOrDefault(c => c.SourceId == dto.SourceId);
				if (existingContent != null)
				{
					contentIdsToMap.Add(existingContent.Id);
				}
				else
				{
					var content = _mapper.Map<Domain.Entities.Content>(dto);
					await _contentRepository.CreateAsync(content);
					contentIdsToMap.Add(content.Id);
					newContentCount++;
				}
			}

			foreach (var contentId in contentIdsToMap)
			{
				await _contentsInPageService.AddMappingAsync(pageId, contentId);
			}

			string msg = newContentCount > 0
					? $"Added {newContentCount} new contents. Mapped {contentIdsToMap.Count} contents to page {pageId}."
					: $"No new contents added; all contents reused. Mapped {contentIdsToMap.Count} contents to page {pageId}.";

			_logger.LogInformation("Successfully processed contents for page ID: {PageId}. {Message}", pageId, msg);
			return new ResponseResult { IsSuccess = true, Msg = msg };
		}

		public async Task<ResponseResult> DeleteContentByContentIdAsync(Guid contentId)
		{
			_logger.LogInformation("Deleting content with ID: {ContentId}", contentId);

			var content = await _contentRepository.GetAsync(contentId);
			if (content == null)
			{
				_logger.LogWarning("Content not found for ID: {ContentId}", contentId);
				return new ResponseResult { IsSuccess = false, Msg = "Content not found." };
			}

			var allContentsInPage = await _contentsInPageService.QueryMappingsAsync();
			if (allContentsInPage.Any(m => m.ContentId == contentId))
			{
				var deleteResult = await _contentsInPageService.DeleteMappingsByContentIdAsync(contentId);
				if (!deleteResult.IsSuccess)
				{
					_logger.LogWarning("Failed to delete mappings for content ID: {ContentId}. Error: {Error}", contentId, deleteResult.Msg);
					return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete content mappings: {deleteResult.Msg}" };
				}
			}

			_logger.LogInformation("Content {ContentId} mappings deleted successfully.", contentId);
			return new ResponseResult { IsSuccess = true, Msg = "Content mappings deleted successfully." };
		}

		public async Task<ContentDocupediaResponseDTO> QueryContentByContentId(Guid contentId)
		{
			_logger.LogInformation("Querying content by ID: {ContentId}", contentId);

			var content = await _contentRepository.GetAsync(contentId);
			if (content == null)
			{
				_logger.LogWarning("Content not found for ID: {ContentId}", contentId);
				return null;
			}

			return _mapper.Map<ContentDocupediaResponseDTO>(content);
		}

		public async Task<List<ContentResponseDTO>> QueryContentsByPageIdAsync(Guid pageId)
		{
			_logger.LogInformation("Querying contents for page ID: {PageId}", pageId);

			var contentIds = await _contentsInPageService.QueryContentIdsByPageIdAsync(pageId);
			if (contentIds == null || !contentIds.Any())
			{
				_logger.LogInformation("No contents found for page ID: {PageId}", pageId);
				return new List<ContentResponseDTO>();
			}

			var allContents = await _contentRepository.GetAllAsync();
			var contents = allContents.Where(c => contentIds.Contains(c.Id))
															 .Select(c => _mapper.Map<ContentResponseDTO>(c))
															 .ToList();

			_logger.LogInformation("Retrieved {Count} contents for page ID: {PageId}", contents.Count, pageId);
			return contents;
		}

		public async Task<ResponseResult> UpdateContentAsync(ContentUpdateDTO dto)
		{
			if (dto == null)
			{
				_logger.LogWarning("UpdateContentAsync received null DTO.");
				return new ResponseResult { IsSuccess = false, Msg = "Content data cannot be null." };
			}

			_logger.LogInformation("Updating content with ID: {ContentId}", dto.Id);

			var content = await _contentRepository.GetAsync(dto.Id);
			if (content == null)
			{
				_logger.LogWarning("Content not found for ID: {ContentId}", dto.Id);
				return new ResponseResult { IsSuccess = false, Msg = "Content not found." };
			}

			_mapper.Map(dto, content);
			try
			{
				await _contentRepository.UpdateAsync(content);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to update content ID: {ContentId}", dto.Id);
				return new ResponseResult { IsSuccess = false, Msg = $"Failed to update content: {ex.Message}" };
			}

			_logger.LogInformation("Content {ContentId} updated successfully.", dto.Id);
			return new ResponseResult { IsSuccess = true, Msg = "Content updated successfully." };
		}

		public async Task<ResponseResult> UpdateContentsAsync(IList<ContentUpdateDTO> dtos)
		{
			if (dtos == null || !dtos.Any())
			{
				_logger.LogWarning("UpdateContentsAsync received null or empty DTO list.");
				return new ResponseResult { IsSuccess = false, Msg = "Content data cannot be null or empty." };
			}

			_logger.LogInformation("Updating {Count} contents", dtos.Count);

			var contents = new List<Domain.Entities.Content>();
			foreach (var dto in dtos)
			{
				var content = await _contentRepository.GetAsync(dto.Id);
				if (content == null)
				{
					_logger.LogWarning("Content not found for ID: {ContentId}", dto.Id);
					return new ResponseResult { IsSuccess = false, Msg = $"Content not found for ID: {dto.Id}" };
				}
				_mapper.Map(dto, content);
				contents.Add(content);
			}

			try
			{
				await _contentRepository.UpdateRangeAsync(contents);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "Failed to batch update contents");
				return new ResponseResult { IsSuccess = false, Msg = $"Failed to update contents: {ex.Message}" };
			}

			_logger.LogInformation("Batch updated {Count} contents successfully", dtos.Count);
			return new ResponseResult { IsSuccess = true, Msg = "Contents updated successfully." };
		}

		public async Task<List<Guid>> QueryContentIdsBySourceIdsAsync(List<string> sourceIds)
		{
			if (sourceIds == null || !sourceIds.Any())
			{
				_logger.LogWarning("QueryContentIdsBySourceIdsAsync received null or empty source IDs.");
				return new List<Guid>();
			}

			_logger.LogInformation("Querying content IDs by {Count} source IDs.", sourceIds.Count);

			var allContents = await _contentRepository.GetAllAsync();
			var contentIds = allContents.Where(c => sourceIds.Contains(c.SourceId))
																 .Select(c => c.Id)
																 .ToList();

			_logger.LogInformation("Retrieved {Count} content IDs for source IDs.", contentIds.Count);
			return contentIds;
		}

		public async Task<(int? VersionNo, int? EmbeddingVersionNo)> GetVersionInfoBySourceIdAsync(string sourceId)
		{
			if (string.IsNullOrEmpty(sourceId))
			{
				_logger.LogWarning("GetVersionInfoBySourceIdAsync received null or empty source ID.");
				return (null, null);
			}

			_logger.LogInformation("Querying version info for source ID: {SourceId}", sourceId);


			var contents = await _contentRepository.QueryAsync(c => c.SourceId == sourceId);
			var content = contents.FirstOrDefault();

			if (content == null)
			{
				_logger.LogInformation("No content found for source ID: {SourceId}", sourceId);
				return (null, null);
			}

			return (content.VersionNo, content.EmbeddingVersionNo);
		}

		public async Task<Dictionary<string, (int? VersionNo, int? EmbeddingVersionNo)>> GetVersionInfosBySourceIdsAsync(List<string> sourceIds)
		{
			if (sourceIds == null || !sourceIds.Any())
			{
				_logger.LogWarning("GetVersionInfosBySourceIdsAsync received null or empty source IDs.");
				return new Dictionary<string, (int? VersionNo, int? EmbeddingVersionNo)>();
			}

			_logger.LogInformation("Querying version info for {Count} source IDs.", sourceIds.Count);


			var contents = await _contentRepository.QueryAsync(c => sourceIds.Contains(c.SourceId));
			var contentDict = contents.ToDictionary(c => c.SourceId, c => ((int?)c.VersionNo, (int?)c.EmbeddingVersionNo));


			var result = new Dictionary<string, (int? VersionNo, int? EmbeddingVersionNo)>();
			foreach (var sourceId in sourceIds)
			{
				result[sourceId] = contentDict.ContainsKey(sourceId) ? contentDict[sourceId] : (null, null);
			}

			_logger.LogInformation("Retrieved version info for {Count} source IDs, found {FoundCount} contents.",
				sourceIds.Count, contentDict.Count);
			return result;
		}

  }
}