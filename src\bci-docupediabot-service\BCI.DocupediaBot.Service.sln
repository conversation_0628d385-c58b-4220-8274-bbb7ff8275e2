﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{4F32ED4A-E150-4120-93E9-E8A4EEB85E15}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BCI.DocupediaBot.UIService", "src\BCI.DocupediaBot.UIService\BCI.DocupediaBot.UIService.csproj", "{7AA0CBFE-9EB9-4288-BA74-F94E92499922}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BCI.DocupediaBot.Application", "src\BCI.DocupediaBot.Application\BCI.DocupediaBot.Application.csproj", "{BF968784-D71D-490D-9C30-EDC52317DD15}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BCI.DocupediaBot.Application.Contracts", "src\BCI.DocupediaBot.Application.Contracts\BCI.DocupediaBot.Application.Contracts.csproj", "{DCF4A9CD-FEBE-48DB-8F62-D775062F9572}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BCI.DocupediaBot.Domain", "src\BCI.DocupediaBot.Domain\BCI.DocupediaBot.Domain.csproj", "{111CD5C6-F79C-44EA-8D84-C8D9585F60DD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BCI.DocupediaBot.Persistence.EF", "src\BCI.DocupediaBot.Persistence.EF\BCI.DocupediaBot.Persistence.EF.csproj", "{9DBC8A0D-AA39-4392-BADC-634CF1C50301}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BCI.DocupediaBot.Infrastructure", "src\BCI.DocupediaBot.Infrastructure\BCI.DocupediaBot.Infrastructure.csproj", "{C44CA80F-3368-4365-A0C1-A75CC09DAEF9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{F6DE51E0-DA86-4D49-8220-084FD25C12FD}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7AA0CBFE-9EB9-4288-BA74-F94E92499922}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7AA0CBFE-9EB9-4288-BA74-F94E92499922}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7AA0CBFE-9EB9-4288-BA74-F94E92499922}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7AA0CBFE-9EB9-4288-BA74-F94E92499922}.Release|Any CPU.Build.0 = Release|Any CPU
		{BF968784-D71D-490D-9C30-EDC52317DD15}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BF968784-D71D-490D-9C30-EDC52317DD15}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BF968784-D71D-490D-9C30-EDC52317DD15}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BF968784-D71D-490D-9C30-EDC52317DD15}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCF4A9CD-FEBE-48DB-8F62-D775062F9572}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCF4A9CD-FEBE-48DB-8F62-D775062F9572}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCF4A9CD-FEBE-48DB-8F62-D775062F9572}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCF4A9CD-FEBE-48DB-8F62-D775062F9572}.Release|Any CPU.Build.0 = Release|Any CPU
		{111CD5C6-F79C-44EA-8D84-C8D9585F60DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{111CD5C6-F79C-44EA-8D84-C8D9585F60DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{111CD5C6-F79C-44EA-8D84-C8D9585F60DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{111CD5C6-F79C-44EA-8D84-C8D9585F60DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{9DBC8A0D-AA39-4392-BADC-634CF1C50301}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9DBC8A0D-AA39-4392-BADC-634CF1C50301}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9DBC8A0D-AA39-4392-BADC-634CF1C50301}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9DBC8A0D-AA39-4392-BADC-634CF1C50301}.Release|Any CPU.Build.0 = Release|Any CPU
		{C44CA80F-3368-4365-A0C1-A75CC09DAEF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C44CA80F-3368-4365-A0C1-A75CC09DAEF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C44CA80F-3368-4365-A0C1-A75CC09DAEF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C44CA80F-3368-4365-A0C1-A75CC09DAEF9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7AA0CBFE-9EB9-4288-BA74-F94E92499922} = {4F32ED4A-E150-4120-93E9-E8A4EEB85E15}
		{BF968784-D71D-490D-9C30-EDC52317DD15} = {4F32ED4A-E150-4120-93E9-E8A4EEB85E15}
		{DCF4A9CD-FEBE-48DB-8F62-D775062F9572} = {4F32ED4A-E150-4120-93E9-E8A4EEB85E15}
		{111CD5C6-F79C-44EA-8D84-C8D9585F60DD} = {4F32ED4A-E150-4120-93E9-E8A4EEB85E15}
		{9DBC8A0D-AA39-4392-BADC-634CF1C50301} = {4F32ED4A-E150-4120-93E9-E8A4EEB85E15}
		{C44CA80F-3368-4365-A0C1-A75CC09DAEF9} = {4F32ED4A-E150-4120-93E9-E8A4EEB85E15}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A4576606-8A54-428B-99AB-62A23AE927A2}
	EndGlobalSection
EndGlobal
