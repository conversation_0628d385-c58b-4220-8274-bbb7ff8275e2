﻿using BCI.DocupediaBot.Infrastructure.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Infrastructure.Entities
{
  public class Entity : IEntity, IAuditEntity, ISoftDeleteEntity, ITenantEntity
  {
    public Guid Id { get; set; }
    public DateTime? CreationTime { get; set; } = default!;
    public string Creator { get; set; } = default!;
    public DateTime? ModificationTime { get; set; } = default!;
    public string Modifier { get; set; } = default!;
    public bool IsDeleted { get; set; } = default!;
    public string TenantId { get; set; } = default!;
  }
}
