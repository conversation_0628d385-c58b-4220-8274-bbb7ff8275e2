﻿using BCI.DocupediaBot.Application.Contracts.Dtos.SysUser;
using System;
using System.Collections.Generic;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup
{
  public class SysGroupResponseDTO
  {
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Size { get; set; }
    public List<SysUserResponseDTO> Users { get; set; } = new List<SysUserResponseDTO>();
  }
}