{"items": [{"name": "Default_Role", "displayName": "Default role of module", "description": "is allowed to access everything in module", "permissions": [{"resourceId": "urn.com.bosch.bci.entitytype.apis", "resourceType": "api", "privileges": ["read", "add", "modify", "delete"]}, {"resourceId": "health", "resourceType": "urn:com:bosch:bci:operation", "privileges": ["execute"]}, {"resourceId": "urn.com.bosch.bci.entitytype.moduleviews", "resourceType": "view", "privileges": ["read"]}, {"resourceId": "urn.com.bosch.bci.entitytype.disclosure", "resourceType": "disclosure-document", "privileges": ["read"]}]}]}