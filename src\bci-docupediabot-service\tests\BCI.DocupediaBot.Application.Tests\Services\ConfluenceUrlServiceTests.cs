using BCI.DocupediaBot.Application.Services.ConfluenceUrl;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net.Http;
using Xunit;

namespace BCI.DocupediaBot.Application.Tests.Services
{
    public class ConfluenceUrlServiceTests
    {
        private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
        private readonly Mock<ILogger<ConfluenceUrlService>> _loggerMock;
        private readonly ConfluenceUrlService _service;

        public ConfluenceUrlServiceTests()
        {
            _httpClientFactoryMock = new Mock<IHttpClientFactory>();
            _loggerMock = new Mock<ILogger<ConfluenceUrlService>>();
            _service = new ConfluenceUrlService(_httpClientFactoryMock.Object, _loggerMock.Object);
        }

        [Theory]
        [InlineData("https://inside-docupedia.bosch.com/confluence/display/BCIESWCN/Test+Page", true)]
        [InlineData("https://inside-docupedia.bosch.com/confluence/pages/viewpage.action?pageId=123456", true)]
        [InlineData("https://outside-site.com/confluence/display/SPACE/Page", false)]
        [InlineData("https://inside-docupedia.bosch.com/other/display/SPACE/Page", false)]
        [InlineData("invalid-url", false)]
        public void IsValidConfluenceUrl_ShouldReturnExpectedResult(string url, bool expected)
        {

            var result = _service.IsValidConfluenceUrl(url);


            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("https://inside-docupedia.bosch.com/confluence/display/BCIESWCN/Test+Page", "prettyUrl")]
        [InlineData("https://inside-docupedia.bosch.com/confluence/pages/viewpage.action?pageId=123456", "pageIdUrl")]
        [InlineData("https://inside-docupedia.bosch.com/confluence/other/path", "unknown")]
        [InlineData("", "unknown")]
        public void GetUrlType_ShouldReturnCorrectType(string url, string expectedType)
        {

            var result = _service.GetUrlType(url);


            Assert.Equal(expectedType, result);
        }
    }
}
