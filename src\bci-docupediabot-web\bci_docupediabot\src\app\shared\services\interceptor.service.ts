/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BciLoaderService } from '@bci-web-core/core';
import { Observable, catchError, finalize, throwError } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class LoaderInterceptor implements HttpInterceptor {
  private static readonly EXCEPT_URLS = ['/data-rows/top/', '/token', '/certs', '/chat/ask'];
  constructor(private loader: BciLoaderService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const isWhitelisted = LoaderInterceptor.EXCEPT_URLS.some(keyword => {
      return request.url.includes(keyword);
    });
    if (isWhitelisted) {
      return next.handle(request);
    }
    const loadingBar = this.loader.showProgressBar();

    return next.handle(request).pipe(finalize(() => this.loader.hideProgressBar(loadingBar)));
  }
}

@Injectable({ providedIn: 'root' })
export class ErrorInterceptor implements HttpInterceptor {
  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError(err => {

        console.error('HTTP Error:', err);


        const errorMessage = err?.error?.message ?? err?.message ?? 'An unknown error occurred';
        return throwError(() => new Error(errorMessage));
      })
    );
  }
}
