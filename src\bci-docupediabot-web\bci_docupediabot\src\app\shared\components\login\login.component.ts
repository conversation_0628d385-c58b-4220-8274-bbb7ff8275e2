import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BciSidebarService, LoggerService } from '@bci-web-core/core';
import { Router } from '@angular/router';
import { BehaviorSubject, lastValueFrom } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { PolicyComponent } from './policy/policy.component';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTableDataSource } from '@angular/material/table';
import { LoginModel } from '@shared/models/system.model';
import { AuthService } from '@shared/services/auth.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, On<PERSON><PERSON>roy, AfterViewInit {
  model: LoginModel = {} as LoginModel;
  loading = false;
  errorMsg = '';
  useTenant = true;

  constructor(
    private router: Router,
    private logger: LoggerService,
    private sidebarService: BciSidebarService,
    private translate: TranslateService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private authService: AuthService
  ) {
    this.model.isReadAgree = false;
  }

  ngOnInit() {
    if (this.authService.isLoggedIn()) {
      this.router.navigate(['/chatbot']);
    }
  }

  ngAfterViewInit() {
    this.sidebarService.setSidebarVisibility(false);
  }

  ngOnDestroy() {
    this.sidebarService.setSidebarVisibility(true);
  }

  login() {
    if (!this.model.UserName || !this.model.Password) {
      this.errorMsg = 'Please enter username and password';
      return;
    }

    if (!this.model.isReadAgree) {
      this.errorMsg = 'Please read and agree to the policy';
      return;
    }

    this.loading = true;
    this.errorMsg = '';

    this.authService.login(this.model.UserName, this.model.Password).subscribe({
      next: (loginSuccess) => {
        if (loginSuccess) {
          this.logger.info('Login successful');

          this.snackBar.open('Login successful!', 'Close', {
            duration: 3000,
            horizontalPosition: 'right',
            verticalPosition: 'top',
          });

          this.router.navigate(['/chatbot']);
        } else {
          this.errorMsg = 'Invalid username or password';
          this.logger.warn('Login failed');
        }
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Login error details:', error);

        if (error.status === 401) {
          this.errorMsg = 'Invalid username or password';
        } else if (error.status === 0) {
          this.errorMsg =
            'Cannot connect to server. Please check your connection.';
        } else if (error.message) {
          this.errorMsg = error.message;
        } else {
          this.errorMsg = 'Login failed. Please try again.';
        }

        this.logger.error('Login error:', error);
        this.loading = false;
      },
    });
  }

  openPolicy() {
    this.dialog.open(PolicyComponent, { minWidth: '800px', data: {} });
  }
}
