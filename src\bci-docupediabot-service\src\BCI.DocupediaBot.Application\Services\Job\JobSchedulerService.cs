﻿using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  public class JobSchedulerService
  {
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly ILogger<JobSchedulerService> _logger;

    public JobSchedulerService(ISchedulerFactory schedulerFactory, ILogger<JobSchedulerService> logger)
    {
      _schedulerFactory = schedulerFactory;
      _logger = logger;
    }


    public async Task ScheduleJobAsync(JobConfig jobConfig)
    {
      try
      {
        var scheduler = await _schedulerFactory.GetScheduler();

        Type jobType = Type.GetType(jobConfig.JobType);
        if (jobType == null)
        {
          _logger.LogError($"Job type not found: {jobConfig.JobType}");
          return;
        }

        var jobDetail = JobBuilder.Create(jobType)
            .WithIdentity(jobConfig.JobName, jobConfig.JobGroup)
            .Build();

        foreach (var item in jobConfig.JobData)
        {
          jobDetail.JobDataMap.Put(item.Key, item.Value);
        }

        ITrigger trigger = CreateTrigger(jobConfig);

        if (await scheduler.CheckExists(jobDetail.Key))
        {
          await scheduler.DeleteJob(jobDetail.Key);
          _logger.LogInformation($"Deleted existing job: {jobConfig.JobName}");
        }

        await scheduler.ScheduleJob(jobDetail, trigger);
        _logger.LogInformation($"Successfully scheduled job: {jobConfig.JobName}, type: {jobConfig.Schedule.ScheduleType}");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, $"Error scheduling job: {jobConfig.JobName}");
        throw;
      }
    }


    private ITrigger CreateTrigger(JobConfig jobConfig)
    {
      var triggerBuilder = TriggerBuilder.Create()
          .WithIdentity($"{jobConfig.JobName}.Trigger", jobConfig.JobGroup);

      switch (jobConfig.Schedule.ScheduleType)
      {
        case ScheduleType.Daily:
          return triggerBuilder
              .WithSchedule(CronScheduleBuilder.DailyAtHourAndMinute(
                  jobConfig.Schedule.Hour ?? 0,
                  jobConfig.Schedule.Minute ?? 0))
              .Build();

        case ScheduleType.Weekly:
          return triggerBuilder
              .WithSchedule(CronScheduleBuilder.WeeklyOnDayAndHourAndMinute(
                  jobConfig.Schedule.DayOfWeek ?? System.DayOfWeek.Monday,
                  jobConfig.Schedule.Hour ?? 0,
                  jobConfig.Schedule.Minute ?? 0))
              .Build();

        case ScheduleType.Interval:
          return triggerBuilder
              .WithSimpleSchedule(x => x
                  .WithIntervalInMinutes(jobConfig.Schedule.IntervalInMinutes ?? 60)
                  .RepeatForever())
              .StartNow()
              .Build();

        default:
          throw new ArgumentException($"Unsupported schedule type: {jobConfig.Schedule.ScheduleType}");
      }
    }
  }
}