import { Component, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-policy',
  templateUrl: './policy.component.html',
  styleUrls: ['./policy.component.scss']
})
export class PolicyComponent implements OnInit {

  public form: FormGroup;
  public isch: boolean;
  public isen: boolean;

  constructor(public dialogRef: MatDialogRef<PolicyComponent>,
    private translate: TranslateService
    ) {
    if(window.localStorage.getItem('language') == 'ch')
    {
      this.isch = true;
      this.isen = false;
    }
    else{
      this.isch = false;
      this.isen = true;
    }
  }

  ngOnInit() {
  }

}
