{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-csharp.js"], "sourcesContent": ["(function (Prism) {\n  /**\n   * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n   *\n   * Note: This is a simple text based replacement. Be careful when using backreferences!\n   *\n   * @param {string} pattern the given pattern.\n   * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n   * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n   * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n   */\n  function replace(pattern, replacements) {\n    return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n      return '(?:' + replacements[+index] + ')';\n    });\n  }\n  /**\n   * @param {string} pattern\n   * @param {string[]} replacements\n   * @param {string} [flags]\n   * @returns {RegExp}\n   */\n  function re(pattern, replacements, flags) {\n    return RegExp(replace(pattern, replacements), flags || '');\n  }\n\n  /**\n   * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n   *\n   * @param {string} pattern\n   * @param {number} depthLog2\n   * @returns {string}\n   */\n  function nested(pattern, depthLog2) {\n    for (var i = 0; i < depthLog2; i++) {\n      pattern = pattern.replace(/<<self>>/g, function () {\n        return '(?:' + pattern + ')';\n      });\n    }\n    return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]');\n  }\n\n  // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n  var keywordKinds = {\n    // keywords which represent a return or variable type\n    type: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n    // keywords which are used to declare a type\n    typeDeclaration: 'class enum interface record struct',\n    // contextual keywords\n    // (\"var\" and \"dynamic\" are missing because they are used like types)\n    contextual: 'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n    // all other keywords\n    other: 'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n  };\n\n  // keywords\n  function keywordsToPattern(words) {\n    return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b';\n  }\n  var typeDeclarationKeywords = keywordsToPattern(keywordKinds.typeDeclaration);\n  var keywords = RegExp(keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other));\n  var nonTypeKeywords = keywordsToPattern(keywordKinds.typeDeclaration + ' ' + keywordKinds.contextual + ' ' + keywordKinds.other);\n  var nonContextualKeywords = keywordsToPattern(keywordKinds.type + ' ' + keywordKinds.typeDeclaration + ' ' + keywordKinds.other);\n\n  // types\n  var generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2); // the idea behind the other forbidden characters is to prevent false positives. Same for tupleElement.\n  var nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2);\n  var name = /@?\\b[A-Za-z_]\\w*\\b/.source;\n  var genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [name, generic]);\n  var identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [nonTypeKeywords, genericName]);\n  var array = /\\[\\s*(?:,\\s*)*\\]/.source;\n  var typeExpressionWithoutTuple = replace(/<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source, [identifier, array]);\n  var tupleElement = replace(/[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source, [generic, nestedRound, array]);\n  var tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement]);\n  var typeExpression = replace(/(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source, [tuple, identifier, array]);\n  var typeInside = {\n    'keyword': keywords,\n    'punctuation': /[<>()?,.:[\\]]/\n  };\n\n  // strings & characters\n  // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#character-literals\n  // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#string-literals\n  var character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source; // simplified pattern\n  var regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source;\n  var verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source;\n  Prism.languages.csharp = Prism.languages.extend('clike', {\n    'string': [{\n      pattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n      lookbehind: true,\n      greedy: true\n    }, {\n      pattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n      lookbehind: true,\n      greedy: true\n    }],\n    'class-name': [{\n      // Using static\n      // using static System.Math;\n      pattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [identifier]),\n      lookbehind: true,\n      inside: typeInside\n    }, {\n      // Using alias (type)\n      // using Project = PC.MyCompany.Project;\n      pattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [name, typeExpression]),\n      lookbehind: true,\n      inside: typeInside\n    }, {\n      // Using alias (alias)\n      // using Project = PC.MyCompany.Project;\n      pattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n      lookbehind: true\n    }, {\n      // Type declarations\n      // class Foo<A, B>\n      // interface Foo<out A, B>\n      pattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [typeDeclarationKeywords, genericName]),\n      lookbehind: true,\n      inside: typeInside\n    }, {\n      // Single catch exception declaration\n      // catch(Foo)\n      // (things like catch(Foo e) is covered by variable declaration)\n      pattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n      lookbehind: true,\n      inside: typeInside\n    }, {\n      // Name of the type parameter of generic constraints\n      // where Foo : class\n      pattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n      lookbehind: true\n    }, {\n      // Casts and checks via as and is.\n      // as Foo<A>, is Bar<B>\n      // (things like if(a is Foo b) is covered by variable declaration)\n      pattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [typeExpressionWithoutTuple]),\n      lookbehind: true,\n      inside: typeInside\n    }, {\n      // Variable, field and parameter declaration\n      // (Foo bar, Bar baz, Foo[,,] bay, Foo<Bar, FooBar<Bar>> bax)\n      pattern: re(/\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/.source, [typeExpression, nonContextualKeywords, name]),\n      inside: typeInside\n    }],\n    'keyword': keywords,\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#literals\n    'number': /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n    'operator': />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n    'punctuation': /\\?\\.?|::|[{}[\\];(),.:]/\n  });\n  Prism.languages.insertBefore('csharp', 'number', {\n    'range': {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    }\n  });\n  Prism.languages.insertBefore('csharp', 'punctuation', {\n    'named-parameter': {\n      pattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n      lookbehind: true,\n      alias: 'punctuation'\n    }\n  });\n  Prism.languages.insertBefore('csharp', 'class-name', {\n    'namespace': {\n      // namespace Foo.Bar {}\n      // using Foo.Bar;\n      pattern: re(/(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source, [name]),\n      lookbehind: true,\n      inside: {\n        'punctuation': /\\./\n      }\n    },\n    'type-expression': {\n      // default(Foo), typeof(Foo<Bar>), sizeof(int)\n      pattern: re(/(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/.source, [nestedRound]),\n      lookbehind: true,\n      alias: 'class-name',\n      inside: typeInside\n    },\n    'return-type': {\n      // Foo<Bar> ForBar(); Foo IFoo.Bar() => 0\n      // int this[int index] => 0; T IReadOnlyList<T>.this[int index] => this[index];\n      // int Foo => 0; int Foo { get; set } = 0;\n      pattern: re(/<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source, [typeExpression, identifier]),\n      inside: typeInside,\n      alias: 'class-name'\n    },\n    'constructor-invocation': {\n      // new List<Foo<Bar[]>> { }\n      pattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n      lookbehind: true,\n      inside: typeInside,\n      alias: 'class-name'\n    },\n    /*'explicit-implementation': {\n    \t// int IFoo<Foo>.Bar => 0; void IFoo<Foo<Foo>>.Foo<T>();\n    \tpattern: replace(/\\b<<0>>(?=\\.<<1>>)/, className, methodOrPropertyDeclaration),\n    \tinside: classNameInside,\n    \talias: 'class-name'\n    },*/\n    'generic-method': {\n      // foo<Bar>()\n      pattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [name, generic]),\n      inside: {\n        'function': re(/^<<0>>/.source, [name]),\n        'generic': {\n          pattern: RegExp(generic),\n          alias: 'class-name',\n          inside: typeInside\n        }\n      }\n    },\n    'type-list': {\n      // The list of types inherited or of generic constraints\n      // class Foo<F> : Bar, IList<FooBar>\n      // where F : Bar, IList<int>\n      pattern: re(/\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/.source, [typeDeclarationKeywords, genericName, name, typeExpression, keywords.source, nestedRound, /\\bnew\\s*\\(\\s*\\)/.source]),\n      lookbehind: true,\n      inside: {\n        'record-arguments': {\n          pattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [genericName, nestedRound]),\n          lookbehind: true,\n          greedy: true,\n          inside: Prism.languages.csharp\n        },\n        'keyword': keywords,\n        'class-name': {\n          pattern: RegExp(typeExpression),\n          greedy: true,\n          inside: typeInside\n        },\n        'punctuation': /[,()]/\n      }\n    },\n    'preprocessor': {\n      pattern: /(^[\\t ]*)#.*/m,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        // highlight preprocessor directives as keywords\n        'directive': {\n          pattern: /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n          lookbehind: true,\n          alias: 'keyword'\n        }\n      }\n    }\n  });\n\n  // attributes\n  var regularStringOrCharacter = regularString + '|' + character;\n  var regularStringCharacterOrComment = replace(/\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source, [regularStringOrCharacter]);\n  var roundExpression = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n\n  // https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/attributes/#attribute-targets\n  var attrTarget = /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/.source;\n  var attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [identifier, roundExpression]);\n  Prism.languages.insertBefore('csharp', 'class-name', {\n    'attribute': {\n      // Attributes\n      // [Foo], [Foo(1), Bar(2, Prop = \"foo\")], [return: Foo(1), Bar(2)], [assembly: Foo(Bar)]\n      pattern: re(/((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/.source, [attrTarget, attr]),\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'target': {\n          pattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n          alias: 'keyword'\n        },\n        'attribute-arguments': {\n          pattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n          inside: Prism.languages.csharp\n        },\n        'class-name': {\n          pattern: RegExp(identifier),\n          inside: {\n            'punctuation': /\\./\n          }\n        },\n        'punctuation': /[:,]/\n      }\n    }\n  });\n\n  // string interpolation\n  var formatString = /:[^}\\r\\n]+/.source;\n  // multi line\n  var mInterpolationRound = nested(replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [regularStringCharacterOrComment]), 2);\n  var mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [mInterpolationRound, formatString]);\n  // single line\n  var sInterpolationRound = nested(replace(/[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/.source, [regularStringOrCharacter]), 2);\n  var sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [sInterpolationRound, formatString]);\n  function createInterpolationInside(interpolation, interpolationRound) {\n    return {\n      'interpolation': {\n        pattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n        lookbehind: true,\n        inside: {\n          'format-string': {\n            pattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [interpolationRound, formatString]),\n            lookbehind: true,\n            inside: {\n              'punctuation': /^:/\n            }\n          },\n          'punctuation': /^\\{|\\}$/,\n          'expression': {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-csharp',\n            inside: Prism.languages.csharp\n          }\n        }\n      },\n      'string': /[\\s\\S]+/\n    };\n  }\n  Prism.languages.insertBefore('csharp', 'string', {\n    'interpolation-string': [{\n      pattern: re(/(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source, [mInterpolation]),\n      lookbehind: true,\n      greedy: true,\n      inside: createInterpolationInside(mInterpolation, mInterpolationRound)\n    }, {\n      pattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [sInterpolation]),\n      lookbehind: true,\n      greedy: true,\n      inside: createInterpolationInside(sInterpolation, sInterpolationRound)\n    }],\n    'char': {\n      pattern: RegExp(character),\n      greedy: true\n    }\n  });\n  Prism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp;\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAWhB,WAAS,QAAQ,SAAS,cAAc;AACtC,WAAO,QAAQ,QAAQ,cAAc,SAAU,GAAG,OAAO;AACvD,aAAO,QAAQ,aAAa,CAAC,KAAK,IAAI;AAAA,IACxC,CAAC;AAAA,EACH;AAOA,WAAS,GAAG,SAAS,cAAc,OAAO;AACxC,WAAO,OAAO,QAAQ,SAAS,YAAY,GAAG,SAAS,EAAE;AAAA,EAC3D;AASA,WAAS,OAAO,SAAS,WAAW;AAClC,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,gBAAU,QAAQ,QAAQ,aAAa,WAAY;AACjD,eAAO,QAAQ,UAAU;AAAA,MAC3B,CAAC;AAAA,IACH;AACA,WAAO,QAAQ,QAAQ,aAAa,WAAW;AAAA,EACjD;AAGA,MAAI,eAAe;AAAA;AAAA,IAEjB,MAAM;AAAA;AAAA,IAEN,iBAAiB;AAAA;AAAA;AAAA,IAGjB,YAAY;AAAA;AAAA,IAEZ,OAAO;AAAA,EACT;AAGA,WAAS,kBAAkB,OAAO;AAChC,WAAO,WAAW,MAAM,KAAK,EAAE,QAAQ,MAAM,GAAG,IAAI;AAAA,EACtD;AACA,MAAI,0BAA0B,kBAAkB,aAAa,eAAe;AAC5E,MAAI,WAAW,OAAO,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa,KAAK,CAAC;AAC1J,MAAI,kBAAkB,kBAAkB,aAAa,kBAAkB,MAAM,aAAa,aAAa,MAAM,aAAa,KAAK;AAC/H,MAAI,wBAAwB,kBAAkB,aAAa,OAAO,MAAM,aAAa,kBAAkB,MAAM,aAAa,KAAK;AAG/H,MAAI,UAAU,OAAO,mCAAmC,QAAQ,CAAC;AACjE,MAAI,cAAc,OAAO,0BAA0B,QAAQ,CAAC;AAC5D,MAAI,OAAO,qBAAqB;AAChC,MAAI,cAAc,QAAQ,qBAAqB,QAAQ,CAAC,MAAM,OAAO,CAAC;AACtE,MAAI,aAAa,QAAQ,mCAAmC,QAAQ,CAAC,iBAAiB,WAAW,CAAC;AAClG,MAAI,QAAQ,mBAAmB;AAC/B,MAAI,6BAA6B,QAAQ,yCAAyC,QAAQ,CAAC,YAAY,KAAK,CAAC;AAC7G,MAAI,eAAe,QAAQ,2CAA2C,QAAQ,CAAC,SAAS,aAAa,KAAK,CAAC;AAC3G,MAAI,QAAQ,QAAQ,yBAAyB,QAAQ,CAAC,YAAY,CAAC;AACnE,MAAI,iBAAiB,QAAQ,mDAAmD,QAAQ,CAAC,OAAO,YAAY,KAAK,CAAC;AAClH,MAAI,aAAa;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,EACjB;AAKA,MAAI,YAAY,8CAA8C;AAC9D,MAAI,gBAAgB,wBAAwB;AAC5C,MAAI,iBAAiB,kCAAkC;AACvD,EAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,SAAS;AAAA,IACvD,UAAU,CAAC;AAAA,MACT,SAAS,GAAG,kBAAkB,QAAQ,CAAC,cAAc,CAAC;AAAA,MACtD,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,GAAG;AAAA,MACD,SAAS,GAAG,mBAAmB,QAAQ,CAAC,aAAa,CAAC;AAAA,MACtD,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,CAAC;AAAA,IACD,cAAc,CAAC;AAAA;AAAA;AAAA,MAGb,SAAS,GAAG,qCAAqC,QAAQ,CAAC,UAAU,CAAC;AAAA,MACrE,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,GAAG;AAAA;AAAA;AAAA,MAGD,SAAS,GAAG,wCAAwC,QAAQ,CAAC,MAAM,cAAc,CAAC;AAAA,MAClF,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,GAAG;AAAA;AAAA;AAAA,MAGD,SAAS,GAAG,4BAA4B,QAAQ,CAAC,IAAI,CAAC;AAAA,MACtD,YAAY;AAAA,IACd,GAAG;AAAA;AAAA;AAAA;AAAA,MAID,SAAS,GAAG,oBAAoB,QAAQ,CAAC,yBAAyB,WAAW,CAAC;AAAA,MAC9E,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,GAAG;AAAA;AAAA;AAAA;AAAA,MAID,SAAS,GAAG,yBAAyB,QAAQ,CAAC,UAAU,CAAC;AAAA,MACzD,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,GAAG;AAAA;AAAA;AAAA,MAGD,SAAS,GAAG,oBAAoB,QAAQ,CAAC,IAAI,CAAC;AAAA,MAC9C,YAAY;AAAA,IACd,GAAG;AAAA;AAAA;AAAA;AAAA,MAID,SAAS,GAAG,mCAAmC,QAAQ,CAAC,0BAA0B,CAAC;AAAA,MACnF,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,GAAG;AAAA;AAAA;AAAA,MAGD,SAAS,GAAG,2EAA2E,QAAQ,CAAC,gBAAgB,uBAAuB,IAAI,CAAC;AAAA,MAC5I,QAAQ;AAAA,IACV,CAAC;AAAA,IACD,WAAW;AAAA;AAAA,IAEX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,eAAe;AAAA,EACjB,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,IAC/C,SAAS;AAAA,MACP,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,UAAU,eAAe;AAAA,IACpD,mBAAmB;AAAA,MACjB,SAAS,GAAG,yBAAyB,QAAQ,CAAC,IAAI,CAAC;AAAA,MACnD,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,IACnD,aAAa;AAAA;AAAA;AAAA,MAGX,SAAS,GAAG,+DAA+D,QAAQ,CAAC,IAAI,CAAC;AAAA,MACzF,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA;AAAA,MAEjB,SAAS,GAAG,kFAAkF,QAAQ,CAAC,WAAW,CAAC;AAAA,MACnH,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,eAAe;AAAA;AAAA;AAAA;AAAA,MAIb,SAAS,GAAG,+DAA+D,QAAQ,CAAC,gBAAgB,UAAU,CAAC;AAAA,MAC/G,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,0BAA0B;AAAA;AAAA,MAExB,SAAS,GAAG,8BAA8B,QAAQ,CAAC,cAAc,CAAC;AAAA,MAClE,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,kBAAkB;AAAA;AAAA,MAEhB,SAAS,GAAG,yBAAyB,QAAQ,CAAC,MAAM,OAAO,CAAC;AAAA,MAC5D,QAAQ;AAAA,QACN,YAAY,GAAG,SAAS,QAAQ,CAAC,IAAI,CAAC;AAAA,QACtC,WAAW;AAAA,UACT,SAAS,OAAO,OAAO;AAAA,UACvB,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,IACA,aAAa;AAAA;AAAA;AAAA;AAAA,MAIX,SAAS,GAAG,kKAAkK,QAAQ,CAAC,yBAAyB,aAAa,MAAM,gBAAgB,SAAS,QAAQ,aAAa,kBAAkB,MAAM,CAAC;AAAA,MAC1S,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,oBAAoB;AAAA,UAClB,SAAS,GAAG,+BAA+B,QAAQ,CAAC,aAAa,WAAW,CAAC;AAAA,UAC7E,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQA,OAAM,UAAU;AAAA,QAC1B;AAAA,QACA,WAAW;AAAA,QACX,cAAc;AAAA,UACZ,SAAS,OAAO,cAAc;AAAA,UAC9B,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA;AAAA,QAEN,aAAa;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,MAAI,2BAA2B,gBAAgB,MAAM;AACrD,MAAI,kCAAkC,QAAQ,iEAAiE,QAAQ,CAAC,wBAAwB,CAAC;AACjJ,MAAI,kBAAkB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,+BAA+B,CAAC,GAAG,CAAC;AAGjH,MAAI,aAAa,wEAAwE;AACzF,MAAI,OAAO,QAAQ,0BAA0B,QAAQ,CAAC,YAAY,eAAe,CAAC;AAClF,EAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,IACnD,aAAa;AAAA;AAAA;AAAA,MAGX,SAAS,GAAG,6EAA6E,QAAQ,CAAC,YAAY,IAAI,CAAC;AAAA,MACnH,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,UAAU;AAAA,UACR,SAAS,GAAG,iBAAiB,QAAQ,CAAC,UAAU,CAAC;AAAA,UACjD,OAAO;AAAA,QACT;AAAA,QACA,uBAAuB;AAAA,UACrB,SAAS,GAAG,aAAa,QAAQ,CAAC,eAAe,CAAC;AAAA,UAClD,QAAQA,OAAM,UAAU;AAAA,QAC1B;AAAA,QACA,cAAc;AAAA,UACZ,SAAS,OAAO,UAAU;AAAA,UAC1B,QAAQ;AAAA,YACN,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF,CAAC;AAGD,MAAI,eAAe,aAAa;AAEhC,MAAI,sBAAsB,OAAO,QAAQ,+BAA+B,QAAQ,CAAC,+BAA+B,CAAC,GAAG,CAAC;AACrH,MAAI,iBAAiB,QAAQ,qCAAqC,QAAQ,CAAC,qBAAqB,YAAY,CAAC;AAE7G,MAAI,sBAAsB,OAAO,QAAQ,mEAAmE,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC;AAClJ,MAAI,iBAAiB,QAAQ,qCAAqC,QAAQ,CAAC,qBAAqB,YAAY,CAAC;AAC7G,WAAS,0BAA0B,eAAe,oBAAoB;AACpE,WAAO;AAAA,MACL,iBAAiB;AAAA,QACf,SAAS,GAAG,6BAA6B,QAAQ,CAAC,aAAa,CAAC;AAAA,QAChE,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,iBAAiB;AAAA,YACf,SAAS,GAAG,sCAAsC,QAAQ,CAAC,oBAAoB,YAAY,CAAC;AAAA,YAC5F,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,eAAe;AAAA,YACjB;AAAA,UACF;AAAA,UACA,eAAe;AAAA,UACf,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF;AACA,EAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,IAC/C,wBAAwB,CAAC;AAAA,MACvB,SAAS,GAAG,4DAA4D,QAAQ,CAAC,cAAc,CAAC;AAAA,MAChG,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,IACvE,GAAG;AAAA,MACD,SAAS,GAAG,4CAA4C,QAAQ,CAAC,cAAc,CAAC;AAAA,MAChF,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,IACvE,CAAC;AAAA,IACD,QAAQ;AAAA,MACN,SAAS,OAAO,SAAS;AAAA,MACzB,QAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAChE,GAAG,KAAK;", "names": ["Prism"]}