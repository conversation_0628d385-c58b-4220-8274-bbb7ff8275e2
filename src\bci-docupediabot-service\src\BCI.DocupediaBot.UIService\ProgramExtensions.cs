﻿using BCI.DocupediaBot.Application.Services.Auth;
using BCI.DocupediaBot.Application.Services.Chat;
using BCI.DocupediaBot.Application.Services.Cache;
using BCI.DocupediaBot.Application.Services.Collection;
using BCI.DocupediaBot.Application.Services.CollectPageContent;
using BCI.DocupediaBot.Application.Services.CollectionsInGroup;
using BCI.DocupediaBot.Application.Services.ConfluenceUrl;
using BCI.DocupediaBot.Application.Services.Content;
using BCI.DocupediaBot.Application.Services.History;
using BCI.DocupediaBot.Application.Services.LLM;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Application.Services.QualityGate;
using BCI.DocupediaBot.Application.Services.SysGroup;
using BCI.DocupediaBot.Application.Services.SysUser;
using BCI.DocupediaBot.Application.Services.SysUsersInGroup;
using BCI.DocupediaBot.Application.Services.VectorDb;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using BCI.DocupediaBot.Infrastructure.Domain;
using BCI.DocupediaBot.Infrastructure.IRepositories;
using BCI.DocupediaBot.Infrastructure.Repositories;
using BCI.DocupediaBot.Persistence.EF.Repositories;
using Bosch.Foundation.ErrorHandling;
using Bosch.Foundation.ServiceClient;
using Bosch.Foundation.Tenant;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using System.Text.Json;

namespace BCI.DocupediaBot.UIService
{
  [ExcludeFromCodeCoverage]
  public static class ProgramExtensions
  {
    public static IServiceCollection AddCorsOptions(this IServiceCollection services, string policy)
    {
      services.AddCors(
        options => options.AddPolicy(
          policy,
          corsBuilder => corsBuilder
            .AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod()
        )
      );

      return services;
    }

    public static IMvcBuilder AddMVC(this IServiceCollection services)
    {
      return services.AddMvc(options =>
        {
          options.EnableEndpointRouting = true;
        })
        .AddJsonOptions(options =>
        {
          options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
          options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
          options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        });
    }

    public static IServiceCollection AddApiVersions(this IServiceCollection services)
    {
      services.AddApiVersioning(o =>
      {
        o.ApiVersionReader = new UrlSegmentApiVersionReader();
        o.DefaultApiVersion = new ApiVersion(1, 0);
        o.AssumeDefaultVersionWhenUnspecified = true;
        o.ReportApiVersions = true;
        o.RegisterMiddleware = true;
        o.UseApiBehavior = true;
      });

      services.AddVersionedApiExplorer(o =>
      {
        o.SubstituteApiVersionInUrl = true;
        o.GroupNameFormat = @"'v'VVV";
      });

      services.AddSingleton<IApiVersionDescriptionProvider, ApiVersionDescriptionProvider>();

      return services;
    }

    public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration config)
    {
      var databaseProvider = config["DatabaseProvider"];









      if (databaseProvider?.ToLowerInvariant() == "pgsql")
      {
        services.AddDbContext<DocupediaBotDbContext>(opt => opt.UseNpgsql(config.GetConnectionString(databaseProvider)));
      }

      return services;
    }

    public static IServiceCollection AddAppServices(this IServiceCollection services)
    {
      services.AddScoped<ICollectPageContentService, CollectPageContentService>();
      services.AddScoped<IVectorDbService, VectorDbService>();
			services.AddScoped<ILLMService, LLMService>();
			services.AddScoped<IChatService, ChatService>();
			services.AddScoped<IAuthService, AuthService>();
			services.AddScoped<IQualityGateService, QualityGateService>();


      return services;
    }

    public static IServiceCollection AddDomainServices(this IServiceCollection services)
    {
      services.AddScoped(typeof(IDomainService<>), typeof(DomainService<>));
      services.AddScoped(typeof(IContentService), typeof(ContentService));
      services.AddScoped(typeof(IPageService), typeof(PageService));
      services.AddScoped(typeof(ICollectionService), typeof(CollectionService));
      services.AddScoped(typeof(IPagesInCollectionService), typeof(PagesInCollectionService));
      services.AddScoped(typeof(IContentsInPageService), typeof(ContentsInPageService));
      services.AddScoped(typeof(IHistoryService), typeof(HistoryService));
      services.AddScoped(typeof(ISysUserService), typeof(SysUserService));
      services.AddScoped(typeof(ISysGroupService), typeof(SysGroupService));
      services.AddScoped(typeof(ISysUsersInGroupService), typeof(SysUsersInGroupService));
      services.AddScoped(typeof(ICollectionsInGroupService), typeof(CollectionsInGroupService));
      services.AddScoped(typeof(IConfluenceUrlService), typeof(ConfluenceUrlService));


      services.AddMemoryCache();
      services.AddScoped(typeof(IUserGroupCacheService), typeof(UserGroupCacheService));

      return services;
    }

    public static IServiceCollection AddEFRepositories(this IServiceCollection services)
    {
      services.AddScoped(typeof(IEntityRepository<>), typeof(EntityRepository<>));
      services.AddScoped(typeof(IContentRepository), typeof(ContentRepository));
      services.AddScoped(typeof(IPageRepository), typeof(PageRepository));
      services.AddScoped(typeof(ICollectionRepository), typeof(CollectionRepository));
      services.AddScoped(typeof(IPagesInCollectionRepository), typeof(PagesInCollectionRepository));
      services.AddScoped(typeof(IContentsInPageRepository), typeof(ContentsInPageRepository));
      services.AddScoped(typeof(IChatHistoryRepository), typeof(ChatHistoryRepository));
      services.AddScoped(typeof(ISysUserRepository), typeof(SysUserRepository));
      services.AddScoped(typeof(ISysGroupRepository), typeof(SysGroupRepository));
      services.AddScoped(typeof(ISysUsersInGroupRepository), typeof(SysUsersInGroupRepository));
      services.AddScoped(typeof(ICollectionsInGroupRepository), typeof(CollectionsInGroupRepository));

      return services;
    }

    public static IServiceCollection AddSwaggerGenWithAuthorization(this IServiceCollection services)
    {
      services.AddSwaggerGen(option =>
      {
        option.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
          In = ParameterLocation.Header,
          Description = "Please enter a access token",
          Name = "Authorization",
          Type = SecuritySchemeType.Http,
          BearerFormat = "JWT",
          Scheme = "Bearer"
        });
        option.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
          {
            new OpenApiSecurityScheme
            {
              Reference = new OpenApiReference
              {
                Type=ReferenceType.SecurityScheme,
                Id="Bearer"
              }
            },
            new string[]{}
          }
        });
      });

      return services;
    }

    public static IApplicationBuilder UseErrorHandler(this IApplicationBuilder app)
    {
      app.UseMiddleware<ErrorResponseMiddleware>();
      app.UseDefaultExceptionHandlers();
      app.UseHttpCommunicationExceptionHandler();

      return app;
    }

    public static IApplicationBuilder UseTenantMiddlewareWithDefaultIgnoredUrls(this IApplicationBuilder builder)
    {
      var urlsToIgnore = new List<string>
      {
        "/api/v1/OidcConfiguration",
      };

      return builder.UseTenantMiddleware(options =>
      {
        options.IgnoreRequestsStartingWith.AddRange(urlsToIgnore);
      });
    }

    public static IApplicationBuilder UseDatabaseMigration(this IApplicationBuilder builder)
    {
      using var scope = builder.ApplicationServices.GetService<IServiceScopeFactory>().CreateScope();
      using var dbContext = scope.ServiceProvider.GetRequiredService<DbContext>();

      var logfactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
      var logger = logfactory.CreateLogger<DbContext>();

      logger.LogInformation("Application start to migrate");
      dbContext.Database.Migrate();
      logger.LogInformation("Application finish the migration");

      return builder;
    }

    public static void UseTheSwagger(this IApplicationBuilder builder)
    {
      using var scope = builder.ApplicationServices.GetService<IServiceScopeFactory>().CreateScope();
      var configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();
      var swaggerEnabled = configuration["SwaggerUI:Enabled"];
      if (swaggerEnabled.EndsWith("true", StringComparison.OrdinalIgnoreCase))
      {
        builder.UseSwagger();
        builder.UseSwaggerUI();
      }
    }

    public static bool IfWithoutNIAS(this IConfiguration config)
    {
      return config.GetValue<bool>("WithoutNIAS");
    }
  }
}
