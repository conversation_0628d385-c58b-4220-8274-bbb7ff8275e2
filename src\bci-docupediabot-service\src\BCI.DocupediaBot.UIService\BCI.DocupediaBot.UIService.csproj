﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bosch.Foundation.ErrorHandling" Version="2402.6.11" />
    <PackageReference Include="Bosch.Foundation.Health" Version="2402.9.5" />
    <PackageReference Include="Bosch.Foundation.OpenId.Authentication.Client" Version="2402.8.13" />
    <PackageReference Include="Bosch.Foundation.OpenId.Macma" Version="2402.5.6" />
    <PackageReference Include="Bosch.Foundation.SCS.MacmaClient" Version="2402.6.0" />
    <PackageReference Include="Bosch.Foundation.SCS.PortalRegistration" Version="2402.7.7" />
    <PackageReference Include="Bosch.Foundation.Security.Api" Version="2402.6.0" />
    <PackageReference Include="Bosch.Foundation.ServiceClient" Version="2402.6.3" />
    <PackageReference Include="Bosch.Foundation.Tenant" Version="2402.6.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.13" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
    <PackageReference Include="System.DirectoryServices" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BCI.DocupediaBot.Application.Contracts\BCI.DocupediaBot.Application.Contracts.csproj" />
    <ProjectReference Include="..\BCI.DocupediaBot.Application\BCI.DocupediaBot.Application.csproj" />
    <ProjectReference Include="..\BCI.DocupediaBot.Infrastructure\BCI.DocupediaBot.Infrastructure.csproj" />
    <ProjectReference Include="..\BCI.DocupediaBot.Persistence.EF\BCI.DocupediaBot.Persistence.EF.csproj" />
  </ItemGroup>

</Project>
