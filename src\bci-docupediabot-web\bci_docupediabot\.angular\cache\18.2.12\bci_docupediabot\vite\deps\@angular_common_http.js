import {
  Fetch<PERSON>ack<PERSON>,
  HTTP_INTERCEPTORS,
  HTTP_ROOT_INTERCEPTOR_FNS,
  HTTP_TRANSFER_CACHE_ORIGIN_MAP,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpInterceptorHandler,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  REQUESTS_CONTRIBUTE_TO_STABILITY,
  provideHttpClient,
  with<PERSON><PERSON><PERSON>,
  withHttpTransferCache,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration
} from "./chunk-6K2TSSVA.js";
import "./chunk-5X2BSBAM.js";
import "./chunk-X3P73C3G.js";
import "./chunk-DWV65MH7.js";
import "./chunk-KXOMTDJ6.js";
import "./chunk-KRK546R7.js";
import "./chunk-Y4T55RDF.js";
export {
  FetchBackend,
  HTTP_INTERCEPTORS,
  HTTP_TRANSFER_CACHE_ORIGIN_MAP,
  HttpBackend,
  HttpClient,
  HttpClientJsonpModule,
  HttpClientModule,
  HttpClientXsrfModule,
  HttpContext,
  HttpContextToken,
  HttpErrorResponse,
  HttpEventType,
  HttpFeatureKind,
  HttpHandler,
  HttpHeaderResponse,
  HttpHeaders,
  HttpParams,
  HttpRequest,
  HttpResponse,
  HttpResponseBase,
  HttpStatusCode,
  HttpUrlEncodingCodec,
  HttpXhrBackend,
  HttpXsrfTokenExtractor,
  JsonpClientBackend,
  JsonpInterceptor,
  provideHttpClient,
  withFetch,
  withInterceptors,
  withInterceptorsFromDi,
  withJsonpSupport,
  withNoXsrfProtection,
  withRequestsMadeViaParent,
  withXsrfConfiguration,
  HTTP_ROOT_INTERCEPTOR_FNS as ɵHTTP_ROOT_INTERCEPTOR_FNS,
  HttpInterceptorHandler as ɵHttpInterceptingHandler,
  HttpInterceptorHandler as ɵHttpInterceptorHandler,
  REQUESTS_CONTRIBUTE_TO_STABILITY as ɵREQUESTS_CONTRIBUTE_TO_STABILITY,
  withHttpTransferCache as ɵwithHttpTransferCache
};
//# sourceMappingURL=@angular_common_http.js.map
