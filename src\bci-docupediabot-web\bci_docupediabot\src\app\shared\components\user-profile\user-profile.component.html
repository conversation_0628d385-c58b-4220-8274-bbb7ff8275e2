<bci-modal-component
  title="User Profile"
  [closeIcon]="true"
  (closeHandler)="close()">
  <p class="modal-subheading">Update your profile information below.</p>
  <form [formGroup]="userProfileForm" class="modal-form">
    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>NT Account</mat-label>
        <input matInput formControlName="userNTAccount" readonly />
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>User Name</mat-label>
        <input matInput formControlName="userName" readonly />
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Given Name</mat-label>
        <input matInput formControlName="givenName" readonly />
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Surname</mat-label>
        <input matInput formControlName="sn" readonly />
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Email</mat-label>
        <input matInput formControlName="mail" readonly />
      </mat-form-field>
      <mat-form-field appearance="fill" class="half-width">
        <mat-label>Department</mat-label>
        <input matInput formControlName="department" readonly />
      </mat-form-field>
    </div>

    <div class="form-row">
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Favorite Collection</mat-label>
        <mat-select formControlName="favCollecitonId">
          <mat-option *ngFor="let collection of collections" [value]="collection.id">
            {{ collection.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </form>

  <div class="subpage-actions">
    <button bciPrimaryButton
            [disabled]="submitDisabled"
            (click)="save()">
      Save
    </button>
    <button bciSecondaryButton
            (click)="close()">
      Cancel
    </button>
  </div>
</bci-modal-component>
