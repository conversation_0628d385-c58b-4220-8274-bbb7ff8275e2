{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "extends": ["eslint:recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:@angular-eslint/recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["eslint-plugin-unicorn", "deprecation"], "rules": {"@angular-eslint/no-host-metadata-property": "off", "@angular-eslint/no-input-rename": "off", "@angular-eslint/no-lifecycle-call": "error", "@angular-eslint/no-queries-metadata-property": "error", "@angular-eslint/prefer-output-readonly": "error", "@angular-eslint/use-component-selector": "error", "@angular-eslint/directive-selector": ["error", {"type": "attribute", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "@typescript-eslint/array-type": ["error", {"default": "array"}], "@typescript-eslint/consistent-type-definitions": "error", "@typescript-eslint/dot-notation": "off", "@typescript-eslint/explicit-member-accessibility": ["error", {"accessibility": "no-public", "overrides": {"constructors": "off", "parameterProperties": "off"}}], "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unnecessary-boolean-literal-compare": "error", "@typescript-eslint/no-var-requires": "error", "@typescript-eslint/member-ordering": ["error", {"default": ["signature", "public-static-field", "protected-static-field", "private-static-field", "public-decorated-field", "protected-decorated-field", "private-decorated-field", "public-instance-field", "protected-instance-field", "private-instance-field", "public-abstract-field", "protected-abstract-field", "public-field", "protected-field", "private-field", "static-field", "instance-field", "abstract-field", "decorated-field", "field", "public-constructor", "protected-constructor", "private-constructor", "constructor", "public-static-method", "protected-static-method", "private-static-method", "public-decorated-method", "protected-decorated-method", "private-decorated-method", "public-instance-method", "protected-instance-method", "private-instance-method", "public-abstract-method", "protected-abstract-method", "public-method", "protected-method", "private-method", "static-method", "instance-method", "abstract-method", "decorated-method", "method"]}], "@typescript-eslint/no-shadow": "error", "@typescript-eslint/naming-convention": ["error", {"selector": "default", "format": ["camelCase"], "leadingUnderscore": "allow", "trailingUnderscore": "allow", "filter": {"regex": "^\\d*", "match": false}}, {"selector": "variable", "format": ["camelCase", "UPPER_CASE"], "leadingUnderscore": "allow", "trailingUnderscore": "allow"}, {"selector": "typeLike", "format": ["PascalCase"]}, {"selector": "enumMember", "format": ["PascalCase"]}], "no-irregular-whitespace": "off", "@typescript-eslint/no-unused-expressions": ["error", {"allowTernary": true}], "@typescript-eslint/explicit-function-return-type": ["error", {"allowExpressions": true, "allowHigherOrderFunctions": true}], "arrow-body-style": "off", "arrow-parens": ["off", "always"], "brace-style": ["error", "1tbs"], "eqeqeq": ["error", "smart"], "max-len": ["error", {"code": 280}], "no-duplicate-imports": "error", "no-magic-numbers": "off", "@typescript-eslint/no-magic-numbers": "off", "no-multiple-empty-lines": "error", "no-underscore-dangle": "off", "no-shadow": "off", "spaced-comment": "error", "unicorn/filename-case": "error", "deprecation/deprecation": "warn", "no-prototype-builtins": "off", "no-dupe-class-members": "off", "no-restricted-globals": ["error", "NotificationService"], "jsdoc/newline-after-description": "off"}}, {"files": ["*.spec.ts"], "rules": {"@typescript-eslint/no-magic-numbers": "off", "@angular-eslint/use-component-selector": "off", "no-restricted-globals": ["error", "fdescribe", "fit"], "no-undef": "off"}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {"@angular-eslint/template/no-any": "error", "@angular-eslint/template/eqeqeq": ["error", {"allowNullOrUndefined": true}]}}]}