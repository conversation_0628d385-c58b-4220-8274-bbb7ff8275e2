﻿using BCI.DocupediaBot.Infrastructure.Entities;
using BCI.DocupediaBot.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Infrastructure.Domain
{
  public interface IDomainService<T> where T : class, IEntity
  {
    IEntityRepository<T> Repository { get; }

    Task CreateAsync(T entity);
    Task DeleteAsync(T entity);
    Task<List<T>> GetAllAsync();
    Task<T> GetAsync(object id);
    Task UpdateAsync(T entity);

    Task<long> CountAsync(Expression<Func<T, bool>> expression);

    Task<(List<T> Items, long TotalCount)> PageAsync(int? pageNumber, int? pageSize, Expression<Func<T, bool>> expression);

    Task CreateRangeAsync(IList<T> entities);
    Task UpdateRangeAsync(IList<T> entities);
    Task DeleteRangeAsync(IList<T> entities);

    Task<List<T>> QueryAsync(Expression<Func<T, bool>> expression);

  }
}