import {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>utt<PERSON>,
  MatButtonModule,
  MatFabAnchor,
  Mat<PERSON>abButton,
  <PERSON><PERSON>conAnch<PERSON>,
  <PERSON><PERSON><PERSON>Button,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-27DZMUYK.js";
import "./chunk-TQKN2ASK.js";
import "./chunk-5X2BSBAM.js";
import "./chunk-X3P73C3G.js";
import "./chunk-DWV65MH7.js";
import "./chunk-KXOMTDJ6.js";
import "./chunk-KRK546R7.js";
import "./chunk-Y4T55RDF.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ButtonModule,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>IconAnch<PERSON>,
  <PERSON><PERSON><PERSON>Butt<PERSON>,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
