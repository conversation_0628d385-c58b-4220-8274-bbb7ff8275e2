import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DocupediaBotComponent } from './docupedia/components/docupedia-bot/docupedia-bot.component';
import { DocupediaTabsComponent } from './docupedia/components/docupedia-tabs/docupedia-tabs.component';
import { DocupediaImportComponent } from './docupedia/components/docupedia-import/docupedia-import.component';
import { DeepdocTabsComponent } from './deepdoc/components/deepdoc-tabs/deepdoc-tabs.component';
import { DeepdocBotComponent } from './deepdoc/components/deepdoc-bot/deepdoc-bot.component';
import { DeepdocImportComponent } from './deepdoc/components/deepdoc-import/deepdoc-import.component';

const routes: Routes = [
  {
    path: '',
    component: DocupediaTabsComponent,
    children: [
      {
        path: '',
        redirectTo: 'docupedia-bot',
        pathMatch: 'full',
      },
      {
        path: 'docupedia-import',
        component: DocupediaImportComponent,
      },
      {
        path: 'docupedia-bot',
        component: DocupediaBotComponent,
      },
    ],
  },
  {
    path: '',
    component: DeepdocTabsComponent,
    children: [
      {
        path: '',
        redirectTo: 'deepdoc-bot',
        pathMatch: 'full',
      },
      {
        path: 'deepdoc-import',
        component: DeepdocImportComponent,
      },
      {
        path: 'deepdoc-bot',
        component: DeepdocBotComponent,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ChatbotRoutingModule { }
