using AutoMapper;
using BCI.DocupediaBot.Application.Services.Cache;
using BCI.DocupediaBot.Application.Services.Collection;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Application.Services.PagesInCollection;
using BCI.DocupediaBot.Application.Services.SysUsersInGroup;
using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using Qdrant.Client;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;

namespace BCI.DocupediaBot.Performance.Tests
{



  public class CollectionServicePerformanceTests
  {
    private readonly ITestOutputHelper _output;
    private readonly Mock<ICollectionRepository> _mockCollectionRepository;
    private readonly Mock<IPageRepository> _mockPageRepository;
    private readonly Mock<ISysUserRepository> _mockSysUserRepository;
    private readonly Mock<ISysUsersInGroupRepository> _mockSysUsersInGroupRepository;
    private readonly Mock<IPagesInCollectionService> _mockPagesInCollectionService;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<QdrantClient> _mockQdrantClient;
    private readonly Mock<ILogger<CollectionService>> _mockLogger;
    private readonly Mock<ICurrentUserAccessor> _mockCurrentUserAccessor;
    private readonly ISysUsersInGroupService _sysUsersInGroupService;

    public CollectionServicePerformanceTests(ITestOutputHelper output)
    {
      _output = output;
      _mockCollectionRepository = new Mock<ICollectionRepository>();
      _mockPageRepository = new Mock<IPageRepository>();
      _mockSysUserRepository = new Mock<ISysUserRepository>();
      _mockSysUsersInGroupRepository = new Mock<ISysUsersInGroupRepository>();
      _mockPagesInCollectionService = new Mock<IPagesInCollectionService>();
      _mockMapper = new Mock<IMapper>();
      _mockQdrantClient = new Mock<QdrantClient>();
      _mockLogger = new Mock<ILogger<CollectionService>>();
      _mockCurrentUserAccessor = new Mock<ICurrentUserAccessor>();


      var memoryCache = new MemoryCache(new MemoryCacheOptions());
      var cacheLogger = new Mock<ILogger<UserGroupCacheService>>();
      var cacheService = new UserGroupCacheService(memoryCache, cacheLogger.Object);
      var sysUsersInGroupLogger = new Mock<ILogger<SysUsersInGroupService>>();

      _sysUsersInGroupService = new SysUsersInGroupService(
        _mockSysUsersInGroupRepository.Object,
        cacheService,
        sysUsersInGroupLogger.Object);
    }

    [Fact]
    public async Task QueryCollectionsAsync_OptimizedVersion_ShouldBeFasterThanNaiveVersion()
    {

      var currentUserId = "testuser";
      var currentUserEntity = new SysUser { Id = Guid.NewGuid(), UserNTAccount = currentUserId };
      var currentUserGroupIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };


      var collections = Enumerable.Range(1, 100).Select(i => new Collection
      {
        Id = Guid.NewGuid(),
        Name = $"Collection {i}",
        Creator = $"creator{i}",
        Comment = $"Comment {i}"
      }).ToList();


      var creators = collections.Select(c => new SysUser
      {
        Id = Guid.NewGuid(),
        UserNTAccount = c.Creator
      }).ToList();


      var creatorGroupsDict = creators.ToDictionary(
        c => c.Id,
        c => creators.IndexOf(c) % 3 == 0 ? currentUserGroupIds : new List<Guid> { Guid.NewGuid() }
      );


      _mockCurrentUserAccessor.Setup(x => x.UserId).Returns(currentUserId);
      _mockSysUserRepository.Setup(r => r.QueryAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<SysUser, bool>>>(), true))
        .Returns<System.Linq.Expressions.Expression<System.Func<SysUser, bool>>, bool>((expr, noTracking) =>
        {
          var compiled = expr.Compile();
          var result = new List<SysUser> { currentUserEntity }.Concat(creators).Where(compiled).ToList();
          return Task.FromResult(result);
        });

      _mockCollectionRepository.Setup(r => r.GetAllAsync(true)).ReturnsAsync(collections);

      _mockSysUsersInGroupRepository.Setup(r => r.GetGroupIdsByUserIdAsync(currentUserEntity.Id))
        .ReturnsAsync(currentUserGroupIds);

      _mockSysUsersInGroupRepository.Setup(r => r.GetGroupIdsByUserIdsBatchAsync(It.IsAny<IEnumerable<Guid>>()))
        .ReturnsAsync(creatorGroupsDict);

      _mockMapper.Setup(m => m.Map<List<BCI.DocupediaBot.Application.Contracts.Dtos.Collection.CollectionResponseDTO>>(It.IsAny<List<Collection>>()))
        .Returns<List<Collection>>(collections => collections.Select(c => new BCI.DocupediaBot.Application.Contracts.Dtos.Collection.CollectionResponseDTO
        {
          Id = c.Id,
          Name = c.Name,
          Comment = c.Comment
        }).ToList());

      var service = new CollectionService(
        _mockCollectionRepository.Object,
        _mockPageRepository.Object,
        _mockPagesInCollectionService.Object,
        _mockMapper.Object,
        _mockQdrantClient.Object,
        _mockLogger.Object,
        _mockSysUserRepository.Object,
        _sysUsersInGroupService,
        _mockCurrentUserAccessor.Object);


      var sw = Stopwatch.StartNew();
      var result = await service.QueryCollectionsAsync();
      sw.Stop();


      Assert.NotNull(result);
      var expectedCount = collections.Count(c => creators.IndexOf(creators.First(cr => cr.UserNTAccount == c.Creator)) % 3 == 0);
      Assert.Equal(expectedCount, result.Count);

      _output.WriteLine($"Optimized QueryCollectionsAsync completed in {sw.ElapsedMilliseconds}ms");
      _output.WriteLine($"Processed {collections.Count} collections, returned {result.Count} matching collections");
      _output.WriteLine($"Average time per collection: {(double)sw.ElapsedMilliseconds / collections.Count:F2}ms");


      Assert.True(sw.ElapsedMilliseconds < 1000, $"Query should complete within 1 second, actual: {sw.ElapsedMilliseconds}ms");
    }

    [Theory]
    [InlineData(10)]
    [InlineData(50)]
    [InlineData(100)]
    [InlineData(500)]
    public async Task QueryCollectionsAsync_ShouldScaleLinearlyWithCollectionCount(int collectionCount)
    {

      var currentUserId = "testuser";
      var currentUserEntity = new SysUser { Id = Guid.NewGuid(), UserNTAccount = currentUserId };
      var currentUserGroupIds = new List<Guid> { Guid.NewGuid() };

      var collections = Enumerable.Range(1, collectionCount).Select(i => new Collection
      {
        Id = Guid.NewGuid(),
        Name = $"Collection {i}",
        Creator = "creator1",
        Comment = $"Comment {i}"
      }).ToList();

      var creator = new SysUser { Id = Guid.NewGuid(), UserNTAccount = "creator1" };


      _mockCurrentUserAccessor.Setup(x => x.UserId).Returns(currentUserId);
      _mockSysUserRepository.Setup(r => r.QueryAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<SysUser, bool>>>(), true))
        .ReturnsAsync(new List<SysUser> { currentUserEntity, creator });

      _mockCollectionRepository.Setup(r => r.GetAllAsync(true)).ReturnsAsync(collections);

      _mockSysUsersInGroupRepository.Setup(r => r.GetGroupIdsByUserIdAsync(currentUserEntity.Id))
        .ReturnsAsync(currentUserGroupIds);

      _mockSysUsersInGroupRepository.Setup(r => r.GetGroupIdsByUserIdsBatchAsync(It.IsAny<IEnumerable<Guid>>()))
        .ReturnsAsync(new Dictionary<Guid, List<Guid>> { { creator.Id, currentUserGroupIds } });

      _mockMapper.Setup(m => m.Map<List<BCI.DocupediaBot.Application.Contracts.Dtos.Collection.CollectionResponseDTO>>(It.IsAny<List<Collection>>()))
        .Returns<List<Collection>>(collections => collections.Select(c => new BCI.DocupediaBot.Application.Contracts.Dtos.Collection.CollectionResponseDTO
        {
          Id = c.Id,
          Name = c.Name,
          Comment = c.Comment
        }).ToList());

      var service = new CollectionService(
        _mockCollectionRepository.Object,
        _mockPageRepository.Object,
        _mockPagesInCollectionService.Object,
        _mockMapper.Object,
        _mockQdrantClient.Object,
        _mockLogger.Object,
        _mockSysUserRepository.Object,
        _sysUsersInGroupService,
        _mockCurrentUserAccessor.Object);


      var sw = Stopwatch.StartNew();
      var result = await service.QueryCollectionsAsync();
      sw.Stop();


      Assert.Equal(collectionCount, result.Count);

      var avgTimePerCollection = (double)sw.ElapsedMilliseconds / collectionCount;
      _output.WriteLine($"Collections: {collectionCount}, Total time: {sw.ElapsedMilliseconds}ms, Avg per collection: {avgTimePerCollection:F2}ms");


      Assert.True(avgTimePerCollection < 2.0, $"Average time per collection ({avgTimePerCollection:F2}ms) should be less than 2ms");
    }

    [Fact]
    public async Task QueryCollectionsAsync_WithCaching_ShouldImprovePerformanceOnRepeatedCalls()
    {

      var currentUserId = "testuser";
      var currentUserEntity = new SysUser { Id = Guid.NewGuid(), UserNTAccount = currentUserId };
      var currentUserGroupIds = new List<Guid> { Guid.NewGuid() };

      var collections = Enumerable.Range(1, 50).Select(i => new Collection
      {
        Id = Guid.NewGuid(),
        Name = $"Collection {i}",
        Creator = "creator1",
        Comment = $"Comment {i}"
      }).ToList();

      var creator = new SysUser { Id = Guid.NewGuid(), UserNTAccount = "creator1" };


      _mockCurrentUserAccessor.Setup(x => x.UserId).Returns(currentUserId);
      _mockSysUserRepository.Setup(r => r.QueryAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<SysUser, bool>>>(), true))
        .ReturnsAsync(new List<SysUser> { currentUserEntity, creator });

      _mockCollectionRepository.Setup(r => r.GetAllAsync(true)).ReturnsAsync(collections);

      _mockSysUsersInGroupRepository.Setup(r => r.GetGroupIdsByUserIdAsync(currentUserEntity.Id))
        .ReturnsAsync(currentUserGroupIds);

      _mockSysUsersInGroupRepository.Setup(r => r.GetGroupIdsByUserIdsBatchAsync(It.IsAny<IEnumerable<Guid>>()))
        .ReturnsAsync(new Dictionary<Guid, List<Guid>> { { creator.Id, currentUserGroupIds } });

      _mockMapper.Setup(m => m.Map<List<BCI.DocupediaBot.Application.Contracts.Dtos.Collection.CollectionResponseDTO>>(It.IsAny<List<Collection>>()))
        .Returns<List<Collection>>(collections => collections.Select(c => new BCI.DocupediaBot.Application.Contracts.Dtos.Collection.CollectionResponseDTO
        {
          Id = c.Id,
          Name = c.Name,
          Comment = c.Comment
        }).ToList());

      var service = new CollectionService(
        _mockCollectionRepository.Object,
        _mockPageRepository.Object,
        _mockPagesInCollectionService.Object,
        _mockMapper.Object,
        _mockQdrantClient.Object,
        _mockLogger.Object,
        _mockSysUserRepository.Object,
        _sysUsersInGroupService,
        _mockCurrentUserAccessor.Object);


      var sw1 = Stopwatch.StartNew();
      var result1 = await service.QueryCollectionsAsync();
      sw1.Stop();


      var sw2 = Stopwatch.StartNew();
      var result2 = await service.QueryCollectionsAsync();
      sw2.Stop();


      Assert.Equal(result1.Count, result2.Count);
      Assert.True(sw2.ElapsedMilliseconds <= sw1.ElapsedMilliseconds,
        $"Second call ({sw2.ElapsedMilliseconds}ms) should be faster or equal to first call ({sw1.ElapsedMilliseconds}ms)");

      _output.WriteLine($"First call: {sw1.ElapsedMilliseconds}ms");
      _output.WriteLine($"Second call (with cache): {sw2.ElapsedMilliseconds}ms");

      if (sw1.ElapsedMilliseconds > 0)
      {
        var improvement = ((double)(sw1.ElapsedMilliseconds - sw2.ElapsedMilliseconds) / sw1.ElapsedMilliseconds * 100);
        _output.WriteLine($"Performance improvement: {improvement:F1}%");
      }
    }
  }
}
