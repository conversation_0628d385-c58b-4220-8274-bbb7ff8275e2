{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-python.js"], "sourcesContent": ["Prism.languages.python = {\n  'comment': {\n    pattern: /(^|[^\\\\])#.*/,\n    lookbehind: true,\n    greedy: true\n  },\n  'string-interpolation': {\n    pattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n    greedy: true,\n    inside: {\n      'interpolation': {\n        // \"{\" <expression> <optional \"!s\", \"!r\", or \"!a\"> <optional \":\" format specifier> \"}\"\n        pattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n        lookbehind: true,\n        inside: {\n          'format-spec': {\n            pattern: /(:)[^:(){}]+(?=\\}$)/,\n            lookbehind: true\n          },\n          'conversion-option': {\n            pattern: /![sra](?=[:}]$)/,\n            alias: 'punctuation'\n          },\n          rest: null\n        }\n      },\n      'string': /[\\s\\S]+/\n    }\n  },\n  'triple-quoted-string': {\n    pattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n    greedy: true,\n    alias: 'string'\n  },\n  'string': {\n    pattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n    greedy: true\n  },\n  'function': {\n    pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n    lookbehind: true\n  },\n  'class-name': {\n    pattern: /(\\bclass\\s+)\\w+/i,\n    lookbehind: true\n  },\n  'decorator': {\n    pattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n    lookbehind: true,\n    alias: ['annotation', 'punctuation'],\n    inside: {\n      'punctuation': /\\./\n    }\n  },\n  'keyword': /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n  'builtin': /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n  'boolean': /\\b(?:False|None|True)\\b/,\n  'number': /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n  'operator': /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n  'punctuation': /[{}[\\];(),.:]/\n};\nPrism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = Prism.languages.python;\nPrism.languages.py = Prism.languages.python;"], "mappings": ";AAAA,MAAM,UAAU,SAAS;AAAA,EACvB,WAAW;AAAA,IACT,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,wBAAwB;AAAA,IACtB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,iBAAiB;AAAA;AAAA,QAEf,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,eAAe;AAAA,YACb,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,wBAAwB;AAAA,IACtB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO,CAAC,cAAc,aAAa;AAAA,IACnC,QAAQ;AAAA,MACN,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AACjB;AACA,MAAM,UAAU,OAAO,sBAAsB,EAAE,OAAO,eAAe,EAAE,OAAO,OAAO,MAAM,UAAU;AACrG,MAAM,UAAU,KAAK,MAAM,UAAU;", "names": []}