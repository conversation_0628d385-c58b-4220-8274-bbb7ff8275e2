{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-javascript.js"], "sourcesContent": ["Prism.languages.javascript = Prism.languages.extend('clike', {\n  'class-name': [Prism.languages.clike['class-name'], {\n    pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n    lookbehind: true\n  }],\n  'keyword': [{\n    pattern: /((?:^|\\})\\s*)catch\\b/,\n    lookbehind: true\n  }, {\n    pattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n    lookbehind: true\n  }],\n  // Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n  'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n  'number': {\n    pattern: RegExp(/(^|[^\\w$])/.source + '(?:' + (\n    // constant\n    /NaN|Infinity/.source + '|' +\n    // binary integer\n    /0[bB][01]+(?:_[01]+)*n?/.source + '|' +\n    // octal integer\n    /0[oO][0-7]+(?:_[0-7]+)*n?/.source + '|' +\n    // hexadecimal integer\n    /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source + '|' +\n    // decimal bigint\n    /\\d+(?:_\\d+)*n/.source + '|' +\n    // decimal number (integer or float) but no bigint\n    /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source) + ')' + /(?![\\w$])/.source),\n    lookbehind: true\n  },\n  'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n});\nPrism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\nPrism.languages.insertBefore('javascript', 'keyword', {\n  'regex': {\n    pattern: RegExp(\n    // lookbehind\n    // eslint-disable-next-line regexp/no-dupe-characters-character-class\n    /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source +\n    // Regex pattern:\n    // There are 2 regex patterns here. The RegExp set notation proposal added support for nested character\n    // classes if the `v` flag is present. Unfortunately, nested CCs are both context-free and incompatible\n    // with the only syntax, so we have to define 2 different regex patterns.\n    /\\//.source + '(?:' + /(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source + '|' +\n    // `v` flag syntax. This supports 3 levels of nested character classes.\n    /(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source + ')' +\n    // lookahead\n    /(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source),\n    lookbehind: true,\n    greedy: true,\n    inside: {\n      'regex-source': {\n        pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n        lookbehind: true,\n        alias: 'language-regex',\n        inside: Prism.languages.regex\n      },\n      'regex-delimiter': /^\\/|\\/$/,\n      'regex-flags': /^[a-z]+$/\n    }\n  },\n  // This must be declared before keyword because we use \"function\" inside the look-forward\n  'function-variable': {\n    pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n    alias: 'function'\n  },\n  'parameter': [{\n    pattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }, {\n    pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }, {\n    pattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }, {\n    pattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }],\n  'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n});\nPrism.languages.insertBefore('javascript', 'string', {\n  'hashbang': {\n    pattern: /^#!.*/,\n    greedy: true,\n    alias: 'comment'\n  },\n  'template-string': {\n    pattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n    greedy: true,\n    inside: {\n      'template-punctuation': {\n        pattern: /^`|`$/,\n        alias: 'string'\n      },\n      'interpolation': {\n        pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n        lookbehind: true,\n        inside: {\n          'interpolation-punctuation': {\n            pattern: /^\\$\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          rest: Prism.languages.javascript\n        }\n      },\n      'string': /[\\s\\S]+/\n    }\n  },\n  'string-property': {\n    pattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n    lookbehind: true,\n    greedy: true,\n    alias: 'property'\n  }\n});\nPrism.languages.insertBefore('javascript', 'operator', {\n  'literal-property': {\n    pattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n    lookbehind: true,\n    alias: 'property'\n  }\n});\nif (Prism.languages.markup) {\n  Prism.languages.markup.tag.addInlined('script', 'javascript');\n\n  // add attribute support for all DOM events.\n  // https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n  Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source, 'javascript');\n}\nPrism.languages.js = Prism.languages.javascript;"], "mappings": ";AAAA,MAAM,UAAU,aAAa,MAAM,UAAU,OAAO,SAAS;AAAA,EAC3D,cAAc,CAAC,MAAM,UAAU,MAAM,YAAY,GAAG;AAAA,IAClD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AAAA,EACD,WAAW,CAAC;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACd,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,EACd,CAAC;AAAA;AAAA,EAED,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,SAAS,OAAO,aAAa,SAAS;AAAA,KAEtC,eAAe,SAAS;AAAA,IAExB,0BAA0B,SAAS;AAAA,IAEnC,4BAA4B,SAAS;AAAA,IAErC,sCAAsC,SAAS;AAAA,IAE/C,gBAAgB,SAAS;AAAA,IAEzB,oFAAoF,UAAU,MAAM,YAAY,MAAM;AAAA,IACtH,YAAY;AAAA,EACd;AAAA,EACA,YAAY;AACd,CAAC;AACD,MAAM,UAAU,WAAW,YAAY,EAAE,CAAC,EAAE,UAAU;AACtD,MAAM,UAAU,aAAa,cAAc,WAAW;AAAA,EACpD,SAAS;AAAA,IACP,SAAS;AAAA;AAAA;AAAA,MAGT,0DAA0D;AAAA;AAAA;AAAA;AAAA,MAK1D,KAAK,SAAS,QAAQ,iEAAiE,SAAS;AAAA,MAEhG,qIAAqI,SAAS;AAAA,MAE9I,kEAAkE;AAAA,IAAM;AAAA,IACxE,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,gBAAgB;AAAA,QACd,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ,MAAM,UAAU;AAAA,MAC1B;AAAA,MACA,mBAAmB;AAAA,MACnB,eAAe;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAEA,qBAAqB;AAAA,IACnB,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,aAAa,CAAC;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,GAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ,MAAM,UAAU;AAAA,EAC1B,CAAC;AAAA,EACD,YAAY;AACd,CAAC;AACD,MAAM,UAAU,aAAa,cAAc,UAAU;AAAA,EACnD,YAAY;AAAA,IACV,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,IACjB,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,wBAAwB;AAAA,QACtB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,6BAA6B;AAAA,YAC3B,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,MAAM,MAAM,UAAU;AAAA,QACxB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,mBAAmB;AAAA,IACjB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACF,CAAC;AACD,MAAM,UAAU,aAAa,cAAc,YAAY;AAAA,EACrD,oBAAoB;AAAA,IAClB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA,EACT;AACF,CAAC;AACD,IAAI,MAAM,UAAU,QAAQ;AAC1B,QAAM,UAAU,OAAO,IAAI,WAAW,UAAU,YAAY;AAI5D,QAAM,UAAU,OAAO,IAAI,aAAa,yNAAyN,QAAQ,YAAY;AACvR;AACA,MAAM,UAAU,KAAK,MAAM,UAAU;", "names": []}