# yaml-language-server: $schema=https://bcidevschemas.blob.core.windows.net/helm/v1/module-chart.schema.json

image:
  registry: bcidockerregistry.azurecr.io
  pullPolicy: Always

# Local variables used only in this chart
# The values for the keys are either strings or dictionaries
local:
  apmCaptureBody: "errors"
  apmCaptureHeaders: "true"
  defaultServicePort: "8080" # Port range is 1024~65535
  logging: {}

export:
  databases:
    ModuleName:
      name: DB_ModuleName
      userName: ModuleName
      secretUserKey: DbSettings__User
      secretPasswordKey: DbSettings__Password
      roles:
        - db_owner
      permissions:
        oracle:
          - NEXEED_BASIC_ROLE
      secretName: data-source-secret
      mssqlSchema: ModuleName
      type: MSSQL|ORACLE
      programmingLanguage: NET
  contextPath: context-path
  namespaceSuffix: ModuleName
  enabled: false
  iasHelmSchemaVersion: 2.2
  macmaConfiguration:
    display_name: "Module Display Name"
    public_client: True
    redirect_uris:
      - "{{ include \"utility-toolkit.moduleUrl\" (list . \"ModuleName\") }}/*"
    web_origins:
      - "{{ include \"utility-toolkit.moduleRootUrl\" (list . \"ModuleName\") }}"
    use_single_audience_tokens: True
    audience_mapper: False
    service_account_roles:
      - module: portal
        name: Portal_Registration
        bci_only: True
      - module: mmpd
        name: User
        bci_only: False
      - module: macma
        name: user-reader
        bci_only: False
      - module: ModuleName
        name: Default_Role
        bci_only: False
    secrets:
      - namespace: '{{ include "utility-toolkit.getNamespaceForModule" (list . "ModuleName") }}'
        secret_name: macma-client-secret
        user_key: YOUR_MODULE_CLIENT_ID
        password_key: YOUR_MODULE_CLIENT_SECRET
    foreignSecrets:
      - module: portal
        secret_name: portal-client-id
        user_key: PORTAL_CLIENT_ID
      - module: mmpd
        secret_name: mmpd-client-id
        user_key: MMPD_CLIENT_ID
      - module: macma
        secret_name: macma-client-id
        user_key: MACMA_CLIENT_ID
    admin_roles:
      - Default_Role
  serverMode: BMLP
  enablePortalRegistration: true
  enableMessagingIntegration: false
  databaseEncryptionSecret: "toBeSet"

deployments:
  ModuleName-service:
    image: ModuleName/application:1.0.0-dev
    envConfigMaps:
      - global-configmap
      - ModuleName-shared-configmap
    mountedConfigmaps:
      - name: ca-certificates-configmap
        mountPath: /etc/ssl/trusted
        defaultMode: 0744
        keys:
          - ca-cert.pem
    envSecrets:
      - ModuleName-shared-secrets
      - macma-client-secret
      - portal-client-id
      - mmpd-client-id
      - data-source-secret
      - macma-client-id

    env:
      NEXEED_ModuleNameAGEMENT_SERVICE_IsIAS: "true"

      Logging__LogLevel__Default: "{{ include \"utility-toolkit.convertToDotnetLoglevel\" (default .Values.global.logging.application .Values.local.logging.application) }}"
      
      DatabaseProvider: "{{ include \"utility-toolkit.serverInstanceType\" (list . .Values.global.modules.ModuleName.databases.ModuleName) }}"
      ConnectionString: "{{ include \"utility-toolkit.connectionStringAuth\" (list . .Values.global.modules.ModuleName.databases.ModuleName) }}{{ include \"utility-toolkit.extraConnectionStringTLS\" (list . .Values.global.modules.ModuleName.databases.ModuleName) }}"
      Database__Version: ""

      OIDC__ServiceUrl: "{{ include \"utility-toolkit.moduleUrl\"  (list . \"macma\") }}"
      OIDC__RequireHttps: "{{ .Values.global.nexeedServerTLSEnabled }}"
      OIDC__ClientId: "$(YOUR_MODULE_CLIENT_ID)"
      OIDC__ClientSecret: "$(YOUR_MODULE_CLIENT_SECRET)"
      
      OIDC__Introspection__EnableCaching: "true"
      OIDC__Introspection__CachingDuration: "30"

      OIDC__Acl__RefreshInterval: "60"

      OIDC__NamedHttpClients__DefaultTokenEndpoint: "{{ include \"utility-toolkit.moduleUrl\"  (list . \"macma\") }}/auth/realms/{tenantId}/protocol/openid-connect/token"
      OIDC__NamedHttpClients__Clients__MacmaClient__Scopes__0: "aud:$(MACMA_CLIENT_ID)"
      OIDC__NamedHttpClients__Clients__PortalClient__TokenEndpoint: "{{ include \"utility-toolkit.moduleUrl\"  (list . \"macma\") }}/auth/realms/{{ .Values.global.nexeedMacmaTenant0Id }}/protocol/openid-connect/token"
      OIDC__NamedHttpClients__Clients__PortalClient__Scopes__0: "aud:$(PORTAL_CLIENT_ID)"
      
      TenantConfig__Tenant0: "{{ .Values.global.nexeedMacmaTenant0Id }}"
      
      Portal__TenantId: "{{ .Values.global.nexeedMacmaTenant0Id }}"
      Portal__Url: "{{ include \"utility-toolkit.moduleUrl\"  (list . \"portal\") }}"
      Portal__Info__Id: "$(YOUR_MODULE_CLIENT_ID)"
      Portal__Info__BaseUrl: "{{ include \"utility-toolkit.moduleUrl\"  (list . \"ModuleName\") }}"
      Portal__Info__AuthProviderClientId: "$(YOUR_MODULE_CLIENT_ID)"

    labels:
      app: ModuleName-service
    ports:
      - name: backend-http
        protocol: TCP
        value: 8080
        expose:
          service:
            type: ClusterIP
            port: 80
        probes:
          readiness:
            path: "/health/ready"
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 10
            failureThreshold: 5
          liveness:
            path: "/health/live"
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 10
            failureThreshold: 5
          startup:
            path: "/health/startup"
            initialDelaySeconds: 10
            timeoutSeconds: 3
            periodSeconds: 10
            failureThreshold: 5
    securityContext:
      runAsNonRoot: true
      allowPrivilegeEscalation: false
    replicaCount: 1
    resources:
      limits:
        memory: "1024M"
        cpu: "1000m"
      requests:
        memory: "300M"
        cpu: "100m"

# Configmaps definitions
configmaps:
  global-configmap:
    NEXEED_GLOBAL_ENVIRONMENT_NAME: "{{ .Values.global.nexeedGlobalEnvironmentName }}"
    NEXEED_GLOBAL_SYSTEM_NAME: "{{ .Values.global.nexeedGlobalSystemName }}"
    ELASTIC_APM_ENABLED: "{{ .Values.global.monitoringApmEnabled }}"
    ELASTIC_APM_ENVIRONMENT: "{{ printf \"%.63s\" .Values.global.nexeedGlobalEnvironmentName }}"
    ELASTIC_APM_SERVER_URLS: "{{ .Values.global.nexeedMonitoringApmUrl }}"
    ELASTIC_APM_CAPTURE_BODY: "{{ .Values.local.apmCaptureBody }}"
    ELASTIC_APM_CAPTURE_HEADERS: "{{ .Values.local.apmCaptureHeaders }}"
  ModuleName-shared-configmap:
    DOTNET_TLS_DISABLE: "{{ not .Values.global.nexeedServerTLSEnabled }}" 
    DOTNET_APP_HTTP_PORT: "{{ .Values.local.defaultServicePort }}"
    ASPNETCORE_URLS: "http://+:{{ .Values.local.defaultServicePort }}" 
    DOTNET_APP_LOG_LEVEL: "{{ include \"utility-toolkit.convertToDotnetLoglevel\" (default .Values.global.logging.application .Values.local.logging.application) }}"
    SSL_CERT_DIR: "/etc/ssl/trusted"
  ca-certificates-configmap: |
    ca-cert.pem: |{{ .Values.global.nexeedCACerts | nindent 2 }}
  gateway-configmap:
    NGINX_TLS_DISABLE: "true"
    APP_TLS_DISABLE: "true"
    NGINX_IPV6_DISABLE: "true"
    NGINX_HEADER_SIZE: "8k"
    NGINX_WAF_DISABLE: "true"
    NGINX_RESOLVER: "kube-dns.kube-system.svc.cluster.local ipv6=off"
    NGINX_LOG_LEVEL: "debug"
  
secrets:
  ModuleName-shared-secrets:
    ELASTIC_APM_SECRET_TOKEN: "{{ .Values.global.elasticApmSecretToken }}"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
