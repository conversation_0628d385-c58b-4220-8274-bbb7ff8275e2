{"options": [{"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "Bug", "assignToRequestor": "true", "additionalFields": "{}"}}], "triggers": [{"branchFilters": ["+refs/heads/develop"], "pathFilters": [], "batchChanges": false, "maxConcurrentBuildsPerBranch": 1, "pollingInterval": 0, "triggerType": 2}], "variables": {"ArtifactName": {"value": "BCI.DocupediaBot_Web"}, "FossIDProjectName": {"value": "BCI-ESW-CN-BCI.DocupediaBot-Web"}, "NodeVersion": {"value": "^20.0.0"}, "ProductName": {"value": "BCI-ESW-CN-BCI.DocupediaBot-Web"}, "PublishFileOrDirectoryPath": {"value": "$(Build.SourcesDirectory)/src/dist"}, "sonarExclusionsFiles": {"value": ",**/environment.*.ts"}, "SonarProductName": {"value": "BCI_CN_BCI.DocupediaBot_Web"}, "WebAppBaseHref": {"value": "/"}, "WebAppRoot": {"value": "$(Build.SourcesDirectory)/src"}, "WhiteSourceDisplayNameDisclosureDoc": {"value": "DisclosureDoc"}, "WhiteSourceProductName": {"value": "BCI-ESW-CN-BCI.DocupediaBot"}, "OSSIgnoreFilesPath": {"value": "/"}}, "variableGroups": [{"variables": {"ArtifactoryServerApiKey": {"value": null, "isSecret": true}, "ArtifactoryServerUrl": {"value": "https://rb-artifactory.bosch.com"}, "ArtifactoryServerUser": {"value": "iin5imb"}}, "type": "Vsts", "name": "01-Artifactory variable group", "description": "", "id": 27}, {"variables": {"FossIDUrl": {"value": "https://rb-fossid.de.bosch.com/BCI"}, "FossIDUser": {"value": "bci_cicd_fossid"}, "FossidApikey": {"value": null, "isSecret": true}}, "type": "Vsts", "name": "02-FOSSID Variable group", "description": "", "id": 29}, {"variables": {"WhiteSourceUserKey": {"value": null, "isSecret": true}}, "type": "Vsts", "name": "03-Whitesource variable group", "description": "Whitesource variable group", "id": 26}, {"variables": {"RunSonarQube": {"value": "true"}, "RunSonarQubeCloud": {"value": "true"}}, "type": "Vsts", "name": "04-SonarQube variable group", "description": "", "id": 62}, {"variables": {"BuildPlatform": {"value": "any cpu"}, "BuildConfiguration": {"value": "release"}, "PathToNugetConfig": {"value": "D:\\BuildServer\\esmc_nuget\\NuGet.Config"}}, "type": "Vsts", "name": "05-VS Build variable group", "description": "", "id": 63}, {"variables": {"MajorVersion": {"value": "1"}, "MinorVersion": {"value": "0"}, "SpVersion": {"value": "0"}, "PathToNugetConfig": {"value": "D:\\BuildServer\\esmc_nuget\\NuGet.Config"}, "RunOSSCheck": {"value": "true"}}, "type": "Vsts", "name": "06-OSS pipeline variable group", "description": "", "id": 28}], "properties": {}, "tags": [], "_links": {"self": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_apis/build/Definitions/335?revision=16"}, "web": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_build/definition?definitionId=335"}, "editor": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_build/designer?id=335&_a=edit-build-definition"}, "badge": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_apis/build/status/335"}}, "jobAuthorizationScope": 2, "jobTimeoutInMinutes": 60, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Use Node", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "31c75bbb-bcdf-4706-8d7c-4da6a1959bc2", "versionSpec": "0.*", "definitionType": "task"}, "inputs": {"versionSource": "spec", "versionSpec": "$(NodeVersion)", "versionFilePath": "", "checkLatest": "true", "force32bit": "false", "nodejsMirror": "https://nodejs.org/dist", "retryCountOnDownloadFails": "5", "delayBetweenRetries": "1000"}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-110] SetMESVariables for Angular ", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "2837dc33-78a5-452b-b430-b6b7e1c2454b", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-140] SonarQube Pre-Build for Angular test", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "a4b2f883-c37a-43c4-97db-c648222aa8a1", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"pathToLcov.info": "test/coverage/lcov.info", "productName": "$(SonarProductName)", "sonarExclusions": "$(sonarExclusionsFiles)", "WebAppRoot": "$(WebAppRoot)"}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-180] Angular build tasks for App", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "cbd6aec2-c736-43b6-9c79-cdfb2df0dcf2", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"BreakBuildOnLintingError": "true", "EnableE2E": "false", "lintArguments": "run lint", "WebAppBaseHref": "$(WebAppBaseHref)", "WebAppRoot": "$(WebAppRoot)", "WebAppVersion": "\"$(MajorVersion).$(MinorVersion).$(SpVersion)\""}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-190] SonarQube Post-Build for Angular ", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "14d3274c-5ee2-4b31-886b-ec79feb9b6f5", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Publish Test Results", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "0b0f01ed-7dde-43ff-9cbb-e48954daf9b1", "versionSpec": "2.*", "definitionType": "task"}, "inputs": {"testRunner": "JUnit", "testResultsFiles": "$(Build.SourcesDirectory)/**/TESTS-*.xml", "searchFolder": "$(Build.SourcesDirectory)", "mergeTestResults": "true", "failTaskOnFailedTests": "false", "failTaskOnFailureToPublishResults": "false", "failTaskOnMissingResultsFile": "false", "testRunTitle": "", "platform": "", "configuration": "", "publishRunAttachments": "true"}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Publish code coverage results", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "2a7ebc54-c13e-490e-81a5-d7561ab7cd97", "versionSpec": "2.*", "definitionType": "task"}, "inputs": {"summaryFileLocation": "$(Build.SourcesDirectory)/**/cobertura/coverage.xml", "pathToSources": "$(WebAppRoot)", "failIfCoverageEmpty": "true"}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Publish Pipeline Artifact", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "ecdc45f6-832d-4ad9-b52b-ee49e94659be", "versionSpec": "1.*", "definitionType": "task"}, "inputs": {"path": "$(PublishFileOrDirectoryPath)", "artifactName": "$(ArtifactName)", "artifactType": "pipeline", "fileSharePath": "", "parallel": "false", "parallelCount": "8", "properties": ""}}], "name": "Angular Build", "refName": "Job_1", "condition": "succeeded()", "target": {"demands": ["yarn", "java"], "executionOptions": {"type": 0}, "allowScriptsAuthAccessOption": false, "type": 1}, "jobAuthorizationScope": 2}, {"steps": [{"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Use Node", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "31c75bbb-bcdf-4706-8d7c-4da6a1959bc2", "versionSpec": "0.*", "definitionType": "task"}, "inputs": {"versionSource": "spec", "versionSpec": "$(NodeVersion)", "versionFilePath": "", "checkLatest": "false", "force32bit": "false", "nodejsMirror": "https://nodejs.org/dist", "retryCountOnDownloadFails": "5", "delayBetweenRetries": "1000"}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-110] SetMESVariables for Angular ", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "2837dc33-78a5-452b-b430-b6b7e1c2454b", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Delete unrelevant files before OSS checking", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "b7e8b412-0437-4065-9371-edc5881de25b", "versionSpec": "1.*", "definitionType": "task"}, "inputs": {"SourceFolder": "$(Build.SourcesDirectory)/src", "Contents": "$(OSSIgnoreFilesPath)", "RemoveSourceFolder": "false", "RemoveDotFiles": "false"}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-160] OSS Check for Angular (3rd Party) $(WebAppRoot)", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "dbdea7d5-3abf-4bb3-8286-1d21cdca0aaf", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"AngularAppRoot": "$(WebAppRoot)", "ArtifactoryServerApiKey": "$(ArtifactoryServerApiKey)", "BreakBuildOnFindings": "", "FossIDApiKey": "$(FossIDApiKey)", "FossIDIgnores": "", "FossIDScanFolder": "$(WebAppRoot)", "FossIDUser": "$(FossIDUser)", "OSSApplicationId": "$(FossIDProjectName)", "PublishArtifactName": "", "RemoveImports": "", "WhiteSourceDisplayNameDisclosureDoc": "$(WhiteSourceDisplayNameDisclosureDoc)", "WhiteSourceProductName": "$(WhiteSourceProductName)", "WhiteSourceUserKey": "$(WhiteSourceUserKey)"}}], "name": "OSS Check", "refName": "Job_2", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "allowScriptsAuthAccessOption": false, "type": 1}, "jobAuthorizationScope": 2}], "type": 1}, "repository": {"properties": {"cleanOptions": "0", "labelSources": "0", "labelSourcesFormat": "$(build.buildNumber)", "reportBuildStatus": "true", "fetchDepth": "1", "gitLfsSupport": "false", "skipSyncSource": "false", "checkoutNestedSubmodules": "false"}, "id": "a6b1c1fc-00c7-41ac-92bf-6033cca42572", "type": "TfsGit", "name": "bosch-bci-ste", "url": "https://dev.azure.com/bosch-bci-cn/BCI_ESW1_CN_ProjectCollection/_git/bosch-bci-ste", "defaultBranch": "refs/heads/develop", "clean": "false", "checkoutSubmodules": false}, "processParameters": {}, "quality": 2, "id": 335, "name": "BCI.DocupediaBot_Pipeline_Frontend", "url": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_apis/build/Definitions/335?revision=16", "uri": "vstfs:///Build/Definition/335", "path": "\\BCI.DocupediaBot", "type": 2, "queueStatus": 0, "revision": 16, "createdDate": "2024-05-09T05:02:24.107Z", "project": {"id": "416d2eff-46cd-468f-829e-07d113b877db", "name": "BCI_ESW1_CN_ProjectCollection", "url": "https://dev.azure.com/bosch-bci-cn/_apis/projects/416d2eff-46cd-468f-829e-07d113b877db", "state": 1, "revision": 1490, "visibility": 0, "lastUpdateTime": "2024-04-29T09:23:13.480Z"}}