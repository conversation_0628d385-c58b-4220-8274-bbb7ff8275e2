{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-jsx.js"], "sourcesContent": ["(function (Prism) {\n  var javascript = Prism.util.clone(Prism.languages.javascript);\n  var space = /(?:\\s|\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))\\*\\/)/.source;\n  var braces = /(?:\\{(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])*\\})/.source;\n  var spread = /(?:\\{<S>*\\.{3}(?:[^{}]|<BRACES>)*\\})/.source;\n\n  /**\n   * @param {string} source\n   * @param {string} [flags]\n   */\n  function re(source, flags) {\n    source = source.replace(/<S>/g, function () {\n      return space;\n    }).replace(/<BRACES>/g, function () {\n      return braces;\n    }).replace(/<SPREAD>/g, function () {\n      return spread;\n    });\n    return RegExp(source, flags);\n  }\n  spread = re(spread).source;\n  Prism.languages.jsx = Prism.languages.extend('markup', javascript);\n  Prism.languages.jsx.tag.pattern = re(/<\\/?(?:[\\w.:-]+(?:<S>+(?:[\\w.:$-]+(?:=(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s{'\"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\\/?)?>/.source);\n  Prism.languages.jsx.tag.inside['tag'].pattern = /^<\\/?[^\\s>\\/]*/;\n  Prism.languages.jsx.tag.inside['attr-value'].pattern = /=(?!\\{)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*'|[^\\s'\">]+)/;\n  Prism.languages.jsx.tag.inside['tag'].inside['class-name'] = /^[A-Z]\\w*(?:\\.[A-Z]\\w*)*$/;\n  Prism.languages.jsx.tag.inside['comment'] = javascript['comment'];\n  Prism.languages.insertBefore('inside', 'attr-name', {\n    'spread': {\n      pattern: re(/<SPREAD>/.source),\n      inside: Prism.languages.jsx\n    }\n  }, Prism.languages.jsx.tag);\n  Prism.languages.insertBefore('inside', 'special-attr', {\n    'script': {\n      // Allow for two levels of nesting\n      pattern: re(/=<BRACES>/.source),\n      alias: 'language-javascript',\n      inside: {\n        'script-punctuation': {\n          pattern: /^=(?=\\{)/,\n          alias: 'punctuation'\n        },\n        rest: Prism.languages.jsx\n      }\n    }\n  }, Prism.languages.jsx.tag);\n\n  // The following will handle plain text inside tags\n  var stringifyToken = function (token) {\n    if (!token) {\n      return '';\n    }\n    if (typeof token === 'string') {\n      return token;\n    }\n    if (typeof token.content === 'string') {\n      return token.content;\n    }\n    return token.content.map(stringifyToken).join('');\n  };\n  var walkTokens = function (tokens) {\n    var openedTags = [];\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n      var notTagNorBrace = false;\n      if (typeof token !== 'string') {\n        if (token.type === 'tag' && token.content[0] && token.content[0].type === 'tag') {\n          // We found a tag, now find its kind\n\n          if (token.content[0].content[0].content === '</') {\n            // Closing tag\n            if (openedTags.length > 0 && openedTags[openedTags.length - 1].tagName === stringifyToken(token.content[0].content[1])) {\n              // Pop matching opening tag\n              openedTags.pop();\n            }\n          } else {\n            if (token.content[token.content.length - 1].content === '/>') {\n              // Autoclosed tag, ignore\n            } else {\n              // Opening tag\n              openedTags.push({\n                tagName: stringifyToken(token.content[0].content[1]),\n                openedBraces: 0\n              });\n            }\n          }\n        } else if (openedTags.length > 0 && token.type === 'punctuation' && token.content === '{') {\n          // Here we might have entered a JSX context inside a tag\n          openedTags[openedTags.length - 1].openedBraces++;\n        } else if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces > 0 && token.type === 'punctuation' && token.content === '}') {\n          // Here we might have left a JSX context inside a tag\n          openedTags[openedTags.length - 1].openedBraces--;\n        } else {\n          notTagNorBrace = true;\n        }\n      }\n      if (notTagNorBrace || typeof token === 'string') {\n        if (openedTags.length > 0 && openedTags[openedTags.length - 1].openedBraces === 0) {\n          // Here we are inside a tag, and not inside a JSX context.\n          // That's plain text: drop any tokens matched.\n          var plainText = stringifyToken(token);\n\n          // And merge text with adjacent text\n          if (i < tokens.length - 1 && (typeof tokens[i + 1] === 'string' || tokens[i + 1].type === 'plain-text')) {\n            plainText += stringifyToken(tokens[i + 1]);\n            tokens.splice(i + 1, 1);\n          }\n          if (i > 0 && (typeof tokens[i - 1] === 'string' || tokens[i - 1].type === 'plain-text')) {\n            plainText = stringifyToken(tokens[i - 1]) + plainText;\n            tokens.splice(i - 1, 1);\n            i--;\n          }\n          tokens[i] = new Prism.Token('plain-text', plainText, null, plainText);\n        }\n      }\n      if (token.content && typeof token.content !== 'string') {\n        walkTokens(token.content);\n      }\n    }\n  };\n  Prism.hooks.add('after-tokenize', function (env) {\n    if (env.language !== 'jsx' && env.language !== 'tsx') {\n      return;\n    }\n    walkTokens(env.tokens);\n  });\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAChB,MAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAC5D,MAAI,QAAQ,+CAA+C;AAC3D,MAAI,SAAS,+CAA+C;AAC5D,MAAI,SAAS,uCAAuC;AAMpD,WAAS,GAAG,QAAQ,OAAO;AACzB,aAAS,OAAO,QAAQ,QAAQ,WAAY;AAC1C,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,aAAa,WAAY;AAClC,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,aAAa,WAAY;AAClC,aAAO;AAAA,IACT,CAAC;AACD,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC7B;AACA,WAAS,GAAG,MAAM,EAAE;AACpB,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,UAAU,UAAU;AACjE,EAAAA,OAAM,UAAU,IAAI,IAAI,UAAU,GAAG,wIAAwI,MAAM;AACnL,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,UAAU;AAChD,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,YAAY,EAAE,UAAU;AACvD,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,KAAK,EAAE,OAAO,YAAY,IAAI;AAC7D,EAAAA,OAAM,UAAU,IAAI,IAAI,OAAO,SAAS,IAAI,WAAW,SAAS;AAChE,EAAAA,OAAM,UAAU,aAAa,UAAU,aAAa;AAAA,IAClD,UAAU;AAAA,MACR,SAAS,GAAG,WAAW,MAAM;AAAA,MAC7B,QAAQA,OAAM,UAAU;AAAA,IAC1B;AAAA,EACF,GAAGA,OAAM,UAAU,IAAI,GAAG;AAC1B,EAAAA,OAAM,UAAU,aAAa,UAAU,gBAAgB;AAAA,IACrD,UAAU;AAAA;AAAA,MAER,SAAS,GAAG,YAAY,MAAM;AAAA,MAC9B,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,sBAAsB;AAAA,UACpB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,MAAMA,OAAM,UAAU;AAAA,MACxB;AAAA,IACF;AAAA,EACF,GAAGA,OAAM,UAAU,IAAI,GAAG;AAG1B,MAAI,iBAAiB,SAAU,OAAO;AACpC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,OAAO,MAAM,YAAY,UAAU;AACrC,aAAO,MAAM;AAAA,IACf;AACA,WAAO,MAAM,QAAQ,IAAI,cAAc,EAAE,KAAK,EAAE;AAAA,EAClD;AACA,MAAI,aAAa,SAAU,QAAQ;AACjC,QAAI,aAAa,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,iBAAiB;AACrB,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,SAAS,SAAS,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,EAAE,SAAS,OAAO;AAG/E,cAAI,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,YAAY,MAAM;AAEhD,gBAAI,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,YAAY,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG;AAEtH,yBAAW,IAAI;AAAA,YACjB;AAAA,UACF,OAAO;AACL,gBAAI,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE,YAAY,MAAM;AAAA,YAE9D,OAAO;AAEL,yBAAW,KAAK;AAAA,gBACd,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,gBACnD,cAAc;AAAA,cAChB,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,WAAW,WAAW,SAAS,KAAK,MAAM,SAAS,iBAAiB,MAAM,YAAY,KAAK;AAEzF,qBAAW,WAAW,SAAS,CAAC,EAAE;AAAA,QACpC,WAAW,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,eAAe,KAAK,MAAM,SAAS,iBAAiB,MAAM,YAAY,KAAK;AAE/I,qBAAW,WAAW,SAAS,CAAC,EAAE;AAAA,QACpC,OAAO;AACL,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,UAAI,kBAAkB,OAAO,UAAU,UAAU;AAC/C,YAAI,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC,EAAE,iBAAiB,GAAG;AAGjF,cAAI,YAAY,eAAe,KAAK;AAGpC,cAAI,IAAI,OAAO,SAAS,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,CAAC,EAAE,SAAS,eAAe;AACvG,yBAAa,eAAe,OAAO,IAAI,CAAC,CAAC;AACzC,mBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,UACxB;AACA,cAAI,IAAI,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,YAAY,OAAO,IAAI,CAAC,EAAE,SAAS,eAAe;AACvF,wBAAY,eAAe,OAAO,IAAI,CAAC,CAAC,IAAI;AAC5C,mBAAO,OAAO,IAAI,GAAG,CAAC;AACtB;AAAA,UACF;AACA,iBAAO,CAAC,IAAI,IAAIA,OAAM,MAAM,cAAc,WAAW,MAAM,SAAS;AAAA,QACtE;AAAA,MACF;AACA,UAAI,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACtD,mBAAW,MAAM,OAAO;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,EAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,QAAI,IAAI,aAAa,SAAS,IAAI,aAAa,OAAO;AACpD;AAAA,IACF;AACA,eAAW,IAAI,MAAM;AAAA,EACvB,CAAC;AACH,GAAG,KAAK;", "names": ["Prism"]}