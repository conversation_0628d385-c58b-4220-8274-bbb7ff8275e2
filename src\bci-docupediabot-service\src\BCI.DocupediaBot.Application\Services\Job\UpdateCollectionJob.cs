﻿using BCI.DocupediaBot.Application.Services.Collection;
using BCI.DocupediaBot.Application.Services.Page;
using BCI.DocupediaBot.Domain.Enums;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  [DisallowConcurrentExecution]
  public class UpdateCollectionJob : IJob
  {
    private readonly ICollectionService _collectionService;
    private readonly IPageService _pageService;
    private readonly ILogger<UpdateCollectionJob> _logger;

    public UpdateCollectionJob(
        ICollectionService collectionService,
        IPageService pageService,
        ILogger<UpdateCollectionJob> logger)
    {
      _collectionService = collectionService ?? throw new ArgumentNullException(nameof(collectionService));
      _pageService = pageService ?? throw new ArgumentNullException(nameof(pageService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }


    public async Task Execute(IJobExecutionContext context)
    {
      var collectionId = context.JobDetail.JobDataMap.GetGuidValue("CollectionId");
      var embeddingModel = (EmbeddingModel)context.JobDetail.JobDataMap.Get("EmbeddingModel");

      try
      {
        _logger.LogInformation("Starting update and embedding process for collection {CollectionId}", collectionId);
        var collection = await _collectionService.QueryCollectionWithPagesAsync(collectionId);

        _logger.LogInformation("Updating content for collection {CollectionId}", collectionId);
        foreach (var page in collection.Pages)
        {
          await _pageService.UpdateContentAsync(page, collectionId);
        }

        _logger.LogInformation("Embedding content for collection {CollectionId}", collectionId);
        foreach (var page in collection.Pages)
        {
          await _pageService.EmbedContentAsync(page, collectionId, embeddingModel);
        }

        // Update collection modification time after successful update and embedding
        _logger.LogInformation("Updating modification time for collection {CollectionId}", collectionId);
        await _collectionService.UpdateCollectionModificationTimeAsync(collectionId);

        _logger.LogInformation("Successfully completed update and embedding for collection {CollectionId}", collectionId);
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to execute update and embedding job for collection ID: {CollectionId}", collectionId);
        throw;
      }
    }
  }
}