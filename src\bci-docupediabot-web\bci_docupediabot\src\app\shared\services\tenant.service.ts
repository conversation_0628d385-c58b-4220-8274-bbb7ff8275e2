/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { Injectable } from '@angular/core';
import { AuthenticationService } from '@bci-web-core/auth';
import { EnvironmentUtility } from '@shared/utility/environment.utility';

@Injectable({
  providedIn: 'root',
})
export class TenantService {
  private _tenantId = '';
  constructor(private authenticationService: AuthenticationService) {
    this._tenantId = this.authenticationService.getTenantId();
  }

  get tenantId(): string {
    return this._tenantId;
  }

  get servicePath(): string {
    return `${EnvironmentUtility.DOMAIN_PATH}/${this._tenantId}`;
  }
}
