/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { ApplicationRef, NgModule } from '@angular/core';
import { OidcConfiguration } from '@shared/models';
import {
  AUTH_OPTIONS,
  DefaultMacmaAuthModuleConfig,
  IMacmaAuthModuleConfig,
  MacmaAuthModule,
} from '@bci-web-core/auth';
import { OIDC_INFO } from '@shared/injectors';
import { AppModule } from './app.module';
import { AppComponent } from './app.component';

export function authOptionsFactory(
  oidcConfig: OidcConfiguration
): IMacmaAuthModuleConfig {
  return {
    ...DefaultMacmaAuthModuleConfig,
    debugAuthentication: false,
    clientId: oidcConfig.publicClientId,
    defaultRealm: oidcConfig.tenantId,
    accessControlEndpoint: oidcConfig.serviceUrl,
    autoLogin: true,
    usePopup: false,
    useRefreshToken: true,
    requestUserInfo: true,
  };
}

@NgModule({
  imports: [
    AppModule,
    MacmaAuthModule.forRoot({
      authOptionsProvider: {
        provide: AUTH_OPTIONS,
        useFactory: authOptionsFactory,
        deps: [OIDC_INFO],
      },
    }),
  ],
})
export class AppModuleWithPortal {
  ngDoBootstrap(appRef: ApplicationRef): void {
    appRef.bootstrap(AppComponent);
  }
}
