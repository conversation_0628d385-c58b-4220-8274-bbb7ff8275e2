{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-yaml.js"], "sourcesContent": ["(function (Prism) {\n  // https://yaml.org/spec/1.2/spec.html#c-ns-anchor-property\n  // https://yaml.org/spec/1.2/spec.html#c-ns-alias-node\n  var anchorOrAlias = /[*&][^\\s[\\]{},]+/;\n  // https://yaml.org/spec/1.2/spec.html#c-ns-tag-property\n  var tag = /!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/;\n  // https://yaml.org/spec/1.2/spec.html#c-ns-properties(n,c)\n  var properties = '(?:' + tag.source + '(?:[ \\t]+' + anchorOrAlias.source + ')?|' + anchorOrAlias.source + '(?:[ \\t]+' + tag.source + ')?)';\n  // https://yaml.org/spec/1.2/spec.html#ns-plain(n,c)\n  // This is a simplified version that doesn't support \"#\" and multiline keys\n  // All these long scarry character classes are simplified versions of YAML's characters\n  var plainKey = /(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g, function () {\n    return /[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/.source;\n  });\n  var string = /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source;\n\n  /**\n   *\n   * @param {string} value\n   * @param {string} [flags]\n   * @returns {RegExp}\n   */\n  function createValuePattern(value, flags) {\n    flags = (flags || '').replace(/m/g, '') + 'm'; // add m flag\n    var pattern = /([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source.replace(/<<prop>>/g, function () {\n      return properties;\n    }).replace(/<<value>>/g, function () {\n      return value;\n    });\n    return RegExp(pattern, flags);\n  }\n  Prism.languages.yaml = {\n    'scalar': {\n      pattern: RegExp(/([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source.replace(/<<prop>>/g, function () {\n        return properties;\n      })),\n      lookbehind: true,\n      alias: 'string'\n    },\n    'comment': /#.*/,\n    'key': {\n      pattern: RegExp(/((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source.replace(/<<prop>>/g, function () {\n        return properties;\n      }).replace(/<<key>>/g, function () {\n        return '(?:' + plainKey + '|' + string + ')';\n      })),\n      lookbehind: true,\n      greedy: true,\n      alias: 'atrule'\n    },\n    'directive': {\n      pattern: /(^[ \\t]*)%.+/m,\n      lookbehind: true,\n      alias: 'important'\n    },\n    'datetime': {\n      pattern: createValuePattern(/\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/.source),\n      lookbehind: true,\n      alias: 'number'\n    },\n    'boolean': {\n      pattern: createValuePattern(/false|true/.source, 'i'),\n      lookbehind: true,\n      alias: 'important'\n    },\n    'null': {\n      pattern: createValuePattern(/null|~/.source, 'i'),\n      lookbehind: true,\n      alias: 'important'\n    },\n    'string': {\n      pattern: createValuePattern(string),\n      lookbehind: true,\n      greedy: true\n    },\n    'number': {\n      pattern: createValuePattern(/[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/.source, 'i'),\n      lookbehind: true\n    },\n    'tag': tag,\n    'important': anchorOrAlias,\n    'punctuation': /---|[:[\\]{}\\-,|>?]|\\.\\.\\./\n  };\n  Prism.languages.yml = Prism.languages.yaml;\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAGhB,MAAI,gBAAgB;AAEpB,MAAI,MAAM;AAEV,MAAI,aAAa,QAAQ,IAAI,SAAS,aAAc,cAAc,SAAS,QAAQ,cAAc,SAAS,aAAc,IAAI,SAAS;AAIrI,MAAI,WAAW,kJAAkJ,OAAO,QAAQ,YAAY,WAAY;AACtM,WAAO,2EAA2E;AAAA,EACpF,CAAC;AACD,MAAI,SAAS,8CAA8C;AAQ3D,WAAS,mBAAmB,OAAO,OAAO;AACxC,aAAS,SAAS,IAAI,QAAQ,MAAM,EAAE,IAAI;AAC1C,QAAI,UAAU,yFAAyF,OAAO,QAAQ,aAAa,WAAY;AAC7I,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,cAAc,WAAY;AACnC,aAAO;AAAA,IACT,CAAC;AACD,WAAO,OAAO,SAAS,KAAK;AAAA,EAC9B;AACA,EAAAA,OAAM,UAAU,OAAO;AAAA,IACrB,UAAU;AAAA,MACR,SAAS,OAAO,6FAA6F,OAAO,QAAQ,aAAa,WAAY;AACnJ,eAAO;AAAA,MACT,CAAC,CAAC;AAAA,MACF,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,IACX,OAAO;AAAA,MACL,SAAS,OAAO,kEAAkE,OAAO,QAAQ,aAAa,WAAY;AACxH,eAAO;AAAA,MACT,CAAC,EAAE,QAAQ,YAAY,WAAY;AACjC,eAAO,QAAQ,WAAW,MAAM,SAAS;AAAA,MAC3C,CAAC,CAAC;AAAA,MACF,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,SAAS,mBAAmB,sJAAsJ,MAAM;AAAA,MACxL,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,SAAS,mBAAmB,aAAa,QAAQ,GAAG;AAAA,MACpD,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,mBAAmB,SAAS,QAAQ,GAAG;AAAA,MAChD,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,SAAS,mBAAmB,MAAM;AAAA,MAClC,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,IACA,UAAU;AAAA,MACR,SAAS,mBAAmB,iFAAiF,QAAQ,GAAG;AAAA,MACxH,YAAY;AAAA,IACd;AAAA,IACA,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,EACjB;AACA,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AACxC,GAAG,KAAK;", "names": ["Prism"]}