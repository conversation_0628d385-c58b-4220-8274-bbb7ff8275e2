﻿using BCI.DocupediaBot.Infrastructure.Configuration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace BCI.DocupediaBot.UIService.Controllers
{
  [ApiController]
  [ApiVersion("1", Deprecated = false)]
  [Route("api/v{version:apiVersion}/[Controller]")]
  [Authorize]
  public class OidcConfigurationController : ControllerBase
  {
    private readonly IOptions<OidcConfiguration> oidcConfiguration;
    private readonly IConfiguration configuration;

    public OidcConfigurationController(IOptions<OidcConfiguration> oidcConfiguration, IConfiguration configuration)
    {
      this.oidcConfiguration = oidcConfiguration;
      this.configuration = configuration;
    }

    [HttpGet]
    public ActionResult<OidcConfiguration> GetOidcConfiguration()
    {
      var tenantId = configuration["TENANTCONFIG:Tenant0"];
      var oidcConfig = oidcConfiguration.Value;
      oidcConfig.TenantId = tenantId;

      return Ok(oidcConfig);
    }
  }
}
