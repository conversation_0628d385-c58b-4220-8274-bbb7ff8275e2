using System;
using System.Net.Http;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using BCI.DocupediaBot.Infrastructure.Configuration;
using BCI.DocupediaBot.Application.Contracts.Dtos.Confluence;
using System.Web;

namespace BCI.DocupediaBot.Application.Services.ConfluenceUrl
{
    public class ConfluenceUrlService : IConfluenceUrlService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<ConfluenceUrlService> _logger;

        public ConfluenceUrlService(
            IHttpClientFactory httpClientFactory,
            ILogger<ConfluenceUrlService> logger)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
        }

        public async Task<ConfluencePageInfo> ResolvePageInfoFromUrlAsync(string url, string userToken)
        {
            if (string.IsNullOrWhiteSpace(url) || string.IsNullOrWhiteSpace(userToken))
            {
                throw new ArgumentException("URL and user token are required");
            }

            var urlType = GetUrlType(url);
            var baseUrl = ExtractBaseUrlFromUrl(url);
            _logger.LogInformation("Resolving PageInfo from URL: {Url}, Type: {UrlType}, BaseUrl: {BaseUrl}", url, urlType, baseUrl);

            return urlType switch
            {
                "pageIdUrl" => await ResolvePageInfoFromPageIdUrlAsync(url, userToken, baseUrl),
                "prettyUrl" => await ResolvePageInfoFromPrettyUrlAsync(url, userToken, baseUrl),
                _ => throw new ArgumentException($"Unsupported URL type: {urlType}. Only Pretty URL and PageId URL are supported.")
            };
        }

        public bool IsValidConfluenceUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return false;

            try
            {
                var uri = new Uri(url);
                return uri.Host.Contains("inside-docupedia.bosch.com") &&
                       uri.AbsolutePath.Contains("/confluence");
            }
            catch
            {
                return false;
            }
        }

        public string GetUrlType(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return "unknown";


            if (Regex.IsMatch(url, @"/pages/viewpage\.action\?pageId=\d+"))
                return "pageIdUrl";


            if (Regex.IsMatch(url, @"/display/[^/]+/.+"))
                return "prettyUrl";

            return "unknown";
        }

        public string ExtractBaseUrlFromUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                throw new ArgumentException("URL cannot be null or empty");
            }

            try
            {
                var uri = new Uri(url);
                var path = uri.AbsolutePath;

                // Extract base URL up to /confluence or /confluence2
                var confluenceMatch = Regex.Match(path, @"^(/confluence2?)/");
                if (confluenceMatch.Success)
                {
                    var baseUrl = $"{uri.Scheme}://{uri.Host}{confluenceMatch.Groups[1].Value}";
                    _logger.LogInformation("Extracted BaseUrl: {BaseUrl} from URL: {Url}", baseUrl, url);
                    return baseUrl;
                }

                throw new ArgumentException($"Could not extract Confluence base URL from: {url}");
            }
            catch (UriFormatException ex)
            {
                throw new ArgumentException($"Invalid URL format: {url}", ex);
            }
        }

        private async Task<ConfluencePageInfo> ResolvePageInfoFromPageIdUrlAsync(string url, string userToken, string baseUrl)
        {
            var match = Regex.Match(url, @"pageId=(\d+)");
            if (!match.Success)
            {
                throw new ArgumentException("Could not extract pageId from URL");
            }

            var pageId = match.Groups[1].Value;
            _logger.LogInformation("Extracted PageId: {PageId} from URL", pageId);

            var apiUrl = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.GetContentbyId, pageId)}{ChatbotSettings.Docupedia.DefaultExpands.Basic}";

            using var httpClient = _httpClientFactory.CreateClient("ConfluenceClient");
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", userToken);

            try
            {
                var response = await httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                var responseJson = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Page detail API response received for PageId: {PageId}", pageId);

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                var pageDetail = JsonSerializer.Deserialize<ConfluencePageResult>(responseJson, options);

                if (pageDetail != null && !string.IsNullOrEmpty(pageDetail.Title))
                {
                    return new ConfluencePageInfo
                    {
                        PageId = pageId,
                        Title = pageDetail.Title
                    };
                }

                throw new InvalidOperationException($"Could not retrieve page details for PageId: {pageId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to resolve PageId URL: {Url}", url);
                throw;
            }
        }

        private async Task<ConfluencePageInfo> ResolvePageInfoFromPrettyUrlAsync(string url, string userToken, string baseUrl)
        {

            var match = Regex.Match(url, @"/display/([^/]+)/(.+)");
            if (!match.Success)
            {
                throw new ArgumentException("Invalid pretty URL format");
            }

            var spaceKey = match.Groups[1].Value;
            var pageTitle = Uri.UnescapeDataString(match.Groups[2].Value.Replace("+", " "));

            _logger.LogInformation("Resolving Pretty URL - Space: {SpaceKey}, Title: {PageTitle}", spaceKey, pageTitle);

            var apiUrl = $"{baseUrl}/{ChatbotSettings.Docupedia.ApiVersion}/{string.Format(ChatbotSettings.Docupedia.API.SearchPageBySpaceAndTitle, spaceKey, Uri.EscapeDataString(pageTitle))}";

            using var httpClient = _httpClientFactory.CreateClient("ConfluenceClient");
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", userToken);

            try
            {
                var response = await httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                var responseJson = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Confluence API response: {Response}", responseJson);

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };
                var searchResult = JsonSerializer.Deserialize<ConfluenceSearchResult>(responseJson, options);

                _logger.LogInformation("Deserialized search result: Results count = {Count}", searchResult?.Results?.Length ?? 0);

                if (searchResult?.Results?.Length > 0)
                {
                    var page = searchResult.Results[0];
                    _logger.LogInformation("Found page ID: {PageId}, Title: {Title}", page.Id, page.Title);

                    return new ConfluencePageInfo
                    {
                        PageId = page.Id,
                        Title = page.Title
                    };
                }

                throw new InvalidOperationException($"Page not found: {pageTitle} in space {spaceKey}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to resolve Pretty URL: {Url}", url);
                throw;
            }
        }


    }
}
