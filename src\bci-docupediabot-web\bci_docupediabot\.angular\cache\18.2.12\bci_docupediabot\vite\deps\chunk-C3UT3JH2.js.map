{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/ponyfill-78459bda.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n}\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCFoundation = /** @class */function () {\n  function MDCFoundation(adapter) {\n    if (adapter === void 0) {\n      adapter = {};\n    }\n    this.adapter = adapter;\n  }\n  Object.defineProperty(MDCFoundation, \"cssClasses\", {\n    get: function () {\n      // Classes extending MDCFoundation should implement this method to return an object which exports every\n      // CSS class the foundation class needs as a property. e.g. {ACTIVE: 'mdc-component--active'}\n      return {};\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCFoundation, \"strings\", {\n    get: function () {\n      // Classes extending MDCFoundation should implement this method to return an object which exports all\n      // semantic strings as constants. e.g. {ARIA_ROLE: 'tablist'}\n      return {};\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCFoundation, \"numbers\", {\n    get: function () {\n      // Classes extending MDCFoundation should implement this method to return an object which exports all\n      // of its semantic numbers as constants. e.g. {ANIMATION_DELAY_MS: 350}\n      return {};\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCFoundation, \"defaultAdapter\", {\n    get: function () {\n      // Classes extending MDCFoundation may choose to implement this getter in order to provide a convenient\n      // way of viewing the necessary methods of an adapter. In the future, this could also be used for adapter\n      // validation.\n      return {};\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCFoundation.prototype.init = function () {\n    // Subclasses should override this method to perform initialization routines (registering events, etc.)\n  };\n  MDCFoundation.prototype.destroy = function () {\n    // Subclasses should override this method to perform de-initialization routines (de-registering events, etc.)\n  };\n  return MDCFoundation;\n}();\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCComponent = /** @class */function () {\n  function MDCComponent(root, foundation) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      args[_i - 2] = arguments[_i];\n    }\n    this.root = root;\n    this.initialize.apply(this, __spreadArray([], __read(args)));\n    // Note that we initialize foundation here and not within the constructor's\n    // default param so that this.root is defined and can be used within the\n    // foundation class.\n    this.foundation = foundation === undefined ? this.getDefaultFoundation() : foundation;\n    this.foundation.init();\n    this.initialSyncWithDOM();\n  }\n  MDCComponent.attachTo = function (root) {\n    // Subclasses which extend MDCBase should provide an attachTo() method that takes a root element and\n    // returns an instantiated component with its root set to that element. Also note that in the cases of\n    // subclasses, an explicit foundation class will not have to be passed in; it will simply be initialized\n    // from getDefaultFoundation().\n    return new MDCComponent(root, new MDCFoundation({}));\n  };\n  /* istanbul ignore next: method param only exists for typing purposes; it does not need to be unit tested */\n  MDCComponent.prototype.initialize = function () {\n    var _args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      _args[_i] = arguments[_i];\n    }\n    // Subclasses can override this to do any additional setup work that would be considered part of a\n    // \"constructor\". Essentially, it is a hook into the parent constructor before the foundation is\n    // initialized. Any additional arguments besides root and foundation will be passed in here.\n  };\n  MDCComponent.prototype.getDefaultFoundation = function () {\n    // Subclasses must override this method to return a properly configured foundation class for the\n    // component.\n    throw new Error('Subclasses must override getDefaultFoundation to return a properly configured ' + 'foundation class');\n  };\n  MDCComponent.prototype.initialSyncWithDOM = function () {\n    // Subclasses should override this method if they need to perform work to synchronize with a host DOM\n    // object. An example of this would be a form control wrapper that needs to synchronize its internal state\n    // to some property or attribute of the host DOM. Please note: this is *not* the place to perform DOM\n    // reads/writes that would cause layout / paint, as this is called synchronously from within the constructor.\n  };\n  MDCComponent.prototype.destroy = function () {\n    // Subclasses may implement this method to release any resources / deregister any listeners they have\n    // attached. An example of this might be deregistering a resize event from the window object.\n    this.foundation.destroy();\n  };\n  MDCComponent.prototype.listen = function (evtType, handler, options) {\n    this.root.addEventListener(evtType, handler, options);\n  };\n  MDCComponent.prototype.unlisten = function (evtType, handler, options) {\n    this.root.removeEventListener(evtType, handler, options);\n  };\n  /**\n   * Fires a cross-browser-compatible custom event from the component root of the given type, with the given data.\n   */\n  MDCComponent.prototype.emit = function (evtType, evtData, shouldBubble) {\n    if (shouldBubble === void 0) {\n      shouldBubble = false;\n    }\n    var evt;\n    if (typeof CustomEvent === 'function') {\n      evt = new CustomEvent(evtType, {\n        bubbles: shouldBubble,\n        detail: evtData\n      });\n    } else {\n      evt = document.createEvent('CustomEvent');\n      evt.initCustomEvent(evtType, shouldBubble, false, evtData);\n    }\n    this.root.dispatchEvent(evt);\n  };\n  return MDCComponent;\n}();\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * @fileoverview A \"ponyfill\" is a polyfill that doesn't modify the global prototype chain.\n * This makes ponyfills safer than traditional polyfills, especially for libraries like MDC.\n */\nfunction closest(element, selector) {\n  if (element.closest) {\n    return element.closest(selector);\n  }\n  var el = element;\n  while (el) {\n    if (matches(el, selector)) {\n      return el;\n    }\n    el = el.parentElement;\n  }\n  return null;\n}\nfunction matches(element, selector) {\n  var nativeMatches = element.matches || element.webkitMatchesSelector || element.msMatchesSelector;\n  return nativeMatches.call(element, selector);\n}\n/**\n * Used to compute the estimated scroll width of elements. When an element is\n * hidden due to display: none; being applied to a parent element, the width is\n * returned as 0. However, the element will have a true width once no longer\n * inside a display: none context. This method computes an estimated width when\n * the element is hidden or returns the true width when the element is visble.\n * @param {Element} element the element whose width to estimate\n */\nfunction estimateScrollWidth(element) {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  var htmlEl = element;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n  var clone = htmlEl.cloneNode(true);\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  var scrollWidth = clone.scrollWidth;\n  document.documentElement.removeChild(clone);\n  return scrollWidth;\n}\nexport { MDCFoundation as M, __extends as _, __assign as a, MDCComponent as b, __values as c, __read as d, estimateScrollWidth as e, __spreadArray as f, closest as g, matches as m };\n\n"], "mappings": ";AAiBA,IAAI,gBAAgB,SAAU,GAAG,GAAG;AAClC,kBAAgB,OAAO,kBAAkB;AAAA,IACvC,WAAW,CAAC;AAAA,EACd,aAAa,SAAS,SAAUA,IAAGC,IAAG;AACpC,IAAAD,GAAE,YAAYC;AAAA,EAChB,KAAK,SAAUD,IAAGC,IAAG;AACnB,aAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAC7E;AACA,SAAO,cAAc,GAAG,CAAC;AAC3B;AACA,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,OAAO,MAAM,cAAc,MAAM,KAAM,OAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AACnI,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AACZ,SAAK,cAAc;AAAA,EACrB;AACA,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACpF;AACA,IAAI,WAAW,WAAY;AACzB,aAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC/C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC7E;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AACA,SAAS,SAAS,GAAG;AACnB,MAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAC7C,IAAI,KAAK,EAAE,CAAC,GACZ,IAAI;AACN,MAAI,EAAG,QAAO,EAAE,KAAK,CAAC;AACtB,MAAI,KAAK,OAAO,EAAE,WAAW,SAAU,QAAO;AAAA,IAC5C,MAAM,WAAY;AAChB,UAAI,KAAK,KAAK,EAAE,OAAQ,KAAI;AAC5B,aAAO;AAAA,QACL,OAAO,KAAK,EAAE,GAAG;AAAA,QACjB,MAAM,CAAC;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AACvF;AACA,SAAS,OAAO,GAAG,GAAG;AACpB,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC,EAAG,QAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GACd,GACA,KAAK,CAAC,GACN;AACF,MAAI;AACF,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,EAC3E,SAAS,OAAO;AACd,QAAI;AAAA,MACF;AAAA,IACF;AAAA,EACF,UAAE;AACA,QAAI;AACF,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,IACjD,UAAE;AACA,UAAI,EAAG,OAAM,EAAE;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,cAAc,IAAI,MAAM,MAAM;AACrC,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACnF,QAAI,MAAM,EAAE,KAAK,OAAO;AACtB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;AAwBA,IAAI;AAAA;AAAA,EAA6B,WAAY;AAC3C,aAASC,eAAc,SAAS;AAC9B,UAAI,YAAY,QAAQ;AACtB,kBAAU,CAAC;AAAA,MACb;AACA,WAAK,UAAU;AAAA,IACjB;AACA,WAAO,eAAeA,gBAAe,cAAc;AAAA,MACjD,KAAK,WAAY;AAGf,eAAO,CAAC;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,gBAAe,WAAW;AAAA,MAC9C,KAAK,WAAY;AAGf,eAAO,CAAC;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,gBAAe,WAAW;AAAA,MAC9C,KAAK,WAAY;AAGf,eAAO,CAAC;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,gBAAe,kBAAkB;AAAA,MACrD,KAAK,WAAY;AAIf,eAAO,CAAC;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,eAAc,UAAU,OAAO,WAAY;AAAA,IAE3C;AACA,IAAAA,eAAc,UAAU,UAAU,WAAY;AAAA,IAE9C;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AAwBF,IAAI;AAAA;AAAA,EAA4B,WAAY;AAC1C,aAASC,cAAa,MAAM,YAAY;AACtC,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,aAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,MAC7B;AACA,WAAK,OAAO;AACZ,WAAK,WAAW,MAAM,MAAM,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAI3D,WAAK,aAAa,eAAe,SAAY,KAAK,qBAAqB,IAAI;AAC3E,WAAK,WAAW,KAAK;AACrB,WAAK,mBAAmB;AAAA,IAC1B;AACA,IAAAA,cAAa,WAAW,SAAU,MAAM;AAKtC,aAAO,IAAIA,cAAa,MAAM,IAAI,cAAc,CAAC,CAAC,CAAC;AAAA,IACrD;AAEA,IAAAA,cAAa,UAAU,aAAa,WAAY;AAC9C,UAAI,QAAQ,CAAC;AACb,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,cAAM,EAAE,IAAI,UAAU,EAAE;AAAA,MAC1B;AAAA,IAIF;AACA,IAAAA,cAAa,UAAU,uBAAuB,WAAY;AAGxD,YAAM,IAAI,MAAM,gGAAqG;AAAA,IACvH;AACA,IAAAA,cAAa,UAAU,qBAAqB,WAAY;AAAA,IAKxD;AACA,IAAAA,cAAa,UAAU,UAAU,WAAY;AAG3C,WAAK,WAAW,QAAQ;AAAA,IAC1B;AACA,IAAAA,cAAa,UAAU,SAAS,SAAU,SAAS,SAAS,SAAS;AACnE,WAAK,KAAK,iBAAiB,SAAS,SAAS,OAAO;AAAA,IACtD;AACA,IAAAA,cAAa,UAAU,WAAW,SAAU,SAAS,SAAS,SAAS;AACrE,WAAK,KAAK,oBAAoB,SAAS,SAAS,OAAO;AAAA,IACzD;AAIA,IAAAA,cAAa,UAAU,OAAO,SAAU,SAAS,SAAS,cAAc;AACtE,UAAI,iBAAiB,QAAQ;AAC3B,uBAAe;AAAA,MACjB;AACA,UAAI;AACJ,UAAI,OAAO,gBAAgB,YAAY;AACrC,cAAM,IAAI,YAAY,SAAS;AAAA,UAC7B,SAAS;AAAA,UACT,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,OAAO;AACL,cAAM,SAAS,YAAY,aAAa;AACxC,YAAI,gBAAgB,SAAS,cAAc,OAAO,OAAO;AAAA,MAC3D;AACA,WAAK,KAAK,cAAc,GAAG;AAAA,IAC7B;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AA4BF,SAAS,QAAQ,SAAS,UAAU;AAClC,MAAI,QAAQ,SAAS;AACnB,WAAO,QAAQ,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI,KAAK;AACT,SAAO,IAAI;AACT,QAAI,QAAQ,IAAI,QAAQ,GAAG;AACzB,aAAO;AAAA,IACT;AACA,SAAK,GAAG;AAAA,EACV;AACA,SAAO;AACT;AACA,SAAS,QAAQ,SAAS,UAAU;AAClC,MAAI,gBAAgB,QAAQ,WAAW,QAAQ,yBAAyB,QAAQ;AAChF,SAAO,cAAc,KAAK,SAAS,QAAQ;AAC7C;AASA,SAAS,oBAAoB,SAAS;AAKpC,MAAI,SAAS;AACb,MAAI,OAAO,iBAAiB,MAAM;AAChC,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,QAAQ,OAAO,UAAU,IAAI;AACjC,QAAM,MAAM,YAAY,YAAY,UAAU;AAC9C,QAAM,MAAM,YAAY,aAAa,6BAA6B;AAClE,WAAS,gBAAgB,YAAY,KAAK;AAC1C,MAAI,cAAc,MAAM;AACxB,WAAS,gBAAgB,YAAY,KAAK;AAC1C,SAAO;AACT;", "names": ["d", "b", "__assign", "MDCFoundation", "MDCComponent"]}