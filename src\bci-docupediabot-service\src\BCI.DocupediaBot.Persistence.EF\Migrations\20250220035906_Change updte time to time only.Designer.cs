﻿
using System;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{
    [DbContext(typeof(DocupediaBotDbContext))]
    [Migration("20250220035906_Change updte time to time only")]
    partial class Changeupdtetimetotimeonly
    {

        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Collections.Collection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ChunkSize")
                        .HasColumnType("integer");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("EmbeddingModel")
                        .HasColumnType("integer");

                    b.Property<int?>("IntervalNumber")
                        .HasColumnType("integer");

                    b.Property<bool>("IsAutomaticUpdate")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<TimeOnly?>("UpdateTime")
                        .HasColumnType("time without time zone");

                    b.Property<int?>("UpdateType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Collection", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Contents.Content", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OriginContent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<string>("Path")
                        .HasColumnType("text");

                    b.Property<string>("ProcessedContent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SourceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("SourceModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.Property<int>("VersionNo")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PageId");

                    b.ToTable("Content", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Pages.Page", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CollectionId")
                        .HasColumnType("uuid");

                    b.Property<DateTimeOffset>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTimeOffset>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Page", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Contents.Content", b =>
                {
                    b.HasOne("BCI.DocupediaBot.Domain.Entities.Pages.Page", "Page")
                        .WithMany("Contents")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Page");
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Pages.Page", b =>
                {
                    b.HasOne("BCI.DocupediaBot.Domain.Entities.Collections.Collection", "Collection")
                        .WithMany("Pages")
                        .HasForeignKey("CollectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Collection");
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Collections.Collection", b =>
                {
                    b.Navigation("Pages");
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Pages.Page", b =>
                {
                    b.Navigation("Contents");
                });
#pragma warning restore 612, 618
        }
    }
}
