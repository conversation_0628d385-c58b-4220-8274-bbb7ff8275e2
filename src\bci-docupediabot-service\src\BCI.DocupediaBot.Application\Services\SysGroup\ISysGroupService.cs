﻿using BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.SysGroup
{
  public interface ISysGroupService
  {
    Task<SysGroupResponseDTO?> QueryGroupWithUsersAsync(Guid groupId);
    Task<List<SysGroupResponseDTO>> QueryGroupsAsync();
    Task<List<SysGroupResponseDTO>> QueryAvailableGroupsForCurrentUserAsync();
    Task<ResponseResult> DeleteGroupByGroupIdAsync(Guid groupId);
    Task<ResponseResult> UpdateGroupAsync(SysGroupUpdateDTO dto);
    Task<ResponseResult> AddGroupAsync(SysGroupAddDTO dto);
  }
}