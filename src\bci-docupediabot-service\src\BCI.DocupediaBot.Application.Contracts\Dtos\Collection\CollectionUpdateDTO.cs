﻿using BCI.DocupediaBot.Domain.Enums;
using System;
using System.Collections.Generic;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Collection
{
  public class CollectionUpdateDTO
	{
		public Guid Id { get; set; }
		public string Name { get; set; } = string.Empty;
		public string Comment { get; set; } = string.Empty;
		public EmbeddingModel EmbeddingModel { get; set; }
		public int ChunkSize { get; set; }
		public bool IsAutomaticUpdate { get; set; }
		public UpdateType? UpdateType { get; set; }
		public int? IntervalNumber { get; set; }
		public TimeOnly? UpdateTime { get; set; }
		public List<Guid> GroupIds { get; set; } = new List<Guid>();
	}
}