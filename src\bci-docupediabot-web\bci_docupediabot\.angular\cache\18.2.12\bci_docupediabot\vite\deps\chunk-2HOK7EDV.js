import {
  getCorrectPropertyName
} from "./chunk-TQL2BNQJ.js";
import {
  MDCComponent,
  MDCFoundation,
  __assign,
  __extends,
  __read,
  __spreadArray,
  __values,
  closest,
  matches
} from "./chunk-C3UT3JH2.js";

// node_modules/@bci-web-core/web-components/dist/esm/component-655cd5b3.js
var _a;
var _b;
var cssClasses$2 = {
  LIST_ITEM_ACTIVATED_CLASS: "mdc-list-item--activated",
  LIST_ITEM_CLASS: "mdc-list-item",
  LIST_ITEM_DISABLED_CLASS: "mdc-list-item--disabled",
  LIST_ITEM_SELECTED_CLASS: "mdc-list-item--selected",
  LIST_ITEM_TEXT_CLASS: "mdc-list-item__text",
  LIST_ITEM_PRIMARY_TEXT_CLASS: "mdc-list-item__primary-text",
  ROOT: "mdc-list"
};
var evolutionClassNameMap = (_a = {}, _a["" + cssClasses$2.LIST_ITEM_ACTIVATED_CLASS] = "mdc-list-item--activated", _a["" + cssClasses$2.LIST_ITEM_CLASS] = "mdc-list-item", _a["" + cssClasses$2.LIST_ITEM_DISABLED_CLASS] = "mdc-list-item--disabled", _a["" + cssClasses$2.LIST_ITEM_SELECTED_CLASS] = "mdc-list-item--selected", _a["" + cssClasses$2.LIST_ITEM_PRIMARY_TEXT_CLASS] = "mdc-list-item__primary-text", _a["" + cssClasses$2.ROOT] = "mdc-list", _a);
var deprecatedClassNameMap = (_b = {}, _b["" + cssClasses$2.LIST_ITEM_ACTIVATED_CLASS] = "mdc-deprecated-list-item--activated", _b["" + cssClasses$2.LIST_ITEM_CLASS] = "mdc-deprecated-list-item", _b["" + cssClasses$2.LIST_ITEM_DISABLED_CLASS] = "mdc-deprecated-list-item--disabled", _b["" + cssClasses$2.LIST_ITEM_SELECTED_CLASS] = "mdc-deprecated-list-item--selected", _b["" + cssClasses$2.LIST_ITEM_TEXT_CLASS] = "mdc-deprecated-list-item__text", _b["" + cssClasses$2.LIST_ITEM_PRIMARY_TEXT_CLASS] = "mdc-deprecated-list-item__primary-text", _b["" + cssClasses$2.ROOT] = "mdc-deprecated-list", _b);
var strings$2 = {
  ACTION_EVENT: "MDCList:action",
  SELECTION_CHANGE_EVENT: "MDCList:selectionChange",
  ARIA_CHECKED: "aria-checked",
  ARIA_CHECKED_CHECKBOX_SELECTOR: '[role="checkbox"][aria-checked="true"]',
  ARIA_CHECKED_RADIO_SELECTOR: '[role="radio"][aria-checked="true"]',
  ARIA_CURRENT: "aria-current",
  ARIA_DISABLED: "aria-disabled",
  ARIA_ORIENTATION: "aria-orientation",
  ARIA_ORIENTATION_HORIZONTAL: "horizontal",
  ARIA_ROLE_CHECKBOX_SELECTOR: '[role="checkbox"]',
  ARIA_SELECTED: "aria-selected",
  ARIA_INTERACTIVE_ROLES_SELECTOR: '[role="listbox"], [role="menu"]',
  ARIA_MULTI_SELECTABLE_SELECTOR: '[aria-multiselectable="true"]',
  CHECKBOX_RADIO_SELECTOR: 'input[type="checkbox"], input[type="radio"]',
  CHECKBOX_SELECTOR: 'input[type="checkbox"]',
  CHILD_ELEMENTS_TO_TOGGLE_TABINDEX: "\n    ." + cssClasses$2.LIST_ITEM_CLASS + " button:not(:disabled),\n    ." + cssClasses$2.LIST_ITEM_CLASS + " a,\n    ." + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + " button:not(:disabled),\n    ." + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + " a\n  ",
  DEPRECATED_SELECTOR: ".mdc-deprecated-list",
  FOCUSABLE_CHILD_ELEMENTS: "\n    ." + cssClasses$2.LIST_ITEM_CLASS + " button:not(:disabled),\n    ." + cssClasses$2.LIST_ITEM_CLASS + " a,\n    ." + cssClasses$2.LIST_ITEM_CLASS + ' input[type="radio"]:not(:disabled),\n    .' + cssClasses$2.LIST_ITEM_CLASS + ' input[type="checkbox"]:not(:disabled),\n    .' + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + " button:not(:disabled),\n    ." + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + " a,\n    ." + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + ' input[type="radio"]:not(:disabled),\n    .' + deprecatedClassNameMap[cssClasses$2.LIST_ITEM_CLASS] + ' input[type="checkbox"]:not(:disabled)\n  ',
  RADIO_SELECTOR: 'input[type="radio"]',
  SELECTED_ITEM_SELECTOR: '[aria-selected="true"], [aria-current="true"]'
};
var numbers$2 = {
  UNSET_INDEX: -1,
  TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS: 300
};
var evolutionAttribute = "evolution";
var KEY = {
  UNKNOWN: "Unknown",
  BACKSPACE: "Backspace",
  ENTER: "Enter",
  SPACEBAR: "Spacebar",
  PAGE_UP: "PageUp",
  PAGE_DOWN: "PageDown",
  END: "End",
  HOME: "Home",
  ARROW_LEFT: "ArrowLeft",
  ARROW_UP: "ArrowUp",
  ARROW_RIGHT: "ArrowRight",
  ARROW_DOWN: "ArrowDown",
  DELETE: "Delete",
  ESCAPE: "Escape",
  TAB: "Tab"
};
var normalizedKeys = /* @__PURE__ */ new Set();
normalizedKeys.add(KEY.BACKSPACE);
normalizedKeys.add(KEY.ENTER);
normalizedKeys.add(KEY.SPACEBAR);
normalizedKeys.add(KEY.PAGE_UP);
normalizedKeys.add(KEY.PAGE_DOWN);
normalizedKeys.add(KEY.END);
normalizedKeys.add(KEY.HOME);
normalizedKeys.add(KEY.ARROW_LEFT);
normalizedKeys.add(KEY.ARROW_UP);
normalizedKeys.add(KEY.ARROW_RIGHT);
normalizedKeys.add(KEY.ARROW_DOWN);
normalizedKeys.add(KEY.DELETE);
normalizedKeys.add(KEY.ESCAPE);
normalizedKeys.add(KEY.TAB);
var KEY_CODE = {
  BACKSPACE: 8,
  ENTER: 13,
  SPACEBAR: 32,
  PAGE_UP: 33,
  PAGE_DOWN: 34,
  END: 35,
  HOME: 36,
  ARROW_LEFT: 37,
  ARROW_UP: 38,
  ARROW_RIGHT: 39,
  ARROW_DOWN: 40,
  DELETE: 46,
  ESCAPE: 27,
  TAB: 9
};
var mappedKeyCodes = /* @__PURE__ */ new Map();
mappedKeyCodes.set(KEY_CODE.BACKSPACE, KEY.BACKSPACE);
mappedKeyCodes.set(KEY_CODE.ENTER, KEY.ENTER);
mappedKeyCodes.set(KEY_CODE.SPACEBAR, KEY.SPACEBAR);
mappedKeyCodes.set(KEY_CODE.PAGE_UP, KEY.PAGE_UP);
mappedKeyCodes.set(KEY_CODE.PAGE_DOWN, KEY.PAGE_DOWN);
mappedKeyCodes.set(KEY_CODE.END, KEY.END);
mappedKeyCodes.set(KEY_CODE.HOME, KEY.HOME);
mappedKeyCodes.set(KEY_CODE.ARROW_LEFT, KEY.ARROW_LEFT);
mappedKeyCodes.set(KEY_CODE.ARROW_UP, KEY.ARROW_UP);
mappedKeyCodes.set(KEY_CODE.ARROW_RIGHT, KEY.ARROW_RIGHT);
mappedKeyCodes.set(KEY_CODE.ARROW_DOWN, KEY.ARROW_DOWN);
mappedKeyCodes.set(KEY_CODE.DELETE, KEY.DELETE);
mappedKeyCodes.set(KEY_CODE.ESCAPE, KEY.ESCAPE);
mappedKeyCodes.set(KEY_CODE.TAB, KEY.TAB);
var navigationKeys = /* @__PURE__ */ new Set();
navigationKeys.add(KEY.PAGE_UP);
navigationKeys.add(KEY.PAGE_DOWN);
navigationKeys.add(KEY.END);
navigationKeys.add(KEY.HOME);
navigationKeys.add(KEY.ARROW_LEFT);
navigationKeys.add(KEY.ARROW_UP);
navigationKeys.add(KEY.ARROW_RIGHT);
navigationKeys.add(KEY.ARROW_DOWN);
function normalizeKey(evt) {
  var key = evt.key;
  if (normalizedKeys.has(key)) {
    return key;
  }
  var mappedKey = mappedKeyCodes.get(evt.keyCode);
  if (mappedKey) {
    return mappedKey;
  }
  return KEY.UNKNOWN;
}
var ELEMENTS_KEY_ALLOWED_IN = ["input", "button", "textarea", "select"];
var preventDefaultEvent = function(evt) {
  var target = evt.target;
  if (!target) {
    return;
  }
  var tagName = ("" + target.tagName).toLowerCase();
  if (ELEMENTS_KEY_ALLOWED_IN.indexOf(tagName) === -1) {
    evt.preventDefault();
  }
};
function initState() {
  var state = {
    bufferClearTimeout: 0,
    currentFirstChar: "",
    sortedIndexCursor: 0,
    typeaheadBuffer: ""
  };
  return state;
}
function initSortedIndex(listItemCount, getPrimaryTextByItemIndex) {
  var sortedIndexByFirstChar = /* @__PURE__ */ new Map();
  for (var i = 0; i < listItemCount; i++) {
    var primaryText = getPrimaryTextByItemIndex(i).trim();
    if (!primaryText) {
      continue;
    }
    var firstChar = primaryText[0].toLowerCase();
    if (!sortedIndexByFirstChar.has(firstChar)) {
      sortedIndexByFirstChar.set(firstChar, []);
    }
    sortedIndexByFirstChar.get(firstChar).push({
      text: primaryText.toLowerCase(),
      index: i
    });
  }
  sortedIndexByFirstChar.forEach(function(values) {
    values.sort(function(first, second) {
      return first.index - second.index;
    });
  });
  return sortedIndexByFirstChar;
}
function matchItem(opts, state) {
  var nextChar = opts.nextChar, focusItemAtIndex = opts.focusItemAtIndex, sortedIndexByFirstChar = opts.sortedIndexByFirstChar, focusedItemIndex = opts.focusedItemIndex, skipFocus = opts.skipFocus, isItemAtIndexDisabled = opts.isItemAtIndexDisabled;
  clearTimeout(state.bufferClearTimeout);
  state.bufferClearTimeout = setTimeout(function() {
    clearBuffer(state);
  }, numbers$2.TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS);
  state.typeaheadBuffer = state.typeaheadBuffer + nextChar;
  var index;
  if (state.typeaheadBuffer.length === 1) {
    index = matchFirstChar(sortedIndexByFirstChar, focusedItemIndex, isItemAtIndexDisabled, state);
  } else {
    index = matchAllChars(sortedIndexByFirstChar, isItemAtIndexDisabled, state);
  }
  if (index !== -1 && !skipFocus) {
    focusItemAtIndex(index);
  }
  return index;
}
function matchFirstChar(sortedIndexByFirstChar, focusedItemIndex, isItemAtIndexDisabled, state) {
  var firstChar = state.typeaheadBuffer[0];
  var itemsMatchingFirstChar = sortedIndexByFirstChar.get(firstChar);
  if (!itemsMatchingFirstChar) {
    return -1;
  }
  if (firstChar === state.currentFirstChar && itemsMatchingFirstChar[state.sortedIndexCursor].index === focusedItemIndex) {
    state.sortedIndexCursor = (state.sortedIndexCursor + 1) % itemsMatchingFirstChar.length;
    var newIndex = itemsMatchingFirstChar[state.sortedIndexCursor].index;
    if (!isItemAtIndexDisabled(newIndex)) {
      return newIndex;
    }
  }
  state.currentFirstChar = firstChar;
  var newCursorPosition = -1;
  var cursorPosition;
  for (cursorPosition = 0; cursorPosition < itemsMatchingFirstChar.length; cursorPosition++) {
    if (!isItemAtIndexDisabled(itemsMatchingFirstChar[cursorPosition].index)) {
      newCursorPosition = cursorPosition;
      break;
    }
  }
  for (; cursorPosition < itemsMatchingFirstChar.length; cursorPosition++) {
    if (itemsMatchingFirstChar[cursorPosition].index > focusedItemIndex && !isItemAtIndexDisabled(itemsMatchingFirstChar[cursorPosition].index)) {
      newCursorPosition = cursorPosition;
      break;
    }
  }
  if (newCursorPosition !== -1) {
    state.sortedIndexCursor = newCursorPosition;
    return itemsMatchingFirstChar[state.sortedIndexCursor].index;
  }
  return -1;
}
function matchAllChars(sortedIndexByFirstChar, isItemAtIndexDisabled, state) {
  var firstChar = state.typeaheadBuffer[0];
  var itemsMatchingFirstChar = sortedIndexByFirstChar.get(firstChar);
  if (!itemsMatchingFirstChar) {
    return -1;
  }
  var startingItem = itemsMatchingFirstChar[state.sortedIndexCursor];
  if (startingItem.text.lastIndexOf(state.typeaheadBuffer, 0) === 0 && !isItemAtIndexDisabled(startingItem.index)) {
    return startingItem.index;
  }
  var cursorPosition = (state.sortedIndexCursor + 1) % itemsMatchingFirstChar.length;
  var nextCursorPosition = -1;
  while (cursorPosition !== state.sortedIndexCursor) {
    var currentItem = itemsMatchingFirstChar[cursorPosition];
    var matches2 = currentItem.text.lastIndexOf(state.typeaheadBuffer, 0) === 0;
    var isEnabled = !isItemAtIndexDisabled(currentItem.index);
    if (matches2 && isEnabled) {
      nextCursorPosition = cursorPosition;
      break;
    }
    cursorPosition = (cursorPosition + 1) % itemsMatchingFirstChar.length;
  }
  if (nextCursorPosition !== -1) {
    state.sortedIndexCursor = nextCursorPosition;
    return itemsMatchingFirstChar[state.sortedIndexCursor].index;
  }
  return -1;
}
function isTypingInProgress(state) {
  return state.typeaheadBuffer.length > 0;
}
function clearBuffer(state) {
  state.typeaheadBuffer = "";
}
function handleKeydown(opts, state) {
  var event = opts.event, isTargetListItem = opts.isTargetListItem, focusedItemIndex = opts.focusedItemIndex, focusItemAtIndex = opts.focusItemAtIndex, sortedIndexByFirstChar = opts.sortedIndexByFirstChar, isItemAtIndexDisabled = opts.isItemAtIndexDisabled;
  var isArrowLeft = normalizeKey(event) === "ArrowLeft";
  var isArrowUp = normalizeKey(event) === "ArrowUp";
  var isArrowRight = normalizeKey(event) === "ArrowRight";
  var isArrowDown = normalizeKey(event) === "ArrowDown";
  var isHome = normalizeKey(event) === "Home";
  var isEnd = normalizeKey(event) === "End";
  var isEnter = normalizeKey(event) === "Enter";
  var isSpace = normalizeKey(event) === "Spacebar";
  if (event.altKey || event.ctrlKey || event.metaKey || isArrowLeft || isArrowUp || isArrowRight || isArrowDown || isHome || isEnd || isEnter) {
    return -1;
  }
  var isCharacterKey = !isSpace && event.key.length === 1;
  if (isCharacterKey) {
    preventDefaultEvent(event);
    var matchItemOpts = {
      focusItemAtIndex,
      focusedItemIndex,
      nextChar: event.key.toLowerCase(),
      sortedIndexByFirstChar,
      skipFocus: false,
      isItemAtIndexDisabled
    };
    return matchItem(matchItemOpts, state);
  }
  if (!isSpace) {
    return -1;
  }
  if (isTargetListItem) {
    preventDefaultEvent(event);
  }
  var typeaheadOnListItem = isTargetListItem && isTypingInProgress(state);
  if (typeaheadOnListItem) {
    var matchItemOpts = {
      focusItemAtIndex,
      focusedItemIndex,
      nextChar: " ",
      sortedIndexByFirstChar,
      skipFocus: false,
      isItemAtIndexDisabled
    };
    return matchItem(matchItemOpts, state);
  }
  return -1;
}
function isNumberArray(selectedIndex) {
  return selectedIndex instanceof Array;
}
var handledModifierKeys = ["Alt", "Control", "Meta", "Shift"];
function createModifierChecker(event) {
  var eventModifiers = new Set(event ? handledModifierKeys.filter(function(m) {
    return event.getModifierState(m);
  }) : []);
  return function(modifiers) {
    return modifiers.every(function(m) {
      return eventModifiers.has(m);
    }) && modifiers.length === eventModifiers.size;
  };
}
var MDCListFoundation = (
  /** @class */
  function(_super) {
    __extends(MDCListFoundation2, _super);
    function MDCListFoundation2(adapter) {
      var _this = _super.call(this, __assign(__assign({}, MDCListFoundation2.defaultAdapter), adapter)) || this;
      _this.wrapFocus = false;
      _this.isVertical = true;
      _this.isSingleSelectionList = false;
      _this.areDisabledItemsFocusable = true;
      _this.selectedIndex = numbers$2.UNSET_INDEX;
      _this.focusedItemIndex = numbers$2.UNSET_INDEX;
      _this.useActivatedClass = false;
      _this.useSelectedAttr = false;
      _this.ariaCurrentAttrValue = null;
      _this.isCheckboxList = false;
      _this.isRadioList = false;
      _this.lastSelectedIndex = null;
      _this.hasTypeahead = false;
      _this.typeaheadState = initState();
      _this.sortedIndexByFirstChar = /* @__PURE__ */ new Map();
      return _this;
    }
    Object.defineProperty(MDCListFoundation2, "strings", {
      get: function() {
        return strings$2;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCListFoundation2, "cssClasses", {
      get: function() {
        return cssClasses$2;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCListFoundation2, "numbers", {
      get: function() {
        return numbers$2;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCListFoundation2, "defaultAdapter", {
      get: function() {
        return {
          addClassForElementIndex: function() {
            return void 0;
          },
          focusItemAtIndex: function() {
            return void 0;
          },
          getAttributeForElementIndex: function() {
            return null;
          },
          getFocusedElementIndex: function() {
            return 0;
          },
          getListItemCount: function() {
            return 0;
          },
          hasCheckboxAtIndex: function() {
            return false;
          },
          hasRadioAtIndex: function() {
            return false;
          },
          isCheckboxCheckedAtIndex: function() {
            return false;
          },
          isFocusInsideList: function() {
            return false;
          },
          isRootFocused: function() {
            return false;
          },
          listItemAtIndexHasClass: function() {
            return false;
          },
          notifyAction: function() {
            return void 0;
          },
          notifySelectionChange: function() {
          },
          removeClassForElementIndex: function() {
            return void 0;
          },
          setAttributeForElementIndex: function() {
            return void 0;
          },
          setCheckedCheckboxOrRadioAtIndex: function() {
            return void 0;
          },
          setTabIndexForListItemChildren: function() {
            return void 0;
          },
          getPrimaryTextAtIndex: function() {
            return "";
          }
        };
      },
      enumerable: false,
      configurable: true
    });
    MDCListFoundation2.prototype.layout = function() {
      if (this.adapter.getListItemCount() === 0) {
        return;
      }
      if (this.adapter.hasCheckboxAtIndex(0)) {
        this.isCheckboxList = true;
      } else if (this.adapter.hasRadioAtIndex(0)) {
        this.isRadioList = true;
      } else {
        this.maybeInitializeSingleSelection();
      }
      if (this.hasTypeahead) {
        this.sortedIndexByFirstChar = this.typeaheadInitSortedIndex();
      }
    };
    MDCListFoundation2.prototype.getFocusedItemIndex = function() {
      return this.focusedItemIndex;
    };
    MDCListFoundation2.prototype.setWrapFocus = function(value) {
      this.wrapFocus = value;
    };
    MDCListFoundation2.prototype.setVerticalOrientation = function(value) {
      this.isVertical = value;
    };
    MDCListFoundation2.prototype.setSingleSelection = function(value) {
      this.isSingleSelectionList = value;
      if (value) {
        this.maybeInitializeSingleSelection();
        this.selectedIndex = this.getSelectedIndexFromDOM();
      }
    };
    MDCListFoundation2.prototype.setDisabledItemsFocusable = function(value) {
      this.areDisabledItemsFocusable = value;
    };
    MDCListFoundation2.prototype.maybeInitializeSingleSelection = function() {
      var selectedItemIndex = this.getSelectedIndexFromDOM();
      if (selectedItemIndex === numbers$2.UNSET_INDEX) return;
      var hasActivatedClass = this.adapter.listItemAtIndexHasClass(selectedItemIndex, cssClasses$2.LIST_ITEM_ACTIVATED_CLASS);
      if (hasActivatedClass) {
        this.setUseActivatedClass(true);
      }
      this.isSingleSelectionList = true;
      this.selectedIndex = selectedItemIndex;
    };
    MDCListFoundation2.prototype.getSelectedIndexFromDOM = function() {
      var selectedIndex = numbers$2.UNSET_INDEX;
      var listItemsCount = this.adapter.getListItemCount();
      for (var i = 0; i < listItemsCount; i++) {
        var hasSelectedClass = this.adapter.listItemAtIndexHasClass(i, cssClasses$2.LIST_ITEM_SELECTED_CLASS);
        var hasActivatedClass = this.adapter.listItemAtIndexHasClass(i, cssClasses$2.LIST_ITEM_ACTIVATED_CLASS);
        if (!(hasSelectedClass || hasActivatedClass)) {
          continue;
        }
        selectedIndex = i;
        break;
      }
      return selectedIndex;
    };
    MDCListFoundation2.prototype.setHasTypeahead = function(hasTypeahead) {
      this.hasTypeahead = hasTypeahead;
      if (hasTypeahead) {
        this.sortedIndexByFirstChar = this.typeaheadInitSortedIndex();
      }
    };
    MDCListFoundation2.prototype.isTypeaheadInProgress = function() {
      return this.hasTypeahead && isTypingInProgress(this.typeaheadState);
    };
    MDCListFoundation2.prototype.setUseActivatedClass = function(useActivated) {
      this.useActivatedClass = useActivated;
    };
    MDCListFoundation2.prototype.setUseSelectedAttribute = function(useSelected) {
      this.useSelectedAttr = useSelected;
    };
    MDCListFoundation2.prototype.getSelectedIndex = function() {
      return this.selectedIndex;
    };
    MDCListFoundation2.prototype.setSelectedIndex = function(index, options) {
      if (options === void 0) {
        options = {};
      }
      if (!this.isIndexValid(index)) {
        return;
      }
      if (this.isCheckboxList) {
        this.setCheckboxAtIndex(index, options);
      } else if (this.isRadioList) {
        this.setRadioAtIndex(index, options);
      } else {
        this.setSingleSelectionAtIndex(index, options);
      }
    };
    MDCListFoundation2.prototype.handleFocusIn = function(listItemIndex) {
      if (listItemIndex >= 0) {
        this.focusedItemIndex = listItemIndex;
        this.adapter.setAttributeForElementIndex(listItemIndex, "tabindex", "0");
        this.adapter.setTabIndexForListItemChildren(listItemIndex, "0");
      }
    };
    MDCListFoundation2.prototype.handleFocusOut = function(listItemIndex) {
      var _this = this;
      if (listItemIndex >= 0) {
        this.adapter.setAttributeForElementIndex(listItemIndex, "tabindex", "-1");
        this.adapter.setTabIndexForListItemChildren(listItemIndex, "-1");
      }
      setTimeout(function() {
        if (!_this.adapter.isFocusInsideList()) {
          _this.setTabindexToFirstSelectedOrFocusedItem();
        }
      }, 0);
    };
    MDCListFoundation2.prototype.isIndexDisabled = function(index) {
      return this.adapter.listItemAtIndexHasClass(index, cssClasses$2.LIST_ITEM_DISABLED_CLASS);
    };
    MDCListFoundation2.prototype.handleKeydown = function(event, isRootListItem, listItemIndex) {
      var _this = this;
      var _a2;
      var isArrowLeft = normalizeKey(event) === "ArrowLeft";
      var isArrowUp = normalizeKey(event) === "ArrowUp";
      var isArrowRight = normalizeKey(event) === "ArrowRight";
      var isArrowDown = normalizeKey(event) === "ArrowDown";
      var isHome = normalizeKey(event) === "Home";
      var isEnd = normalizeKey(event) === "End";
      var isEnter = normalizeKey(event) === "Enter";
      var isSpace = normalizeKey(event) === "Spacebar";
      var isForward = this.isVertical && isArrowDown || !this.isVertical && isArrowRight;
      var isBack = this.isVertical && isArrowUp || !this.isVertical && isArrowLeft;
      var isLetterA = event.key === "A" || event.key === "a";
      var eventHasModifiers = createModifierChecker(event);
      if (this.adapter.isRootFocused()) {
        if ((isBack || isEnd) && eventHasModifiers([])) {
          event.preventDefault();
          this.focusLastElement();
        } else if ((isForward || isHome) && eventHasModifiers([])) {
          event.preventDefault();
          this.focusFirstElement();
        } else if (isBack && eventHasModifiers(["Shift"]) && this.isCheckboxList) {
          event.preventDefault();
          var focusedIndex = this.focusLastElement();
          if (focusedIndex !== -1) {
            this.setSelectedIndexOnAction(focusedIndex, false);
          }
        } else if (isForward && eventHasModifiers(["Shift"]) && this.isCheckboxList) {
          event.preventDefault();
          var focusedIndex = this.focusFirstElement();
          if (focusedIndex !== -1) {
            this.setSelectedIndexOnAction(focusedIndex, false);
          }
        }
        if (this.hasTypeahead) {
          var handleKeydownOpts = {
            event,
            focusItemAtIndex: function(index) {
              _this.focusItemAtIndex(index);
            },
            focusedItemIndex: -1,
            isTargetListItem: isRootListItem,
            sortedIndexByFirstChar: this.sortedIndexByFirstChar,
            isItemAtIndexDisabled: function(index) {
              return _this.isIndexDisabled(index);
            }
          };
          handleKeydown(handleKeydownOpts, this.typeaheadState);
        }
        return;
      }
      var currentIndex = this.adapter.getFocusedElementIndex();
      if (currentIndex === -1) {
        currentIndex = listItemIndex;
        if (currentIndex < 0) {
          return;
        }
      }
      if (isForward && eventHasModifiers([])) {
        preventDefaultEvent(event);
        this.focusNextElement(currentIndex);
      } else if (isBack && eventHasModifiers([])) {
        preventDefaultEvent(event);
        this.focusPrevElement(currentIndex);
      } else if (isForward && eventHasModifiers(["Shift"]) && this.isCheckboxList) {
        preventDefaultEvent(event);
        var focusedIndex = this.focusNextElement(currentIndex);
        if (focusedIndex !== -1) {
          this.setSelectedIndexOnAction(focusedIndex, false);
        }
      } else if (isBack && eventHasModifiers(["Shift"]) && this.isCheckboxList) {
        preventDefaultEvent(event);
        var focusedIndex = this.focusPrevElement(currentIndex);
        if (focusedIndex !== -1) {
          this.setSelectedIndexOnAction(focusedIndex, false);
        }
      } else if (isHome && eventHasModifiers([])) {
        preventDefaultEvent(event);
        this.focusFirstElement();
      } else if (isEnd && eventHasModifiers([])) {
        preventDefaultEvent(event);
        this.focusLastElement();
      } else if (isHome && eventHasModifiers(["Control", "Shift"]) && this.isCheckboxList) {
        preventDefaultEvent(event);
        if (this.isIndexDisabled(currentIndex)) {
          return;
        }
        this.focusFirstElement();
        this.toggleCheckboxRange(0, currentIndex, currentIndex);
      } else if (isEnd && eventHasModifiers(["Control", "Shift"]) && this.isCheckboxList) {
        preventDefaultEvent(event);
        if (this.isIndexDisabled(currentIndex)) {
          return;
        }
        this.focusLastElement();
        this.toggleCheckboxRange(currentIndex, this.adapter.getListItemCount() - 1, currentIndex);
      } else if (isLetterA && eventHasModifiers(["Control"]) && this.isCheckboxList) {
        event.preventDefault();
        this.checkboxListToggleAll(this.selectedIndex === numbers$2.UNSET_INDEX ? [] : this.selectedIndex, true);
      } else if ((isEnter || isSpace) && eventHasModifiers([])) {
        if (isRootListItem) {
          var target = event.target;
          if (target && target.tagName === "A" && isEnter) {
            return;
          }
          preventDefaultEvent(event);
          if (this.isIndexDisabled(currentIndex)) {
            return;
          }
          if (!this.isTypeaheadInProgress()) {
            if (this.isSelectableList()) {
              this.setSelectedIndexOnAction(currentIndex, false);
            }
            this.adapter.notifyAction(currentIndex);
          }
        }
      } else if ((isEnter || isSpace) && eventHasModifiers(["Shift"]) && this.isCheckboxList) {
        var target = event.target;
        if (target && target.tagName === "A" && isEnter) {
          return;
        }
        preventDefaultEvent(event);
        if (this.isIndexDisabled(currentIndex)) {
          return;
        }
        if (!this.isTypeaheadInProgress()) {
          this.toggleCheckboxRange((_a2 = this.lastSelectedIndex) !== null && _a2 !== void 0 ? _a2 : currentIndex, currentIndex, currentIndex);
          this.adapter.notifyAction(currentIndex);
        }
      }
      if (this.hasTypeahead) {
        var handleKeydownOpts = {
          event,
          focusItemAtIndex: function(index) {
            _this.focusItemAtIndex(index);
          },
          focusedItemIndex: this.focusedItemIndex,
          isTargetListItem: isRootListItem,
          sortedIndexByFirstChar: this.sortedIndexByFirstChar,
          isItemAtIndexDisabled: function(index) {
            return _this.isIndexDisabled(index);
          }
        };
        handleKeydown(handleKeydownOpts, this.typeaheadState);
      }
    };
    MDCListFoundation2.prototype.handleClick = function(index, isCheckboxAlreadyUpdatedInAdapter, event) {
      var _a2;
      var eventHasModifiers = createModifierChecker(event);
      if (index === numbers$2.UNSET_INDEX) {
        return;
      }
      if (this.isIndexDisabled(index)) {
        return;
      }
      if (eventHasModifiers([])) {
        if (this.isSelectableList()) {
          this.setSelectedIndexOnAction(index, isCheckboxAlreadyUpdatedInAdapter);
        }
        this.adapter.notifyAction(index);
      } else if (this.isCheckboxList && eventHasModifiers(["Shift"])) {
        this.toggleCheckboxRange((_a2 = this.lastSelectedIndex) !== null && _a2 !== void 0 ? _a2 : index, index, index);
        this.adapter.notifyAction(index);
      }
    };
    MDCListFoundation2.prototype.focusNextElement = function(index) {
      var count = this.adapter.getListItemCount();
      var nextIndex = index;
      var firstChecked = null;
      do {
        nextIndex++;
        if (nextIndex >= count) {
          if (this.wrapFocus) {
            nextIndex = 0;
          } else {
            return index;
          }
        }
        if (nextIndex === firstChecked) {
          return -1;
        }
        firstChecked = firstChecked !== null && firstChecked !== void 0 ? firstChecked : nextIndex;
      } while (!this.areDisabledItemsFocusable && this.isIndexDisabled(nextIndex));
      this.focusItemAtIndex(nextIndex);
      return nextIndex;
    };
    MDCListFoundation2.prototype.focusPrevElement = function(index) {
      var count = this.adapter.getListItemCount();
      var prevIndex = index;
      var firstChecked = null;
      do {
        prevIndex--;
        if (prevIndex < 0) {
          if (this.wrapFocus) {
            prevIndex = count - 1;
          } else {
            return index;
          }
        }
        if (prevIndex === firstChecked) {
          return -1;
        }
        firstChecked = firstChecked !== null && firstChecked !== void 0 ? firstChecked : prevIndex;
      } while (!this.areDisabledItemsFocusable && this.isIndexDisabled(prevIndex));
      this.focusItemAtIndex(prevIndex);
      return prevIndex;
    };
    MDCListFoundation2.prototype.focusFirstElement = function() {
      return this.focusNextElement(-1);
    };
    MDCListFoundation2.prototype.focusLastElement = function() {
      return this.focusPrevElement(this.adapter.getListItemCount());
    };
    MDCListFoundation2.prototype.focusInitialElement = function() {
      var initialIndex = this.getFirstSelectedOrFocusedItemIndex();
      this.focusItemAtIndex(initialIndex);
      return initialIndex;
    };
    MDCListFoundation2.prototype.setEnabled = function(itemIndex, isEnabled) {
      if (!this.isIndexValid(itemIndex, false)) {
        return;
      }
      if (isEnabled) {
        this.adapter.removeClassForElementIndex(itemIndex, cssClasses$2.LIST_ITEM_DISABLED_CLASS);
        this.adapter.setAttributeForElementIndex(itemIndex, strings$2.ARIA_DISABLED, "false");
      } else {
        this.adapter.addClassForElementIndex(itemIndex, cssClasses$2.LIST_ITEM_DISABLED_CLASS);
        this.adapter.setAttributeForElementIndex(itemIndex, strings$2.ARIA_DISABLED, "true");
      }
    };
    MDCListFoundation2.prototype.setSingleSelectionAtIndex = function(index, options) {
      if (options === void 0) {
        options = {};
      }
      if (this.selectedIndex === index && !options.forceUpdate) {
        return;
      }
      var selectedClassName = cssClasses$2.LIST_ITEM_SELECTED_CLASS;
      if (this.useActivatedClass) {
        selectedClassName = cssClasses$2.LIST_ITEM_ACTIVATED_CLASS;
      }
      if (this.selectedIndex !== numbers$2.UNSET_INDEX) {
        this.adapter.removeClassForElementIndex(this.selectedIndex, selectedClassName);
      }
      this.setAriaForSingleSelectionAtIndex(index);
      this.setTabindexAtIndex(index);
      if (index !== numbers$2.UNSET_INDEX) {
        this.adapter.addClassForElementIndex(index, selectedClassName);
      }
      this.selectedIndex = index;
      if (options.isUserInteraction && !options.forceUpdate) {
        this.adapter.notifySelectionChange([index]);
      }
    };
    MDCListFoundation2.prototype.setAriaForSingleSelectionAtIndex = function(index) {
      if (this.selectedIndex === numbers$2.UNSET_INDEX) {
        this.ariaCurrentAttrValue = this.adapter.getAttributeForElementIndex(index, strings$2.ARIA_CURRENT);
      }
      var isAriaCurrent = this.ariaCurrentAttrValue !== null;
      var ariaAttribute = isAriaCurrent ? strings$2.ARIA_CURRENT : strings$2.ARIA_SELECTED;
      if (this.selectedIndex !== numbers$2.UNSET_INDEX) {
        this.adapter.setAttributeForElementIndex(this.selectedIndex, ariaAttribute, "false");
      }
      if (index !== numbers$2.UNSET_INDEX) {
        var ariaAttributeValue = isAriaCurrent ? this.ariaCurrentAttrValue : "true";
        this.adapter.setAttributeForElementIndex(index, ariaAttribute, ariaAttributeValue);
      }
    };
    MDCListFoundation2.prototype.getSelectionAttribute = function() {
      return this.useSelectedAttr ? strings$2.ARIA_SELECTED : strings$2.ARIA_CHECKED;
    };
    MDCListFoundation2.prototype.setRadioAtIndex = function(index, options) {
      if (options === void 0) {
        options = {};
      }
      var selectionAttribute = this.getSelectionAttribute();
      this.adapter.setCheckedCheckboxOrRadioAtIndex(index, true);
      if (this.selectedIndex === index && !options.forceUpdate) {
        return;
      }
      if (this.selectedIndex !== numbers$2.UNSET_INDEX) {
        this.adapter.setAttributeForElementIndex(this.selectedIndex, selectionAttribute, "false");
      }
      this.adapter.setAttributeForElementIndex(index, selectionAttribute, "true");
      this.selectedIndex = index;
      if (options.isUserInteraction && !options.forceUpdate) {
        this.adapter.notifySelectionChange([index]);
      }
    };
    MDCListFoundation2.prototype.setCheckboxAtIndex = function(index, options) {
      if (options === void 0) {
        options = {};
      }
      var currentIndex = this.selectedIndex;
      var currentlySelected = options.isUserInteraction ? new Set(currentIndex === numbers$2.UNSET_INDEX ? [] : currentIndex) : null;
      var selectionAttribute = this.getSelectionAttribute();
      var changedIndices = [];
      for (var i = 0; i < this.adapter.getListItemCount(); i++) {
        var previousIsChecked = currentlySelected === null || currentlySelected === void 0 ? void 0 : currentlySelected.has(i);
        var newIsChecked = index.indexOf(i) >= 0;
        if (newIsChecked !== previousIsChecked) {
          changedIndices.push(i);
        }
        this.adapter.setCheckedCheckboxOrRadioAtIndex(i, newIsChecked);
        this.adapter.setAttributeForElementIndex(i, selectionAttribute, newIsChecked ? "true" : "false");
      }
      this.selectedIndex = index;
      if (options.isUserInteraction && changedIndices.length) {
        this.adapter.notifySelectionChange(changedIndices);
      }
    };
    MDCListFoundation2.prototype.toggleCheckboxRange = function(fromIndex, toIndex, toggleIndex) {
      this.lastSelectedIndex = toggleIndex;
      var currentlySelected = new Set(this.selectedIndex === numbers$2.UNSET_INDEX ? [] : this.selectedIndex);
      var newIsChecked = !(currentlySelected === null || currentlySelected === void 0 ? void 0 : currentlySelected.has(toggleIndex));
      var _a2 = __read([fromIndex, toIndex].sort(), 2), startIndex = _a2[0], endIndex = _a2[1];
      var selectionAttribute = this.getSelectionAttribute();
      var changedIndices = [];
      for (var i = startIndex; i <= endIndex; i++) {
        if (this.isIndexDisabled(i)) {
          continue;
        }
        var previousIsChecked = currentlySelected.has(i);
        if (newIsChecked !== previousIsChecked) {
          changedIndices.push(i);
          this.adapter.setCheckedCheckboxOrRadioAtIndex(i, newIsChecked);
          this.adapter.setAttributeForElementIndex(i, selectionAttribute, "" + newIsChecked);
          if (newIsChecked) {
            currentlySelected.add(i);
          } else {
            currentlySelected.delete(i);
          }
        }
      }
      if (changedIndices.length) {
        this.selectedIndex = __spreadArray([], __read(currentlySelected));
        this.adapter.notifySelectionChange(changedIndices);
      }
    };
    MDCListFoundation2.prototype.setTabindexAtIndex = function(index) {
      if (this.focusedItemIndex === numbers$2.UNSET_INDEX && index !== 0) {
        this.adapter.setAttributeForElementIndex(0, "tabindex", "-1");
      } else if (this.focusedItemIndex >= 0 && this.focusedItemIndex !== index) {
        this.adapter.setAttributeForElementIndex(this.focusedItemIndex, "tabindex", "-1");
      }
      if (!(this.selectedIndex instanceof Array) && this.selectedIndex !== index) {
        this.adapter.setAttributeForElementIndex(this.selectedIndex, "tabindex", "-1");
      }
      if (index !== numbers$2.UNSET_INDEX) {
        this.adapter.setAttributeForElementIndex(index, "tabindex", "0");
      }
    };
    MDCListFoundation2.prototype.isSelectableList = function() {
      return this.isSingleSelectionList || this.isCheckboxList || this.isRadioList;
    };
    MDCListFoundation2.prototype.setTabindexToFirstSelectedOrFocusedItem = function() {
      var targetIndex = this.getFirstSelectedOrFocusedItemIndex();
      this.setTabindexAtIndex(targetIndex);
    };
    MDCListFoundation2.prototype.getFirstSelectedOrFocusedItemIndex = function() {
      if (!this.isSelectableList()) {
        return Math.max(this.focusedItemIndex, 0);
      }
      if (typeof this.selectedIndex === "number" && this.selectedIndex !== numbers$2.UNSET_INDEX) {
        return this.selectedIndex;
      }
      if (isNumberArray(this.selectedIndex) && this.selectedIndex.length > 0) {
        return this.selectedIndex.reduce(function(minIndex, currentIndex) {
          return Math.min(minIndex, currentIndex);
        });
      }
      return 0;
    };
    MDCListFoundation2.prototype.isIndexValid = function(index, validateListType) {
      var _this = this;
      if (validateListType === void 0) {
        validateListType = true;
      }
      if (index instanceof Array) {
        if (!this.isCheckboxList && validateListType) {
          throw new Error("MDCListFoundation: Array of index is only supported for checkbox based list");
        }
        if (index.length === 0) {
          return true;
        } else {
          return index.some(function(i) {
            return _this.isIndexInRange(i);
          });
        }
      } else if (typeof index === "number") {
        if (this.isCheckboxList && validateListType) {
          throw new Error("MDCListFoundation: Expected array of index for checkbox based list but got number: " + index);
        }
        return this.isIndexInRange(index) || this.isSingleSelectionList && index === numbers$2.UNSET_INDEX;
      } else {
        return false;
      }
    };
    MDCListFoundation2.prototype.isIndexInRange = function(index) {
      var listSize = this.adapter.getListItemCount();
      return index >= 0 && index < listSize;
    };
    MDCListFoundation2.prototype.setSelectedIndexOnAction = function(index, isCheckboxAlreadyUpdatedInAdapter) {
      this.lastSelectedIndex = index;
      if (this.isCheckboxList) {
        this.toggleCheckboxAtIndex(index, isCheckboxAlreadyUpdatedInAdapter);
        this.adapter.notifySelectionChange([index]);
      } else {
        this.setSelectedIndex(index, {
          isUserInteraction: true
        });
      }
    };
    MDCListFoundation2.prototype.toggleCheckboxAtIndex = function(index, isCheckboxAlreadyUpdatedInAdapter) {
      var selectionAttribute = this.getSelectionAttribute();
      var adapterIsChecked = this.adapter.isCheckboxCheckedAtIndex(index);
      var newCheckedValue;
      if (isCheckboxAlreadyUpdatedInAdapter) {
        newCheckedValue = adapterIsChecked;
      } else {
        newCheckedValue = !adapterIsChecked;
        this.adapter.setCheckedCheckboxOrRadioAtIndex(index, newCheckedValue);
      }
      this.adapter.setAttributeForElementIndex(index, selectionAttribute, newCheckedValue ? "true" : "false");
      var selectedIndexes = this.selectedIndex === numbers$2.UNSET_INDEX ? [] : this.selectedIndex.slice();
      if (newCheckedValue) {
        selectedIndexes.push(index);
      } else {
        selectedIndexes = selectedIndexes.filter(function(i) {
          return i !== index;
        });
      }
      this.selectedIndex = selectedIndexes;
    };
    MDCListFoundation2.prototype.focusItemAtIndex = function(index) {
      this.adapter.focusItemAtIndex(index);
      this.focusedItemIndex = index;
    };
    MDCListFoundation2.prototype.checkboxListToggleAll = function(currentlySelectedIndexes, isUserInteraction) {
      var count = this.adapter.getListItemCount();
      if (currentlySelectedIndexes.length === count) {
        this.setCheckboxAtIndex([], {
          isUserInteraction
        });
      } else {
        var allIndexes = [];
        for (var i = 0; i < count; i++) {
          if (!this.isIndexDisabled(i) || currentlySelectedIndexes.indexOf(i) > -1) {
            allIndexes.push(i);
          }
        }
        this.setCheckboxAtIndex(allIndexes, {
          isUserInteraction
        });
      }
    };
    MDCListFoundation2.prototype.typeaheadMatchItem = function(nextChar, startingIndex, skipFocus) {
      var _this = this;
      if (skipFocus === void 0) {
        skipFocus = false;
      }
      var opts = {
        focusItemAtIndex: function(index) {
          _this.focusItemAtIndex(index);
        },
        focusedItemIndex: startingIndex ? startingIndex : this.focusedItemIndex,
        nextChar,
        sortedIndexByFirstChar: this.sortedIndexByFirstChar,
        skipFocus,
        isItemAtIndexDisabled: function(index) {
          return _this.isIndexDisabled(index);
        }
      };
      return matchItem(opts, this.typeaheadState);
    };
    MDCListFoundation2.prototype.typeaheadInitSortedIndex = function() {
      return initSortedIndex(this.adapter.getListItemCount(), this.adapter.getPrimaryTextAtIndex);
    };
    MDCListFoundation2.prototype.clearTypeaheadBuffer = function() {
      clearBuffer(this.typeaheadState);
    };
    return MDCListFoundation2;
  }(MDCFoundation)
);
var MDCList = (
  /** @class */
  function(_super) {
    __extends(MDCList2, _super);
    function MDCList2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    Object.defineProperty(MDCList2.prototype, "vertical", {
      set: function(value) {
        this.foundation.setVerticalOrientation(value);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCList2.prototype, "listElements", {
      get: function() {
        return Array.from(this.root.querySelectorAll("." + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS]));
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCList2.prototype, "wrapFocus", {
      set: function(value) {
        this.foundation.setWrapFocus(value);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCList2.prototype, "typeaheadInProgress", {
      /**
       * @return Whether typeahead is currently matching a user-specified prefix.
       */
      get: function() {
        return this.foundation.isTypeaheadInProgress();
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCList2.prototype, "hasTypeahead", {
      /**
       * Sets whether typeahead functionality is enabled on the list.
       * @param hasTypeahead Whether typeahead is enabled.
       */
      set: function(hasTypeahead) {
        this.foundation.setHasTypeahead(hasTypeahead);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCList2.prototype, "singleSelection", {
      set: function(isSingleSelectionList) {
        this.foundation.setSingleSelection(isSingleSelectionList);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCList2.prototype, "disabledItemsFocusable", {
      set: function(areDisabledItemsFocusable) {
        this.foundation.setDisabledItemsFocusable(areDisabledItemsFocusable);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCList2.prototype, "selectedIndex", {
      get: function() {
        return this.foundation.getSelectedIndex();
      },
      set: function(index) {
        this.foundation.setSelectedIndex(index);
      },
      enumerable: false,
      configurable: true
    });
    MDCList2.attachTo = function(root) {
      return new MDCList2(root);
    };
    MDCList2.prototype.initialSyncWithDOM = function() {
      this.isEvolutionEnabled = evolutionAttribute in this.root.dataset;
      if (this.isEvolutionEnabled) {
        this.classNameMap = evolutionClassNameMap;
      } else if (matches(this.root, strings$2.DEPRECATED_SELECTOR)) {
        this.classNameMap = deprecatedClassNameMap;
      } else {
        this.classNameMap = Object.values(cssClasses$2).reduce(function(obj, className) {
          obj[className] = className;
          return obj;
        }, {});
      }
      this.handleClick = this.handleClickEvent.bind(this);
      this.handleKeydown = this.handleKeydownEvent.bind(this);
      this.focusInEventListener = this.handleFocusInEvent.bind(this);
      this.focusOutEventListener = this.handleFocusOutEvent.bind(this);
      this.listen("keydown", this.handleKeydown);
      this.listen("click", this.handleClick);
      this.listen("focusin", this.focusInEventListener);
      this.listen("focusout", this.focusOutEventListener);
      this.layout();
      this.initializeListType();
      this.ensureFocusable();
    };
    MDCList2.prototype.destroy = function() {
      this.unlisten("keydown", this.handleKeydown);
      this.unlisten("click", this.handleClick);
      this.unlisten("focusin", this.focusInEventListener);
      this.unlisten("focusout", this.focusOutEventListener);
    };
    MDCList2.prototype.layout = function() {
      var direction = this.root.getAttribute(strings$2.ARIA_ORIENTATION);
      this.vertical = direction !== strings$2.ARIA_ORIENTATION_HORIZONTAL;
      var itemSelector = "." + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + ":not([tabindex])";
      var childSelector = strings$2.FOCUSABLE_CHILD_ELEMENTS;
      var itemEls = this.root.querySelectorAll(itemSelector);
      if (itemEls.length) {
        Array.prototype.forEach.call(itemEls, function(el) {
          el.setAttribute("tabindex", "-1");
        });
      }
      var focusableChildEls = this.root.querySelectorAll(childSelector);
      if (focusableChildEls.length) {
        Array.prototype.forEach.call(focusableChildEls, function(el) {
          el.setAttribute("tabindex", "-1");
        });
      }
      if (this.isEvolutionEnabled) {
        this.foundation.setUseSelectedAttribute(true);
      }
      this.foundation.layout();
    };
    MDCList2.prototype.getPrimaryText = function(item) {
      var _a2;
      var primaryText = item.querySelector("." + this.classNameMap[cssClasses$2.LIST_ITEM_PRIMARY_TEXT_CLASS]);
      if (this.isEvolutionEnabled || primaryText) {
        return (_a2 = primaryText === null || primaryText === void 0 ? void 0 : primaryText.textContent) !== null && _a2 !== void 0 ? _a2 : "";
      }
      var singleLineText = item.querySelector("." + this.classNameMap[cssClasses$2.LIST_ITEM_TEXT_CLASS]);
      return singleLineText && singleLineText.textContent || "";
    };
    MDCList2.prototype.initializeListType = function() {
      var _this = this;
      this.isInteractive = matches(this.root, strings$2.ARIA_INTERACTIVE_ROLES_SELECTOR);
      if (this.isEvolutionEnabled && this.isInteractive) {
        var selection = Array.from(this.root.querySelectorAll(strings$2.SELECTED_ITEM_SELECTOR), function(listItem) {
          return _this.listElements.indexOf(listItem);
        });
        if (matches(this.root, strings$2.ARIA_MULTI_SELECTABLE_SELECTOR)) {
          this.selectedIndex = selection;
        } else if (selection.length > 0) {
          this.selectedIndex = selection[0];
        }
        return;
      }
      var checkboxListItems = this.root.querySelectorAll(strings$2.ARIA_ROLE_CHECKBOX_SELECTOR);
      var radioSelectedListItem = this.root.querySelector(strings$2.ARIA_CHECKED_RADIO_SELECTOR);
      if (checkboxListItems.length) {
        var preselectedItems = this.root.querySelectorAll(strings$2.ARIA_CHECKED_CHECKBOX_SELECTOR);
        this.selectedIndex = Array.from(preselectedItems, function(listItem) {
          return _this.listElements.indexOf(listItem);
        });
      } else if (radioSelectedListItem) {
        this.selectedIndex = this.listElements.indexOf(radioSelectedListItem);
      }
    };
    MDCList2.prototype.setEnabled = function(itemIndex, isEnabled) {
      this.foundation.setEnabled(itemIndex, isEnabled);
    };
    MDCList2.prototype.typeaheadMatchItem = function(nextChar, startingIndex) {
      return this.foundation.typeaheadMatchItem(
        nextChar,
        startingIndex,
        /** skipFocus */
        true
      );
    };
    MDCList2.prototype.getDefaultFoundation = function() {
      var _this = this;
      var adapter = {
        addClassForElementIndex: function(index, className) {
          var element = _this.listElements[index];
          if (element) {
            element.classList.add(_this.classNameMap[className]);
          }
        },
        focusItemAtIndex: function(index) {
          var element = _this.listElements[index];
          if (element) {
            element.focus();
          }
        },
        getAttributeForElementIndex: function(index, attr) {
          return _this.listElements[index].getAttribute(attr);
        },
        getFocusedElementIndex: function() {
          return _this.listElements.indexOf(document.activeElement);
        },
        getListItemCount: function() {
          return _this.listElements.length;
        },
        getPrimaryTextAtIndex: function(index) {
          return _this.getPrimaryText(_this.listElements[index]);
        },
        hasCheckboxAtIndex: function(index) {
          var listItem = _this.listElements[index];
          return !!listItem.querySelector(strings$2.CHECKBOX_SELECTOR);
        },
        hasRadioAtIndex: function(index) {
          var listItem = _this.listElements[index];
          return !!listItem.querySelector(strings$2.RADIO_SELECTOR);
        },
        isCheckboxCheckedAtIndex: function(index) {
          var listItem = _this.listElements[index];
          var toggleEl = listItem.querySelector(strings$2.CHECKBOX_SELECTOR);
          return toggleEl.checked;
        },
        isFocusInsideList: function() {
          return _this.root !== document.activeElement && _this.root.contains(document.activeElement);
        },
        isRootFocused: function() {
          return document.activeElement === _this.root;
        },
        listItemAtIndexHasClass: function(index, className) {
          return _this.listElements[index].classList.contains(_this.classNameMap[className]);
        },
        notifyAction: function(index) {
          _this.emit(
            strings$2.ACTION_EVENT,
            {
              index
            },
            /** shouldBubble */
            true
          );
        },
        notifySelectionChange: function(changedIndices) {
          _this.emit(
            strings$2.SELECTION_CHANGE_EVENT,
            {
              changedIndices
            },
            /** shouldBubble */
            true
          );
        },
        removeClassForElementIndex: function(index, className) {
          var element = _this.listElements[index];
          if (element) {
            element.classList.remove(_this.classNameMap[className]);
          }
        },
        setAttributeForElementIndex: function(index, attr, value) {
          var element = _this.listElements[index];
          if (element) {
            element.setAttribute(attr, value);
          }
        },
        setCheckedCheckboxOrRadioAtIndex: function(index, isChecked) {
          var listItem = _this.listElements[index];
          var toggleEl = listItem.querySelector(strings$2.CHECKBOX_RADIO_SELECTOR);
          toggleEl.checked = isChecked;
          var event = document.createEvent("Event");
          event.initEvent("change", true, true);
          toggleEl.dispatchEvent(event);
        },
        setTabIndexForListItemChildren: function(listItemIndex, tabIndexValue) {
          var element = _this.listElements[listItemIndex];
          var selector = strings$2.CHILD_ELEMENTS_TO_TOGGLE_TABINDEX;
          Array.prototype.forEach.call(element.querySelectorAll(selector), function(el) {
            el.setAttribute("tabindex", tabIndexValue);
          });
        }
      };
      return new MDCListFoundation(adapter);
    };
    MDCList2.prototype.ensureFocusable = function() {
      if (this.isEvolutionEnabled && this.isInteractive) {
        if (!this.root.querySelector("." + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + '[tabindex="0"]')) {
          var index = this.initialFocusIndex();
          if (index !== -1) {
            this.listElements[index].tabIndex = 0;
          }
        }
      }
    };
    MDCList2.prototype.initialFocusIndex = function() {
      if (this.selectedIndex instanceof Array && this.selectedIndex.length > 0) {
        return this.selectedIndex[0];
      }
      if (typeof this.selectedIndex === "number" && this.selectedIndex !== numbers$2.UNSET_INDEX) {
        return this.selectedIndex;
      }
      var el = this.root.querySelector("." + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + ":not(." + this.classNameMap[cssClasses$2.LIST_ITEM_DISABLED_CLASS] + ")");
      if (el === null) {
        return -1;
      }
      return this.getListItemIndex(el);
    };
    MDCList2.prototype.getListItemIndex = function(el) {
      var nearestParent = closest(el, "." + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS] + ", ." + this.classNameMap[cssClasses$2.ROOT]);
      if (nearestParent && matches(nearestParent, "." + this.classNameMap[cssClasses$2.LIST_ITEM_CLASS])) {
        return this.listElements.indexOf(nearestParent);
      }
      return -1;
    };
    MDCList2.prototype.handleFocusInEvent = function(evt) {
      var index = this.getListItemIndex(evt.target);
      this.foundation.handleFocusIn(index);
    };
    MDCList2.prototype.handleFocusOutEvent = function(evt) {
      var index = this.getListItemIndex(evt.target);
      this.foundation.handleFocusOut(index);
    };
    MDCList2.prototype.handleKeydownEvent = function(evt) {
      var index = this.getListItemIndex(evt.target);
      var target = evt.target;
      this.foundation.handleKeydown(evt, target.classList.contains(this.classNameMap[cssClasses$2.LIST_ITEM_CLASS]), index);
    };
    MDCList2.prototype.handleClickEvent = function(evt) {
      var index = this.getListItemIndex(evt.target);
      var target = evt.target;
      var toggleCheckbox = !matches(target, strings$2.CHECKBOX_RADIO_SELECTOR);
      this.foundation.handleClick(index, toggleCheckbox, evt);
    };
    return MDCList2;
  }(MDCComponent)
);
var cssClasses$1 = {
  ANCHOR: "mdc-menu-surface--anchor",
  ANIMATING_CLOSED: "mdc-menu-surface--animating-closed",
  ANIMATING_OPEN: "mdc-menu-surface--animating-open",
  FIXED: "mdc-menu-surface--fixed",
  IS_OPEN_BELOW: "mdc-menu-surface--is-open-below",
  OPEN: "mdc-menu-surface--open",
  ROOT: "mdc-menu-surface"
};
var strings$1 = {
  CLOSED_EVENT: "MDCMenuSurface:closed",
  CLOSING_EVENT: "MDCMenuSurface:closing",
  OPENED_EVENT: "MDCMenuSurface:opened",
  OPENING_EVENT: "MDCMenuSurface:opening",
  FOCUSABLE_ELEMENTS: ["button:not(:disabled)", '[href]:not([aria-disabled="true"])', "input:not(:disabled)", "select:not(:disabled)", "textarea:not(:disabled)", '[tabindex]:not([tabindex="-1"]):not([aria-disabled="true"])'].join(", ")
};
var numbers$1 = {
  /** Total duration of menu-surface open animation. */
  TRANSITION_OPEN_DURATION: 120,
  /** Total duration of menu-surface close animation. */
  TRANSITION_CLOSE_DURATION: 75,
  /**
   * Margin left to the edge of the viewport when menu-surface is at maximum
   * possible height. Also used as a viewport margin.
   */
  MARGIN_TO_EDGE: 32,
  /**
   * Ratio of anchor width to menu-surface width for switching from corner
   * positioning to center positioning.
   */
  ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO: 0.67,
  /**
   * Amount of time to wait before restoring focus when closing the menu
   * surface. This is important because if a touch event triggered the menu
   * close, and the subsequent mouse event occurs after focus is restored, then
   * the restored focus would be lost.
   */
  TOUCH_EVENT_WAIT_MS: 30
};
var CornerBit;
(function(CornerBit2) {
  CornerBit2[CornerBit2["BOTTOM"] = 1] = "BOTTOM";
  CornerBit2[CornerBit2["CENTER"] = 2] = "CENTER";
  CornerBit2[CornerBit2["RIGHT"] = 4] = "RIGHT";
  CornerBit2[CornerBit2["FLIP_RTL"] = 8] = "FLIP_RTL";
})(CornerBit || (CornerBit = {}));
var Corner;
(function(Corner2) {
  Corner2[Corner2["TOP_LEFT"] = 0] = "TOP_LEFT";
  Corner2[Corner2["TOP_RIGHT"] = 4] = "TOP_RIGHT";
  Corner2[Corner2["BOTTOM_LEFT"] = 1] = "BOTTOM_LEFT";
  Corner2[Corner2["BOTTOM_RIGHT"] = 5] = "BOTTOM_RIGHT";
  Corner2[Corner2["TOP_START"] = 8] = "TOP_START";
  Corner2[Corner2["TOP_END"] = 12] = "TOP_END";
  Corner2[Corner2["BOTTOM_START"] = 9] = "BOTTOM_START";
  Corner2[Corner2["BOTTOM_END"] = 13] = "BOTTOM_END";
})(Corner || (Corner = {}));
var MDCMenuSurfaceFoundation = (
  /** @class */
  function(_super) {
    __extends(MDCMenuSurfaceFoundation2, _super);
    function MDCMenuSurfaceFoundation2(adapter) {
      var _this = _super.call(this, __assign(__assign({}, MDCMenuSurfaceFoundation2.defaultAdapter), adapter)) || this;
      _this.isSurfaceOpen = false;
      _this.isQuickOpen = false;
      _this.isHoistedElement = false;
      _this.isFixedPosition = false;
      _this.isHorizontallyCenteredOnViewport = false;
      _this.maxHeight = 0;
      _this.openBottomBias = 0;
      _this.openAnimationEndTimerId = 0;
      _this.closeAnimationEndTimerId = 0;
      _this.animationRequestId = 0;
      _this.anchorCorner = Corner.TOP_START;
      _this.originCorner = Corner.TOP_START;
      _this.anchorMargin = {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
      };
      _this.position = {
        x: 0,
        y: 0
      };
      return _this;
    }
    Object.defineProperty(MDCMenuSurfaceFoundation2, "cssClasses", {
      get: function() {
        return cssClasses$1;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenuSurfaceFoundation2, "strings", {
      get: function() {
        return strings$1;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenuSurfaceFoundation2, "numbers", {
      get: function() {
        return numbers$1;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenuSurfaceFoundation2, "Corner", {
      get: function() {
        return Corner;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenuSurfaceFoundation2, "defaultAdapter", {
      /**
       * @see {@link MDCMenuSurfaceAdapter} for typing information on parameters and return types.
       */
      get: function() {
        return {
          addClass: function() {
            return void 0;
          },
          removeClass: function() {
            return void 0;
          },
          hasClass: function() {
            return false;
          },
          hasAnchor: function() {
            return false;
          },
          isElementInContainer: function() {
            return false;
          },
          isFocused: function() {
            return false;
          },
          isRtl: function() {
            return false;
          },
          getInnerDimensions: function() {
            return {
              height: 0,
              width: 0
            };
          },
          getAnchorDimensions: function() {
            return null;
          },
          getWindowDimensions: function() {
            return {
              height: 0,
              width: 0
            };
          },
          getBodyDimensions: function() {
            return {
              height: 0,
              width: 0
            };
          },
          getWindowScroll: function() {
            return {
              x: 0,
              y: 0
            };
          },
          setPosition: function() {
            return void 0;
          },
          setMaxHeight: function() {
            return void 0;
          },
          setTransformOrigin: function() {
            return void 0;
          },
          saveFocus: function() {
            return void 0;
          },
          restoreFocus: function() {
            return void 0;
          },
          notifyClose: function() {
            return void 0;
          },
          notifyClosing: function() {
            return void 0;
          },
          notifyOpen: function() {
            return void 0;
          },
          notifyOpening: function() {
            return void 0;
          }
        };
      },
      enumerable: false,
      configurable: true
    });
    MDCMenuSurfaceFoundation2.prototype.init = function() {
      var _a2 = MDCMenuSurfaceFoundation2.cssClasses, ROOT = _a2.ROOT, OPEN = _a2.OPEN;
      if (!this.adapter.hasClass(ROOT)) {
        throw new Error(ROOT + " class required in root element.");
      }
      if (this.adapter.hasClass(OPEN)) {
        this.isSurfaceOpen = true;
      }
    };
    MDCMenuSurfaceFoundation2.prototype.destroy = function() {
      clearTimeout(this.openAnimationEndTimerId);
      clearTimeout(this.closeAnimationEndTimerId);
      cancelAnimationFrame(this.animationRequestId);
    };
    MDCMenuSurfaceFoundation2.prototype.setAnchorCorner = function(corner) {
      this.anchorCorner = corner;
    };
    MDCMenuSurfaceFoundation2.prototype.flipCornerHorizontally = function() {
      this.originCorner = this.originCorner ^ CornerBit.RIGHT;
    };
    MDCMenuSurfaceFoundation2.prototype.setAnchorMargin = function(margin) {
      this.anchorMargin.top = margin.top || 0;
      this.anchorMargin.right = margin.right || 0;
      this.anchorMargin.bottom = margin.bottom || 0;
      this.anchorMargin.left = margin.left || 0;
    };
    MDCMenuSurfaceFoundation2.prototype.setIsHoisted = function(isHoisted) {
      this.isHoistedElement = isHoisted;
    };
    MDCMenuSurfaceFoundation2.prototype.setFixedPosition = function(isFixedPosition) {
      this.isFixedPosition = isFixedPosition;
    };
    MDCMenuSurfaceFoundation2.prototype.isFixed = function() {
      return this.isFixedPosition;
    };
    MDCMenuSurfaceFoundation2.prototype.setAbsolutePosition = function(x, y) {
      this.position.x = this.isFinite(x) ? x : 0;
      this.position.y = this.isFinite(y) ? y : 0;
    };
    MDCMenuSurfaceFoundation2.prototype.setIsHorizontallyCenteredOnViewport = function(isCentered) {
      this.isHorizontallyCenteredOnViewport = isCentered;
    };
    MDCMenuSurfaceFoundation2.prototype.setQuickOpen = function(quickOpen) {
      this.isQuickOpen = quickOpen;
    };
    MDCMenuSurfaceFoundation2.prototype.setMaxHeight = function(maxHeight) {
      this.maxHeight = maxHeight;
    };
    MDCMenuSurfaceFoundation2.prototype.setOpenBottomBias = function(bias) {
      this.openBottomBias = bias;
    };
    MDCMenuSurfaceFoundation2.prototype.isOpen = function() {
      return this.isSurfaceOpen;
    };
    MDCMenuSurfaceFoundation2.prototype.open = function() {
      var _this = this;
      if (this.isSurfaceOpen) {
        return;
      }
      this.adapter.notifyOpening();
      this.adapter.saveFocus();
      if (this.isQuickOpen) {
        this.isSurfaceOpen = true;
        this.adapter.addClass(MDCMenuSurfaceFoundation2.cssClasses.OPEN);
        this.dimensions = this.adapter.getInnerDimensions();
        this.autoposition();
        this.adapter.notifyOpen();
      } else {
        this.adapter.addClass(MDCMenuSurfaceFoundation2.cssClasses.ANIMATING_OPEN);
        this.animationRequestId = requestAnimationFrame(function() {
          _this.dimensions = _this.adapter.getInnerDimensions();
          _this.autoposition();
          _this.adapter.addClass(MDCMenuSurfaceFoundation2.cssClasses.OPEN);
          _this.openAnimationEndTimerId = setTimeout(function() {
            _this.openAnimationEndTimerId = 0;
            _this.adapter.removeClass(MDCMenuSurfaceFoundation2.cssClasses.ANIMATING_OPEN);
            _this.adapter.notifyOpen();
          }, numbers$1.TRANSITION_OPEN_DURATION);
        });
        this.isSurfaceOpen = true;
      }
    };
    MDCMenuSurfaceFoundation2.prototype.close = function(skipRestoreFocus) {
      var _this = this;
      if (skipRestoreFocus === void 0) {
        skipRestoreFocus = false;
      }
      if (!this.isSurfaceOpen) {
        return;
      }
      this.adapter.notifyClosing();
      if (this.isQuickOpen) {
        this.isSurfaceOpen = false;
        if (!skipRestoreFocus) {
          this.maybeRestoreFocus();
        }
        this.adapter.removeClass(MDCMenuSurfaceFoundation2.cssClasses.OPEN);
        this.adapter.removeClass(MDCMenuSurfaceFoundation2.cssClasses.IS_OPEN_BELOW);
        this.adapter.notifyClose();
        return;
      }
      this.adapter.addClass(MDCMenuSurfaceFoundation2.cssClasses.ANIMATING_CLOSED);
      requestAnimationFrame(function() {
        _this.adapter.removeClass(MDCMenuSurfaceFoundation2.cssClasses.OPEN);
        _this.adapter.removeClass(MDCMenuSurfaceFoundation2.cssClasses.IS_OPEN_BELOW);
        _this.closeAnimationEndTimerId = setTimeout(function() {
          _this.closeAnimationEndTimerId = 0;
          _this.adapter.removeClass(MDCMenuSurfaceFoundation2.cssClasses.ANIMATING_CLOSED);
          _this.adapter.notifyClose();
        }, numbers$1.TRANSITION_CLOSE_DURATION);
      });
      this.isSurfaceOpen = false;
      if (!skipRestoreFocus) {
        this.maybeRestoreFocus();
      }
    };
    MDCMenuSurfaceFoundation2.prototype.handleBodyClick = function(evt) {
      var el = evt.target;
      if (this.adapter.isElementInContainer(el)) {
        return;
      }
      this.close();
    };
    MDCMenuSurfaceFoundation2.prototype.handleKeydown = function(evt) {
      var keyCode = evt.keyCode, key = evt.key;
      var isEscape = key === "Escape" || keyCode === 27;
      if (isEscape) {
        this.close();
      }
    };
    MDCMenuSurfaceFoundation2.prototype.autoposition = function() {
      var _a2;
      this.measurements = this.getAutoLayoutmeasurements();
      var corner = this.getoriginCorner();
      var maxMenuSurfaceHeight = this.getMenuSurfaceMaxHeight(corner);
      var verticalAlignment = this.hasBit(corner, CornerBit.BOTTOM) ? "bottom" : "top";
      var horizontalAlignment = this.hasBit(corner, CornerBit.RIGHT) ? "right" : "left";
      var horizontalOffset = this.getHorizontalOriginOffset(corner);
      var verticalOffset = this.getVerticalOriginOffset(corner);
      var _b2 = this.measurements, anchorSize = _b2.anchorSize, surfaceSize = _b2.surfaceSize;
      var position = (_a2 = {}, _a2[horizontalAlignment] = horizontalOffset, _a2[verticalAlignment] = verticalOffset, _a2);
      if (anchorSize.width / surfaceSize.width > numbers$1.ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO) {
        horizontalAlignment = "center";
      }
      if (this.isHoistedElement || this.isFixedPosition) {
        this.adjustPositionForHoistedElement(position);
      }
      this.adapter.setTransformOrigin(horizontalAlignment + " " + verticalAlignment);
      this.adapter.setPosition(position);
      this.adapter.setMaxHeight(maxMenuSurfaceHeight ? maxMenuSurfaceHeight + "px" : "");
      if (!this.hasBit(corner, CornerBit.BOTTOM)) {
        this.adapter.addClass(MDCMenuSurfaceFoundation2.cssClasses.IS_OPEN_BELOW);
      }
    };
    MDCMenuSurfaceFoundation2.prototype.getAutoLayoutmeasurements = function() {
      var anchorRect = this.adapter.getAnchorDimensions();
      var bodySize = this.adapter.getBodyDimensions();
      var viewportSize = this.adapter.getWindowDimensions();
      var windowScroll = this.adapter.getWindowScroll();
      if (!anchorRect) {
        anchorRect = {
          top: this.position.y,
          right: this.position.x,
          bottom: this.position.y,
          left: this.position.x,
          width: 0,
          height: 0
        };
      }
      return {
        anchorSize: anchorRect,
        bodySize,
        surfaceSize: this.dimensions,
        viewportDistance: {
          // tslint:disable:object-literal-sort-keys Positional properties are more readable when they're grouped together
          top: anchorRect.top,
          right: viewportSize.width - anchorRect.right,
          bottom: viewportSize.height - anchorRect.bottom,
          left: anchorRect.left
          // tslint:enable:object-literal-sort-keys
        },
        viewportSize,
        windowScroll
      };
    };
    MDCMenuSurfaceFoundation2.prototype.getoriginCorner = function() {
      var corner = this.originCorner;
      var _a2 = this.measurements, viewportDistance = _a2.viewportDistance, anchorSize = _a2.anchorSize, surfaceSize = _a2.surfaceSize;
      var MARGIN_TO_EDGE = MDCMenuSurfaceFoundation2.numbers.MARGIN_TO_EDGE;
      var isAnchoredToBottom = this.hasBit(this.anchorCorner, CornerBit.BOTTOM);
      var availableTop;
      var availableBottom;
      if (isAnchoredToBottom) {
        availableTop = viewportDistance.top - MARGIN_TO_EDGE + this.anchorMargin.bottom;
        availableBottom = viewportDistance.bottom - MARGIN_TO_EDGE - this.anchorMargin.bottom;
      } else {
        availableTop = viewportDistance.top - MARGIN_TO_EDGE + this.anchorMargin.top;
        availableBottom = viewportDistance.bottom - MARGIN_TO_EDGE + anchorSize.height - this.anchorMargin.top;
      }
      var isAvailableBottom = availableBottom - surfaceSize.height > 0;
      if (!isAvailableBottom && availableTop > availableBottom + this.openBottomBias) {
        corner = this.setBit(corner, CornerBit.BOTTOM);
      }
      var isRtl = this.adapter.isRtl();
      var isFlipRtl = this.hasBit(this.anchorCorner, CornerBit.FLIP_RTL);
      var hasRightBit = this.hasBit(this.anchorCorner, CornerBit.RIGHT) || this.hasBit(corner, CornerBit.RIGHT);
      var isAnchoredToRight = false;
      if (isRtl && isFlipRtl) {
        isAnchoredToRight = !hasRightBit;
      } else {
        isAnchoredToRight = hasRightBit;
      }
      var availableLeft;
      var availableRight;
      if (isAnchoredToRight) {
        availableLeft = viewportDistance.left + anchorSize.width + this.anchorMargin.right;
        availableRight = viewportDistance.right - this.anchorMargin.right;
      } else {
        availableLeft = viewportDistance.left + this.anchorMargin.left;
        availableRight = viewportDistance.right + anchorSize.width - this.anchorMargin.left;
      }
      var isAvailableLeft = availableLeft - surfaceSize.width > 0;
      var isAvailableRight = availableRight - surfaceSize.width > 0;
      var isOriginCornerAlignedToEnd = this.hasBit(corner, CornerBit.FLIP_RTL) && this.hasBit(corner, CornerBit.RIGHT);
      if (isAvailableRight && isOriginCornerAlignedToEnd && isRtl || !isAvailableLeft && isOriginCornerAlignedToEnd) {
        corner = this.unsetBit(corner, CornerBit.RIGHT);
      } else if (isAvailableLeft && isAnchoredToRight && isRtl || isAvailableLeft && !isAnchoredToRight && hasRightBit || !isAvailableRight && availableLeft >= availableRight) {
        corner = this.setBit(corner, CornerBit.RIGHT);
      }
      return corner;
    };
    MDCMenuSurfaceFoundation2.prototype.getMenuSurfaceMaxHeight = function(corner) {
      if (this.maxHeight > 0) {
        return this.maxHeight;
      }
      var viewportDistance = this.measurements.viewportDistance;
      var maxHeight = 0;
      var isBottomAligned = this.hasBit(corner, CornerBit.BOTTOM);
      var isBottomAnchored = this.hasBit(this.anchorCorner, CornerBit.BOTTOM);
      var MARGIN_TO_EDGE = MDCMenuSurfaceFoundation2.numbers.MARGIN_TO_EDGE;
      if (isBottomAligned) {
        maxHeight = viewportDistance.top + this.anchorMargin.top - MARGIN_TO_EDGE;
        if (!isBottomAnchored) {
          maxHeight += this.measurements.anchorSize.height;
        }
      } else {
        maxHeight = viewportDistance.bottom - this.anchorMargin.bottom + this.measurements.anchorSize.height - MARGIN_TO_EDGE;
        if (isBottomAnchored) {
          maxHeight -= this.measurements.anchorSize.height;
        }
      }
      return maxHeight;
    };
    MDCMenuSurfaceFoundation2.prototype.getHorizontalOriginOffset = function(corner) {
      var anchorSize = this.measurements.anchorSize;
      var isRightAligned = this.hasBit(corner, CornerBit.RIGHT);
      var avoidHorizontalOverlap = this.hasBit(this.anchorCorner, CornerBit.RIGHT);
      if (isRightAligned) {
        var rightOffset = avoidHorizontalOverlap ? anchorSize.width - this.anchorMargin.left : this.anchorMargin.right;
        if (this.isHoistedElement || this.isFixedPosition) {
          return rightOffset - (this.measurements.viewportSize.width - this.measurements.bodySize.width);
        }
        return rightOffset;
      }
      return avoidHorizontalOverlap ? anchorSize.width - this.anchorMargin.right : this.anchorMargin.left;
    };
    MDCMenuSurfaceFoundation2.prototype.getVerticalOriginOffset = function(corner) {
      var anchorSize = this.measurements.anchorSize;
      var isBottomAligned = this.hasBit(corner, CornerBit.BOTTOM);
      var avoidVerticalOverlap = this.hasBit(this.anchorCorner, CornerBit.BOTTOM);
      var y = 0;
      if (isBottomAligned) {
        y = avoidVerticalOverlap ? anchorSize.height - this.anchorMargin.top : -this.anchorMargin.bottom;
      } else {
        y = avoidVerticalOverlap ? anchorSize.height + this.anchorMargin.bottom : this.anchorMargin.top;
      }
      return y;
    };
    MDCMenuSurfaceFoundation2.prototype.adjustPositionForHoistedElement = function(position) {
      var e_1, _a2;
      var _b2 = this.measurements, windowScroll = _b2.windowScroll, viewportDistance = _b2.viewportDistance, surfaceSize = _b2.surfaceSize, viewportSize = _b2.viewportSize;
      var props = Object.keys(position);
      try {
        for (var props_1 = __values(props), props_1_1 = props_1.next(); !props_1_1.done; props_1_1 = props_1.next()) {
          var prop = props_1_1.value;
          var value = position[prop] || 0;
          if (this.isHorizontallyCenteredOnViewport && (prop === "left" || prop === "right")) {
            position[prop] = (viewportSize.width - surfaceSize.width) / 2;
            continue;
          }
          value += viewportDistance[prop];
          if (!this.isFixedPosition) {
            if (prop === "top") {
              value += windowScroll.y;
            } else if (prop === "bottom") {
              value -= windowScroll.y;
            } else if (prop === "left") {
              value += windowScroll.x;
            } else {
              value -= windowScroll.x;
            }
          }
          position[prop] = value;
        }
      } catch (e_1_1) {
        e_1 = {
          error: e_1_1
        };
      } finally {
        try {
          if (props_1_1 && !props_1_1.done && (_a2 = props_1.return)) _a2.call(props_1);
        } finally {
          if (e_1) throw e_1.error;
        }
      }
    };
    MDCMenuSurfaceFoundation2.prototype.maybeRestoreFocus = function() {
      var _this = this;
      var isRootFocused = this.adapter.isFocused();
      var ownerDocument = this.adapter.getOwnerDocument ? this.adapter.getOwnerDocument() : document;
      var childHasFocus = ownerDocument.activeElement && this.adapter.isElementInContainer(ownerDocument.activeElement);
      if (isRootFocused || childHasFocus) {
        setTimeout(function() {
          _this.adapter.restoreFocus();
        }, numbers$1.TOUCH_EVENT_WAIT_MS);
      }
    };
    MDCMenuSurfaceFoundation2.prototype.hasBit = function(corner, bit) {
      return Boolean(corner & bit);
    };
    MDCMenuSurfaceFoundation2.prototype.setBit = function(corner, bit) {
      return corner | bit;
    };
    MDCMenuSurfaceFoundation2.prototype.unsetBit = function(corner, bit) {
      return corner ^ bit;
    };
    MDCMenuSurfaceFoundation2.prototype.isFinite = function(num) {
      return typeof num === "number" && isFinite(num);
    };
    return MDCMenuSurfaceFoundation2;
  }(MDCFoundation)
);
var MDCMenuSurface = (
  /** @class */
  function(_super) {
    __extends(MDCMenuSurface2, _super);
    function MDCMenuSurface2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    MDCMenuSurface2.attachTo = function(root) {
      return new MDCMenuSurface2(root);
    };
    MDCMenuSurface2.prototype.initialSyncWithDOM = function() {
      var _this = this;
      var parentEl = this.root.parentElement;
      this.anchorElement = parentEl && parentEl.classList.contains(cssClasses$1.ANCHOR) ? parentEl : null;
      if (this.root.classList.contains(cssClasses$1.FIXED)) {
        this.setFixedPosition(true);
      }
      this.handleKeydown = function(event) {
        _this.foundation.handleKeydown(event);
      };
      this.handleBodyClick = function(event) {
        _this.foundation.handleBodyClick(event);
      };
      this.registerBodyClickListener = function() {
        document.body.addEventListener("click", _this.handleBodyClick, {
          capture: true
        });
      };
      this.deregisterBodyClickListener = function() {
        document.body.removeEventListener("click", _this.handleBodyClick, {
          capture: true
        });
      };
      this.listen("keydown", this.handleKeydown);
      this.listen(strings$1.OPENED_EVENT, this.registerBodyClickListener);
      this.listen(strings$1.CLOSED_EVENT, this.deregisterBodyClickListener);
    };
    MDCMenuSurface2.prototype.destroy = function() {
      this.unlisten("keydown", this.handleKeydown);
      this.unlisten(strings$1.OPENED_EVENT, this.registerBodyClickListener);
      this.unlisten(strings$1.CLOSED_EVENT, this.deregisterBodyClickListener);
      _super.prototype.destroy.call(this);
    };
    MDCMenuSurface2.prototype.isOpen = function() {
      return this.foundation.isOpen();
    };
    MDCMenuSurface2.prototype.open = function() {
      this.foundation.open();
    };
    MDCMenuSurface2.prototype.close = function(skipRestoreFocus) {
      if (skipRestoreFocus === void 0) {
        skipRestoreFocus = false;
      }
      this.foundation.close(skipRestoreFocus);
    };
    Object.defineProperty(MDCMenuSurface2.prototype, "quickOpen", {
      set: function(quickOpen) {
        this.foundation.setQuickOpen(quickOpen);
      },
      enumerable: false,
      configurable: true
    });
    MDCMenuSurface2.prototype.setIsHoisted = function(isHoisted) {
      this.foundation.setIsHoisted(isHoisted);
    };
    MDCMenuSurface2.prototype.setMenuSurfaceAnchorElement = function(element) {
      this.anchorElement = element;
    };
    MDCMenuSurface2.prototype.setFixedPosition = function(isFixed) {
      if (isFixed) {
        this.root.classList.add(cssClasses$1.FIXED);
      } else {
        this.root.classList.remove(cssClasses$1.FIXED);
      }
      this.foundation.setFixedPosition(isFixed);
    };
    MDCMenuSurface2.prototype.setAbsolutePosition = function(x, y) {
      this.foundation.setAbsolutePosition(x, y);
      this.setIsHoisted(true);
    };
    MDCMenuSurface2.prototype.setAnchorCorner = function(corner) {
      this.foundation.setAnchorCorner(corner);
    };
    MDCMenuSurface2.prototype.setAnchorMargin = function(margin) {
      this.foundation.setAnchorMargin(margin);
    };
    MDCMenuSurface2.prototype.getDefaultFoundation = function() {
      var _this = this;
      var adapter = {
        addClass: function(className) {
          return _this.root.classList.add(className);
        },
        removeClass: function(className) {
          return _this.root.classList.remove(className);
        },
        hasClass: function(className) {
          return _this.root.classList.contains(className);
        },
        hasAnchor: function() {
          return !!_this.anchorElement;
        },
        notifyClose: function() {
          return _this.emit(MDCMenuSurfaceFoundation.strings.CLOSED_EVENT, {});
        },
        notifyClosing: function() {
          _this.emit(MDCMenuSurfaceFoundation.strings.CLOSING_EVENT, {});
        },
        notifyOpen: function() {
          return _this.emit(MDCMenuSurfaceFoundation.strings.OPENED_EVENT, {});
        },
        notifyOpening: function() {
          return _this.emit(MDCMenuSurfaceFoundation.strings.OPENING_EVENT, {});
        },
        isElementInContainer: function(el) {
          return _this.root.contains(el);
        },
        isRtl: function() {
          return getComputedStyle(_this.root).getPropertyValue("direction") === "rtl";
        },
        setTransformOrigin: function(origin) {
          var propertyName = getCorrectPropertyName(window, "transform") + "-origin";
          _this.root.style.setProperty(propertyName, origin);
        },
        isFocused: function() {
          return document.activeElement === _this.root;
        },
        saveFocus: function() {
          _this.previousFocus = document.activeElement;
        },
        restoreFocus: function() {
          if (_this.root.contains(document.activeElement)) {
            if (_this.previousFocus && _this.previousFocus.focus) {
              _this.previousFocus.focus();
            }
          }
        },
        getInnerDimensions: function() {
          return {
            width: _this.root.offsetWidth,
            height: _this.root.offsetHeight
          };
        },
        getAnchorDimensions: function() {
          return _this.anchorElement ? _this.anchorElement.getBoundingClientRect() : null;
        },
        getWindowDimensions: function() {
          return {
            width: window.innerWidth,
            height: window.innerHeight
          };
        },
        getBodyDimensions: function() {
          return {
            width: document.body.clientWidth,
            height: document.body.clientHeight
          };
        },
        getWindowScroll: function() {
          return {
            x: window.pageXOffset,
            y: window.pageYOffset
          };
        },
        setPosition: function(position) {
          var rootHTML = _this.root;
          rootHTML.style.left = "left" in position ? position.left + "px" : "";
          rootHTML.style.right = "right" in position ? position.right + "px" : "";
          rootHTML.style.top = "top" in position ? position.top + "px" : "";
          rootHTML.style.bottom = "bottom" in position ? position.bottom + "px" : "";
        },
        setMaxHeight: function(height) {
          _this.root.style.maxHeight = height;
        }
      };
      return new MDCMenuSurfaceFoundation(adapter);
    };
    return MDCMenuSurface2;
  }(MDCComponent)
);
var cssClasses = {
  MENU_SELECTED_LIST_ITEM: "mdc-menu-item--selected",
  MENU_SELECTION_GROUP: "mdc-menu__selection-group",
  ROOT: "mdc-menu"
};
var strings = {
  ARIA_CHECKED_ATTR: "aria-checked",
  ARIA_DISABLED_ATTR: "aria-disabled",
  CHECKBOX_SELECTOR: 'input[type="checkbox"]',
  LIST_SELECTOR: ".mdc-list,.mdc-deprecated-list",
  SELECTED_EVENT: "MDCMenu:selected",
  SKIP_RESTORE_FOCUS: "data-menu-item-skip-restore-focus"
};
var numbers = {
  FOCUS_ROOT_INDEX: -1
};
var DefaultFocusState;
(function(DefaultFocusState2) {
  DefaultFocusState2[DefaultFocusState2["NONE"] = 0] = "NONE";
  DefaultFocusState2[DefaultFocusState2["LIST_ROOT"] = 1] = "LIST_ROOT";
  DefaultFocusState2[DefaultFocusState2["FIRST_ITEM"] = 2] = "FIRST_ITEM";
  DefaultFocusState2[DefaultFocusState2["LAST_ITEM"] = 3] = "LAST_ITEM";
})(DefaultFocusState || (DefaultFocusState = {}));
var MDCMenuFoundation = (
  /** @class */
  function(_super) {
    __extends(MDCMenuFoundation2, _super);
    function MDCMenuFoundation2(adapter) {
      var _this = _super.call(this, __assign(__assign({}, MDCMenuFoundation2.defaultAdapter), adapter)) || this;
      _this.closeAnimationEndTimerId = 0;
      _this.defaultFocusState = DefaultFocusState.LIST_ROOT;
      _this.selectedIndex = -1;
      return _this;
    }
    Object.defineProperty(MDCMenuFoundation2, "cssClasses", {
      get: function() {
        return cssClasses;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenuFoundation2, "strings", {
      get: function() {
        return strings;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenuFoundation2, "numbers", {
      get: function() {
        return numbers;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenuFoundation2, "defaultAdapter", {
      /**
       * @see {@link MDCMenuAdapter} for typing information on parameters and return types.
       */
      get: function() {
        return {
          addClassToElementAtIndex: function() {
            return void 0;
          },
          removeClassFromElementAtIndex: function() {
            return void 0;
          },
          addAttributeToElementAtIndex: function() {
            return void 0;
          },
          removeAttributeFromElementAtIndex: function() {
            return void 0;
          },
          getAttributeFromElementAtIndex: function() {
            return null;
          },
          elementContainsClass: function() {
            return false;
          },
          closeSurface: function() {
            return void 0;
          },
          getElementIndex: function() {
            return -1;
          },
          notifySelected: function() {
            return void 0;
          },
          getMenuItemCount: function() {
            return 0;
          },
          focusItemAtIndex: function() {
            return void 0;
          },
          focusListRoot: function() {
            return void 0;
          },
          getSelectedSiblingOfItemAtIndex: function() {
            return -1;
          },
          isSelectableItemAtIndex: function() {
            return false;
          }
        };
      },
      enumerable: false,
      configurable: true
    });
    MDCMenuFoundation2.prototype.destroy = function() {
      if (this.closeAnimationEndTimerId) {
        clearTimeout(this.closeAnimationEndTimerId);
      }
      this.adapter.closeSurface();
    };
    MDCMenuFoundation2.prototype.handleKeydown = function(evt) {
      var key = evt.key, keyCode = evt.keyCode;
      var isTab = key === "Tab" || keyCode === 9;
      if (isTab) {
        this.adapter.closeSurface(
          /** skipRestoreFocus */
          true
        );
      }
    };
    MDCMenuFoundation2.prototype.handleItemAction = function(listItem) {
      var _this = this;
      var index = this.adapter.getElementIndex(listItem);
      if (index < 0) {
        return;
      }
      this.adapter.notifySelected({
        index
      });
      var skipRestoreFocus = this.adapter.getAttributeFromElementAtIndex(index, strings.SKIP_RESTORE_FOCUS) === "true";
      this.adapter.closeSurface(skipRestoreFocus);
      this.closeAnimationEndTimerId = setTimeout(function() {
        var recomputedIndex = _this.adapter.getElementIndex(listItem);
        if (recomputedIndex >= 0 && _this.adapter.isSelectableItemAtIndex(recomputedIndex)) {
          _this.setSelectedIndex(recomputedIndex);
        }
      }, MDCMenuSurfaceFoundation.numbers.TRANSITION_CLOSE_DURATION);
    };
    MDCMenuFoundation2.prototype.handleMenuSurfaceOpened = function() {
      switch (this.defaultFocusState) {
        case DefaultFocusState.FIRST_ITEM:
          this.adapter.focusItemAtIndex(0);
          break;
        case DefaultFocusState.LAST_ITEM:
          this.adapter.focusItemAtIndex(this.adapter.getMenuItemCount() - 1);
          break;
        case DefaultFocusState.NONE:
          break;
        default:
          this.adapter.focusListRoot();
          break;
      }
    };
    MDCMenuFoundation2.prototype.setDefaultFocusState = function(focusState) {
      this.defaultFocusState = focusState;
    };
    MDCMenuFoundation2.prototype.getSelectedIndex = function() {
      return this.selectedIndex;
    };
    MDCMenuFoundation2.prototype.setSelectedIndex = function(index) {
      this.validatedIndex(index);
      if (!this.adapter.isSelectableItemAtIndex(index)) {
        throw new Error("MDCMenuFoundation: No selection group at specified index.");
      }
      var prevSelectedIndex = this.adapter.getSelectedSiblingOfItemAtIndex(index);
      if (prevSelectedIndex >= 0) {
        this.adapter.removeAttributeFromElementAtIndex(prevSelectedIndex, strings.ARIA_CHECKED_ATTR);
        this.adapter.removeClassFromElementAtIndex(prevSelectedIndex, cssClasses.MENU_SELECTED_LIST_ITEM);
      }
      this.adapter.addClassToElementAtIndex(index, cssClasses.MENU_SELECTED_LIST_ITEM);
      this.adapter.addAttributeToElementAtIndex(index, strings.ARIA_CHECKED_ATTR, "true");
      this.selectedIndex = index;
    };
    MDCMenuFoundation2.prototype.setEnabled = function(index, isEnabled) {
      this.validatedIndex(index);
      if (isEnabled) {
        this.adapter.removeClassFromElementAtIndex(index, cssClasses$2.LIST_ITEM_DISABLED_CLASS);
        this.adapter.addAttributeToElementAtIndex(index, strings.ARIA_DISABLED_ATTR, "false");
      } else {
        this.adapter.addClassToElementAtIndex(index, cssClasses$2.LIST_ITEM_DISABLED_CLASS);
        this.adapter.addAttributeToElementAtIndex(index, strings.ARIA_DISABLED_ATTR, "true");
      }
    };
    MDCMenuFoundation2.prototype.validatedIndex = function(index) {
      var menuSize = this.adapter.getMenuItemCount();
      var isIndexInRange = index >= 0 && index < menuSize;
      if (!isIndexInRange) {
        throw new Error("MDCMenuFoundation: No list item at specified index.");
      }
    };
    return MDCMenuFoundation2;
  }(MDCFoundation)
);
var MDCMenu = (
  /** @class */
  function(_super) {
    __extends(MDCMenu2, _super);
    function MDCMenu2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    MDCMenu2.attachTo = function(root) {
      return new MDCMenu2(root);
    };
    MDCMenu2.prototype.initialize = function(menuSurfaceFactory, listFactory) {
      if (menuSurfaceFactory === void 0) {
        menuSurfaceFactory = function(el) {
          return new MDCMenuSurface(el);
        };
      }
      if (listFactory === void 0) {
        listFactory = function(el) {
          return new MDCList(el);
        };
      }
      this.menuSurfaceFactory = menuSurfaceFactory;
      this.listFactory = listFactory;
    };
    MDCMenu2.prototype.initialSyncWithDOM = function() {
      var _this = this;
      this.menuSurface = this.menuSurfaceFactory(this.root);
      var list = this.root.querySelector(strings.LIST_SELECTOR);
      if (list) {
        this.list = this.listFactory(list);
        this.list.wrapFocus = true;
      } else {
        this.list = null;
      }
      this.handleKeydown = function(evt) {
        _this.foundation.handleKeydown(evt);
      };
      this.handleItemAction = function(evt) {
        _this.foundation.handleItemAction(_this.items[evt.detail.index]);
      };
      this.handleMenuSurfaceOpened = function() {
        _this.foundation.handleMenuSurfaceOpened();
      };
      this.menuSurface.listen(MDCMenuSurfaceFoundation.strings.OPENED_EVENT, this.handleMenuSurfaceOpened);
      this.listen("keydown", this.handleKeydown);
      this.listen(MDCListFoundation.strings.ACTION_EVENT, this.handleItemAction);
    };
    MDCMenu2.prototype.destroy = function() {
      if (this.list) {
        this.list.destroy();
      }
      this.menuSurface.destroy();
      this.menuSurface.unlisten(MDCMenuSurfaceFoundation.strings.OPENED_EVENT, this.handleMenuSurfaceOpened);
      this.unlisten("keydown", this.handleKeydown);
      this.unlisten(MDCListFoundation.strings.ACTION_EVENT, this.handleItemAction);
      _super.prototype.destroy.call(this);
    };
    Object.defineProperty(MDCMenu2.prototype, "open", {
      get: function() {
        return this.menuSurface.isOpen();
      },
      set: function(value) {
        if (value) {
          this.menuSurface.open();
        } else {
          this.menuSurface.close();
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenu2.prototype, "wrapFocus", {
      get: function() {
        return this.list ? this.list.wrapFocus : false;
      },
      set: function(value) {
        if (this.list) {
          this.list.wrapFocus = value;
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenu2.prototype, "hasTypeahead", {
      /**
       * Sets whether the menu has typeahead functionality.
       * @param value Whether typeahead is enabled.
       */
      set: function(value) {
        if (this.list) {
          this.list.hasTypeahead = value;
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenu2.prototype, "typeaheadInProgress", {
      /**
       * @return Whether typeahead logic is currently matching some user prefix.
       */
      get: function() {
        return this.list ? this.list.typeaheadInProgress : false;
      },
      enumerable: false,
      configurable: true
    });
    MDCMenu2.prototype.typeaheadMatchItem = function(nextChar, startingIndex) {
      if (this.list) {
        return this.list.typeaheadMatchItem(nextChar, startingIndex);
      }
      return -1;
    };
    MDCMenu2.prototype.layout = function() {
      if (this.list) {
        this.list.layout();
      }
    };
    Object.defineProperty(MDCMenu2.prototype, "items", {
      /**
       * Return the items within the menu. Note that this only contains the set of elements within
       * the items container that are proper list items, and not supplemental / presentational DOM
       * elements.
       */
      get: function() {
        return this.list ? this.list.listElements : [];
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenu2.prototype, "singleSelection", {
      /**
       * Turns on/off the underlying list's single selection mode. Used mainly
       * by select menu.
       *
       * @param singleSelection Whether to enable single selection mode.
       */
      set: function(singleSelection) {
        if (this.list) {
          this.list.singleSelection = singleSelection;
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenu2.prototype, "selectedIndex", {
      /**
       * Retrieves the selected index. Only applicable to select menus.
       * @return The selected index, which is a number for single selection and
       *     radio lists, and an array of numbers for checkbox lists.
       */
      get: function() {
        return this.list ? this.list.selectedIndex : numbers$2.UNSET_INDEX;
      },
      /**
       * Sets the selected index of the list. Only applicable to select menus.
       * @param index The selected index, which is a number for single selection and
       *     radio lists, and an array of numbers for checkbox lists.
       */
      set: function(index) {
        if (this.list) {
          this.list.selectedIndex = index;
        }
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(MDCMenu2.prototype, "quickOpen", {
      set: function(quickOpen) {
        this.menuSurface.quickOpen = quickOpen;
      },
      enumerable: false,
      configurable: true
    });
    MDCMenu2.prototype.setDefaultFocusState = function(focusState) {
      this.foundation.setDefaultFocusState(focusState);
    };
    MDCMenu2.prototype.setAnchorCorner = function(corner) {
      this.menuSurface.setAnchorCorner(corner);
    };
    MDCMenu2.prototype.setAnchorMargin = function(margin) {
      this.menuSurface.setAnchorMargin(margin);
    };
    MDCMenu2.prototype.setSelectedIndex = function(index) {
      this.foundation.setSelectedIndex(index);
    };
    MDCMenu2.prototype.setEnabled = function(index, isEnabled) {
      this.foundation.setEnabled(index, isEnabled);
    };
    MDCMenu2.prototype.getOptionByIndex = function(index) {
      var items = this.items;
      if (index < items.length) {
        return this.items[index];
      } else {
        return null;
      }
    };
    MDCMenu2.prototype.getPrimaryTextAtIndex = function(index) {
      var item = this.getOptionByIndex(index);
      if (item && this.list) {
        return this.list.getPrimaryText(item) || "";
      }
      return "";
    };
    MDCMenu2.prototype.setFixedPosition = function(isFixed) {
      this.menuSurface.setFixedPosition(isFixed);
    };
    MDCMenu2.prototype.setIsHoisted = function(isHoisted) {
      this.menuSurface.setIsHoisted(isHoisted);
    };
    MDCMenu2.prototype.setAbsolutePosition = function(x, y) {
      this.menuSurface.setAbsolutePosition(x, y);
    };
    MDCMenu2.prototype.setAnchorElement = function(element) {
      this.menuSurface.anchorElement = element;
    };
    MDCMenu2.prototype.getDefaultFoundation = function() {
      var _this = this;
      var adapter = {
        addClassToElementAtIndex: function(index, className) {
          var list = _this.items;
          list[index].classList.add(className);
        },
        removeClassFromElementAtIndex: function(index, className) {
          var list = _this.items;
          list[index].classList.remove(className);
        },
        addAttributeToElementAtIndex: function(index, attr, value) {
          var list = _this.items;
          list[index].setAttribute(attr, value);
        },
        removeAttributeFromElementAtIndex: function(index, attr) {
          var list = _this.items;
          list[index].removeAttribute(attr);
        },
        getAttributeFromElementAtIndex: function(index, attr) {
          var list = _this.items;
          return list[index].getAttribute(attr);
        },
        elementContainsClass: function(element, className) {
          return element.classList.contains(className);
        },
        closeSurface: function(skipRestoreFocus) {
          _this.menuSurface.close(skipRestoreFocus);
        },
        getElementIndex: function(element) {
          return _this.items.indexOf(element);
        },
        notifySelected: function(evtData) {
          _this.emit(strings.SELECTED_EVENT, {
            index: evtData.index,
            item: _this.items[evtData.index]
          });
        },
        getMenuItemCount: function() {
          return _this.items.length;
        },
        focusItemAtIndex: function(index) {
          _this.items[index].focus();
        },
        focusListRoot: function() {
          _this.root.querySelector(strings.LIST_SELECTOR).focus();
        },
        isSelectableItemAtIndex: function(index) {
          return !!closest(_this.items[index], "." + cssClasses.MENU_SELECTION_GROUP);
        },
        getSelectedSiblingOfItemAtIndex: function(index) {
          var selectionGroupEl = closest(_this.items[index], "." + cssClasses.MENU_SELECTION_GROUP);
          var selectedItemEl = selectionGroupEl.querySelector("." + cssClasses.MENU_SELECTED_LIST_ITEM);
          return selectedItemEl ? _this.items.indexOf(selectedItemEl) : -1;
        }
      };
      return new MDCMenuFoundation(adapter);
    };
    return MDCMenu2;
  }(MDCComponent)
);

export {
  KEY,
  normalizeKey,
  strings$1,
  Corner,
  MDCMenuSurface,
  strings,
  DefaultFocusState,
  MDCMenu
};
/*! Bundled license information:

@bci-web-core/web-components/dist/esm/component-655cd5b3.js:
  (**
   * @license
   * Copyright 2018 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   *)
  (**
   * @license
   * Copyright 2020 Google Inc.
   *
   * Permission is hereby granted, free of charge, to any person obtaining a copy
   * of this software and associated documentation files (the "Software"), to deal
   * in the Software without restriction, including without limitation the rights
   * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
   * copies of the Software, and to permit persons to whom the Software is
   * furnished to do so, subject to the following conditions:
   *
   * The above copyright notice and this permission notice shall be included in
   * all copies or substantial portions of the Software.
   *
   * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
   * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
   * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
   * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
   * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
   * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
   * THE SOFTWARE.
   *)
*/
//# sourceMappingURL=chunk-2HOK7EDV.js.map
