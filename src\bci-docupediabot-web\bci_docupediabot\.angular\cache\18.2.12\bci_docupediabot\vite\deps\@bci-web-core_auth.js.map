{"version": 3, "sources": ["../../../../../../node_modules/rfc4648/lib/rfc4648.js", "../../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs", "../../../../../../node_modules/angular-auth-oidc-client/fesm2022/angular-auth-oidc-client.mjs", "../../../../../../node_modules/@bci-web-core/auth/fesm2022/bci-web-core-auth.mjs"], "sourcesContent": ["/* eslint-disable @typescript-eslint/strict-boolean-expressions */\nfunction parse(string, encoding, opts) {\n  var _opts$out;\n  if (opts === void 0) {\n    opts = {};\n  }\n\n  // Build the character lookup table:\n  if (!encoding.codes) {\n    encoding.codes = {};\n    for (var i = 0; i < encoding.chars.length; ++i) {\n      encoding.codes[encoding.chars[i]] = i;\n    }\n  } // The string must have a whole number of bytes:\n\n  if (!opts.loose && string.length * encoding.bits & 7) {\n    throw new SyntaxError('Invalid padding');\n  } // Count the padding bytes:\n\n  var end = string.length;\n  while (string[end - 1] === '=') {\n    --end; // If we get a whole number of bytes, there is too much padding:\n\n    if (!opts.loose && !((string.length - end) * encoding.bits & 7)) {\n      throw new SyntaxError('Invalid padding');\n    }\n  } // Allocate the output:\n\n  var out = new ((_opts$out = opts.out) != null ? _opts$out : Uint8Array)(end * encoding.bits / 8 | 0); // Parse the data:\n\n  var bits = 0; // Number of bits currently in the buffer\n\n  var buffer = 0; // Bits waiting to be written out, MSB first\n\n  var written = 0; // Next byte to write\n\n  for (var _i = 0; _i < end; ++_i) {\n    // Read one character from the string:\n    var value = encoding.codes[string[_i]];\n    if (value === undefined) {\n      throw new SyntaxError('Invalid character ' + string[_i]);\n    } // Append the bits to the buffer:\n\n    buffer = buffer << encoding.bits | value;\n    bits += encoding.bits; // Write out some bits if the buffer has a byte's worth:\n\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 0xff & buffer >> bits;\n    }\n  } // Verify that we have received just enough bits:\n\n  if (bits >= encoding.bits || 0xff & buffer << 8 - bits) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n  return out;\n}\nfunction stringify(data, encoding, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  var _opts = opts,\n    _opts$pad = _opts.pad,\n    pad = _opts$pad === void 0 ? true : _opts$pad;\n  var mask = (1 << encoding.bits) - 1;\n  var out = '';\n  var bits = 0; // Number of bits currently in the buffer\n\n  var buffer = 0; // Bits waiting to be written out, MSB first\n\n  for (var i = 0; i < data.length; ++i) {\n    // Slurp data into the buffer:\n    buffer = buffer << 8 | 0xff & data[i];\n    bits += 8; // Write out as much as we can:\n\n    while (bits > encoding.bits) {\n      bits -= encoding.bits;\n      out += encoding.chars[mask & buffer >> bits];\n    }\n  } // Partial character:\n\n  if (bits) {\n    out += encoding.chars[mask & buffer << encoding.bits - bits];\n  } // Add padding characters until we hit a byte boundary:\n\n  if (pad) {\n    while (out.length * encoding.bits & 7) {\n      out += '=';\n    }\n  }\n  return out;\n}\n\n/* eslint-disable @typescript-eslint/strict-boolean-expressions */\nvar base16Encoding = {\n  chars: '0123456789ABCDEF',\n  bits: 4\n};\nvar base32Encoding = {\n  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',\n  bits: 5\n};\nvar base32HexEncoding = {\n  chars: '0123456789ABCDEFGHIJKLMNOPQRSTUV',\n  bits: 5\n};\nvar base64Encoding = {\n  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n  bits: 6\n};\nvar base64UrlEncoding = {\n  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bits: 6\n};\nvar base16 = {\n  parse: function parse$1(string, opts) {\n    return parse(string.toUpperCase(), base16Encoding, opts);\n  },\n  stringify: function stringify$1(data, opts) {\n    return stringify(data, base16Encoding, opts);\n  }\n};\nvar base32 = {\n  parse: function parse$1(string, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    return parse(opts.loose ? string.toUpperCase().replace(/0/g, 'O').replace(/1/g, 'L').replace(/8/g, 'B') : string, base32Encoding, opts);\n  },\n  stringify: function stringify$1(data, opts) {\n    return stringify(data, base32Encoding, opts);\n  }\n};\nvar base32hex = {\n  parse: function parse$1(string, opts) {\n    return parse(string, base32HexEncoding, opts);\n  },\n  stringify: function stringify$1(data, opts) {\n    return stringify(data, base32HexEncoding, opts);\n  }\n};\nvar base64 = {\n  parse: function parse$1(string, opts) {\n    return parse(string, base64Encoding, opts);\n  },\n  stringify: function stringify$1(data, opts) {\n    return stringify(data, base64Encoding, opts);\n  }\n};\nvar base64url = {\n  parse: function parse$1(string, opts) {\n    return parse(string, base64UrlEncoding, opts);\n  },\n  stringify: function stringify$1(data, opts) {\n    return stringify(data, base64UrlEncoding, opts);\n  }\n};\nvar codec = {\n  parse: parse,\n  stringify: stringify\n};\nexport { base16, base32, base32hex, base64, base64url, codec };", "/**\n * @license Angular v18.2.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError, ɵgetOutputDestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  constructor(source) {\n    this.source = source;\n    this.destroyed = false;\n    this.destroyRef = inject(DestroyRef);\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new ɵRuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nfunction outputToObservable(ref) {\n  const destroyRef = ɵgetOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { outputFromObservable, outputToObservable, takeUntilDestroyed, toObservable, toSignal };\n", "import { CommonModule, DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport { provideHttpClient, withInterceptorsFromDi, HttpParams, HttpClient, HttpHeaders, HttpErrorResponse, HttpResponse } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, makeEnvironmentProviders, NgModule, inject, NgZone, PLATFORM_ID, RendererFactory2 } from '@angular/core';\nimport { of, forkJoin, from, throwError, timer, ReplaySubject, BehaviorSubject, Observable, Subject, TimeoutError } from 'rxjs';\nimport { map, mergeMap, tap, switchMap, retryWhen, catchError, distinctUntilChanged, retry, concatMap, finalize, take, timeout } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { base64url } from 'rfc4648';\nimport { toSignal } from '@angular/core/rxjs-interop';\nclass OpenIdConfigLoader {}\nclass StsConfigLoader {}\nclass StsConfigStaticLoader {\n  constructor(passedConfigs) {\n    this.passedConfigs = passedConfigs;\n  }\n  loadConfigs() {\n    if (Array.isArray(this.passedConfigs)) {\n      return of(this.passedConfigs);\n    }\n    return of([this.passedConfigs]);\n  }\n}\nclass StsConfigHttpLoader {\n  constructor(configs$) {\n    this.configs$ = configs$;\n  }\n  loadConfigs() {\n    if (Array.isArray(this.configs$)) {\n      return forkJoin(this.configs$);\n    }\n    const singleConfigOrArray = this.configs$;\n    return singleConfigOrArray.pipe(map(value => {\n      if (Array.isArray(value)) {\n        return value;\n      }\n      return [value];\n    }));\n  }\n}\nfunction createStaticLoader(passedConfig) {\n  if (!passedConfig?.config) {\n    throw new Error('No config provided!');\n  }\n  return new StsConfigStaticLoader(passedConfig.config);\n}\nconst PASSED_CONFIG = new InjectionToken('PASSED_CONFIG');\n\n/**\n * Implement this class-interface to create a custom logger service.\n */\nclass AbstractLoggerService {\n  static {\n    this.ɵfac = function AbstractLoggerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbstractLoggerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbstractLoggerService,\n      factory: AbstractLoggerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbstractLoggerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ConsoleLoggerService {\n  logError(message, ...args) {\n    console.error(message, ...args);\n  }\n  logWarning(message, ...args) {\n    console.warn(message, ...args);\n  }\n  logDebug(message, ...args) {\n    console.debug(message, ...args);\n  }\n  static {\n    this.ɵfac = function ConsoleLoggerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConsoleLoggerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConsoleLoggerService,\n      factory: ConsoleLoggerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConsoleLoggerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Implement this class-interface to create a custom storage.\n */\nclass AbstractSecurityStorage {\n  static {\n    this.ɵfac = function AbstractSecurityStorage_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AbstractSecurityStorage)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AbstractSecurityStorage,\n      factory: AbstractSecurityStorage.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbstractSecurityStorage, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass DefaultSessionStorageService {\n  read(key) {\n    return sessionStorage.getItem(key);\n  }\n  write(key, value) {\n    sessionStorage.setItem(key, value);\n  }\n  remove(key) {\n    sessionStorage.removeItem(key);\n  }\n  clear() {\n    sessionStorage.clear();\n  }\n  static {\n    this.ɵfac = function DefaultSessionStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DefaultSessionStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DefaultSessionStorageService,\n      factory: DefaultSessionStorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultSessionStorageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction provideAuth(passedConfig) {\n  return makeEnvironmentProviders([..._provideAuth(passedConfig)]);\n}\nfunction _provideAuth(passedConfig) {\n  return [\n  // Make the PASSED_CONFIG available through injection\n  {\n    provide: PASSED_CONFIG,\n    useValue: passedConfig\n  },\n  // Create the loader: Either the one getting passed or a static one\n  passedConfig?.loader || {\n    provide: StsConfigLoader,\n    useFactory: createStaticLoader,\n    deps: [PASSED_CONFIG]\n  }, {\n    provide: AbstractSecurityStorage,\n    useClass: DefaultSessionStorageService\n  }, {\n    provide: AbstractLoggerService,\n    useClass: ConsoleLoggerService\n  }];\n}\nclass AuthModule {\n  static forRoot(passedConfig) {\n    return {\n      ngModule: AuthModule,\n      providers: [..._provideAuth(passedConfig)]\n    };\n  }\n  static {\n    this.ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AuthModule,\n      imports: [CommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [provideHttpClient(withInterceptorsFromDi())],\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      exports: [],\n      imports: [CommonModule],\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[LogLevel[\"None\"] = 0] = \"None\";\n  LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\n  LogLevel[LogLevel[\"Warn\"] = 2] = \"Warn\";\n  LogLevel[LogLevel[\"Error\"] = 3] = \"Error\";\n})(LogLevel || (LogLevel = {}));\nclass LoggerService {\n  constructor() {\n    this.abstractLoggerService = inject(AbstractLoggerService);\n  }\n  logError(configuration, message, ...args) {\n    if (this.loggingIsTurnedOff(configuration)) {\n      return;\n    }\n    const {\n      configId\n    } = configuration;\n    const messageToLog = this.isObject(message) ? JSON.stringify(message) : message;\n    if (!!args && !!args.length) {\n      this.abstractLoggerService.logError(`[ERROR] ${configId} - ${messageToLog}`, ...args);\n    } else {\n      this.abstractLoggerService.logError(`[ERROR] ${configId} - ${messageToLog}`);\n    }\n  }\n  logWarning(configuration, message, ...args) {\n    if (!this.logLevelIsSet(configuration)) {\n      return;\n    }\n    if (this.loggingIsTurnedOff(configuration)) {\n      return;\n    }\n    if (!this.currentLogLevelIsEqualOrSmallerThan(configuration, LogLevel.Warn)) {\n      return;\n    }\n    const {\n      configId\n    } = configuration;\n    const messageToLog = this.isObject(message) ? JSON.stringify(message) : message;\n    if (!!args && !!args.length) {\n      this.abstractLoggerService.logWarning(`[WARN] ${configId} - ${messageToLog}`, ...args);\n    } else {\n      this.abstractLoggerService.logWarning(`[WARN] ${configId} - ${messageToLog}`);\n    }\n  }\n  logDebug(configuration, message, ...args) {\n    if (!configuration) {\n      return;\n    }\n    if (!this.logLevelIsSet(configuration)) {\n      return;\n    }\n    if (this.loggingIsTurnedOff(configuration)) {\n      return;\n    }\n    if (!this.currentLogLevelIsEqualOrSmallerThan(configuration, LogLevel.Debug)) {\n      return;\n    }\n    const {\n      configId\n    } = configuration;\n    const messageToLog = this.isObject(message) ? JSON.stringify(message) : message;\n    if (!!args && !!args.length) {\n      this.abstractLoggerService.logDebug(`[DEBUG] ${configId} - ${messageToLog}`, ...args);\n    } else {\n      this.abstractLoggerService.logDebug(`[DEBUG] ${configId} - ${messageToLog}`);\n    }\n  }\n  currentLogLevelIsEqualOrSmallerThan(configuration, logLevelToCompare) {\n    const {\n      logLevel\n    } = configuration || {};\n    if (!logLevel) {\n      return false;\n    }\n    return logLevel <= logLevelToCompare;\n  }\n  logLevelIsSet(configuration) {\n    const {\n      logLevel\n    } = configuration || {};\n    if (logLevel === null) {\n      return false;\n    }\n    return logLevel !== undefined;\n  }\n  loggingIsTurnedOff(configuration) {\n    const {\n      logLevel\n    } = configuration || {};\n    return logLevel === LogLevel.None;\n  }\n  isObject(possibleObject) {\n    return Object.prototype.toString.call(possibleObject) === '[object Object]';\n  }\n  static {\n    this.ɵfac = function LoggerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoggerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LoggerService,\n      factory: LoggerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoggerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass BrowserStorageService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.abstractSecurityStorage = inject(AbstractSecurityStorage);\n  }\n  read(key, configuration) {\n    const {\n      configId\n    } = configuration;\n    if (!configId) {\n      this.loggerService.logDebug(configuration, `Wanted to read '${key}' but configId was '${configId}'`);\n      return null;\n    }\n    if (!this.hasStorage()) {\n      this.loggerService.logDebug(configuration, `Wanted to read '${key}' but Storage was undefined`);\n      return null;\n    }\n    const storedConfig = this.abstractSecurityStorage.read(configId);\n    if (!storedConfig) {\n      return null;\n    }\n    return JSON.parse(storedConfig);\n  }\n  write(value, configuration) {\n    const {\n      configId\n    } = configuration;\n    if (!configId) {\n      this.loggerService.logDebug(configuration, `Wanted to write but configId was '${configId}'`);\n      return false;\n    }\n    if (!this.hasStorage()) {\n      this.loggerService.logDebug(configuration, `Wanted to write but Storage was falsy`);\n      return false;\n    }\n    value = value || null;\n    this.abstractSecurityStorage.write(configId, JSON.stringify(value));\n    return true;\n  }\n  remove(key, configuration) {\n    if (!this.hasStorage()) {\n      this.loggerService.logDebug(configuration, `Wanted to remove '${key}' but Storage was falsy`);\n      return false;\n    }\n    // const storage = this.getStorage(configuration);\n    // if (!storage) {\n    //   this.loggerService.logDebug(configuration, `Wanted to write '${key}' but Storage was falsy`);\n    //   return false;\n    // }\n    this.abstractSecurityStorage.remove(key);\n    return true;\n  }\n  // TODO THIS STORAGE WANTS AN ID BUT CLEARS EVERYTHING\n  clear(configuration) {\n    if (!this.hasStorage()) {\n      this.loggerService.logDebug(configuration, `Wanted to clear storage but Storage was falsy`);\n      return false;\n    }\n    // const storage = this.getStorage(configuration);\n    // if (!storage) {\n    //   this.loggerService.logDebug(configuration, `Wanted to clear storage but Storage was falsy`);\n    //   return false;\n    // }\n    this.abstractSecurityStorage.clear();\n    return true;\n  }\n  hasStorage() {\n    return typeof Storage !== 'undefined';\n  }\n  static {\n    this.ɵfac = function BrowserStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BrowserStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserStorageService,\n      factory: BrowserStorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserStorageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass StoragePersistenceService {\n  constructor() {\n    this.browserStorageService = inject(BrowserStorageService);\n  }\n  read(key, config) {\n    const storedConfig = this.browserStorageService.read(key, config) || {};\n    return storedConfig[key];\n  }\n  write(key, value, config) {\n    const storedConfig = this.browserStorageService.read(key, config) || {};\n    storedConfig[key] = value;\n    return this.browserStorageService.write(storedConfig, config);\n  }\n  remove(key, config) {\n    const storedConfig = this.browserStorageService.read(key, config) || {};\n    delete storedConfig[key];\n    this.browserStorageService.write(storedConfig, config);\n  }\n  clear(config) {\n    this.browserStorageService.clear(config);\n  }\n  resetStorageFlowData(config) {\n    this.remove('session_state', config);\n    this.remove('storageSilentRenewRunning', config);\n    this.remove('storageCodeFlowInProgress', config);\n    this.remove('codeVerifier', config);\n    this.remove('userData', config);\n    this.remove('storageCustomParamsAuthRequest', config);\n    this.remove('access_token_expires_at', config);\n    this.remove('storageCustomParamsRefresh', config);\n    this.remove('storageCustomParamsEndSession', config);\n    this.remove('reusable_refresh_token', config);\n  }\n  resetAuthStateInStorage(config) {\n    this.remove('authzData', config);\n    this.remove('reusable_refresh_token', config);\n    this.remove('authnResult', config);\n  }\n  getAccessToken(config) {\n    return this.read('authzData', config);\n  }\n  getIdToken(config) {\n    return this.read('authnResult', config)?.id_token;\n  }\n  getRefreshToken(config) {\n    const refreshToken = this.read('authnResult', config)?.refresh_token;\n    if (!refreshToken && config.allowUnsafeReuseRefreshToken) {\n      return this.read('reusable_refresh_token', config);\n    }\n    return refreshToken;\n  }\n  getAuthenticationResult(config) {\n    return this.read('authnResult', config);\n  }\n  static {\n    this.ɵfac = function StoragePersistenceService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StoragePersistenceService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: StoragePersistenceService,\n      factory: StoragePersistenceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StoragePersistenceService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst STORAGE_KEY = 'redirect';\nclass AutoLoginService {\n  constructor() {\n    this.storageService = inject(StoragePersistenceService);\n    this.router = inject(Router);\n  }\n  checkSavedRedirectRouteAndNavigate(config) {\n    if (!config) {\n      return;\n    }\n    const savedRouteForRedirect = this.getStoredRedirectRoute(config);\n    if (savedRouteForRedirect != null) {\n      this.deleteStoredRedirectRoute(config);\n      this.router.navigateByUrl(savedRouteForRedirect);\n    }\n  }\n  /**\n   * Saves the redirect URL to storage.\n   *\n   * @param config The OpenId configuration.\n   * @param url The redirect URL to save.\n   */\n  saveRedirectRoute(config, url) {\n    if (!config) {\n      return;\n    }\n    this.storageService.write(STORAGE_KEY, url, config);\n  }\n  /**\n   * Gets the stored redirect URL from storage.\n   */\n  getStoredRedirectRoute(config) {\n    return this.storageService.read(STORAGE_KEY, config);\n  }\n  /**\n   * Removes the redirect URL from storage.\n   */\n  deleteStoredRedirectRoute(config) {\n    this.storageService.remove(STORAGE_KEY, config);\n  }\n  static {\n    this.ɵfac = function AutoLoginService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutoLoginService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutoLoginService,\n      factory: AutoLoginService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoLoginService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass FlowHelper {\n  isCurrentFlowCodeFlow(configuration) {\n    return this.currentFlowIs('code', configuration);\n  }\n  isCurrentFlowAnyImplicitFlow(configuration) {\n    return this.isCurrentFlowImplicitFlowWithAccessToken(configuration) || this.isCurrentFlowImplicitFlowWithoutAccessToken(configuration);\n  }\n  isCurrentFlowCodeFlowWithRefreshTokens(configuration) {\n    if (!configuration) {\n      return false;\n    }\n    const {\n      useRefreshToken\n    } = configuration;\n    return this.isCurrentFlowCodeFlow(configuration) && Boolean(useRefreshToken);\n  }\n  isCurrentFlowImplicitFlowWithAccessToken(configuration) {\n    return this.currentFlowIs('id_token token', configuration);\n  }\n  currentFlowIs(flowTypes, configuration) {\n    const {\n      responseType\n    } = configuration;\n    if (Array.isArray(flowTypes)) {\n      return flowTypes.some(x => responseType === x);\n    }\n    return responseType === flowTypes;\n  }\n  isCurrentFlowImplicitFlowWithoutAccessToken(configuration) {\n    return this.currentFlowIs('id_token', configuration);\n  }\n  static {\n    this.ɵfac = function FlowHelper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FlowHelper)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FlowHelper,\n      factory: FlowHelper.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FlowHelper, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass CryptoService {\n  constructor() {\n    this.document = inject(DOCUMENT);\n  }\n  getCrypto() {\n    // support for IE,  (window.crypto || window.msCrypto)\n    return this.document.defaultView?.crypto || this.document.defaultView?.msCrypto;\n  }\n  static {\n    this.ɵfac = function CryptoService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CryptoService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CryptoService,\n      factory: CryptoService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CryptoService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RandomService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.cryptoService = inject(CryptoService);\n  }\n  createRandom(requiredLength, configuration) {\n    if (requiredLength <= 0) {\n      return '';\n    }\n    if (requiredLength > 0 && requiredLength < 7) {\n      this.loggerService.logWarning(configuration, `RandomService called with ${requiredLength} but 7 chars is the minimum, returning 10 chars`);\n      requiredLength = 10;\n    }\n    const length = requiredLength - 6;\n    const arr = new Uint8Array(Math.floor(length / 2));\n    const crypto = this.cryptoService.getCrypto();\n    if (crypto) {\n      crypto.getRandomValues(arr);\n    }\n    return Array.from(arr, this.toHex).join('') + this.randomString(7);\n  }\n  toHex(dec) {\n    return ('0' + dec.toString(16)).substr(-2);\n  }\n  randomString(length) {\n    let result = '';\n    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    const values = new Uint32Array(length);\n    const crypto = this.cryptoService.getCrypto();\n    if (crypto) {\n      crypto.getRandomValues(values);\n      for (let i = 0; i < length; i++) {\n        result += characters[values[i] % characters.length];\n      }\n    }\n    return result;\n  }\n  static {\n    this.ɵfac = function RandomService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RandomService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RandomService,\n      factory: RandomService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RandomService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass FlowsDataService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.randomService = inject(RandomService);\n  }\n  createNonce(configuration) {\n    const nonce = this.randomService.createRandom(40, configuration);\n    this.loggerService.logDebug(configuration, 'Nonce created. nonce:' + nonce);\n    this.setNonce(nonce, configuration);\n    return nonce;\n  }\n  setNonce(nonce, configuration) {\n    this.storagePersistenceService.write('authNonce', nonce, configuration);\n  }\n  getAuthStateControl(configuration) {\n    if (!configuration) {\n      return '';\n    }\n    return this.storagePersistenceService.read('authStateControl', configuration);\n  }\n  setAuthStateControl(authStateControl, configuration) {\n    if (!configuration) {\n      return false;\n    }\n    return this.storagePersistenceService.write('authStateControl', authStateControl, configuration);\n  }\n  getExistingOrCreateAuthStateControl(configuration) {\n    let state = this.storagePersistenceService.read('authStateControl', configuration);\n    if (!state) {\n      state = this.randomService.createRandom(40, configuration);\n      this.storagePersistenceService.write('authStateControl', state, configuration);\n    }\n    return state;\n  }\n  setSessionState(sessionState, configuration) {\n    this.storagePersistenceService.write('session_state', sessionState, configuration);\n  }\n  resetStorageFlowData(configuration) {\n    this.storagePersistenceService.resetStorageFlowData(configuration);\n  }\n  getCodeVerifier(configuration) {\n    return this.storagePersistenceService.read('codeVerifier', configuration);\n  }\n  createCodeVerifier(configuration) {\n    const codeVerifier = this.randomService.createRandom(67, configuration);\n    this.storagePersistenceService.write('codeVerifier', codeVerifier, configuration);\n    return codeVerifier;\n  }\n  isCodeFlowInProgress(configuration) {\n    return !!this.storagePersistenceService.read('storageCodeFlowInProgress', configuration);\n  }\n  setCodeFlowInProgress(configuration) {\n    this.storagePersistenceService.write('storageCodeFlowInProgress', true, configuration);\n  }\n  resetCodeFlowInProgress(configuration) {\n    this.storagePersistenceService.write('storageCodeFlowInProgress', false, configuration);\n  }\n  isSilentRenewRunning(configuration) {\n    const {\n      configId,\n      silentRenewTimeoutInSeconds\n    } = configuration;\n    const storageObject = this.getSilentRenewRunningStorageEntry(configuration);\n    if (!storageObject) {\n      return false;\n    }\n    if (storageObject.state === 'not-running') {\n      return false;\n    }\n    const timeOutInMilliseconds = (silentRenewTimeoutInSeconds ?? 0) * 1000;\n    const dateOfLaunchedProcessUtc = Date.parse(storageObject.dateOfLaunchedProcessUtc);\n    const currentDateUtc = Date.parse(new Date().toISOString());\n    const elapsedTimeInMilliseconds = Math.abs(currentDateUtc - dateOfLaunchedProcessUtc);\n    const isProbablyStuck = elapsedTimeInMilliseconds > timeOutInMilliseconds;\n    if (isProbablyStuck) {\n      this.loggerService.logDebug(configuration, 'silent renew process is probably stuck, state will be reset.', configId);\n      this.resetSilentRenewRunning(configuration);\n      return false;\n    }\n    return storageObject.state === 'running';\n  }\n  setSilentRenewRunning(configuration) {\n    const storageObject = {\n      state: 'running',\n      dateOfLaunchedProcessUtc: new Date().toISOString()\n    };\n    this.storagePersistenceService.write('storageSilentRenewRunning', JSON.stringify(storageObject), configuration);\n  }\n  resetSilentRenewRunning(configuration) {\n    if (!configuration) {\n      return;\n    }\n    this.storagePersistenceService.write('storageSilentRenewRunning', '', configuration);\n  }\n  getSilentRenewRunningStorageEntry(configuration) {\n    const storageEntry = this.storagePersistenceService.read('storageSilentRenewRunning', configuration);\n    if (!storageEntry) {\n      return {\n        dateOfLaunchedProcessUtc: '',\n        state: 'not-running'\n      };\n    }\n    return JSON.parse(storageEntry);\n  }\n  static {\n    this.ɵfac = function FlowsDataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FlowsDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FlowsDataService,\n      factory: FlowsDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FlowsDataService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass JwtWindowCryptoService {\n  constructor() {\n    this.cryptoService = inject(CryptoService);\n  }\n  generateCodeChallenge(codeVerifier) {\n    return this.calcHash(codeVerifier).pipe(map(challengeRaw => this.base64UrlEncode(challengeRaw)));\n  }\n  generateAtHash(accessToken, algorithm) {\n    return this.calcHash(accessToken, algorithm).pipe(map(tokenHash => {\n      const substr = tokenHash.substr(0, tokenHash.length / 2);\n      const tokenHashBase64 = btoa(substr);\n      return tokenHashBase64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n    }));\n  }\n  calcHash(valueToHash, algorithm = 'SHA-256') {\n    const msgBuffer = new TextEncoder().encode(valueToHash);\n    return from(this.cryptoService.getCrypto().subtle.digest(algorithm, msgBuffer)).pipe(map(hashBuffer => {\n      const buffer = hashBuffer;\n      const hashArray = Array.from(new Uint8Array(buffer));\n      return this.toHashString(hashArray);\n    }));\n  }\n  toHashString(byteArray) {\n    let result = '';\n    for (const e of byteArray) {\n      result += String.fromCharCode(e);\n    }\n    return result;\n  }\n  base64UrlEncode(str) {\n    const base64 = btoa(str);\n    return base64.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n  }\n  static {\n    this.ɵfac = function JwtWindowCryptoService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || JwtWindowCryptoService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: JwtWindowCryptoService,\n      factory: JwtWindowCryptoService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwtWindowCryptoService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass UriEncoder {\n  encodeKey(key) {\n    return encodeURIComponent(key);\n  }\n  encodeValue(value) {\n    return encodeURIComponent(value);\n  }\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nconst CALLBACK_PARAMS_TO_CHECK = ['code', 'state', 'token', 'id_token'];\nconst AUTH0_ENDPOINT = 'auth0.com';\nclass UrlService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.flowHelper = inject(FlowHelper);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.jwtWindowCryptoService = inject(JwtWindowCryptoService);\n  }\n  getUrlParameter(urlToCheck, name) {\n    if (!urlToCheck) {\n      return '';\n    }\n    if (!name) {\n      return '';\n    }\n    name = name.replace(/[[]/, '\\\\[').replace(/[\\]]/, '\\\\]');\n    const regex = new RegExp('[\\\\?&#]' + name + '=([^&#]*)');\n    const results = regex.exec(urlToCheck);\n    return results === null ? '' : decodeURIComponent(results[1]);\n  }\n  isCallbackFromSts(currentUrl) {\n    return CALLBACK_PARAMS_TO_CHECK.some(x => !!this.getUrlParameter(currentUrl, x));\n  }\n  getRefreshSessionSilentRenewUrl(config, customParams) {\n    if (this.flowHelper.isCurrentFlowCodeFlow(config)) {\n      return this.createUrlCodeFlowWithSilentRenew(config, customParams);\n    }\n    return of(this.createUrlImplicitFlowWithSilentRenew(config, customParams));\n  }\n  getAuthorizeParUrl(requestUri, configuration) {\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    if (!authWellKnownEndPoints) {\n      this.loggerService.logError(configuration, 'authWellKnownEndpoints is undefined');\n      return null;\n    }\n    const authorizationEndpoint = authWellKnownEndPoints.authorizationEndpoint;\n    if (!authorizationEndpoint) {\n      this.loggerService.logError(configuration, `Can not create an authorize URL when authorizationEndpoint is '${authorizationEndpoint}'`);\n      return null;\n    }\n    const {\n      clientId\n    } = configuration;\n    if (!clientId) {\n      this.loggerService.logError(configuration, `getAuthorizeParUrl could not add clientId because it was: `, clientId);\n      return null;\n    }\n    const urlParts = authorizationEndpoint.split('?');\n    const authorizationUrl = urlParts[0];\n    const existingParams = urlParts[1];\n    let params = this.createHttpParams(existingParams);\n    params = params.set('request_uri', requestUri);\n    params = params.append('client_id', clientId);\n    return `${authorizationUrl}?${params}`;\n  }\n  getAuthorizeUrl(config, authOptions) {\n    if (!config) {\n      return of(null);\n    }\n    if (this.flowHelper.isCurrentFlowCodeFlow(config)) {\n      return this.createUrlCodeFlowAuthorize(config, authOptions);\n    }\n    return of(this.createUrlImplicitFlowAuthorize(config, authOptions) || '');\n  }\n  getEndSessionEndpoint(configuration) {\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    const endSessionEndpoint = authWellKnownEndPoints?.endSessionEndpoint;\n    if (!endSessionEndpoint) {\n      return {\n        url: '',\n        existingParams: ''\n      };\n    }\n    const urlParts = endSessionEndpoint.split('?');\n    const url = urlParts[0];\n    const existingParams = urlParts[1] ?? '';\n    return {\n      url,\n      existingParams\n    };\n  }\n  getEndSessionUrl(configuration, customParams) {\n    if (!configuration) {\n      return null;\n    }\n    const idToken = this.storagePersistenceService.getIdToken(configuration);\n    const {\n      customParamsEndSessionRequest\n    } = configuration;\n    const mergedParams = {\n      ...customParamsEndSessionRequest,\n      ...customParams\n    };\n    return this.createEndSessionUrl(idToken, configuration, mergedParams);\n  }\n  createRevocationEndpointBodyAccessToken(token, configuration) {\n    const clientId = this.getClientId(configuration);\n    if (!clientId) {\n      return null;\n    }\n    let params = this.createHttpParams();\n    params = params.set('client_id', clientId);\n    params = params.set('token', token);\n    params = params.set('token_type_hint', 'access_token');\n    return params.toString();\n  }\n  createRevocationEndpointBodyRefreshToken(token, configuration) {\n    const clientId = this.getClientId(configuration);\n    if (!clientId) {\n      return null;\n    }\n    let params = this.createHttpParams();\n    params = params.set('client_id', clientId);\n    params = params.set('token', token);\n    params = params.set('token_type_hint', 'refresh_token');\n    return params.toString();\n  }\n  getRevocationEndpointUrl(configuration) {\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    const revocationEndpoint = authWellKnownEndPoints?.revocationEndpoint;\n    if (!revocationEndpoint) {\n      return null;\n    }\n    const urlParts = revocationEndpoint.split('?');\n    return urlParts[0];\n  }\n  createBodyForCodeFlowCodeRequest(code, configuration, customTokenParams) {\n    const clientId = this.getClientId(configuration);\n    if (!clientId) {\n      return null;\n    }\n    let params = this.createHttpParams();\n    params = params.set('grant_type', 'authorization_code');\n    params = params.set('client_id', clientId);\n    if (!configuration.disablePkce) {\n      const codeVerifier = this.flowsDataService.getCodeVerifier(configuration);\n      if (!codeVerifier) {\n        this.loggerService.logError(configuration, `CodeVerifier is not set `, codeVerifier);\n        return null;\n      }\n      params = params.set('code_verifier', codeVerifier);\n    }\n    params = params.set('code', code);\n    if (customTokenParams) {\n      params = this.appendCustomParams({\n        ...customTokenParams\n      }, params);\n    }\n    const silentRenewUrl = this.getSilentRenewUrl(configuration);\n    if (this.flowsDataService.isSilentRenewRunning(configuration) && silentRenewUrl) {\n      params = params.set('redirect_uri', silentRenewUrl);\n      return params.toString();\n    }\n    const redirectUrl = this.getRedirectUrl(configuration);\n    if (!redirectUrl) {\n      return null;\n    }\n    params = params.set('redirect_uri', redirectUrl);\n    return params.toString();\n  }\n  createBodyForCodeFlowRefreshTokensRequest(refreshToken, configuration, customParamsRefresh) {\n    const clientId = this.getClientId(configuration);\n    if (!clientId) {\n      return null;\n    }\n    let params = this.createHttpParams();\n    params = params.set('grant_type', 'refresh_token');\n    params = params.set('client_id', clientId);\n    params = params.set('refresh_token', refreshToken);\n    if (customParamsRefresh) {\n      params = this.appendCustomParams({\n        ...customParamsRefresh\n      }, params);\n    }\n    return params.toString();\n  }\n  createBodyForParCodeFlowRequest(configuration, authOptions) {\n    const redirectUrl = this.getRedirectUrl(configuration, authOptions);\n    if (!redirectUrl) {\n      return of(null);\n    }\n    const state = this.flowsDataService.getExistingOrCreateAuthStateControl(configuration);\n    const nonce = this.flowsDataService.createNonce(configuration);\n    this.loggerService.logDebug(configuration, 'Authorize created. adding myautostate: ' + state);\n    // code_challenge with \"S256\"\n    const codeVerifier = this.flowsDataService.createCodeVerifier(configuration);\n    return this.jwtWindowCryptoService.generateCodeChallenge(codeVerifier).pipe(map(codeChallenge => {\n      const {\n        clientId,\n        responseType,\n        scope,\n        hdParam,\n        customParamsAuthRequest\n      } = configuration;\n      let params = this.createHttpParams('');\n      params = params.set('client_id', clientId ?? '');\n      params = params.append('redirect_uri', redirectUrl);\n      params = params.append('response_type', responseType ?? '');\n      params = params.append('scope', scope ?? '');\n      params = params.append('nonce', nonce);\n      params = params.append('state', state);\n      params = params.append('code_challenge', codeChallenge);\n      params = params.append('code_challenge_method', 'S256');\n      if (hdParam) {\n        params = params.append('hd', hdParam);\n      }\n      if (customParamsAuthRequest) {\n        params = this.appendCustomParams({\n          ...customParamsAuthRequest\n        }, params);\n      }\n      if (authOptions?.customParams) {\n        params = this.appendCustomParams({\n          ...authOptions.customParams\n        }, params);\n      }\n      return params.toString();\n    }));\n  }\n  getPostLogoutRedirectUrl(configuration) {\n    const {\n      postLogoutRedirectUri\n    } = configuration;\n    if (!postLogoutRedirectUri) {\n      this.loggerService.logError(configuration, `could not get postLogoutRedirectUri, was: `, postLogoutRedirectUri);\n      return null;\n    }\n    return postLogoutRedirectUri;\n  }\n  createEndSessionUrl(idTokenHint, configuration, customParamsEndSession) {\n    // Auth0 needs a special logout url\n    // See https://auth0.com/docs/api/authentication#logout\n    if (this.isAuth0Endpoint(configuration)) {\n      return this.composeAuth0Endpoint(configuration);\n    }\n    const {\n      url,\n      existingParams\n    } = this.getEndSessionEndpoint(configuration);\n    if (!url) {\n      return null;\n    }\n    let params = this.createHttpParams(existingParams);\n    if (!!idTokenHint) {\n      params = params.set('id_token_hint', idTokenHint);\n    }\n    const postLogoutRedirectUri = this.getPostLogoutRedirectUrl(configuration);\n    if (postLogoutRedirectUri) {\n      params = params.append('post_logout_redirect_uri', postLogoutRedirectUri);\n    }\n    if (customParamsEndSession) {\n      params = this.appendCustomParams({\n        ...customParamsEndSession\n      }, params);\n    }\n    return `${url}?${params}`;\n  }\n  createAuthorizeUrl(codeChallenge, redirectUrl, nonce, state, configuration, prompt, customRequestParams) {\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    const authorizationEndpoint = authWellKnownEndPoints?.authorizationEndpoint;\n    if (!authorizationEndpoint) {\n      this.loggerService.logError(configuration, `Can not create an authorize URL when authorizationEndpoint is '${authorizationEndpoint}'`);\n      return '';\n    }\n    const {\n      clientId,\n      responseType,\n      scope,\n      hdParam,\n      customParamsAuthRequest\n    } = configuration;\n    if (!clientId) {\n      this.loggerService.logError(configuration, `createAuthorizeUrl could not add clientId because it was: `, clientId);\n      return '';\n    }\n    if (!responseType) {\n      this.loggerService.logError(configuration, `createAuthorizeUrl could not add responseType because it was: `, responseType);\n      return '';\n    }\n    if (!scope) {\n      this.loggerService.logError(configuration, `createAuthorizeUrl could not add scope because it was: `, scope);\n      return '';\n    }\n    const urlParts = authorizationEndpoint.split('?');\n    const authorizationUrl = urlParts[0];\n    const existingParams = urlParts[1];\n    let params = this.createHttpParams(existingParams);\n    params = params.set('client_id', clientId);\n    params = params.append('redirect_uri', redirectUrl);\n    params = params.append('response_type', responseType);\n    params = params.append('scope', scope);\n    params = params.append('nonce', nonce);\n    params = params.append('state', state);\n    if (this.flowHelper.isCurrentFlowCodeFlow(configuration)) {\n      params = params.append('code_challenge', codeChallenge);\n      params = params.append('code_challenge_method', 'S256');\n    }\n    const mergedParams = {\n      ...customParamsAuthRequest,\n      ...customRequestParams\n    };\n    if (Object.keys(mergedParams).length > 0) {\n      params = this.appendCustomParams({\n        ...mergedParams\n      }, params);\n    }\n    if (prompt) {\n      params = this.overWriteParam(params, 'prompt', prompt);\n    }\n    if (hdParam) {\n      params = params.append('hd', hdParam);\n    }\n    return `${authorizationUrl}?${params}`;\n  }\n  createUrlImplicitFlowWithSilentRenew(configuration, customParams) {\n    const state = this.flowsDataService.getExistingOrCreateAuthStateControl(configuration);\n    const nonce = this.flowsDataService.createNonce(configuration);\n    const silentRenewUrl = this.getSilentRenewUrl(configuration);\n    if (!silentRenewUrl) {\n      return null;\n    }\n    this.loggerService.logDebug(configuration, 'RefreshSession created. adding myautostate: ', state);\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    if (authWellKnownEndPoints) {\n      return this.createAuthorizeUrl('', silentRenewUrl, nonce, state, configuration, 'none', customParams);\n    }\n    this.loggerService.logError(configuration, 'authWellKnownEndpoints is undefined');\n    return null;\n  }\n  createUrlCodeFlowWithSilentRenew(configuration, customParams) {\n    const state = this.flowsDataService.getExistingOrCreateAuthStateControl(configuration);\n    const nonce = this.flowsDataService.createNonce(configuration);\n    this.loggerService.logDebug(configuration, 'RefreshSession created. adding myautostate: ' + state);\n    // code_challenge with \"S256\"\n    const codeVerifier = this.flowsDataService.createCodeVerifier(configuration);\n    return this.jwtWindowCryptoService.generateCodeChallenge(codeVerifier).pipe(map(codeChallenge => {\n      const silentRenewUrl = this.getSilentRenewUrl(configuration);\n      if (!silentRenewUrl) {\n        return '';\n      }\n      const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n      if (authWellKnownEndPoints) {\n        return this.createAuthorizeUrl(codeChallenge, silentRenewUrl, nonce, state, configuration, 'none', customParams);\n      }\n      this.loggerService.logWarning(configuration, 'authWellKnownEndpoints is undefined');\n      return '';\n    }));\n  }\n  createUrlImplicitFlowAuthorize(configuration, authOptions) {\n    const state = this.flowsDataService.getExistingOrCreateAuthStateControl(configuration);\n    const nonce = this.flowsDataService.createNonce(configuration);\n    this.loggerService.logDebug(configuration, 'Authorize created. adding myautostate: ' + state);\n    const redirectUrl = this.getRedirectUrl(configuration, authOptions);\n    if (!redirectUrl) {\n      return null;\n    }\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    if (authWellKnownEndPoints) {\n      const {\n        customParams\n      } = authOptions || {};\n      return this.createAuthorizeUrl('', redirectUrl, nonce, state, configuration, '', customParams);\n    }\n    this.loggerService.logError(configuration, 'authWellKnownEndpoints is undefined');\n    return null;\n  }\n  createUrlCodeFlowAuthorize(config, authOptions) {\n    const state = this.flowsDataService.getExistingOrCreateAuthStateControl(config);\n    const nonce = this.flowsDataService.createNonce(config);\n    this.loggerService.logDebug(config, 'Authorize created. adding myautostate: ' + state);\n    const redirectUrl = this.getRedirectUrl(config, authOptions);\n    if (!redirectUrl) {\n      return of(null);\n    }\n    return this.getCodeChallenge(config).pipe(map(codeChallenge => {\n      const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', config);\n      if (authWellKnownEndPoints) {\n        const {\n          customParams\n        } = authOptions || {};\n        return this.createAuthorizeUrl(codeChallenge, redirectUrl, nonce, state, config, '', customParams);\n      }\n      this.loggerService.logError(config, 'authWellKnownEndpoints is undefined');\n      return '';\n    }));\n  }\n  getCodeChallenge(config) {\n    if (config.disablePkce) {\n      return of('');\n    }\n    // code_challenge with \"S256\"\n    const codeVerifier = this.flowsDataService.createCodeVerifier(config);\n    return this.jwtWindowCryptoService.generateCodeChallenge(codeVerifier);\n  }\n  getRedirectUrl(configuration, authOptions) {\n    let {\n      redirectUrl\n    } = configuration;\n    if (authOptions?.redirectUrl) {\n      // override by redirectUrl from authOptions\n      redirectUrl = authOptions.redirectUrl;\n    }\n    if (!redirectUrl) {\n      this.loggerService.logError(configuration, `could not get redirectUrl, was: `, redirectUrl);\n      return null;\n    }\n    return redirectUrl;\n  }\n  getSilentRenewUrl(configuration) {\n    const {\n      silentRenewUrl\n    } = configuration;\n    if (!silentRenewUrl) {\n      this.loggerService.logError(configuration, `could not get silentRenewUrl, was: `, silentRenewUrl);\n      return null;\n    }\n    return silentRenewUrl;\n  }\n  getClientId(configuration) {\n    const {\n      clientId\n    } = configuration;\n    if (!clientId) {\n      this.loggerService.logError(configuration, `could not get clientId, was: `, clientId);\n      return null;\n    }\n    return clientId;\n  }\n  appendCustomParams(customParams, params) {\n    for (const [key, value] of Object.entries({\n      ...customParams\n    })) {\n      params = params.append(key, value.toString());\n    }\n    return params;\n  }\n  overWriteParam(params, key, value) {\n    return params.set(key, value);\n  }\n  createHttpParams(existingParams) {\n    existingParams = existingParams ?? '';\n    return new HttpParams({\n      fromString: existingParams,\n      encoder: new UriEncoder()\n    });\n  }\n  isAuth0Endpoint(configuration) {\n    const {\n      authority,\n      useCustomAuth0Domain\n    } = configuration;\n    if (!authority) {\n      return false;\n    }\n    return authority.endsWith(AUTH0_ENDPOINT) || Boolean(useCustomAuth0Domain);\n  }\n  composeAuth0Endpoint(configuration) {\n    // format: https://YOUR_DOMAIN/v2/logout?client_id=YOUR_CLIENT_ID&returnTo=LOGOUT_URL\n    const {\n      authority,\n      clientId\n    } = configuration;\n    const postLogoutRedirectUrl = this.getPostLogoutRedirectUrl(configuration);\n    return `${authority}/v2/logout?client_id=${clientId}&returnTo=${postLogoutRedirectUrl}`;\n  }\n  static {\n    this.ɵfac = function UrlService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UrlService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UrlService,\n      factory: UrlService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UrlService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass HttpBaseService {\n  constructor() {\n    this.http = inject(HttpClient);\n  }\n  get(url, params) {\n    return this.http.get(url, params);\n  }\n  post(url, body, params) {\n    return this.http.post(url, body, params);\n  }\n  static {\n    this.ɵfac = function HttpBaseService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HttpBaseService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HttpBaseService,\n      factory: HttpBaseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpBaseService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst NGSW_CUSTOM_PARAM = 'ngsw-bypass';\nclass DataService {\n  constructor() {\n    this.httpClient = inject(HttpBaseService);\n  }\n  get(url, config, token) {\n    const headers = this.prepareHeaders(token);\n    const params = this.prepareParams(config);\n    return this.httpClient.get(url, {\n      headers,\n      params\n    });\n  }\n  post(url, body, config, headersParams) {\n    const headers = headersParams || this.prepareHeaders();\n    const params = this.prepareParams(config);\n    return this.httpClient.post(url ?? '', body, {\n      headers,\n      params\n    });\n  }\n  prepareHeaders(token) {\n    let headers = new HttpHeaders();\n    headers = headers.set('Accept', 'application/json');\n    if (!!token) {\n      headers = headers.set('Authorization', 'Bearer ' + decodeURIComponent(token));\n    }\n    return headers;\n  }\n  prepareParams(config) {\n    let params = new HttpParams();\n    const {\n      ngswBypass\n    } = config;\n    if (ngswBypass) {\n      params = params.set(NGSW_CUSTOM_PARAM, '');\n    }\n    return params;\n  }\n  static {\n    this.ɵfac = function DataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DataService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DataService,\n      factory: DataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DataService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass JwkExtractor {\n  extractJwk(keys, spec, throwOnEmpty = true) {\n    if (0 === keys.length) {\n      throw JwkExtractorInvalidArgumentError;\n    }\n    const foundKeys = keys.filter(k => spec?.kid ? k['kid'] === spec.kid : true).filter(k => spec?.use ? k['use'] === spec.use : true).filter(k => spec?.kty ? k['kty'] === spec.kty : true);\n    if (foundKeys.length === 0 && throwOnEmpty) {\n      throw JwkExtractorNoMatchingKeysError;\n    }\n    if (foundKeys.length > 1 && (null === spec || undefined === spec)) {\n      throw JwkExtractorSeveralMatchingKeysError;\n    }\n    return foundKeys;\n  }\n  static {\n    this.ɵfac = function JwkExtractor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || JwkExtractor)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: JwkExtractor,\n      factory: JwkExtractor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwkExtractor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction buildErrorName(name) {\n  return JwkExtractor.name + ': ' + name;\n}\nconst JwkExtractorInvalidArgumentError = {\n  name: buildErrorName('InvalidArgumentError'),\n  message: 'Array of keys was empty. Unable to extract'\n};\nconst JwkExtractorNoMatchingKeysError = {\n  name: buildErrorName('NoMatchingKeysError'),\n  message: 'No key found matching the spec'\n};\nconst JwkExtractorSeveralMatchingKeysError = {\n  name: buildErrorName('SeveralMatchingKeysError'),\n  message: 'More than one key found. Please use spec to filter'\n};\nconst PARTS_OF_TOKEN = 3;\nclass TokenHelperService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.document = inject(DOCUMENT);\n  }\n  getTokenExpirationDate(dataIdToken) {\n    if (!Object.prototype.hasOwnProperty.call(dataIdToken, 'exp')) {\n      return new Date(new Date().toUTCString());\n    }\n    const date = new Date(0); // The 0 here is the key, which sets the date to the epoch\n    date.setUTCSeconds(dataIdToken.exp);\n    return date;\n  }\n  getSigningInputFromToken(token, encoded, configuration) {\n    if (!this.tokenIsValid(token, configuration)) {\n      return '';\n    }\n    const header = this.getHeaderFromToken(token, encoded, configuration);\n    const payload = this.getPayloadFromToken(token, encoded, configuration);\n    return [header, payload].join('.');\n  }\n  getHeaderFromToken(token, encoded, configuration) {\n    if (!this.tokenIsValid(token, configuration)) {\n      return {};\n    }\n    return this.getPartOfToken(token, 0, encoded);\n  }\n  getPayloadFromToken(token, encoded, configuration) {\n    if (!configuration) {\n      return {};\n    }\n    if (!this.tokenIsValid(token, configuration)) {\n      return {};\n    }\n    return this.getPartOfToken(token, 1, encoded);\n  }\n  getSignatureFromToken(token, encoded, configuration) {\n    if (!this.tokenIsValid(token, configuration)) {\n      return {};\n    }\n    return this.getPartOfToken(token, 2, encoded);\n  }\n  getPartOfToken(token, index, encoded) {\n    const partOfToken = this.extractPartOfToken(token, index);\n    if (encoded) {\n      return partOfToken;\n    }\n    const result = this.urlBase64Decode(partOfToken);\n    return JSON.parse(result);\n  }\n  urlBase64Decode(str) {\n    let output = str.replace(/-/g, '+').replace(/_/g, '/');\n    switch (output.length % 4) {\n      case 0:\n        break;\n      case 2:\n        output += '==';\n        break;\n      case 3:\n        output += '=';\n        break;\n      default:\n        throw Error('Illegal base64url string!');\n    }\n    const decoded = typeof this.document.defaultView !== 'undefined' ? this.document.defaultView?.atob(output) : Buffer.from(output, 'base64').toString('binary');\n    if (!decoded) {\n      return '';\n    }\n    try {\n      // Going backwards: from byte stream, to percent-encoding, to original string.\n      return decodeURIComponent(decoded.split('').map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join(''));\n    } catch (err) {\n      return decoded;\n    }\n  }\n  tokenIsValid(token, configuration) {\n    if (!token) {\n      this.loggerService.logError(configuration, `token '${token}' is not valid --> token falsy`);\n      return false;\n    }\n    if (!token.includes('.')) {\n      this.loggerService.logError(configuration, `token '${token}' is not valid --> no dots included`);\n      return false;\n    }\n    const parts = token.split('.');\n    if (parts.length !== PARTS_OF_TOKEN) {\n      this.loggerService.logError(configuration, `token '${token}' is not valid --> token has to have exactly ${PARTS_OF_TOKEN - 1} dots`);\n      return false;\n    }\n    return true;\n  }\n  extractPartOfToken(token, index) {\n    return token.split('.')[index];\n  }\n  static {\n    this.ɵfac = function TokenHelperService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TokenHelperService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TokenHelperService,\n      factory: TokenHelperService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TokenHelperService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass JwkWindowCryptoService {\n  constructor() {\n    this.cryptoService = inject(CryptoService);\n  }\n  importVerificationKey(key, algorithm) {\n    return this.cryptoService.getCrypto().subtle.importKey('jwk', key, algorithm, false, ['verify']);\n  }\n  verifyKey(verifyAlgorithm, cryptoKey, signature, signingInput) {\n    return this.cryptoService.getCrypto().subtle.verify(verifyAlgorithm, cryptoKey, signature, new TextEncoder().encode(signingInput));\n  }\n  static {\n    this.ɵfac = function JwkWindowCryptoService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || JwkWindowCryptoService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: JwkWindowCryptoService,\n      factory: JwkWindowCryptoService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JwkWindowCryptoService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction getVerifyAlg(alg) {\n  switch (alg.charAt(0)) {\n    case 'R':\n      return {\n        name: 'RSASSA-PKCS1-v1_5',\n        hash: 'SHA-256'\n      };\n    case 'E':\n      if (alg.includes('256')) {\n        return {\n          name: 'ECDSA',\n          hash: 'SHA-256'\n        };\n      } else if (alg.includes('384')) {\n        return {\n          name: 'ECDSA',\n          hash: 'SHA-384'\n        };\n      } else {\n        return null;\n      }\n    default:\n      return null;\n  }\n}\nfunction alg2kty(alg) {\n  switch (alg.charAt(0)) {\n    case 'R':\n      return 'RSA';\n    case 'E':\n      return 'EC';\n    default:\n      throw new Error('Cannot infer kty from alg: ' + alg);\n  }\n}\nfunction getImportAlg(alg) {\n  switch (alg.charAt(0)) {\n    case 'R':\n      if (alg.includes('256')) {\n        return {\n          name: 'RSASSA-PKCS1-v1_5',\n          hash: 'SHA-256'\n        };\n      } else if (alg.includes('384')) {\n        return {\n          name: 'RSASSA-PKCS1-v1_5',\n          hash: 'SHA-384'\n        };\n      } else if (alg.includes('512')) {\n        return {\n          name: 'RSASSA-PKCS1-v1_5',\n          hash: 'SHA-512'\n        };\n      } else {\n        return null;\n      }\n    case 'E':\n      if (alg.includes('256')) {\n        return {\n          name: 'ECDSA',\n          namedCurve: 'P-256'\n        };\n      } else if (alg.includes('384')) {\n        return {\n          name: 'ECDSA',\n          namedCurve: 'P-384'\n        };\n      } else {\n        return null;\n      }\n    default:\n      return null;\n  }\n}\n\n// http://openid.net/specs/openid-connect-implicit-1_0.html\n// id_token\n// id_token C1: The Issuer Identifier for the OpenID Provider (which is typically obtained during Discovery)\n// MUST exactly match the value of the iss (issuer) Claim.\n//\n// id_token C2: The Client MUST validate that the aud (audience) Claim contains its client_id value registered at the Issuer identified\n// by the iss (issuer) Claim as an audience.The ID Token MUST be rejected if the ID Token does not list the Client as a valid audience,\n// or if it contains additional audiences not trusted by the Client.\n//\n// id_token C3: If the ID Token contains multiple audiences, the Client SHOULD verify that an azp Claim is present.\n//\n// id_token C4: If an azp (authorized party) Claim is present, the Client SHOULD verify that its client_id is the Claim Value.\n//\n// id_token C5: The Client MUST validate the signature of the ID Token according to JWS [JWS] using the algorithm specified in the\n// alg Header Parameter of the JOSE Header.The Client MUST use the keys provided by the Issuer.\n//\n// id_token C6: The alg value SHOULD be RS256. Validation of tokens using other signing algorithms is described in the OpenID Connect\n// Core 1.0\n// [OpenID.Core] specification.\n//\n// id_token C7: The current time MUST be before the time represented by the exp Claim (possibly allowing for some small leeway to account\n// for clock skew).\n//\n// id_token C8: The iat Claim can be used to reject tokens that were issued too far away from the current time,\n// limiting the amount of time that nonces need to be stored to prevent attacks.The acceptable range is Client specific.\n//\n// id_token C9: The value of the nonce Claim MUST be checked to verify that it is the same value as the one that was sent\n// in the Authentication Request.The Client SHOULD check the nonce value for replay attacks.The precise method for detecting replay attacks\n// is Client specific.\n//\n// id_token C10: If the acr Claim was requested, the Client SHOULD check that the asserted Claim Value is appropriate.\n// The meaning and processing of acr Claim Values is out of scope for this document.\n//\n// id_token C11: When a max_age request is made, the Client SHOULD check the auth_time Claim value and request re- authentication\n// if it determines too much time has elapsed since the last End- User authentication.\n// Access Token Validation\n// access_token C1: Hash the octets of the ASCII representation of the access_token with the hash algorithm specified in JWA[JWA]\n// for the alg Header Parameter of the ID Token's JOSE Header. For instance, if the alg is RS256, the hash algorithm used is SHA-256.\n// access_token C2: Take the left- most half of the hash and base64url- encode it.\n// access_token C3: The value of at_hash in the ID Token MUST match the value produced in the previous step if at_hash is present\n// in the ID Token.\nclass TokenValidationService {\n  constructor() {\n    this.keyAlgorithms = ['HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'PS256', 'PS384', 'PS512'];\n    this.tokenHelperService = inject(TokenHelperService);\n    this.loggerService = inject(LoggerService);\n    this.jwkExtractor = inject(JwkExtractor);\n    this.jwkWindowCryptoService = inject(JwkWindowCryptoService);\n    this.jwtWindowCryptoService = inject(JwtWindowCryptoService);\n  }\n  static {\n    this.refreshTokenNoncePlaceholder = '--RefreshToken--';\n  }\n  // id_token C7: The current time MUST be before the time represented by the exp Claim\n  // (possibly allowing for some small leeway to account for clock skew).\n  hasIdTokenExpired(token, configuration, offsetSeconds) {\n    const decoded = this.tokenHelperService.getPayloadFromToken(token, false, configuration);\n    return !this.validateIdTokenExpNotExpired(decoded, configuration, offsetSeconds);\n  }\n  // id_token C7: The current time MUST be before the time represented by the exp Claim\n  // (possibly allowing for some small leeway to account for clock skew).\n  validateIdTokenExpNotExpired(decodedIdToken, configuration, offsetSeconds) {\n    const tokenExpirationDate = this.tokenHelperService.getTokenExpirationDate(decodedIdToken);\n    offsetSeconds = offsetSeconds || 0;\n    if (!tokenExpirationDate) {\n      return false;\n    }\n    const tokenExpirationValue = tokenExpirationDate.valueOf();\n    const nowWithOffset = this.calculateNowWithOffset(offsetSeconds);\n    const tokenNotExpired = tokenExpirationValue > nowWithOffset;\n    this.loggerService.logDebug(configuration, `Has idToken expired: ${!tokenNotExpired} --> expires in ${this.millisToMinutesAndSeconds(tokenExpirationValue - nowWithOffset)} , ${new Date(tokenExpirationValue).toLocaleTimeString()} > ${new Date(nowWithOffset).toLocaleTimeString()}`);\n    return tokenNotExpired;\n  }\n  validateAccessTokenNotExpired(accessTokenExpiresAt, configuration, offsetSeconds) {\n    // value is optional, so if it does not exist, then it has not expired\n    if (!accessTokenExpiresAt) {\n      return true;\n    }\n    offsetSeconds = offsetSeconds || 0;\n    const accessTokenExpirationValue = accessTokenExpiresAt.valueOf();\n    const nowWithOffset = this.calculateNowWithOffset(offsetSeconds);\n    const tokenNotExpired = accessTokenExpirationValue > nowWithOffset;\n    this.loggerService.logDebug(configuration, `Has accessToken expired: ${!tokenNotExpired} --> expires in ${this.millisToMinutesAndSeconds(accessTokenExpirationValue - nowWithOffset)} , ${new Date(accessTokenExpirationValue).toLocaleTimeString()} > ${new Date(nowWithOffset).toLocaleTimeString()}`);\n    return tokenNotExpired;\n  }\n  // iss\n  // REQUIRED. Issuer Identifier for the Issuer of the response.The iss value is a case-sensitive URL using the\n  // https scheme that contains scheme, host,\n  // and optionally, port number and path components and no query or fragment components.\n  //\n  // sub\n  // REQUIRED. Subject Identifier.Locally unique and never reassigned identifier within the Issuer for the End- User,\n  // which is intended to be consumed by the Client, e.g., 24400320 or AItOawmwtWwcT0k51BayewNvutrJUqsvl6qs7A4.\n  // It MUST NOT exceed 255 ASCII characters in length.The sub value is a case-sensitive string.\n  //\n  // aud\n  // REQUIRED. Audience(s) that this ID Token is intended for. It MUST contain the OAuth 2.0 client_id of the Relying Party as an\n  // audience value.\n  // It MAY also contain identifiers for other audiences.In the general case, the aud value is an array of case-sensitive strings.\n  // In the common special case when there is one audience, the aud value MAY be a single case-sensitive string.\n  //\n  // exp\n  // REQUIRED. Expiration time on or after which the ID Token MUST NOT be accepted for processing.\n  // The processing of this parameter requires that the current date/ time MUST be before the expiration date/ time listed in the value.\n  // Implementers MAY provide for some small leeway, usually no more than a few minutes, to account for clock skew.\n  // Its value is a JSON [RFC7159] number representing the number of seconds from 1970- 01 - 01T00: 00:00Z as measured in UTC until\n  // the date/ time.\n  // See RFC 3339 [RFC3339] for details regarding date/ times in general and UTC in particular.\n  //\n  // iat\n  // REQUIRED. Time at which the JWT was issued. Its value is a JSON number representing the number of seconds from\n  // 1970- 01 - 01T00: 00: 00Z as measured\n  // in UTC until the date/ time.\n  validateRequiredIdToken(dataIdToken, configuration) {\n    let validated = true;\n    if (!Object.prototype.hasOwnProperty.call(dataIdToken, 'iss')) {\n      validated = false;\n      this.loggerService.logWarning(configuration, 'iss is missing, this is required in the id_token');\n    }\n    if (!Object.prototype.hasOwnProperty.call(dataIdToken, 'sub')) {\n      validated = false;\n      this.loggerService.logWarning(configuration, 'sub is missing, this is required in the id_token');\n    }\n    if (!Object.prototype.hasOwnProperty.call(dataIdToken, 'aud')) {\n      validated = false;\n      this.loggerService.logWarning(configuration, 'aud is missing, this is required in the id_token');\n    }\n    if (!Object.prototype.hasOwnProperty.call(dataIdToken, 'exp')) {\n      validated = false;\n      this.loggerService.logWarning(configuration, 'exp is missing, this is required in the id_token');\n    }\n    if (!Object.prototype.hasOwnProperty.call(dataIdToken, 'iat')) {\n      validated = false;\n      this.loggerService.logWarning(configuration, 'iat is missing, this is required in the id_token');\n    }\n    return validated;\n  }\n  // id_token C8: The iat Claim can be used to reject tokens that were issued too far away from the current time,\n  // limiting the amount of time that nonces need to be stored to prevent attacks.The acceptable range is Client specific.\n  validateIdTokenIatMaxOffset(dataIdToken, maxOffsetAllowedInSeconds, disableIatOffsetValidation, configuration) {\n    if (disableIatOffsetValidation) {\n      return true;\n    }\n    if (!Object.prototype.hasOwnProperty.call(dataIdToken, 'iat')) {\n      return false;\n    }\n    const dateTimeIatIdToken = new Date(0); // The 0 here is the key, which sets the date to the epoch\n    dateTimeIatIdToken.setUTCSeconds(dataIdToken.iat);\n    maxOffsetAllowedInSeconds = maxOffsetAllowedInSeconds || 0;\n    const nowInUtc = new Date(new Date().toUTCString());\n    const diff = nowInUtc.valueOf() - dateTimeIatIdToken.valueOf();\n    const maxOffsetAllowedInMilliseconds = maxOffsetAllowedInSeconds * 1000;\n    this.loggerService.logDebug(configuration, `validate id token iat max offset ${diff} < ${maxOffsetAllowedInMilliseconds}`);\n    if (diff > 0) {\n      return diff < maxOffsetAllowedInMilliseconds;\n    }\n    return -diff < maxOffsetAllowedInMilliseconds;\n  }\n  // id_token C9: The value of the nonce Claim MUST be checked to verify that it is the same value as the one\n  // that was sent in the Authentication Request.The Client SHOULD check the nonce value for replay attacks.\n  // The precise method for detecting replay attacks is Client specific.\n  // However the nonce claim SHOULD not be present for the refresh_token grant type\n  // https://bitbucket.org/openid/connect/issues/1025/ambiguity-with-how-nonce-is-handled-on\n  // The current spec is ambiguous and KeyCloak does send it.\n  validateIdTokenNonce(dataIdToken, localNonce, ignoreNonceAfterRefresh, configuration) {\n    const isFromRefreshToken = (dataIdToken.nonce === undefined || ignoreNonceAfterRefresh) && localNonce === TokenValidationService.refreshTokenNoncePlaceholder;\n    if (!isFromRefreshToken && dataIdToken.nonce !== localNonce) {\n      this.loggerService.logDebug(configuration, 'Validate_id_token_nonce failed, dataIdToken.nonce: ' + dataIdToken.nonce + ' local_nonce:' + localNonce);\n      return false;\n    }\n    return true;\n  }\n  // id_token C1: The Issuer Identifier for the OpenID Provider (which is typically obtained during Discovery)\n  // MUST exactly match the value of the iss (issuer) Claim.\n  validateIdTokenIss(dataIdToken, authWellKnownEndpointsIssuer, configuration) {\n    if (dataIdToken.iss !== authWellKnownEndpointsIssuer) {\n      this.loggerService.logDebug(configuration, 'Validate_id_token_iss failed, dataIdToken.iss: ' + dataIdToken.iss + ' authWellKnownEndpoints issuer:' + authWellKnownEndpointsIssuer);\n      return false;\n    }\n    return true;\n  }\n  // id_token C2: The Client MUST validate that the aud (audience) Claim contains its client_id value registered at the Issuer identified\n  // by the iss (issuer) Claim as an audience.\n  // The ID Token MUST be rejected if the ID Token does not list the Client as a valid audience, or if it contains additional audiences\n  // not trusted by the Client.\n  validateIdTokenAud(dataIdToken, aud, configuration) {\n    if (Array.isArray(dataIdToken.aud)) {\n      const result = dataIdToken.aud.includes(aud);\n      if (!result) {\n        this.loggerService.logDebug(configuration, 'Validate_id_token_aud array failed, dataIdToken.aud: ' + dataIdToken.aud + ' client_id:' + aud);\n        return false;\n      }\n      return true;\n    } else if (dataIdToken.aud !== aud) {\n      this.loggerService.logDebug(configuration, 'Validate_id_token_aud failed, dataIdToken.aud: ' + dataIdToken.aud + ' client_id:' + aud);\n      return false;\n    }\n    return true;\n  }\n  validateIdTokenAzpExistsIfMoreThanOneAud(dataIdToken) {\n    if (!dataIdToken) {\n      return false;\n    }\n    return !(Array.isArray(dataIdToken.aud) && dataIdToken.aud.length > 1 && !dataIdToken.azp);\n  }\n  // If an azp (authorized party) Claim is present, the Client SHOULD verify that its client_id is the Claim Value.\n  validateIdTokenAzpValid(dataIdToken, clientId) {\n    if (!dataIdToken?.azp) {\n      return true;\n    }\n    return dataIdToken.azp === clientId;\n  }\n  validateStateFromHashCallback(state, localState, configuration) {\n    if (state !== localState) {\n      this.loggerService.logDebug(configuration, 'ValidateStateFromHashCallback failed, state: ' + state + ' local_state:' + localState);\n      return false;\n    }\n    return true;\n  }\n  // id_token C5: The Client MUST validate the signature of the ID Token according to JWS [JWS] using the algorithm specified in the alg\n  // Header Parameter of the JOSE Header.The Client MUST use the keys provided by the Issuer.\n  // id_token C6: The alg value SHOULD be RS256. Validation of tokens using other signing algorithms is described in the\n  // OpenID Connect Core 1.0 [OpenID.Core] specification.\n  validateSignatureIdToken(idToken, jwtkeys, configuration) {\n    if (!idToken) {\n      return of(true);\n    }\n    if (!jwtkeys || !jwtkeys.keys) {\n      return of(false);\n    }\n    const headerData = this.tokenHelperService.getHeaderFromToken(idToken, false, configuration);\n    if (Object.keys(headerData).length === 0 && headerData.constructor === Object) {\n      this.loggerService.logWarning(configuration, 'id token has no header data');\n      return of(false);\n    }\n    const kid = headerData.kid;\n    const alg = headerData.alg;\n    const keys = jwtkeys.keys;\n    let foundKeys;\n    let key;\n    if (!this.keyAlgorithms.includes(alg)) {\n      this.loggerService.logWarning(configuration, 'alg not supported', alg);\n      return of(false);\n    }\n    const kty = alg2kty(alg);\n    const use = 'sig';\n    try {\n      foundKeys = kid ? this.jwkExtractor.extractJwk(keys, {\n        kid,\n        kty,\n        use\n      }, false) : this.jwkExtractor.extractJwk(keys, {\n        kty,\n        use\n      }, false);\n      if (foundKeys.length === 0) {\n        foundKeys = kid ? this.jwkExtractor.extractJwk(keys, {\n          kid,\n          kty\n        }) : this.jwkExtractor.extractJwk(keys, {\n          kty\n        });\n      }\n      key = foundKeys[0];\n    } catch (e) {\n      this.loggerService.logError(configuration, e);\n      return of(false);\n    }\n    const algorithm = getImportAlg(alg);\n    const signingInput = this.tokenHelperService.getSigningInputFromToken(idToken, true, configuration);\n    const rawSignature = this.tokenHelperService.getSignatureFromToken(idToken, true, configuration);\n    return from(this.jwkWindowCryptoService.importVerificationKey(key, algorithm)).pipe(mergeMap(cryptoKey => {\n      const signature = base64url.parse(rawSignature, {\n        loose: true\n      });\n      const verifyAlgorithm = getVerifyAlg(alg);\n      return from(this.jwkWindowCryptoService.verifyKey(verifyAlgorithm, cryptoKey, signature, signingInput));\n    }), tap(isValid => {\n      if (!isValid) {\n        this.loggerService.logWarning(configuration, 'incorrect Signature, validation failed for id_token');\n      }\n    }));\n  }\n  // Accepts ID Token without 'kid' claim in JOSE header if only one JWK supplied in 'jwks_url'\n  //// private validate_no_kid_in_header_only_one_allowed_in_jwtkeys(header_data: any, jwtkeys: any): boolean {\n  ////    this.oidcSecurityCommon.logDebug('amount of jwtkeys.keys: ' + jwtkeys.keys.length);\n  ////    if (!header_data.hasOwnProperty('kid')) {\n  ////        // no kid defined in Jose header\n  ////        if (jwtkeys.keys.length != 1) {\n  ////            this.oidcSecurityCommon.logDebug('jwtkeys.keys.length != 1 and no kid in header');\n  ////            return false;\n  ////        }\n  ////    }\n  ////    return true;\n  //// }\n  // Access Token Validation\n  // access_token C1: Hash the octets of the ASCII representation of the access_token with the hash algorithm specified in JWA[JWA]\n  // for the alg Header Parameter of the ID Token's JOSE Header. For instance, if the alg is RS256, the hash algorithm used is SHA-256.\n  // access_token C2: Take the left- most half of the hash and base64url- encode it.\n  // access_token C3: The value of at_hash in the ID Token MUST match the value produced in the previous step if at_hash\n  // is present in the ID Token.\n  validateIdTokenAtHash(accessToken, atHash, idTokenAlg, configuration) {\n    this.loggerService.logDebug(configuration, 'at_hash from the server:' + atHash);\n    // 'sha256' 'sha384' 'sha512'\n    let sha = 'SHA-256';\n    if (idTokenAlg.includes('384')) {\n      sha = 'SHA-384';\n    } else if (idTokenAlg.includes('512')) {\n      sha = 'SHA-512';\n    }\n    return this.jwtWindowCryptoService.generateAtHash('' + accessToken, sha).pipe(mergeMap(hash => {\n      this.loggerService.logDebug(configuration, 'at_hash client validation not decoded:' + hash);\n      if (hash === atHash) {\n        return of(true); // isValid;\n      } else {\n        return this.jwtWindowCryptoService.generateAtHash('' + decodeURIComponent(accessToken), sha).pipe(map(newHash => {\n          this.loggerService.logDebug(configuration, '-gen access--' + hash);\n          return newHash === atHash;\n        }));\n      }\n    }));\n  }\n  millisToMinutesAndSeconds(millis) {\n    const minutes = Math.floor(millis / 60000);\n    const seconds = (millis % 60000 / 1000).toFixed(0);\n    return minutes + ':' + (+seconds < 10 ? '0' : '') + seconds;\n  }\n  calculateNowWithOffset(offsetSeconds) {\n    return new Date(new Date().toUTCString()).valueOf() + offsetSeconds * 1000;\n  }\n  static {\n    this.ɵfac = function TokenValidationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TokenValidationService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TokenValidationService,\n      factory: TokenValidationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TokenValidationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * checks if the error is a network error\n * by checking if either internal error is a ProgressEvent with type error\n * or another error with status 0\n * @param error\n * @returns true if the error is a network error\n */\nconst isNetworkError = error => !!error && error instanceof HttpErrorResponse && (error.error instanceof ProgressEvent && error.error.type === 'error' || error.status === 0 && !!error.error);\nclass CodeFlowCallbackHandlerService {\n  constructor() {\n    this.urlService = inject(UrlService);\n    this.loggerService = inject(LoggerService);\n    this.tokenValidationService = inject(TokenValidationService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.dataService = inject(DataService);\n  }\n  // STEP 1 Code Flow\n  codeFlowCallback(urlToCheck, config) {\n    const code = this.urlService.getUrlParameter(urlToCheck, 'code');\n    const state = this.urlService.getUrlParameter(urlToCheck, 'state');\n    const sessionState = this.urlService.getUrlParameter(urlToCheck, 'session_state');\n    if (!state) {\n      this.loggerService.logDebug(config, 'no state in url');\n      return throwError(() => new Error('no state in url'));\n    }\n    if (!code) {\n      this.loggerService.logDebug(config, 'no code in url');\n      return throwError(() => new Error('no code in url'));\n    }\n    this.loggerService.logDebug(config, 'running validation for callback', urlToCheck);\n    const initialCallbackContext = {\n      code,\n      refreshToken: '',\n      state,\n      sessionState,\n      authResult: null,\n      isRenewProcess: false,\n      jwtKeys: null,\n      validationResult: null,\n      existingIdToken: null\n    };\n    return of(initialCallbackContext);\n  }\n  // STEP 2 Code Flow //  Code Flow Silent Renew starts here\n  codeFlowCodeRequest(callbackContext, config) {\n    const authStateControl = this.flowsDataService.getAuthStateControl(config);\n    const isStateCorrect = this.tokenValidationService.validateStateFromHashCallback(callbackContext.state, authStateControl, config);\n    if (!isStateCorrect) {\n      return throwError(() => new Error('codeFlowCodeRequest incorrect state'));\n    }\n    const authWellknownEndpoints = this.storagePersistenceService.read('authWellKnownEndPoints', config);\n    const tokenEndpoint = authWellknownEndpoints?.tokenEndpoint;\n    if (!tokenEndpoint) {\n      return throwError(() => new Error('Token Endpoint not defined'));\n    }\n    let headers = new HttpHeaders();\n    headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');\n    const bodyForCodeFlow = this.urlService.createBodyForCodeFlowCodeRequest(callbackContext.code, config, config?.customParamsCodeRequest);\n    return this.dataService.post(tokenEndpoint, bodyForCodeFlow, config, headers).pipe(switchMap(response => {\n      if (response) {\n        const authResult = {\n          ...response,\n          state: callbackContext.state,\n          session_state: callbackContext.sessionState\n        };\n        callbackContext.authResult = authResult;\n      }\n      return of(callbackContext);\n    }), retryWhen(error => this.handleRefreshRetry(error, config)), catchError(error => {\n      const {\n        authority\n      } = config;\n      const errorMessage = `OidcService code request ${authority}`;\n      this.loggerService.logError(config, errorMessage, error);\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  handleRefreshRetry(errors, config) {\n    return errors.pipe(mergeMap(error => {\n      // retry token refresh if there is no internet connection\n      if (isNetworkError(error)) {\n        const {\n          authority,\n          refreshTokenRetryInSeconds\n        } = config;\n        const errorMessage = `OidcService code request ${authority} - no internet connection`;\n        this.loggerService.logWarning(config, errorMessage, error);\n        return timer((refreshTokenRetryInSeconds ?? 0) * 1000);\n      }\n      return throwError(() => error);\n    }));\n  }\n  static {\n    this.ɵfac = function CodeFlowCallbackHandlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CodeFlowCallbackHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CodeFlowCallbackHandlerService,\n      factory: CodeFlowCallbackHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CodeFlowCallbackHandlerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nvar EventTypes;\n(function (EventTypes) {\n  /**\n   *  This only works in the AppModule Constructor\n   */\n  EventTypes[EventTypes[\"ConfigLoaded\"] = 0] = \"ConfigLoaded\";\n  EventTypes[EventTypes[\"CheckingAuth\"] = 1] = \"CheckingAuth\";\n  EventTypes[EventTypes[\"CheckingAuthFinished\"] = 2] = \"CheckingAuthFinished\";\n  EventTypes[EventTypes[\"CheckingAuthFinishedWithError\"] = 3] = \"CheckingAuthFinishedWithError\";\n  EventTypes[EventTypes[\"ConfigLoadingFailed\"] = 4] = \"ConfigLoadingFailed\";\n  EventTypes[EventTypes[\"CheckSessionReceived\"] = 5] = \"CheckSessionReceived\";\n  EventTypes[EventTypes[\"UserDataChanged\"] = 6] = \"UserDataChanged\";\n  EventTypes[EventTypes[\"NewAuthenticationResult\"] = 7] = \"NewAuthenticationResult\";\n  EventTypes[EventTypes[\"TokenExpired\"] = 8] = \"TokenExpired\";\n  EventTypes[EventTypes[\"IdTokenExpired\"] = 9] = \"IdTokenExpired\";\n  EventTypes[EventTypes[\"SilentRenewStarted\"] = 10] = \"SilentRenewStarted\";\n  EventTypes[EventTypes[\"SilentRenewFailed\"] = 11] = \"SilentRenewFailed\";\n})(EventTypes || (EventTypes = {}));\nclass PublicEventsService {\n  constructor() {\n    this.notify = new ReplaySubject(1);\n  }\n  /**\n   * Fires a new event.\n   *\n   * @param type The event type.\n   * @param value The event value.\n   */\n  fireEvent(type, value) {\n    this.notify.next({\n      type,\n      value\n    });\n  }\n  /**\n   * Wires up the event notification observable.\n   */\n  registerForEvents() {\n    return this.notify.asObservable();\n  }\n  static {\n    this.ɵfac = function PublicEventsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PublicEventsService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PublicEventsService,\n      factory: PublicEventsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PublicEventsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst DEFAULT_AUTHRESULT = {\n  isAuthenticated: false,\n  allConfigsAuthenticated: []\n};\nclass AuthStateService {\n  constructor() {\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.loggerService = inject(LoggerService);\n    this.publicEventsService = inject(PublicEventsService);\n    this.tokenValidationService = inject(TokenValidationService);\n    this.authenticatedInternal$ = new BehaviorSubject(DEFAULT_AUTHRESULT);\n  }\n  get authenticated$() {\n    return this.authenticatedInternal$.asObservable().pipe(distinctUntilChanged());\n  }\n  setAuthenticatedAndFireEvent(allConfigs) {\n    const result = this.composeAuthenticatedResult(allConfigs);\n    this.authenticatedInternal$.next(result);\n  }\n  setUnauthenticatedAndFireEvent(currentConfig, allConfigs) {\n    this.storagePersistenceService.resetAuthStateInStorage(currentConfig);\n    const result = this.composeUnAuthenticatedResult(allConfigs);\n    this.authenticatedInternal$.next(result);\n  }\n  updateAndPublishAuthState(authenticationResult) {\n    this.publicEventsService.fireEvent(EventTypes.NewAuthenticationResult, authenticationResult);\n  }\n  setAuthorizationData(accessToken, authResult, currentConfig, allConfigs) {\n    this.loggerService.logDebug(currentConfig, `storing the accessToken '${accessToken}'`);\n    this.storagePersistenceService.write('authzData', accessToken, currentConfig);\n    this.persistAccessTokenExpirationTime(authResult, currentConfig);\n    this.setAuthenticatedAndFireEvent(allConfigs);\n  }\n  getAccessToken(configuration) {\n    if (!configuration) {\n      return '';\n    }\n    if (!this.isAuthenticated(configuration)) {\n      return '';\n    }\n    const token = this.storagePersistenceService.getAccessToken(configuration);\n    return this.decodeURIComponentSafely(token);\n  }\n  getIdToken(configuration) {\n    if (!configuration) {\n      return '';\n    }\n    if (!this.isAuthenticated(configuration)) {\n      return '';\n    }\n    const token = this.storagePersistenceService.getIdToken(configuration);\n    return this.decodeURIComponentSafely(token);\n  }\n  getRefreshToken(configuration) {\n    if (!configuration) {\n      return '';\n    }\n    if (!this.isAuthenticated(configuration)) {\n      return '';\n    }\n    const token = this.storagePersistenceService.getRefreshToken(configuration);\n    return this.decodeURIComponentSafely(token);\n  }\n  getAuthenticationResult(configuration) {\n    if (!configuration) {\n      return null;\n    }\n    if (!this.isAuthenticated(configuration)) {\n      return null;\n    }\n    return this.storagePersistenceService.getAuthenticationResult(configuration);\n  }\n  areAuthStorageTokensValid(configuration) {\n    if (!configuration) {\n      return false;\n    }\n    if (!this.isAuthenticated(configuration)) {\n      return false;\n    }\n    if (this.hasIdTokenExpiredAndRenewCheckIsEnabled(configuration)) {\n      this.loggerService.logDebug(configuration, 'persisted idToken is expired');\n      return false;\n    }\n    if (this.hasAccessTokenExpiredIfExpiryExists(configuration)) {\n      this.loggerService.logDebug(configuration, 'persisted accessToken is expired');\n      return false;\n    }\n    this.loggerService.logDebug(configuration, 'persisted idToken and accessToken are valid');\n    return true;\n  }\n  hasIdTokenExpiredAndRenewCheckIsEnabled(configuration) {\n    const {\n      renewTimeBeforeTokenExpiresInSeconds,\n      triggerRefreshWhenIdTokenExpired,\n      disableIdTokenValidation\n    } = configuration;\n    if (!triggerRefreshWhenIdTokenExpired || disableIdTokenValidation) {\n      return false;\n    }\n    const tokenToCheck = this.storagePersistenceService.getIdToken(configuration);\n    const idTokenExpired = this.tokenValidationService.hasIdTokenExpired(tokenToCheck, configuration, renewTimeBeforeTokenExpiresInSeconds);\n    if (idTokenExpired) {\n      this.publicEventsService.fireEvent(EventTypes.IdTokenExpired, idTokenExpired);\n    }\n    return idTokenExpired;\n  }\n  hasAccessTokenExpiredIfExpiryExists(configuration) {\n    const {\n      renewTimeBeforeTokenExpiresInSeconds\n    } = configuration;\n    const accessTokenExpiresIn = this.storagePersistenceService.read('access_token_expires_at', configuration);\n    const accessTokenHasNotExpired = this.tokenValidationService.validateAccessTokenNotExpired(accessTokenExpiresIn, configuration, renewTimeBeforeTokenExpiresInSeconds);\n    const hasExpired = !accessTokenHasNotExpired;\n    if (hasExpired) {\n      this.publicEventsService.fireEvent(EventTypes.TokenExpired, hasExpired);\n    }\n    return hasExpired;\n  }\n  isAuthenticated(configuration) {\n    if (!configuration) {\n      throwError(() => new Error('Please provide a configuration before setting up the module'));\n      return false;\n    }\n    const hasAccessToken = !!this.storagePersistenceService.getAccessToken(configuration);\n    const hasIdToken = !!this.storagePersistenceService.getIdToken(configuration);\n    return hasAccessToken && hasIdToken;\n  }\n  decodeURIComponentSafely(token) {\n    if (token) {\n      return decodeURIComponent(token);\n    } else {\n      return '';\n    }\n  }\n  persistAccessTokenExpirationTime(authResult, configuration) {\n    if (authResult?.expires_in) {\n      const accessTokenExpiryTime = new Date(new Date().toUTCString()).valueOf() + authResult.expires_in * 1000;\n      this.storagePersistenceService.write('access_token_expires_at', accessTokenExpiryTime, configuration);\n    }\n  }\n  composeAuthenticatedResult(allConfigs) {\n    if (allConfigs.length === 1) {\n      const {\n        configId\n      } = allConfigs[0];\n      return {\n        isAuthenticated: true,\n        allConfigsAuthenticated: [{\n          configId: configId ?? '',\n          isAuthenticated: true\n        }]\n      };\n    }\n    return this.checkAllConfigsIfTheyAreAuthenticated(allConfigs);\n  }\n  composeUnAuthenticatedResult(allConfigs) {\n    if (allConfigs.length === 1) {\n      const {\n        configId\n      } = allConfigs[0];\n      return {\n        isAuthenticated: false,\n        allConfigsAuthenticated: [{\n          configId: configId ?? '',\n          isAuthenticated: false\n        }]\n      };\n    }\n    return this.checkAllConfigsIfTheyAreAuthenticated(allConfigs);\n  }\n  checkAllConfigsIfTheyAreAuthenticated(allConfigs) {\n    const allConfigsAuthenticated = allConfigs.map(config => ({\n      configId: config.configId ?? '',\n      isAuthenticated: this.isAuthenticated(config)\n    }));\n    const isAuthenticated = allConfigsAuthenticated.every(x => !!x.isAuthenticated);\n    return {\n      allConfigsAuthenticated,\n      isAuthenticated\n    };\n  }\n  static {\n    this.ɵfac = function AuthStateService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthStateService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthStateService,\n      factory: AuthStateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthStateService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nvar ValidationResult;\n(function (ValidationResult) {\n  ValidationResult[\"NotSet\"] = \"NotSet\";\n  ValidationResult[\"StatesDoNotMatch\"] = \"StatesDoNotMatch\";\n  ValidationResult[\"SignatureFailed\"] = \"SignatureFailed\";\n  ValidationResult[\"IncorrectNonce\"] = \"IncorrectNonce\";\n  ValidationResult[\"RequiredPropertyMissing\"] = \"RequiredPropertyMissing\";\n  ValidationResult[\"MaxOffsetExpired\"] = \"MaxOffsetExpired\";\n  ValidationResult[\"IssDoesNotMatchIssuer\"] = \"IssDoesNotMatchIssuer\";\n  ValidationResult[\"NoAuthWellKnownEndPoints\"] = \"NoAuthWellKnownEndPoints\";\n  ValidationResult[\"IncorrectAud\"] = \"IncorrectAud\";\n  ValidationResult[\"IncorrectIdTokenClaimsAfterRefresh\"] = \"IncorrectIdTokenClaimsAfterRefresh\";\n  ValidationResult[\"IncorrectAzp\"] = \"IncorrectAzp\";\n  ValidationResult[\"TokenExpired\"] = \"TokenExpired\";\n  ValidationResult[\"IncorrectAtHash\"] = \"IncorrectAtHash\";\n  ValidationResult[\"Ok\"] = \"Ok\";\n  ValidationResult[\"LoginRequired\"] = \"LoginRequired\";\n  ValidationResult[\"SecureTokenServerError\"] = \"SecureTokenServerError\";\n})(ValidationResult || (ValidationResult = {}));\nconst DEFAULT_USERRESULT = {\n  userData: null,\n  allUserData: []\n};\nclass UserService {\n  constructor() {\n    this.userDataInternal$ = new BehaviorSubject(DEFAULT_USERRESULT);\n    this.loggerService = inject(LoggerService);\n    this.tokenHelperService = inject(TokenHelperService);\n    this.flowHelper = inject(FlowHelper);\n    this.oidcDataService = inject(DataService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.eventService = inject(PublicEventsService);\n  }\n  get userData$() {\n    return this.userDataInternal$.asObservable();\n  }\n  getAndPersistUserDataInStore(currentConfiguration, allConfigs, isRenewProcess = false, idToken, decodedIdToken) {\n    idToken = idToken || this.storagePersistenceService.getIdToken(currentConfiguration);\n    decodedIdToken = decodedIdToken || this.tokenHelperService.getPayloadFromToken(idToken, false, currentConfiguration);\n    const existingUserDataFromStorage = this.getUserDataFromStore(currentConfiguration);\n    const haveUserData = !!existingUserDataFromStorage;\n    const isCurrentFlowImplicitFlowWithAccessToken = this.flowHelper.isCurrentFlowImplicitFlowWithAccessToken(currentConfiguration);\n    const isCurrentFlowCodeFlow = this.flowHelper.isCurrentFlowCodeFlow(currentConfiguration);\n    const accessToken = this.storagePersistenceService.getAccessToken(currentConfiguration);\n    if (!(isCurrentFlowImplicitFlowWithAccessToken || isCurrentFlowCodeFlow)) {\n      this.loggerService.logDebug(currentConfiguration, `authCallback idToken flow with accessToken ${accessToken}`);\n      this.setUserDataToStore(decodedIdToken, currentConfiguration, allConfigs);\n      return of(decodedIdToken);\n    }\n    const {\n      renewUserInfoAfterTokenRenew\n    } = currentConfiguration;\n    if (!isRenewProcess || renewUserInfoAfterTokenRenew || !haveUserData) {\n      return this.getUserDataOidcFlowAndSave(decodedIdToken.sub, currentConfiguration, allConfigs).pipe(switchMap(userData => {\n        this.loggerService.logDebug(currentConfiguration, 'Received user data: ', userData);\n        if (!!userData) {\n          this.loggerService.logDebug(currentConfiguration, 'accessToken: ', accessToken);\n          return of(userData);\n        } else {\n          return throwError(() => new Error('Received no user data, request failed'));\n        }\n      }));\n    }\n    return of(existingUserDataFromStorage);\n  }\n  getUserDataFromStore(currentConfiguration) {\n    if (!currentConfiguration) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    return this.storagePersistenceService.read('userData', currentConfiguration) || null;\n  }\n  publishUserDataIfExists(currentConfiguration, allConfigs) {\n    const userData = this.getUserDataFromStore(currentConfiguration);\n    if (userData) {\n      this.fireUserDataEvent(currentConfiguration, allConfigs, userData);\n    }\n  }\n  setUserDataToStore(userData, currentConfiguration, allConfigs) {\n    this.storagePersistenceService.write('userData', userData, currentConfiguration);\n    this.fireUserDataEvent(currentConfiguration, allConfigs, userData);\n  }\n  resetUserDataInStore(currentConfiguration, allConfigs) {\n    this.storagePersistenceService.remove('userData', currentConfiguration);\n    this.fireUserDataEvent(currentConfiguration, allConfigs, null);\n  }\n  getUserDataOidcFlowAndSave(idTokenSub, currentConfiguration, allConfigs) {\n    return this.getIdentityUserData(currentConfiguration).pipe(map(data => {\n      if (this.validateUserDataSubIdToken(currentConfiguration, idTokenSub, data?.sub)) {\n        this.setUserDataToStore(data, currentConfiguration, allConfigs);\n        return data;\n      } else {\n        // something went wrong, user data sub does not match that from id_token\n        this.loggerService.logWarning(currentConfiguration, `User data sub does not match sub in id_token, resetting`);\n        this.resetUserDataInStore(currentConfiguration, allConfigs);\n        return null;\n      }\n    }));\n  }\n  getIdentityUserData(currentConfiguration) {\n    const token = this.storagePersistenceService.getAccessToken(currentConfiguration);\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', currentConfiguration);\n    if (!authWellKnownEndPoints) {\n      this.loggerService.logWarning(currentConfiguration, 'init check session: authWellKnownEndpoints is undefined');\n      return throwError(() => new Error('authWellKnownEndpoints is undefined'));\n    }\n    const userInfoEndpoint = authWellKnownEndPoints.userInfoEndpoint;\n    if (!userInfoEndpoint) {\n      this.loggerService.logError(currentConfiguration, 'init check session: authWellKnownEndpoints.userinfo_endpoint is undefined; set auto_userinfo = false in config');\n      return throwError(() => new Error('authWellKnownEndpoints.userinfo_endpoint is undefined'));\n    }\n    return this.oidcDataService.get(userInfoEndpoint, currentConfiguration, token).pipe(retry(2));\n  }\n  validateUserDataSubIdToken(currentConfiguration, idTokenSub, userDataSub) {\n    if (!idTokenSub) {\n      return false;\n    }\n    if (!userDataSub) {\n      return false;\n    }\n    if (idTokenSub.toString() !== userDataSub.toString()) {\n      this.loggerService.logDebug(currentConfiguration, 'validateUserDataSubIdToken failed', idTokenSub, userDataSub);\n      return false;\n    }\n    return true;\n  }\n  fireUserDataEvent(currentConfiguration, allConfigs, passedUserData) {\n    const userData = this.composeSingleOrMultipleUserDataObject(currentConfiguration, allConfigs, passedUserData);\n    this.userDataInternal$.next(userData);\n    const {\n      configId\n    } = currentConfiguration;\n    this.eventService.fireEvent(EventTypes.UserDataChanged, {\n      configId,\n      userData: passedUserData\n    });\n  }\n  composeSingleOrMultipleUserDataObject(currentConfiguration, allConfigs, passedUserData) {\n    const hasManyConfigs = allConfigs.length > 1;\n    if (!hasManyConfigs) {\n      const {\n        configId\n      } = currentConfiguration;\n      return this.composeSingleUserDataResult(configId ?? '', passedUserData);\n    }\n    const allUserData = allConfigs.map(config => {\n      const currentConfigId = currentConfiguration.configId ?? '';\n      const configId = config.configId ?? '';\n      if (this.currentConfigIsToUpdate(currentConfigId, config)) {\n        return {\n          configId,\n          userData: passedUserData\n        };\n      }\n      const alreadySavedUserData = this.storagePersistenceService.read('userData', config) || null;\n      return {\n        configId,\n        userData: alreadySavedUserData\n      };\n    });\n    return {\n      userData: null,\n      allUserData\n    };\n  }\n  composeSingleUserDataResult(configId, userData) {\n    return {\n      userData,\n      allUserData: [{\n        configId,\n        userData\n      }]\n    };\n  }\n  currentConfigIsToUpdate(configId, config) {\n    return config.configId === configId;\n  }\n  static {\n    this.ɵfac = function UserService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UserService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ResetAuthDataService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.userService = inject(UserService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.authStateService = inject(AuthStateService);\n  }\n  resetAuthorizationData(currentConfiguration, allConfigs) {\n    if (!currentConfiguration) {\n      return;\n    }\n    this.userService.resetUserDataInStore(currentConfiguration, allConfigs);\n    this.flowsDataService.resetStorageFlowData(currentConfiguration);\n    this.authStateService.setUnauthenticatedAndFireEvent(currentConfiguration, allConfigs);\n    this.loggerService.logDebug(currentConfiguration, 'Local Login information cleaned up and event fired');\n  }\n  static {\n    this.ɵfac = function ResetAuthDataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ResetAuthDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ResetAuthDataService,\n      factory: ResetAuthDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResetAuthDataService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass SigninKeyDataService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.dataService = inject(DataService);\n  }\n  getSigningKeys(currentConfiguration) {\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', currentConfiguration);\n    const jwksUri = authWellKnownEndPoints?.jwksUri;\n    if (!jwksUri) {\n      const error = `getSigningKeys: authWellKnownEndpoints.jwksUri is: '${jwksUri}'`;\n      this.loggerService.logWarning(currentConfiguration, error);\n      return throwError(() => new Error(error));\n    }\n    this.loggerService.logDebug(currentConfiguration, 'Getting signinkeys from ', jwksUri);\n    return this.dataService.get(jwksUri, currentConfiguration).pipe(retry(2), catchError(e => this.handleErrorGetSigningKeys(e, currentConfiguration)));\n  }\n  handleErrorGetSigningKeys(errorResponse, currentConfiguration) {\n    let errMsg = '';\n    if (errorResponse instanceof HttpResponse) {\n      const body = errorResponse.body || {};\n      const err = JSON.stringify(body);\n      const {\n        status,\n        statusText\n      } = errorResponse;\n      errMsg = `${status || ''} - ${statusText || ''} ${err || ''}`;\n    } else {\n      const {\n        message\n      } = errorResponse;\n      errMsg = !!message ? message : `${errorResponse}`;\n    }\n    this.loggerService.logError(currentConfiguration, errMsg);\n    return throwError(() => new Error(errMsg));\n  }\n  static {\n    this.ɵfac = function SigninKeyDataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SigninKeyDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SigninKeyDataService,\n      factory: SigninKeyDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SigninKeyDataService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst JWT_KEYS = 'jwtKeys';\nclass HistoryJwtKeysCallbackHandlerService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.authStateService = inject(AuthStateService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.signInKeyDataService = inject(SigninKeyDataService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.resetAuthDataService = inject(ResetAuthDataService);\n    this.document = inject(DOCUMENT);\n  }\n  // STEP 3 Code Flow, STEP 2 Implicit Flow, STEP 3 Refresh Token\n  callbackHistoryAndResetJwtKeys(callbackContext, config, allConfigs) {\n    let toWrite = {\n      ...callbackContext.authResult\n    };\n    if (!this.responseHasIdToken(callbackContext)) {\n      const existingIdToken = this.storagePersistenceService.getIdToken(config);\n      toWrite = {\n        ...toWrite,\n        id_token: existingIdToken\n      };\n    }\n    this.storagePersistenceService.write('authnResult', toWrite, config);\n    if (config.allowUnsafeReuseRefreshToken && callbackContext.authResult?.refresh_token) {\n      this.storagePersistenceService.write('reusable_refresh_token', callbackContext.authResult.refresh_token, config);\n    }\n    if (this.historyCleanUpTurnedOn(config) && !callbackContext.isRenewProcess) {\n      this.resetBrowserHistory();\n    } else {\n      this.loggerService.logDebug(config, 'history clean up inactive');\n    }\n    if (callbackContext.authResult?.error) {\n      const errorMessage = `AuthCallback AuthResult came with error: ${callbackContext.authResult.error}`;\n      this.loggerService.logDebug(config, errorMessage);\n      this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n      this.flowsDataService.setNonce('', config);\n      this.handleResultErrorFromCallback(callbackContext.authResult, callbackContext.isRenewProcess);\n      return throwError(() => new Error(errorMessage));\n    }\n    this.loggerService.logDebug(config, `AuthResult '${JSON.stringify(callbackContext.authResult, null, 2)}'.\n      AuthCallback created, begin token validation`);\n    return this.signInKeyDataService.getSigningKeys(config).pipe(tap(jwtKeys => this.storeSigningKeys(jwtKeys, config)), catchError(err => {\n      // fallback: try to load jwtKeys from storage\n      const storedJwtKeys = this.readSigningKeys(config);\n      if (!!storedJwtKeys) {\n        this.loggerService.logWarning(config, `Failed to retrieve signing keys, fallback to stored keys`);\n        return of(storedJwtKeys);\n      }\n      return throwError(() => new Error(err));\n    }), switchMap(jwtKeys => {\n      if (jwtKeys) {\n        callbackContext.jwtKeys = jwtKeys;\n        return of(callbackContext);\n      }\n      const errorMessage = `Failed to retrieve signing key`;\n      this.loggerService.logWarning(config, errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }), catchError(err => {\n      const errorMessage = `Failed to retrieve signing key with error: ${err}`;\n      this.loggerService.logWarning(config, errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  responseHasIdToken(callbackContext) {\n    return !!callbackContext?.authResult?.id_token;\n  }\n  handleResultErrorFromCallback(result, isRenewProcess) {\n    let validationResult = ValidationResult.SecureTokenServerError;\n    if (result && typeof result === 'object' && 'error' in result && result.error === 'login_required') {\n      validationResult = ValidationResult.LoginRequired;\n    }\n    this.authStateService.updateAndPublishAuthState({\n      isAuthenticated: false,\n      validationResult,\n      isRenewProcess\n    });\n  }\n  historyCleanUpTurnedOn(config) {\n    const {\n      historyCleanupOff\n    } = config;\n    return !historyCleanupOff;\n  }\n  resetBrowserHistory() {\n    this.document.defaultView?.history.replaceState({}, this.document.title, this.document.defaultView.location.origin + this.document.defaultView.location.pathname);\n  }\n  storeSigningKeys(jwtKeys, config) {\n    this.storagePersistenceService.write(JWT_KEYS, jwtKeys, config);\n  }\n  readSigningKeys(config) {\n    return this.storagePersistenceService.read(JWT_KEYS, config);\n  }\n  static {\n    this.ɵfac = function HistoryJwtKeysCallbackHandlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HistoryJwtKeysCallbackHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HistoryJwtKeysCallbackHandlerService,\n      factory: HistoryJwtKeysCallbackHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HistoryJwtKeysCallbackHandlerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ImplicitFlowCallbackHandlerService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.resetAuthDataService = inject(ResetAuthDataService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.document = inject(DOCUMENT);\n  }\n  // STEP 1 Code Flow\n  // STEP 1 Implicit Flow\n  implicitFlowCallback(config, allConfigs, hash) {\n    const isRenewProcessData = this.flowsDataService.isSilentRenewRunning(config);\n    this.loggerService.logDebug(config, 'BEGIN callback, no auth data');\n    if (!isRenewProcessData) {\n      this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n    }\n    hash = hash || this.document.location.hash.substring(1);\n    const authResult = hash.split('&').reduce((resultData, item) => {\n      const parts = item.split('=');\n      resultData[parts.shift()] = parts.join('=');\n      return resultData;\n    }, {});\n    const callbackContext = {\n      code: '',\n      refreshToken: '',\n      state: '',\n      sessionState: null,\n      authResult,\n      isRenewProcess: isRenewProcessData,\n      jwtKeys: null,\n      validationResult: null,\n      existingIdToken: null\n    };\n    return of(callbackContext);\n  }\n  static {\n    this.ɵfac = function ImplicitFlowCallbackHandlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImplicitFlowCallbackHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ImplicitFlowCallbackHandlerService,\n      factory: ImplicitFlowCallbackHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImplicitFlowCallbackHandlerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RefreshSessionCallbackHandlerService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.authStateService = inject(AuthStateService);\n    this.flowsDataService = inject(FlowsDataService);\n  }\n  // STEP 1 Refresh session\n  refreshSessionWithRefreshTokens(config) {\n    const stateData = this.flowsDataService.getExistingOrCreateAuthStateControl(config);\n    this.loggerService.logDebug(config, 'RefreshSession created. Adding myautostate: ' + stateData);\n    const refreshToken = this.authStateService.getRefreshToken(config);\n    const idToken = this.authStateService.getIdToken(config);\n    if (refreshToken) {\n      const callbackContext = {\n        code: '',\n        refreshToken,\n        state: stateData,\n        sessionState: null,\n        authResult: null,\n        isRenewProcess: true,\n        jwtKeys: null,\n        validationResult: null,\n        existingIdToken: idToken\n      };\n      this.loggerService.logDebug(config, 'found refresh code, obtaining new credentials with refresh code');\n      // Nonce is not used with refresh tokens; but Key cloak may send it anyway\n      this.flowsDataService.setNonce(TokenValidationService.refreshTokenNoncePlaceholder, config);\n      return of(callbackContext);\n    } else {\n      const errorMessage = 'no refresh token found, please login';\n      this.loggerService.logError(config, errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }\n  }\n  static {\n    this.ɵfac = function RefreshSessionCallbackHandlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RefreshSessionCallbackHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RefreshSessionCallbackHandlerService,\n      factory: RefreshSessionCallbackHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RefreshSessionCallbackHandlerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RefreshTokenCallbackHandlerService {\n  constructor() {\n    this.urlService = inject(UrlService);\n    this.loggerService = inject(LoggerService);\n    this.dataService = inject(DataService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n  }\n  // STEP 2 Refresh Token\n  refreshTokensRequestTokens(callbackContext, config, customParamsRefresh) {\n    let headers = new HttpHeaders();\n    headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');\n    const authWellknownEndpoints = this.storagePersistenceService.read('authWellKnownEndPoints', config);\n    const tokenEndpoint = authWellknownEndpoints?.tokenEndpoint;\n    if (!tokenEndpoint) {\n      return throwError(() => new Error('Token Endpoint not defined'));\n    }\n    const data = this.urlService.createBodyForCodeFlowRefreshTokensRequest(callbackContext.refreshToken, config, customParamsRefresh);\n    return this.dataService.post(tokenEndpoint, data, config, headers).pipe(switchMap(response => {\n      this.loggerService.logDebug(config, `token refresh response: ${response}`);\n      if (response) {\n        response.state = callbackContext.state;\n      }\n      callbackContext.authResult = response;\n      return of(callbackContext);\n    }), retryWhen(error => this.handleRefreshRetry(error, config)), catchError(error => {\n      const {\n        authority\n      } = config;\n      const errorMessage = `OidcService code request ${authority}`;\n      this.loggerService.logError(config, errorMessage, error);\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  handleRefreshRetry(errors, config) {\n    return errors.pipe(mergeMap(error => {\n      // retry token refresh if there is no internet connection\n      if (isNetworkError(error)) {\n        const {\n          authority,\n          refreshTokenRetryInSeconds\n        } = config;\n        const errorMessage = `OidcService code request ${authority} - no internet connection`;\n        this.loggerService.logWarning(config, errorMessage, error);\n        return timer((refreshTokenRetryInSeconds ?? 0) * 1000);\n      }\n      return throwError(() => error);\n    }));\n  }\n  static {\n    this.ɵfac = function RefreshTokenCallbackHandlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RefreshTokenCallbackHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RefreshTokenCallbackHandlerService,\n      factory: RefreshTokenCallbackHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RefreshTokenCallbackHandlerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass EqualityService {\n  isStringEqualOrNonOrderedArrayEqual(value1, value2) {\n    if (this.isNullOrUndefined(value1)) {\n      return false;\n    }\n    if (this.isNullOrUndefined(value2)) {\n      return false;\n    }\n    if (this.oneValueIsStringAndTheOtherIsArray(value1, value2)) {\n      return false;\n    }\n    if (this.bothValuesAreStrings(value1, value2)) {\n      return value1 === value2;\n    }\n    return this.arraysHaveEqualContent(value1, value2);\n  }\n  areEqual(value1, value2) {\n    if (!value1 || !value2) {\n      return false;\n    }\n    if (this.bothValuesAreArrays(value1, value2)) {\n      return this.arraysStrictEqual(value1, value2);\n    }\n    if (this.bothValuesAreStrings(value1, value2)) {\n      return value1 === value2;\n    }\n    if (this.bothValuesAreObjects(value1, value2)) {\n      return JSON.stringify(value1).toLowerCase() === JSON.stringify(value2).toLowerCase();\n    }\n    if (this.oneValueIsStringAndTheOtherIsArray(value1, value2)) {\n      if (Array.isArray(value1) && this.valueIsString(value2)) {\n        return value1[0] === value2;\n      }\n      if (Array.isArray(value2) && this.valueIsString(value1)) {\n        return value2[0] === value1;\n      }\n    }\n    return value1 === value2;\n  }\n  oneValueIsStringAndTheOtherIsArray(value1, value2) {\n    return Array.isArray(value1) && this.valueIsString(value2) || Array.isArray(value2) && this.valueIsString(value1);\n  }\n  bothValuesAreObjects(value1, value2) {\n    return this.valueIsObject(value1) && this.valueIsObject(value2);\n  }\n  bothValuesAreStrings(value1, value2) {\n    return this.valueIsString(value1) && this.valueIsString(value2);\n  }\n  bothValuesAreArrays(value1, value2) {\n    return Array.isArray(value1) && Array.isArray(value2);\n  }\n  valueIsString(value) {\n    return typeof value === 'string' || value instanceof String;\n  }\n  valueIsObject(value) {\n    return typeof value === 'object';\n  }\n  arraysStrictEqual(arr1, arr2) {\n    if (arr1.length !== arr2.length) {\n      return false;\n    }\n    for (let i = arr1.length; i--;) {\n      if (arr1[i] !== arr2[i]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  arraysHaveEqualContent(arr1, arr2) {\n    if (arr1.length !== arr2.length) {\n      return false;\n    }\n    return arr1.some(v => arr2.includes(v));\n  }\n  isNullOrUndefined(val) {\n    return val === null || val === undefined;\n  }\n  static {\n    this.ɵfac = function EqualityService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EqualityService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EqualityService,\n      factory: EqualityService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EqualityService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass StateValidationResult {\n  constructor(accessToken = '', idToken = '', authResponseIsValid = false, decodedIdToken = {\n    at_hash: ''\n  }, state = ValidationResult.NotSet) {\n    this.accessToken = accessToken;\n    this.idToken = idToken;\n    this.authResponseIsValid = authResponseIsValid;\n    this.decodedIdToken = decodedIdToken;\n    this.state = state;\n  }\n}\nclass StateValidationService {\n  constructor() {\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.tokenValidationService = inject(TokenValidationService);\n    this.tokenHelperService = inject(TokenHelperService);\n    this.loggerService = inject(LoggerService);\n    this.equalityService = inject(EqualityService);\n    this.flowHelper = inject(FlowHelper);\n  }\n  getValidatedStateResult(callbackContext, configuration) {\n    const hasError = Boolean(callbackContext.authResult?.error);\n    const hasCallbackContext = Boolean(callbackContext);\n    if (!hasCallbackContext || hasError) {\n      return of(new StateValidationResult('', '', false, {}));\n    }\n    return this.validateState(callbackContext, configuration);\n  }\n  validateState(callbackContext, configuration) {\n    const toReturn = new StateValidationResult();\n    const authStateControl = this.storagePersistenceService.read('authStateControl', configuration);\n    if (!this.tokenValidationService.validateStateFromHashCallback(callbackContext.authResult?.state, authStateControl, configuration)) {\n      this.loggerService.logWarning(configuration, 'authCallback incorrect state');\n      toReturn.state = ValidationResult.StatesDoNotMatch;\n      this.handleUnsuccessfulValidation(configuration);\n      return of(toReturn);\n    }\n    const isCurrentFlowImplicitFlowWithAccessToken = this.flowHelper.isCurrentFlowImplicitFlowWithAccessToken(configuration);\n    const isCurrentFlowCodeFlow = this.flowHelper.isCurrentFlowCodeFlow(configuration);\n    if (isCurrentFlowImplicitFlowWithAccessToken || isCurrentFlowCodeFlow) {\n      toReturn.accessToken = callbackContext.authResult?.access_token ?? '';\n    }\n    const disableIdTokenValidation = configuration.disableIdTokenValidation;\n    if (disableIdTokenValidation) {\n      toReturn.state = ValidationResult.Ok;\n      toReturn.authResponseIsValid = true;\n      return of(toReturn);\n    }\n    const isInRefreshTokenFlow = callbackContext.isRenewProcess && !!callbackContext.refreshToken;\n    const hasIdToken = Boolean(callbackContext.authResult?.id_token);\n    if (isInRefreshTokenFlow && !hasIdToken) {\n      toReturn.state = ValidationResult.Ok;\n      toReturn.authResponseIsValid = true;\n      return of(toReturn);\n    }\n    if (hasIdToken) {\n      const {\n        clientId,\n        issValidationOff,\n        maxIdTokenIatOffsetAllowedInSeconds,\n        disableIatOffsetValidation,\n        ignoreNonceAfterRefresh,\n        renewTimeBeforeTokenExpiresInSeconds\n      } = configuration;\n      toReturn.idToken = callbackContext.authResult?.id_token ?? '';\n      toReturn.decodedIdToken = this.tokenHelperService.getPayloadFromToken(toReturn.idToken, false, configuration);\n      return this.tokenValidationService.validateSignatureIdToken(toReturn.idToken, callbackContext.jwtKeys, configuration).pipe(mergeMap(isSignatureIdTokenValid => {\n        if (!isSignatureIdTokenValid) {\n          this.loggerService.logDebug(configuration, 'authCallback Signature validation failed id_token');\n          toReturn.state = ValidationResult.SignatureFailed;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        const authNonce = this.storagePersistenceService.read('authNonce', configuration);\n        if (!this.tokenValidationService.validateIdTokenNonce(toReturn.decodedIdToken, authNonce, Boolean(ignoreNonceAfterRefresh), configuration)) {\n          this.loggerService.logWarning(configuration, 'authCallback incorrect nonce, did you call the checkAuth() method multiple times?');\n          toReturn.state = ValidationResult.IncorrectNonce;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        if (!this.tokenValidationService.validateRequiredIdToken(toReturn.decodedIdToken, configuration)) {\n          this.loggerService.logDebug(configuration, 'authCallback Validation, one of the REQUIRED properties missing from id_token');\n          toReturn.state = ValidationResult.RequiredPropertyMissing;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        if (!isInRefreshTokenFlow && !this.tokenValidationService.validateIdTokenIatMaxOffset(toReturn.decodedIdToken, maxIdTokenIatOffsetAllowedInSeconds ?? 120, Boolean(disableIatOffsetValidation), configuration)) {\n          this.loggerService.logWarning(configuration, 'authCallback Validation, iat rejected id_token was issued too far away from the current time');\n          toReturn.state = ValidationResult.MaxOffsetExpired;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n        if (authWellKnownEndPoints) {\n          if (issValidationOff) {\n            this.loggerService.logDebug(configuration, 'iss validation is turned off, this is not recommended!');\n          } else if (!issValidationOff && !this.tokenValidationService.validateIdTokenIss(toReturn.decodedIdToken, authWellKnownEndPoints.issuer, configuration)) {\n            this.loggerService.logWarning(configuration, 'authCallback incorrect iss does not match authWellKnownEndpoints issuer');\n            toReturn.state = ValidationResult.IssDoesNotMatchIssuer;\n            this.handleUnsuccessfulValidation(configuration);\n            return of(toReturn);\n          }\n        } else {\n          this.loggerService.logWarning(configuration, 'authWellKnownEndpoints is undefined');\n          toReturn.state = ValidationResult.NoAuthWellKnownEndPoints;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        if (!this.tokenValidationService.validateIdTokenAud(toReturn.decodedIdToken, clientId, configuration)) {\n          this.loggerService.logWarning(configuration, 'authCallback incorrect aud');\n          toReturn.state = ValidationResult.IncorrectAud;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        if (!this.tokenValidationService.validateIdTokenAzpExistsIfMoreThanOneAud(toReturn.decodedIdToken)) {\n          this.loggerService.logWarning(configuration, 'authCallback missing azp');\n          toReturn.state = ValidationResult.IncorrectAzp;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        if (!this.tokenValidationService.validateIdTokenAzpValid(toReturn.decodedIdToken, clientId)) {\n          this.loggerService.logWarning(configuration, 'authCallback incorrect azp');\n          toReturn.state = ValidationResult.IncorrectAzp;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        if (!this.isIdTokenAfterRefreshTokenRequestValid(callbackContext, toReturn.decodedIdToken, configuration)) {\n          this.loggerService.logWarning(configuration, 'authCallback pre, post id_token claims do not match in refresh');\n          toReturn.state = ValidationResult.IncorrectIdTokenClaimsAfterRefresh;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        if (!isInRefreshTokenFlow && !this.tokenValidationService.validateIdTokenExpNotExpired(toReturn.decodedIdToken, configuration, renewTimeBeforeTokenExpiresInSeconds)) {\n          this.loggerService.logWarning(configuration, 'authCallback id token expired');\n          toReturn.state = ValidationResult.TokenExpired;\n          this.handleUnsuccessfulValidation(configuration);\n          return of(toReturn);\n        }\n        return this.validateDefault(isCurrentFlowImplicitFlowWithAccessToken, isCurrentFlowCodeFlow, toReturn, configuration, callbackContext);\n      }));\n    } else {\n      this.loggerService.logDebug(configuration, 'No id_token found, skipping id_token validation');\n    }\n    return this.validateDefault(isCurrentFlowImplicitFlowWithAccessToken, isCurrentFlowCodeFlow, toReturn, configuration, callbackContext);\n  }\n  validateDefault(isCurrentFlowImplicitFlowWithAccessToken, isCurrentFlowCodeFlow, toReturn, configuration, callbackContext) {\n    // flow id_token\n    if (!isCurrentFlowImplicitFlowWithAccessToken && !isCurrentFlowCodeFlow) {\n      toReturn.authResponseIsValid = true;\n      toReturn.state = ValidationResult.Ok;\n      this.handleSuccessfulValidation(configuration);\n      this.handleUnsuccessfulValidation(configuration);\n      return of(toReturn);\n    }\n    // only do check if id_token returned, no always the case when using refresh tokens\n    if (callbackContext.authResult?.id_token) {\n      const idTokenHeader = this.tokenHelperService.getHeaderFromToken(toReturn.idToken, false, configuration);\n      if (isCurrentFlowCodeFlow && !toReturn.decodedIdToken.at_hash) {\n        this.loggerService.logDebug(configuration, 'Code Flow active, and no at_hash in the id_token, skipping check!');\n      } else {\n        return this.tokenValidationService.validateIdTokenAtHash(toReturn.accessToken, toReturn.decodedIdToken.at_hash, idTokenHeader.alg,\n        // 'RS256'\n        configuration).pipe(map(valid => {\n          if (!valid || !toReturn.accessToken) {\n            this.loggerService.logWarning(configuration, 'authCallback incorrect at_hash');\n            toReturn.state = ValidationResult.IncorrectAtHash;\n            this.handleUnsuccessfulValidation(configuration);\n            return toReturn;\n          } else {\n            toReturn.authResponseIsValid = true;\n            toReturn.state = ValidationResult.Ok;\n            this.handleSuccessfulValidation(configuration);\n            return toReturn;\n          }\n        }));\n      }\n    }\n    toReturn.authResponseIsValid = true;\n    toReturn.state = ValidationResult.Ok;\n    this.handleSuccessfulValidation(configuration);\n    return of(toReturn);\n  }\n  isIdTokenAfterRefreshTokenRequestValid(callbackContext, newIdToken, configuration) {\n    const {\n      useRefreshToken,\n      disableRefreshIdTokenAuthTimeValidation\n    } = configuration;\n    if (!useRefreshToken) {\n      return true;\n    }\n    if (!callbackContext.existingIdToken) {\n      return true;\n    }\n    const decodedIdToken = this.tokenHelperService.getPayloadFromToken(callbackContext.existingIdToken, false, configuration);\n    // Upon successful validation of the Refresh Token, the response body is the Token Response of Section 3.1.3.3\n    // except that it might not contain an id_token.\n    // If an ID Token is returned as a result of a token refresh request, the following requirements apply:\n    // its iss Claim Value MUST be the same as in the ID Token issued when the original authentication occurred,\n    if (decodedIdToken.iss !== newIdToken.iss) {\n      this.loggerService.logDebug(configuration, `iss do not match: ${decodedIdToken.iss} ${newIdToken.iss}`);\n      return false;\n    }\n    // its azp Claim Value MUST be the same as in the ID Token issued when the original authentication occurred;\n    //   if no azp Claim was present in the original ID Token, one MUST NOT be present in the new ID Token, and\n    // otherwise, the same rules apply as apply when issuing an ID Token at the time of the original authentication.\n    if (decodedIdToken.azp !== newIdToken.azp) {\n      this.loggerService.logDebug(configuration, `azp do not match: ${decodedIdToken.azp} ${newIdToken.azp}`);\n      return false;\n    }\n    // its sub Claim Value MUST be the same as in the ID Token issued when the original authentication occurred,\n    if (decodedIdToken.sub !== newIdToken.sub) {\n      this.loggerService.logDebug(configuration, `sub do not match: ${decodedIdToken.sub} ${newIdToken.sub}`);\n      return false;\n    }\n    // its aud Claim Value MUST be the same as in the ID Token issued when the original authentication occurred,\n    if (!this.equalityService.isStringEqualOrNonOrderedArrayEqual(decodedIdToken?.aud, newIdToken?.aud)) {\n      this.loggerService.logDebug(configuration, `aud in new id_token is not valid: '${decodedIdToken?.aud}' '${newIdToken.aud}'`);\n      return false;\n    }\n    if (disableRefreshIdTokenAuthTimeValidation) {\n      return true;\n    }\n    // its iat Claim MUST represent the time that the new ID Token is issued,\n    // if the ID Token contains an auth_time Claim, its value MUST represent the time of the original authentication\n    // - not the time that the new ID token is issued,\n    if (decodedIdToken.auth_time !== newIdToken.auth_time) {\n      this.loggerService.logDebug(configuration, `auth_time do not match: ${decodedIdToken.auth_time} ${newIdToken.auth_time}`);\n      return false;\n    }\n    return true;\n  }\n  handleSuccessfulValidation(configuration) {\n    const {\n      autoCleanStateAfterAuthentication\n    } = configuration;\n    this.storagePersistenceService.write('authNonce', null, configuration);\n    if (autoCleanStateAfterAuthentication) {\n      this.storagePersistenceService.write('authStateControl', '', configuration);\n    }\n    this.loggerService.logDebug(configuration, 'authCallback token(s) validated, continue');\n  }\n  handleUnsuccessfulValidation(configuration) {\n    const {\n      autoCleanStateAfterAuthentication\n    } = configuration;\n    this.storagePersistenceService.write('authNonce', null, configuration);\n    if (autoCleanStateAfterAuthentication) {\n      this.storagePersistenceService.write('authStateControl', '', configuration);\n    }\n    this.loggerService.logDebug(configuration, 'authCallback token(s) invalid');\n  }\n  static {\n    this.ɵfac = function StateValidationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StateValidationService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: StateValidationService,\n      factory: StateValidationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StateValidationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass StateValidationCallbackHandlerService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.stateValidationService = inject(StateValidationService);\n    this.authStateService = inject(AuthStateService);\n    this.resetAuthDataService = inject(ResetAuthDataService);\n    this.document = inject(DOCUMENT);\n  }\n  // STEP 4 All flows\n  callbackStateValidation(callbackContext, configuration, allConfigs) {\n    return this.stateValidationService.getValidatedStateResult(callbackContext, configuration).pipe(map(validationResult => {\n      callbackContext.validationResult = validationResult;\n      if (validationResult.authResponseIsValid) {\n        this.authStateService.setAuthorizationData(validationResult.accessToken, callbackContext.authResult, configuration, allConfigs);\n        return callbackContext;\n      } else {\n        const errorMessage = `authorizedCallback, token(s) validation failed, resetting. Hash: ${this.document.location.hash}`;\n        this.loggerService.logWarning(configuration, errorMessage);\n        this.resetAuthDataService.resetAuthorizationData(configuration, allConfigs);\n        this.publishUnauthorizedState(callbackContext.validationResult, callbackContext.isRenewProcess);\n        throw new Error(errorMessage);\n      }\n    }));\n  }\n  publishUnauthorizedState(stateValidationResult, isRenewProcess) {\n    this.authStateService.updateAndPublishAuthState({\n      isAuthenticated: false,\n      validationResult: stateValidationResult.state,\n      isRenewProcess\n    });\n  }\n  static {\n    this.ɵfac = function StateValidationCallbackHandlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StateValidationCallbackHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: StateValidationCallbackHandlerService,\n      factory: StateValidationCallbackHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StateValidationCallbackHandlerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass UserCallbackHandlerService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.authStateService = inject(AuthStateService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.userService = inject(UserService);\n    this.resetAuthDataService = inject(ResetAuthDataService);\n  }\n  // STEP 5 userData\n  callbackUser(callbackContext, configuration, allConfigs) {\n    const {\n      isRenewProcess,\n      validationResult,\n      authResult,\n      refreshToken\n    } = callbackContext;\n    const {\n      autoUserInfo,\n      renewUserInfoAfterTokenRenew\n    } = configuration;\n    if (!autoUserInfo) {\n      if (!isRenewProcess || renewUserInfoAfterTokenRenew) {\n        // userData is set to the id_token decoded, auto get user data set to false\n        if (validationResult?.decodedIdToken) {\n          this.userService.setUserDataToStore(validationResult.decodedIdToken, configuration, allConfigs);\n        }\n      }\n      if (!isRenewProcess && !refreshToken) {\n        this.flowsDataService.setSessionState(authResult?.session_state, configuration);\n      }\n      this.publishAuthState(validationResult, isRenewProcess);\n      return of(callbackContext);\n    }\n    return this.userService.getAndPersistUserDataInStore(configuration, allConfigs, isRenewProcess, validationResult?.idToken, validationResult?.decodedIdToken).pipe(switchMap(userData => {\n      if (!!userData) {\n        if (!refreshToken) {\n          this.flowsDataService.setSessionState(authResult?.session_state, configuration);\n        }\n        this.publishAuthState(validationResult, isRenewProcess);\n        return of(callbackContext);\n      } else {\n        this.resetAuthDataService.resetAuthorizationData(configuration, allConfigs);\n        this.publishUnauthenticatedState(validationResult, isRenewProcess);\n        const errorMessage = `Called for userData but they were ${userData}`;\n        this.loggerService.logWarning(configuration, errorMessage);\n        return throwError(() => new Error(errorMessage));\n      }\n    }), catchError(err => {\n      const errorMessage = `Failed to retrieve user info with error:  ${err}`;\n      this.loggerService.logWarning(configuration, errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  publishAuthState(stateValidationResult, isRenewProcess) {\n    if (!stateValidationResult) {\n      return;\n    }\n    this.authStateService.updateAndPublishAuthState({\n      isAuthenticated: true,\n      validationResult: stateValidationResult.state,\n      isRenewProcess\n    });\n  }\n  publishUnauthenticatedState(stateValidationResult, isRenewProcess) {\n    if (!stateValidationResult) {\n      return;\n    }\n    this.authStateService.updateAndPublishAuthState({\n      isAuthenticated: false,\n      validationResult: stateValidationResult.state,\n      isRenewProcess\n    });\n  }\n  static {\n    this.ɵfac = function UserCallbackHandlerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserCallbackHandlerService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: UserCallbackHandlerService,\n      factory: UserCallbackHandlerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UserCallbackHandlerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass FlowsService {\n  constructor() {\n    this.codeFlowCallbackHandlerService = inject(CodeFlowCallbackHandlerService);\n    this.implicitFlowCallbackHandlerService = inject(ImplicitFlowCallbackHandlerService);\n    this.historyJwtKeysCallbackHandlerService = inject(HistoryJwtKeysCallbackHandlerService);\n    this.userHandlerService = inject(UserCallbackHandlerService);\n    this.stateValidationCallbackHandlerService = inject(StateValidationCallbackHandlerService);\n    this.refreshSessionCallbackHandlerService = inject(RefreshSessionCallbackHandlerService);\n    this.refreshTokenCallbackHandlerService = inject(RefreshTokenCallbackHandlerService);\n  }\n  processCodeFlowCallback(urlToCheck, config, allConfigs) {\n    return this.codeFlowCallbackHandlerService.codeFlowCallback(urlToCheck, config).pipe(concatMap(callbackContext => this.codeFlowCallbackHandlerService.codeFlowCodeRequest(callbackContext, config)), concatMap(callbackContext => this.historyJwtKeysCallbackHandlerService.callbackHistoryAndResetJwtKeys(callbackContext, config, allConfigs)), concatMap(callbackContext => this.stateValidationCallbackHandlerService.callbackStateValidation(callbackContext, config, allConfigs)), concatMap(callbackContext => this.userHandlerService.callbackUser(callbackContext, config, allConfigs)));\n  }\n  processSilentRenewCodeFlowCallback(firstContext, config, allConfigs) {\n    return this.codeFlowCallbackHandlerService.codeFlowCodeRequest(firstContext, config).pipe(concatMap(callbackContext => this.historyJwtKeysCallbackHandlerService.callbackHistoryAndResetJwtKeys(callbackContext, config, allConfigs)), concatMap(callbackContext => this.stateValidationCallbackHandlerService.callbackStateValidation(callbackContext, config, allConfigs)), concatMap(callbackContext => this.userHandlerService.callbackUser(callbackContext, config, allConfigs)));\n  }\n  processImplicitFlowCallback(config, allConfigs, hash) {\n    return this.implicitFlowCallbackHandlerService.implicitFlowCallback(config, allConfigs, hash).pipe(concatMap(callbackContext => this.historyJwtKeysCallbackHandlerService.callbackHistoryAndResetJwtKeys(callbackContext, config, allConfigs)), concatMap(callbackContext => this.stateValidationCallbackHandlerService.callbackStateValidation(callbackContext, config, allConfigs)), concatMap(callbackContext => this.userHandlerService.callbackUser(callbackContext, config, allConfigs)));\n  }\n  processRefreshToken(config, allConfigs, customParamsRefresh) {\n    return this.refreshSessionCallbackHandlerService.refreshSessionWithRefreshTokens(config).pipe(concatMap(callbackContext => this.refreshTokenCallbackHandlerService.refreshTokensRequestTokens(callbackContext, config, customParamsRefresh)), concatMap(callbackContext => this.historyJwtKeysCallbackHandlerService.callbackHistoryAndResetJwtKeys(callbackContext, config, allConfigs)), concatMap(callbackContext => this.stateValidationCallbackHandlerService.callbackStateValidation(callbackContext, config, allConfigs)), concatMap(callbackContext => this.userHandlerService.callbackUser(callbackContext, config, allConfigs)));\n  }\n  static {\n    this.ɵfac = function FlowsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FlowsService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FlowsService,\n      factory: FlowsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FlowsService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass IntervalService {\n  constructor() {\n    this.zone = inject(NgZone);\n    this.document = inject(DOCUMENT);\n    this.runTokenValidationRunning = null;\n  }\n  isTokenValidationRunning() {\n    return Boolean(this.runTokenValidationRunning);\n  }\n  stopPeriodicTokenCheck() {\n    if (this.runTokenValidationRunning) {\n      this.runTokenValidationRunning.unsubscribe();\n      this.runTokenValidationRunning = null;\n    }\n  }\n  startPeriodicTokenCheck(repeatAfterSeconds) {\n    const millisecondsDelayBetweenTokenCheck = repeatAfterSeconds * 1000;\n    return new Observable(subscriber => {\n      let intervalId;\n      this.zone.runOutsideAngular(() => {\n        intervalId = this.document?.defaultView?.setInterval(() => this.zone.run(() => subscriber.next()), millisecondsDelayBetweenTokenCheck);\n      });\n      return () => {\n        clearInterval(intervalId);\n      };\n    });\n  }\n  static {\n    this.ɵfac = function IntervalService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IntervalService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: IntervalService,\n      factory: IntervalService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IntervalService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass CodeFlowCallbackService {\n  constructor() {\n    this.flowsService = inject(FlowsService);\n    this.router = inject(Router);\n    this.flowsDataService = inject(FlowsDataService);\n    this.intervalService = inject(IntervalService);\n  }\n  authenticatedCallbackWithCode(urlToCheck, config, allConfigs) {\n    const isRenewProcess = this.flowsDataService.isSilentRenewRunning(config);\n    const {\n      triggerAuthorizationResultEvent\n    } = config;\n    const postLoginRoute = config.postLoginRoute || '/';\n    const unauthorizedRoute = config.unauthorizedRoute || '/';\n    return this.flowsService.processCodeFlowCallback(urlToCheck, config, allConfigs).pipe(tap(callbackContext => {\n      this.flowsDataService.resetCodeFlowInProgress(config);\n      if (!triggerAuthorizationResultEvent && !callbackContext.isRenewProcess) {\n        this.router.navigateByUrl(postLoginRoute);\n      }\n    }), catchError(error => {\n      this.flowsDataService.resetSilentRenewRunning(config);\n      this.flowsDataService.resetCodeFlowInProgress(config);\n      this.intervalService.stopPeriodicTokenCheck();\n      if (!triggerAuthorizationResultEvent && !isRenewProcess) {\n        this.router.navigateByUrl(unauthorizedRoute);\n      }\n      return throwError(() => new Error(error));\n    }));\n  }\n  static {\n    this.ɵfac = function CodeFlowCallbackService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CodeFlowCallbackService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CodeFlowCallbackService,\n      factory: CodeFlowCallbackService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CodeFlowCallbackService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ImplicitFlowCallbackService {\n  constructor() {\n    this.flowsService = inject(FlowsService);\n    this.router = inject(Router);\n    this.flowsDataService = inject(FlowsDataService);\n    this.intervalService = inject(IntervalService);\n  }\n  authenticatedImplicitFlowCallback(config, allConfigs, hash) {\n    const isRenewProcess = this.flowsDataService.isSilentRenewRunning(config);\n    const triggerAuthorizationResultEvent = Boolean(config.triggerAuthorizationResultEvent);\n    const postLoginRoute = config.postLoginRoute ?? '';\n    const unauthorizedRoute = config.unauthorizedRoute ?? '';\n    return this.flowsService.processImplicitFlowCallback(config, allConfigs, hash).pipe(tap(callbackContext => {\n      if (!triggerAuthorizationResultEvent && !callbackContext.isRenewProcess) {\n        this.router.navigateByUrl(postLoginRoute);\n      }\n    }), catchError(error => {\n      this.flowsDataService.resetSilentRenewRunning(config);\n      this.intervalService.stopPeriodicTokenCheck();\n      if (!triggerAuthorizationResultEvent && !isRenewProcess) {\n        this.router.navigateByUrl(unauthorizedRoute);\n      }\n      return throwError(() => new Error(error));\n    }));\n  }\n  static {\n    this.ɵfac = function ImplicitFlowCallbackService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImplicitFlowCallbackService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ImplicitFlowCallbackService,\n      factory: ImplicitFlowCallbackService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImplicitFlowCallbackService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass CallbackService {\n  constructor() {\n    this.urlService = inject(UrlService);\n    this.flowHelper = inject(FlowHelper);\n    this.implicitFlowCallbackService = inject(ImplicitFlowCallbackService);\n    this.codeFlowCallbackService = inject(CodeFlowCallbackService);\n    this.stsCallbackInternal$ = new Subject();\n  }\n  get stsCallback$() {\n    return this.stsCallbackInternal$.asObservable();\n  }\n  isCallback(currentUrl) {\n    if (!currentUrl) {\n      return false;\n    }\n    return this.urlService.isCallbackFromSts(currentUrl);\n  }\n  handleCallbackAndFireEvents(currentCallbackUrl, config, allConfigs) {\n    let callback$ = new Observable();\n    if (this.flowHelper.isCurrentFlowCodeFlow(config)) {\n      callback$ = this.codeFlowCallbackService.authenticatedCallbackWithCode(currentCallbackUrl, config, allConfigs);\n    } else if (this.flowHelper.isCurrentFlowAnyImplicitFlow(config)) {\n      if (currentCallbackUrl?.includes('#')) {\n        const hash = currentCallbackUrl.substring(currentCallbackUrl.indexOf('#') + 1);\n        callback$ = this.implicitFlowCallbackService.authenticatedImplicitFlowCallback(config, allConfigs, hash);\n      } else {\n        callback$ = this.implicitFlowCallbackService.authenticatedImplicitFlowCallback(config, allConfigs);\n      }\n    }\n    return callback$.pipe(tap(() => this.stsCallbackInternal$.next()));\n  }\n  static {\n    this.ɵfac = function CallbackService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CallbackService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CallbackService,\n      factory: CallbackService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CallbackService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass PlatformProvider {\n  constructor() {\n    this.platformId = inject(PLATFORM_ID);\n  }\n  isBrowser() {\n    return isPlatformBrowser(this.platformId);\n  }\n  static {\n    this.ɵfac = function PlatformProvider_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlatformProvider)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PlatformProvider,\n      factory: PlatformProvider.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformProvider, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst WELL_KNOWN_SUFFIX = `/.well-known/openid-configuration`;\nclass AuthWellKnownDataService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.http = inject(DataService);\n  }\n  getWellKnownEndPointsForConfig(config) {\n    const {\n      authWellknownEndpointUrl\n    } = config;\n    if (!authWellknownEndpointUrl) {\n      const errorMessage = 'no authWellknownEndpoint given!';\n      this.loggerService.logError(config, errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }\n    return this.getWellKnownDocument(authWellknownEndpointUrl, config).pipe(map(wellKnownEndpoints => ({\n      issuer: wellKnownEndpoints.issuer,\n      jwksUri: wellKnownEndpoints.jwks_uri,\n      authorizationEndpoint: wellKnownEndpoints.authorization_endpoint,\n      tokenEndpoint: wellKnownEndpoints.token_endpoint,\n      userInfoEndpoint: wellKnownEndpoints.userinfo_endpoint,\n      endSessionEndpoint: wellKnownEndpoints.end_session_endpoint,\n      checkSessionIframe: wellKnownEndpoints.check_session_iframe,\n      revocationEndpoint: wellKnownEndpoints.revocation_endpoint,\n      introspectionEndpoint: wellKnownEndpoints.introspection_endpoint,\n      parEndpoint: wellKnownEndpoints.pushed_authorization_request_endpoint\n    })));\n  }\n  getWellKnownDocument(wellKnownEndpoint, config) {\n    let url = wellKnownEndpoint;\n    const wellKnownSuffix = config.authWellknownUrlSuffix || WELL_KNOWN_SUFFIX;\n    if (!wellKnownEndpoint.includes(wellKnownSuffix)) {\n      url = `${wellKnownEndpoint}${wellKnownSuffix}`;\n    }\n    return this.http.get(url, config).pipe(retry(2));\n  }\n  static {\n    this.ɵfac = function AuthWellKnownDataService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthWellKnownDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthWellKnownDataService,\n      factory: AuthWellKnownDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthWellKnownDataService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass AuthWellKnownService {\n  constructor() {\n    this.dataService = inject(AuthWellKnownDataService);\n    this.publicEventsService = inject(PublicEventsService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n  }\n  storeWellKnownEndpoints(config, mappedWellKnownEndpoints) {\n    this.storagePersistenceService.write('authWellKnownEndPoints', mappedWellKnownEndpoints, config);\n  }\n  queryAndStoreAuthWellKnownEndPoints(config) {\n    if (!config) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    return this.dataService.getWellKnownEndPointsForConfig(config).pipe(tap(mappedWellKnownEndpoints => this.storeWellKnownEndpoints(config, mappedWellKnownEndpoints)), catchError(error => {\n      this.publicEventsService.fireEvent(EventTypes.ConfigLoadingFailed, null);\n      return throwError(() => new Error(error));\n    }));\n  }\n  static {\n    this.ɵfac = function AuthWellKnownService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthWellKnownService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthWellKnownService,\n      factory: AuthWellKnownService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthWellKnownService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst DEFAULT_CONFIG = {\n  authority: 'https://please_set',\n  authWellknownEndpointUrl: '',\n  authWellknownEndpoints: undefined,\n  redirectUrl: 'https://please_set',\n  clientId: 'please_set',\n  responseType: 'code',\n  scope: 'openid email profile',\n  hdParam: '',\n  postLogoutRedirectUri: 'https://please_set',\n  startCheckSession: false,\n  silentRenew: false,\n  silentRenewUrl: 'https://please_set',\n  silentRenewTimeoutInSeconds: 20,\n  renewTimeBeforeTokenExpiresInSeconds: 0,\n  useRefreshToken: false,\n  usePushedAuthorisationRequests: false,\n  ignoreNonceAfterRefresh: false,\n  postLoginRoute: '/',\n  forbiddenRoute: '/forbidden',\n  unauthorizedRoute: '/unauthorized',\n  autoUserInfo: true,\n  autoCleanStateAfterAuthentication: true,\n  triggerAuthorizationResultEvent: false,\n  logLevel: LogLevel.Warn,\n  issValidationOff: false,\n  historyCleanupOff: false,\n  maxIdTokenIatOffsetAllowedInSeconds: 120,\n  disableIatOffsetValidation: false,\n  customParamsAuthRequest: {},\n  customParamsRefreshTokenRequest: {},\n  customParamsEndSessionRequest: {},\n  customParamsCodeRequest: {},\n  disableRefreshIdTokenAuthTimeValidation: false,\n  triggerRefreshWhenIdTokenExpired: true,\n  tokenRefreshInSeconds: 4,\n  refreshTokenRetryInSeconds: 3,\n  ngswBypass: false\n};\nconst POSITIVE_VALIDATION_RESULT = {\n  result: true,\n  messages: [],\n  level: 'none'\n};\nconst ensureAuthority = passedConfig => {\n  if (!passedConfig.authority) {\n    return {\n      result: false,\n      messages: ['The authority URL MUST be provided in the configuration! '],\n      level: 'error'\n    };\n  }\n  return POSITIVE_VALIDATION_RESULT;\n};\nconst ensureClientId = passedConfig => {\n  if (!passedConfig.clientId) {\n    return {\n      result: false,\n      messages: ['The clientId is required and missing from your config!'],\n      level: 'error'\n    };\n  }\n  return POSITIVE_VALIDATION_RESULT;\n};\nconst createIdentifierToCheck = passedConfig => {\n  if (!passedConfig) {\n    return '';\n  }\n  const {\n    authority,\n    clientId,\n    scope\n  } = passedConfig;\n  return `${authority}${clientId}${scope}`;\n};\nconst arrayHasDuplicates = array => new Set(array).size !== array.length;\nconst ensureNoDuplicatedConfigsRule = passedConfigs => {\n  const allIdentifiers = passedConfigs.map(x => createIdentifierToCheck(x));\n  const someAreNotSet = allIdentifiers.some(x => x === '');\n  if (someAreNotSet) {\n    return {\n      result: false,\n      messages: [`Please make sure you add an object with a 'config' property: ....({ config }) instead of ...(config)`],\n      level: 'error'\n    };\n  }\n  const hasDuplicates = arrayHasDuplicates(allIdentifiers);\n  if (hasDuplicates) {\n    return {\n      result: false,\n      messages: ['You added multiple configs with the same authority, clientId and scope'],\n      level: 'warning'\n    };\n  }\n  return POSITIVE_VALIDATION_RESULT;\n};\nconst ensureRedirectRule = passedConfig => {\n  if (!passedConfig.redirectUrl) {\n    return {\n      result: false,\n      messages: ['The redirectUrl is required and missing from your config'],\n      level: 'error'\n    };\n  }\n  return POSITIVE_VALIDATION_RESULT;\n};\nconst ensureSilentRenewUrlWhenNoRefreshTokenUsed = passedConfig => {\n  const usesSilentRenew = passedConfig.silentRenew;\n  const usesRefreshToken = passedConfig.useRefreshToken;\n  const hasSilentRenewUrl = passedConfig.silentRenewUrl;\n  if (usesSilentRenew && !usesRefreshToken && !hasSilentRenewUrl) {\n    return {\n      result: false,\n      messages: ['Please provide a silent renew URL if using renew and not refresh tokens'],\n      level: 'error'\n    };\n  }\n  return POSITIVE_VALIDATION_RESULT;\n};\nconst useOfflineScopeWithSilentRenew = passedConfig => {\n  const hasRefreshToken = passedConfig.useRefreshToken;\n  const hasSilentRenew = passedConfig.silentRenew;\n  const scope = passedConfig.scope || '';\n  const hasOfflineScope = scope.split(' ').includes('offline_access');\n  if (hasRefreshToken && hasSilentRenew && !hasOfflineScope) {\n    return {\n      result: false,\n      messages: ['When using silent renew and refresh tokens please set the `offline_access` scope'],\n      level: 'warning'\n    };\n  }\n  return POSITIVE_VALIDATION_RESULT;\n};\nconst allRules = [ensureAuthority, useOfflineScopeWithSilentRenew, ensureRedirectRule, ensureClientId, ensureSilentRenewUrlWhenNoRefreshTokenUsed];\nconst allMultipleConfigRules = [ensureNoDuplicatedConfigsRule];\nclass ConfigValidationService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n  }\n  validateConfigs(passedConfigs) {\n    return this.validateConfigsInternal(passedConfigs ?? [], allMultipleConfigRules);\n  }\n  validateConfig(passedConfig) {\n    return this.validateConfigInternal(passedConfig, allRules);\n  }\n  validateConfigsInternal(passedConfigs, allRulesToUse) {\n    if (passedConfigs.length === 0) {\n      return false;\n    }\n    const allValidationResults = allRulesToUse.map(rule => rule(passedConfigs));\n    let overallErrorCount = 0;\n    passedConfigs.forEach(passedConfig => {\n      const errorCount = this.processValidationResultsAndGetErrorCount(allValidationResults, passedConfig);\n      overallErrorCount += errorCount;\n    });\n    return overallErrorCount === 0;\n  }\n  validateConfigInternal(passedConfig, allRulesToUse) {\n    const allValidationResults = allRulesToUse.map(rule => rule(passedConfig));\n    const errorCount = this.processValidationResultsAndGetErrorCount(allValidationResults, passedConfig);\n    return errorCount === 0;\n  }\n  processValidationResultsAndGetErrorCount(allValidationResults, config) {\n    const allMessages = allValidationResults.filter(x => x.messages.length > 0);\n    const allErrorMessages = this.getAllMessagesOfType('error', allMessages);\n    const allWarnings = this.getAllMessagesOfType('warning', allMessages);\n    allErrorMessages.forEach(message => this.loggerService.logError(config, message));\n    allWarnings.forEach(message => this.loggerService.logWarning(config, message));\n    return allErrorMessages.length;\n  }\n  getAllMessagesOfType(type, results) {\n    const allMessages = results.filter(x => x.level === type).map(result => result.messages);\n    return allMessages.reduce((acc, val) => acc.concat(val), []);\n  }\n  static {\n    this.ɵfac = function ConfigValidationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfigValidationService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConfigValidationService,\n      factory: ConfigValidationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigValidationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ConfigurationService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.publicEventsService = inject(PublicEventsService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.platformProvider = inject(PlatformProvider);\n    this.authWellKnownService = inject(AuthWellKnownService);\n    this.loader = inject(StsConfigLoader);\n    this.configValidationService = inject(ConfigValidationService);\n    this.configsInternal = {};\n  }\n  hasManyConfigs() {\n    return Object.keys(this.configsInternal).length > 1;\n  }\n  getAllConfigurations() {\n    return Object.values(this.configsInternal);\n  }\n  getOpenIDConfiguration(configId) {\n    if (this.configsAlreadySaved()) {\n      return of(this.getConfig(configId));\n    }\n    return this.getOpenIDConfigurations(configId).pipe(map(result => result.currentConfig));\n  }\n  getOpenIDConfigurations(configId) {\n    return this.loadConfigs().pipe(concatMap(allConfigs => this.prepareAndSaveConfigs(allConfigs)), map(allPreparedConfigs => ({\n      allConfigs: allPreparedConfigs,\n      currentConfig: this.getConfig(configId)\n    })));\n  }\n  hasAtLeastOneConfig() {\n    return Object.keys(this.configsInternal).length > 0;\n  }\n  saveConfig(readyConfig) {\n    const {\n      configId\n    } = readyConfig;\n    this.configsInternal[configId] = readyConfig;\n  }\n  loadConfigs() {\n    return this.loader.loadConfigs();\n  }\n  configsAlreadySaved() {\n    return this.hasAtLeastOneConfig();\n  }\n  getConfig(configId) {\n    if (Boolean(configId)) {\n      return this.configsInternal[configId] || null;\n    }\n    const [, value] = Object.entries(this.configsInternal)[0] || [[null, null]];\n    return value || null;\n  }\n  prepareAndSaveConfigs(passedConfigs) {\n    if (!this.configValidationService.validateConfigs(passedConfigs)) {\n      return of([]);\n    }\n    this.createUniqueIds(passedConfigs);\n    const allHandleConfigs$ = passedConfigs.map(x => this.handleConfig(x));\n    const as = forkJoin(allHandleConfigs$).pipe(map(config => config.filter(conf => Boolean(conf))), map(c => c));\n    return as;\n  }\n  createUniqueIds(passedConfigs) {\n    passedConfigs.forEach((config, index) => {\n      if (!config.configId) {\n        config.configId = `${index}-${config.clientId}`;\n      }\n    });\n  }\n  handleConfig(passedConfig) {\n    if (!this.configValidationService.validateConfig(passedConfig)) {\n      this.loggerService.logError(passedConfig, 'Validation of config rejected with errors. Config is NOT set.');\n      return of(null);\n    }\n    if (!passedConfig.authWellknownEndpointUrl) {\n      passedConfig.authWellknownEndpointUrl = passedConfig.authority;\n    }\n    const usedConfig = this.prepareConfig(passedConfig);\n    this.saveConfig(usedConfig);\n    const configWithAuthWellKnown = this.enhanceConfigWithWellKnownEndpoint(usedConfig);\n    this.publicEventsService.fireEvent(EventTypes.ConfigLoaded, configWithAuthWellKnown);\n    return of(usedConfig);\n  }\n  enhanceConfigWithWellKnownEndpoint(configuration) {\n    const alreadyExistingAuthWellKnownEndpoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    if (!!alreadyExistingAuthWellKnownEndpoints) {\n      configuration.authWellknownEndpoints = alreadyExistingAuthWellKnownEndpoints;\n      return configuration;\n    }\n    const passedAuthWellKnownEndpoints = configuration.authWellknownEndpoints;\n    if (!!passedAuthWellKnownEndpoints) {\n      this.authWellKnownService.storeWellKnownEndpoints(configuration, passedAuthWellKnownEndpoints);\n      configuration.authWellknownEndpoints = passedAuthWellKnownEndpoints;\n      return configuration;\n    }\n    return configuration;\n  }\n  prepareConfig(configuration) {\n    const openIdConfigurationInternal = {\n      ...DEFAULT_CONFIG,\n      ...configuration\n    };\n    this.setSpecialCases(openIdConfigurationInternal);\n    return openIdConfigurationInternal;\n  }\n  setSpecialCases(currentConfig) {\n    if (!this.platformProvider.isBrowser()) {\n      currentConfig.startCheckSession = false;\n      currentConfig.silentRenew = false;\n      currentConfig.useRefreshToken = false;\n      currentConfig.usePushedAuthorisationRequests = false;\n    }\n  }\n  static {\n    this.ɵfac = function ConfigurationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfigurationService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConfigurationService,\n      factory: ConfigurationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass IFrameService {\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.loggerService = inject(LoggerService);\n  }\n  getExistingIFrame(identifier) {\n    const iFrameOnParent = this.getIFrameFromParentWindow(identifier);\n    if (this.isIFrameElement(iFrameOnParent)) {\n      return iFrameOnParent;\n    }\n    const iFrameOnSelf = this.getIFrameFromWindow(identifier);\n    if (this.isIFrameElement(iFrameOnSelf)) {\n      return iFrameOnSelf;\n    }\n    return null;\n  }\n  addIFrameToWindowBody(identifier, config) {\n    const sessionIframe = this.document.createElement('iframe');\n    sessionIframe.id = identifier;\n    sessionIframe.title = identifier;\n    this.loggerService.logDebug(config, sessionIframe);\n    sessionIframe.style.display = 'none';\n    this.document.body.appendChild(sessionIframe);\n    return sessionIframe;\n  }\n  getIFrameFromParentWindow(identifier) {\n    try {\n      const iFrameElement = this.document.defaultView?.parent.document.getElementById(identifier);\n      if (this.isIFrameElement(iFrameElement)) {\n        return iFrameElement;\n      }\n      return null;\n    } catch (e) {\n      return null;\n    }\n  }\n  getIFrameFromWindow(identifier) {\n    const iFrameElement = this.document.getElementById(identifier);\n    if (this.isIFrameElement(iFrameElement)) {\n      return iFrameElement;\n    }\n    return null;\n  }\n  isIFrameElement(element) {\n    return !!element && element instanceof HTMLIFrameElement;\n  }\n  static {\n    this.ɵfac = function IFrameService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IFrameService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: IFrameService,\n      factory: IFrameService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IFrameService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst IFRAME_FOR_SILENT_RENEW_IDENTIFIER = 'myiFrameForSilentRenew';\nclass SilentRenewService {\n  constructor() {\n    this.refreshSessionWithIFrameCompletedInternal$ = new Subject();\n    this.loggerService = inject(LoggerService);\n    this.iFrameService = inject(IFrameService);\n    this.flowsService = inject(FlowsService);\n    this.resetAuthDataService = inject(ResetAuthDataService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.authStateService = inject(AuthStateService);\n    this.flowHelper = inject(FlowHelper);\n    this.implicitFlowCallbackService = inject(ImplicitFlowCallbackService);\n    this.intervalService = inject(IntervalService);\n  }\n  get refreshSessionWithIFrameCompleted$() {\n    return this.refreshSessionWithIFrameCompletedInternal$.asObservable();\n  }\n  getOrCreateIframe(config) {\n    const existingIframe = this.getExistingIframe();\n    if (!existingIframe) {\n      return this.iFrameService.addIFrameToWindowBody(IFRAME_FOR_SILENT_RENEW_IDENTIFIER, config);\n    }\n    return existingIframe;\n  }\n  isSilentRenewConfigured(configuration) {\n    const {\n      useRefreshToken,\n      silentRenew\n    } = configuration;\n    return !useRefreshToken && Boolean(silentRenew);\n  }\n  codeFlowCallbackSilentRenewIframe(urlParts, config, allConfigs) {\n    const params = new HttpParams({\n      fromString: urlParts[1]\n    });\n    const errorParam = params.get('error');\n    if (errorParam) {\n      this.authStateService.updateAndPublishAuthState({\n        isAuthenticated: false,\n        validationResult: ValidationResult.LoginRequired,\n        isRenewProcess: true\n      });\n      this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n      this.flowsDataService.setNonce('', config);\n      this.intervalService.stopPeriodicTokenCheck();\n      return throwError(() => new Error(errorParam));\n    }\n    const code = params.get('code') ?? '';\n    const state = params.get('state') ?? '';\n    const sessionState = params.get('session_state');\n    const callbackContext = {\n      code,\n      refreshToken: '',\n      state,\n      sessionState,\n      authResult: null,\n      isRenewProcess: true,\n      jwtKeys: null,\n      validationResult: null,\n      existingIdToken: null\n    };\n    return this.flowsService.processSilentRenewCodeFlowCallback(callbackContext, config, allConfigs).pipe(catchError(error => {\n      this.intervalService.stopPeriodicTokenCheck();\n      this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n      return throwError(() => new Error(error));\n    }));\n  }\n  silentRenewEventHandler(e, config, allConfigs) {\n    this.loggerService.logDebug(config, 'silentRenewEventHandler');\n    if (!e.detail) {\n      return;\n    }\n    let callback$;\n    const isCodeFlow = this.flowHelper.isCurrentFlowCodeFlow(config);\n    if (isCodeFlow) {\n      const urlParts = e.detail.toString().split('?');\n      callback$ = this.codeFlowCallbackSilentRenewIframe(urlParts, config, allConfigs);\n    } else {\n      callback$ = this.implicitFlowCallbackService.authenticatedImplicitFlowCallback(config, allConfigs, e.detail);\n    }\n    callback$.subscribe({\n      next: callbackContext => {\n        this.refreshSessionWithIFrameCompletedInternal$.next(callbackContext);\n        this.flowsDataService.resetSilentRenewRunning(config);\n      },\n      error: err => {\n        this.loggerService.logError(config, 'Error: ' + err);\n        this.refreshSessionWithIFrameCompletedInternal$.next(null);\n        this.flowsDataService.resetSilentRenewRunning(config);\n      }\n    });\n  }\n  getExistingIframe() {\n    return this.iFrameService.getExistingIFrame(IFRAME_FOR_SILENT_RENEW_IDENTIFIER);\n  }\n  static {\n    this.ɵfac = function SilentRenewService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SilentRenewService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SilentRenewService,\n      factory: SilentRenewService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SilentRenewService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RefreshSessionIframeService {\n  constructor() {\n    this.renderer = inject(RendererFactory2).createRenderer(null, null);\n    this.loggerService = inject(LoggerService);\n    this.urlService = inject(UrlService);\n    this.silentRenewService = inject(SilentRenewService);\n    this.document = inject(DOCUMENT);\n  }\n  refreshSessionWithIframe(config, allConfigs, customParams) {\n    this.loggerService.logDebug(config, 'BEGIN refresh session Authorize Iframe renew');\n    return this.urlService.getRefreshSessionSilentRenewUrl(config, customParams).pipe(switchMap(url => {\n      return this.sendAuthorizeRequestUsingSilentRenew(url, config, allConfigs);\n    }));\n  }\n  sendAuthorizeRequestUsingSilentRenew(url, config, allConfigs) {\n    const sessionIframe = this.silentRenewService.getOrCreateIframe(config);\n    this.initSilentRenewRequest(config, allConfigs);\n    this.loggerService.logDebug(config, `sendAuthorizeRequestUsingSilentRenew for URL: ${url}`);\n    return new Observable(observer => {\n      const onLoadHandler = () => {\n        sessionIframe.removeEventListener('load', onLoadHandler);\n        this.loggerService.logDebug(config, 'removed event listener from IFrame');\n        observer.next(true);\n        observer.complete();\n      };\n      sessionIframe.addEventListener('load', onLoadHandler);\n      sessionIframe.contentWindow?.location.replace(url ?? '');\n    });\n  }\n  initSilentRenewRequest(config, allConfigs) {\n    const instanceId = Math.random();\n    const initDestroyHandler = this.renderer.listen('window', 'oidc-silent-renew-init', e => {\n      if (e.detail !== instanceId) {\n        initDestroyHandler();\n        renewDestroyHandler();\n      }\n    });\n    const renewDestroyHandler = this.renderer.listen('window', 'oidc-silent-renew-message', e => this.silentRenewService.silentRenewEventHandler(e, config, allConfigs));\n    this.document.defaultView?.dispatchEvent(new CustomEvent('oidc-silent-renew-init', {\n      detail: instanceId\n    }));\n  }\n  static {\n    this.ɵfac = function RefreshSessionIframeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RefreshSessionIframeService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RefreshSessionIframeService,\n      factory: RefreshSessionIframeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RefreshSessionIframeService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RefreshSessionRefreshTokenService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.resetAuthDataService = inject(ResetAuthDataService);\n    this.flowsService = inject(FlowsService);\n    this.intervalService = inject(IntervalService);\n  }\n  refreshSessionWithRefreshTokens(config, allConfigs, customParamsRefresh) {\n    this.loggerService.logDebug(config, 'BEGIN refresh session Authorize');\n    let refreshTokenFailed = false;\n    return this.flowsService.processRefreshToken(config, allConfigs, customParamsRefresh).pipe(catchError(error => {\n      this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n      refreshTokenFailed = true;\n      return throwError(() => new Error(error));\n    }), finalize(() => refreshTokenFailed && this.intervalService.stopPeriodicTokenCheck()));\n  }\n  static {\n    this.ɵfac = function RefreshSessionRefreshTokenService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RefreshSessionRefreshTokenService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RefreshSessionRefreshTokenService,\n      factory: RefreshSessionRefreshTokenService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RefreshSessionRefreshTokenService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass PeriodicallyTokenCheckService {\n  constructor() {\n    this.resetAuthDataService = inject(ResetAuthDataService);\n    this.flowHelper = inject(FlowHelper);\n    this.flowsDataService = inject(FlowsDataService);\n    this.loggerService = inject(LoggerService);\n    this.userService = inject(UserService);\n    this.authStateService = inject(AuthStateService);\n    this.refreshSessionIframeService = inject(RefreshSessionIframeService);\n    this.refreshSessionRefreshTokenService = inject(RefreshSessionRefreshTokenService);\n    this.intervalService = inject(IntervalService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.publicEventsService = inject(PublicEventsService);\n    this.configurationService = inject(ConfigurationService);\n  }\n  startTokenValidationPeriodically(allConfigs, currentConfig) {\n    const configsWithSilentRenewEnabled = this.getConfigsWithSilentRenewEnabled(allConfigs);\n    if (configsWithSilentRenewEnabled.length <= 0) {\n      return;\n    }\n    if (this.intervalService.isTokenValidationRunning()) {\n      return;\n    }\n    const refreshTimeInSeconds = this.getSmallestRefreshTimeFromConfigs(configsWithSilentRenewEnabled);\n    const periodicallyCheck$ = this.intervalService.startPeriodicTokenCheck(refreshTimeInSeconds).pipe(switchMap(() => {\n      const objectWithConfigIdsAndRefreshEvent = {};\n      configsWithSilentRenewEnabled.forEach(config => {\n        const identifier = config.configId;\n        const refreshEvent = this.getRefreshEvent(config, allConfigs);\n        objectWithConfigIdsAndRefreshEvent[identifier] = refreshEvent;\n      });\n      return forkJoin(objectWithConfigIdsAndRefreshEvent);\n    }));\n    this.intervalService.runTokenValidationRunning = periodicallyCheck$.pipe(catchError(error => throwError(() => new Error(error)))).subscribe({\n      next: objectWithConfigIds => {\n        for (const [configId, _] of Object.entries(objectWithConfigIds)) {\n          this.configurationService.getOpenIDConfiguration(configId).subscribe(config => {\n            this.loggerService.logDebug(config, 'silent renew, periodic check finished!');\n            if (this.flowHelper.isCurrentFlowCodeFlowWithRefreshTokens(config)) {\n              this.flowsDataService.resetSilentRenewRunning(config);\n            }\n          });\n        }\n      },\n      error: error => {\n        this.loggerService.logError(currentConfig, 'silent renew failed!', error);\n      }\n    });\n  }\n  getRefreshEvent(config, allConfigs) {\n    const shouldStartRefreshEvent = this.shouldStartPeriodicallyCheckForConfig(config);\n    if (!shouldStartRefreshEvent) {\n      return of(null);\n    }\n    const refreshEvent$ = this.createRefreshEventForConfig(config, allConfigs);\n    this.publicEventsService.fireEvent(EventTypes.SilentRenewStarted);\n    return refreshEvent$.pipe(catchError(error => {\n      this.loggerService.logError(config, 'silent renew failed!', error);\n      this.publicEventsService.fireEvent(EventTypes.SilentRenewFailed, error);\n      this.flowsDataService.resetSilentRenewRunning(config);\n      return throwError(() => new Error(error));\n    }));\n  }\n  getSmallestRefreshTimeFromConfigs(configsWithSilentRenewEnabled) {\n    const result = configsWithSilentRenewEnabled.reduce((prev, curr) => (prev.tokenRefreshInSeconds ?? 0) < (curr.tokenRefreshInSeconds ?? 0) ? prev : curr);\n    return result.tokenRefreshInSeconds ?? 0;\n  }\n  getConfigsWithSilentRenewEnabled(allConfigs) {\n    return allConfigs.filter(x => x.silentRenew);\n  }\n  createRefreshEventForConfig(configuration, allConfigs) {\n    this.loggerService.logDebug(configuration, 'starting silent renew...');\n    return this.configurationService.getOpenIDConfiguration(configuration.configId).pipe(switchMap(config => {\n      if (!config?.silentRenew) {\n        this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n        return of(null);\n      }\n      this.flowsDataService.setSilentRenewRunning(config);\n      if (this.flowHelper.isCurrentFlowCodeFlowWithRefreshTokens(config)) {\n        // Retrieve Dynamically Set Custom Params for refresh body\n        const customParamsRefresh = this.storagePersistenceService.read('storageCustomParamsRefresh', config) || {};\n        const {\n          customParamsRefreshTokenRequest\n        } = config;\n        const mergedParams = {\n          ...customParamsRefreshTokenRequest,\n          ...customParamsRefresh\n        };\n        // Refresh Session using Refresh tokens\n        return this.refreshSessionRefreshTokenService.refreshSessionWithRefreshTokens(config, allConfigs, mergedParams);\n      }\n      // Retrieve Dynamically Set Custom Params\n      const customParams = this.storagePersistenceService.read('storageCustomParamsAuthRequest', config);\n      return this.refreshSessionIframeService.refreshSessionWithIframe(config, allConfigs, customParams);\n    }));\n  }\n  shouldStartPeriodicallyCheckForConfig(config) {\n    const idToken = this.authStateService.getIdToken(config);\n    const isSilentRenewRunning = this.flowsDataService.isSilentRenewRunning(config);\n    const isCodeFlowInProgress = this.flowsDataService.isCodeFlowInProgress(config);\n    const userDataFromStore = this.userService.getUserDataFromStore(config);\n    this.loggerService.logDebug(config, `Checking: silentRenewRunning: ${isSilentRenewRunning}, isCodeFlowInProgress: ${isCodeFlowInProgress} - has idToken: ${!!idToken} - has userData: ${!!userDataFromStore}`);\n    const shouldBeExecuted = !!userDataFromStore && !isSilentRenewRunning && !!idToken && !isCodeFlowInProgress;\n    if (!shouldBeExecuted) {\n      return false;\n    }\n    const idTokenExpired = this.authStateService.hasIdTokenExpiredAndRenewCheckIsEnabled(config);\n    const accessTokenExpired = this.authStateService.hasAccessTokenExpiredIfExpiryExists(config);\n    return idTokenExpired || accessTokenExpired;\n  }\n  static {\n    this.ɵfac = function PeriodicallyTokenCheckService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PeriodicallyTokenCheckService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PeriodicallyTokenCheckService,\n      factory: PeriodicallyTokenCheckService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PeriodicallyTokenCheckService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst MAX_RETRY_ATTEMPTS = 3;\nclass RefreshSessionService {\n  constructor() {\n    this.flowHelper = inject(FlowHelper);\n    this.flowsDataService = inject(FlowsDataService);\n    this.loggerService = inject(LoggerService);\n    this.silentRenewService = inject(SilentRenewService);\n    this.authStateService = inject(AuthStateService);\n    this.authWellKnownService = inject(AuthWellKnownService);\n    this.refreshSessionIframeService = inject(RefreshSessionIframeService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.refreshSessionRefreshTokenService = inject(RefreshSessionRefreshTokenService);\n    this.userService = inject(UserService);\n  }\n  userForceRefreshSession(config, allConfigs, extraCustomParams) {\n    if (!config) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    this.persistCustomParams(extraCustomParams, config);\n    return this.forceRefreshSession(config, allConfigs, extraCustomParams).pipe(tap(() => this.flowsDataService.resetSilentRenewRunning(config)));\n  }\n  forceRefreshSession(config, allConfigs, extraCustomParams) {\n    const {\n      customParamsRefreshTokenRequest,\n      configId\n    } = config;\n    const mergedParams = {\n      ...customParamsRefreshTokenRequest,\n      ...extraCustomParams\n    };\n    if (this.flowHelper.isCurrentFlowCodeFlowWithRefreshTokens(config)) {\n      return this.startRefreshSession(config, allConfigs, mergedParams).pipe(map(() => {\n        const isAuthenticated = this.authStateService.areAuthStorageTokensValid(config);\n        if (isAuthenticated) {\n          return {\n            idToken: this.authStateService.getIdToken(config),\n            accessToken: this.authStateService.getAccessToken(config),\n            userData: this.userService.getUserDataFromStore(config),\n            isAuthenticated,\n            configId\n          };\n        }\n        return {\n          isAuthenticated: false,\n          errorMessage: '',\n          userData: null,\n          idToken: '',\n          accessToken: '',\n          configId\n        };\n      }));\n    }\n    const {\n      silentRenewTimeoutInSeconds\n    } = config;\n    const timeOutTime = (silentRenewTimeoutInSeconds ?? 0) * 1000;\n    return forkJoin([this.startRefreshSession(config, allConfigs, extraCustomParams), this.silentRenewService.refreshSessionWithIFrameCompleted$.pipe(take(1))]).pipe(timeout(timeOutTime), retryWhen(errors => {\n      return errors.pipe(mergeMap((error, index) => {\n        const scalingDuration = 1000;\n        const currentAttempt = index + 1;\n        if (!(error instanceof TimeoutError) || currentAttempt > MAX_RETRY_ATTEMPTS) {\n          return throwError(() => new Error(error));\n        }\n        this.loggerService.logDebug(config, `forceRefreshSession timeout. Attempt #${currentAttempt}`);\n        this.flowsDataService.resetSilentRenewRunning(config);\n        return timer(currentAttempt * scalingDuration);\n      }));\n    }), map(([_, callbackContext]) => {\n      const isAuthenticated = this.authStateService.areAuthStorageTokensValid(config);\n      if (isAuthenticated) {\n        return {\n          idToken: callbackContext?.authResult?.id_token ?? '',\n          accessToken: callbackContext?.authResult?.access_token ?? '',\n          userData: this.userService.getUserDataFromStore(config),\n          isAuthenticated,\n          configId\n        };\n      }\n      return {\n        isAuthenticated: false,\n        errorMessage: '',\n        userData: null,\n        idToken: '',\n        accessToken: '',\n        configId\n      };\n    }));\n  }\n  persistCustomParams(extraCustomParams, config) {\n    const {\n      useRefreshToken\n    } = config;\n    if (extraCustomParams) {\n      if (useRefreshToken) {\n        this.storagePersistenceService.write('storageCustomParamsRefresh', extraCustomParams, config);\n      } else {\n        this.storagePersistenceService.write('storageCustomParamsAuthRequest', extraCustomParams, config);\n      }\n    }\n  }\n  startRefreshSession(config, allConfigs, extraCustomParams) {\n    const isSilentRenewRunning = this.flowsDataService.isSilentRenewRunning(config);\n    this.loggerService.logDebug(config, `Checking: silentRenewRunning: ${isSilentRenewRunning}`);\n    const shouldBeExecuted = !isSilentRenewRunning;\n    if (!shouldBeExecuted) {\n      return of(null);\n    }\n    return this.authWellKnownService.queryAndStoreAuthWellKnownEndPoints(config).pipe(switchMap(() => {\n      this.flowsDataService.setSilentRenewRunning(config);\n      if (this.flowHelper.isCurrentFlowCodeFlowWithRefreshTokens(config)) {\n        // Refresh Session using Refresh tokens\n        return this.refreshSessionRefreshTokenService.refreshSessionWithRefreshTokens(config, allConfigs, extraCustomParams);\n      }\n      return this.refreshSessionIframeService.refreshSessionWithIframe(config, allConfigs, extraCustomParams);\n    }));\n  }\n  static {\n    this.ɵfac = function RefreshSessionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RefreshSessionService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RefreshSessionService,\n      factory: RefreshSessionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RefreshSessionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst IFRAME_FOR_CHECK_SESSION_IDENTIFIER = 'myiFrameForCheckSession';\n// http://openid.net/specs/openid-connect-session-1_0-ID4.html\nclass CheckSessionService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.iFrameService = inject(IFrameService);\n    this.eventService = inject(PublicEventsService);\n    this.zone = inject(NgZone);\n    this.document = inject(DOCUMENT);\n    this.checkSessionReceived = false;\n    this.scheduledHeartBeatRunning = null;\n    this.lastIFrameRefresh = 0;\n    this.outstandingMessages = 0;\n    this.heartBeatInterval = 3000;\n    this.iframeRefreshInterval = 60000;\n    this.checkSessionChangedInternal$ = new BehaviorSubject(false);\n  }\n  get checkSessionChanged$() {\n    return this.checkSessionChangedInternal$.asObservable();\n  }\n  ngOnDestroy() {\n    this.stop();\n    const windowAsDefaultView = this.document.defaultView;\n    if (windowAsDefaultView && this.iframeMessageEventListener) {\n      windowAsDefaultView.removeEventListener('message', this.iframeMessageEventListener, false);\n    }\n  }\n  isCheckSessionConfigured(configuration) {\n    const {\n      startCheckSession\n    } = configuration;\n    return Boolean(startCheckSession);\n  }\n  start(configuration) {\n    if (!!this.scheduledHeartBeatRunning) {\n      return;\n    }\n    const {\n      clientId\n    } = configuration;\n    this.pollServerSession(clientId, configuration);\n  }\n  stop() {\n    if (!this.scheduledHeartBeatRunning) {\n      return;\n    }\n    this.clearScheduledHeartBeat();\n    this.checkSessionReceived = false;\n  }\n  serverStateChanged(configuration) {\n    const {\n      startCheckSession\n    } = configuration;\n    return Boolean(startCheckSession) && this.checkSessionReceived;\n  }\n  getExistingIframe() {\n    return this.iFrameService.getExistingIFrame(IFRAME_FOR_CHECK_SESSION_IDENTIFIER);\n  }\n  init(configuration) {\n    if (this.lastIFrameRefresh + this.iframeRefreshInterval > Date.now()) {\n      return of(undefined);\n    }\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    if (!authWellKnownEndPoints) {\n      this.loggerService.logWarning(configuration, 'CheckSession - init check session: authWellKnownEndpoints is undefined. Returning.');\n      return of();\n    }\n    const existingIframe = this.getOrCreateIframe(configuration);\n    // https://www.w3.org/TR/2000/REC-DOM-Level-2-Events-20001113/events.html#Events-EventTarget-addEventListener\n    // If multiple identical EventListeners are registered on the same EventTarget with the same parameters the duplicate instances are discarded. They do not cause the EventListener to be called twice and since they are discarded they do not need to be removed with the removeEventListener method.\n    // this is done even if iframe exists for HMR to work, since iframe exists on service init\n    this.bindMessageEventToIframe(configuration);\n    const checkSessionIframe = authWellKnownEndPoints.checkSessionIframe;\n    const contentWindow = existingIframe.contentWindow;\n    if (!checkSessionIframe) {\n      this.loggerService.logWarning(configuration, 'CheckSession - init check session: checkSessionIframe is not configured to run');\n    }\n    if (!contentWindow) {\n      this.loggerService.logWarning(configuration, 'CheckSession - init check session: IFrame contentWindow does not exist');\n    } else {\n      contentWindow.location.replace(checkSessionIframe);\n    }\n    return new Observable(observer => {\n      existingIframe.onload = () => {\n        this.lastIFrameRefresh = Date.now();\n        observer.next();\n        observer.complete();\n      };\n    });\n  }\n  pollServerSession(clientId, configuration) {\n    this.outstandingMessages = 0;\n    const pollServerSessionRecur = () => {\n      this.init(configuration).pipe(take(1)).subscribe(() => {\n        const existingIframe = this.getExistingIframe();\n        if (existingIframe && clientId) {\n          this.loggerService.logDebug(configuration, `CheckSession - clientId : '${clientId}' - existingIframe: '${existingIframe}'`);\n          const sessionState = this.storagePersistenceService.read('session_state', configuration);\n          const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n          const contentWindow = existingIframe.contentWindow;\n          if (sessionState && authWellKnownEndPoints?.checkSessionIframe && contentWindow) {\n            const iframeOrigin = new URL(authWellKnownEndPoints.checkSessionIframe)?.origin;\n            this.outstandingMessages++;\n            contentWindow.postMessage(clientId + ' ' + sessionState, iframeOrigin);\n          } else {\n            this.loggerService.logDebug(configuration, `CheckSession - session_state is '${sessionState}' - AuthWellKnownEndPoints is '${JSON.stringify(authWellKnownEndPoints, null, 2)}'`);\n            this.checkSessionChangedInternal$.next(true);\n          }\n        } else {\n          this.loggerService.logWarning(configuration, `CheckSession - OidcSecurityCheckSession pollServerSession checkSession IFrame does not exist:\n               clientId : '${clientId}' - existingIframe: '${existingIframe}'`);\n        }\n        // after sending three messages with no response, fail.\n        if (this.outstandingMessages > 3) {\n          this.loggerService.logError(configuration, `CheckSession - OidcSecurityCheckSession not receiving check session response messages.\n                            Outstanding messages: '${this.outstandingMessages}'. Server unreachable?`);\n        }\n        this.zone.runOutsideAngular(() => {\n          this.scheduledHeartBeatRunning = this.document?.defaultView?.setTimeout(() => this.zone.run(pollServerSessionRecur), this.heartBeatInterval) ?? null;\n        });\n      });\n    };\n    pollServerSessionRecur();\n  }\n  clearScheduledHeartBeat() {\n    if (this.scheduledHeartBeatRunning !== null) {\n      clearTimeout(this.scheduledHeartBeatRunning);\n      this.scheduledHeartBeatRunning = null;\n    }\n  }\n  messageHandler(configuration, e) {\n    const existingIFrame = this.getExistingIframe();\n    const authWellKnownEndPoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    const startsWith = !!authWellKnownEndPoints?.checkSessionIframe?.startsWith(e.origin);\n    this.outstandingMessages = 0;\n    if (existingIFrame && startsWith && e.source === existingIFrame.contentWindow) {\n      if (e.data === 'error') {\n        this.loggerService.logWarning(configuration, 'CheckSession - error from check session messageHandler');\n      } else if (e.data === 'changed') {\n        this.loggerService.logDebug(configuration, `CheckSession - ${e} from check session messageHandler`);\n        this.checkSessionReceived = true;\n        this.eventService.fireEvent(EventTypes.CheckSessionReceived, e.data);\n        this.checkSessionChangedInternal$.next(true);\n      } else {\n        this.eventService.fireEvent(EventTypes.CheckSessionReceived, e.data);\n        this.loggerService.logDebug(configuration, `CheckSession - ${e.data} from check session messageHandler`);\n      }\n    }\n  }\n  bindMessageEventToIframe(configuration) {\n    this.iframeMessageEventListener = this.messageHandler.bind(this, configuration);\n    const defaultView = this.document.defaultView;\n    if (defaultView) {\n      defaultView.addEventListener('message', this.iframeMessageEventListener, false);\n    }\n  }\n  getOrCreateIframe(configuration) {\n    return this.getExistingIframe() || this.iFrameService.addIFrameToWindowBody(IFRAME_FOR_CHECK_SESSION_IDENTIFIER, configuration);\n  }\n  static {\n    this.ɵfac = function CheckSessionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CheckSessionService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CheckSessionService,\n      factory: CheckSessionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CheckSessionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass PopUpService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.document = inject(DOCUMENT);\n    this.STORAGE_IDENTIFIER = 'popupauth';\n    this.popUp = null;\n    this.handle = -1;\n    this.resultInternal$ = new Subject();\n  }\n  get result$() {\n    return this.resultInternal$.asObservable();\n  }\n  get windowInternal() {\n    return this.document.defaultView;\n  }\n  isCurrentlyInPopup(config) {\n    if (this.canAccessSessionStorage()) {\n      const popup = this.storagePersistenceService.read(this.STORAGE_IDENTIFIER, config);\n      const windowIdentifier = this.windowInternal;\n      if (!windowIdentifier) {\n        return false;\n      }\n      return Boolean(windowIdentifier.opener) && windowIdentifier.opener !== windowIdentifier && Boolean(popup);\n    }\n    return false;\n  }\n  openPopUp(url, popupOptions, config) {\n    const optionsToPass = this.getOptions(popupOptions);\n    this.storagePersistenceService.write(this.STORAGE_IDENTIFIER, 'true', config);\n    const windowIdentifier = this.windowInternal;\n    if (!windowIdentifier) {\n      return;\n    }\n    if (!url) {\n      this.loggerService.logError(config, 'Could not open popup, url is empty');\n      return;\n    }\n    this.popUp = windowIdentifier.open(url, '_blank', optionsToPass);\n    if (!this.popUp) {\n      this.storagePersistenceService.remove(this.STORAGE_IDENTIFIER, config);\n      this.loggerService.logError(config, 'Could not open popup');\n      return;\n    }\n    this.loggerService.logDebug(config, 'Opened popup with url ' + url);\n    const listener = event => {\n      if (!event?.data || typeof event.data !== 'string') {\n        this.cleanUp(listener, config);\n        return;\n      }\n      this.loggerService.logDebug(config, 'Received message from popup with url ' + event.data);\n      this.resultInternal$.next({\n        userClosed: false,\n        receivedUrl: event.data\n      });\n      this.cleanUp(listener, config);\n    };\n    windowIdentifier.addEventListener('message', listener, false);\n    this.handle = windowIdentifier.setInterval(() => {\n      if (this.popUp?.closed) {\n        this.resultInternal$.next({\n          userClosed: true,\n          receivedUrl: ''\n        });\n        this.cleanUp(listener, config);\n      }\n    }, 200);\n  }\n  sendMessageToMainWindow(url, config) {\n    const windowIdentifier = this.windowInternal;\n    if (!windowIdentifier) {\n      return;\n    }\n    if (windowIdentifier.opener) {\n      const href = windowIdentifier.location.href;\n      this.sendMessage(url, href, config);\n    }\n  }\n  cleanUp(listener, config) {\n    const windowIdentifier = this.windowInternal;\n    if (!windowIdentifier) {\n      return;\n    }\n    windowIdentifier.removeEventListener('message', listener, false);\n    windowIdentifier.clearInterval(this.handle);\n    if (this.popUp) {\n      this.storagePersistenceService.remove(this.STORAGE_IDENTIFIER, config);\n      this.popUp.close();\n      this.popUp = null;\n    }\n  }\n  sendMessage(url, href, config) {\n    const windowIdentifier = this.windowInternal;\n    if (!windowIdentifier) {\n      return;\n    }\n    if (!url) {\n      this.loggerService.logDebug(config, `Can not send message to parent, no url: '${url}'`);\n      return;\n    }\n    windowIdentifier.opener.postMessage(url, href);\n  }\n  getOptions(popupOptions) {\n    const popupDefaultOptions = {\n      width: 500,\n      height: 500,\n      left: 50,\n      top: 50\n    };\n    const options = {\n      ...popupDefaultOptions,\n      ...(popupOptions || {})\n    };\n    const windowIdentifier = this.windowInternal;\n    if (!windowIdentifier) {\n      return '';\n    }\n    const width = options.width || popupDefaultOptions.width;\n    const height = options.height || popupDefaultOptions.height;\n    const left = windowIdentifier.screenLeft + (windowIdentifier.outerWidth - width) / 2;\n    const top = windowIdentifier.screenTop + (windowIdentifier.outerHeight - height) / 2;\n    options.left = left;\n    options.top = top;\n    return Object.entries(options).map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`).join(',');\n  }\n  canAccessSessionStorage() {\n    return typeof navigator !== 'undefined' && navigator.cookieEnabled && typeof Storage !== 'undefined';\n  }\n  static {\n    this.ɵfac = function PopUpService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PopUpService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PopUpService,\n      factory: PopUpService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopUpService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass CurrentUrlService {\n  constructor() {\n    this.document = inject(DOCUMENT);\n  }\n  getStateParamFromCurrentUrl(url) {\n    const currentUrl = url || this.getCurrentUrl();\n    if (!currentUrl) {\n      return null;\n    }\n    const parsedUrl = new URL(currentUrl);\n    const urlParams = new URLSearchParams(parsedUrl.search);\n    return urlParams.get('state');\n  }\n  getCurrentUrl() {\n    return this.document?.defaultView?.location.toString() ?? null;\n  }\n  static {\n    this.ɵfac = function CurrentUrlService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CurrentUrlService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CurrentUrlService,\n      factory: CurrentUrlService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CurrentUrlService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass CheckAuthService {\n  constructor() {\n    this.checkSessionService = inject(CheckSessionService);\n    this.currentUrlService = inject(CurrentUrlService);\n    this.silentRenewService = inject(SilentRenewService);\n    this.userService = inject(UserService);\n    this.loggerService = inject(LoggerService);\n    this.authStateService = inject(AuthStateService);\n    this.callbackService = inject(CallbackService);\n    this.refreshSessionService = inject(RefreshSessionService);\n    this.periodicallyTokenCheckService = inject(PeriodicallyTokenCheckService);\n    this.popupService = inject(PopUpService);\n    this.autoLoginService = inject(AutoLoginService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.publicEventsService = inject(PublicEventsService);\n  }\n  getConfig(configuration, url) {\n    const stateParamFromUrl = this.currentUrlService.getStateParamFromCurrentUrl(url);\n    return Boolean(stateParamFromUrl) ? this.getConfigurationWithUrlState([configuration], stateParamFromUrl) : configuration;\n  }\n  checkAuth(configuration, allConfigs, url) {\n    if (!configuration) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    this.publicEventsService.fireEvent(EventTypes.CheckingAuth);\n    const stateParamFromUrl = this.currentUrlService.getStateParamFromCurrentUrl(url);\n    const config = this.getConfig(configuration, url);\n    if (!config) {\n      return throwError(() => new Error(`could not find matching config for state ${stateParamFromUrl}`));\n    }\n    return this.checkAuthWithConfig(configuration, allConfigs, url);\n  }\n  checkAuthMultiple(allConfigs, url) {\n    const stateParamFromUrl = this.currentUrlService.getStateParamFromCurrentUrl(url);\n    if (stateParamFromUrl) {\n      const config = this.getConfigurationWithUrlState(allConfigs, stateParamFromUrl);\n      if (!config) {\n        return throwError(() => new Error(`could not find matching config for state ${stateParamFromUrl}`));\n      }\n      return this.composeMultipleLoginResults(allConfigs, config, url);\n    }\n    const configs = allConfigs;\n    const allChecks$ = configs.map(x => this.checkAuthWithConfig(x, configs, url));\n    return forkJoin(allChecks$);\n  }\n  checkAuthIncludingServer(configuration, allConfigs) {\n    if (!configuration) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    return this.checkAuthWithConfig(configuration, allConfigs).pipe(switchMap(loginResponse => {\n      const {\n        isAuthenticated\n      } = loginResponse;\n      if (isAuthenticated) {\n        return of(loginResponse);\n      }\n      return this.refreshSessionService.forceRefreshSession(configuration, allConfigs).pipe(tap(loginResponseAfterRefreshSession => {\n        if (loginResponseAfterRefreshSession?.isAuthenticated) {\n          this.startCheckSessionAndValidation(configuration, allConfigs);\n        }\n      }));\n    }));\n  }\n  checkAuthWithConfig(config, allConfigs, url) {\n    if (!config) {\n      const errorMessage = 'Please provide at least one configuration before setting up the module';\n      this.loggerService.logError(config, errorMessage);\n      const result = {\n        isAuthenticated: false,\n        errorMessage,\n        userData: null,\n        idToken: '',\n        accessToken: '',\n        configId: ''\n      };\n      return of(result);\n    }\n    const currentUrl = url || this.currentUrlService.getCurrentUrl();\n    if (!currentUrl) {\n      const errorMessage = 'No URL found!';\n      this.loggerService.logError(config, errorMessage);\n      const result = {\n        isAuthenticated: false,\n        errorMessage,\n        userData: null,\n        idToken: '',\n        accessToken: '',\n        configId: ''\n      };\n      return of(result);\n    }\n    const {\n      configId,\n      authority\n    } = config;\n    this.loggerService.logDebug(config, `Working with config '${configId}' using '${authority}'`);\n    if (this.popupService.isCurrentlyInPopup(config)) {\n      this.popupService.sendMessageToMainWindow(currentUrl, config);\n      const result = {\n        isAuthenticated: false,\n        errorMessage: '',\n        userData: null,\n        idToken: '',\n        accessToken: '',\n        configId: ''\n      };\n      return of(result);\n    }\n    const isCallback = this.callbackService.isCallback(currentUrl);\n    this.loggerService.logDebug(config, `currentUrl to check auth with: '${currentUrl}'`);\n    const callback$ = isCallback ? this.callbackService.handleCallbackAndFireEvents(currentUrl, config, allConfigs) : of({});\n    return callback$.pipe(map(() => {\n      const isAuthenticated = this.authStateService.areAuthStorageTokensValid(config);\n      this.loggerService.logDebug(config, `checkAuth completed. Firing events now. isAuthenticated: ${isAuthenticated}`);\n      if (isAuthenticated) {\n        this.startCheckSessionAndValidation(config, allConfigs);\n        if (!isCallback) {\n          this.authStateService.setAuthenticatedAndFireEvent(allConfigs);\n          this.userService.publishUserDataIfExists(config, allConfigs);\n        }\n      }\n      this.publicEventsService.fireEvent(EventTypes.CheckingAuthFinished);\n      const result = {\n        isAuthenticated,\n        userData: this.userService.getUserDataFromStore(config),\n        accessToken: this.authStateService.getAccessToken(config),\n        idToken: this.authStateService.getIdToken(config),\n        configId\n      };\n      return result;\n    }), tap(({\n      isAuthenticated\n    }) => {\n      if (isAuthenticated) {\n        this.autoLoginService.checkSavedRedirectRouteAndNavigate(config);\n      }\n    }), catchError(({\n      message\n    }) => {\n      this.loggerService.logError(config, message);\n      this.publicEventsService.fireEvent(EventTypes.CheckingAuthFinishedWithError, message);\n      const result = {\n        isAuthenticated: false,\n        errorMessage: message,\n        userData: null,\n        idToken: '',\n        accessToken: '',\n        configId\n      };\n      return of(result);\n    }));\n  }\n  startCheckSessionAndValidation(config, allConfigs) {\n    if (this.checkSessionService.isCheckSessionConfigured(config)) {\n      this.checkSessionService.start(config);\n    }\n    this.periodicallyTokenCheckService.startTokenValidationPeriodically(allConfigs, config);\n    if (this.silentRenewService.isSilentRenewConfigured(config)) {\n      this.silentRenewService.getOrCreateIframe(config);\n    }\n  }\n  getConfigurationWithUrlState(configurations, stateFromUrl) {\n    if (!stateFromUrl) {\n      return null;\n    }\n    for (const config of configurations) {\n      const storedState = this.storagePersistenceService.read('authStateControl', config);\n      if (storedState === stateFromUrl) {\n        return config;\n      }\n    }\n    return null;\n  }\n  composeMultipleLoginResults(configurations, activeConfig, url) {\n    const allOtherConfigs = configurations.filter(x => x.configId !== activeConfig.configId);\n    const currentConfigResult = this.checkAuthWithConfig(activeConfig, configurations, url);\n    const allOtherConfigResults = allOtherConfigs.map(config => {\n      const {\n        redirectUrl\n      } = config;\n      return this.checkAuthWithConfig(config, configurations, redirectUrl);\n    });\n    return forkJoin([currentConfigResult, ...allOtherConfigResults]);\n  }\n  static {\n    this.ɵfac = function CheckAuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CheckAuthService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CheckAuthService,\n      factory: CheckAuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CheckAuthService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RedirectService {\n  constructor() {\n    this.document = inject(DOCUMENT);\n  }\n  redirectTo(url) {\n    this.document.location.href = url;\n  }\n  static {\n    this.ɵfac = function RedirectService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RedirectService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RedirectService,\n      factory: RedirectService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RedirectService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ResponseTypeValidationService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.flowHelper = inject(FlowHelper);\n  }\n  hasConfigValidResponseType(configuration) {\n    if (this.flowHelper.isCurrentFlowAnyImplicitFlow(configuration)) {\n      return true;\n    }\n    if (this.flowHelper.isCurrentFlowCodeFlow(configuration)) {\n      return true;\n    }\n    this.loggerService.logWarning(configuration, 'module configured incorrectly, invalid response_type. Check the responseType in the config');\n    return false;\n  }\n  static {\n    this.ɵfac = function ResponseTypeValidationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ResponseTypeValidationService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ResponseTypeValidationService,\n      factory: ResponseTypeValidationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResponseTypeValidationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ParService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.urlService = inject(UrlService);\n    this.dataService = inject(DataService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n  }\n  postParRequest(configuration, authOptions) {\n    let headers = new HttpHeaders();\n    headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');\n    const authWellKnownEndpoints = this.storagePersistenceService.read('authWellKnownEndPoints', configuration);\n    if (!authWellKnownEndpoints) {\n      return throwError(() => new Error('Could not read PAR endpoint because authWellKnownEndPoints are not given'));\n    }\n    const parEndpoint = authWellKnownEndpoints.parEndpoint;\n    if (!parEndpoint) {\n      return throwError(() => new Error('Could not read PAR endpoint from authWellKnownEndpoints'));\n    }\n    return this.urlService.createBodyForParCodeFlowRequest(configuration, authOptions).pipe(switchMap(data => {\n      return this.dataService.post(parEndpoint, data, configuration, headers).pipe(retry(2), map(response => {\n        this.loggerService.logDebug(configuration, 'par response: ', response);\n        return {\n          expiresIn: response.expires_in,\n          requestUri: response.request_uri\n        };\n      }), catchError(error => {\n        const errorMessage = `There was an error on ParService postParRequest`;\n        this.loggerService.logError(configuration, errorMessage, error);\n        return throwError(() => new Error(errorMessage));\n      }));\n    }));\n  }\n  static {\n    this.ɵfac = function ParService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ParService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ParService,\n      factory: ParService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ParService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass ParLoginService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.responseTypeValidationService = inject(ResponseTypeValidationService);\n    this.urlService = inject(UrlService);\n    this.redirectService = inject(RedirectService);\n    this.authWellKnownService = inject(AuthWellKnownService);\n    this.popupService = inject(PopUpService);\n    this.checkAuthService = inject(CheckAuthService);\n    this.parService = inject(ParService);\n  }\n  loginPar(configuration, authOptions) {\n    if (!this.responseTypeValidationService.hasConfigValidResponseType(configuration)) {\n      this.loggerService.logError(configuration, 'Invalid response type!');\n      return;\n    }\n    this.loggerService.logDebug(configuration, 'BEGIN Authorize OIDC Flow, no auth data');\n    this.authWellKnownService.queryAndStoreAuthWellKnownEndPoints(configuration).pipe(switchMap(() => this.parService.postParRequest(configuration, authOptions))).subscribe(response => {\n      this.loggerService.logDebug(configuration, 'par response: ', response);\n      const url = this.urlService.getAuthorizeParUrl(response.requestUri, configuration);\n      this.loggerService.logDebug(configuration, 'par request url: ', url);\n      if (!url) {\n        this.loggerService.logError(configuration, `Could not create URL with param ${response.requestUri}: '${url}'`);\n        return;\n      }\n      if (authOptions?.urlHandler) {\n        authOptions.urlHandler(url);\n      } else {\n        this.redirectService.redirectTo(url);\n      }\n    });\n  }\n  loginWithPopUpPar(configuration, allConfigs, authOptions, popupOptions) {\n    const {\n      configId\n    } = configuration;\n    if (!this.responseTypeValidationService.hasConfigValidResponseType(configuration)) {\n      const errorMessage = 'Invalid response type!';\n      this.loggerService.logError(configuration, errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }\n    this.loggerService.logDebug(configuration, 'BEGIN Authorize OIDC Flow with popup, no auth data');\n    return this.authWellKnownService.queryAndStoreAuthWellKnownEndPoints(configuration).pipe(switchMap(() => this.parService.postParRequest(configuration, authOptions)), switchMap(response => {\n      this.loggerService.logDebug(configuration, `par response: ${response}`);\n      const url = this.urlService.getAuthorizeParUrl(response.requestUri, configuration);\n      this.loggerService.logDebug(configuration, 'par request url: ', url);\n      if (!url) {\n        const errorMessage = `Could not create URL with param ${response.requestUri}: 'url'`;\n        this.loggerService.logError(configuration, errorMessage);\n        return throwError(() => new Error(errorMessage));\n      }\n      this.popupService.openPopUp(url, popupOptions, configuration);\n      return this.popupService.result$.pipe(take(1), switchMap(result => {\n        const {\n          userClosed,\n          receivedUrl\n        } = result;\n        if (userClosed) {\n          return of({\n            isAuthenticated: false,\n            errorMessage: 'User closed popup',\n            userData: null,\n            idToken: '',\n            accessToken: '',\n            configId\n          });\n        }\n        return this.checkAuthService.checkAuth(configuration, allConfigs, receivedUrl);\n      }));\n    }));\n  }\n  static {\n    this.ɵfac = function ParLoginService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ParLoginService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ParLoginService,\n      factory: ParLoginService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ParLoginService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass PopUpLoginService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.responseTypeValidationService = inject(ResponseTypeValidationService);\n    this.urlService = inject(UrlService);\n    this.authWellKnownService = inject(AuthWellKnownService);\n    this.popupService = inject(PopUpService);\n    this.checkAuthService = inject(CheckAuthService);\n  }\n  loginWithPopUpStandard(configuration, allConfigs, authOptions, popupOptions) {\n    const {\n      configId\n    } = configuration;\n    if (!this.responseTypeValidationService.hasConfigValidResponseType(configuration)) {\n      const errorMessage = 'Invalid response type!';\n      this.loggerService.logError(configuration, errorMessage);\n      return throwError(() => new Error(errorMessage));\n    }\n    this.loggerService.logDebug(configuration, 'BEGIN Authorize OIDC Flow with popup, no auth data');\n    return this.authWellKnownService.queryAndStoreAuthWellKnownEndPoints(configuration).pipe(switchMap(() => this.urlService.getAuthorizeUrl(configuration, authOptions)), tap(authUrl => this.popupService.openPopUp(authUrl, popupOptions, configuration)), switchMap(() => {\n      return this.popupService.result$.pipe(take(1), switchMap(result => {\n        const {\n          userClosed,\n          receivedUrl\n        } = result;\n        if (userClosed) {\n          const response = {\n            isAuthenticated: false,\n            errorMessage: 'User closed popup',\n            userData: null,\n            idToken: '',\n            accessToken: '',\n            configId\n          };\n          return of(response);\n        }\n        return this.checkAuthService.checkAuth(configuration, allConfigs, receivedUrl);\n      }));\n    }));\n  }\n  static {\n    this.ɵfac = function PopUpLoginService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PopUpLoginService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PopUpLoginService,\n      factory: PopUpLoginService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PopUpLoginService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass StandardLoginService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.responseTypeValidationService = inject(ResponseTypeValidationService);\n    this.urlService = inject(UrlService);\n    this.redirectService = inject(RedirectService);\n    this.authWellKnownService = inject(AuthWellKnownService);\n    this.flowsDataService = inject(FlowsDataService);\n  }\n  loginStandard(configuration, authOptions) {\n    if (!this.responseTypeValidationService.hasConfigValidResponseType(configuration)) {\n      this.loggerService.logError(configuration, 'Invalid response type!');\n      return;\n    }\n    this.loggerService.logDebug(configuration, 'BEGIN Authorize OIDC Flow, no auth data');\n    this.flowsDataService.setCodeFlowInProgress(configuration);\n    this.authWellKnownService.queryAndStoreAuthWellKnownEndPoints(configuration).subscribe(() => {\n      const {\n        urlHandler\n      } = authOptions || {};\n      this.flowsDataService.resetSilentRenewRunning(configuration);\n      this.urlService.getAuthorizeUrl(configuration, authOptions).subscribe(url => {\n        if (!url) {\n          this.loggerService.logError(configuration, 'Could not create URL', url);\n          return;\n        }\n        if (urlHandler) {\n          urlHandler(url);\n        } else {\n          this.redirectService.redirectTo(url);\n        }\n      });\n    });\n  }\n  static {\n    this.ɵfac = function StandardLoginService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StandardLoginService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: StandardLoginService,\n      factory: StandardLoginService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StandardLoginService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass LoginService {\n  constructor() {\n    this.parLoginService = inject(ParLoginService);\n    this.popUpLoginService = inject(PopUpLoginService);\n    this.standardLoginService = inject(StandardLoginService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.popupService = inject(PopUpService);\n  }\n  login(configuration, authOptions) {\n    if (!configuration) {\n      throw new Error('Please provide a configuration before setting up the module');\n    }\n    const {\n      usePushedAuthorisationRequests\n    } = configuration;\n    if (authOptions?.customParams) {\n      this.storagePersistenceService.write('storageCustomParamsAuthRequest', authOptions.customParams, configuration);\n    }\n    if (usePushedAuthorisationRequests) {\n      return this.parLoginService.loginPar(configuration, authOptions);\n    } else {\n      return this.standardLoginService.loginStandard(configuration, authOptions);\n    }\n  }\n  loginWithPopUp(configuration, allConfigs, authOptions, popupOptions) {\n    if (!configuration) {\n      throw new Error('Please provide a configuration before setting up the module');\n    }\n    const isAlreadyInPopUp = this.popupService.isCurrentlyInPopup(configuration);\n    if (isAlreadyInPopUp) {\n      return of({\n        errorMessage: 'There is already a popup open.'\n      });\n    }\n    const {\n      usePushedAuthorisationRequests\n    } = configuration;\n    if (authOptions?.customParams) {\n      this.storagePersistenceService.write('storageCustomParamsAuthRequest', authOptions.customParams, configuration);\n    }\n    if (usePushedAuthorisationRequests) {\n      return this.parLoginService.loginWithPopUpPar(configuration, allConfigs, authOptions, popupOptions);\n    }\n    return this.popUpLoginService.loginWithPopUpStandard(configuration, allConfigs, authOptions, popupOptions);\n  }\n  static {\n    this.ɵfac = function LoginService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LoginService,\n      factory: LoginService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoginService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * @deprecated Please do not use the `AutoLoginAllRoutesGuard` anymore as it is not recommended anymore, deprecated and will be removed in future versions of this library. More information [Why is AutoLoginAllRoutesGuard not recommended?](https://github.com/damienbod/angular-auth-oidc-client/issues/1549)\n */\nclass AutoLoginAllRoutesGuard {\n  constructor() {\n    this.autoLoginService = inject(AutoLoginService);\n    this.checkAuthService = inject(CheckAuthService);\n    this.loginService = inject(LoginService);\n    this.configurationService = inject(ConfigurationService);\n    this.router = inject(Router);\n  }\n  canLoad() {\n    const url = this.router.getCurrentNavigation()?.extractedUrl.toString().substring(1) ?? '';\n    return checkAuth$1(url, this.configurationService, this.checkAuthService, this.autoLoginService, this.loginService);\n  }\n  canActivate(route, state) {\n    return checkAuth$1(state.url, this.configurationService, this.checkAuthService, this.autoLoginService, this.loginService);\n  }\n  canActivateChild(route, state) {\n    return checkAuth$1(state.url, this.configurationService, this.checkAuthService, this.autoLoginService, this.loginService);\n  }\n  static {\n    this.ɵfac = function AutoLoginAllRoutesGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutoLoginAllRoutesGuard)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutoLoginAllRoutesGuard,\n      factory: AutoLoginAllRoutesGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoLoginAllRoutesGuard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction checkAuth$1(url, configurationService, checkAuthService, autoLoginService, loginService) {\n  return configurationService.getOpenIDConfiguration().pipe(switchMap(config => {\n    const allConfigs = configurationService.getAllConfigurations();\n    return checkAuthService.checkAuth(config, allConfigs).pipe(take(1), map(({\n      isAuthenticated\n    }) => {\n      if (isAuthenticated) {\n        autoLoginService.checkSavedRedirectRouteAndNavigate(config);\n      }\n      if (!isAuthenticated) {\n        autoLoginService.saveRedirectRoute(config, url);\n        loginService.login(config);\n      }\n      return isAuthenticated;\n    }));\n  }));\n}\nclass AutoLoginPartialRoutesGuard {\n  constructor() {\n    this.autoLoginService = inject(AutoLoginService);\n    this.authStateService = inject(AuthStateService);\n    this.loginService = inject(LoginService);\n    this.configurationService = inject(ConfigurationService);\n    this.router = inject(Router);\n  }\n  canLoad() {\n    const url = this.router.getCurrentNavigation()?.extractedUrl.toString().substring(1) ?? '';\n    return checkAuth(url, this.configurationService, this.authStateService, this.autoLoginService, this.loginService);\n  }\n  canActivate(route, state) {\n    const authOptions = route?.data ? {\n      customParams: route.data\n    } : undefined;\n    return checkAuth(state.url, this.configurationService, this.authStateService, this.autoLoginService, this.loginService, authOptions);\n  }\n  canActivateChild(route, state) {\n    const authOptions = route?.data ? {\n      customParams: route.data\n    } : undefined;\n    return checkAuth(state.url, this.configurationService, this.authStateService, this.autoLoginService, this.loginService, authOptions);\n  }\n  static {\n    this.ɵfac = function AutoLoginPartialRoutesGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutoLoginPartialRoutesGuard)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutoLoginPartialRoutesGuard,\n      factory: AutoLoginPartialRoutesGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoLoginPartialRoutesGuard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction autoLoginPartialRoutesGuard(route) {\n  const configurationService = inject(ConfigurationService);\n  const authStateService = inject(AuthStateService);\n  const loginService = inject(LoginService);\n  const autoLoginService = inject(AutoLoginService);\n  const router = inject(Router);\n  const authOptions = route?.data ? {\n    customParams: route.data\n  } : undefined;\n  const url = router.getCurrentNavigation()?.extractedUrl.toString().substring(1) ?? '';\n  return checkAuth(url, configurationService, authStateService, autoLoginService, loginService, authOptions);\n}\nfunction checkAuth(url, configurationService, authStateService, autoLoginService, loginService, authOptions) {\n  return configurationService.getOpenIDConfiguration().pipe(map(configuration => {\n    const isAuthenticated = authStateService.areAuthStorageTokensValid(configuration);\n    if (isAuthenticated) {\n      autoLoginService.checkSavedRedirectRouteAndNavigate(configuration);\n    }\n    if (!isAuthenticated) {\n      autoLoginService.saveRedirectRoute(configuration, url);\n      if (authOptions) {\n        loginService.login(configuration, authOptions);\n      } else {\n        loginService.login(configuration);\n      }\n    }\n    return isAuthenticated;\n  }));\n}\nfunction flattenArray(array) {\n  return array.reduce((flattened, elem) => flattened.concat(Array.isArray(elem) ? flattenArray(elem) : elem), []);\n}\nclass ClosestMatchingRouteService {\n  getConfigIdForClosestMatchingRoute(route, configurations) {\n    for (const config of configurations) {\n      const {\n        secureRoutes\n      } = config;\n      for (const configuredRoute of secureRoutes ?? []) {\n        if (route.startsWith(configuredRoute)) {\n          return {\n            matchingRoute: configuredRoute,\n            matchingConfig: config\n          };\n        }\n      }\n    }\n    return {\n      matchingRoute: null,\n      matchingConfig: null\n    };\n  }\n  static {\n    this.ɵfac = function ClosestMatchingRouteService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ClosestMatchingRouteService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ClosestMatchingRouteService,\n      factory: ClosestMatchingRouteService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClosestMatchingRouteService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass AuthInterceptor {\n  constructor() {\n    this.authStateService = inject(AuthStateService);\n    this.configurationService = inject(ConfigurationService);\n    this.loggerService = inject(LoggerService);\n    this.closestMatchingRouteService = inject(ClosestMatchingRouteService);\n  }\n  intercept(req, next) {\n    return interceptRequest(req, next.handle, {\n      configurationService: this.configurationService,\n      authStateService: this.authStateService,\n      closestMatchingRouteService: this.closestMatchingRouteService,\n      loggerService: this.loggerService\n    });\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthInterceptor)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthInterceptor, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction authInterceptor() {\n  return (req, next) => {\n    return interceptRequest(req, next, {\n      configurationService: inject(ConfigurationService),\n      authStateService: inject(AuthStateService),\n      closestMatchingRouteService: inject(ClosestMatchingRouteService),\n      loggerService: inject(LoggerService)\n    });\n  };\n}\nfunction interceptRequest(req, next, deps) {\n  if (!deps.configurationService.hasAtLeastOneConfig()) {\n    return next(req);\n  }\n  const allConfigurations = deps.configurationService.getAllConfigurations();\n  const allRoutesConfigured = allConfigurations.map(x => x.secureRoutes || []);\n  const allRoutesConfiguredFlat = flattenArray(allRoutesConfigured);\n  if (allRoutesConfiguredFlat.length === 0) {\n    deps.loggerService.logDebug(allConfigurations[0], `No routes to check configured`);\n    return next(req);\n  }\n  const {\n    matchingConfig,\n    matchingRoute\n  } = deps.closestMatchingRouteService.getConfigIdForClosestMatchingRoute(req.url, allConfigurations);\n  if (!matchingConfig) {\n    deps.loggerService.logDebug(allConfigurations[0], `Did not find any configured route for route ${req.url}`);\n    return next(req);\n  }\n  deps.loggerService.logDebug(matchingConfig, `'${req.url}' matches configured route '${matchingRoute}'`);\n  const token = deps.authStateService.getAccessToken(matchingConfig);\n  if (!token) {\n    deps.loggerService.logDebug(matchingConfig, `Wanted to add token to ${req.url} but found no token: '${token}'`);\n    return next(req);\n  }\n  deps.loggerService.logDebug(matchingConfig, `'${req.url}' matches configured route '${matchingRoute}', adding token`);\n  req = req.clone({\n    headers: req.headers.set('Authorization', 'Bearer ' + token)\n  });\n  return next(req);\n}\nfunction removeNullAndUndefinedValues(obj) {\n  const copy = {\n    ...obj\n  };\n  for (const key in obj) {\n    if (obj[key] === undefined || obj[key] === null) {\n      delete copy[key];\n    }\n  }\n  return copy;\n}\nclass LogoffRevocationService {\n  constructor() {\n    this.loggerService = inject(LoggerService);\n    this.dataService = inject(DataService);\n    this.storagePersistenceService = inject(StoragePersistenceService);\n    this.urlService = inject(UrlService);\n    this.checkSessionService = inject(CheckSessionService);\n    this.resetAuthDataService = inject(ResetAuthDataService);\n    this.redirectService = inject(RedirectService);\n  }\n  // Logs out on the server and the local client.\n  // If the server state has changed, check session, then only a local logout.\n  logoff(config, allConfigs, logoutAuthOptions) {\n    if (!config) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    this.loggerService.logDebug(config, 'logoff, remove auth', logoutAuthOptions);\n    const {\n      urlHandler,\n      customParams\n    } = logoutAuthOptions || {};\n    const endSessionUrl = this.urlService.getEndSessionUrl(config, customParams);\n    if (!endSessionUrl) {\n      this.loggerService.logDebug(config, 'No endsessionUrl present. Logoff was only locally. Returning.');\n      return of(null);\n    }\n    if (this.checkSessionService.serverStateChanged(config)) {\n      this.loggerService.logDebug(config, 'Server State changed. Logoff was only locally. Returning.');\n      return of(null);\n    }\n    if (urlHandler) {\n      this.loggerService.logDebug(config, `Custom UrlHandler found. Using this to handle logoff with url '${endSessionUrl}'`);\n      urlHandler(endSessionUrl);\n      this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n      return of(null);\n    }\n    return this.logoffInternal(logoutAuthOptions, endSessionUrl, config, allConfigs);\n  }\n  logoffLocal(config, allConfigs) {\n    this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n    this.checkSessionService.stop();\n  }\n  logoffLocalMultiple(allConfigs) {\n    allConfigs.forEach(configuration => this.logoffLocal(configuration, allConfigs));\n  }\n  // The refresh token and and the access token are revoked on the server. If the refresh token does not exist\n  // only the access token is revoked. Then the logout run.\n  logoffAndRevokeTokens(config, allConfigs, logoutAuthOptions) {\n    if (!config) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    const {\n      revocationEndpoint\n    } = this.storagePersistenceService.read('authWellKnownEndPoints', config) || {};\n    if (!revocationEndpoint) {\n      this.loggerService.logDebug(config, 'revocation endpoint not supported');\n      return this.logoff(config, allConfigs, logoutAuthOptions);\n    }\n    if (this.storagePersistenceService.getRefreshToken(config)) {\n      return this.revokeRefreshToken(config).pipe(switchMap(_ => this.revokeAccessToken(config)), catchError(error => {\n        const errorMessage = `revoke token failed`;\n        this.loggerService.logError(config, errorMessage, error);\n        return throwError(() => new Error(errorMessage));\n      }), concatMap(() => this.logoff(config, allConfigs, logoutAuthOptions)));\n    } else {\n      return this.revokeAccessToken(config).pipe(catchError(error => {\n        const errorMessage = `revoke accessToken failed`;\n        this.loggerService.logError(config, errorMessage, error);\n        return throwError(() => new Error(errorMessage));\n      }), concatMap(() => this.logoff(config, allConfigs, logoutAuthOptions)));\n    }\n  }\n  // https://tools.ietf.org/html/rfc7009\n  // revokes an access token on the STS. If no token is provided, then the token from\n  // the storage is revoked. You can pass any token to revoke. This makes it possible to\n  // manage your own tokens. The is a public API.\n  revokeAccessToken(configuration, accessToken) {\n    if (!configuration) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    const accessTok = accessToken || this.storagePersistenceService.getAccessToken(configuration);\n    const body = this.urlService.createRevocationEndpointBodyAccessToken(accessTok, configuration);\n    return this.sendRevokeRequest(configuration, body);\n  }\n  // https://tools.ietf.org/html/rfc7009\n  // revokes an refresh token on the STS. This is only required in the code flow with refresh tokens.\n  // If no token is provided, then the token from the storage is revoked. You can pass any token to revoke.\n  // This makes it possible to manage your own tokens.\n  revokeRefreshToken(configuration, refreshToken) {\n    if (!configuration) {\n      return throwError(() => new Error('Please provide a configuration before setting up the module'));\n    }\n    const refreshTok = refreshToken || this.storagePersistenceService.getRefreshToken(configuration);\n    const body = this.urlService.createRevocationEndpointBodyRefreshToken(refreshTok, configuration);\n    return this.sendRevokeRequest(configuration, body);\n  }\n  logoffInternal(logoutAuthOptions, endSessionUrl, config, allConfigs) {\n    const {\n      logoffMethod,\n      customParams\n    } = logoutAuthOptions || {};\n    if (!logoffMethod || logoffMethod === 'GET') {\n      this.redirectService.redirectTo(endSessionUrl);\n      this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n      return of(null);\n    }\n    const {\n      state,\n      logout_hint,\n      ui_locales\n    } = customParams || {};\n    const {\n      clientId\n    } = config;\n    const idToken = this.storagePersistenceService.getIdToken(config);\n    const postLogoutRedirectUrl = this.urlService.getPostLogoutRedirectUrl(config);\n    const headers = this.getHeaders();\n    const {\n      url\n    } = this.urlService.getEndSessionEndpoint(config);\n    const body = {\n      id_token_hint: idToken,\n      client_id: clientId,\n      post_logout_redirect_uri: postLogoutRedirectUrl,\n      state,\n      logout_hint,\n      ui_locales\n    };\n    const bodyWithoutNullOrUndefined = removeNullAndUndefinedValues(body);\n    this.resetAuthDataService.resetAuthorizationData(config, allConfigs);\n    return this.dataService.post(url, bodyWithoutNullOrUndefined, config, headers);\n  }\n  sendRevokeRequest(configuration, body) {\n    const url = this.urlService.getRevocationEndpointUrl(configuration);\n    const headers = this.getHeaders();\n    return this.dataService.post(url, body, configuration, headers).pipe(retry(2), switchMap(response => {\n      this.loggerService.logDebug(configuration, 'revocation endpoint post response: ', response);\n      return of(response);\n    }), catchError(error => {\n      const errorMessage = `Revocation request failed`;\n      this.loggerService.logError(configuration, errorMessage, error);\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  getHeaders() {\n    let headers = new HttpHeaders();\n    headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');\n    return headers;\n  }\n  static {\n    this.ɵfac = function LogoffRevocationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LogoffRevocationService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LogoffRevocationService,\n      factory: LogoffRevocationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LogoffRevocationService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass OidcSecurityService {\n  constructor() {\n    this.checkSessionService = inject(CheckSessionService);\n    this.checkAuthService = inject(CheckAuthService);\n    this.userService = inject(UserService);\n    this.tokenHelperService = inject(TokenHelperService);\n    this.configurationService = inject(ConfigurationService);\n    this.authStateService = inject(AuthStateService);\n    this.flowsDataService = inject(FlowsDataService);\n    this.callbackService = inject(CallbackService);\n    this.logoffRevocationService = inject(LogoffRevocationService);\n    this.loginService = inject(LoginService);\n    this.refreshSessionService = inject(RefreshSessionService);\n    this.urlService = inject(UrlService);\n    this.authWellKnownService = inject(AuthWellKnownService);\n    /**\n     * Provides information about the user after they have logged in.\n     *\n     * @returns Returns an object containing either the user data directly (single config) or\n     * the user data per config in case you are running with multiple configs\n     */\n    this.userData = toSignal(this.userData$, {\n      requireSync: true\n    });\n    /**\n     * Emits each time an authorization event occurs.\n     *\n     * @returns Returns an object containing if you are authenticated or not.\n     * Single Config: true if config is authenticated, false if not.\n     * Multiple Configs: true is all configs are authenticated, false if only one of them is not\n     *\n     * The `allConfigsAuthenticated` property contains the auth information _per config_.\n     */\n    this.authenticated = toSignal(this.isAuthenticated$, {\n      requireSync: true\n    });\n  }\n  /**\n   * Provides information about the user after they have logged in.\n   *\n   * @returns Returns an object containing either the user data directly (single config) or\n   * the user data per config in case you are running with multiple configs\n   */\n  get userData$() {\n    return this.userService.userData$;\n  }\n  /**\n   * Emits each time an authorization event occurs.\n   *\n   * @returns Returns an object containing if you are authenticated or not.\n   * Single Config: true if config is authenticated, false if not.\n   * Multiple Configs: true is all configs are authenticated, false if only one of them is not\n   *\n   * The `allConfigsAuthenticated` property contains the auth information _per config_.\n   */\n  get isAuthenticated$() {\n    return this.authStateService.authenticated$;\n  }\n  /**\n   * Emits each time the server sends a CheckSession event and the value changed. This property will always return\n   * true.\n   */\n  get checkSessionChanged$() {\n    return this.checkSessionService.checkSessionChanged$;\n  }\n  /**\n   * Emits on a Security Token Service callback. The observable will never contain a value.\n   */\n  get stsCallback$() {\n    return this.callbackService.stsCallback$;\n  }\n  preloadAuthWellKnownDocument(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(concatMap(config => this.authWellKnownService.queryAndStoreAuthWellKnownEndPoints(config)));\n  }\n  /**\n   * Returns the currently active OpenID configurations.\n   *\n   * @returns an array of OpenIdConfigurations.\n   */\n  getConfigurations() {\n    return this.configurationService.getAllConfigurations();\n  }\n  /**\n   * Returns a single active OpenIdConfiguration.\n   *\n   * @param configId The configId to identify the config. If not passed, the first one is being returned\n   */\n  getConfiguration(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId);\n  }\n  /**\n   * Returns the userData for a configuration\n   *\n   * @param configId The configId to identify the config. If not passed, the first one is being used\n   */\n  getUserData(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.userService.getUserDataFromStore(config)));\n  }\n  /**\n   * Starts the complete setup flow for one configuration. Calling will start the entire authentication flow, and the returned observable\n   * will denote whether the user was successfully authenticated including the user data, the access token, the configId and\n   * an error message in case an error happened\n   *\n   * @param url The URL to perform the authorization on the behalf of.\n   * @param configId The configId to perform the authorization on the behalf of. If not passed, the first configs will be taken\n   *\n   * @returns An object `LoginResponse` containing all information about the login\n   */\n  checkAuth(url, configId) {\n    return this.configurationService.getOpenIDConfigurations(configId).pipe(concatMap(({\n      allConfigs,\n      currentConfig\n    }) => this.checkAuthService.checkAuth(currentConfig, allConfigs, url)));\n  }\n  /**\n   * Starts the complete setup flow for multiple configurations.\n   * Calling will start the entire authentication flow, and the returned observable\n   * will denote whether the user was successfully authenticated including the user data, the access token, the configId and\n   * an error message in case an error happened in an array for each config which was provided\n   *\n   * @param url The URL to perform the authorization on the behalf of.\n   *\n   * @returns An array of `LoginResponse` objects containing all information about the logins\n   */\n  checkAuthMultiple(url) {\n    return this.configurationService.getOpenIDConfigurations().pipe(concatMap(({\n      allConfigs\n    }) => this.checkAuthService.checkAuthMultiple(allConfigs, url)));\n  }\n  /**\n   * Provides information about the current authenticated state\n   *\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns A boolean whether the config is authenticated or not.\n   */\n  isAuthenticated(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.authStateService.isAuthenticated(config)));\n  }\n  /**\n   * Checks the server for an authenticated session using the iframe silent renew if not locally authenticated.\n   */\n  checkAuthIncludingServer(configId) {\n    return this.configurationService.getOpenIDConfigurations(configId).pipe(concatMap(({\n      allConfigs,\n      currentConfig\n    }) => this.checkAuthService.checkAuthIncludingServer(currentConfig, allConfigs)));\n  }\n  /**\n   * Returns the access token for the login scenario.\n   *\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns A string with the access token.\n   */\n  getAccessToken(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.authStateService.getAccessToken(config)));\n  }\n  /**\n   * Returns the ID token for the sign-in.\n   *\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns A string with the id token.\n   */\n  getIdToken(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.authStateService.getIdToken(config)));\n  }\n  /**\n   * Returns the refresh token, if present, for the sign-in.\n   *\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns A string with the refresh token.\n   */\n  getRefreshToken(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.authStateService.getRefreshToken(config)));\n  }\n  /**\n   * Returns the authentication result, if present, for the sign-in.\n   *\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns A object with the authentication result\n   */\n  getAuthenticationResult(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.authStateService.getAuthenticationResult(config)));\n  }\n  /**\n   * Returns the payload from the ID token.\n   *\n   * @param encode Set to true if the payload is base64 encoded\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns The payload from the id token.\n   */\n  getPayloadFromIdToken(encode = false, configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => {\n      const token = this.authStateService.getIdToken(config);\n      return this.tokenHelperService.getPayloadFromToken(token, encode, config);\n    }));\n  }\n  /**\n   * Returns the payload from the access token.\n   *\n   * @param encode Set to true if the payload is base64 encoded\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns The payload from the access token.\n   */\n  getPayloadFromAccessToken(encode = false, configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => {\n      const token = this.authStateService.getAccessToken(config);\n      return this.tokenHelperService.getPayloadFromToken(token, encode, config);\n    }));\n  }\n  /**\n   * Sets a custom state for the authorize request.\n   *\n   * @param state The state to set.\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   */\n  setState(state, configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.flowsDataService.setAuthStateControl(state, config)));\n  }\n  /**\n   * Gets the state value used for the authorize request.\n   *\n   * @param configId The configId to check the information for. If not passed, the first configs will be taken\n   *\n   * @returns The state value used for the authorize request.\n   */\n  getState(configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.flowsDataService.getAuthStateControl(config)));\n  }\n  /**\n   * Redirects the user to the Security Token Service to begin the authentication process.\n   *\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   * @param authOptions The custom options for the the authentication request.\n   */\n  authorize(configId, authOptions) {\n    this.configurationService.getOpenIDConfiguration(configId).subscribe(config => this.loginService.login(config, authOptions));\n  }\n  /**\n   * Opens the Security Token Service in a new window to begin the authentication process.\n   *\n   * @param authOptions The custom options for the authentication request.\n   * @param popupOptions The configuration for the popup window.\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   *\n   * @returns An `Observable<LoginResponse>` containing all information about the login\n   */\n  authorizeWithPopUp(authOptions, popupOptions, configId) {\n    return this.configurationService.getOpenIDConfigurations(configId).pipe(concatMap(({\n      allConfigs,\n      currentConfig\n    }) => this.loginService.loginWithPopUp(currentConfig, allConfigs, authOptions, popupOptions)));\n  }\n  /**\n   * Manually refreshes the session.\n   *\n   * @param customParams Custom parameters to pass to the refresh request.\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   *\n   * @returns An `Observable<LoginResponse>` containing all information about the login\n   */\n  forceRefreshSession(customParams, configId) {\n    return this.configurationService.getOpenIDConfigurations(configId).pipe(concatMap(({\n      allConfigs,\n      currentConfig\n    }) => this.refreshSessionService.userForceRefreshSession(currentConfig, allConfigs, customParams)));\n  }\n  /**\n   * Revokes the refresh token (if present) and the access token on the server and then performs the logoff operation.\n   * The refresh token and and the access token are revoked on the server. If the refresh token does not exist\n   * only the access token is revoked. Then the logout run.\n   *\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   * @param logoutAuthOptions The custom options for the request.\n   *\n   * @returns An observable when the action is finished\n   */\n  logoffAndRevokeTokens(configId, logoutAuthOptions) {\n    return this.configurationService.getOpenIDConfigurations(configId).pipe(concatMap(({\n      allConfigs,\n      currentConfig\n    }) => this.logoffRevocationService.logoffAndRevokeTokens(currentConfig, allConfigs, logoutAuthOptions)));\n  }\n  /**\n   * Logs out on the server and the local client. If the server state has changed, confirmed via check session,\n   * then only a local logout is performed.\n   *\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   * @param logoutAuthOptions with custom parameters and/or an custom url handler\n   */\n  logoff(configId, logoutAuthOptions) {\n    return this.configurationService.getOpenIDConfigurations(configId).pipe(concatMap(({\n      allConfigs,\n      currentConfig\n    }) => this.logoffRevocationService.logoff(currentConfig, allConfigs, logoutAuthOptions)));\n  }\n  /**\n   * Logs the user out of the application without logging them out of the server.\n   * Use this method if you have _one_ config enabled.\n   *\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   */\n  logoffLocal(configId) {\n    this.configurationService.getOpenIDConfigurations(configId).subscribe(({\n      allConfigs,\n      currentConfig\n    }) => this.logoffRevocationService.logoffLocal(currentConfig, allConfigs));\n  }\n  /**\n   * Logs the user out of the application for all configs without logging them out of the server.\n   * Use this method if you have _multiple_ configs enabled.\n   */\n  logoffLocalMultiple() {\n    this.configurationService.getOpenIDConfigurations().subscribe(({\n      allConfigs\n    }) => this.logoffRevocationService.logoffLocalMultiple(allConfigs));\n  }\n  /**\n   * Revokes an access token on the Security Token Service. This is only required in the code flow with refresh tokens. If no token is\n   * provided, then the token from the storage is revoked. You can pass any token to revoke.\n   * https://tools.ietf.org/html/rfc7009\n   *\n   * @param accessToken The access token to revoke.\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   *\n   * @returns An observable when the action is finished\n   */\n  revokeAccessToken(accessToken, configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(concatMap(config => this.logoffRevocationService.revokeAccessToken(config, accessToken)));\n  }\n  /**\n   * Revokes a refresh token on the Security Token Service. This is only required in the code flow with refresh tokens. If no token is\n   * provided, then the token from the storage is revoked. You can pass any token to revoke.\n   * https://tools.ietf.org/html/rfc7009\n   *\n   * @param refreshToken The access token to revoke.\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   *\n   * @returns An observable when the action is finished\n   */\n  revokeRefreshToken(refreshToken, configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(concatMap(config => this.logoffRevocationService.revokeRefreshToken(config, refreshToken)));\n  }\n  /**\n   * Creates the end session URL which can be used to implement an alternate server logout.\n   *\n   * @param customParams\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   *\n   * @returns A string with the end session url or null\n   */\n  getEndSessionUrl(customParams, configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(map(config => this.urlService.getEndSessionUrl(config, customParams)));\n  }\n  /**\n   * Creates the authorize URL based on your flow\n   *\n   * @param customParams\n   * @param configId The configId to perform the action in behalf of. If not passed, the first configs will be taken\n   *\n   * @returns A string with the authorize URL or null\n   */\n  getAuthorizeUrl(customParams, configId) {\n    return this.configurationService.getOpenIDConfiguration(configId).pipe(concatMap(config => this.urlService.getAuthorizeUrl(config, customParams ? {\n      customParams\n    } : undefined)));\n  }\n  static {\n    this.ɵfac = function OidcSecurityService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OidcSecurityService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OidcSecurityService,\n      factory: OidcSecurityService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OidcSecurityService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass DefaultLocalStorageService {\n  read(key) {\n    return localStorage.getItem(key);\n  }\n  write(key, value) {\n    localStorage.setItem(key, value);\n  }\n  remove(key) {\n    localStorage.removeItem(key);\n  }\n  clear() {\n    localStorage.clear();\n  }\n  static {\n    this.ɵfac = function DefaultLocalStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DefaultLocalStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DefaultLocalStorageService,\n      factory: DefaultLocalStorageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultLocalStorageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n// Public classes.\n\n/*\n * Public API Surface of angular-auth-oidc-client\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AbstractLoggerService, AbstractSecurityStorage, AuthInterceptor, AuthModule, AutoLoginAllRoutesGuard, AutoLoginPartialRoutesGuard, ConfigurationService, DefaultLocalStorageService, DefaultSessionStorageService, EventTypes, LogLevel, OidcSecurityService, OpenIdConfigLoader, PopUpService, PublicEventsService, StateValidationResult, StsConfigHttpLoader, StsConfigLoader, StsConfigStaticLoader, ValidationResult, _provideAuth, authInterceptor, autoLoginPartialRoutesGuard, provideAuth };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, Optional, APP_INITIALIZER, NgModule, SkipSelf } from '@angular/core';\nimport * as i1 from '@angular/router';\nimport { map, mergeMap, first, filter, distinctUntilChanged, shareReplay, switchMap as switchMap$1, catchError, tap, takeUntil } from 'rxjs/operators';\nimport { __decorate, __param, __metadata } from 'tslib';\nimport { BehaviorSubject, Subject, switchMap, merge, fromEvent, of, isObservable, pipe, timer, combineLatest, EMPTY, delay, forkJoin, throwError, iif } from 'rxjs';\nimport * as i1$1 from '@angular/common';\nimport { APP_BASE_HREF } from '@angular/common';\nimport * as i1$2 from 'angular-auth-oidc-client';\nimport { AbstractSecurityStorage, EventTypes, ValidationResult, StsConfigHttpLoader, LogLevel, PublicEventsService, OidcSecurityService, AuthModule as AuthModule$1, StsConfigLoader } from 'angular-auth-oidc-client';\nimport * as i2 from '@angular/common/http';\nimport { HTTP_INTERCEPTORS, HttpClientModule, HttpHeaders, HttpParams } from '@angular/common/http';\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n/* tslint:disable:variable-name */\nconst DefaultAuthModuleConfig = {\n  headerName: 'Authorization',\n  // tokenEndpoint: '',\n  resourceSetName: '',\n  authScheme: 'Bearer',\n  skipWhenExpired: false,\n  automaticTokenRefresh: true,\n  refreshTokenTimeInSeconds: 2 * 60,\n  debugAuthentication: false\n};\n/* tslint:enable:variable-name */\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nconst AUTH_OPTIONS = new InjectionToken('AUTH_OPTIONS');\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AuthenticationService {}\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AuthGuard {\n  constructor(_router, _authService) {\n    this._router = _router;\n    this._authService = _authService;\n  }\n  canLoad(route) {\n    // TODO: skipWhenExpired beachten\n    const url = `/${route.path}`;\n    return this.checkLogin(url, null);\n  }\n  canActivate(route, state) {\n    const url = state.url;\n    return this.checkLogin(url, route.queryParams);\n  }\n  checkLogin(url, params) {\n    return this._authService.isAuthenticated().pipe(map(authenticated => {\n      if (authenticated) {\n        return true;\n      }\n      this._authService.redirectUrl = url;\n      if (params != null) {\n        this._router.navigate(['/login'], {\n          queryParams: params\n        });\n      } else {\n        this._router.navigate(['/login']);\n      }\n      return false;\n    }));\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(AuthenticationService));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthGuard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Router\n  }, {\n    type: AuthenticationService\n  }], null);\n})();\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nlet AuthInterceptor = class AuthInterceptor {\n  constructor(config, authenticationService) {\n    this.config = config;\n    this.authenticationService = authenticationService;\n  }\n  intercept(req, next) {\n    return this.getToken().pipe(mergeMap(token => {\n      if (!token || req.headers.has(this.config.headerName) || req.url.includes('oauth/token') || !!this.config.addAuthHeader && !this.config.addAuthHeader(req)) {\n        return next.handle(req);\n      }\n      const cloned = req.clone({\n        headers: req.headers.set(this.config.headerName, `${this.config.authScheme} ${token}`)\n      });\n      return next.handle(cloned);\n    }));\n  }\n  getToken() {\n    return this.authenticationService.getAccessToken().pipe(first());\n  }\n};\nAuthInterceptor = __decorate([__param(0, Inject(AUTH_OPTIONS)), __metadata(\"design:paramtypes\", [Object, AuthenticationService])], AuthInterceptor);\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nvar PRIVILEGE;\n(function (PRIVILEGE) {\n  PRIVILEGE[PRIVILEGE[\"NONE\"] = 0] = \"NONE\";\n  PRIVILEGE[PRIVILEGE[\"READ\"] = 1] = \"READ\";\n  PRIVILEGE[PRIVILEGE[\"MODIFY\"] = 2] = \"MODIFY\";\n  PRIVILEGE[PRIVILEGE[\"ADD\"] = 4] = \"ADD\";\n  PRIVILEGE[PRIVILEGE[\"DELETE\"] = 8] = \"DELETE\";\n  PRIVILEGE[PRIVILEGE[\"EXECUTE\"] = 16] = \"EXECUTE\";\n})(PRIVILEGE || (PRIVILEGE = {}));\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AuthorizationService {\n  static resourceSetMatches(resourceSet, resource, tenantId, applicationId, resourceType) {\n    return resourceSet.ResourceId === resource && (tenantId === null || resourceSet.TenantId === tenantId) && (applicationId === null || resourceSet.ApplicationId === applicationId) && (resourceType === null || resourceSet.ResourceType === resourceType);\n  }\n  constructor() {\n    this.resourceSets$ = new BehaviorSubject([]);\n    // do nothing\n  }\n  /**\n   * checks, whether the logged in user have read-right.\n   * @param resource resource to check against\n   * @param tenantId tenant to which the application belongs\n   * @param applicationId application to which the resource belongs.\n   * @param resourceType resources of different types can have same resourceId.\n   * Therefore the resourceType might be required to distinguish.\n   */\n  canRead(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilege(resource, PRIVILEGE.READ, tenantId, applicationId, resourceType);\n  }\n  /**\n   * checks, whether the logged in user roles have modify-right.\n   * @param resource resource to check against\n   * @param tenantId tenant to which the application belongs\n   * @param applicationId application to which the resource belongs.\n   * @param resourceType resources of different types can have same resourceId.\n   * Therefore the resourceType might be required to distinguish.\n   */\n  canModify(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilege(resource, PRIVILEGE.MODIFY, tenantId, applicationId, resourceType);\n  }\n  /**\n   * checks, whether the logged in user have add-right.\n   * @param resource resource to check against\n   * @param tenantId tenant to which the application belongs\n   * @param applicationId application to which the resource belongs.\n   * @param resourceType resources of different types can have same resourceId.\n   * Therefore the resourceType might be required to distinguish.\n   */\n  canAdd(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilege(resource, PRIVILEGE.ADD, tenantId, applicationId, resourceType);\n  }\n  /**\n   * checks, whether the logged in user have delete-right.\n   * @param resource resource to check against\n   * @param tenantId tenant to which the application belongs\n   * @param applicationId application to which the resource belongs.\n   * @param resourceType resources of different types can have same resourceId.\n   * Therefore the resourceType might be required to distinguish.\n   */\n  canDelete(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilege(resource, PRIVILEGE.DELETE, tenantId, applicationId, resourceType);\n  }\n  /**\n   * checks, whether the logged in user have execute-right.\n   * @param resource resource to check against\n   * @param tenantId tenant to which the application belongs\n   * @param applicationId application to which the resource belongs.\n   * @param resourceType resources of different types can have same resourceId.\n   * Therefore the resourceType might be required to distinguish.\n   */\n  canExecute(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilege(resource, PRIVILEGE.EXECUTE, tenantId, applicationId, resourceType);\n  }\n  /**\n   * Returns resourceSets when they are undefined.\n   */\n  getResourceSets() {\n    return this.resourceSets$.asObservable();\n  }\n  /**\n   * Checks whether logged in user has given privilege for given resource .\n   */\n  hasPrivilege(resource, privilege, tenantId, applicationId, resourceType) {\n    if (!this.roles) {\n      console.error(\"User's roles are unknown!\");\n      return false;\n    }\n    for (const role of this.roles) {\n      /* tslint:disable:no-bitwise */\n      const check = (privilege & this._getPrivilege(resource, role, tenantId, applicationId, resourceType)) === privilege;\n      /* tslint:enable:no-bitwise */\n      if (check) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * Extracts privilege for given resource and role.\n   */\n  _getPrivilege(resource, role, tenantId, applicationId, resourceType) {\n    if (!this.resourceSets$.getValue()) {\n      return 0;\n    }\n    const resourceSet = this.resourceSets$.getValue().find(rs => AuthorizationService.resourceSetMatches(rs, resource, tenantId, applicationId, resourceType));\n    if (!resourceSet || !resourceSet.Permissions) {\n      return 0;\n    }\n    const permission = resourceSet.Permissions.find(p => p.RoleName === role);\n    if (!permission) {\n      return 0;\n    }\n    return permission.PrivilegesGranted;\n  }\n  canReadObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilegeObservable(resource, PRIVILEGE.READ, tenantId, applicationId, resourceType);\n  }\n  canAddObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilegeObservable(resource, PRIVILEGE.ADD, tenantId, applicationId, resourceType);\n  }\n  canModifyObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilegeObservable(resource, PRIVILEGE.MODIFY, tenantId, applicationId, resourceType);\n  }\n  canDeleteObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilegeObservable(resource, PRIVILEGE.DELETE, tenantId, applicationId, resourceType);\n  }\n  canExecuteObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPrivilegeObservable(resource, PRIVILEGE.EXECUTE, tenantId, applicationId, resourceType);\n  }\n  hasPrivilegeObservable(resource, privilege, tenantId, applicationId, resourceType) {\n    return this._getPrivilegeObservable(resource, tenantId, applicationId, resourceType).pipe( /* tslint:disable:no-bitwise */\n    map(privilegesOnResources => (privilege & privilegesOnResources) === privilege)\n    /* tslint:enable:no-bitwise */);\n  }\n  /**\n   * Extracts privilege for given resource and role.\n   */\n  _getPrivilegeObservable(resource, tenantId, applicationId, resourceType) {\n    return this.resourceSets$.pipe(map(resourceSets => {\n      if (!resourceSets || !this.roles) {\n        return 0;\n      }\n      const resourceSet = resourceSets.find(rs => AuthorizationService.resourceSetMatches(rs, resource, tenantId, applicationId, resourceType));\n      if (!resourceSet || !resourceSet.Permissions) {\n        return 0;\n      }\n      const perm = resourceSet.Permissions.find(p => this.roles.includes(p.RoleName));\n      if (!perm) {\n        return 0;\n      }\n      return perm.PrivilegesGranted;\n    }));\n  }\n}\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AccessControlAuthResponse {\n  /* tslint:disable:variable-name */\n  constructor(access_token, token_type, expires_in, refresh_token) {\n    this.access_token = access_token;\n    this.token_type = token_type;\n    this.expires_in = expires_in;\n    this.refresh_token = refresh_token;\n  }\n}\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass JwtToken {}\n\n/*\n * Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nconst STORAGE_SCOPE_REDIRECT_QUERY_PARAMETER = 'redirectStorageScope';\nclass MacmaStorageService {\n  constructor() {\n    this.storageScopeInitialized = false;\n  }\n  get storageScope() {\n    this.ensureScopeInitialized();\n    return this._storageScope;\n  }\n  read(key) {\n    return sessionStorage.getItem(this.makeKey(key));\n  }\n  write(key, value) {\n    sessionStorage.setItem(this.makeKey(key), value);\n  }\n  remove(key) {\n    sessionStorage.removeItem(this.makeKey(key));\n  }\n  clear() {\n    if (this._storageScope != null) {\n      for (let i = 0; i < sessionStorage.length; ++i) {\n        const key = sessionStorage.key(i);\n        if (key && key.startsWith(this._storageScope)) {\n          sessionStorage.removeItem(key);\n        }\n      }\n    } else {\n      sessionStorage.clear();\n    }\n  }\n  initializeStorageScope(config) {\n    if (config.queryParamStorageScopeDiscriminator === STORAGE_SCOPE_REDIRECT_QUERY_PARAMETER) {\n      throw new Error(`Storage scope discriminator cannot be reserved name \"${STORAGE_SCOPE_REDIRECT_QUERY_PARAMETER}\"`);\n    }\n    if (config.queryParamStorageScopeDiscriminator) {\n      const urlParams = new URLSearchParams(window.location.search);\n      this._storageScope = urlParams.get(STORAGE_SCOPE_REDIRECT_QUERY_PARAMETER) ?? urlParams.get(config.queryParamStorageScopeDiscriminator);\n      if (config.debugAuthentication) {\n        if (this._storageScope) {\n          console.log(`using storage scope ${this._storageScope}`);\n        } else {\n          console.log(`Using unscoped storage since URL parameter ${config.queryParamStorageScopeDiscriminator} was not found`);\n        }\n      }\n    }\n    this.storageScopeInitialized = true;\n  }\n  makeKey(key) {\n    this.ensureScopeInitialized();\n    if (this._storageScope != null) {\n      return `${this._storageScope}-${key}`;\n    }\n    return key;\n  }\n  ensureScopeInitialized() {\n    if (!this.storageScopeInitialized) {\n      console.error('Storage scope not initialized. This should not happen');\n    }\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function MacmaStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MacmaStorageService)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MacmaStorageService,\n      factory: MacmaStorageService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MacmaStorageService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass OauthService {\n  constructor(config, storageService, baseHref, locationStrategy) {\n    this.config = config;\n    this.storageService = storageService;\n    this.baseHref = baseHref;\n    this.locationStrategy = locationStrategy;\n    this.componentDestroyed$ = new Subject();\n    if (!this.baseHref) {\n      this.baseHref = '/';\n    }\n  }\n  get desiredRealm() {\n    return this._desiredRealm;\n  }\n  ngOnDestroy() {\n    this.componentDestroyed$.next();\n    this.componentDestroyed$.complete();\n  }\n  setRealm(desiredRealm) {\n    if (desiredRealm === this._desiredRealm) {\n      return;\n    }\n    this._desiredRealm = desiredRealm;\n  }\n  logoff(oidcSecurityService) {\n    return this.updateRedirectUrls(oidcSecurityService, false).pipe(switchMap(() => oidcSecurityService.logoff()));\n  }\n  login(oidcSecurityService, usePopup = this.config.usePopup) {\n    this.updateRedirectUrls(oidcSecurityService, usePopup).subscribe(() => {\n      // if you need to add extra parameters to the login\n      // let culture = 'de-CH';\n      // this.oidcSecurityService.setCustomRequestParameters({ 'ui_locales': culture });\n      if (!usePopup) {\n        // redirect login\n        oidcSecurityService.authorize();\n      } else {\n        // popup / iframe login\n        const intendedWidth = 0.8;\n        const intendedHeight = 0.8;\n        const minWidth = 320;\n        const minHeight = 320;\n        const screenWidth = screen.availWidth;\n        const screenHeight = screen.availHeight;\n        const width = Math.max(minWidth, Math.floor(intendedWidth * screenWidth));\n        const height = Math.max(minHeight, Math.floor(intendedHeight * screenHeight));\n        const top = Math.max(0, Math.floor((screenHeight - height) / 2));\n        const left = Math.max(0, Math.floor((screenWidth - width) / 2));\n        oidcSecurityService.authorize(undefined, {\n          urlHandler: authUrl => {\n            // handle the authorization URL\n            let loginWindow;\n            try {\n              loginWindow = window.open(authUrl, '_blank', 'toolbar=0,location=0,menubar=0,left=' + left + ',top=' + top + ',width=' + width + ',height=' + height);\n            } catch (e) {\n              // In iframe with sandbox attribute and without allow-popups token, an InvalidAccessError will be thrown.\n              console.warn('Opening popup was blocked. Maybe popups are blocked or not allowed in iframe?', e);\n            }\n            // if popup cannot be opened, try with regular redirection login\n            if (!loginWindow) {\n              this.login(oidcSecurityService, false);\n            }\n          }\n        });\n      }\n    });\n  }\n  getLoginRedirectUrl() {\n    return this.getHostUrl() + this.baseHref + this.config.authAssetsPath + 'login.html';\n  }\n  getRenewRedirectUrl() {\n    return this.getHostUrl() + this.baseHref + this.config.authAssetsPath + 'renew.html';\n  }\n  getHostUrl() {\n    return window.location.protocol + '//' + window.location.host;\n  }\n  updateRedirectUrls(oidcSecurityService, forPopup) {\n    // must match the one used during auth code exchange at /token endpoint\n    const redirectUrl = new URL(window.location.href);\n    redirectUrl.hash = '';\n    if (this.storageService.storageScope) {\n      redirectUrl.searchParams.set(STORAGE_SCOPE_REDIRECT_QUERY_PARAMETER, this.storageService.storageScope);\n    }\n    const baseHrefReg = new RegExp(`^${this.locationStrategy.getBaseHref()}`);\n    let postLoginRoute = this.locationStrategy.path(true).replace(baseHrefReg, '');\n    if (!postLoginRoute.startsWith('/')) {\n      postLoginRoute = `/${postLoginRoute}`;\n    }\n    console.log('Updating redirect url to ' + redirectUrl + ' and post-login-route to ' + postLoginRoute);\n    return oidcSecurityService.getConfiguration().pipe(map(config => {\n      this.openIdFlowConfiguration = config;\n      this.openIdFlowConfiguration.silentRenewUrl = this.getRenewRedirectUrl();\n      // redirect login\n      this.openIdFlowConfiguration.redirectUrl = redirectUrl.toString();\n      if (forPopup) {\n        // popup / iframe login\n        this.openIdFlowConfiguration.redirectUrl = this.getLoginRedirectUrl();\n      }\n      const hasStorage = typeof Storage !== 'undefined';\n      if (hasStorage) {\n        this.storageService.write('redirectUrl', this.openIdFlowConfiguration.redirectUrl);\n        this.storageService.write('postLoginRoute', postLoginRoute);\n      }\n      this.openIdFlowConfiguration.postLoginRoute = postLoginRoute;\n      this.openIdFlowConfiguration.postLogoutRedirectUri = redirectUrl.toString();\n    }));\n  }\n  getCurrentOpenIdFlowConfiguration() {\n    return this.openIdFlowConfiguration;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function OauthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OauthService)(i0.ɵɵinject(AUTH_OPTIONS), i0.ɵɵinject(AbstractSecurityStorage), i0.ɵɵinject(APP_BASE_HREF, 8), i0.ɵɵinject(i1$1.LocationStrategy));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OauthService,\n      factory: OauthService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OauthService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [AUTH_OPTIONS]\n    }]\n  }, {\n    type: MacmaStorageService,\n    decorators: [{\n      type: Inject,\n      args: [AbstractSecurityStorage]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [APP_BASE_HREF]\n    }]\n  }, {\n    type: i1$1.LocationStrategy\n  }], null);\n})();\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass MacmaAuthenticationService extends AuthenticationService {\n  constructor(oidcSecurityService, oauthService, eventsService) {\n    super();\n    this.oidcSecurityService = oidcSecurityService;\n    this.oauthService = oauthService;\n    this.eventsService = eventsService;\n    this._loginRequired$ = merge(fromEvent(window, 'oidc-code-message').pipe(filter(event => event.detail.includes('error=login_required'))), this.eventsService.registerForEvents().pipe(filter(notification => notification.type === EventTypes.NewAuthenticationResult && notification.value?.validationResult === ValidationResult.LoginRequired)));\n    this.isAuthenticated().pipe(switchMap(() => this.oidcSecurityService.getIdToken())).subscribe(idToken => this.idToken = idToken);\n    this.isAuthenticated().pipe(switchMap(isAuthenticated => isAuthenticated ? this.oidcSecurityService.getPayloadFromIdToken() : of({}))).subscribe(payload => this.idTokenPayload = payload);\n  }\n  /**\n   * Return an observable that emits whenever silent token renewal (or inital login) failed and manual login is required.\n   * This may either be the case when the single-sign-on session ended or if third party cookies are blocked.\n   */\n  get loginRequired$() {\n    return this._loginRequired$;\n  }\n  /**\n   * Returns accessToken or undefined observable.\n   */\n  getAccessToken() {\n    return this.oidcSecurityService.getConfigurations().length > 0 ? this.oidcSecurityService.getAccessToken().pipe(map(token => token === '' ? null : token)) : of(undefined);\n  }\n  /**\n   * Return object representing payload of id token or null.\n   */\n  getIdTokenPayload() {\n    if (!!this.idToken) {\n      return this.idTokenPayload;\n    } else {\n      return null;\n    }\n  }\n  /**\n   * Returns expiration time of accessToken in milliseconds unix or 0.\n   */\n  getExpiration() {\n    return !!this.getIdTokenPayload() ? of(this.getIdTokenPayload().exp * 1000) : of(0);\n  }\n  /**\n   * Returns name of logged in user or null.\n   */\n  getUsername() {\n    const payloadFromIdToken = this.getIdTokenPayload();\n    if (!!payloadFromIdToken) {\n      return payloadFromIdToken.name || payloadFromIdToken.preferred_username || payloadFromIdToken.unique_name;\n    }\n    return null;\n  }\n  /**\n   * Returns realm of logged in user or null.\n   */\n  getTenantId() {\n    const payloadFromIdToken = this.getIdTokenPayload();\n    if (!!payloadFromIdToken) {\n      return payloadFromIdToken.tid;\n    }\n    return null;\n  }\n  getRoles() {\n    return this.oidcSecurityService.userData$.pipe(map(data => !!data && !!data.userData ? data.userData.roles : []));\n  }\n  isAuthenticated() {\n    return this.oidcSecurityService.isAuthenticated$.pipe(map(result => result.isAuthenticated), distinctUntilChanged());\n  }\n  login() {\n    this.oauthService.login(this.oidcSecurityService);\n  }\n  logout() {\n    const logoutObservable = this.oauthService.logoff(this.oidcSecurityService).pipe(map(() => true), first(), shareReplay(1));\n    logoutObservable.subscribe();\n    return logoutObservable;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function MacmaAuthenticationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MacmaAuthenticationService)(i0.ɵɵinject(i1$2.OidcSecurityService), i0.ɵɵinject(OauthService), i0.ɵɵinject(i1$2.PublicEventsService));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MacmaAuthenticationService,\n      factory: MacmaAuthenticationService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MacmaAuthenticationService, [{\n    type: Injectable\n  }], () => [{\n    type: i1$2.OidcSecurityService\n  }, {\n    type: OauthService\n  }, {\n    type: i1$2.PublicEventsService\n  }], null);\n})();\n\n/*\n * Copyright (c) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nfunction asObservable(value) {\n  return isObservable(value) ? value : of(value);\n}\n\n/*\n * Copyright (c) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n/**\n * Periodic timer reseted by every emission.\n * @param delayValue Delay to wait until emitting another value in ms.\n */\nfunction periodicRepeat(delayValue) {\n  return pipe(switchMap$1(value => merge(of(value), timer(delayValue, delayValue).pipe(map(() => value)))));\n}\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass MacmaAuthorizationService extends AuthorizationService {\n  static {\n    this.READ_PRIVILEGE = 'read';\n  }\n  static {\n    this.ADD_PRIVILEGE = 'add';\n  }\n  static {\n    this.DELETE_PRIVILEGE = 'delete';\n  }\n  static {\n    this.MODIFY_PRIVILEGE = 'modify';\n  }\n  static {\n    this.EXECUTE_PRIVILEGE = 'execute';\n  }\n  static createKey(aclEntry, roleName, privilege) {\n    return JSON.stringify([aclEntry.resourceId, roleName, privilege, aclEntry.resourceOwningTenantId, aclEntry.applicationId, aclEntry.resourceType]);\n  }\n  static resourceMatches(aclEntry, resourceId, tenantId, applicationId, resourceType) {\n    // \"aclEntry.resourceOwningTenantId === '0'\" is necessary here to support MES, where tenant is set to '0'\n    return aclEntry.resourceId === resourceId && (aclEntry.resourceOwningTenantId === null || aclEntry.resourceOwningTenantId === '0' || aclEntry.resourceOwningTenantId === tenantId) && (aclEntry.applicationId === null || aclEntry.applicationId === applicationId) && (aclEntry.resourceType === null || aclEntry.resourceType === resourceType);\n  }\n  constructor(config, authenticationService, http) {\n    super();\n    this.config = config;\n    this.authenticationService = authenticationService;\n    this.http = http;\n    this.permissionKeys = new Set();\n    this.aclLoadedSubject = new BehaviorSubject(false);\n    this.ngUnsubscribe = new Subject();\n    this.refreshSubject = new BehaviorSubject(void 0);\n    this.userAclSource = combineLatest([this.authenticationService.isAuthenticated().pipe(distinctUntilChanged()), asObservable(this.config.authServerBaseUrl ?? this.config.accessControlEndpoint).pipe(distinctUntilChanged()), asObservable(this.config.authServerACLBaseUrl).pipe(distinctUntilChanged()), asObservable(this.config.aclPath ?? '/access-management/v1/user/acl').pipe(distinctUntilChanged()), this.refreshSubject]).pipe(periodicRepeat(this.config.aclReloadIntervalMs), switchMap$1(([isAuthenticated, authServerUrl, authServerAclUrl, aclPath]) => {\n      if (isAuthenticated) {\n        const userAclUrl = (authServerAclUrl ?? authServerUrl) + aclPath;\n        return this.http.get(userAclUrl).pipe(map(response => response.items), catchError(error => {\n          console.error('Failed to load userAcl: ' + JSON.stringify(error));\n          return EMPTY;\n        }));\n      } else {\n        return of([]);\n      }\n    }), tap(() => this.aclLoadedSubject.next(true)), takeUntil(this.ngUnsubscribe), shareReplay(1));\n    this.authenticationService.getRoles().pipe(takeUntil(this.ngUnsubscribe)).subscribe(roles => this.roles = roles);\n    this.userAclSource.pipe(takeUntil(this.ngUnsubscribe)).subscribe(aclEntries => {\n      this.permissionKeys = this.fillPermissionKeys(aclEntries);\n    });\n  }\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n  canRead(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermission(resource, MacmaAuthorizationService.READ_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canReadObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermissionObservable(resource, MacmaAuthorizationService.READ_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canAdd(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermission(resource, MacmaAuthorizationService.ADD_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canAddObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermissionObservable(resource, MacmaAuthorizationService.ADD_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canModify(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermission(resource, MacmaAuthorizationService.MODIFY_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canModifyObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermissionObservable(resource, MacmaAuthorizationService.MODIFY_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canDelete(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermission(resource, MacmaAuthorizationService.DELETE_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canDeleteObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermissionObservable(resource, MacmaAuthorizationService.DELETE_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canExecute(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermission(resource, MacmaAuthorizationService.EXECUTE_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  canExecuteObservable(resource, tenantId, applicationId, resourceType) {\n    return this.hasPermissionObservable(resource, MacmaAuthorizationService.EXECUTE_PRIVILEGE, tenantId, applicationId, resourceType);\n  }\n  /**\n   * Returns true as soon as the acl is loaded the first time\n   */\n  get aclLoaded$() {\n    return this.aclLoadedSubject.asObservable();\n  }\n  hasPermission(resource, privilege, tenantId, applicationId, resourceType) {\n    return this.roles.some(roleName => {\n      // key2 is used here to support MES with ACV3 where tenant is always '0'\n      const key = JSON.stringify([resource, roleName, privilege, tenantId, applicationId, resourceType]);\n      const key2 = JSON.stringify([resource, roleName, privilege, '0', applicationId, resourceType]);\n      return this.permissionKeys.has(key) || this.permissionKeys.has(key2);\n    });\n  }\n  hasPermissionObservable(resource, privilege, tenantId, applicationId, resourceType) {\n    return combineLatest([this.userAclSource, this.authenticationService.getRoles()]).pipe(map(([aclEntries, roles]) => {\n      return aclEntries.some(aclEntry => this.roleAndPermissionMatches(aclEntry, resource, roles, privilege, tenantId, applicationId, resourceType));\n    }));\n  }\n  roleAndPermissionMatches(aclEntry, resourceId, roles, privilege, tenantId, applicationId, resourceType) {\n    return MacmaAuthorizationService.resourceMatches(aclEntry, resourceId, tenantId, applicationId, resourceType) && aclEntry.grants.some(grant => roles.includes(grant.roleName) && grant.privileges.includes(privilege));\n  }\n  fillPermissionKeys(aclEntries) {\n    const permissionKeys = new Set();\n    aclEntries.forEach(aclEntry => {\n      aclEntry.grants.forEach(grant => {\n        // only if the current user has the required role\n        if (this.roles.includes(grant.roleName)) {\n          grant.privileges.forEach(privilege => {\n            const key = MacmaAuthorizationService.createKey(aclEntry, grant.roleName, privilege);\n            permissionKeys.add(key);\n          });\n        }\n      });\n    });\n    return permissionKeys;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function MacmaAuthorizationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MacmaAuthorizationService)(i0.ɵɵinject(AUTH_OPTIONS), i0.ɵɵinject(AuthenticationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MacmaAuthorizationService,\n      factory: MacmaAuthorizationService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MacmaAuthorizationService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [AUTH_OPTIONS]\n    }]\n  }, {\n    type: AuthenticationService\n  }, {\n    type: i2.HttpClient\n  }], null);\n})();\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n/* tslint:disable:variable-name */\nconst DefaultMacmaAuthModuleConfig = {\n  ...DefaultAuthModuleConfig,\n  // refresh tokens seem to require scope \"offline_access\"\n  scope: 'openid email profile',\n  authAssetsPath: 'assets/auth/',\n  usePopup: true,\n  queryParamRealm: 'tenant',\n  requestUserInfo: false,\n  autoLogin: true,\n  // silent renew without acces token may fail if auth-server is cross origin due to browser tracking protection\n  useRefreshToken: false,\n  // might require scope \"offline_access\"\n  aclPath: '/access-management/v1/user/acl',\n  // consider using '/ac-identity/v2/user/acl' for Access Control 3\n  queryParamStorageScopeDiscriminator: undefined\n};\n/* tslint:enable:variable-name */\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AuthOIDCInitializer {\n  static loadOIDCConfiguration(config, oauthService, storageService, baseHref) {\n    storageService.initializeStorageScope(config);\n    return new StsConfigHttpLoader(AuthOIDCInitializer.loadOIDCConfigurationInternal(config, oauthService, storageService, baseHref));\n  }\n  static loadOIDCConfigurationInternal(config, oauthService, storageService, baseHref) {\n    baseHref = baseHref || '/';\n    const urlParams = new URLSearchParams(window.location.search);\n    const queriedRealm = urlParams.get(config.queryParamRealm || 'tenant');\n    // try to retrieve remembered realm\n    const hasStorage = this.hasStorage();\n    let storedRealm = hasStorage ? storageService.read('realm') : null;\n    if (storedRealm === 'undefined' || storedRealm === 'null') {\n      storedRealm = undefined;\n      storageService.remove('realm');\n    }\n    const redirectUrl = this.getRedirectUrl(urlParams, storageService);\n    return combineLatest([asObservable(config.clientId), asObservable(config.defaultRealm), asObservable(config.authServerBaseUrl ?? config.accessControlEndpoint)]).pipe(map(([clientId, defaultRealm, authServerBaseUrl]) => {\n      // use realm from query parameter over session storage over default configuration\n      const desiredRealm = queriedRealm || storedRealm || defaultRealm;\n      const configId = `web-core-auth-${clientId}`;\n      // Clear persisted tokens etc. when switching tenant/realm\n      if (!!storedRealm && desiredRealm !== storedRealm) {\n        // see unexported StoragePersistanceService\n        if (hasStorage) {\n          if (config.debugAuthentication) {\n            console.log('clearing other tenant\\'s auth state');\n          }\n          // key equals configid as defined below, see also lib's OidcConfigService.createUniqueIds and ResetAuthDataService.resetAuthorizationData\n          storageService.remove(configId);\n        }\n      }\n      // remember realm\n      if (hasStorage) {\n        storageService.write('realm', desiredRealm);\n      }\n      if (!desiredRealm) {\n        console.error('No tenant configured and none set for authentication. ' + 'Consider configuring defaultRealm or setting \\'' + config.queryParamRealm || 'tenant' + '\\' query parameter.');\n      }\n      oauthService.setRealm(desiredRealm);\n      const authServerUrl = this.getAuthServerUrl(authServerBaseUrl, desiredRealm);\n      const customParamsAuthRequest = {};\n      if (storageService.storageScope) {\n        customParamsAuthRequest[STORAGE_SCOPE_REDIRECT_QUERY_PARAMETER] = storageService.storageScope;\n      }\n      console.log('CONFIGURING OIDC with auth server ' + authServerUrl);\n      return {\n        configId: configId,\n        // see above where stored things are cleared e.g. when switching tenants/realms\n        authority: authServerUrl,\n        autoUserinfo: config.requestUserInfo,\n        // somehow triggers CORS issues with Keycloak 4.5/4.6 and thereby kills silent renew\n        // The Client MUST validate that the aud (audience) Claim contains its client_id value registered at the Issuer identified by the iss (issuer) Claim as an audience.\n        // The ID Token MUST be rejected if the ID Token does not list the Client as a valid audience, or if it contains additional audiences not trusted by the Client.\n        clientId: clientId,\n        responseType: 'code',\n        // 'id_token token' Implicit Flow\n        scope: config.scope || DefaultMacmaAuthModuleConfig.scope,\n        startCheckSession: false,\n        silentRenew: config.automaticTokenRefresh,\n        // silently renew token via hidden iframe as long as session with identity provider exists\n        useRefreshToken: config.useRefreshToken,\n        ignoreNonceAfterRefresh: true,\n        // makes refresh tokens work with keycloak\n        renewTimeBeforeTokenExpiresInSeconds: config.refreshTokenTimeInSeconds,\n        silentRenewUrl: window.location.protocol + '//' + window.location.host + baseHref + config.authAssetsPath + 'renew.html',\n        triggerAuthorizationResultEvent: false,\n        // set to false to enable post login route navigation. this setting has no effect on the auth event stream\n        forbiddenRoute: '/forbidden',\n        // HTTP 403\n        unauthorizedRoute: '/unauthorized',\n        // HTTP 401\n        logLevel: config.debugAuthentication || DefaultMacmaAuthModuleConfig.debugAuthentication ? LogLevel.Debug : LogLevel.Warn,\n        // id_token C8: The iat Claim can be used to reject tokens that were issued too far away from the current time,\n        // As only server side checks are relevant and telling users to sync their clocks is not great and the security benefit is negligible,\n        // iat claim check of the ID token is disabled.\n        // (similar to what Auth0 did as well, see https://github.com/damienbod/angular-auth-oidc-client/issues/419 and https://github.com/auth0/auth0-spa-js/issues/320)\n        disableIatOffsetValidation: true,\n        historyCleanupOff: true,\n        redirectUrl,\n        postLoginRoute: storageService.read('postLoginRoute') ?? window.location.pathname,\n        postLogoutRoute: window.location.pathname,\n        postLogoutRedirectUri: window.location.href.split('?')[0],\n        storage: storageService,\n        customParamsAuthRequest\n      };\n    }), shareReplay(1), map(newConfig =>\n    // Workaround: return existing config because checkAuth of OIDC lib always loads config again, thus overwriting the changes made for popup login.\n    // OIDC Lib's native popup functionality does not work for external identity providers because windows.opener is null in that case and thus the\n    // callback post message does not arrive. The native popup functionality uses an internal checkAuth and passes the current confings instead of getting\n    // new the original configs from the loader.\n    // Potential risk: lib is used to get an original config frequently, so returning the used/altered config might have side affects in the lib.\n    oauthService.getCurrentOpenIdFlowConfiguration() ?? newConfig));\n  }\n  static hasStorage() {\n    return typeof Storage !== 'undefined';\n  }\n  static getRedirectUrl(urlParams, storageService) {\n    const hasStorage = this.hasStorage();\n    // must match the one used during login at /auth endpoint\n    const defaultRedirectUrl = window.location.href;\n    // try to retrieve remembered redirect url to avoid invalid code issues (400 on code exchange for token)\n    let storedRedirectUrl = hasStorage ? storageService.read('redirectUrl') : null;\n    if (storedRedirectUrl === 'undefined' || storedRedirectUrl === 'null') {\n      storedRedirectUrl = undefined;\n      storageService.remove('redirectUrl');\n    }\n    // use redirect from session storage if we have an authorization callback request\n    const redirectUrl = urlParams.has('code') ? storedRedirectUrl : defaultRedirectUrl;\n    // remember redirect url\n    if (hasStorage) {\n      storageService.write('redirectUrl', redirectUrl);\n    }\n    console.log('Using redirect url \"' + redirectUrl + '\"');\n    return redirectUrl;\n  }\n  static getAuthServerUrl(authServerBaseUrl, desiredRealm) {\n    if (desiredRealm === null || desiredRealm === undefined) {\n      console.log('Using authorization Server ' + authServerBaseUrl + ' without any realm');\n      return authServerBaseUrl;\n    } else {\n      console.log('Using authorization Server ' + authServerBaseUrl + ' with realm ' + desiredRealm);\n      return authServerBaseUrl + '/auth/realms/' + desiredRealm;\n    }\n  }\n}\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nconst httpLoaderFactory = (config, oauthService, storageService, baseHref) => {\n  return AuthOIDCInitializer.loadOIDCConfiguration(config, oauthService, storageService, baseHref);\n};\nclass MacmaAuthModule {\n  constructor(parentModule, oidcSecurityService) {\n    this.oidcSecurityService = oidcSecurityService;\n    if (parentModule) {\n      throw new Error(\"MacmaAuthModule is already loaded. It should only be imported in your application's main module.\");\n    }\n    merge(\n    // Authorization callback handling for Iframes and Popups\n    fromEvent(window, 'oidc-code-message').pipe(tap(event => console.log('Received auth callback via event/message', event)), map(event => event.detail)),\n    // Authorization callback handling for popups when external identity provider (= redirect to different domain) is involved,\n    // and thus window.opener is null inside popup, making events unusable\n    fromEvent(window, 'storage').pipe(filter(event => event.key === 'oidc-code-message'), filter(event => !!event.newValue), tap(event => console.log('Received auth callback via storage', event)),\n    // check if still in storage, otherwise another window already handled it\n    filter(event => {\n      const isStillInStorage = !!localStorage.getItem(event.key);\n      if (!isStillInStorage) {\n        console.log('Another window has handled the auth callback already. Processing stopped.');\n      }\n      return isStillInStorage;\n    }),\n    // cleanup of stored value\n    tap(event => localStorage.removeItem(event.key)), map(event => event.newValue))).pipe(\n    // make sure we only try code-exchange once per login callback\n    distinctUntilChanged()).subscribe(callbackUrl => {\n      console.log('Received authorization callback.', callbackUrl);\n      this.oidcSecurityService.checkAuth(callbackUrl).pipe(first()).subscribe(result => console.log('Auth result:', result));\n    });\n    oidcSecurityService.stsCallback$;\n  }\n  static forRoot(options) {\n    return {\n      ngModule: MacmaAuthModule,\n      providers: [options.authOptionsProvider || {\n        provide: AUTH_OPTIONS,\n        useValue: Object.assign({}, DefaultMacmaAuthModuleConfig, options.config)\n      }, OauthService, {\n        provide: AuthenticationService,\n        useClass: MacmaAuthenticationService\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        deps: [AUTH_OPTIONS, AuthenticationService],\n        multi: true\n      }, {\n        provide: AuthorizationService,\n        useClass: MacmaAuthorizationService\n      }]\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function MacmaAuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MacmaAuthModule)(i0.ɵɵinject(MacmaAuthModule, 12), i0.ɵɵinject(i1$2.OidcSecurityService));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MacmaAuthModule,\n      imports: [HttpClientModule, i1$2.AuthModule]\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: APP_INITIALIZER,\n        useFactory: autoLoginFactory,\n        deps: [PublicEventsService, OidcSecurityService, OauthService, AUTH_OPTIONS],\n        multi: true\n      }, {\n        provide: AbstractSecurityStorage,\n        useClass: MacmaStorageService\n      }],\n      imports: [HttpClientModule, AuthModule$1.forRoot({\n        loader: {\n          provide: StsConfigLoader,\n          useFactory: httpLoaderFactory,\n          deps: [AUTH_OPTIONS, OauthService, AbstractSecurityStorage, [new Optional(), APP_BASE_HREF]]\n        }\n      })]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MacmaAuthModule, [{\n    type: NgModule,\n    args: [{\n      imports: [HttpClientModule, AuthModule$1.forRoot({\n        loader: {\n          provide: StsConfigLoader,\n          useFactory: httpLoaderFactory,\n          deps: [AUTH_OPTIONS, OauthService, AbstractSecurityStorage, [new Optional(), APP_BASE_HREF]]\n        }\n      })],\n      providers: [{\n        provide: APP_INITIALIZER,\n        useFactory: autoLoginFactory,\n        deps: [PublicEventsService, OidcSecurityService, OauthService, AUTH_OPTIONS],\n        multi: true\n      }, {\n        provide: AbstractSecurityStorage,\n        useClass: MacmaStorageService\n      }]\n    }]\n  }], () => [{\n    type: MacmaAuthModule,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: i1$2.OidcSecurityService\n  }], null);\n})();\nfunction autoLoginFactory(publicEventsService, oidcSecurityService, oauthService, config) {\n  return () => oidcSecurityService.preloadAuthWellKnownDocument().pipe(switchMap(() => publicEventsService.registerForEvents()), filter(event => event.type === EventTypes.ConfigLoaded), first(),\n  //switchMap((oidcConfig) => oidcConfigService.withConfig(oidcConfig)),\n  switchMap(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    if (urlParams.has('code')) {\n      if (config.debugAuthentication) {\n        console.log('Got a login callback. Exchanging code for token.');\n      }\n      return oidcSecurityService.checkAuth();\n      // if not login callback and user is not authenticated, trigger check for SSO\n      // auth result is persisted, but without the following code, the lib does not initialize it's authenticated state and refresh check correctly\n    } else if (!urlParams.has('code') && !urlParams.has('error')) {\n      // similar to AutoLoginGuard https://github.com/damienbod/angular-auth-oidc-client/blob/main/projects/angular-auth-oidc-client/src/lib/auto-login/auto-login.guard.ts\n      if (config.debugAuthentication) {\n        console.log('Checking login status');\n      }\n      return oidcSecurityService.isAuthenticated$.pipe(first(), switchMap(result => result.isAuthenticated ? of(true) : oidcSecurityService.checkAuth().pipe(map(loginResponse => loginResponse.isAuthenticated))), first(), switchMap(isAuthenticated => {\n        console.log('Is authenticated: ' + isAuthenticated);\n        if (!isAuthenticated && config.autoLogin) {\n          if (config.debugAuthentication) {\n            console.log('Triggering autologin');\n          }\n          oauthService.login(oidcSecurityService, false);\n          // login is done via redirects, therefore this initializer should wait for login flow to be triggerd two avoid flashing bootstrapping\n          return of(false).pipe(delay(50));\n        } else {\n          return of(true);\n        }\n      }));\n    }\n    return of(void 0);\n  }));\n}\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nconst STORAGE_TOKEN = 'auth.id_token';\nconst STORAGE_EXPIRES_AT = 'auth.expires_at';\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AccessControlAuthenticationService extends AuthenticationService {\n  constructor(config, _http) {\n    super();\n    this.config = config;\n    this._http = _http;\n    this.authResponse$ = new BehaviorSubject(this._restoreAccessControlAuthResponse());\n    this.getUser().subscribe(user => this.user = user);\n    this._automaticTokenRefresh();\n  }\n  login(username, password, tenant, useWinAuth = false) {\n    // Refreshing of tokens only works with exactly the same username, as the username, the user used to login\n    if (!!username && username.indexOf('\\\\') === -1) {\n      username = 'MES\\\\' + username;\n    }\n    this._deleteToken();\n    return this._makeLoginRequest(username, password, tenant, useWinAuth).pipe(mergeMap(t => this._saveToken(t)), tap(() => this._automaticTokenRefresh()));\n  }\n  logout() {\n    this._deleteToken();\n    return of(true);\n  }\n  getAccessControlAuthResponse() {\n    return this.authResponse$.asObservable();\n  }\n  isAuthenticated() {\n    return this.getAccessControlAuthResponse().pipe(switchMap$1(token => forkJoin([of(token), this.getExpiration().pipe(first())])), map(([token, expiresAt]) => {\n      return !!token && Object.keys(token).length > 0 && expiresAt > new Date().getTime();\n    }), distinctUntilChanged());\n  }\n  /**\n   * Returns expiration time of accessToken or 0.\n   */\n  getExpiration() {\n    const expiresAtString = localStorage.getItem(STORAGE_EXPIRES_AT);\n    if (!expiresAtString) {\n      return of(0);\n    }\n    return of(Number(expiresAtString));\n  }\n  /**\n   * Returns accessToken as string or null.\n   */\n  getAccessToken() {\n    return this.getAccessControlAuthResponse().pipe(map(response => !!response && !!response.access_token ? response.access_token : null));\n  }\n  getRoles() {\n    return this.getUser().pipe(map(user => user.roles));\n  }\n  getUser() {\n    return this.getAccessControlAuthResponse().pipe(map(response => {\n      if (!response || !response.access_token) {\n        return {};\n      }\n      const accessToken = this._decodeAccessToken(response.access_token);\n      return {\n        username: accessToken.payload.unique_name,\n        roles: accessToken.payload.role,\n        token: accessToken\n      };\n    }), shareReplay(1));\n  }\n  /**\n   * Return object representing payload of id token or null.\n   */\n  getIdTokenPayload() {\n    return !!this.user && !!this.user.token ? this.user.token.payload : null;\n  }\n  /**\n   * Returns name of logged in user or null.\n   */\n  getUsername() {\n    return !!this.user ? this.user.username : null;\n  }\n  /**\n  * Returns realm of logged in user or null.\n  */\n  getTenantId() {\n    const idTokenPayload = this.getIdTokenPayload();\n    return !!idTokenPayload ? idTokenPayload.tid : null;\n  }\n  _restoreAccessControlAuthResponse() {\n    return JSON.parse(localStorage.getItem(STORAGE_TOKEN) || '{}');\n  }\n  /**\n   * Works only, if login with following scheme is used: <DOMAIN>\\<USER>. Does not work with <USER>!!\n   */\n  refreshToken() {\n    return this.getAccessControlAuthResponse().pipe(first(), filter(token => !!token && !!token.access_token), map(token => {\n      const jwtToken = this._decodeAccessToken(token.access_token);\n      return {\n        token: token,\n        accessToken: jwtToken\n      };\n    }), mergeMap(token => {\n      let headers = new HttpHeaders();\n      let body = new HttpParams();\n      headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');\n      body = body.set('grant_type', 'refresh_token').set('client_id', token.accessToken.payload.unique_name).set('refresh_token', token.token.refresh_token);\n      return this._getOauthTokenEndpoint().pipe(mergeMap(endpoint => this._http.post(endpoint, body, {\n        headers: headers,\n        withCredentials: true\n      })), mergeMap(refreshedToken => this._saveToken(refreshedToken)));\n    }));\n  }\n  _makeLoginRequest(username, password, tenant, useWinAuth) {\n    let headers = new HttpHeaders();\n    let body = new HttpParams();\n    headers = headers.set('Content-Type', 'application/x-www-form-urlencoded');\n    body = body.set('grant_type', 'client_credentials');\n    if (!useWinAuth) {\n      headers = headers.set('Authorization', 'Basic ' + btoa(unescape(encodeURIComponent(username + ':' + password))));\n      body = body.set('username', username).set('password', password);\n      if (tenant) {\n        body = body.set('tenant', tenant);\n      }\n    } else {\n      headers = headers.set('WinAuth', 'true');\n    }\n    return this._getOauthTokenEndpoint().pipe(mergeMap(endpoint => this._http.post(endpoint, body, {\n      headers: headers,\n      withCredentials: true\n    })));\n  }\n  _saveToken(token) {\n    const expiresAt = new Date().getTime() + token.expires_in * 1000;\n    localStorage.setItem(STORAGE_EXPIRES_AT, expiresAt.toString());\n    localStorage.setItem(STORAGE_TOKEN, JSON.stringify(token));\n    this.authResponse$.next(token);\n    return of(token);\n  }\n  _deleteToken() {\n    localStorage.removeItem(STORAGE_TOKEN);\n    localStorage.removeItem(STORAGE_EXPIRES_AT);\n    this.authResponse$.next(undefined);\n  }\n  _decodeAccessToken(accessToken) {\n    if (!accessToken) {\n      return {\n        header: undefined,\n        payload: undefined\n      };\n    }\n    const splittedAccessToken = accessToken.split('.');\n    if (splittedAccessToken.length >= 2) {\n      const header = JSON.parse(atob(splittedAccessToken[0]));\n      const payload = JSON.parse(atob(splittedAccessToken[1]));\n      return {\n        header: header,\n        payload: payload\n      };\n    }\n    return {\n      header: undefined,\n      payload: undefined\n    };\n  }\n  _getOauthTokenEndpoint() {\n    const authServerBaseUrl = this.config.authServerBaseUrl ?? this.config.accessControlEndpoint;\n    if (!(!!authServerBaseUrl && typeof authServerBaseUrl === 'string' && authServerBaseUrl.length > 0 || isObservable(authServerBaseUrl))) {\n      const error = 'Empty authServerBaseUrl in ngAuth-Module Configuration. Please define an authServerBaseUrl.';\n      console.error(error);\n      return throwError(error);\n    }\n    return asObservable(authServerBaseUrl).pipe(map(accessControlEndpoint => {\n      if (!accessControlEndpoint.endsWith('/')) {\n        accessControlEndpoint += '/';\n      }\n      accessControlEndpoint += 'oauth/token';\n      return accessControlEndpoint;\n    }));\n  }\n  _automaticTokenRefresh() {\n    if (this.config.automaticTokenRefresh === true) {\n      this._activateAutomaticRefresh().subscribe();\n    }\n  }\n  _activateAutomaticRefresh() {\n    return this.getExpiration().pipe(map(exp => exp - (this.config.refreshTokenTimeInSeconds || DefaultAuthModuleConfig.refreshTokenTimeInSeconds) * 1000), mergeMap(expiration => timer(expiration - new Date().getTime())), mergeMap(() => this.refreshToken()), mergeMap(() => this._activateAutomaticRefresh()));\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function AccessControlAuthenticationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AccessControlAuthenticationService)(i0.ɵɵinject(AUTH_OPTIONS), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AccessControlAuthenticationService,\n      factory: AccessControlAuthenticationService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccessControlAuthenticationService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [AUTH_OPTIONS]\n    }]\n  }, {\n    type: i2.HttpClient\n  }], null);\n})();\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AccessControlAuthorizationService extends AuthorizationService {\n  constructor(config, authenticationService, _httpClient) {\n    super();\n    this.config = config;\n    this.authenticationService = authenticationService;\n    this._httpClient = _httpClient;\n    this.authenticationService.getRoles().subscribe(roles => this.roles = roles);\n    this.authenticationService.isAuthenticated().pipe(mergeMap(isAuthenticated => iif(() => isAuthenticated, this.loadResourceSets(), of(undefined))), tap(resourceSets => this.resourceSets$.next(resourceSets))).subscribe();\n  }\n  /**\n   * Returns ResourceSets provided by AccessControl for the logged in user.\n   */\n  loadResourceSets() {\n    const authServerBaseUrl = this.config.authServerBaseUrl ?? this.config.accessControlEndpoint;\n    if (!(!!authServerBaseUrl && typeof authServerBaseUrl === 'string' && authServerBaseUrl.length > 0 || isObservable(authServerBaseUrl))) {\n      const error = 'Empty authServerBaseUrl in ngAuth-Module Configuration. Please define an authServerBaseUrl.';\n      console.error(error);\n      return throwError(error);\n    }\n    const accessControlConfigurationEndpoint = asObservable(authServerBaseUrl);\n    const accessControlConfigurationResourceSetName = asObservable(this.config.resourceSetName);\n    return forkJoin([accessControlConfigurationEndpoint, accessControlConfigurationResourceSetName]).pipe(mergeMap(([accessControlEndpoint, resourceSetName]) => {\n      if (!accessControlEndpoint.endsWith('/')) {\n        accessControlEndpoint += '/';\n      }\n      return this._httpClient.get(accessControlEndpoint + 'api/v1/ResourceSets/' + resourceSetName, {\n        observe: 'response'\n      });\n    }), map(response => response.body));\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function AccessControlAuthorizationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AccessControlAuthorizationService)(i0.ɵɵinject(AUTH_OPTIONS), i0.ɵɵinject(AuthenticationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AccessControlAuthorizationService,\n      factory: AccessControlAuthorizationService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccessControlAuthorizationService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [AUTH_OPTIONS]\n    }]\n  }, {\n    type: AuthenticationService\n  }, {\n    type: i2.HttpClient\n  }], null);\n})();\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass User {}\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nclass AuthModule {\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error(\"AuthModule is already loaded. It should only be imported in your application's main module.\");\n    }\n  }\n  static forRoot(options) {\n    return {\n      ngModule: AuthModule,\n      providers: [options.authOptionsProvider || {\n        provide: AUTH_OPTIONS,\n        useValue: Object.assign({}, DefaultAuthModuleConfig, options.config)\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        deps: [AUTH_OPTIONS, AuthenticationService],\n        multi: true\n      }, {\n        provide: AuthenticationService,\n        useClass: AccessControlAuthenticationService\n      }, {\n        provide: AuthorizationService,\n        useClass: AccessControlAuthorizationService\n      }]\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthModule)(i0.ɵɵinject(AuthModule, 12));\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AuthModule,\n      imports: [HttpClientModule]\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [HttpClientModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AuthModule, [{\n    type: NgModule,\n    args: [{\n      imports: [HttpClientModule]\n    }]\n  }], () => [{\n    type: AuthModule,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], null);\n})();\n\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n\n/*\n * Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\n/**\n * @module\n * @description\n * Entry point for all public APIs of the @bci-web-core/auth package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTH_OPTIONS, AccessControlAuthResponse, AccessControlAuthenticationService, AccessControlAuthorizationService, AuthGuard, AuthInterceptor, AuthModule, AuthOIDCInitializer, AuthenticationService, AuthorizationService, DefaultAuthModuleConfig, DefaultMacmaAuthModuleConfig, JwtToken, MacmaAuthModule, MacmaAuthenticationService, MacmaAuthorizationService, OauthService, PRIVILEGE, STORAGE_EXPIRES_AT, STORAGE_TOKEN, User, httpLoaderFactory };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAAS,MAAM,QAAQ,UAAU,MAAM;AACrC,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAGA,MAAI,CAAC,SAAS,OAAO;AACnB,aAAS,QAAQ,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,SAAS,MAAM,QAAQ,EAAE,GAAG;AAC9C,eAAS,MAAM,SAAS,MAAM,CAAC,CAAC,IAAI;AAAA,IACtC;AAAA,EACF;AAEA,MAAI,CAAC,KAAK,SAAS,OAAO,SAAS,SAAS,OAAO,GAAG;AACpD,UAAM,IAAI,YAAY,iBAAiB;AAAA,EACzC;AAEA,MAAI,MAAM,OAAO;AACjB,SAAO,OAAO,MAAM,CAAC,MAAM,KAAK;AAC9B,MAAE;AAEF,QAAI,CAAC,KAAK,SAAS,GAAG,OAAO,SAAS,OAAO,SAAS,OAAO,IAAI;AAC/D,YAAM,IAAI,YAAY,iBAAiB;AAAA,IACzC;AAAA,EACF;AAEA,MAAI,MAAM,MAAM,YAAY,KAAK,QAAQ,OAAO,YAAY,YAAY,MAAM,SAAS,OAAO,IAAI,CAAC;AAEnG,MAAI,OAAO;AAEX,MAAI,SAAS;AAEb,MAAI,UAAU;AAEd,WAAS,KAAK,GAAG,KAAK,KAAK,EAAE,IAAI;AAE/B,QAAI,QAAQ,SAAS,MAAM,OAAO,EAAE,CAAC;AACrC,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,YAAY,uBAAuB,OAAO,EAAE,CAAC;AAAA,IACzD;AAEA,aAAS,UAAU,SAAS,OAAO;AACnC,YAAQ,SAAS;AAEjB,QAAI,QAAQ,GAAG;AACb,cAAQ;AACR,UAAI,SAAS,IAAI,MAAO,UAAU;AAAA,IACpC;AAAA,EACF;AAEA,MAAI,QAAQ,SAAS,QAAQ,MAAO,UAAU,IAAI,MAAM;AACtD,UAAM,IAAI,YAAY,wBAAwB;AAAA,EAChD;AACA,SAAO;AACT;AACA,SAAS,UAAU,MAAM,UAAU,MAAM;AACvC,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,QAAQ,MACV,YAAY,MAAM,KAClB,MAAM,cAAc,SAAS,OAAO;AACtC,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAClC,MAAI,MAAM;AACV,MAAI,OAAO;AAEX,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAEpC,aAAS,UAAU,IAAI,MAAO,KAAK,CAAC;AACpC,YAAQ;AAER,WAAO,OAAO,SAAS,MAAM;AAC3B,cAAQ,SAAS;AACjB,aAAO,SAAS,MAAM,OAAO,UAAU,IAAI;AAAA,IAC7C;AAAA,EACF;AAEA,MAAI,MAAM;AACR,WAAO,SAAS,MAAM,OAAO,UAAU,SAAS,OAAO,IAAI;AAAA,EAC7D;AAEA,MAAI,KAAK;AACP,WAAO,IAAI,SAAS,SAAS,OAAO,GAAG;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAmBA,IAAI,oBAAoB;AAAA,EACtB,OAAO;AAAA,EACP,MAAM;AACR;AAoCA,IAAI,YAAY;AAAA,EACd,OAAO,SAAS,QAAQ,QAAQ,MAAM;AACpC,WAAO,MAAM,QAAQ,mBAAmB,IAAI;AAAA,EAC9C;AAAA,EACA,WAAW,SAAS,YAAY,MAAM,MAAM;AAC1C,WAAO,UAAU,MAAM,mBAAmB,IAAI;AAAA,EAChD;AACF;;;ACYA,SAAS,SAAS,QAAQ,SAAS;AACjC,eAAa,2BAA2B,UAAU,6JAAkK;AACpN,QAAM,kBAAkB,CAAC,SAAS;AAClC,qBAAmB,CAAC,SAAS,YAAY,yBAAyB,QAAQ;AAC1E,QAAM,aAAa,kBAAkB,SAAS,UAAU,IAAI,UAAU,KAAK,OAAO,UAAU,IAAI;AAChG,QAAM,QAAQ,kBAAkB,SAAS,KAAK;AAG9C,MAAI;AACJ,MAAI,SAAS,aAAa;AAExB,YAAQ,OAAO;AAAA,MACb,MAAM;AAAA;AAAA,IACR,GAAG;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AAEL,YAAQ,OAAO;AAAA,MACb,MAAM;AAAA,MACN,OAAO,SAAS;AAAA,IAClB,GAAG;AAAA,MACD;AAAA,IACF,CAAC;AAAA,EACH;AAOA,QAAM,MAAM,OAAO,UAAU;AAAA,IAC3B,MAAM,WAAS,MAAM,IAAI;AAAA,MACvB,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,IACD,OAAO,WAAS;AACd,UAAI,SAAS,cAAc;AAGzB,cAAM;AAAA,MACR;AACA,YAAM,IAAI;AAAA,QACR,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;AAAA,EAGF,CAAC;AACD,MAAI,SAAS,eAAe,MAAM,EAAE,SAAS,GAA2B;AACtE,UAAM,IAAI,aAAc,MAA6D,OAAO,cAAc,eAAe,cAAc,qFAAqF;AAAA,EAC9N;AAEA,cAAY,UAAU,IAAI,YAAY,KAAK,GAAG,CAAC;AAG/C,SAAO,SAAS,MAAM;AACpB,UAAM,UAAU,MAAM;AACtB,YAAQ,QAAQ,MAAM;AAAA,MACpB,KAAK;AACH,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,cAAM,QAAQ;AAAA,MAChB,KAAK;AAEH,cAAM,IAAI,aAAc,MAA6D,OAAO,cAAc,eAAe,cAAc,qFAAqF;AAAA,IAChO;AAAA,EACF,GAAG;AAAA,IACD,OAAO,SAAS;AAAA,EAClB,CAAC;AACH;AACA,SAAS,kBAAkB,eAAe,OAAO,IAAI;AACnD,SAAO,CAAC,GAAG,MAAM,EAAE,SAAS,KAA2B,EAAE,SAAS,KAA2B,aAAa,EAAE,OAAO,EAAE,KAAK;AAC5H;;;ACxOA,IAAM,kBAAN,MAAsB;AAAC;AACvB,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,eAAe;AACzB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,cAAc;AACZ,QAAI,MAAM,QAAQ,KAAK,aAAa,GAAG;AACrC,aAAO,GAAG,KAAK,aAAa;AAAA,IAC9B;AACA,WAAO,GAAG,CAAC,KAAK,aAAa,CAAC;AAAA,EAChC;AACF;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,cAAc;AACZ,QAAI,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAChC,aAAO,SAAS,KAAK,QAAQ;AAAA,IAC/B;AACA,UAAM,sBAAsB,KAAK;AACjC,WAAO,oBAAoB,KAAK,IAAI,WAAS;AAC3C,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,eAAO;AAAA,MACT;AACA,aAAO,CAAC,KAAK;AAAA,IACf,CAAC,CAAC;AAAA,EACJ;AACF;AACA,SAAS,mBAAmB,cAAc;AACxC,MAAI,CAAC,cAAc,QAAQ;AACzB,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AACA,SAAO,IAAI,sBAAsB,aAAa,MAAM;AACtD;AACA,IAAM,gBAAgB,IAAI,eAAe,eAAe;AAKxD,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,SAAS,YAAY,MAAM;AACzB,YAAQ,MAAM,SAAS,GAAG,IAAI;AAAA,EAChC;AAAA,EACA,WAAW,YAAY,MAAM;AAC3B,YAAQ,KAAK,SAAS,GAAG,IAAI;AAAA,EAC/B;AAAA,EACA,SAAS,YAAY,MAAM;AACzB,YAAQ,MAAM,SAAS,GAAG,IAAI;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,KAAK,KAAK;AACR,WAAO,eAAe,QAAQ,GAAG;AAAA,EACnC;AAAA,EACA,MAAM,KAAK,OAAO;AAChB,mBAAe,QAAQ,KAAK,KAAK;AAAA,EACnC;AAAA,EACA,OAAO,KAAK;AACV,mBAAe,WAAW,GAAG;AAAA,EAC/B;AAAA,EACA,QAAQ;AACN,mBAAe,MAAM;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAA8B;AAAA,IACjE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,8BAA6B;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,SAAS,aAAa,cAAc;AAClC,SAAO;AAAA;AAAA,IAEP;AAAA,MACE,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA;AAAA,IAEA,cAAc,UAAU;AAAA,MACtB,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,MAAM,CAAC,aAAa;AAAA,IACtB;AAAA,IAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,IAAG;AAAA,MACD,SAAS;AAAA,MACT,UAAU;AAAA,IACZ;AAAA,EAAC;AACH;AACA,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,QAAQ,cAAc;AAC3B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,GAAG,aAAa,YAAY,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,MACvD,SAAS,CAAC,YAAY;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC;AAAA,MACf,SAAS,CAAC;AAAA,MACV,SAAS,CAAC,YAAY;AAAA,MACtB,WAAW,CAAC,kBAAkB,uBAAuB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI;AAAA,CACH,SAAUA,WAAU;AACnB,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAClC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACjC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AACpC,GAAG,aAAa,WAAW,CAAC,EAAE;AAC9B,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,SAAK,wBAAwB,OAAO,qBAAqB;AAAA,EAC3D;AAAA,EACA,SAAS,eAAe,YAAY,MAAM;AACxC,QAAI,KAAK,mBAAmB,aAAa,GAAG;AAC1C;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,KAAK,SAAS,OAAO,IAAI,KAAK,UAAU,OAAO,IAAI;AACxE,QAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,QAAQ;AAC3B,WAAK,sBAAsB,SAAS,WAAW,QAAQ,MAAM,YAAY,IAAI,GAAG,IAAI;AAAA,IACtF,OAAO;AACL,WAAK,sBAAsB,SAAS,WAAW,QAAQ,MAAM,YAAY,EAAE;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,WAAW,eAAe,YAAY,MAAM;AAC1C,QAAI,CAAC,KAAK,cAAc,aAAa,GAAG;AACtC;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,aAAa,GAAG;AAC1C;AAAA,IACF;AACA,QAAI,CAAC,KAAK,oCAAoC,eAAe,SAAS,IAAI,GAAG;AAC3E;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,KAAK,SAAS,OAAO,IAAI,KAAK,UAAU,OAAO,IAAI;AACxE,QAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,QAAQ;AAC3B,WAAK,sBAAsB,WAAW,UAAU,QAAQ,MAAM,YAAY,IAAI,GAAG,IAAI;AAAA,IACvF,OAAO;AACL,WAAK,sBAAsB,WAAW,UAAU,QAAQ,MAAM,YAAY,EAAE;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,SAAS,eAAe,YAAY,MAAM;AACxC,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,cAAc,aAAa,GAAG;AACtC;AAAA,IACF;AACA,QAAI,KAAK,mBAAmB,aAAa,GAAG;AAC1C;AAAA,IACF;AACA,QAAI,CAAC,KAAK,oCAAoC,eAAe,SAAS,KAAK,GAAG;AAC5E;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,KAAK,SAAS,OAAO,IAAI,KAAK,UAAU,OAAO,IAAI;AACxE,QAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,QAAQ;AAC3B,WAAK,sBAAsB,SAAS,WAAW,QAAQ,MAAM,YAAY,IAAI,GAAG,IAAI;AAAA,IACtF,OAAO;AACL,WAAK,sBAAsB,SAAS,WAAW,QAAQ,MAAM,YAAY,EAAE;AAAA,IAC7E;AAAA,EACF;AAAA,EACA,oCAAoC,eAAe,mBAAmB;AACpE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,iBAAiB,CAAC;AACtB,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,WAAO,YAAY;AAAA,EACrB;AAAA,EACA,cAAc,eAAe;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,iBAAiB,CAAC;AACtB,QAAI,aAAa,MAAM;AACrB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AAAA,EACA,mBAAmB,eAAe;AAChC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,iBAAiB,CAAC;AACtB,WAAO,aAAa,SAAS;AAAA,EAC/B;AAAA,EACA,SAAS,gBAAgB;AACvB,WAAO,OAAO,UAAU,SAAS,KAAK,cAAc,MAAM;AAAA,EAC5D;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,0BAA0B,OAAO,uBAAuB;AAAA,EAC/D;AAAA,EACA,KAAK,KAAK,eAAe;AACvB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,UAAU;AACb,WAAK,cAAc,SAAS,eAAe,mBAAmB,GAAG,uBAAuB,QAAQ,GAAG;AACnG,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB,WAAK,cAAc,SAAS,eAAe,mBAAmB,GAAG,6BAA6B;AAC9F,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,wBAAwB,KAAK,QAAQ;AAC/D,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,MAAM,YAAY;AAAA,EAChC;AAAA,EACA,MAAM,OAAO,eAAe;AAC1B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,UAAU;AACb,WAAK,cAAc,SAAS,eAAe,qCAAqC,QAAQ,GAAG;AAC3F,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB,WAAK,cAAc,SAAS,eAAe,uCAAuC;AAClF,aAAO;AAAA,IACT;AACA,YAAQ,SAAS;AACjB,SAAK,wBAAwB,MAAM,UAAU,KAAK,UAAU,KAAK,CAAC;AAClE,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,eAAe;AACzB,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB,WAAK,cAAc,SAAS,eAAe,qBAAqB,GAAG,yBAAyB;AAC5F,aAAO;AAAA,IACT;AAMA,SAAK,wBAAwB,OAAO,GAAG;AACvC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,MAAM,eAAe;AACnB,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB,WAAK,cAAc,SAAS,eAAe,+CAA+C;AAC1F,aAAO;AAAA,IACT;AAMA,SAAK,wBAAwB,MAAM;AACnC,WAAO;AAAA,EACT;AAAA,EACA,aAAa;AACX,WAAO,OAAO,YAAY;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AACZ,SAAK,wBAAwB,OAAO,qBAAqB;AAAA,EAC3D;AAAA,EACA,KAAK,KAAK,QAAQ;AAChB,UAAM,eAAe,KAAK,sBAAsB,KAAK,KAAK,MAAM,KAAK,CAAC;AACtE,WAAO,aAAa,GAAG;AAAA,EACzB;AAAA,EACA,MAAM,KAAK,OAAO,QAAQ;AACxB,UAAM,eAAe,KAAK,sBAAsB,KAAK,KAAK,MAAM,KAAK,CAAC;AACtE,iBAAa,GAAG,IAAI;AACpB,WAAO,KAAK,sBAAsB,MAAM,cAAc,MAAM;AAAA,EAC9D;AAAA,EACA,OAAO,KAAK,QAAQ;AAClB,UAAM,eAAe,KAAK,sBAAsB,KAAK,KAAK,MAAM,KAAK,CAAC;AACtE,WAAO,aAAa,GAAG;AACvB,SAAK,sBAAsB,MAAM,cAAc,MAAM;AAAA,EACvD;AAAA,EACA,MAAM,QAAQ;AACZ,SAAK,sBAAsB,MAAM,MAAM;AAAA,EACzC;AAAA,EACA,qBAAqB,QAAQ;AAC3B,SAAK,OAAO,iBAAiB,MAAM;AACnC,SAAK,OAAO,6BAA6B,MAAM;AAC/C,SAAK,OAAO,6BAA6B,MAAM;AAC/C,SAAK,OAAO,gBAAgB,MAAM;AAClC,SAAK,OAAO,YAAY,MAAM;AAC9B,SAAK,OAAO,kCAAkC,MAAM;AACpD,SAAK,OAAO,2BAA2B,MAAM;AAC7C,SAAK,OAAO,8BAA8B,MAAM;AAChD,SAAK,OAAO,iCAAiC,MAAM;AACnD,SAAK,OAAO,0BAA0B,MAAM;AAAA,EAC9C;AAAA,EACA,wBAAwB,QAAQ;AAC9B,SAAK,OAAO,aAAa,MAAM;AAC/B,SAAK,OAAO,0BAA0B,MAAM;AAC5C,SAAK,OAAO,eAAe,MAAM;AAAA,EACnC;AAAA,EACA,eAAe,QAAQ;AACrB,WAAO,KAAK,KAAK,aAAa,MAAM;AAAA,EACtC;AAAA,EACA,WAAW,QAAQ;AACjB,WAAO,KAAK,KAAK,eAAe,MAAM,GAAG;AAAA,EAC3C;AAAA,EACA,gBAAgB,QAAQ;AACtB,UAAM,eAAe,KAAK,KAAK,eAAe,MAAM,GAAG;AACvD,QAAI,CAAC,gBAAgB,OAAO,8BAA8B;AACxD,aAAO,KAAK,KAAK,0BAA0B,MAAM;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB,QAAQ;AAC9B,WAAO,KAAK,KAAK,eAAe,MAAM;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA2B;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,2BAA0B;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,cAAc;AACpB,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,iBAAiB,OAAO,yBAAyB;AACtD,SAAK,SAAS,OAAO,MAAM;AAAA,EAC7B;AAAA,EACA,mCAAmC,QAAQ;AACzC,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,UAAM,wBAAwB,KAAK,uBAAuB,MAAM;AAChE,QAAI,yBAAyB,MAAM;AACjC,WAAK,0BAA0B,MAAM;AACrC,WAAK,OAAO,cAAc,qBAAqB;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,QAAQ,KAAK;AAC7B,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,SAAK,eAAe,MAAM,aAAa,KAAK,MAAM;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB,QAAQ;AAC7B,WAAO,KAAK,eAAe,KAAK,aAAa,MAAM;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B,QAAQ;AAChC,SAAK,eAAe,OAAO,aAAa,MAAM;AAAA,EAChD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,sBAAsB,eAAe;AACnC,WAAO,KAAK,cAAc,QAAQ,aAAa;AAAA,EACjD;AAAA,EACA,6BAA6B,eAAe;AAC1C,WAAO,KAAK,yCAAyC,aAAa,KAAK,KAAK,4CAA4C,aAAa;AAAA,EACvI;AAAA,EACA,uCAAuC,eAAe;AACpD,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,KAAK,sBAAsB,aAAa,KAAK,QAAQ,eAAe;AAAA,EAC7E;AAAA,EACA,yCAAyC,eAAe;AACtD,WAAO,KAAK,cAAc,kBAAkB,aAAa;AAAA,EAC3D;AAAA,EACA,cAAc,WAAW,eAAe;AACtC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,aAAO,UAAU,KAAK,OAAK,iBAAiB,CAAC;AAAA,IAC/C;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,4CAA4C,eAAe;AACzD,WAAO,KAAK,cAAc,YAAY,aAAa;AAAA,EACrD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,YAAW;AAAA,MACpB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA,EACA,YAAY;AAEV,WAAO,KAAK,SAAS,aAAa,UAAU,KAAK,SAAS,aAAa;AAAA,EACzE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,gBAAgB,OAAO,aAAa;AAAA,EAC3C;AAAA,EACA,aAAa,gBAAgB,eAAe;AAC1C,QAAI,kBAAkB,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,KAAK,iBAAiB,GAAG;AAC5C,WAAK,cAAc,WAAW,eAAe,6BAA6B,cAAc,iDAAiD;AACzI,uBAAiB;AAAA,IACnB;AACA,UAAM,SAAS,iBAAiB;AAChC,UAAM,MAAM,IAAI,WAAW,KAAK,MAAM,SAAS,CAAC,CAAC;AACjD,UAAM,SAAS,KAAK,cAAc,UAAU;AAC5C,QAAI,QAAQ;AACV,aAAO,gBAAgB,GAAG;AAAA,IAC5B;AACA,WAAO,MAAM,KAAK,KAAK,KAAK,KAAK,EAAE,KAAK,EAAE,IAAI,KAAK,aAAa,CAAC;AAAA,EACnE;AAAA,EACA,MAAM,KAAK;AACT,YAAQ,MAAM,IAAI,SAAS,EAAE,GAAG,OAAO,EAAE;AAAA,EAC3C;AAAA,EACA,aAAa,QAAQ;AACnB,QAAI,SAAS;AACb,UAAM,aAAa;AACnB,UAAM,SAAS,IAAI,YAAY,MAAM;AACrC,UAAM,SAAS,KAAK,cAAc,UAAU;AAC5C,QAAI,QAAQ;AACV,aAAO,gBAAgB,MAAM;AAC7B,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,kBAAU,WAAW,OAAO,CAAC,IAAI,WAAW,MAAM;AAAA,MACpD;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,gBAAgB,OAAO,aAAa;AAAA,EAC3C;AAAA,EACA,YAAY,eAAe;AACzB,UAAM,QAAQ,KAAK,cAAc,aAAa,IAAI,aAAa;AAC/D,SAAK,cAAc,SAAS,eAAe,0BAA0B,KAAK;AAC1E,SAAK,SAAS,OAAO,aAAa;AAClC,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO,eAAe;AAC7B,SAAK,0BAA0B,MAAM,aAAa,OAAO,aAAa;AAAA,EACxE;AAAA,EACA,oBAAoB,eAAe;AACjC,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,KAAK,oBAAoB,aAAa;AAAA,EAC9E;AAAA,EACA,oBAAoB,kBAAkB,eAAe;AACnD,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,MAAM,oBAAoB,kBAAkB,aAAa;AAAA,EACjG;AAAA,EACA,oCAAoC,eAAe;AACjD,QAAI,QAAQ,KAAK,0BAA0B,KAAK,oBAAoB,aAAa;AACjF,QAAI,CAAC,OAAO;AACV,cAAQ,KAAK,cAAc,aAAa,IAAI,aAAa;AACzD,WAAK,0BAA0B,MAAM,oBAAoB,OAAO,aAAa;AAAA,IAC/E;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,cAAc,eAAe;AAC3C,SAAK,0BAA0B,MAAM,iBAAiB,cAAc,aAAa;AAAA,EACnF;AAAA,EACA,qBAAqB,eAAe;AAClC,SAAK,0BAA0B,qBAAqB,aAAa;AAAA,EACnE;AAAA,EACA,gBAAgB,eAAe;AAC7B,WAAO,KAAK,0BAA0B,KAAK,gBAAgB,aAAa;AAAA,EAC1E;AAAA,EACA,mBAAmB,eAAe;AAChC,UAAM,eAAe,KAAK,cAAc,aAAa,IAAI,aAAa;AACtE,SAAK,0BAA0B,MAAM,gBAAgB,cAAc,aAAa;AAChF,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB,eAAe;AAClC,WAAO,CAAC,CAAC,KAAK,0BAA0B,KAAK,6BAA6B,aAAa;AAAA,EACzF;AAAA,EACA,sBAAsB,eAAe;AACnC,SAAK,0BAA0B,MAAM,6BAA6B,MAAM,aAAa;AAAA,EACvF;AAAA,EACA,wBAAwB,eAAe;AACrC,SAAK,0BAA0B,MAAM,6BAA6B,OAAO,aAAa;AAAA,EACxF;AAAA,EACA,qBAAqB,eAAe;AAClC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,gBAAgB,KAAK,kCAAkC,aAAa;AAC1E,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,UAAU,eAAe;AACzC,aAAO;AAAA,IACT;AACA,UAAM,yBAAyB,+BAA+B,KAAK;AACnE,UAAM,2BAA2B,KAAK,MAAM,cAAc,wBAAwB;AAClF,UAAM,iBAAiB,KAAK,OAAM,oBAAI,KAAK,GAAE,YAAY,CAAC;AAC1D,UAAM,4BAA4B,KAAK,IAAI,iBAAiB,wBAAwB;AACpF,UAAM,kBAAkB,4BAA4B;AACpD,QAAI,iBAAiB;AACnB,WAAK,cAAc,SAAS,eAAe,gEAAgE,QAAQ;AACnH,WAAK,wBAAwB,aAAa;AAC1C,aAAO;AAAA,IACT;AACA,WAAO,cAAc,UAAU;AAAA,EACjC;AAAA,EACA,sBAAsB,eAAe;AACnC,UAAM,gBAAgB;AAAA,MACpB,OAAO;AAAA,MACP,2BAA0B,oBAAI,KAAK,GAAE,YAAY;AAAA,IACnD;AACA,SAAK,0BAA0B,MAAM,6BAA6B,KAAK,UAAU,aAAa,GAAG,aAAa;AAAA,EAChH;AAAA,EACA,wBAAwB,eAAe;AACrC,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AACA,SAAK,0BAA0B,MAAM,6BAA6B,IAAI,aAAa;AAAA,EACrF;AAAA,EACA,kCAAkC,eAAe;AAC/C,UAAM,eAAe,KAAK,0BAA0B,KAAK,6BAA6B,aAAa;AACnG,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,OAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,KAAK,MAAM,YAAY;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AAAA,EAC3C;AAAA,EACA,sBAAsB,cAAc;AAClC,WAAO,KAAK,SAAS,YAAY,EAAE,KAAK,IAAI,kBAAgB,KAAK,gBAAgB,YAAY,CAAC,CAAC;AAAA,EACjG;AAAA,EACA,eAAe,aAAa,WAAW;AACrC,WAAO,KAAK,SAAS,aAAa,SAAS,EAAE,KAAK,IAAI,eAAa;AACjE,YAAM,SAAS,UAAU,OAAO,GAAG,UAAU,SAAS,CAAC;AACvD,YAAM,kBAAkB,KAAK,MAAM;AACnC,aAAO,gBAAgB,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AAAA,IACjF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,SAAS,aAAa,YAAY,WAAW;AAC3C,UAAM,YAAY,IAAI,YAAY,EAAE,OAAO,WAAW;AACtD,WAAO,KAAK,KAAK,cAAc,UAAU,EAAE,OAAO,OAAO,WAAW,SAAS,CAAC,EAAE,KAAK,IAAI,gBAAc;AACrG,YAAM,SAAS;AACf,YAAM,YAAY,MAAM,KAAK,IAAI,WAAW,MAAM,CAAC;AACnD,aAAO,KAAK,aAAa,SAAS;AAAA,IACpC,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,aAAa,WAAW;AACtB,QAAI,SAAS;AACb,eAAW,KAAK,WAAW;AACzB,gBAAU,OAAO,aAAa,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,KAAK;AACnB,UAAM,SAAS,KAAK,GAAG;AACvB,WAAO,OAAO,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,MAAM,EAAE;AAAA,EACxE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,aAAN,MAAiB;AAAA,EACf,UAAU,KAAK;AACb,WAAO,mBAAmB,GAAG;AAAA,EAC/B;AAAA,EACA,YAAY,OAAO;AACjB,WAAO,mBAAmB,KAAK;AAAA,EACjC;AAAA,EACA,UAAU,KAAK;AACb,WAAO,mBAAmB,GAAG;AAAA,EAC/B;AAAA,EACA,YAAY,OAAO;AACjB,WAAO,mBAAmB,KAAK;AAAA,EACjC;AACF;AACA,IAAM,2BAA2B,CAAC,QAAQ,SAAS,SAAS,UAAU;AACtE,IAAM,iBAAiB;AACvB,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,yBAAyB,OAAO,sBAAsB;AAAA,EAC7D;AAAA,EACA,gBAAgB,YAAY,MAAM;AAChC,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,OAAO,KAAK,EAAE,QAAQ,QAAQ,KAAK;AACvD,UAAM,QAAQ,IAAI,OAAO,YAAY,OAAO,WAAW;AACvD,UAAM,UAAU,MAAM,KAAK,UAAU;AACrC,WAAO,YAAY,OAAO,KAAK,mBAAmB,QAAQ,CAAC,CAAC;AAAA,EAC9D;AAAA,EACA,kBAAkB,YAAY;AAC5B,WAAO,yBAAyB,KAAK,OAAK,CAAC,CAAC,KAAK,gBAAgB,YAAY,CAAC,CAAC;AAAA,EACjF;AAAA,EACA,gCAAgC,QAAQ,cAAc;AACpD,QAAI,KAAK,WAAW,sBAAsB,MAAM,GAAG;AACjD,aAAO,KAAK,iCAAiC,QAAQ,YAAY;AAAA,IACnE;AACA,WAAO,GAAG,KAAK,qCAAqC,QAAQ,YAAY,CAAC;AAAA,EAC3E;AAAA,EACA,mBAAmB,YAAY,eAAe;AAC5C,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,QAAI,CAAC,wBAAwB;AAC3B,WAAK,cAAc,SAAS,eAAe,qCAAqC;AAChF,aAAO;AAAA,IACT;AACA,UAAM,wBAAwB,uBAAuB;AACrD,QAAI,CAAC,uBAAuB;AAC1B,WAAK,cAAc,SAAS,eAAe,kEAAkE,qBAAqB,GAAG;AACrI,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,UAAU;AACb,WAAK,cAAc,SAAS,eAAe,8DAA8D,QAAQ;AACjH,aAAO;AAAA,IACT;AACA,UAAM,WAAW,sBAAsB,MAAM,GAAG;AAChD,UAAM,mBAAmB,SAAS,CAAC;AACnC,UAAM,iBAAiB,SAAS,CAAC;AACjC,QAAI,SAAS,KAAK,iBAAiB,cAAc;AACjD,aAAS,OAAO,IAAI,eAAe,UAAU;AAC7C,aAAS,OAAO,OAAO,aAAa,QAAQ;AAC5C,WAAO,GAAG,gBAAgB,IAAI,MAAM;AAAA,EACtC;AAAA,EACA,gBAAgB,QAAQ,aAAa;AACnC,QAAI,CAAC,QAAQ;AACX,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,QAAI,KAAK,WAAW,sBAAsB,MAAM,GAAG;AACjD,aAAO,KAAK,2BAA2B,QAAQ,WAAW;AAAA,IAC5D;AACA,WAAO,GAAG,KAAK,+BAA+B,QAAQ,WAAW,KAAK,EAAE;AAAA,EAC1E;AAAA,EACA,sBAAsB,eAAe;AACnC,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,UAAM,qBAAqB,wBAAwB;AACnD,QAAI,CAAC,oBAAoB;AACvB,aAAO;AAAA,QACL,KAAK;AAAA,QACL,gBAAgB;AAAA,MAClB;AAAA,IACF;AACA,UAAM,WAAW,mBAAmB,MAAM,GAAG;AAC7C,UAAM,MAAM,SAAS,CAAC;AACtB,UAAM,iBAAiB,SAAS,CAAC,KAAK;AACtC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,eAAe,cAAc;AAC5C,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,UAAM,UAAU,KAAK,0BAA0B,WAAW,aAAa;AACvE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,kCAChB,gCACA;AAEL,WAAO,KAAK,oBAAoB,SAAS,eAAe,YAAY;AAAA,EACtE;AAAA,EACA,wCAAwC,OAAO,eAAe;AAC5D,UAAM,WAAW,KAAK,YAAY,aAAa;AAC/C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK,iBAAiB;AACnC,aAAS,OAAO,IAAI,aAAa,QAAQ;AACzC,aAAS,OAAO,IAAI,SAAS,KAAK;AAClC,aAAS,OAAO,IAAI,mBAAmB,cAAc;AACrD,WAAO,OAAO,SAAS;AAAA,EACzB;AAAA,EACA,yCAAyC,OAAO,eAAe;AAC7D,UAAM,WAAW,KAAK,YAAY,aAAa;AAC/C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK,iBAAiB;AACnC,aAAS,OAAO,IAAI,aAAa,QAAQ;AACzC,aAAS,OAAO,IAAI,SAAS,KAAK;AAClC,aAAS,OAAO,IAAI,mBAAmB,eAAe;AACtD,WAAO,OAAO,SAAS;AAAA,EACzB;AAAA,EACA,yBAAyB,eAAe;AACtC,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,UAAM,qBAAqB,wBAAwB;AACnD,QAAI,CAAC,oBAAoB;AACvB,aAAO;AAAA,IACT;AACA,UAAM,WAAW,mBAAmB,MAAM,GAAG;AAC7C,WAAO,SAAS,CAAC;AAAA,EACnB;AAAA,EACA,iCAAiC,MAAM,eAAe,mBAAmB;AACvE,UAAM,WAAW,KAAK,YAAY,aAAa;AAC/C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK,iBAAiB;AACnC,aAAS,OAAO,IAAI,cAAc,oBAAoB;AACtD,aAAS,OAAO,IAAI,aAAa,QAAQ;AACzC,QAAI,CAAC,cAAc,aAAa;AAC9B,YAAM,eAAe,KAAK,iBAAiB,gBAAgB,aAAa;AACxE,UAAI,CAAC,cAAc;AACjB,aAAK,cAAc,SAAS,eAAe,4BAA4B,YAAY;AACnF,eAAO;AAAA,MACT;AACA,eAAS,OAAO,IAAI,iBAAiB,YAAY;AAAA,IACnD;AACA,aAAS,OAAO,IAAI,QAAQ,IAAI;AAChC,QAAI,mBAAmB;AACrB,eAAS,KAAK,mBAAmB,mBAC5B,oBACF,MAAM;AAAA,IACX;AACA,UAAM,iBAAiB,KAAK,kBAAkB,aAAa;AAC3D,QAAI,KAAK,iBAAiB,qBAAqB,aAAa,KAAK,gBAAgB;AAC/E,eAAS,OAAO,IAAI,gBAAgB,cAAc;AAClD,aAAO,OAAO,SAAS;AAAA,IACzB;AACA,UAAM,cAAc,KAAK,eAAe,aAAa;AACrD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,aAAS,OAAO,IAAI,gBAAgB,WAAW;AAC/C,WAAO,OAAO,SAAS;AAAA,EACzB;AAAA,EACA,0CAA0C,cAAc,eAAe,qBAAqB;AAC1F,UAAM,WAAW,KAAK,YAAY,aAAa;AAC/C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK,iBAAiB;AACnC,aAAS,OAAO,IAAI,cAAc,eAAe;AACjD,aAAS,OAAO,IAAI,aAAa,QAAQ;AACzC,aAAS,OAAO,IAAI,iBAAiB,YAAY;AACjD,QAAI,qBAAqB;AACvB,eAAS,KAAK,mBAAmB,mBAC5B,sBACF,MAAM;AAAA,IACX;AACA,WAAO,OAAO,SAAS;AAAA,EACzB;AAAA,EACA,gCAAgC,eAAe,aAAa;AAC1D,UAAM,cAAc,KAAK,eAAe,eAAe,WAAW;AAClE,QAAI,CAAC,aAAa;AAChB,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,UAAM,QAAQ,KAAK,iBAAiB,oCAAoC,aAAa;AACrF,UAAM,QAAQ,KAAK,iBAAiB,YAAY,aAAa;AAC7D,SAAK,cAAc,SAAS,eAAe,4CAA4C,KAAK;AAE5F,UAAM,eAAe,KAAK,iBAAiB,mBAAmB,aAAa;AAC3E,WAAO,KAAK,uBAAuB,sBAAsB,YAAY,EAAE,KAAK,IAAI,mBAAiB;AAC/F,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,SAAS,KAAK,iBAAiB,EAAE;AACrC,eAAS,OAAO,IAAI,aAAa,YAAY,EAAE;AAC/C,eAAS,OAAO,OAAO,gBAAgB,WAAW;AAClD,eAAS,OAAO,OAAO,iBAAiB,gBAAgB,EAAE;AAC1D,eAAS,OAAO,OAAO,SAAS,SAAS,EAAE;AAC3C,eAAS,OAAO,OAAO,SAAS,KAAK;AACrC,eAAS,OAAO,OAAO,SAAS,KAAK;AACrC,eAAS,OAAO,OAAO,kBAAkB,aAAa;AACtD,eAAS,OAAO,OAAO,yBAAyB,MAAM;AACtD,UAAI,SAAS;AACX,iBAAS,OAAO,OAAO,MAAM,OAAO;AAAA,MACtC;AACA,UAAI,yBAAyB;AAC3B,iBAAS,KAAK,mBAAmB,mBAC5B,0BACF,MAAM;AAAA,MACX;AACA,UAAI,aAAa,cAAc;AAC7B,iBAAS,KAAK,mBAAmB,mBAC5B,YAAY,eACd,MAAM;AAAA,MACX;AACA,aAAO,OAAO,SAAS;AAAA,IACzB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,yBAAyB,eAAe;AACtC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,uBAAuB;AAC1B,WAAK,cAAc,SAAS,eAAe,8CAA8C,qBAAqB;AAC9G,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,aAAa,eAAe,wBAAwB;AAGtE,QAAI,KAAK,gBAAgB,aAAa,GAAG;AACvC,aAAO,KAAK,qBAAqB,aAAa;AAAA,IAChD;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,sBAAsB,aAAa;AAC5C,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,QAAI,SAAS,KAAK,iBAAiB,cAAc;AACjD,QAAI,CAAC,CAAC,aAAa;AACjB,eAAS,OAAO,IAAI,iBAAiB,WAAW;AAAA,IAClD;AACA,UAAM,wBAAwB,KAAK,yBAAyB,aAAa;AACzE,QAAI,uBAAuB;AACzB,eAAS,OAAO,OAAO,4BAA4B,qBAAqB;AAAA,IAC1E;AACA,QAAI,wBAAwB;AAC1B,eAAS,KAAK,mBAAmB,mBAC5B,yBACF,MAAM;AAAA,IACX;AACA,WAAO,GAAG,GAAG,IAAI,MAAM;AAAA,EACzB;AAAA,EACA,mBAAmB,eAAe,aAAa,OAAO,OAAO,eAAe,QAAQ,qBAAqB;AACvG,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,UAAM,wBAAwB,wBAAwB;AACtD,QAAI,CAAC,uBAAuB;AAC1B,WAAK,cAAc,SAAS,eAAe,kEAAkE,qBAAqB,GAAG;AACrI,aAAO;AAAA,IACT;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,UAAU;AACb,WAAK,cAAc,SAAS,eAAe,8DAA8D,QAAQ;AACjH,aAAO;AAAA,IACT;AACA,QAAI,CAAC,cAAc;AACjB,WAAK,cAAc,SAAS,eAAe,kEAAkE,YAAY;AACzH,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO;AACV,WAAK,cAAc,SAAS,eAAe,2DAA2D,KAAK;AAC3G,aAAO;AAAA,IACT;AACA,UAAM,WAAW,sBAAsB,MAAM,GAAG;AAChD,UAAM,mBAAmB,SAAS,CAAC;AACnC,UAAM,iBAAiB,SAAS,CAAC;AACjC,QAAI,SAAS,KAAK,iBAAiB,cAAc;AACjD,aAAS,OAAO,IAAI,aAAa,QAAQ;AACzC,aAAS,OAAO,OAAO,gBAAgB,WAAW;AAClD,aAAS,OAAO,OAAO,iBAAiB,YAAY;AACpD,aAAS,OAAO,OAAO,SAAS,KAAK;AACrC,aAAS,OAAO,OAAO,SAAS,KAAK;AACrC,aAAS,OAAO,OAAO,SAAS,KAAK;AACrC,QAAI,KAAK,WAAW,sBAAsB,aAAa,GAAG;AACxD,eAAS,OAAO,OAAO,kBAAkB,aAAa;AACtD,eAAS,OAAO,OAAO,yBAAyB,MAAM;AAAA,IACxD;AACA,UAAM,eAAe,kCAChB,0BACA;AAEL,QAAI,OAAO,KAAK,YAAY,EAAE,SAAS,GAAG;AACxC,eAAS,KAAK,mBAAmB,mBAC5B,eACF,MAAM;AAAA,IACX;AACA,QAAI,QAAQ;AACV,eAAS,KAAK,eAAe,QAAQ,UAAU,MAAM;AAAA,IACvD;AACA,QAAI,SAAS;AACX,eAAS,OAAO,OAAO,MAAM,OAAO;AAAA,IACtC;AACA,WAAO,GAAG,gBAAgB,IAAI,MAAM;AAAA,EACtC;AAAA,EACA,qCAAqC,eAAe,cAAc;AAChE,UAAM,QAAQ,KAAK,iBAAiB,oCAAoC,aAAa;AACrF,UAAM,QAAQ,KAAK,iBAAiB,YAAY,aAAa;AAC7D,UAAM,iBAAiB,KAAK,kBAAkB,aAAa;AAC3D,QAAI,CAAC,gBAAgB;AACnB,aAAO;AAAA,IACT;AACA,SAAK,cAAc,SAAS,eAAe,gDAAgD,KAAK;AAChG,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,QAAI,wBAAwB;AAC1B,aAAO,KAAK,mBAAmB,IAAI,gBAAgB,OAAO,OAAO,eAAe,QAAQ,YAAY;AAAA,IACtG;AACA,SAAK,cAAc,SAAS,eAAe,qCAAqC;AAChF,WAAO;AAAA,EACT;AAAA,EACA,iCAAiC,eAAe,cAAc;AAC5D,UAAM,QAAQ,KAAK,iBAAiB,oCAAoC,aAAa;AACrF,UAAM,QAAQ,KAAK,iBAAiB,YAAY,aAAa;AAC7D,SAAK,cAAc,SAAS,eAAe,iDAAiD,KAAK;AAEjG,UAAM,eAAe,KAAK,iBAAiB,mBAAmB,aAAa;AAC3E,WAAO,KAAK,uBAAuB,sBAAsB,YAAY,EAAE,KAAK,IAAI,mBAAiB;AAC/F,YAAM,iBAAiB,KAAK,kBAAkB,aAAa;AAC3D,UAAI,CAAC,gBAAgB;AACnB,eAAO;AAAA,MACT;AACA,YAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,UAAI,wBAAwB;AAC1B,eAAO,KAAK,mBAAmB,eAAe,gBAAgB,OAAO,OAAO,eAAe,QAAQ,YAAY;AAAA,MACjH;AACA,WAAK,cAAc,WAAW,eAAe,qCAAqC;AAClF,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,+BAA+B,eAAe,aAAa;AACzD,UAAM,QAAQ,KAAK,iBAAiB,oCAAoC,aAAa;AACrF,UAAM,QAAQ,KAAK,iBAAiB,YAAY,aAAa;AAC7D,SAAK,cAAc,SAAS,eAAe,4CAA4C,KAAK;AAC5F,UAAM,cAAc,KAAK,eAAe,eAAe,WAAW;AAClE,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,QAAI,wBAAwB;AAC1B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,eAAe,CAAC;AACpB,aAAO,KAAK,mBAAmB,IAAI,aAAa,OAAO,OAAO,eAAe,IAAI,YAAY;AAAA,IAC/F;AACA,SAAK,cAAc,SAAS,eAAe,qCAAqC;AAChF,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,QAAQ,aAAa;AAC9C,UAAM,QAAQ,KAAK,iBAAiB,oCAAoC,MAAM;AAC9E,UAAM,QAAQ,KAAK,iBAAiB,YAAY,MAAM;AACtD,SAAK,cAAc,SAAS,QAAQ,4CAA4C,KAAK;AACrF,UAAM,cAAc,KAAK,eAAe,QAAQ,WAAW;AAC3D,QAAI,CAAC,aAAa;AAChB,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,WAAO,KAAK,iBAAiB,MAAM,EAAE,KAAK,IAAI,mBAAiB;AAC7D,YAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,MAAM;AACnG,UAAI,wBAAwB;AAC1B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI,eAAe,CAAC;AACpB,eAAO,KAAK,mBAAmB,eAAe,aAAa,OAAO,OAAO,QAAQ,IAAI,YAAY;AAAA,MACnG;AACA,WAAK,cAAc,SAAS,QAAQ,qCAAqC;AACzE,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,iBAAiB,QAAQ;AACvB,QAAI,OAAO,aAAa;AACtB,aAAO,GAAG,EAAE;AAAA,IACd;AAEA,UAAM,eAAe,KAAK,iBAAiB,mBAAmB,MAAM;AACpE,WAAO,KAAK,uBAAuB,sBAAsB,YAAY;AAAA,EACvE;AAAA,EACA,eAAe,eAAe,aAAa;AACzC,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,aAAa;AAE5B,oBAAc,YAAY;AAAA,IAC5B;AACA,QAAI,CAAC,aAAa;AAChB,WAAK,cAAc,SAAS,eAAe,oCAAoC,WAAW;AAC1F,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,eAAe;AAC/B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,gBAAgB;AACnB,WAAK,cAAc,SAAS,eAAe,uCAAuC,cAAc;AAChG,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,eAAe;AACzB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,UAAU;AACb,WAAK,cAAc,SAAS,eAAe,iCAAiC,QAAQ;AACpF,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,cAAc,QAAQ;AACvC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,mBACrC,aACJ,GAAG;AACF,eAAS,OAAO,OAAO,KAAK,MAAM,SAAS,CAAC;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,QAAQ,KAAK,OAAO;AACjC,WAAO,OAAO,IAAI,KAAK,KAAK;AAAA,EAC9B;AAAA,EACA,iBAAiB,gBAAgB;AAC/B,qBAAiB,kBAAkB;AACnC,WAAO,IAAI,WAAW;AAAA,MACpB,YAAY;AAAA,MACZ,SAAS,IAAI,WAAW;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,eAAe;AAC7B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AACA,WAAO,UAAU,SAAS,cAAc,KAAK,QAAQ,oBAAoB;AAAA,EAC3E;AAAA,EACA,qBAAqB,eAAe;AAElC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,wBAAwB,KAAK,yBAAyB,aAAa;AACzE,WAAO,GAAG,SAAS,wBAAwB,QAAQ,aAAa,qBAAqB;AAAA,EACvF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,YAAW;AAAA,MACpB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,OAAO,OAAO,UAAU;AAAA,EAC/B;AAAA,EACA,IAAI,KAAK,QAAQ;AACf,WAAO,KAAK,KAAK,IAAI,KAAK,MAAM;AAAA,EAClC;AAAA,EACA,KAAK,KAAK,MAAM,QAAQ;AACtB,WAAO,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAoB;AAC1B,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc;AACZ,SAAK,aAAa,OAAO,eAAe;AAAA,EAC1C;AAAA,EACA,IAAI,KAAK,QAAQ,OAAO;AACtB,UAAM,UAAU,KAAK,eAAe,KAAK;AACzC,UAAM,SAAS,KAAK,cAAc,MAAM;AACxC,WAAO,KAAK,WAAW,IAAI,KAAK;AAAA,MAC9B;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,KAAK,KAAK,MAAM,QAAQ,eAAe;AACrC,UAAM,UAAU,iBAAiB,KAAK,eAAe;AACrD,UAAM,SAAS,KAAK,cAAc,MAAM;AACxC,WAAO,KAAK,WAAW,KAAK,OAAO,IAAI,MAAM;AAAA,MAC3C;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,UAAU,IAAI,YAAY;AAC9B,cAAU,QAAQ,IAAI,UAAU,kBAAkB;AAClD,QAAI,CAAC,CAAC,OAAO;AACX,gBAAU,QAAQ,IAAI,iBAAiB,YAAY,mBAAmB,KAAK,CAAC;AAAA,IAC9E;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAQ;AACpB,QAAI,SAAS,IAAI,WAAW;AAC5B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,YAAY;AACd,eAAS,OAAO,IAAI,mBAAmB,EAAE;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAa;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,aAAY;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,WAAW,MAAM,MAAM,eAAe,MAAM;AAC1C,QAAI,MAAM,KAAK,QAAQ;AACrB,YAAM;AAAA,IACR;AACA,UAAM,YAAY,KAAK,OAAO,OAAK,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,OAAO,OAAK,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,OAAO,OAAK,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,MAAM,IAAI;AACvL,QAAI,UAAU,WAAW,KAAK,cAAc;AAC1C,YAAM;AAAA,IACR;AACA,QAAI,UAAU,SAAS,MAAM,SAAS,QAAQ,WAAc,OAAO;AACjE,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,eAAe,MAAM;AAC5B,SAAO,aAAa,OAAO,OAAO;AACpC;AACA,IAAM,mCAAmC;AAAA,EACvC,MAAM,eAAe,sBAAsB;AAAA,EAC3C,SAAS;AACX;AACA,IAAM,kCAAkC;AAAA,EACtC,MAAM,eAAe,qBAAqB;AAAA,EAC1C,SAAS;AACX;AACA,IAAM,uCAAuC;AAAA,EAC3C,MAAM,eAAe,0BAA0B;AAAA,EAC/C,SAAS;AACX;AACA,IAAM,iBAAiB;AACvB,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA,EACA,uBAAuB,aAAa;AAClC,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa,KAAK,GAAG;AAC7D,aAAO,IAAI,MAAK,oBAAI,KAAK,GAAE,YAAY,CAAC;AAAA,IAC1C;AACA,UAAM,OAAO,oBAAI,KAAK,CAAC;AACvB,SAAK,cAAc,YAAY,GAAG;AAClC,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB,OAAO,SAAS,eAAe;AACtD,QAAI,CAAC,KAAK,aAAa,OAAO,aAAa,GAAG;AAC5C,aAAO;AAAA,IACT;AACA,UAAM,SAAS,KAAK,mBAAmB,OAAO,SAAS,aAAa;AACpE,UAAM,UAAU,KAAK,oBAAoB,OAAO,SAAS,aAAa;AACtE,WAAO,CAAC,QAAQ,OAAO,EAAE,KAAK,GAAG;AAAA,EACnC;AAAA,EACA,mBAAmB,OAAO,SAAS,eAAe;AAChD,QAAI,CAAC,KAAK,aAAa,OAAO,aAAa,GAAG;AAC5C,aAAO,CAAC;AAAA,IACV;AACA,WAAO,KAAK,eAAe,OAAO,GAAG,OAAO;AAAA,EAC9C;AAAA,EACA,oBAAoB,OAAO,SAAS,eAAe;AACjD,QAAI,CAAC,eAAe;AAClB,aAAO,CAAC;AAAA,IACV;AACA,QAAI,CAAC,KAAK,aAAa,OAAO,aAAa,GAAG;AAC5C,aAAO,CAAC;AAAA,IACV;AACA,WAAO,KAAK,eAAe,OAAO,GAAG,OAAO;AAAA,EAC9C;AAAA,EACA,sBAAsB,OAAO,SAAS,eAAe;AACnD,QAAI,CAAC,KAAK,aAAa,OAAO,aAAa,GAAG;AAC5C,aAAO,CAAC;AAAA,IACV;AACA,WAAO,KAAK,eAAe,OAAO,GAAG,OAAO;AAAA,EAC9C;AAAA,EACA,eAAe,OAAO,OAAO,SAAS;AACpC,UAAM,cAAc,KAAK,mBAAmB,OAAO,KAAK;AACxD,QAAI,SAAS;AACX,aAAO;AAAA,IACT;AACA,UAAM,SAAS,KAAK,gBAAgB,WAAW;AAC/C,WAAO,KAAK,MAAM,MAAM;AAAA,EAC1B;AAAA,EACA,gBAAgB,KAAK;AACnB,QAAI,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACrD,YAAQ,OAAO,SAAS,GAAG;AAAA,MACzB,KAAK;AACH;AAAA,MACF,KAAK;AACH,kBAAU;AACV;AAAA,MACF,KAAK;AACH,kBAAU;AACV;AAAA,MACF;AACE,cAAM,MAAM,2BAA2B;AAAA,IAC3C;AACA,UAAM,UAAU,OAAO,KAAK,SAAS,gBAAgB,cAAc,KAAK,SAAS,aAAa,KAAK,MAAM,IAAI,OAAO,KAAK,QAAQ,QAAQ,EAAE,SAAS,QAAQ;AAC5J,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AACA,QAAI;AAEF,aAAO,mBAAmB,QAAQ,MAAM,EAAE,EAAE,IAAI,OAAK,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;AAAA,IACtH,SAAS,KAAK;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,aAAa,OAAO,eAAe;AACjC,QAAI,CAAC,OAAO;AACV,WAAK,cAAc,SAAS,eAAe,UAAU,KAAK,gCAAgC;AAC1F,aAAO;AAAA,IACT;AACA,QAAI,CAAC,MAAM,SAAS,GAAG,GAAG;AACxB,WAAK,cAAc,SAAS,eAAe,UAAU,KAAK,qCAAqC;AAC/F,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,QAAI,MAAM,WAAW,gBAAgB;AACnC,WAAK,cAAc,SAAS,eAAe,UAAU,KAAK,gDAAgD,iBAAiB,CAAC,OAAO;AACnI,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,OAAO,OAAO;AAC/B,WAAO,MAAM,MAAM,GAAG,EAAE,KAAK;AAAA,EAC/B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AAAA,EAC3C;AAAA,EACA,sBAAsB,KAAK,WAAW;AACpC,WAAO,KAAK,cAAc,UAAU,EAAE,OAAO,UAAU,OAAO,KAAK,WAAW,OAAO,CAAC,QAAQ,CAAC;AAAA,EACjG;AAAA,EACA,UAAU,iBAAiB,WAAW,WAAW,cAAc;AAC7D,WAAO,KAAK,cAAc,UAAU,EAAE,OAAO,OAAO,iBAAiB,WAAW,WAAW,IAAI,YAAY,EAAE,OAAO,YAAY,CAAC;AAAA,EACnI;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,aAAa,KAAK;AACzB,UAAQ,IAAI,OAAO,CAAC,GAAG;AAAA,IACrB,KAAK;AACH,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF,KAAK;AACH,UAAI,IAAI,SAAS,KAAK,GAAG;AACvB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF,WAAW,IAAI,SAAS,KAAK,GAAG;AAC9B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACE,aAAO;AAAA,EACX;AACF;AACA,SAAS,QAAQ,KAAK;AACpB,UAAQ,IAAI,OAAO,CAAC,GAAG;AAAA,IACrB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,YAAM,IAAI,MAAM,gCAAgC,GAAG;AAAA,EACvD;AACF;AACA,SAAS,aAAa,KAAK;AACzB,UAAQ,IAAI,OAAO,CAAC,GAAG;AAAA,IACrB,KAAK;AACH,UAAI,IAAI,SAAS,KAAK,GAAG;AACvB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF,WAAW,IAAI,SAAS,KAAK,GAAG;AAC9B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF,WAAW,IAAI,SAAS,KAAK,GAAG;AAC9B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,KAAK;AACH,UAAI,IAAI,SAAS,KAAK,GAAG;AACvB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,YAAY;AAAA,QACd;AAAA,MACF,WAAW,IAAI,SAAS,KAAK,GAAG;AAC9B,eAAO;AAAA,UACL,MAAM;AAAA,UACN,YAAY;AAAA,QACd;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AACE,aAAO;AAAA,EACX;AACF;AA2CA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,gBAAgB,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AACvH,SAAK,qBAAqB,OAAO,kBAAkB;AACnD,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,yBAAyB,OAAO,sBAAsB;AAC3D,SAAK,yBAAyB,OAAO,sBAAsB;AAAA,EAC7D;AAAA,EACA,OAAO;AACL,SAAK,+BAA+B;AAAA,EACtC;AAAA;AAAA;AAAA,EAGA,kBAAkB,OAAO,eAAe,eAAe;AACrD,UAAM,UAAU,KAAK,mBAAmB,oBAAoB,OAAO,OAAO,aAAa;AACvF,WAAO,CAAC,KAAK,6BAA6B,SAAS,eAAe,aAAa;AAAA,EACjF;AAAA;AAAA;AAAA,EAGA,6BAA6B,gBAAgB,eAAe,eAAe;AACzE,UAAM,sBAAsB,KAAK,mBAAmB,uBAAuB,cAAc;AACzF,oBAAgB,iBAAiB;AACjC,QAAI,CAAC,qBAAqB;AACxB,aAAO;AAAA,IACT;AACA,UAAM,uBAAuB,oBAAoB,QAAQ;AACzD,UAAM,gBAAgB,KAAK,uBAAuB,aAAa;AAC/D,UAAM,kBAAkB,uBAAuB;AAC/C,SAAK,cAAc,SAAS,eAAe,wBAAwB,CAAC,eAAe,mBAAmB,KAAK,0BAA0B,uBAAuB,aAAa,CAAC,MAAM,IAAI,KAAK,oBAAoB,EAAE,mBAAmB,CAAC,MAAM,IAAI,KAAK,aAAa,EAAE,mBAAmB,CAAC,EAAE;AACvR,WAAO;AAAA,EACT;AAAA,EACA,8BAA8B,sBAAsB,eAAe,eAAe;AAEhF,QAAI,CAAC,sBAAsB;AACzB,aAAO;AAAA,IACT;AACA,oBAAgB,iBAAiB;AACjC,UAAM,6BAA6B,qBAAqB,QAAQ;AAChE,UAAM,gBAAgB,KAAK,uBAAuB,aAAa;AAC/D,UAAM,kBAAkB,6BAA6B;AACrD,SAAK,cAAc,SAAS,eAAe,4BAA4B,CAAC,eAAe,mBAAmB,KAAK,0BAA0B,6BAA6B,aAAa,CAAC,MAAM,IAAI,KAAK,0BAA0B,EAAE,mBAAmB,CAAC,MAAM,IAAI,KAAK,aAAa,EAAE,mBAAmB,CAAC,EAAE;AACvS,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,wBAAwB,aAAa,eAAe;AAClD,QAAI,YAAY;AAChB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa,KAAK,GAAG;AAC7D,kBAAY;AACZ,WAAK,cAAc,WAAW,eAAe,kDAAkD;AAAA,IACjG;AACA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa,KAAK,GAAG;AAC7D,kBAAY;AACZ,WAAK,cAAc,WAAW,eAAe,kDAAkD;AAAA,IACjG;AACA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa,KAAK,GAAG;AAC7D,kBAAY;AACZ,WAAK,cAAc,WAAW,eAAe,kDAAkD;AAAA,IACjG;AACA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa,KAAK,GAAG;AAC7D,kBAAY;AACZ,WAAK,cAAc,WAAW,eAAe,kDAAkD;AAAA,IACjG;AACA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa,KAAK,GAAG;AAC7D,kBAAY;AACZ,WAAK,cAAc,WAAW,eAAe,kDAAkD;AAAA,IACjG;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,4BAA4B,aAAa,2BAA2B,4BAA4B,eAAe;AAC7G,QAAI,4BAA4B;AAC9B,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,aAAa,KAAK,GAAG;AAC7D,aAAO;AAAA,IACT;AACA,UAAM,qBAAqB,oBAAI,KAAK,CAAC;AACrC,uBAAmB,cAAc,YAAY,GAAG;AAChD,gCAA4B,6BAA6B;AACzD,UAAM,WAAW,IAAI,MAAK,oBAAI,KAAK,GAAE,YAAY,CAAC;AAClD,UAAM,OAAO,SAAS,QAAQ,IAAI,mBAAmB,QAAQ;AAC7D,UAAM,iCAAiC,4BAA4B;AACnE,SAAK,cAAc,SAAS,eAAe,oCAAoC,IAAI,MAAM,8BAA8B,EAAE;AACzH,QAAI,OAAO,GAAG;AACZ,aAAO,OAAO;AAAA,IAChB;AACA,WAAO,CAAC,OAAO;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB,aAAa,YAAY,yBAAyB,eAAe;AACpF,UAAM,sBAAsB,YAAY,UAAU,UAAa,4BAA4B,eAAe,wBAAuB;AACjI,QAAI,CAAC,sBAAsB,YAAY,UAAU,YAAY;AAC3D,WAAK,cAAc,SAAS,eAAe,wDAAwD,YAAY,QAAQ,kBAAkB,UAAU;AACnJ,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,mBAAmB,aAAa,8BAA8B,eAAe;AAC3E,QAAI,YAAY,QAAQ,8BAA8B;AACpD,WAAK,cAAc,SAAS,eAAe,oDAAoD,YAAY,MAAM,oCAAoC,4BAA4B;AACjL,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,aAAa,KAAK,eAAe;AAClD,QAAI,MAAM,QAAQ,YAAY,GAAG,GAAG;AAClC,YAAM,SAAS,YAAY,IAAI,SAAS,GAAG;AAC3C,UAAI,CAAC,QAAQ;AACX,aAAK,cAAc,SAAS,eAAe,0DAA0D,YAAY,MAAM,gBAAgB,GAAG;AAC1I,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,WAAW,YAAY,QAAQ,KAAK;AAClC,WAAK,cAAc,SAAS,eAAe,oDAAoD,YAAY,MAAM,gBAAgB,GAAG;AACpI,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,yCAAyC,aAAa;AACpD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,WAAO,EAAE,MAAM,QAAQ,YAAY,GAAG,KAAK,YAAY,IAAI,SAAS,KAAK,CAAC,YAAY;AAAA,EACxF;AAAA;AAAA,EAEA,wBAAwB,aAAa,UAAU;AAC7C,QAAI,CAAC,aAAa,KAAK;AACrB,aAAO;AAAA,IACT;AACA,WAAO,YAAY,QAAQ;AAAA,EAC7B;AAAA,EACA,8BAA8B,OAAO,YAAY,eAAe;AAC9D,QAAI,UAAU,YAAY;AACxB,WAAK,cAAc,SAAS,eAAe,kDAAkD,QAAQ,kBAAkB,UAAU;AACjI,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,SAAS,SAAS,eAAe;AACxD,QAAI,CAAC,SAAS;AACZ,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,QAAI,CAAC,WAAW,CAAC,QAAQ,MAAM;AAC7B,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,UAAM,aAAa,KAAK,mBAAmB,mBAAmB,SAAS,OAAO,aAAa;AAC3F,QAAI,OAAO,KAAK,UAAU,EAAE,WAAW,KAAK,WAAW,gBAAgB,QAAQ;AAC7E,WAAK,cAAc,WAAW,eAAe,6BAA6B;AAC1E,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,UAAM,MAAM,WAAW;AACvB,UAAM,MAAM,WAAW;AACvB,UAAM,OAAO,QAAQ;AACrB,QAAI;AACJ,QAAI;AACJ,QAAI,CAAC,KAAK,cAAc,SAAS,GAAG,GAAG;AACrC,WAAK,cAAc,WAAW,eAAe,qBAAqB,GAAG;AACrE,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,UAAM,MAAM,QAAQ,GAAG;AACvB,UAAM,MAAM;AACZ,QAAI;AACF,kBAAY,MAAM,KAAK,aAAa,WAAW,MAAM;AAAA,QACnD;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,KAAK,IAAI,KAAK,aAAa,WAAW,MAAM;AAAA,QAC7C;AAAA,QACA;AAAA,MACF,GAAG,KAAK;AACR,UAAI,UAAU,WAAW,GAAG;AAC1B,oBAAY,MAAM,KAAK,aAAa,WAAW,MAAM;AAAA,UACnD;AAAA,UACA;AAAA,QACF,CAAC,IAAI,KAAK,aAAa,WAAW,MAAM;AAAA,UACtC;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,UAAU,CAAC;AAAA,IACnB,SAAS,GAAG;AACV,WAAK,cAAc,SAAS,eAAe,CAAC;AAC5C,aAAO,GAAG,KAAK;AAAA,IACjB;AACA,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,eAAe,KAAK,mBAAmB,yBAAyB,SAAS,MAAM,aAAa;AAClG,UAAM,eAAe,KAAK,mBAAmB,sBAAsB,SAAS,MAAM,aAAa;AAC/F,WAAO,KAAK,KAAK,uBAAuB,sBAAsB,KAAK,SAAS,CAAC,EAAE,KAAK,SAAS,eAAa;AACxG,YAAM,YAAY,UAAU,MAAM,cAAc;AAAA,QAC9C,OAAO;AAAA,MACT,CAAC;AACD,YAAM,kBAAkB,aAAa,GAAG;AACxC,aAAO,KAAK,KAAK,uBAAuB,UAAU,iBAAiB,WAAW,WAAW,YAAY,CAAC;AAAA,IACxG,CAAC,GAAG,IAAI,aAAW;AACjB,UAAI,CAAC,SAAS;AACZ,aAAK,cAAc,WAAW,eAAe,qDAAqD;AAAA,MACpG;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,sBAAsB,aAAa,QAAQ,YAAY,eAAe;AACpE,SAAK,cAAc,SAAS,eAAe,6BAA6B,MAAM;AAE9E,QAAI,MAAM;AACV,QAAI,WAAW,SAAS,KAAK,GAAG;AAC9B,YAAM;AAAA,IACR,WAAW,WAAW,SAAS,KAAK,GAAG;AACrC,YAAM;AAAA,IACR;AACA,WAAO,KAAK,uBAAuB,eAAe,KAAK,aAAa,GAAG,EAAE,KAAK,SAAS,UAAQ;AAC7F,WAAK,cAAc,SAAS,eAAe,2CAA2C,IAAI;AAC1F,UAAI,SAAS,QAAQ;AACnB,eAAO,GAAG,IAAI;AAAA,MAChB,OAAO;AACL,eAAO,KAAK,uBAAuB,eAAe,KAAK,mBAAmB,WAAW,GAAG,GAAG,EAAE,KAAK,IAAI,aAAW;AAC/G,eAAK,cAAc,SAAS,eAAe,kBAAkB,IAAI;AACjE,iBAAO,YAAY;AAAA,QACrB,CAAC,CAAC;AAAA,MACJ;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,0BAA0B,QAAQ;AAChC,UAAM,UAAU,KAAK,MAAM,SAAS,GAAK;AACzC,UAAM,WAAW,SAAS,MAAQ,KAAM,QAAQ,CAAC;AACjD,WAAO,UAAU,OAAO,CAAC,UAAU,KAAK,MAAM,MAAM;AAAA,EACtD;AAAA,EACA,uBAAuB,eAAe;AACpC,WAAO,IAAI,MAAK,oBAAI,KAAK,GAAE,YAAY,CAAC,EAAE,QAAQ,IAAI,gBAAgB;AAAA,EACxE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AASH,IAAM,iBAAiB,WAAS,CAAC,CAAC,SAAS,iBAAiB,sBAAsB,MAAM,iBAAiB,iBAAiB,MAAM,MAAM,SAAS,WAAW,MAAM,WAAW,KAAK,CAAC,CAAC,MAAM;AACxL,IAAM,iCAAN,MAAM,gCAA+B;AAAA,EACnC,cAAc;AACZ,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,yBAAyB,OAAO,sBAAsB;AAC3D,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,cAAc,OAAO,WAAW;AAAA,EACvC;AAAA;AAAA,EAEA,iBAAiB,YAAY,QAAQ;AACnC,UAAM,OAAO,KAAK,WAAW,gBAAgB,YAAY,MAAM;AAC/D,UAAM,QAAQ,KAAK,WAAW,gBAAgB,YAAY,OAAO;AACjE,UAAM,eAAe,KAAK,WAAW,gBAAgB,YAAY,eAAe;AAChF,QAAI,CAAC,OAAO;AACV,WAAK,cAAc,SAAS,QAAQ,iBAAiB;AACrD,aAAO,WAAW,MAAM,IAAI,MAAM,iBAAiB,CAAC;AAAA,IACtD;AACA,QAAI,CAAC,MAAM;AACT,WAAK,cAAc,SAAS,QAAQ,gBAAgB;AACpD,aAAO,WAAW,MAAM,IAAI,MAAM,gBAAgB,CAAC;AAAA,IACrD;AACA,SAAK,cAAc,SAAS,QAAQ,mCAAmC,UAAU;AACjF,UAAM,yBAAyB;AAAA,MAC7B;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,IACnB;AACA,WAAO,GAAG,sBAAsB;AAAA,EAClC;AAAA;AAAA,EAEA,oBAAoB,iBAAiB,QAAQ;AAC3C,UAAM,mBAAmB,KAAK,iBAAiB,oBAAoB,MAAM;AACzE,UAAM,iBAAiB,KAAK,uBAAuB,8BAA8B,gBAAgB,OAAO,kBAAkB,MAAM;AAChI,QAAI,CAAC,gBAAgB;AACnB,aAAO,WAAW,MAAM,IAAI,MAAM,qCAAqC,CAAC;AAAA,IAC1E;AACA,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,MAAM;AACnG,UAAM,gBAAgB,wBAAwB;AAC9C,QAAI,CAAC,eAAe;AAClB,aAAO,WAAW,MAAM,IAAI,MAAM,4BAA4B,CAAC;AAAA,IACjE;AACA,QAAI,UAAU,IAAI,YAAY;AAC9B,cAAU,QAAQ,IAAI,gBAAgB,mCAAmC;AACzE,UAAM,kBAAkB,KAAK,WAAW,iCAAiC,gBAAgB,MAAM,QAAQ,QAAQ,uBAAuB;AACtI,WAAO,KAAK,YAAY,KAAK,eAAe,iBAAiB,QAAQ,OAAO,EAAE,KAAK,UAAU,cAAY;AACvG,UAAI,UAAU;AACZ,cAAM,aAAa,iCACd,WADc;AAAA,UAEjB,OAAO,gBAAgB;AAAA,UACvB,eAAe,gBAAgB;AAAA,QACjC;AACA,wBAAgB,aAAa;AAAA,MAC/B;AACA,aAAO,GAAG,eAAe;AAAA,IAC3B,CAAC,GAAG,UAAU,WAAS,KAAK,mBAAmB,OAAO,MAAM,CAAC,GAAG,WAAW,WAAS;AAClF,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,eAAe,4BAA4B,SAAS;AAC1D,WAAK,cAAc,SAAS,QAAQ,cAAc,KAAK;AACvD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,mBAAmB,QAAQ,QAAQ;AACjC,WAAO,OAAO,KAAK,SAAS,WAAS;AAEnC,UAAI,eAAe,KAAK,GAAG;AACzB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,4BAA4B,SAAS;AAC1D,aAAK,cAAc,WAAW,QAAQ,cAAc,KAAK;AACzD,eAAO,OAAO,8BAA8B,KAAK,GAAI;AAAA,MACvD;AACA,aAAO,WAAW,MAAM,KAAK;AAAA,IAC/B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uCAAuC,mBAAmB;AAC7E,aAAO,KAAK,qBAAqB,iCAAgC;AAAA,IACnE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gCAA+B;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gCAAgC,CAAC;AAAA,IACvG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI;AAAA,CACH,SAAUC,aAAY;AAIrB,EAAAA,YAAWA,YAAW,cAAc,IAAI,CAAC,IAAI;AAC7C,EAAAA,YAAWA,YAAW,cAAc,IAAI,CAAC,IAAI;AAC7C,EAAAA,YAAWA,YAAW,sBAAsB,IAAI,CAAC,IAAI;AACrD,EAAAA,YAAWA,YAAW,+BAA+B,IAAI,CAAC,IAAI;AAC9D,EAAAA,YAAWA,YAAW,qBAAqB,IAAI,CAAC,IAAI;AACpD,EAAAA,YAAWA,YAAW,sBAAsB,IAAI,CAAC,IAAI;AACrD,EAAAA,YAAWA,YAAW,iBAAiB,IAAI,CAAC,IAAI;AAChD,EAAAA,YAAWA,YAAW,yBAAyB,IAAI,CAAC,IAAI;AACxD,EAAAA,YAAWA,YAAW,cAAc,IAAI,CAAC,IAAI;AAC7C,EAAAA,YAAWA,YAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,EAAAA,YAAWA,YAAW,oBAAoB,IAAI,EAAE,IAAI;AACpD,EAAAA,YAAWA,YAAW,mBAAmB,IAAI,EAAE,IAAI;AACrD,GAAG,eAAe,aAAa,CAAC,EAAE;AAClC,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,SAAS,IAAI,cAAc,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,MAAM,OAAO;AACrB,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,WAAO,KAAK,OAAO,aAAa;AAAA,EAClC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qBAAqB;AAAA,EACzB,iBAAiB;AAAA,EACjB,yBAAyB,CAAC;AAC5B;AACA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,yBAAyB,OAAO,sBAAsB;AAC3D,SAAK,yBAAyB,IAAI,gBAAgB,kBAAkB;AAAA,EACtE;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,uBAAuB,aAAa,EAAE,KAAK,qBAAqB,CAAC;AAAA,EAC/E;AAAA,EACA,6BAA6B,YAAY;AACvC,UAAM,SAAS,KAAK,2BAA2B,UAAU;AACzD,SAAK,uBAAuB,KAAK,MAAM;AAAA,EACzC;AAAA,EACA,+BAA+B,eAAe,YAAY;AACxD,SAAK,0BAA0B,wBAAwB,aAAa;AACpE,UAAM,SAAS,KAAK,6BAA6B,UAAU;AAC3D,SAAK,uBAAuB,KAAK,MAAM;AAAA,EACzC;AAAA,EACA,0BAA0B,sBAAsB;AAC9C,SAAK,oBAAoB,UAAU,WAAW,yBAAyB,oBAAoB;AAAA,EAC7F;AAAA,EACA,qBAAqB,aAAa,YAAY,eAAe,YAAY;AACvE,SAAK,cAAc,SAAS,eAAe,4BAA4B,WAAW,GAAG;AACrF,SAAK,0BAA0B,MAAM,aAAa,aAAa,aAAa;AAC5E,SAAK,iCAAiC,YAAY,aAAa;AAC/D,SAAK,6BAA6B,UAAU;AAAA,EAC9C;AAAA,EACA,eAAe,eAAe;AAC5B,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,gBAAgB,aAAa,GAAG;AACxC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,0BAA0B,eAAe,aAAa;AACzE,WAAO,KAAK,yBAAyB,KAAK;AAAA,EAC5C;AAAA,EACA,WAAW,eAAe;AACxB,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,gBAAgB,aAAa,GAAG;AACxC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,0BAA0B,WAAW,aAAa;AACrE,WAAO,KAAK,yBAAyB,KAAK;AAAA,EAC5C;AAAA,EACA,gBAAgB,eAAe;AAC7B,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,gBAAgB,aAAa,GAAG;AACxC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,KAAK,0BAA0B,gBAAgB,aAAa;AAC1E,WAAO,KAAK,yBAAyB,KAAK;AAAA,EAC5C;AAAA,EACA,wBAAwB,eAAe;AACrC,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,gBAAgB,aAAa,GAAG;AACxC,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,wBAAwB,aAAa;AAAA,EAC7E;AAAA,EACA,0BAA0B,eAAe;AACvC,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,gBAAgB,aAAa,GAAG;AACxC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,wCAAwC,aAAa,GAAG;AAC/D,WAAK,cAAc,SAAS,eAAe,8BAA8B;AACzE,aAAO;AAAA,IACT;AACA,QAAI,KAAK,oCAAoC,aAAa,GAAG;AAC3D,WAAK,cAAc,SAAS,eAAe,kCAAkC;AAC7E,aAAO;AAAA,IACT;AACA,SAAK,cAAc,SAAS,eAAe,6CAA6C;AACxF,WAAO;AAAA,EACT;AAAA,EACA,wCAAwC,eAAe;AACrD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,oCAAoC,0BAA0B;AACjE,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,0BAA0B,WAAW,aAAa;AAC5E,UAAM,iBAAiB,KAAK,uBAAuB,kBAAkB,cAAc,eAAe,oCAAoC;AACtI,QAAI,gBAAgB;AAClB,WAAK,oBAAoB,UAAU,WAAW,gBAAgB,cAAc;AAAA,IAC9E;AACA,WAAO;AAAA,EACT;AAAA,EACA,oCAAoC,eAAe;AACjD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,uBAAuB,KAAK,0BAA0B,KAAK,2BAA2B,aAAa;AACzG,UAAM,2BAA2B,KAAK,uBAAuB,8BAA8B,sBAAsB,eAAe,oCAAoC;AACpK,UAAM,aAAa,CAAC;AACpB,QAAI,YAAY;AACd,WAAK,oBAAoB,UAAU,WAAW,cAAc,UAAU;AAAA,IACxE;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,eAAe;AAC7B,QAAI,CAAC,eAAe;AAClB,iBAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AACzF,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,CAAC,CAAC,KAAK,0BAA0B,eAAe,aAAa;AACpF,UAAM,aAAa,CAAC,CAAC,KAAK,0BAA0B,WAAW,aAAa;AAC5E,WAAO,kBAAkB;AAAA,EAC3B;AAAA,EACA,yBAAyB,OAAO;AAC9B,QAAI,OAAO;AACT,aAAO,mBAAmB,KAAK;AAAA,IACjC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,iCAAiC,YAAY,eAAe;AAC1D,QAAI,YAAY,YAAY;AAC1B,YAAM,wBAAwB,IAAI,MAAK,oBAAI,KAAK,GAAE,YAAY,CAAC,EAAE,QAAQ,IAAI,WAAW,aAAa;AACrG,WAAK,0BAA0B,MAAM,2BAA2B,uBAAuB,aAAa;AAAA,IACtG;AAAA,EACF;AAAA,EACA,2BAA2B,YAAY;AACrC,QAAI,WAAW,WAAW,GAAG;AAC3B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,WAAW,CAAC;AAChB,aAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,yBAAyB,CAAC;AAAA,UACxB,UAAU,YAAY;AAAA,UACtB,iBAAiB;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,KAAK,sCAAsC,UAAU;AAAA,EAC9D;AAAA,EACA,6BAA6B,YAAY;AACvC,QAAI,WAAW,WAAW,GAAG;AAC3B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,WAAW,CAAC;AAChB,aAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,yBAAyB,CAAC;AAAA,UACxB,UAAU,YAAY;AAAA,UACtB,iBAAiB;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,KAAK,sCAAsC,UAAU;AAAA,EAC9D;AAAA,EACA,sCAAsC,YAAY;AAChD,UAAM,0BAA0B,WAAW,IAAI,aAAW;AAAA,MACxD,UAAU,OAAO,YAAY;AAAA,MAC7B,iBAAiB,KAAK,gBAAgB,MAAM;AAAA,IAC9C,EAAE;AACF,UAAM,kBAAkB,wBAAwB,MAAM,OAAK,CAAC,CAAC,EAAE,eAAe;AAC9E,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiB,QAAQ,IAAI;AAC7B,EAAAA,kBAAiB,kBAAkB,IAAI;AACvC,EAAAA,kBAAiB,iBAAiB,IAAI;AACtC,EAAAA,kBAAiB,gBAAgB,IAAI;AACrC,EAAAA,kBAAiB,yBAAyB,IAAI;AAC9C,EAAAA,kBAAiB,kBAAkB,IAAI;AACvC,EAAAA,kBAAiB,uBAAuB,IAAI;AAC5C,EAAAA,kBAAiB,0BAA0B,IAAI;AAC/C,EAAAA,kBAAiB,cAAc,IAAI;AACnC,EAAAA,kBAAiB,oCAAoC,IAAI;AACzD,EAAAA,kBAAiB,cAAc,IAAI;AACnC,EAAAA,kBAAiB,cAAc,IAAI;AACnC,EAAAA,kBAAiB,iBAAiB,IAAI;AACtC,EAAAA,kBAAiB,IAAI,IAAI;AACzB,EAAAA,kBAAiB,eAAe,IAAI;AACpC,EAAAA,kBAAiB,wBAAwB,IAAI;AAC/C,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAM,qBAAqB;AAAA,EACzB,UAAU;AAAA,EACV,aAAa,CAAC;AAChB;AACA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc;AACZ,SAAK,oBAAoB,IAAI,gBAAgB,kBAAkB;AAC/D,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,qBAAqB,OAAO,kBAAkB;AACnD,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,kBAAkB,OAAO,WAAW;AACzC,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,eAAe,OAAO,mBAAmB;AAAA,EAChD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,kBAAkB,aAAa;AAAA,EAC7C;AAAA,EACA,6BAA6B,sBAAsB,YAAY,iBAAiB,OAAO,SAAS,gBAAgB;AAC9G,cAAU,WAAW,KAAK,0BAA0B,WAAW,oBAAoB;AACnF,qBAAiB,kBAAkB,KAAK,mBAAmB,oBAAoB,SAAS,OAAO,oBAAoB;AACnH,UAAM,8BAA8B,KAAK,qBAAqB,oBAAoB;AAClF,UAAM,eAAe,CAAC,CAAC;AACvB,UAAM,2CAA2C,KAAK,WAAW,yCAAyC,oBAAoB;AAC9H,UAAM,wBAAwB,KAAK,WAAW,sBAAsB,oBAAoB;AACxF,UAAM,cAAc,KAAK,0BAA0B,eAAe,oBAAoB;AACtF,QAAI,EAAE,4CAA4C,wBAAwB;AACxE,WAAK,cAAc,SAAS,sBAAsB,8CAA8C,WAAW,EAAE;AAC7G,WAAK,mBAAmB,gBAAgB,sBAAsB,UAAU;AACxE,aAAO,GAAG,cAAc;AAAA,IAC1B;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,kBAAkB,gCAAgC,CAAC,cAAc;AACpE,aAAO,KAAK,2BAA2B,eAAe,KAAK,sBAAsB,UAAU,EAAE,KAAK,UAAU,cAAY;AACtH,aAAK,cAAc,SAAS,sBAAsB,wBAAwB,QAAQ;AAClF,YAAI,CAAC,CAAC,UAAU;AACd,eAAK,cAAc,SAAS,sBAAsB,iBAAiB,WAAW;AAC9E,iBAAO,GAAG,QAAQ;AAAA,QACpB,OAAO;AACL,iBAAO,WAAW,MAAM,IAAI,MAAM,uCAAuC,CAAC;AAAA,QAC5E;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,WAAO,GAAG,2BAA2B;AAAA,EACvC;AAAA,EACA,qBAAqB,sBAAsB;AACzC,QAAI,CAAC,sBAAsB;AACzB,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,WAAO,KAAK,0BAA0B,KAAK,YAAY,oBAAoB,KAAK;AAAA,EAClF;AAAA,EACA,wBAAwB,sBAAsB,YAAY;AACxD,UAAM,WAAW,KAAK,qBAAqB,oBAAoB;AAC/D,QAAI,UAAU;AACZ,WAAK,kBAAkB,sBAAsB,YAAY,QAAQ;AAAA,IACnE;AAAA,EACF;AAAA,EACA,mBAAmB,UAAU,sBAAsB,YAAY;AAC7D,SAAK,0BAA0B,MAAM,YAAY,UAAU,oBAAoB;AAC/E,SAAK,kBAAkB,sBAAsB,YAAY,QAAQ;AAAA,EACnE;AAAA,EACA,qBAAqB,sBAAsB,YAAY;AACrD,SAAK,0BAA0B,OAAO,YAAY,oBAAoB;AACtE,SAAK,kBAAkB,sBAAsB,YAAY,IAAI;AAAA,EAC/D;AAAA,EACA,2BAA2B,YAAY,sBAAsB,YAAY;AACvE,WAAO,KAAK,oBAAoB,oBAAoB,EAAE,KAAK,IAAI,UAAQ;AACrE,UAAI,KAAK,2BAA2B,sBAAsB,YAAY,MAAM,GAAG,GAAG;AAChF,aAAK,mBAAmB,MAAM,sBAAsB,UAAU;AAC9D,eAAO;AAAA,MACT,OAAO;AAEL,aAAK,cAAc,WAAW,sBAAsB,yDAAyD;AAC7G,aAAK,qBAAqB,sBAAsB,UAAU;AAC1D,eAAO;AAAA,MACT;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oBAAoB,sBAAsB;AACxC,UAAM,QAAQ,KAAK,0BAA0B,eAAe,oBAAoB;AAChF,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,oBAAoB;AACjH,QAAI,CAAC,wBAAwB;AAC3B,WAAK,cAAc,WAAW,sBAAsB,yDAAyD;AAC7G,aAAO,WAAW,MAAM,IAAI,MAAM,qCAAqC,CAAC;AAAA,IAC1E;AACA,UAAM,mBAAmB,uBAAuB;AAChD,QAAI,CAAC,kBAAkB;AACrB,WAAK,cAAc,SAAS,sBAAsB,gHAAgH;AAClK,aAAO,WAAW,MAAM,IAAI,MAAM,uDAAuD,CAAC;AAAA,IAC5F;AACA,WAAO,KAAK,gBAAgB,IAAI,kBAAkB,sBAAsB,KAAK,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EAC9F;AAAA,EACA,2BAA2B,sBAAsB,YAAY,aAAa;AACxE,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,SAAS,MAAM,YAAY,SAAS,GAAG;AACpD,WAAK,cAAc,SAAS,sBAAsB,qCAAqC,YAAY,WAAW;AAC9G,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,sBAAsB,YAAY,gBAAgB;AAClE,UAAM,WAAW,KAAK,sCAAsC,sBAAsB,YAAY,cAAc;AAC5G,SAAK,kBAAkB,KAAK,QAAQ;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,aAAa,UAAU,WAAW,iBAAiB;AAAA,MACtD;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,sCAAsC,sBAAsB,YAAY,gBAAgB;AACtF,UAAM,iBAAiB,WAAW,SAAS;AAC3C,QAAI,CAAC,gBAAgB;AACnB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,aAAO,KAAK,4BAA4B,YAAY,IAAI,cAAc;AAAA,IACxE;AACA,UAAM,cAAc,WAAW,IAAI,YAAU;AAC3C,YAAM,kBAAkB,qBAAqB,YAAY;AACzD,YAAM,WAAW,OAAO,YAAY;AACpC,UAAI,KAAK,wBAAwB,iBAAiB,MAAM,GAAG;AACzD,eAAO;AAAA,UACL;AAAA,UACA,UAAU;AAAA,QACZ;AAAA,MACF;AACA,YAAM,uBAAuB,KAAK,0BAA0B,KAAK,YAAY,MAAM,KAAK;AACxF,aAAO;AAAA,QACL;AAAA,QACA,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,UAAU;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA,EACA,4BAA4B,UAAU,UAAU;AAC9C,WAAO;AAAA,MACL;AAAA,MACA,aAAa,CAAC;AAAA,QACZ;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,wBAAwB,UAAU,QAAQ;AACxC,WAAO,OAAO,aAAa;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAa;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,aAAY;AAAA,MACrB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAAA,EACjD;AAAA,EACA,uBAAuB,sBAAsB,YAAY;AACvD,QAAI,CAAC,sBAAsB;AACzB;AAAA,IACF;AACA,SAAK,YAAY,qBAAqB,sBAAsB,UAAU;AACtE,SAAK,iBAAiB,qBAAqB,oBAAoB;AAC/D,SAAK,iBAAiB,+BAA+B,sBAAsB,UAAU;AACrF,SAAK,cAAc,SAAS,sBAAsB,oDAAoD;AAAA,EACxG;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,cAAc,OAAO,WAAW;AAAA,EACvC;AAAA,EACA,eAAe,sBAAsB;AACnC,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,oBAAoB;AACjH,UAAM,UAAU,wBAAwB;AACxC,QAAI,CAAC,SAAS;AACZ,YAAM,QAAQ,uDAAuD,OAAO;AAC5E,WAAK,cAAc,WAAW,sBAAsB,KAAK;AACzD,aAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,IAC1C;AACA,SAAK,cAAc,SAAS,sBAAsB,4BAA4B,OAAO;AACrF,WAAO,KAAK,YAAY,IAAI,SAAS,oBAAoB,EAAE,KAAK,MAAM,CAAC,GAAG,WAAW,OAAK,KAAK,0BAA0B,GAAG,oBAAoB,CAAC,CAAC;AAAA,EACpJ;AAAA,EACA,0BAA0B,eAAe,sBAAsB;AAC7D,QAAI,SAAS;AACb,QAAI,yBAAyB,cAAc;AACzC,YAAM,OAAO,cAAc,QAAQ,CAAC;AACpC,YAAM,MAAM,KAAK,UAAU,IAAI;AAC/B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,eAAS,GAAG,UAAU,EAAE,MAAM,cAAc,EAAE,IAAI,OAAO,EAAE;AAAA,IAC7D,OAAO;AACL,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,eAAS,CAAC,CAAC,UAAU,UAAU,GAAG,aAAa;AAAA,IACjD;AACA,SAAK,cAAc,SAAS,sBAAsB,MAAM;AACxD,WAAO,WAAW,MAAM,IAAI,MAAM,MAAM,CAAC;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,WAAW;AACjB,IAAM,uCAAN,MAAM,sCAAqC;AAAA,EACzC,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA;AAAA,EAEA,+BAA+B,iBAAiB,QAAQ,YAAY;AAClE,QAAI,UAAU,mBACT,gBAAgB;AAErB,QAAI,CAAC,KAAK,mBAAmB,eAAe,GAAG;AAC7C,YAAM,kBAAkB,KAAK,0BAA0B,WAAW,MAAM;AACxE,gBAAU,iCACL,UADK;AAAA,QAER,UAAU;AAAA,MACZ;AAAA,IACF;AACA,SAAK,0BAA0B,MAAM,eAAe,SAAS,MAAM;AACnE,QAAI,OAAO,gCAAgC,gBAAgB,YAAY,eAAe;AACpF,WAAK,0BAA0B,MAAM,0BAA0B,gBAAgB,WAAW,eAAe,MAAM;AAAA,IACjH;AACA,QAAI,KAAK,uBAAuB,MAAM,KAAK,CAAC,gBAAgB,gBAAgB;AAC1E,WAAK,oBAAoB;AAAA,IAC3B,OAAO;AACL,WAAK,cAAc,SAAS,QAAQ,2BAA2B;AAAA,IACjE;AACA,QAAI,gBAAgB,YAAY,OAAO;AACrC,YAAM,eAAe,4CAA4C,gBAAgB,WAAW,KAAK;AACjG,WAAK,cAAc,SAAS,QAAQ,YAAY;AAChD,WAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,WAAK,iBAAiB,SAAS,IAAI,MAAM;AACzC,WAAK,8BAA8B,gBAAgB,YAAY,gBAAgB,cAAc;AAC7F,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD;AACA,SAAK,cAAc,SAAS,QAAQ,eAAe,KAAK,UAAU,gBAAgB,YAAY,MAAM,CAAC,CAAC;AAAA,mDACvD;AAC/C,WAAO,KAAK,qBAAqB,eAAe,MAAM,EAAE,KAAK,IAAI,aAAW,KAAK,iBAAiB,SAAS,MAAM,CAAC,GAAG,WAAW,SAAO;AAErI,YAAM,gBAAgB,KAAK,gBAAgB,MAAM;AACjD,UAAI,CAAC,CAAC,eAAe;AACnB,aAAK,cAAc,WAAW,QAAQ,0DAA0D;AAChG,eAAO,GAAG,aAAa;AAAA,MACzB;AACA,aAAO,WAAW,MAAM,IAAI,MAAM,GAAG,CAAC;AAAA,IACxC,CAAC,GAAG,UAAU,aAAW;AACvB,UAAI,SAAS;AACX,wBAAgB,UAAU;AAC1B,eAAO,GAAG,eAAe;AAAA,MAC3B;AACA,YAAM,eAAe;AACrB,WAAK,cAAc,WAAW,QAAQ,YAAY;AAClD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD,CAAC,GAAG,WAAW,SAAO;AACpB,YAAM,eAAe,8CAA8C,GAAG;AACtE,WAAK,cAAc,WAAW,QAAQ,YAAY;AAClD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,mBAAmB,iBAAiB;AAClC,WAAO,CAAC,CAAC,iBAAiB,YAAY;AAAA,EACxC;AAAA,EACA,8BAA8B,QAAQ,gBAAgB;AACpD,QAAI,mBAAmB,iBAAiB;AACxC,QAAI,UAAU,OAAO,WAAW,YAAY,WAAW,UAAU,OAAO,UAAU,kBAAkB;AAClG,yBAAmB,iBAAiB;AAAA,IACtC;AACA,SAAK,iBAAiB,0BAA0B;AAAA,MAC9C,iBAAiB;AAAA,MACjB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,QAAQ;AAC7B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC;AAAA,EACV;AAAA,EACA,sBAAsB;AACpB,SAAK,SAAS,aAAa,QAAQ,aAAa,CAAC,GAAG,KAAK,SAAS,OAAO,KAAK,SAAS,YAAY,SAAS,SAAS,KAAK,SAAS,YAAY,SAAS,QAAQ;AAAA,EAClK;AAAA,EACA,iBAAiB,SAAS,QAAQ;AAChC,SAAK,0BAA0B,MAAM,UAAU,SAAS,MAAM;AAAA,EAChE;AAAA,EACA,gBAAgB,QAAQ;AACtB,WAAO,KAAK,0BAA0B,KAAK,UAAU,MAAM;AAAA,EAC7D;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6CAA6C,mBAAmB;AACnF,aAAO,KAAK,qBAAqB,uCAAsC;AAAA,IACzE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sCAAqC;AAAA,MAC9C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sCAAsC,CAAC;AAAA,IAC7G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qCAAN,MAAM,oCAAmC;AAAA,EACvC,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA;AAAA;AAAA,EAGA,qBAAqB,QAAQ,YAAY,MAAM;AAC7C,UAAM,qBAAqB,KAAK,iBAAiB,qBAAqB,MAAM;AAC5E,SAAK,cAAc,SAAS,QAAQ,8BAA8B;AAClE,QAAI,CAAC,oBAAoB;AACvB,WAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AAAA,IACrE;AACA,WAAO,QAAQ,KAAK,SAAS,SAAS,KAAK,UAAU,CAAC;AACtD,UAAM,aAAa,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC,YAAY,SAAS;AAC9D,YAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,iBAAW,MAAM,MAAM,CAAC,IAAI,MAAM,KAAK,GAAG;AAC1C,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,UAAM,kBAAkB;AAAA,MACtB,MAAM;AAAA,MACN,cAAc;AAAA,MACd,OAAO;AAAA,MACP,cAAc;AAAA,MACd;AAAA,MACA,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,IACnB;AACA,WAAO,GAAG,eAAe;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,mBAAmB;AACjF,aAAO,KAAK,qBAAqB,qCAAoC;AAAA,IACvE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oCAAmC;AAAA,MAC5C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uCAAN,MAAM,sCAAqC;AAAA,EACzC,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAAA,EACjD;AAAA;AAAA,EAEA,gCAAgC,QAAQ;AACtC,UAAM,YAAY,KAAK,iBAAiB,oCAAoC,MAAM;AAClF,SAAK,cAAc,SAAS,QAAQ,iDAAiD,SAAS;AAC9F,UAAM,eAAe,KAAK,iBAAiB,gBAAgB,MAAM;AACjE,UAAM,UAAU,KAAK,iBAAiB,WAAW,MAAM;AACvD,QAAI,cAAc;AAChB,YAAM,kBAAkB;AAAA,QACtB,MAAM;AAAA,QACN;AAAA,QACA,OAAO;AAAA,QACP,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,MACnB;AACA,WAAK,cAAc,SAAS,QAAQ,iEAAiE;AAErG,WAAK,iBAAiB,SAAS,uBAAuB,8BAA8B,MAAM;AAC1F,aAAO,GAAG,eAAe;AAAA,IAC3B,OAAO;AACL,YAAM,eAAe;AACrB,WAAK,cAAc,SAAS,QAAQ,YAAY;AAChD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6CAA6C,mBAAmB;AACnF,aAAO,KAAK,qBAAqB,uCAAsC;AAAA,IACzE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sCAAqC;AAAA,MAC9C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sCAAsC,CAAC;AAAA,IAC7G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qCAAN,MAAM,oCAAmC;AAAA,EACvC,cAAc;AACZ,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,4BAA4B,OAAO,yBAAyB;AAAA,EACnE;AAAA;AAAA,EAEA,2BAA2B,iBAAiB,QAAQ,qBAAqB;AACvE,QAAI,UAAU,IAAI,YAAY;AAC9B,cAAU,QAAQ,IAAI,gBAAgB,mCAAmC;AACzE,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,MAAM;AACnG,UAAM,gBAAgB,wBAAwB;AAC9C,QAAI,CAAC,eAAe;AAClB,aAAO,WAAW,MAAM,IAAI,MAAM,4BAA4B,CAAC;AAAA,IACjE;AACA,UAAM,OAAO,KAAK,WAAW,0CAA0C,gBAAgB,cAAc,QAAQ,mBAAmB;AAChI,WAAO,KAAK,YAAY,KAAK,eAAe,MAAM,QAAQ,OAAO,EAAE,KAAK,UAAU,cAAY;AAC5F,WAAK,cAAc,SAAS,QAAQ,2BAA2B,QAAQ,EAAE;AACzE,UAAI,UAAU;AACZ,iBAAS,QAAQ,gBAAgB;AAAA,MACnC;AACA,sBAAgB,aAAa;AAC7B,aAAO,GAAG,eAAe;AAAA,IAC3B,CAAC,GAAG,UAAU,WAAS,KAAK,mBAAmB,OAAO,MAAM,CAAC,GAAG,WAAW,WAAS;AAClF,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,eAAe,4BAA4B,SAAS;AAC1D,WAAK,cAAc,SAAS,QAAQ,cAAc,KAAK;AACvD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,mBAAmB,QAAQ,QAAQ;AACjC,WAAO,OAAO,KAAK,SAAS,WAAS;AAEnC,UAAI,eAAe,KAAK,GAAG;AACzB,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,4BAA4B,SAAS;AAC1D,aAAK,cAAc,WAAW,QAAQ,cAAc,KAAK;AACzD,eAAO,OAAO,8BAA8B,KAAK,GAAI;AAAA,MACvD;AACA,aAAO,WAAW,MAAM,KAAK;AAAA,IAC/B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,mBAAmB;AACjF,aAAO,KAAK,qBAAqB,qCAAoC;AAAA,IACvE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oCAAmC;AAAA,MAC5C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,oCAAoC,QAAQ,QAAQ;AAClD,QAAI,KAAK,kBAAkB,MAAM,GAAG;AAClC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,kBAAkB,MAAM,GAAG;AAClC,aAAO;AAAA,IACT;AACA,QAAI,KAAK,mCAAmC,QAAQ,MAAM,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,QAAI,KAAK,qBAAqB,QAAQ,MAAM,GAAG;AAC7C,aAAO,WAAW;AAAA,IACpB;AACA,WAAO,KAAK,uBAAuB,QAAQ,MAAM;AAAA,EACnD;AAAA,EACA,SAAS,QAAQ,QAAQ;AACvB,QAAI,CAAC,UAAU,CAAC,QAAQ;AACtB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,oBAAoB,QAAQ,MAAM,GAAG;AAC5C,aAAO,KAAK,kBAAkB,QAAQ,MAAM;AAAA,IAC9C;AACA,QAAI,KAAK,qBAAqB,QAAQ,MAAM,GAAG;AAC7C,aAAO,WAAW;AAAA,IACpB;AACA,QAAI,KAAK,qBAAqB,QAAQ,MAAM,GAAG;AAC7C,aAAO,KAAK,UAAU,MAAM,EAAE,YAAY,MAAM,KAAK,UAAU,MAAM,EAAE,YAAY;AAAA,IACrF;AACA,QAAI,KAAK,mCAAmC,QAAQ,MAAM,GAAG;AAC3D,UAAI,MAAM,QAAQ,MAAM,KAAK,KAAK,cAAc,MAAM,GAAG;AACvD,eAAO,OAAO,CAAC,MAAM;AAAA,MACvB;AACA,UAAI,MAAM,QAAQ,MAAM,KAAK,KAAK,cAAc,MAAM,GAAG;AACvD,eAAO,OAAO,CAAC,MAAM;AAAA,MACvB;AAAA,IACF;AACA,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,mCAAmC,QAAQ,QAAQ;AACjD,WAAO,MAAM,QAAQ,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,MAAM,QAAQ,MAAM,KAAK,KAAK,cAAc,MAAM;AAAA,EAClH;AAAA,EACA,qBAAqB,QAAQ,QAAQ;AACnC,WAAO,KAAK,cAAc,MAAM,KAAK,KAAK,cAAc,MAAM;AAAA,EAChE;AAAA,EACA,qBAAqB,QAAQ,QAAQ;AACnC,WAAO,KAAK,cAAc,MAAM,KAAK,KAAK,cAAc,MAAM;AAAA,EAChE;AAAA,EACA,oBAAoB,QAAQ,QAAQ;AAClC,WAAO,MAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ,MAAM;AAAA,EACtD;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,OAAO,UAAU,YAAY,iBAAiB;AAAA,EACvD;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,OAAO,UAAU;AAAA,EAC1B;AAAA,EACA,kBAAkB,MAAM,MAAM;AAC5B,QAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAO;AAAA,IACT;AACA,aAAS,IAAI,KAAK,QAAQ,OAAM;AAC9B,UAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,MAAM,MAAM;AACjC,QAAI,KAAK,WAAW,KAAK,QAAQ;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,KAAK,OAAK,KAAK,SAAS,CAAC,CAAC;AAAA,EACxC;AAAA,EACA,kBAAkB,KAAK;AACrB,WAAO,QAAQ,QAAQ,QAAQ;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAN,MAA4B;AAAA,EAC1B,YAAY,cAAc,IAAI,UAAU,IAAI,sBAAsB,OAAO,iBAAiB;AAAA,IACxF,SAAS;AAAA,EACX,GAAG,QAAQ,iBAAiB,QAAQ;AAClC,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,cAAc;AACZ,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,yBAAyB,OAAO,sBAAsB;AAC3D,SAAK,qBAAqB,OAAO,kBAAkB;AACnD,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,aAAa,OAAO,UAAU;AAAA,EACrC;AAAA,EACA,wBAAwB,iBAAiB,eAAe;AACtD,UAAM,WAAW,QAAQ,gBAAgB,YAAY,KAAK;AAC1D,UAAM,qBAAqB,QAAQ,eAAe;AAClD,QAAI,CAAC,sBAAsB,UAAU;AACnC,aAAO,GAAG,IAAI,sBAAsB,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC;AAAA,IACxD;AACA,WAAO,KAAK,cAAc,iBAAiB,aAAa;AAAA,EAC1D;AAAA,EACA,cAAc,iBAAiB,eAAe;AAC5C,UAAM,WAAW,IAAI,sBAAsB;AAC3C,UAAM,mBAAmB,KAAK,0BAA0B,KAAK,oBAAoB,aAAa;AAC9F,QAAI,CAAC,KAAK,uBAAuB,8BAA8B,gBAAgB,YAAY,OAAO,kBAAkB,aAAa,GAAG;AAClI,WAAK,cAAc,WAAW,eAAe,8BAA8B;AAC3E,eAAS,QAAQ,iBAAiB;AAClC,WAAK,6BAA6B,aAAa;AAC/C,aAAO,GAAG,QAAQ;AAAA,IACpB;AACA,UAAM,2CAA2C,KAAK,WAAW,yCAAyC,aAAa;AACvH,UAAM,wBAAwB,KAAK,WAAW,sBAAsB,aAAa;AACjF,QAAI,4CAA4C,uBAAuB;AACrE,eAAS,cAAc,gBAAgB,YAAY,gBAAgB;AAAA,IACrE;AACA,UAAM,2BAA2B,cAAc;AAC/C,QAAI,0BAA0B;AAC5B,eAAS,QAAQ,iBAAiB;AAClC,eAAS,sBAAsB;AAC/B,aAAO,GAAG,QAAQ;AAAA,IACpB;AACA,UAAM,uBAAuB,gBAAgB,kBAAkB,CAAC,CAAC,gBAAgB;AACjF,UAAM,aAAa,QAAQ,gBAAgB,YAAY,QAAQ;AAC/D,QAAI,wBAAwB,CAAC,YAAY;AACvC,eAAS,QAAQ,iBAAiB;AAClC,eAAS,sBAAsB;AAC/B,aAAO,GAAG,QAAQ;AAAA,IACpB;AACA,QAAI,YAAY;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,eAAS,UAAU,gBAAgB,YAAY,YAAY;AAC3D,eAAS,iBAAiB,KAAK,mBAAmB,oBAAoB,SAAS,SAAS,OAAO,aAAa;AAC5G,aAAO,KAAK,uBAAuB,yBAAyB,SAAS,SAAS,gBAAgB,SAAS,aAAa,EAAE,KAAK,SAAS,6BAA2B;AAC7J,YAAI,CAAC,yBAAyB;AAC5B,eAAK,cAAc,SAAS,eAAe,mDAAmD;AAC9F,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,cAAM,YAAY,KAAK,0BAA0B,KAAK,aAAa,aAAa;AAChF,YAAI,CAAC,KAAK,uBAAuB,qBAAqB,SAAS,gBAAgB,WAAW,QAAQ,uBAAuB,GAAG,aAAa,GAAG;AAC1I,eAAK,cAAc,WAAW,eAAe,mFAAmF;AAChI,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,KAAK,uBAAuB,wBAAwB,SAAS,gBAAgB,aAAa,GAAG;AAChG,eAAK,cAAc,SAAS,eAAe,+EAA+E;AAC1H,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,wBAAwB,CAAC,KAAK,uBAAuB,4BAA4B,SAAS,gBAAgB,uCAAuC,KAAK,QAAQ,0BAA0B,GAAG,aAAa,GAAG;AAC9M,eAAK,cAAc,WAAW,eAAe,8FAA8F;AAC3I,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,cAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,YAAI,wBAAwB;AAC1B,cAAI,kBAAkB;AACpB,iBAAK,cAAc,SAAS,eAAe,wDAAwD;AAAA,UACrG,WAAW,CAAC,oBAAoB,CAAC,KAAK,uBAAuB,mBAAmB,SAAS,gBAAgB,uBAAuB,QAAQ,aAAa,GAAG;AACtJ,iBAAK,cAAc,WAAW,eAAe,yEAAyE;AACtH,qBAAS,QAAQ,iBAAiB;AAClC,iBAAK,6BAA6B,aAAa;AAC/C,mBAAO,GAAG,QAAQ;AAAA,UACpB;AAAA,QACF,OAAO;AACL,eAAK,cAAc,WAAW,eAAe,qCAAqC;AAClF,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,KAAK,uBAAuB,mBAAmB,SAAS,gBAAgB,UAAU,aAAa,GAAG;AACrG,eAAK,cAAc,WAAW,eAAe,4BAA4B;AACzE,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,KAAK,uBAAuB,yCAAyC,SAAS,cAAc,GAAG;AAClG,eAAK,cAAc,WAAW,eAAe,0BAA0B;AACvE,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,KAAK,uBAAuB,wBAAwB,SAAS,gBAAgB,QAAQ,GAAG;AAC3F,eAAK,cAAc,WAAW,eAAe,4BAA4B;AACzE,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,KAAK,uCAAuC,iBAAiB,SAAS,gBAAgB,aAAa,GAAG;AACzG,eAAK,cAAc,WAAW,eAAe,gEAAgE;AAC7G,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,wBAAwB,CAAC,KAAK,uBAAuB,6BAA6B,SAAS,gBAAgB,eAAe,oCAAoC,GAAG;AACpK,eAAK,cAAc,WAAW,eAAe,+BAA+B;AAC5E,mBAAS,QAAQ,iBAAiB;AAClC,eAAK,6BAA6B,aAAa;AAC/C,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,eAAO,KAAK,gBAAgB,0CAA0C,uBAAuB,UAAU,eAAe,eAAe;AAAA,MACvI,CAAC,CAAC;AAAA,IACJ,OAAO;AACL,WAAK,cAAc,SAAS,eAAe,iDAAiD;AAAA,IAC9F;AACA,WAAO,KAAK,gBAAgB,0CAA0C,uBAAuB,UAAU,eAAe,eAAe;AAAA,EACvI;AAAA,EACA,gBAAgB,0CAA0C,uBAAuB,UAAU,eAAe,iBAAiB;AAEzH,QAAI,CAAC,4CAA4C,CAAC,uBAAuB;AACvE,eAAS,sBAAsB;AAC/B,eAAS,QAAQ,iBAAiB;AAClC,WAAK,2BAA2B,aAAa;AAC7C,WAAK,6BAA6B,aAAa;AAC/C,aAAO,GAAG,QAAQ;AAAA,IACpB;AAEA,QAAI,gBAAgB,YAAY,UAAU;AACxC,YAAM,gBAAgB,KAAK,mBAAmB,mBAAmB,SAAS,SAAS,OAAO,aAAa;AACvG,UAAI,yBAAyB,CAAC,SAAS,eAAe,SAAS;AAC7D,aAAK,cAAc,SAAS,eAAe,mEAAmE;AAAA,MAChH,OAAO;AACL,eAAO,KAAK,uBAAuB;AAAA,UAAsB,SAAS;AAAA,UAAa,SAAS,eAAe;AAAA,UAAS,cAAc;AAAA;AAAA,UAE9H;AAAA,QAAa,EAAE,KAAK,IAAI,WAAS;AAC/B,cAAI,CAAC,SAAS,CAAC,SAAS,aAAa;AACnC,iBAAK,cAAc,WAAW,eAAe,gCAAgC;AAC7E,qBAAS,QAAQ,iBAAiB;AAClC,iBAAK,6BAA6B,aAAa;AAC/C,mBAAO;AAAA,UACT,OAAO;AACL,qBAAS,sBAAsB;AAC/B,qBAAS,QAAQ,iBAAiB;AAClC,iBAAK,2BAA2B,aAAa;AAC7C,mBAAO;AAAA,UACT;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,aAAS,sBAAsB;AAC/B,aAAS,QAAQ,iBAAiB;AAClC,SAAK,2BAA2B,aAAa;AAC7C,WAAO,GAAG,QAAQ;AAAA,EACpB;AAAA,EACA,uCAAuC,iBAAiB,YAAY,eAAe;AACjF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,gBAAgB,iBAAiB;AACpC,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,KAAK,mBAAmB,oBAAoB,gBAAgB,iBAAiB,OAAO,aAAa;AAKxH,QAAI,eAAe,QAAQ,WAAW,KAAK;AACzC,WAAK,cAAc,SAAS,eAAe,qBAAqB,eAAe,GAAG,IAAI,WAAW,GAAG,EAAE;AACtG,aAAO;AAAA,IACT;AAIA,QAAI,eAAe,QAAQ,WAAW,KAAK;AACzC,WAAK,cAAc,SAAS,eAAe,qBAAqB,eAAe,GAAG,IAAI,WAAW,GAAG,EAAE;AACtG,aAAO;AAAA,IACT;AAEA,QAAI,eAAe,QAAQ,WAAW,KAAK;AACzC,WAAK,cAAc,SAAS,eAAe,qBAAqB,eAAe,GAAG,IAAI,WAAW,GAAG,EAAE;AACtG,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,KAAK,gBAAgB,oCAAoC,gBAAgB,KAAK,YAAY,GAAG,GAAG;AACnG,WAAK,cAAc,SAAS,eAAe,sCAAsC,gBAAgB,GAAG,MAAM,WAAW,GAAG,GAAG;AAC3H,aAAO;AAAA,IACT;AACA,QAAI,yCAAyC;AAC3C,aAAO;AAAA,IACT;AAIA,QAAI,eAAe,cAAc,WAAW,WAAW;AACrD,WAAK,cAAc,SAAS,eAAe,2BAA2B,eAAe,SAAS,IAAI,WAAW,SAAS,EAAE;AACxH,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,2BAA2B,eAAe;AACxC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,0BAA0B,MAAM,aAAa,MAAM,aAAa;AACrE,QAAI,mCAAmC;AACrC,WAAK,0BAA0B,MAAM,oBAAoB,IAAI,aAAa;AAAA,IAC5E;AACA,SAAK,cAAc,SAAS,eAAe,2CAA2C;AAAA,EACxF;AAAA,EACA,6BAA6B,eAAe;AAC1C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,0BAA0B,MAAM,aAAa,MAAM,aAAa;AACrE,QAAI,mCAAmC;AACrC,WAAK,0BAA0B,MAAM,oBAAoB,IAAI,aAAa;AAAA,IAC5E;AACA,SAAK,cAAc,SAAS,eAAe,+BAA+B;AAAA,EAC5E;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,wBAAuB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wCAAN,MAAM,uCAAsC;AAAA,EAC1C,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,yBAAyB,OAAO,sBAAsB;AAC3D,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA;AAAA,EAEA,wBAAwB,iBAAiB,eAAe,YAAY;AAClE,WAAO,KAAK,uBAAuB,wBAAwB,iBAAiB,aAAa,EAAE,KAAK,IAAI,sBAAoB;AACtH,sBAAgB,mBAAmB;AACnC,UAAI,iBAAiB,qBAAqB;AACxC,aAAK,iBAAiB,qBAAqB,iBAAiB,aAAa,gBAAgB,YAAY,eAAe,UAAU;AAC9H,eAAO;AAAA,MACT,OAAO;AACL,cAAM,eAAe,oEAAoE,KAAK,SAAS,SAAS,IAAI;AACpH,aAAK,cAAc,WAAW,eAAe,YAAY;AACzD,aAAK,qBAAqB,uBAAuB,eAAe,UAAU;AAC1E,aAAK,yBAAyB,gBAAgB,kBAAkB,gBAAgB,cAAc;AAC9F,cAAM,IAAI,MAAM,YAAY;AAAA,MAC9B;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,yBAAyB,uBAAuB,gBAAgB;AAC9D,SAAK,iBAAiB,0BAA0B;AAAA,MAC9C,iBAAiB;AAAA,MACjB,kBAAkB,sBAAsB;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8CAA8C,mBAAmB;AACpF,aAAO,KAAK,qBAAqB,wCAAuC;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uCAAsC;AAAA,MAC/C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uCAAuC,CAAC;AAAA,IAC9G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,uBAAuB,OAAO,oBAAoB;AAAA,EACzD;AAAA;AAAA,EAEA,aAAa,iBAAiB,eAAe,YAAY;AACvD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,cAAc;AACjB,UAAI,CAAC,kBAAkB,8BAA8B;AAEnD,YAAI,kBAAkB,gBAAgB;AACpC,eAAK,YAAY,mBAAmB,iBAAiB,gBAAgB,eAAe,UAAU;AAAA,QAChG;AAAA,MACF;AACA,UAAI,CAAC,kBAAkB,CAAC,cAAc;AACpC,aAAK,iBAAiB,gBAAgB,YAAY,eAAe,aAAa;AAAA,MAChF;AACA,WAAK,iBAAiB,kBAAkB,cAAc;AACtD,aAAO,GAAG,eAAe;AAAA,IAC3B;AACA,WAAO,KAAK,YAAY,6BAA6B,eAAe,YAAY,gBAAgB,kBAAkB,SAAS,kBAAkB,cAAc,EAAE,KAAK,UAAU,cAAY;AACtL,UAAI,CAAC,CAAC,UAAU;AACd,YAAI,CAAC,cAAc;AACjB,eAAK,iBAAiB,gBAAgB,YAAY,eAAe,aAAa;AAAA,QAChF;AACA,aAAK,iBAAiB,kBAAkB,cAAc;AACtD,eAAO,GAAG,eAAe;AAAA,MAC3B,OAAO;AACL,aAAK,qBAAqB,uBAAuB,eAAe,UAAU;AAC1E,aAAK,4BAA4B,kBAAkB,cAAc;AACjE,cAAM,eAAe,qCAAqC,QAAQ;AAClE,aAAK,cAAc,WAAW,eAAe,YAAY;AACzD,eAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,MACjD;AAAA,IACF,CAAC,GAAG,WAAW,SAAO;AACpB,YAAM,eAAe,6CAA6C,GAAG;AACrE,WAAK,cAAc,WAAW,eAAe,YAAY;AACzD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,iBAAiB,uBAAuB,gBAAgB;AACtD,QAAI,CAAC,uBAAuB;AAC1B;AAAA,IACF;AACA,SAAK,iBAAiB,0BAA0B;AAAA,MAC9C,iBAAiB;AAAA,MACjB,kBAAkB,sBAAsB;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B,uBAAuB,gBAAgB;AACjE,QAAI,CAAC,uBAAuB;AAC1B;AAAA,IACF;AACA,SAAK,iBAAiB,0BAA0B;AAAA,MAC9C,iBAAiB;AAAA,MACjB,kBAAkB,sBAAsB;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,4BAA2B;AAAA,MACpC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,SAAK,iCAAiC,OAAO,8BAA8B;AAC3E,SAAK,qCAAqC,OAAO,kCAAkC;AACnF,SAAK,uCAAuC,OAAO,oCAAoC;AACvF,SAAK,qBAAqB,OAAO,0BAA0B;AAC3D,SAAK,wCAAwC,OAAO,qCAAqC;AACzF,SAAK,uCAAuC,OAAO,oCAAoC;AACvF,SAAK,qCAAqC,OAAO,kCAAkC;AAAA,EACrF;AAAA,EACA,wBAAwB,YAAY,QAAQ,YAAY;AACtD,WAAO,KAAK,+BAA+B,iBAAiB,YAAY,MAAM,EAAE,KAAK,UAAU,qBAAmB,KAAK,+BAA+B,oBAAoB,iBAAiB,MAAM,CAAC,GAAG,UAAU,qBAAmB,KAAK,qCAAqC,+BAA+B,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,sCAAsC,wBAAwB,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,mBAAmB,aAAa,iBAAiB,QAAQ,UAAU,CAAC,CAAC;AAAA,EAClkB;AAAA,EACA,mCAAmC,cAAc,QAAQ,YAAY;AACnE,WAAO,KAAK,+BAA+B,oBAAoB,cAAc,MAAM,EAAE,KAAK,UAAU,qBAAmB,KAAK,qCAAqC,+BAA+B,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,sCAAsC,wBAAwB,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,mBAAmB,aAAa,iBAAiB,QAAQ,UAAU,CAAC,CAAC;AAAA,EACvd;AAAA,EACA,4BAA4B,QAAQ,YAAY,MAAM;AACpD,WAAO,KAAK,mCAAmC,qBAAqB,QAAQ,YAAY,IAAI,EAAE,KAAK,UAAU,qBAAmB,KAAK,qCAAqC,+BAA+B,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,sCAAsC,wBAAwB,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,mBAAmB,aAAa,iBAAiB,QAAQ,UAAU,CAAC,CAAC;AAAA,EAChe;AAAA,EACA,oBAAoB,QAAQ,YAAY,qBAAqB;AAC3D,WAAO,KAAK,qCAAqC,gCAAgC,MAAM,EAAE,KAAK,UAAU,qBAAmB,KAAK,mCAAmC,2BAA2B,iBAAiB,QAAQ,mBAAmB,CAAC,GAAG,UAAU,qBAAmB,KAAK,qCAAqC,+BAA+B,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,sCAAsC,wBAAwB,iBAAiB,QAAQ,UAAU,CAAC,GAAG,UAAU,qBAAmB,KAAK,mBAAmB,aAAa,iBAAiB,QAAQ,UAAU,CAAC,CAAC;AAAA,EAC3mB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,OAAO,OAAO,MAAM;AACzB,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,4BAA4B;AAAA,EACnC;AAAA,EACA,2BAA2B;AACzB,WAAO,QAAQ,KAAK,yBAAyB;AAAA,EAC/C;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,2BAA2B;AAClC,WAAK,0BAA0B,YAAY;AAC3C,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,wBAAwB,oBAAoB;AAC1C,UAAM,qCAAqC,qBAAqB;AAChE,WAAO,IAAI,WAAW,gBAAc;AAClC,UAAI;AACJ,WAAK,KAAK,kBAAkB,MAAM;AAChC,qBAAa,KAAK,UAAU,aAAa,YAAY,MAAM,KAAK,KAAK,IAAI,MAAM,WAAW,KAAK,CAAC,GAAG,kCAAkC;AAAA,MACvI,CAAC;AACD,aAAO,MAAM;AACX,sBAAc,UAAU;AAAA,MAC1B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,kBAAkB,OAAO,eAAe;AAAA,EAC/C;AAAA,EACA,8BAA8B,YAAY,QAAQ,YAAY;AAC5D,UAAM,iBAAiB,KAAK,iBAAiB,qBAAqB,MAAM;AACxE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,iBAAiB,OAAO,kBAAkB;AAChD,UAAM,oBAAoB,OAAO,qBAAqB;AACtD,WAAO,KAAK,aAAa,wBAAwB,YAAY,QAAQ,UAAU,EAAE,KAAK,IAAI,qBAAmB;AAC3G,WAAK,iBAAiB,wBAAwB,MAAM;AACpD,UAAI,CAAC,mCAAmC,CAAC,gBAAgB,gBAAgB;AACvE,aAAK,OAAO,cAAc,cAAc;AAAA,MAC1C;AAAA,IACF,CAAC,GAAG,WAAW,WAAS;AACtB,WAAK,iBAAiB,wBAAwB,MAAM;AACpD,WAAK,iBAAiB,wBAAwB,MAAM;AACpD,WAAK,gBAAgB,uBAAuB;AAC5C,UAAI,CAAC,mCAAmC,CAAC,gBAAgB;AACvD,aAAK,OAAO,cAAc,iBAAiB;AAAA,MAC7C;AACA,aAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,IAC1C,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,cAAc;AACZ,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,SAAS,OAAO,MAAM;AAC3B,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,kBAAkB,OAAO,eAAe;AAAA,EAC/C;AAAA,EACA,kCAAkC,QAAQ,YAAY,MAAM;AAC1D,UAAM,iBAAiB,KAAK,iBAAiB,qBAAqB,MAAM;AACxE,UAAM,kCAAkC,QAAQ,OAAO,+BAA+B;AACtF,UAAM,iBAAiB,OAAO,kBAAkB;AAChD,UAAM,oBAAoB,OAAO,qBAAqB;AACtD,WAAO,KAAK,aAAa,4BAA4B,QAAQ,YAAY,IAAI,EAAE,KAAK,IAAI,qBAAmB;AACzG,UAAI,CAAC,mCAAmC,CAAC,gBAAgB,gBAAgB;AACvE,aAAK,OAAO,cAAc,cAAc;AAAA,MAC1C;AAAA,IACF,CAAC,GAAG,WAAW,WAAS;AACtB,WAAK,iBAAiB,wBAAwB,MAAM;AACpD,WAAK,gBAAgB,uBAAuB;AAC5C,UAAI,CAAC,mCAAmC,CAAC,gBAAgB;AACvD,aAAK,OAAO,cAAc,iBAAiB;AAAA,MAC7C;AACA,aAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,IAC1C,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAA6B;AAAA,IAChE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,6BAA4B;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,8BAA8B,OAAO,2BAA2B;AACrE,SAAK,0BAA0B,OAAO,uBAAuB;AAC7D,SAAK,uBAAuB,IAAI,QAAQ;AAAA,EAC1C;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,qBAAqB,aAAa;AAAA,EAChD;AAAA,EACA,WAAW,YAAY;AACrB,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,WAAO,KAAK,WAAW,kBAAkB,UAAU;AAAA,EACrD;AAAA,EACA,4BAA4B,oBAAoB,QAAQ,YAAY;AAClE,QAAI,YAAY,IAAI,WAAW;AAC/B,QAAI,KAAK,WAAW,sBAAsB,MAAM,GAAG;AACjD,kBAAY,KAAK,wBAAwB,8BAA8B,oBAAoB,QAAQ,UAAU;AAAA,IAC/G,WAAW,KAAK,WAAW,6BAA6B,MAAM,GAAG;AAC/D,UAAI,oBAAoB,SAAS,GAAG,GAAG;AACrC,cAAM,OAAO,mBAAmB,UAAU,mBAAmB,QAAQ,GAAG,IAAI,CAAC;AAC7E,oBAAY,KAAK,4BAA4B,kCAAkC,QAAQ,YAAY,IAAI;AAAA,MACzG,OAAO;AACL,oBAAY,KAAK,4BAA4B,kCAAkC,QAAQ,UAAU;AAAA,MACnG;AAAA,IACF;AACA,WAAO,UAAU,KAAK,IAAI,MAAM,KAAK,qBAAqB,KAAK,CAAC,CAAC;AAAA,EACnE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,aAAa,OAAO,WAAW;AAAA,EACtC;AAAA,EACA,YAAY;AACV,WAAO,kBAAkB,KAAK,UAAU;AAAA,EAC1C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAoB;AAC1B,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,OAAO,OAAO,WAAW;AAAA,EAChC;AAAA,EACA,+BAA+B,QAAQ;AACrC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,0BAA0B;AAC7B,YAAM,eAAe;AACrB,WAAK,cAAc,SAAS,QAAQ,YAAY;AAChD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD;AACA,WAAO,KAAK,qBAAqB,0BAA0B,MAAM,EAAE,KAAK,IAAI,yBAAuB;AAAA,MACjG,QAAQ,mBAAmB;AAAA,MAC3B,SAAS,mBAAmB;AAAA,MAC5B,uBAAuB,mBAAmB;AAAA,MAC1C,eAAe,mBAAmB;AAAA,MAClC,kBAAkB,mBAAmB;AAAA,MACrC,oBAAoB,mBAAmB;AAAA,MACvC,oBAAoB,mBAAmB;AAAA,MACvC,oBAAoB,mBAAmB;AAAA,MACvC,uBAAuB,mBAAmB;AAAA,MAC1C,aAAa,mBAAmB;AAAA,IAClC,EAAE,CAAC;AAAA,EACL;AAAA,EACA,qBAAqB,mBAAmB,QAAQ;AAC9C,QAAI,MAAM;AACV,UAAM,kBAAkB,OAAO,0BAA0B;AACzD,QAAI,CAAC,kBAAkB,SAAS,eAAe,GAAG;AAChD,YAAM,GAAG,iBAAiB,GAAG,eAAe;AAAA,IAC9C;AACA,WAAO,KAAK,KAAK,IAAI,KAAK,MAAM,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,EACjD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,0BAAyB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,cAAc,OAAO,wBAAwB;AAClD,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,4BAA4B,OAAO,yBAAyB;AAAA,EACnE;AAAA,EACA,wBAAwB,QAAQ,0BAA0B;AACxD,SAAK,0BAA0B,MAAM,0BAA0B,0BAA0B,MAAM;AAAA,EACjG;AAAA,EACA,oCAAoC,QAAQ;AAC1C,QAAI,CAAC,QAAQ;AACX,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,WAAO,KAAK,YAAY,+BAA+B,MAAM,EAAE,KAAK,IAAI,8BAA4B,KAAK,wBAAwB,QAAQ,wBAAwB,CAAC,GAAG,WAAW,WAAS;AACvL,WAAK,oBAAoB,UAAU,WAAW,qBAAqB,IAAI;AACvE,aAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,IAC1C,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,iBAAiB;AAAA,EACrB,WAAW;AAAA,EACX,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,aAAa;AAAA,EACb,UAAU;AAAA,EACV,cAAc;AAAA,EACd,OAAO;AAAA,EACP,SAAS;AAAA,EACT,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,6BAA6B;AAAA,EAC7B,sCAAsC;AAAA,EACtC,iBAAiB;AAAA,EACjB,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,UAAU,SAAS;AAAA,EACnB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,qCAAqC;AAAA,EACrC,4BAA4B;AAAA,EAC5B,yBAAyB,CAAC;AAAA,EAC1B,iCAAiC,CAAC;AAAA,EAClC,+BAA+B,CAAC;AAAA,EAChC,yBAAyB,CAAC;AAAA,EAC1B,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,YAAY;AACd;AACA,IAAM,6BAA6B;AAAA,EACjC,QAAQ;AAAA,EACR,UAAU,CAAC;AAAA,EACX,OAAO;AACT;AACA,IAAM,kBAAkB,kBAAgB;AACtC,MAAI,CAAC,aAAa,WAAW;AAC3B,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAC,2DAA2D;AAAA,MACtE,OAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,kBAAgB;AACrC,MAAI,CAAC,aAAa,UAAU;AAC1B,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAC,wDAAwD;AAAA,MACnE,OAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,0BAA0B,kBAAgB;AAC9C,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,GAAG,SAAS,GAAG,QAAQ,GAAG,KAAK;AACxC;AACA,IAAM,qBAAqB,WAAS,IAAI,IAAI,KAAK,EAAE,SAAS,MAAM;AAClE,IAAM,gCAAgC,mBAAiB;AACrD,QAAM,iBAAiB,cAAc,IAAI,OAAK,wBAAwB,CAAC,CAAC;AACxE,QAAM,gBAAgB,eAAe,KAAK,OAAK,MAAM,EAAE;AACvD,MAAI,eAAe;AACjB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAC,sGAAsG;AAAA,MACjH,OAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,gBAAgB,mBAAmB,cAAc;AACvD,MAAI,eAAe;AACjB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAC,wEAAwE;AAAA,MACnF,OAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,qBAAqB,kBAAgB;AACzC,MAAI,CAAC,aAAa,aAAa;AAC7B,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAC,0DAA0D;AAAA,MACrE,OAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,6CAA6C,kBAAgB;AACjE,QAAM,kBAAkB,aAAa;AACrC,QAAM,mBAAmB,aAAa;AACtC,QAAM,oBAAoB,aAAa;AACvC,MAAI,mBAAmB,CAAC,oBAAoB,CAAC,mBAAmB;AAC9D,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAC,yEAAyE;AAAA,MACpF,OAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,iCAAiC,kBAAgB;AACrD,QAAM,kBAAkB,aAAa;AACrC,QAAM,iBAAiB,aAAa;AACpC,QAAM,QAAQ,aAAa,SAAS;AACpC,QAAM,kBAAkB,MAAM,MAAM,GAAG,EAAE,SAAS,gBAAgB;AAClE,MAAI,mBAAmB,kBAAkB,CAAC,iBAAiB;AACzD,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAC,kFAAkF;AAAA,MAC7F,OAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,WAAW,CAAC,iBAAiB,gCAAgC,oBAAoB,gBAAgB,0CAA0C;AACjJ,IAAM,yBAAyB,CAAC,6BAA6B;AAC7D,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AAAA,EAC3C;AAAA,EACA,gBAAgB,eAAe;AAC7B,WAAO,KAAK,wBAAwB,iBAAiB,CAAC,GAAG,sBAAsB;AAAA,EACjF;AAAA,EACA,eAAe,cAAc;AAC3B,WAAO,KAAK,uBAAuB,cAAc,QAAQ;AAAA,EAC3D;AAAA,EACA,wBAAwB,eAAe,eAAe;AACpD,QAAI,cAAc,WAAW,GAAG;AAC9B,aAAO;AAAA,IACT;AACA,UAAM,uBAAuB,cAAc,IAAI,UAAQ,KAAK,aAAa,CAAC;AAC1E,QAAI,oBAAoB;AACxB,kBAAc,QAAQ,kBAAgB;AACpC,YAAM,aAAa,KAAK,yCAAyC,sBAAsB,YAAY;AACnG,2BAAqB;AAAA,IACvB,CAAC;AACD,WAAO,sBAAsB;AAAA,EAC/B;AAAA,EACA,uBAAuB,cAAc,eAAe;AAClD,UAAM,uBAAuB,cAAc,IAAI,UAAQ,KAAK,YAAY,CAAC;AACzE,UAAM,aAAa,KAAK,yCAAyC,sBAAsB,YAAY;AACnG,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,yCAAyC,sBAAsB,QAAQ;AACrE,UAAM,cAAc,qBAAqB,OAAO,OAAK,EAAE,SAAS,SAAS,CAAC;AAC1E,UAAM,mBAAmB,KAAK,qBAAqB,SAAS,WAAW;AACvE,UAAM,cAAc,KAAK,qBAAqB,WAAW,WAAW;AACpE,qBAAiB,QAAQ,aAAW,KAAK,cAAc,SAAS,QAAQ,OAAO,CAAC;AAChF,gBAAY,QAAQ,aAAW,KAAK,cAAc,WAAW,QAAQ,OAAO,CAAC;AAC7E,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,qBAAqB,MAAM,SAAS;AAClC,UAAM,cAAc,QAAQ,OAAO,OAAK,EAAE,UAAU,IAAI,EAAE,IAAI,YAAU,OAAO,QAAQ;AACvF,WAAO,YAAY,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,GAAG,GAAG,CAAC,CAAC;AAAA,EAC7D;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,SAAS,OAAO,eAAe;AACpC,SAAK,0BAA0B,OAAO,uBAAuB;AAC7D,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,iBAAiB;AACf,WAAO,OAAO,KAAK,KAAK,eAAe,EAAE,SAAS;AAAA,EACpD;AAAA,EACA,uBAAuB;AACrB,WAAO,OAAO,OAAO,KAAK,eAAe;AAAA,EAC3C;AAAA,EACA,uBAAuB,UAAU;AAC/B,QAAI,KAAK,oBAAoB,GAAG;AAC9B,aAAO,GAAG,KAAK,UAAU,QAAQ,CAAC;AAAA,IACpC;AACA,WAAO,KAAK,wBAAwB,QAAQ,EAAE,KAAK,IAAI,YAAU,OAAO,aAAa,CAAC;AAAA,EACxF;AAAA,EACA,wBAAwB,UAAU;AAChC,WAAO,KAAK,YAAY,EAAE,KAAK,UAAU,gBAAc,KAAK,sBAAsB,UAAU,CAAC,GAAG,IAAI,yBAAuB;AAAA,MACzH,YAAY;AAAA,MACZ,eAAe,KAAK,UAAU,QAAQ;AAAA,IACxC,EAAE,CAAC;AAAA,EACL;AAAA,EACA,sBAAsB;AACpB,WAAO,OAAO,KAAK,KAAK,eAAe,EAAE,SAAS;AAAA,EACpD;AAAA,EACA,WAAW,aAAa;AACtB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,gBAAgB,QAAQ,IAAI;AAAA,EACnC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,OAAO,YAAY;AAAA,EACjC;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,UAAU,UAAU;AAClB,QAAI,QAAQ,QAAQ,GAAG;AACrB,aAAO,KAAK,gBAAgB,QAAQ,KAAK;AAAA,IAC3C;AACA,UAAM,CAAC,EAAE,KAAK,IAAI,OAAO,QAAQ,KAAK,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC;AAC1E,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,sBAAsB,eAAe;AACnC,QAAI,CAAC,KAAK,wBAAwB,gBAAgB,aAAa,GAAG;AAChE,aAAO,GAAG,CAAC,CAAC;AAAA,IACd;AACA,SAAK,gBAAgB,aAAa;AAClC,UAAM,oBAAoB,cAAc,IAAI,OAAK,KAAK,aAAa,CAAC,CAAC;AACrE,UAAM,KAAK,SAAS,iBAAiB,EAAE,KAAK,IAAI,YAAU,OAAO,OAAO,UAAQ,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,OAAK,CAAC,CAAC;AAC5G,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,eAAe;AAC7B,kBAAc,QAAQ,CAAC,QAAQ,UAAU;AACvC,UAAI,CAAC,OAAO,UAAU;AACpB,eAAO,WAAW,GAAG,KAAK,IAAI,OAAO,QAAQ;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,cAAc;AACzB,QAAI,CAAC,KAAK,wBAAwB,eAAe,YAAY,GAAG;AAC9D,WAAK,cAAc,SAAS,cAAc,+DAA+D;AACzG,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,QAAI,CAAC,aAAa,0BAA0B;AAC1C,mBAAa,2BAA2B,aAAa;AAAA,IACvD;AACA,UAAM,aAAa,KAAK,cAAc,YAAY;AAClD,SAAK,WAAW,UAAU;AAC1B,UAAM,0BAA0B,KAAK,mCAAmC,UAAU;AAClF,SAAK,oBAAoB,UAAU,WAAW,cAAc,uBAAuB;AACnF,WAAO,GAAG,UAAU;AAAA,EACtB;AAAA,EACA,mCAAmC,eAAe;AAChD,UAAM,wCAAwC,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AACzH,QAAI,CAAC,CAAC,uCAAuC;AAC3C,oBAAc,yBAAyB;AACvC,aAAO;AAAA,IACT;AACA,UAAM,+BAA+B,cAAc;AACnD,QAAI,CAAC,CAAC,8BAA8B;AAClC,WAAK,qBAAqB,wBAAwB,eAAe,4BAA4B;AAC7F,oBAAc,yBAAyB;AACvC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,eAAe;AAC3B,UAAM,8BAA8B,kCAC/B,iBACA;AAEL,SAAK,gBAAgB,2BAA2B;AAChD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,eAAe;AAC7B,QAAI,CAAC,KAAK,iBAAiB,UAAU,GAAG;AACtC,oBAAc,oBAAoB;AAClC,oBAAc,cAAc;AAC5B,oBAAc,kBAAkB;AAChC,oBAAc,iCAAiC;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,gBAAgB,OAAO,aAAa;AAAA,EAC3C;AAAA,EACA,kBAAkB,YAAY;AAC5B,UAAM,iBAAiB,KAAK,0BAA0B,UAAU;AAChE,QAAI,KAAK,gBAAgB,cAAc,GAAG;AACxC,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,oBAAoB,UAAU;AACxD,QAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,YAAY,QAAQ;AACxC,UAAM,gBAAgB,KAAK,SAAS,cAAc,QAAQ;AAC1D,kBAAc,KAAK;AACnB,kBAAc,QAAQ;AACtB,SAAK,cAAc,SAAS,QAAQ,aAAa;AACjD,kBAAc,MAAM,UAAU;AAC9B,SAAK,SAAS,KAAK,YAAY,aAAa;AAC5C,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B,YAAY;AACpC,QAAI;AACF,YAAM,gBAAgB,KAAK,SAAS,aAAa,OAAO,SAAS,eAAe,UAAU;AAC1F,UAAI,KAAK,gBAAgB,aAAa,GAAG;AACvC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,oBAAoB,YAAY;AAC9B,UAAM,gBAAgB,KAAK,SAAS,eAAe,UAAU;AAC7D,QAAI,KAAK,gBAAgB,aAAa,GAAG;AACvC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,SAAS;AACvB,WAAO,CAAC,CAAC,WAAW,mBAAmB;AAAA,EACzC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,eAAc;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qCAAqC;AAC3C,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,6CAA6C,IAAI,QAAQ;AAC9D,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,8BAA8B,OAAO,2BAA2B;AACrE,SAAK,kBAAkB,OAAO,eAAe;AAAA,EAC/C;AAAA,EACA,IAAI,qCAAqC;AACvC,WAAO,KAAK,2CAA2C,aAAa;AAAA,EACtE;AAAA,EACA,kBAAkB,QAAQ;AACxB,UAAM,iBAAiB,KAAK,kBAAkB;AAC9C,QAAI,CAAC,gBAAgB;AACnB,aAAO,KAAK,cAAc,sBAAsB,oCAAoC,MAAM;AAAA,IAC5F;AACA,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB,eAAe;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,mBAAmB,QAAQ,WAAW;AAAA,EAChD;AAAA,EACA,kCAAkC,UAAU,QAAQ,YAAY;AAC9D,UAAM,SAAS,IAAI,WAAW;AAAA,MAC5B,YAAY,SAAS,CAAC;AAAA,IACxB,CAAC;AACD,UAAM,aAAa,OAAO,IAAI,OAAO;AACrC,QAAI,YAAY;AACd,WAAK,iBAAiB,0BAA0B;AAAA,QAC9C,iBAAiB;AAAA,QACjB,kBAAkB,iBAAiB;AAAA,QACnC,gBAAgB;AAAA,MAClB,CAAC;AACD,WAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,WAAK,iBAAiB,SAAS,IAAI,MAAM;AACzC,WAAK,gBAAgB,uBAAuB;AAC5C,aAAO,WAAW,MAAM,IAAI,MAAM,UAAU,CAAC;AAAA,IAC/C;AACA,UAAM,OAAO,OAAO,IAAI,MAAM,KAAK;AACnC,UAAM,QAAQ,OAAO,IAAI,OAAO,KAAK;AACrC,UAAM,eAAe,OAAO,IAAI,eAAe;AAC/C,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,IACnB;AACA,WAAO,KAAK,aAAa,mCAAmC,iBAAiB,QAAQ,UAAU,EAAE,KAAK,WAAW,WAAS;AACxH,WAAK,gBAAgB,uBAAuB;AAC5C,WAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,aAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,IAC1C,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,wBAAwB,GAAG,QAAQ,YAAY;AAC7C,SAAK,cAAc,SAAS,QAAQ,yBAAyB;AAC7D,QAAI,CAAC,EAAE,QAAQ;AACb;AAAA,IACF;AACA,QAAI;AACJ,UAAM,aAAa,KAAK,WAAW,sBAAsB,MAAM;AAC/D,QAAI,YAAY;AACd,YAAM,WAAW,EAAE,OAAO,SAAS,EAAE,MAAM,GAAG;AAC9C,kBAAY,KAAK,kCAAkC,UAAU,QAAQ,UAAU;AAAA,IACjF,OAAO;AACL,kBAAY,KAAK,4BAA4B,kCAAkC,QAAQ,YAAY,EAAE,MAAM;AAAA,IAC7G;AACA,cAAU,UAAU;AAAA,MAClB,MAAM,qBAAmB;AACvB,aAAK,2CAA2C,KAAK,eAAe;AACpE,aAAK,iBAAiB,wBAAwB,MAAM;AAAA,MACtD;AAAA,MACA,OAAO,SAAO;AACZ,aAAK,cAAc,SAAS,QAAQ,YAAY,GAAG;AACnD,aAAK,2CAA2C,KAAK,IAAI;AACzD,aAAK,iBAAiB,wBAAwB,MAAM;AAAA,MACtD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,cAAc,kBAAkB,kCAAkC;AAAA,EAChF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oBAAmB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,cAAc;AACZ,SAAK,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAClE,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,qBAAqB,OAAO,kBAAkB;AACnD,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA,EACA,yBAAyB,QAAQ,YAAY,cAAc;AACzD,SAAK,cAAc,SAAS,QAAQ,8CAA8C;AAClF,WAAO,KAAK,WAAW,gCAAgC,QAAQ,YAAY,EAAE,KAAK,UAAU,SAAO;AACjG,aAAO,KAAK,qCAAqC,KAAK,QAAQ,UAAU;AAAA,IAC1E,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,qCAAqC,KAAK,QAAQ,YAAY;AAC5D,UAAM,gBAAgB,KAAK,mBAAmB,kBAAkB,MAAM;AACtE,SAAK,uBAAuB,QAAQ,UAAU;AAC9C,SAAK,cAAc,SAAS,QAAQ,iDAAiD,GAAG,EAAE;AAC1F,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,gBAAgB,MAAM;AAC1B,sBAAc,oBAAoB,QAAQ,aAAa;AACvD,aAAK,cAAc,SAAS,QAAQ,oCAAoC;AACxE,iBAAS,KAAK,IAAI;AAClB,iBAAS,SAAS;AAAA,MACpB;AACA,oBAAc,iBAAiB,QAAQ,aAAa;AACpD,oBAAc,eAAe,SAAS,QAAQ,OAAO,EAAE;AAAA,IACzD,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB,QAAQ,YAAY;AACzC,UAAM,aAAa,KAAK,OAAO;AAC/B,UAAM,qBAAqB,KAAK,SAAS,OAAO,UAAU,0BAA0B,OAAK;AACvF,UAAI,EAAE,WAAW,YAAY;AAC3B,2BAAmB;AACnB,4BAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AACD,UAAM,sBAAsB,KAAK,SAAS,OAAO,UAAU,6BAA6B,OAAK,KAAK,mBAAmB,wBAAwB,GAAG,QAAQ,UAAU,CAAC;AACnK,SAAK,SAAS,aAAa,cAAc,IAAI,YAAY,0BAA0B;AAAA,MACjF,QAAQ;AAAA,IACV,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAA6B;AAAA,IAChE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,6BAA4B;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oCAAN,MAAM,mCAAkC;AAAA,EACtC,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,kBAAkB,OAAO,eAAe;AAAA,EAC/C;AAAA,EACA,gCAAgC,QAAQ,YAAY,qBAAqB;AACvE,SAAK,cAAc,SAAS,QAAQ,iCAAiC;AACrE,QAAI,qBAAqB;AACzB,WAAO,KAAK,aAAa,oBAAoB,QAAQ,YAAY,mBAAmB,EAAE,KAAK,WAAW,WAAS;AAC7G,WAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,2BAAqB;AACrB,aAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,IAC1C,CAAC,GAAG,SAAS,MAAM,sBAAsB,KAAK,gBAAgB,uBAAuB,CAAC,CAAC;AAAA,EACzF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0CAA0C,mBAAmB;AAChF,aAAO,KAAK,qBAAqB,oCAAmC;AAAA,IACtE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mCAAkC;AAAA,MAC3C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,8BAA8B,OAAO,2BAA2B;AACrE,SAAK,oCAAoC,OAAO,iCAAiC;AACjF,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,uBAAuB,OAAO,oBAAoB;AAAA,EACzD;AAAA,EACA,iCAAiC,YAAY,eAAe;AAC1D,UAAM,gCAAgC,KAAK,iCAAiC,UAAU;AACtF,QAAI,8BAA8B,UAAU,GAAG;AAC7C;AAAA,IACF;AACA,QAAI,KAAK,gBAAgB,yBAAyB,GAAG;AACnD;AAAA,IACF;AACA,UAAM,uBAAuB,KAAK,kCAAkC,6BAA6B;AACjG,UAAM,qBAAqB,KAAK,gBAAgB,wBAAwB,oBAAoB,EAAE,KAAK,UAAU,MAAM;AACjH,YAAM,qCAAqC,CAAC;AAC5C,oCAA8B,QAAQ,YAAU;AAC9C,cAAM,aAAa,OAAO;AAC1B,cAAM,eAAe,KAAK,gBAAgB,QAAQ,UAAU;AAC5D,2CAAmC,UAAU,IAAI;AAAA,MACnD,CAAC;AACD,aAAO,SAAS,kCAAkC;AAAA,IACpD,CAAC,CAAC;AACF,SAAK,gBAAgB,4BAA4B,mBAAmB,KAAK,WAAW,WAAS,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU;AAAA,MAC1I,MAAM,yBAAuB;AAC3B,mBAAW,CAAC,UAAU,CAAC,KAAK,OAAO,QAAQ,mBAAmB,GAAG;AAC/D,eAAK,qBAAqB,uBAAuB,QAAQ,EAAE,UAAU,YAAU;AAC7E,iBAAK,cAAc,SAAS,QAAQ,wCAAwC;AAC5E,gBAAI,KAAK,WAAW,uCAAuC,MAAM,GAAG;AAClE,mBAAK,iBAAiB,wBAAwB,MAAM;AAAA,YACtD;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,OAAO,WAAS;AACd,aAAK,cAAc,SAAS,eAAe,wBAAwB,KAAK;AAAA,MAC1E;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,QAAQ,YAAY;AAClC,UAAM,0BAA0B,KAAK,sCAAsC,MAAM;AACjF,QAAI,CAAC,yBAAyB;AAC5B,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,UAAM,gBAAgB,KAAK,4BAA4B,QAAQ,UAAU;AACzE,SAAK,oBAAoB,UAAU,WAAW,kBAAkB;AAChE,WAAO,cAAc,KAAK,WAAW,WAAS;AAC5C,WAAK,cAAc,SAAS,QAAQ,wBAAwB,KAAK;AACjE,WAAK,oBAAoB,UAAU,WAAW,mBAAmB,KAAK;AACtE,WAAK,iBAAiB,wBAAwB,MAAM;AACpD,aAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,IAC1C,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,kCAAkC,+BAA+B;AAC/D,UAAM,SAAS,8BAA8B,OAAO,CAAC,MAAM,UAAU,KAAK,yBAAyB,MAAM,KAAK,yBAAyB,KAAK,OAAO,IAAI;AACvJ,WAAO,OAAO,yBAAyB;AAAA,EACzC;AAAA,EACA,iCAAiC,YAAY;AAC3C,WAAO,WAAW,OAAO,OAAK,EAAE,WAAW;AAAA,EAC7C;AAAA,EACA,4BAA4B,eAAe,YAAY;AACrD,SAAK,cAAc,SAAS,eAAe,0BAA0B;AACrE,WAAO,KAAK,qBAAqB,uBAAuB,cAAc,QAAQ,EAAE,KAAK,UAAU,YAAU;AACvG,UAAI,CAAC,QAAQ,aAAa;AACxB,aAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,eAAO,GAAG,IAAI;AAAA,MAChB;AACA,WAAK,iBAAiB,sBAAsB,MAAM;AAClD,UAAI,KAAK,WAAW,uCAAuC,MAAM,GAAG;AAElE,cAAM,sBAAsB,KAAK,0BAA0B,KAAK,8BAA8B,MAAM,KAAK,CAAC;AAC1G,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,eAAe,kCAChB,kCACA;AAGL,eAAO,KAAK,kCAAkC,gCAAgC,QAAQ,YAAY,YAAY;AAAA,MAChH;AAEA,YAAM,eAAe,KAAK,0BAA0B,KAAK,kCAAkC,MAAM;AACjG,aAAO,KAAK,4BAA4B,yBAAyB,QAAQ,YAAY,YAAY;AAAA,IACnG,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,sCAAsC,QAAQ;AAC5C,UAAM,UAAU,KAAK,iBAAiB,WAAW,MAAM;AACvD,UAAM,uBAAuB,KAAK,iBAAiB,qBAAqB,MAAM;AAC9E,UAAM,uBAAuB,KAAK,iBAAiB,qBAAqB,MAAM;AAC9E,UAAM,oBAAoB,KAAK,YAAY,qBAAqB,MAAM;AACtE,SAAK,cAAc,SAAS,QAAQ,iCAAiC,oBAAoB,2BAA2B,oBAAoB,mBAAmB,CAAC,CAAC,OAAO,oBAAoB,CAAC,CAAC,iBAAiB,EAAE;AAC7M,UAAM,mBAAmB,CAAC,CAAC,qBAAqB,CAAC,wBAAwB,CAAC,CAAC,WAAW,CAAC;AACvF,QAAI,CAAC,kBAAkB;AACrB,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,KAAK,iBAAiB,wCAAwC,MAAM;AAC3F,UAAM,qBAAqB,KAAK,iBAAiB,oCAAoC,MAAM;AAC3F,WAAO,kBAAkB;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAA+B;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,+BAA8B;AAAA,MACvC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,qBAAqB;AAC3B,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,qBAAqB,OAAO,kBAAkB;AACnD,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,8BAA8B,OAAO,2BAA2B;AACrE,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,oCAAoC,OAAO,iCAAiC;AACjF,SAAK,cAAc,OAAO,WAAW;AAAA,EACvC;AAAA,EACA,wBAAwB,QAAQ,YAAY,mBAAmB;AAC7D,QAAI,CAAC,QAAQ;AACX,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,SAAK,oBAAoB,mBAAmB,MAAM;AAClD,WAAO,KAAK,oBAAoB,QAAQ,YAAY,iBAAiB,EAAE,KAAK,IAAI,MAAM,KAAK,iBAAiB,wBAAwB,MAAM,CAAC,CAAC;AAAA,EAC9I;AAAA,EACA,oBAAoB,QAAQ,YAAY,mBAAmB;AACzD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,kCAChB,kCACA;AAEL,QAAI,KAAK,WAAW,uCAAuC,MAAM,GAAG;AAClE,aAAO,KAAK,oBAAoB,QAAQ,YAAY,YAAY,EAAE,KAAK,IAAI,MAAM;AAC/E,cAAM,kBAAkB,KAAK,iBAAiB,0BAA0B,MAAM;AAC9E,YAAI,iBAAiB;AACnB,iBAAO;AAAA,YACL,SAAS,KAAK,iBAAiB,WAAW,MAAM;AAAA,YAChD,aAAa,KAAK,iBAAiB,eAAe,MAAM;AAAA,YACxD,UAAU,KAAK,YAAY,qBAAqB,MAAM;AAAA,YACtD;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,cAAc;AAAA,UACd,UAAU;AAAA,UACV,SAAS;AAAA,UACT,aAAa;AAAA,UACb;AAAA,QACF;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,+BAA+B,KAAK;AACzD,WAAO,SAAS,CAAC,KAAK,oBAAoB,QAAQ,YAAY,iBAAiB,GAAG,KAAK,mBAAmB,mCAAmC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,WAAW,GAAG,UAAU,YAAU;AAC1M,aAAO,OAAO,KAAK,SAAS,CAAC,OAAO,UAAU;AAC5C,cAAM,kBAAkB;AACxB,cAAM,iBAAiB,QAAQ;AAC/B,YAAI,EAAE,iBAAiB,iBAAiB,iBAAiB,oBAAoB;AAC3E,iBAAO,WAAW,MAAM,IAAI,MAAM,KAAK,CAAC;AAAA,QAC1C;AACA,aAAK,cAAc,SAAS,QAAQ,yCAAyC,cAAc,EAAE;AAC7F,aAAK,iBAAiB,wBAAwB,MAAM;AACpD,eAAO,MAAM,iBAAiB,eAAe;AAAA,MAC/C,CAAC,CAAC;AAAA,IACJ,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,eAAe,MAAM;AAChC,YAAM,kBAAkB,KAAK,iBAAiB,0BAA0B,MAAM;AAC9E,UAAI,iBAAiB;AACnB,eAAO;AAAA,UACL,SAAS,iBAAiB,YAAY,YAAY;AAAA,UAClD,aAAa,iBAAiB,YAAY,gBAAgB;AAAA,UAC1D,UAAU,KAAK,YAAY,qBAAqB,MAAM;AAAA,UACtD;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oBAAoB,mBAAmB,QAAQ;AAC7C,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,mBAAmB;AACrB,UAAI,iBAAiB;AACnB,aAAK,0BAA0B,MAAM,8BAA8B,mBAAmB,MAAM;AAAA,MAC9F,OAAO;AACL,aAAK,0BAA0B,MAAM,kCAAkC,mBAAmB,MAAM;AAAA,MAClG;AAAA,IACF;AAAA,EACF;AAAA,EACA,oBAAoB,QAAQ,YAAY,mBAAmB;AACzD,UAAM,uBAAuB,KAAK,iBAAiB,qBAAqB,MAAM;AAC9E,SAAK,cAAc,SAAS,QAAQ,iCAAiC,oBAAoB,EAAE;AAC3F,UAAM,mBAAmB,CAAC;AAC1B,QAAI,CAAC,kBAAkB;AACrB,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,WAAO,KAAK,qBAAqB,oCAAoC,MAAM,EAAE,KAAK,UAAU,MAAM;AAChG,WAAK,iBAAiB,sBAAsB,MAAM;AAClD,UAAI,KAAK,WAAW,uCAAuC,MAAM,GAAG;AAElE,eAAO,KAAK,kCAAkC,gCAAgC,QAAQ,YAAY,iBAAiB;AAAA,MACrH;AACA,aAAO,KAAK,4BAA4B,yBAAyB,QAAQ,YAAY,iBAAiB;AAAA,IACxG,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sCAAsC;AAE5C,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,eAAe,OAAO,mBAAmB;AAC9C,SAAK,OAAO,OAAO,MAAM;AACzB,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,uBAAuB;AAC5B,SAAK,4BAA4B;AACjC,SAAK,oBAAoB;AACzB,SAAK,sBAAsB;AAC3B,SAAK,oBAAoB;AACzB,SAAK,wBAAwB;AAC7B,SAAK,+BAA+B,IAAI,gBAAgB,KAAK;AAAA,EAC/D;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,KAAK,6BAA6B,aAAa;AAAA,EACxD;AAAA,EACA,cAAc;AACZ,SAAK,KAAK;AACV,UAAM,sBAAsB,KAAK,SAAS;AAC1C,QAAI,uBAAuB,KAAK,4BAA4B;AAC1D,0BAAoB,oBAAoB,WAAW,KAAK,4BAA4B,KAAK;AAAA,IAC3F;AAAA,EACF;AAAA,EACA,yBAAyB,eAAe;AACtC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,QAAQ,iBAAiB;AAAA,EAClC;AAAA,EACA,MAAM,eAAe;AACnB,QAAI,CAAC,CAAC,KAAK,2BAA2B;AACpC;AAAA,IACF;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,SAAK,kBAAkB,UAAU,aAAa;AAAA,EAChD;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,2BAA2B;AACnC;AAAA,IACF;AACA,SAAK,wBAAwB;AAC7B,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,mBAAmB,eAAe;AAChC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,QAAQ,iBAAiB,KAAK,KAAK;AAAA,EAC5C;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,cAAc,kBAAkB,mCAAmC;AAAA,EACjF;AAAA,EACA,KAAK,eAAe;AAClB,QAAI,KAAK,oBAAoB,KAAK,wBAAwB,KAAK,IAAI,GAAG;AACpE,aAAO,GAAG,MAAS;AAAA,IACrB;AACA,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,QAAI,CAAC,wBAAwB;AAC3B,WAAK,cAAc,WAAW,eAAe,oFAAoF;AACjI,aAAO,GAAG;AAAA,IACZ;AACA,UAAM,iBAAiB,KAAK,kBAAkB,aAAa;AAI3D,SAAK,yBAAyB,aAAa;AAC3C,UAAM,qBAAqB,uBAAuB;AAClD,UAAM,gBAAgB,eAAe;AACrC,QAAI,CAAC,oBAAoB;AACvB,WAAK,cAAc,WAAW,eAAe,gFAAgF;AAAA,IAC/H;AACA,QAAI,CAAC,eAAe;AAClB,WAAK,cAAc,WAAW,eAAe,wEAAwE;AAAA,IACvH,OAAO;AACL,oBAAc,SAAS,QAAQ,kBAAkB;AAAA,IACnD;AACA,WAAO,IAAI,WAAW,cAAY;AAChC,qBAAe,SAAS,MAAM;AAC5B,aAAK,oBAAoB,KAAK,IAAI;AAClC,iBAAS,KAAK;AACd,iBAAS,SAAS;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,UAAU,eAAe;AACzC,SAAK,sBAAsB;AAC3B,UAAM,yBAAyB,MAAM;AACnC,WAAK,KAAK,aAAa,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACrD,cAAM,iBAAiB,KAAK,kBAAkB;AAC9C,YAAI,kBAAkB,UAAU;AAC9B,eAAK,cAAc,SAAS,eAAe,8BAA8B,QAAQ,wBAAwB,cAAc,GAAG;AAC1H,gBAAM,eAAe,KAAK,0BAA0B,KAAK,iBAAiB,aAAa;AACvF,gBAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,gBAAM,gBAAgB,eAAe;AACrC,cAAI,gBAAgB,wBAAwB,sBAAsB,eAAe;AAC/E,kBAAM,eAAe,IAAI,IAAI,uBAAuB,kBAAkB,GAAG;AACzE,iBAAK;AACL,0BAAc,YAAY,WAAW,MAAM,cAAc,YAAY;AAAA,UACvE,OAAO;AACL,iBAAK,cAAc,SAAS,eAAe,oCAAoC,YAAY,kCAAkC,KAAK,UAAU,wBAAwB,MAAM,CAAC,CAAC,GAAG;AAC/K,iBAAK,6BAA6B,KAAK,IAAI;AAAA,UAC7C;AAAA,QACF,OAAO;AACL,eAAK,cAAc,WAAW,eAAe;AAAA,6BAC1B,QAAQ,wBAAwB,cAAc,GAAG;AAAA,QACtE;AAEA,YAAI,KAAK,sBAAsB,GAAG;AAChC,eAAK,cAAc,SAAS,eAAe;AAAA,qDACA,KAAK,mBAAmB,wBAAwB;AAAA,QAC7F;AACA,aAAK,KAAK,kBAAkB,MAAM;AAChC,eAAK,4BAA4B,KAAK,UAAU,aAAa,WAAW,MAAM,KAAK,KAAK,IAAI,sBAAsB,GAAG,KAAK,iBAAiB,KAAK;AAAA,QAClJ,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,2BAAuB;AAAA,EACzB;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,8BAA8B,MAAM;AAC3C,mBAAa,KAAK,yBAAyB;AAC3C,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,eAAe,eAAe,GAAG;AAC/B,UAAM,iBAAiB,KAAK,kBAAkB;AAC9C,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,UAAM,aAAa,CAAC,CAAC,wBAAwB,oBAAoB,WAAW,EAAE,MAAM;AACpF,SAAK,sBAAsB;AAC3B,QAAI,kBAAkB,cAAc,EAAE,WAAW,eAAe,eAAe;AAC7E,UAAI,EAAE,SAAS,SAAS;AACtB,aAAK,cAAc,WAAW,eAAe,wDAAwD;AAAA,MACvG,WAAW,EAAE,SAAS,WAAW;AAC/B,aAAK,cAAc,SAAS,eAAe,kBAAkB,CAAC,oCAAoC;AAClG,aAAK,uBAAuB;AAC5B,aAAK,aAAa,UAAU,WAAW,sBAAsB,EAAE,IAAI;AACnE,aAAK,6BAA6B,KAAK,IAAI;AAAA,MAC7C,OAAO;AACL,aAAK,aAAa,UAAU,WAAW,sBAAsB,EAAE,IAAI;AACnE,aAAK,cAAc,SAAS,eAAe,kBAAkB,EAAE,IAAI,oCAAoC;AAAA,MACzG;AAAA,IACF;AAAA,EACF;AAAA,EACA,yBAAyB,eAAe;AACtC,SAAK,6BAA6B,KAAK,eAAe,KAAK,MAAM,aAAa;AAC9E,UAAM,cAAc,KAAK,SAAS;AAClC,QAAI,aAAa;AACf,kBAAY,iBAAiB,WAAW,KAAK,4BAA4B,KAAK;AAAA,IAChF;AAAA,EACF;AAAA,EACA,kBAAkB,eAAe;AAC/B,WAAO,KAAK,kBAAkB,KAAK,KAAK,cAAc,sBAAsB,qCAAqC,aAAa;AAAA,EAChI;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,WAAW,OAAO,QAAQ;AAC/B,SAAK,qBAAqB;AAC1B,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,kBAAkB,IAAI,QAAQ;AAAA,EACrC;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,gBAAgB,aAAa;AAAA,EAC3C;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,mBAAmB,QAAQ;AACzB,QAAI,KAAK,wBAAwB,GAAG;AAClC,YAAM,QAAQ,KAAK,0BAA0B,KAAK,KAAK,oBAAoB,MAAM;AACjF,YAAM,mBAAmB,KAAK;AAC9B,UAAI,CAAC,kBAAkB;AACrB,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,iBAAiB,MAAM,KAAK,iBAAiB,WAAW,oBAAoB,QAAQ,KAAK;AAAA,IAC1G;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,KAAK,cAAc,QAAQ;AACnC,UAAM,gBAAgB,KAAK,WAAW,YAAY;AAClD,SAAK,0BAA0B,MAAM,KAAK,oBAAoB,QAAQ,MAAM;AAC5E,UAAM,mBAAmB,KAAK;AAC9B,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,QAAI,CAAC,KAAK;AACR,WAAK,cAAc,SAAS,QAAQ,oCAAoC;AACxE;AAAA,IACF;AACA,SAAK,QAAQ,iBAAiB,KAAK,KAAK,UAAU,aAAa;AAC/D,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,0BAA0B,OAAO,KAAK,oBAAoB,MAAM;AACrE,WAAK,cAAc,SAAS,QAAQ,sBAAsB;AAC1D;AAAA,IACF;AACA,SAAK,cAAc,SAAS,QAAQ,2BAA2B,GAAG;AAClE,UAAM,WAAW,WAAS;AACxB,UAAI,CAAC,OAAO,QAAQ,OAAO,MAAM,SAAS,UAAU;AAClD,aAAK,QAAQ,UAAU,MAAM;AAC7B;AAAA,MACF;AACA,WAAK,cAAc,SAAS,QAAQ,0CAA0C,MAAM,IAAI;AACxF,WAAK,gBAAgB,KAAK;AAAA,QACxB,YAAY;AAAA,QACZ,aAAa,MAAM;AAAA,MACrB,CAAC;AACD,WAAK,QAAQ,UAAU,MAAM;AAAA,IAC/B;AACA,qBAAiB,iBAAiB,WAAW,UAAU,KAAK;AAC5D,SAAK,SAAS,iBAAiB,YAAY,MAAM;AAC/C,UAAI,KAAK,OAAO,QAAQ;AACtB,aAAK,gBAAgB,KAAK;AAAA,UACxB,YAAY;AAAA,UACZ,aAAa;AAAA,QACf,CAAC;AACD,aAAK,QAAQ,UAAU,MAAM;AAAA,MAC/B;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAAA,EACA,wBAAwB,KAAK,QAAQ;AACnC,UAAM,mBAAmB,KAAK;AAC9B,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,QAAI,iBAAiB,QAAQ;AAC3B,YAAM,OAAO,iBAAiB,SAAS;AACvC,WAAK,YAAY,KAAK,MAAM,MAAM;AAAA,IACpC;AAAA,EACF;AAAA,EACA,QAAQ,UAAU,QAAQ;AACxB,UAAM,mBAAmB,KAAK;AAC9B,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,qBAAiB,oBAAoB,WAAW,UAAU,KAAK;AAC/D,qBAAiB,cAAc,KAAK,MAAM;AAC1C,QAAI,KAAK,OAAO;AACd,WAAK,0BAA0B,OAAO,KAAK,oBAAoB,MAAM;AACrE,WAAK,MAAM,MAAM;AACjB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,YAAY,KAAK,MAAM,QAAQ;AAC7B,UAAM,mBAAmB,KAAK;AAC9B,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,QAAI,CAAC,KAAK;AACR,WAAK,cAAc,SAAS,QAAQ,4CAA4C,GAAG,GAAG;AACtF;AAAA,IACF;AACA,qBAAiB,OAAO,YAAY,KAAK,IAAI;AAAA,EAC/C;AAAA,EACA,WAAW,cAAc;AACvB,UAAM,sBAAsB;AAAA,MAC1B,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,UAAM,UAAU,kCACX,sBACC,gBAAgB,CAAC;AAEvB,UAAM,mBAAmB,KAAK;AAC9B,QAAI,CAAC,kBAAkB;AACrB,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,QAAQ,SAAS,oBAAoB;AACnD,UAAM,SAAS,QAAQ,UAAU,oBAAoB;AACrD,UAAM,OAAO,iBAAiB,cAAc,iBAAiB,aAAa,SAAS;AACnF,UAAM,MAAM,iBAAiB,aAAa,iBAAiB,cAAc,UAAU;AACnF,YAAQ,OAAO;AACf,YAAQ,MAAM;AACd,WAAO,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG;AAAA,EAC1H;AAAA,EACA,0BAA0B;AACxB,WAAO,OAAO,cAAc,eAAe,UAAU,iBAAiB,OAAO,YAAY;AAAA,EAC3F;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA,EACA,4BAA4B,KAAK;AAC/B,UAAM,aAAa,OAAO,KAAK,cAAc;AAC7C,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,UAAM,YAAY,IAAI,IAAI,UAAU;AACpC,UAAM,YAAY,IAAI,gBAAgB,UAAU,MAAM;AACtD,WAAO,UAAU,IAAI,OAAO;AAAA,EAC9B;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,UAAU,aAAa,SAAS,SAAS,KAAK;AAAA,EAC5D;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAmB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,cAAc;AACZ,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,oBAAoB,OAAO,iBAAiB;AACjD,SAAK,qBAAqB,OAAO,kBAAkB;AACnD,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,wBAAwB,OAAO,qBAAqB;AACzD,SAAK,gCAAgC,OAAO,6BAA6B;AACzE,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,sBAAsB,OAAO,mBAAmB;AAAA,EACvD;AAAA,EACA,UAAU,eAAe,KAAK;AAC5B,UAAM,oBAAoB,KAAK,kBAAkB,4BAA4B,GAAG;AAChF,WAAO,QAAQ,iBAAiB,IAAI,KAAK,6BAA6B,CAAC,aAAa,GAAG,iBAAiB,IAAI;AAAA,EAC9G;AAAA,EACA,UAAU,eAAe,YAAY,KAAK;AACxC,QAAI,CAAC,eAAe;AAClB,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,SAAK,oBAAoB,UAAU,WAAW,YAAY;AAC1D,UAAM,oBAAoB,KAAK,kBAAkB,4BAA4B,GAAG;AAChF,UAAM,SAAS,KAAK,UAAU,eAAe,GAAG;AAChD,QAAI,CAAC,QAAQ;AACX,aAAO,WAAW,MAAM,IAAI,MAAM,4CAA4C,iBAAiB,EAAE,CAAC;AAAA,IACpG;AACA,WAAO,KAAK,oBAAoB,eAAe,YAAY,GAAG;AAAA,EAChE;AAAA,EACA,kBAAkB,YAAY,KAAK;AACjC,UAAM,oBAAoB,KAAK,kBAAkB,4BAA4B,GAAG;AAChF,QAAI,mBAAmB;AACrB,YAAM,SAAS,KAAK,6BAA6B,YAAY,iBAAiB;AAC9E,UAAI,CAAC,QAAQ;AACX,eAAO,WAAW,MAAM,IAAI,MAAM,4CAA4C,iBAAiB,EAAE,CAAC;AAAA,MACpG;AACA,aAAO,KAAK,4BAA4B,YAAY,QAAQ,GAAG;AAAA,IACjE;AACA,UAAM,UAAU;AAChB,UAAM,aAAa,QAAQ,IAAI,OAAK,KAAK,oBAAoB,GAAG,SAAS,GAAG,CAAC;AAC7E,WAAO,SAAS,UAAU;AAAA,EAC5B;AAAA,EACA,yBAAyB,eAAe,YAAY;AAClD,QAAI,CAAC,eAAe;AAClB,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,WAAO,KAAK,oBAAoB,eAAe,UAAU,EAAE,KAAK,UAAU,mBAAiB;AACzF,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,iBAAiB;AACnB,eAAO,GAAG,aAAa;AAAA,MACzB;AACA,aAAO,KAAK,sBAAsB,oBAAoB,eAAe,UAAU,EAAE,KAAK,IAAI,sCAAoC;AAC5H,YAAI,kCAAkC,iBAAiB;AACrD,eAAK,+BAA+B,eAAe,UAAU;AAAA,QAC/D;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oBAAoB,QAAQ,YAAY,KAAK;AAC3C,QAAI,CAAC,QAAQ;AACX,YAAM,eAAe;AACrB,WAAK,cAAc,SAAS,QAAQ,YAAY;AAChD,YAAM,SAAS;AAAA,QACb,iBAAiB;AAAA,QACjB;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,GAAG,MAAM;AAAA,IAClB;AACA,UAAM,aAAa,OAAO,KAAK,kBAAkB,cAAc;AAC/D,QAAI,CAAC,YAAY;AACf,YAAM,eAAe;AACrB,WAAK,cAAc,SAAS,QAAQ,YAAY;AAChD,YAAM,SAAS;AAAA,QACb,iBAAiB;AAAA,QACjB;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,GAAG,MAAM;AAAA,IAClB;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,SAAK,cAAc,SAAS,QAAQ,wBAAwB,QAAQ,YAAY,SAAS,GAAG;AAC5F,QAAI,KAAK,aAAa,mBAAmB,MAAM,GAAG;AAChD,WAAK,aAAa,wBAAwB,YAAY,MAAM;AAC5D,YAAM,SAAS;AAAA,QACb,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,aAAO,GAAG,MAAM;AAAA,IAClB;AACA,UAAM,aAAa,KAAK,gBAAgB,WAAW,UAAU;AAC7D,SAAK,cAAc,SAAS,QAAQ,mCAAmC,UAAU,GAAG;AACpF,UAAM,YAAY,aAAa,KAAK,gBAAgB,4BAA4B,YAAY,QAAQ,UAAU,IAAI,GAAG,CAAC,CAAC;AACvH,WAAO,UAAU,KAAK,IAAI,MAAM;AAC9B,YAAM,kBAAkB,KAAK,iBAAiB,0BAA0B,MAAM;AAC9E,WAAK,cAAc,SAAS,QAAQ,4DAA4D,eAAe,EAAE;AACjH,UAAI,iBAAiB;AACnB,aAAK,+BAA+B,QAAQ,UAAU;AACtD,YAAI,CAAC,YAAY;AACf,eAAK,iBAAiB,6BAA6B,UAAU;AAC7D,eAAK,YAAY,wBAAwB,QAAQ,UAAU;AAAA,QAC7D;AAAA,MACF;AACA,WAAK,oBAAoB,UAAU,WAAW,oBAAoB;AAClE,YAAM,SAAS;AAAA,QACb;AAAA,QACA,UAAU,KAAK,YAAY,qBAAqB,MAAM;AAAA,QACtD,aAAa,KAAK,iBAAiB,eAAe,MAAM;AAAA,QACxD,SAAS,KAAK,iBAAiB,WAAW,MAAM;AAAA,QAChD;AAAA,MACF;AACA,aAAO;AAAA,IACT,CAAC,GAAG,IAAI,CAAC;AAAA,MACP;AAAA,IACF,MAAM;AACJ,UAAI,iBAAiB;AACnB,aAAK,iBAAiB,mCAAmC,MAAM;AAAA,MACjE;AAAA,IACF,CAAC,GAAG,WAAW,CAAC;AAAA,MACd;AAAA,IACF,MAAM;AACJ,WAAK,cAAc,SAAS,QAAQ,OAAO;AAC3C,WAAK,oBAAoB,UAAU,WAAW,+BAA+B,OAAO;AACpF,YAAM,SAAS;AAAA,QACb,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb;AAAA,MACF;AACA,aAAO,GAAG,MAAM;AAAA,IAClB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,+BAA+B,QAAQ,YAAY;AACjD,QAAI,KAAK,oBAAoB,yBAAyB,MAAM,GAAG;AAC7D,WAAK,oBAAoB,MAAM,MAAM;AAAA,IACvC;AACA,SAAK,8BAA8B,iCAAiC,YAAY,MAAM;AACtF,QAAI,KAAK,mBAAmB,wBAAwB,MAAM,GAAG;AAC3D,WAAK,mBAAmB,kBAAkB,MAAM;AAAA,IAClD;AAAA,EACF;AAAA,EACA,6BAA6B,gBAAgB,cAAc;AACzD,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AACA,eAAW,UAAU,gBAAgB;AACnC,YAAM,cAAc,KAAK,0BAA0B,KAAK,oBAAoB,MAAM;AAClF,UAAI,gBAAgB,cAAc;AAChC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,4BAA4B,gBAAgB,cAAc,KAAK;AAC7D,UAAM,kBAAkB,eAAe,OAAO,OAAK,EAAE,aAAa,aAAa,QAAQ;AACvF,UAAM,sBAAsB,KAAK,oBAAoB,cAAc,gBAAgB,GAAG;AACtF,UAAM,wBAAwB,gBAAgB,IAAI,YAAU;AAC1D,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,aAAO,KAAK,oBAAoB,QAAQ,gBAAgB,WAAW;AAAA,IACrE,CAAC;AACD,WAAO,SAAS,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAkB;AAAA,IACrD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,WAAW,OAAO,QAAQ;AAAA,EACjC;AAAA,EACA,WAAW,KAAK;AACd,SAAK,SAAS,SAAS,OAAO;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,aAAa,OAAO,UAAU;AAAA,EACrC;AAAA,EACA,2BAA2B,eAAe;AACxC,QAAI,KAAK,WAAW,6BAA6B,aAAa,GAAG;AAC/D,aAAO;AAAA,IACT;AACA,QAAI,KAAK,WAAW,sBAAsB,aAAa,GAAG;AACxD,aAAO;AAAA,IACT;AACA,SAAK,cAAc,WAAW,eAAe,4FAA4F;AACzI,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAA+B;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,+BAA8B;AAAA,MACvC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,4BAA4B,OAAO,yBAAyB;AAAA,EACnE;AAAA,EACA,eAAe,eAAe,aAAa;AACzC,QAAI,UAAU,IAAI,YAAY;AAC9B,cAAU,QAAQ,IAAI,gBAAgB,mCAAmC;AACzE,UAAM,yBAAyB,KAAK,0BAA0B,KAAK,0BAA0B,aAAa;AAC1G,QAAI,CAAC,wBAAwB;AAC3B,aAAO,WAAW,MAAM,IAAI,MAAM,0EAA0E,CAAC;AAAA,IAC/G;AACA,UAAM,cAAc,uBAAuB;AAC3C,QAAI,CAAC,aAAa;AAChB,aAAO,WAAW,MAAM,IAAI,MAAM,yDAAyD,CAAC;AAAA,IAC9F;AACA,WAAO,KAAK,WAAW,gCAAgC,eAAe,WAAW,EAAE,KAAK,UAAU,UAAQ;AACxG,aAAO,KAAK,YAAY,KAAK,aAAa,MAAM,eAAe,OAAO,EAAE,KAAK,MAAM,CAAC,GAAG,IAAI,cAAY;AACrG,aAAK,cAAc,SAAS,eAAe,kBAAkB,QAAQ;AACrE,eAAO;AAAA,UACL,WAAW,SAAS;AAAA,UACpB,YAAY,SAAS;AAAA,QACvB;AAAA,MACF,CAAC,GAAG,WAAW,WAAS;AACtB,cAAM,eAAe;AACrB,aAAK,cAAc,SAAS,eAAe,cAAc,KAAK;AAC9D,eAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,MACjD,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAY;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,YAAW;AAAA,MACpB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,gCAAgC,OAAO,6BAA6B;AACzE,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,aAAa,OAAO,UAAU;AAAA,EACrC;AAAA,EACA,SAAS,eAAe,aAAa;AACnC,QAAI,CAAC,KAAK,8BAA8B,2BAA2B,aAAa,GAAG;AACjF,WAAK,cAAc,SAAS,eAAe,wBAAwB;AACnE;AAAA,IACF;AACA,SAAK,cAAc,SAAS,eAAe,yCAAyC;AACpF,SAAK,qBAAqB,oCAAoC,aAAa,EAAE,KAAK,UAAU,MAAM,KAAK,WAAW,eAAe,eAAe,WAAW,CAAC,CAAC,EAAE,UAAU,cAAY;AACnL,WAAK,cAAc,SAAS,eAAe,kBAAkB,QAAQ;AACrE,YAAM,MAAM,KAAK,WAAW,mBAAmB,SAAS,YAAY,aAAa;AACjF,WAAK,cAAc,SAAS,eAAe,qBAAqB,GAAG;AACnE,UAAI,CAAC,KAAK;AACR,aAAK,cAAc,SAAS,eAAe,mCAAmC,SAAS,UAAU,MAAM,GAAG,GAAG;AAC7G;AAAA,MACF;AACA,UAAI,aAAa,YAAY;AAC3B,oBAAY,WAAW,GAAG;AAAA,MAC5B,OAAO;AACL,aAAK,gBAAgB,WAAW,GAAG;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,eAAe,YAAY,aAAa,cAAc;AACtE,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,KAAK,8BAA8B,2BAA2B,aAAa,GAAG;AACjF,YAAM,eAAe;AACrB,WAAK,cAAc,SAAS,eAAe,YAAY;AACvD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD;AACA,SAAK,cAAc,SAAS,eAAe,oDAAoD;AAC/F,WAAO,KAAK,qBAAqB,oCAAoC,aAAa,EAAE,KAAK,UAAU,MAAM,KAAK,WAAW,eAAe,eAAe,WAAW,CAAC,GAAG,UAAU,cAAY;AAC1L,WAAK,cAAc,SAAS,eAAe,iBAAiB,QAAQ,EAAE;AACtE,YAAM,MAAM,KAAK,WAAW,mBAAmB,SAAS,YAAY,aAAa;AACjF,WAAK,cAAc,SAAS,eAAe,qBAAqB,GAAG;AACnE,UAAI,CAAC,KAAK;AACR,cAAM,eAAe,mCAAmC,SAAS,UAAU;AAC3E,aAAK,cAAc,SAAS,eAAe,YAAY;AACvD,eAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,MACjD;AACA,WAAK,aAAa,UAAU,KAAK,cAAc,aAAa;AAC5D,aAAO,KAAK,aAAa,QAAQ,KAAK,KAAK,CAAC,GAAG,UAAU,YAAU;AACjE,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,YAAY;AACd,iBAAO,GAAG;AAAA,YACR,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,UAAU;AAAA,YACV,SAAS;AAAA,YACT,aAAa;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO,KAAK,iBAAiB,UAAU,eAAe,YAAY,WAAW;AAAA,MAC/E,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,gCAAgC,OAAO,6BAA6B;AACzE,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,mBAAmB,OAAO,gBAAgB;AAAA,EACjD;AAAA,EACA,uBAAuB,eAAe,YAAY,aAAa,cAAc;AAC3E,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,KAAK,8BAA8B,2BAA2B,aAAa,GAAG;AACjF,YAAM,eAAe;AACrB,WAAK,cAAc,SAAS,eAAe,YAAY;AACvD,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD;AACA,SAAK,cAAc,SAAS,eAAe,oDAAoD;AAC/F,WAAO,KAAK,qBAAqB,oCAAoC,aAAa,EAAE,KAAK,UAAU,MAAM,KAAK,WAAW,gBAAgB,eAAe,WAAW,CAAC,GAAG,IAAI,aAAW,KAAK,aAAa,UAAU,SAAS,cAAc,aAAa,CAAC,GAAG,UAAU,MAAM;AACxQ,aAAO,KAAK,aAAa,QAAQ,KAAK,KAAK,CAAC,GAAG,UAAU,YAAU;AACjE,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,YAAY;AACd,gBAAM,WAAW;AAAA,YACf,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,UAAU;AAAA,YACV,SAAS;AAAA,YACT,aAAa;AAAA,YACb;AAAA,UACF;AACA,iBAAO,GAAG,QAAQ;AAAA,QACpB;AACA,eAAO,KAAK,iBAAiB,UAAU,eAAe,YAAY,WAAW;AAAA,MAC/E,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAmB;AAAA,IACtD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,gCAAgC,OAAO,6BAA6B;AACzE,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,mBAAmB,OAAO,gBAAgB;AAAA,EACjD;AAAA,EACA,cAAc,eAAe,aAAa;AACxC,QAAI,CAAC,KAAK,8BAA8B,2BAA2B,aAAa,GAAG;AACjF,WAAK,cAAc,SAAS,eAAe,wBAAwB;AACnE;AAAA,IACF;AACA,SAAK,cAAc,SAAS,eAAe,yCAAyC;AACpF,SAAK,iBAAiB,sBAAsB,aAAa;AACzD,SAAK,qBAAqB,oCAAoC,aAAa,EAAE,UAAU,MAAM;AAC3F,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,eAAe,CAAC;AACpB,WAAK,iBAAiB,wBAAwB,aAAa;AAC3D,WAAK,WAAW,gBAAgB,eAAe,WAAW,EAAE,UAAU,SAAO;AAC3E,YAAI,CAAC,KAAK;AACR,eAAK,cAAc,SAAS,eAAe,wBAAwB,GAAG;AACtE;AAAA,QACF;AACA,YAAI,YAAY;AACd,qBAAW,GAAG;AAAA,QAChB,OAAO;AACL,eAAK,gBAAgB,WAAW,GAAG;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAsB;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,sBAAqB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AACZ,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,oBAAoB,OAAO,iBAAiB;AACjD,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,eAAe,OAAO,YAAY;AAAA,EACzC;AAAA,EACA,MAAM,eAAe,aAAa;AAChC,QAAI,CAAC,eAAe;AAClB,YAAM,IAAI,MAAM,6DAA6D;AAAA,IAC/E;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,cAAc;AAC7B,WAAK,0BAA0B,MAAM,kCAAkC,YAAY,cAAc,aAAa;AAAA,IAChH;AACA,QAAI,gCAAgC;AAClC,aAAO,KAAK,gBAAgB,SAAS,eAAe,WAAW;AAAA,IACjE,OAAO;AACL,aAAO,KAAK,qBAAqB,cAAc,eAAe,WAAW;AAAA,IAC3E;AAAA,EACF;AAAA,EACA,eAAe,eAAe,YAAY,aAAa,cAAc;AACnE,QAAI,CAAC,eAAe;AAClB,YAAM,IAAI,MAAM,6DAA6D;AAAA,IAC/E;AACA,UAAM,mBAAmB,KAAK,aAAa,mBAAmB,aAAa;AAC3E,QAAI,kBAAkB;AACpB,aAAO,GAAG;AAAA,QACR,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,aAAa,cAAc;AAC7B,WAAK,0BAA0B,MAAM,kCAAkC,YAAY,cAAc,aAAa;AAAA,IAChH;AACA,QAAI,gCAAgC;AAClC,aAAO,KAAK,gBAAgB,kBAAkB,eAAe,YAAY,aAAa,YAAY;AAAA,IACpG;AACA,WAAO,KAAK,kBAAkB,uBAAuB,eAAe,YAAY,aAAa,YAAY;AAAA,EAC3G;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,SAAS,OAAO,MAAM;AAAA,EAC7B;AAAA,EACA,UAAU;AACR,UAAM,MAAM,KAAK,OAAO,qBAAqB,GAAG,aAAa,SAAS,EAAE,UAAU,CAAC,KAAK;AACxF,WAAO,YAAY,KAAK,KAAK,sBAAsB,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,YAAY;AAAA,EACpH;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,WAAO,YAAY,MAAM,KAAK,KAAK,sBAAsB,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,YAAY;AAAA,EAC1H;AAAA,EACA,iBAAiB,OAAO,OAAO;AAC7B,WAAO,YAAY,MAAM,KAAK,KAAK,sBAAsB,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,YAAY;AAAA,EAC1H;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,YAAY,KAAK,sBAAsB,kBAAkB,kBAAkB,cAAc;AAChG,SAAO,qBAAqB,uBAAuB,EAAE,KAAK,UAAU,YAAU;AAC5E,UAAM,aAAa,qBAAqB,qBAAqB;AAC7D,WAAO,iBAAiB,UAAU,QAAQ,UAAU,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;AAAA,MACvE;AAAA,IACF,MAAM;AACJ,UAAI,iBAAiB;AACnB,yBAAiB,mCAAmC,MAAM;AAAA,MAC5D;AACA,UAAI,CAAC,iBAAiB;AACpB,yBAAiB,kBAAkB,QAAQ,GAAG;AAC9C,qBAAa,MAAM,MAAM;AAAA,MAC3B;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ,CAAC,CAAC;AACJ;AACA,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,cAAc;AACZ,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,SAAS,OAAO,MAAM;AAAA,EAC7B;AAAA,EACA,UAAU;AACR,UAAM,MAAM,KAAK,OAAO,qBAAqB,GAAG,aAAa,SAAS,EAAE,UAAU,CAAC,KAAK;AACxF,WAAO,UAAU,KAAK,KAAK,sBAAsB,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,YAAY;AAAA,EAClH;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,UAAM,cAAc,OAAO,OAAO;AAAA,MAChC,cAAc,MAAM;AAAA,IACtB,IAAI;AACJ,WAAO,UAAU,MAAM,KAAK,KAAK,sBAAsB,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,cAAc,WAAW;AAAA,EACrI;AAAA,EACA,iBAAiB,OAAO,OAAO;AAC7B,UAAM,cAAc,OAAO,OAAO;AAAA,MAChC,cAAc,MAAM;AAAA,IACtB,IAAI;AACJ,WAAO,UAAU,MAAM,KAAK,KAAK,sBAAsB,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,cAAc,WAAW;AAAA,EACrI;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAA6B;AAAA,IAChE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,6BAA4B;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAaH,SAAS,UAAU,KAAK,sBAAsB,kBAAkB,kBAAkB,cAAc,aAAa;AAC3G,SAAO,qBAAqB,uBAAuB,EAAE,KAAK,IAAI,mBAAiB;AAC7E,UAAM,kBAAkB,iBAAiB,0BAA0B,aAAa;AAChF,QAAI,iBAAiB;AACnB,uBAAiB,mCAAmC,aAAa;AAAA,IACnE;AACA,QAAI,CAAC,iBAAiB;AACpB,uBAAiB,kBAAkB,eAAe,GAAG;AACrD,UAAI,aAAa;AACf,qBAAa,MAAM,eAAe,WAAW;AAAA,MAC/C,OAAO;AACL,qBAAa,MAAM,aAAa;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,OAAO,CAAC,WAAW,SAAS,UAAU,OAAO,MAAM,QAAQ,IAAI,IAAI,aAAa,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;AAChH;AACA,IAAM,8BAAN,MAAM,6BAA4B;AAAA,EAChC,mCAAmC,OAAO,gBAAgB;AACxD,eAAW,UAAU,gBAAgB;AACnC,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,iBAAW,mBAAmB,gBAAgB,CAAC,GAAG;AAChD,YAAI,MAAM,WAAW,eAAe,GAAG;AACrC,iBAAO;AAAA,YACL,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,eAAe;AAAA,MACf,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,mBAAmB;AAC1E,aAAO,KAAK,qBAAqB,8BAA6B;AAAA,IAChE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,6BAA4B;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,8BAA8B,OAAO,2BAA2B;AAAA,EACvE;AAAA,EACA,UAAU,KAAK,MAAM;AACnB,WAAO,iBAAiB,KAAK,KAAK,QAAQ;AAAA,MACxC,sBAAsB,KAAK;AAAA,MAC3B,kBAAkB,KAAK;AAAA,MACvB,6BAA6B,KAAK;AAAA,MAClC,eAAe,KAAK;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAWH,SAAS,iBAAiB,KAAK,MAAM,MAAM;AACzC,MAAI,CAAC,KAAK,qBAAqB,oBAAoB,GAAG;AACpD,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,QAAM,oBAAoB,KAAK,qBAAqB,qBAAqB;AACzE,QAAM,sBAAsB,kBAAkB,IAAI,OAAK,EAAE,gBAAgB,CAAC,CAAC;AAC3E,QAAM,0BAA0B,aAAa,mBAAmB;AAChE,MAAI,wBAAwB,WAAW,GAAG;AACxC,SAAK,cAAc,SAAS,kBAAkB,CAAC,GAAG,+BAA+B;AACjF,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,KAAK,4BAA4B,mCAAmC,IAAI,KAAK,iBAAiB;AAClG,MAAI,CAAC,gBAAgB;AACnB,SAAK,cAAc,SAAS,kBAAkB,CAAC,GAAG,+CAA+C,IAAI,GAAG,EAAE;AAC1G,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,OAAK,cAAc,SAAS,gBAAgB,IAAI,IAAI,GAAG,+BAA+B,aAAa,GAAG;AACtG,QAAM,QAAQ,KAAK,iBAAiB,eAAe,cAAc;AACjE,MAAI,CAAC,OAAO;AACV,SAAK,cAAc,SAAS,gBAAgB,0BAA0B,IAAI,GAAG,yBAAyB,KAAK,GAAG;AAC9G,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,OAAK,cAAc,SAAS,gBAAgB,IAAI,IAAI,GAAG,+BAA+B,aAAa,iBAAiB;AACpH,QAAM,IAAI,MAAM;AAAA,IACd,SAAS,IAAI,QAAQ,IAAI,iBAAiB,YAAY,KAAK;AAAA,EAC7D,CAAC;AACD,SAAO,KAAK,GAAG;AACjB;AACA,SAAS,6BAA6B,KAAK;AACzC,QAAM,OAAO,mBACR;AAEL,aAAW,OAAO,KAAK;AACrB,QAAI,IAAI,GAAG,MAAM,UAAa,IAAI,GAAG,MAAM,MAAM;AAC/C,aAAO,KAAK,GAAG;AAAA,IACjB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,cAAc;AACZ,SAAK,gBAAgB,OAAO,aAAa;AACzC,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,4BAA4B,OAAO,yBAAyB;AACjE,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,kBAAkB,OAAO,eAAe;AAAA,EAC/C;AAAA;AAAA;AAAA,EAGA,OAAO,QAAQ,YAAY,mBAAmB;AAC5C,QAAI,CAAC,QAAQ;AACX,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,SAAK,cAAc,SAAS,QAAQ,uBAAuB,iBAAiB;AAC5E,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,qBAAqB,CAAC;AAC1B,UAAM,gBAAgB,KAAK,WAAW,iBAAiB,QAAQ,YAAY;AAC3E,QAAI,CAAC,eAAe;AAClB,WAAK,cAAc,SAAS,QAAQ,+DAA+D;AACnG,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,QAAI,KAAK,oBAAoB,mBAAmB,MAAM,GAAG;AACvD,WAAK,cAAc,SAAS,QAAQ,2DAA2D;AAC/F,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,QAAI,YAAY;AACd,WAAK,cAAc,SAAS,QAAQ,kEAAkE,aAAa,GAAG;AACtH,iBAAW,aAAa;AACxB,WAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,WAAO,KAAK,eAAe,mBAAmB,eAAe,QAAQ,UAAU;AAAA,EACjF;AAAA,EACA,YAAY,QAAQ,YAAY;AAC9B,SAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,SAAK,oBAAoB,KAAK;AAAA,EAChC;AAAA,EACA,oBAAoB,YAAY;AAC9B,eAAW,QAAQ,mBAAiB,KAAK,YAAY,eAAe,UAAU,CAAC;AAAA,EACjF;AAAA;AAAA;AAAA,EAGA,sBAAsB,QAAQ,YAAY,mBAAmB;AAC3D,QAAI,CAAC,QAAQ;AACX,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,0BAA0B,KAAK,0BAA0B,MAAM,KAAK,CAAC;AAC9E,QAAI,CAAC,oBAAoB;AACvB,WAAK,cAAc,SAAS,QAAQ,mCAAmC;AACvE,aAAO,KAAK,OAAO,QAAQ,YAAY,iBAAiB;AAAA,IAC1D;AACA,QAAI,KAAK,0BAA0B,gBAAgB,MAAM,GAAG;AAC1D,aAAO,KAAK,mBAAmB,MAAM,EAAE,KAAK,UAAU,OAAK,KAAK,kBAAkB,MAAM,CAAC,GAAG,WAAW,WAAS;AAC9G,cAAM,eAAe;AACrB,aAAK,cAAc,SAAS,QAAQ,cAAc,KAAK;AACvD,eAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,MACjD,CAAC,GAAG,UAAU,MAAM,KAAK,OAAO,QAAQ,YAAY,iBAAiB,CAAC,CAAC;AAAA,IACzE,OAAO;AACL,aAAO,KAAK,kBAAkB,MAAM,EAAE,KAAK,WAAW,WAAS;AAC7D,cAAM,eAAe;AACrB,aAAK,cAAc,SAAS,QAAQ,cAAc,KAAK;AACvD,eAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,MACjD,CAAC,GAAG,UAAU,MAAM,KAAK,OAAO,QAAQ,YAAY,iBAAiB,CAAC,CAAC;AAAA,IACzE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,eAAe,aAAa;AAC5C,QAAI,CAAC,eAAe;AAClB,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,UAAM,YAAY,eAAe,KAAK,0BAA0B,eAAe,aAAa;AAC5F,UAAM,OAAO,KAAK,WAAW,wCAAwC,WAAW,aAAa;AAC7F,WAAO,KAAK,kBAAkB,eAAe,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,eAAe,cAAc;AAC9C,QAAI,CAAC,eAAe;AAClB,aAAO,WAAW,MAAM,IAAI,MAAM,6DAA6D,CAAC;AAAA,IAClG;AACA,UAAM,aAAa,gBAAgB,KAAK,0BAA0B,gBAAgB,aAAa;AAC/F,UAAM,OAAO,KAAK,WAAW,yCAAyC,YAAY,aAAa;AAC/F,WAAO,KAAK,kBAAkB,eAAe,IAAI;AAAA,EACnD;AAAA,EACA,eAAe,mBAAmB,eAAe,QAAQ,YAAY;AACnE,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,qBAAqB,CAAC;AAC1B,QAAI,CAAC,gBAAgB,iBAAiB,OAAO;AAC3C,WAAK,gBAAgB,WAAW,aAAa;AAC7C,WAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,CAAC;AACrB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,KAAK,0BAA0B,WAAW,MAAM;AAChE,UAAM,wBAAwB,KAAK,WAAW,yBAAyB,MAAM;AAC7E,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK,WAAW,sBAAsB,MAAM;AAChD,UAAM,OAAO;AAAA,MACX,eAAe;AAAA,MACf,WAAW;AAAA,MACX,0BAA0B;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,6BAA6B,6BAA6B,IAAI;AACpE,SAAK,qBAAqB,uBAAuB,QAAQ,UAAU;AACnE,WAAO,KAAK,YAAY,KAAK,KAAK,4BAA4B,QAAQ,OAAO;AAAA,EAC/E;AAAA,EACA,kBAAkB,eAAe,MAAM;AACrC,UAAM,MAAM,KAAK,WAAW,yBAAyB,aAAa;AAClE,UAAM,UAAU,KAAK,WAAW;AAChC,WAAO,KAAK,YAAY,KAAK,KAAK,MAAM,eAAe,OAAO,EAAE,KAAK,MAAM,CAAC,GAAG,UAAU,cAAY;AACnG,WAAK,cAAc,SAAS,eAAe,uCAAuC,QAAQ;AAC1F,aAAO,GAAG,QAAQ;AAAA,IACpB,CAAC,GAAG,WAAW,WAAS;AACtB,YAAM,eAAe;AACrB,WAAK,cAAc,SAAS,eAAe,cAAc,KAAK;AAC9D,aAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;AAAA,IACjD,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,aAAa;AACX,QAAI,UAAU,IAAI,YAAY;AAC9B,cAAU,QAAQ,IAAI,gBAAgB,mCAAmC;AACzE,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAAyB;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,sBAAsB,OAAO,mBAAmB;AACrD,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,cAAc,OAAO,WAAW;AACrC,SAAK,qBAAqB,OAAO,kBAAkB;AACnD,SAAK,uBAAuB,OAAO,oBAAoB;AACvD,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,mBAAmB,OAAO,gBAAgB;AAC/C,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,0BAA0B,OAAO,uBAAuB;AAC7D,SAAK,eAAe,OAAO,YAAY;AACvC,SAAK,wBAAwB,OAAO,qBAAqB;AACzD,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,uBAAuB,OAAO,oBAAoB;AAOvD,SAAK,WAAW,SAAS,KAAK,WAAW;AAAA,MACvC,aAAa;AAAA,IACf,CAAC;AAUD,SAAK,gBAAgB,SAAS,KAAK,kBAAkB;AAAA,MACnD,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,YAAY;AACd,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,mBAAmB;AACrB,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,uBAAuB;AACzB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACjB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,6BAA6B,UAAU;AACrC,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,UAAU,YAAU,KAAK,qBAAqB,oCAAoC,MAAM,CAAC,CAAC;AAAA,EACnK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,WAAO,KAAK,qBAAqB,qBAAqB;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,UAAU;AACzB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,UAAU;AACpB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,YAAY,qBAAqB,MAAM,CAAC,CAAC;AAAA,EACrI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,UAAU,KAAK,UAAU;AACvB,WAAO,KAAK,qBAAqB,wBAAwB,QAAQ,EAAE,KAAK,UAAU,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,IACF,MAAM,KAAK,iBAAiB,UAAU,eAAe,YAAY,GAAG,CAAC,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,KAAK;AACrB,WAAO,KAAK,qBAAqB,wBAAwB,EAAE,KAAK,UAAU,CAAC;AAAA,MACzE;AAAA,IACF,MAAM,KAAK,iBAAiB,kBAAkB,YAAY,GAAG,CAAC,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,UAAU;AACxB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,iBAAiB,gBAAgB,MAAM,CAAC,CAAC;AAAA,EACrI;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB,UAAU;AACjC,WAAO,KAAK,qBAAqB,wBAAwB,QAAQ,EAAE,KAAK,UAAU,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,IACF,MAAM,KAAK,iBAAiB,yBAAyB,eAAe,UAAU,CAAC,CAAC;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,UAAU;AACvB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,iBAAiB,eAAe,MAAM,CAAC,CAAC;AAAA,EACpI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU;AACnB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,iBAAiB,WAAW,MAAM,CAAC,CAAC;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,UAAU;AACxB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,iBAAiB,gBAAgB,MAAM,CAAC,CAAC;AAAA,EACrI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB,UAAU;AAChC,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,iBAAiB,wBAAwB,MAAM,CAAC,CAAC;AAAA,EAC7I;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,SAAS,OAAO,UAAU;AAC9C,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU;AACnF,YAAM,QAAQ,KAAK,iBAAiB,WAAW,MAAM;AACrD,aAAO,KAAK,mBAAmB,oBAAoB,OAAO,QAAQ,MAAM;AAAA,IAC1E,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,0BAA0B,SAAS,OAAO,UAAU;AAClD,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU;AACnF,YAAM,QAAQ,KAAK,iBAAiB,eAAe,MAAM;AACzD,aAAO,KAAK,mBAAmB,oBAAoB,OAAO,QAAQ,MAAM;AAAA,IAC1E,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO,UAAU;AACxB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,iBAAiB,oBAAoB,OAAO,MAAM,CAAC,CAAC;AAAA,EAChJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,UAAU;AACjB,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,iBAAiB,oBAAoB,MAAM,CAAC,CAAC;AAAA,EACzI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,UAAU,aAAa;AAC/B,SAAK,qBAAqB,uBAAuB,QAAQ,EAAE,UAAU,YAAU,KAAK,aAAa,MAAM,QAAQ,WAAW,CAAC;AAAA,EAC7H;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,aAAa,cAAc,UAAU;AACtD,WAAO,KAAK,qBAAqB,wBAAwB,QAAQ,EAAE,KAAK,UAAU,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,IACF,MAAM,KAAK,aAAa,eAAe,eAAe,YAAY,aAAa,YAAY,CAAC,CAAC;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,cAAc,UAAU;AAC1C,WAAO,KAAK,qBAAqB,wBAAwB,QAAQ,EAAE,KAAK,UAAU,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,IACF,MAAM,KAAK,sBAAsB,wBAAwB,eAAe,YAAY,YAAY,CAAC,CAAC;AAAA,EACpG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,sBAAsB,UAAU,mBAAmB;AACjD,WAAO,KAAK,qBAAqB,wBAAwB,QAAQ,EAAE,KAAK,UAAU,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,IACF,MAAM,KAAK,wBAAwB,sBAAsB,eAAe,YAAY,iBAAiB,CAAC,CAAC;AAAA,EACzG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,UAAU,mBAAmB;AAClC,WAAO,KAAK,qBAAqB,wBAAwB,QAAQ,EAAE,KAAK,UAAU,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,IACF,MAAM,KAAK,wBAAwB,OAAO,eAAe,YAAY,iBAAiB,CAAC,CAAC;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,UAAU;AACpB,SAAK,qBAAqB,wBAAwB,QAAQ,EAAE,UAAU,CAAC;AAAA,MACrE;AAAA,MACA;AAAA,IACF,MAAM,KAAK,wBAAwB,YAAY,eAAe,UAAU,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AACpB,SAAK,qBAAqB,wBAAwB,EAAE,UAAU,CAAC;AAAA,MAC7D;AAAA,IACF,MAAM,KAAK,wBAAwB,oBAAoB,UAAU,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,aAAa,UAAU;AACvC,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,UAAU,YAAU,KAAK,wBAAwB,kBAAkB,QAAQ,WAAW,CAAC,CAAC;AAAA,EACjK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,mBAAmB,cAAc,UAAU;AACzC,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,UAAU,YAAU,KAAK,wBAAwB,mBAAmB,QAAQ,YAAY,CAAC,CAAC;AAAA,EACnK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,iBAAiB,cAAc,UAAU;AACvC,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,IAAI,YAAU,KAAK,WAAW,iBAAiB,QAAQ,YAAY,CAAC,CAAC;AAAA,EAC9I;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,cAAc,UAAU;AACtC,WAAO,KAAK,qBAAqB,uBAAuB,QAAQ,EAAE,KAAK,UAAU,YAAU,KAAK,WAAW,gBAAgB,QAAQ,eAAe;AAAA,MAChJ;AAAA,IACF,IAAI,MAAS,CAAC,CAAC;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,MAC7B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,KAAK,KAAK;AACR,WAAO,aAAa,QAAQ,GAAG;AAAA,EACjC;AAAA,EACA,MAAM,KAAK,OAAO;AAChB,iBAAa,QAAQ,KAAK,KAAK;AAAA,EACjC;AAAA,EACA,OAAO,KAAK;AACV,iBAAa,WAAW,GAAG;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,iBAAa,MAAM;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,4BAA2B;AAAA,MACpC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC/9MH,IAAM,0BAA0B;AAAA,EAC9B,YAAY;AAAA;AAAA,EAEZ,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,2BAA2B,IAAI;AAAA,EAC/B,qBAAqB;AACvB;AAMA,IAAM,eAAe,IAAI,eAAe,cAAc;AAKtD,IAAM,wBAAN,MAA4B;AAAC;AAK7B,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAAY,SAAS,cAAc;AACjC,SAAK,UAAU;AACf,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,QAAQ,OAAO;AAEb,UAAM,MAAM,IAAI,MAAM,IAAI;AAC1B,WAAO,KAAK,WAAW,KAAK,IAAI;AAAA,EAClC;AAAA,EACA,YAAY,OAAO,OAAO;AACxB,UAAM,MAAM,MAAM;AAClB,WAAO,KAAK,WAAW,KAAK,MAAM,WAAW;AAAA,EAC/C;AAAA,EACA,WAAW,KAAK,QAAQ;AACtB,WAAO,KAAK,aAAa,gBAAgB,EAAE,KAAK,IAAI,mBAAiB;AACnE,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AACA,WAAK,aAAa,cAAc;AAChC,UAAI,UAAU,MAAM;AAClB,aAAK,QAAQ,SAAS,CAAC,QAAQ,GAAG;AAAA,UAChC,aAAa;AAAA,QACf,CAAC;AAAA,MACH,OAAO;AACL,aAAK,QAAQ,SAAS,CAAC,QAAQ,CAAC;AAAA,MAClC;AACA,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,kBAAkB,mBAAmB;AACxD,aAAO,KAAK,qBAAqB,YAAc,SAAY,MAAM,GAAM,SAAS,qBAAqB,CAAC;AAAA,IACxG;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,WAAU;AAAA,MACnB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAIC,mBAAkB,MAAMA,iBAAgB;AAAA,EAC1C,YAAY,QAAQ,uBAAuB;AACzC,SAAK,SAAS;AACd,SAAK,wBAAwB;AAAA,EAC/B;AAAA,EACA,UAAU,KAAK,MAAM;AACnB,WAAO,KAAK,SAAS,EAAE,KAAK,SAAS,WAAS;AAC5C,UAAI,CAAC,SAAS,IAAI,QAAQ,IAAI,KAAK,OAAO,UAAU,KAAK,IAAI,IAAI,SAAS,aAAa,KAAK,CAAC,CAAC,KAAK,OAAO,iBAAiB,CAAC,KAAK,OAAO,cAAc,GAAG,GAAG;AAC1J,eAAO,KAAK,OAAO,GAAG;AAAA,MACxB;AACA,YAAM,SAAS,IAAI,MAAM;AAAA,QACvB,SAAS,IAAI,QAAQ,IAAI,KAAK,OAAO,YAAY,GAAG,KAAK,OAAO,UAAU,IAAI,KAAK,EAAE;AAAA,MACvF,CAAC;AACD,aAAO,KAAK,OAAO,MAAM;AAAA,IAC3B,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,WAAW;AACT,WAAO,KAAK,sBAAsB,eAAe,EAAE,KAAK,MAAM,CAAC;AAAA,EACjE;AACF;AACAA,mBAAkB,WAAW,CAAC,QAAQ,GAAG,OAAO,YAAY,CAAC,GAAG,WAAW,qBAAqB,CAAC,QAAQ,qBAAqB,CAAC,CAAC,GAAGA,gBAAe;AAKlJ,IAAI;AAAA,CACH,SAAUC,YAAW;AACpB,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,KAAK,IAAI,CAAC,IAAI;AAClC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,SAAS,IAAI,EAAE,IAAI;AACzC,GAAG,cAAc,YAAY,CAAC,EAAE;AAKhC,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,mBAAmB,aAAa,UAAU,UAAU,eAAe,cAAc;AACtF,WAAO,YAAY,eAAe,aAAa,aAAa,QAAQ,YAAY,aAAa,cAAc,kBAAkB,QAAQ,YAAY,kBAAkB,mBAAmB,iBAAiB,QAAQ,YAAY,iBAAiB;AAAA,EAC9O;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,IAAI,gBAAgB,CAAC,CAAC;AAAA,EAE7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,UAAU,UAAU,eAAe,cAAc;AACvD,WAAO,KAAK,aAAa,UAAU,UAAU,MAAM,UAAU,eAAe,YAAY;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,UAAU,UAAU,eAAe,cAAc;AACzD,WAAO,KAAK,aAAa,UAAU,UAAU,QAAQ,UAAU,eAAe,YAAY;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,UAAU,UAAU,eAAe,cAAc;AACtD,WAAO,KAAK,aAAa,UAAU,UAAU,KAAK,UAAU,eAAe,YAAY;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,UAAU,UAAU,eAAe,cAAc;AACzD,WAAO,KAAK,aAAa,UAAU,UAAU,QAAQ,UAAU,eAAe,YAAY;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,UAAU,UAAU,eAAe,cAAc;AAC1D,WAAO,KAAK,aAAa,UAAU,UAAU,SAAS,UAAU,eAAe,YAAY;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,WAAO,KAAK,cAAc,aAAa;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,UAAU,WAAW,UAAU,eAAe,cAAc;AACvE,QAAI,CAAC,KAAK,OAAO;AACf,cAAQ,MAAM,2BAA2B;AACzC,aAAO;AAAA,IACT;AACA,eAAW,QAAQ,KAAK,OAAO;AAE7B,YAAM,SAAS,YAAY,KAAK,cAAc,UAAU,MAAM,UAAU,eAAe,YAAY,OAAO;AAE1G,UAAI,OAAO;AACT,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,UAAU,MAAM,UAAU,eAAe,cAAc;AACnE,QAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AAClC,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,cAAc,SAAS,EAAE,KAAK,QAAM,sBAAqB,mBAAmB,IAAI,UAAU,UAAU,eAAe,YAAY,CAAC;AACzJ,QAAI,CAAC,eAAe,CAAC,YAAY,aAAa;AAC5C,aAAO;AAAA,IACT;AACA,UAAM,aAAa,YAAY,YAAY,KAAK,OAAK,EAAE,aAAa,IAAI;AACxE,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AACA,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,kBAAkB,UAAU,UAAU,eAAe,cAAc;AACjE,WAAO,KAAK,uBAAuB,UAAU,UAAU,MAAM,UAAU,eAAe,YAAY;AAAA,EACpG;AAAA,EACA,iBAAiB,UAAU,UAAU,eAAe,cAAc;AAChE,WAAO,KAAK,uBAAuB,UAAU,UAAU,KAAK,UAAU,eAAe,YAAY;AAAA,EACnG;AAAA,EACA,oBAAoB,UAAU,UAAU,eAAe,cAAc;AACnE,WAAO,KAAK,uBAAuB,UAAU,UAAU,QAAQ,UAAU,eAAe,YAAY;AAAA,EACtG;AAAA,EACA,oBAAoB,UAAU,UAAU,eAAe,cAAc;AACnE,WAAO,KAAK,uBAAuB,UAAU,UAAU,QAAQ,UAAU,eAAe,YAAY;AAAA,EACtG;AAAA,EACA,qBAAqB,UAAU,UAAU,eAAe,cAAc;AACpE,WAAO,KAAK,uBAAuB,UAAU,UAAU,SAAS,UAAU,eAAe,YAAY;AAAA,EACvG;AAAA,EACA,uBAAuB,UAAU,WAAW,UAAU,eAAe,cAAc;AACjF,WAAO,KAAK,wBAAwB,UAAU,UAAU,eAAe,YAAY,EAAE;AAAA;AAAA,MACrF,IAAI,4BAA0B,YAAY,2BAA2B,SAAS;AAAA;AAAA,IAChD;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB,UAAU,UAAU,eAAe,cAAc;AACvE,WAAO,KAAK,cAAc,KAAK,IAAI,kBAAgB;AACjD,UAAI,CAAC,gBAAgB,CAAC,KAAK,OAAO;AAChC,eAAO;AAAA,MACT;AACA,YAAM,cAAc,aAAa,KAAK,QAAM,sBAAqB,mBAAmB,IAAI,UAAU,UAAU,eAAe,YAAY,CAAC;AACxI,UAAI,CAAC,eAAe,CAAC,YAAY,aAAa;AAC5C,eAAO;AAAA,MACT;AACA,YAAM,OAAO,YAAY,YAAY,KAAK,OAAK,KAAK,MAAM,SAAS,EAAE,QAAQ,CAAC;AAC9E,UAAI,CAAC,MAAM;AACT,eAAO;AAAA,MACT;AACA,aAAO,KAAK;AAAA,IACd,CAAC,CAAC;AAAA,EACJ;AACF;AAKA,IAAM,4BAAN,MAAgC;AAAA;AAAA,EAE9B,YAAY,cAAc,YAAY,YAAY,eAAe;AAC/D,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AAAA,EACvB;AACF;AAKA,IAAM,WAAN,MAAe;AAAC;AAKhB,IAAM,yCAAyC;AAC/C,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc;AACZ,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,IAAI,eAAe;AACjB,SAAK,uBAAuB;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,KAAK,KAAK;AACR,WAAO,eAAe,QAAQ,KAAK,QAAQ,GAAG,CAAC;AAAA,EACjD;AAAA,EACA,MAAM,KAAK,OAAO;AAChB,mBAAe,QAAQ,KAAK,QAAQ,GAAG,GAAG,KAAK;AAAA,EACjD;AAAA,EACA,OAAO,KAAK;AACV,mBAAe,WAAW,KAAK,QAAQ,GAAG,CAAC;AAAA,EAC7C;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,iBAAiB,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,EAAE,GAAG;AAC9C,cAAM,MAAM,eAAe,IAAI,CAAC;AAChC,YAAI,OAAO,IAAI,WAAW,KAAK,aAAa,GAAG;AAC7C,yBAAe,WAAW,GAAG;AAAA,QAC/B;AAAA,MACF;AAAA,IACF,OAAO;AACL,qBAAe,MAAM;AAAA,IACvB;AAAA,EACF;AAAA,EACA,uBAAuB,QAAQ;AAC7B,QAAI,OAAO,wCAAwC,wCAAwC;AACzF,YAAM,IAAI,MAAM,wDAAwD,sCAAsC,GAAG;AAAA,IACnH;AACA,QAAI,OAAO,qCAAqC;AAC9C,YAAM,YAAY,IAAI,gBAAgB,OAAO,SAAS,MAAM;AAC5D,WAAK,gBAAgB,UAAU,IAAI,sCAAsC,KAAK,UAAU,IAAI,OAAO,mCAAmC;AACtI,UAAI,OAAO,qBAAqB;AAC9B,YAAI,KAAK,eAAe;AACtB,kBAAQ,IAAI,uBAAuB,KAAK,aAAa,EAAE;AAAA,QACzD,OAAO;AACL,kBAAQ,IAAI,8CAA8C,OAAO,mCAAmC,gBAAgB;AAAA,QACtH;AAAA,MACF;AAAA,IACF;AACA,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,QAAQ,KAAK;AACX,SAAK,uBAAuB;AAC5B,QAAI,KAAK,iBAAiB,MAAM;AAC9B,aAAO,GAAG,KAAK,aAAa,IAAI,GAAG;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,yBAAyB;AACjC,cAAQ,MAAM,uDAAuD;AAAA,IACvE;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,QAAQ,gBAAgB,UAAU,kBAAkB;AAC9D,SAAK,SAAS;AACd,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAChB,SAAK,mBAAmB;AACxB,SAAK,sBAAsB,IAAI,QAAQ;AACvC,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc;AACZ,SAAK,oBAAoB,KAAK;AAC9B,SAAK,oBAAoB,SAAS;AAAA,EACpC;AAAA,EACA,SAAS,cAAc;AACrB,QAAI,iBAAiB,KAAK,eAAe;AACvC;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO,qBAAqB;AAC1B,WAAO,KAAK,mBAAmB,qBAAqB,KAAK,EAAE,KAAK,UAAU,MAAM,oBAAoB,OAAO,CAAC,CAAC;AAAA,EAC/G;AAAA,EACA,MAAM,qBAAqB,WAAW,KAAK,OAAO,UAAU;AAC1D,SAAK,mBAAmB,qBAAqB,QAAQ,EAAE,UAAU,MAAM;AAIrE,UAAI,CAAC,UAAU;AAEb,4BAAoB,UAAU;AAAA,MAChC,OAAO;AAEL,cAAM,gBAAgB;AACtB,cAAM,iBAAiB;AACvB,cAAM,WAAW;AACjB,cAAM,YAAY;AAClB,cAAM,cAAc,OAAO;AAC3B,cAAM,eAAe,OAAO;AAC5B,cAAM,QAAQ,KAAK,IAAI,UAAU,KAAK,MAAM,gBAAgB,WAAW,CAAC;AACxE,cAAM,SAAS,KAAK,IAAI,WAAW,KAAK,MAAM,iBAAiB,YAAY,CAAC;AAC5E,cAAM,MAAM,KAAK,IAAI,GAAG,KAAK,OAAO,eAAe,UAAU,CAAC,CAAC;AAC/D,cAAM,OAAO,KAAK,IAAI,GAAG,KAAK,OAAO,cAAc,SAAS,CAAC,CAAC;AAC9D,4BAAoB,UAAU,QAAW;AAAA,UACvC,YAAY,aAAW;AAErB,gBAAI;AACJ,gBAAI;AACF,4BAAc,OAAO,KAAK,SAAS,UAAU,yCAAyC,OAAO,UAAU,MAAM,YAAY,QAAQ,aAAa,MAAM;AAAA,YACtJ,SAAS,GAAG;AAEV,sBAAQ,KAAK,iFAAiF,CAAC;AAAA,YACjG;AAEA,gBAAI,CAAC,aAAa;AAChB,mBAAK,MAAM,qBAAqB,KAAK;AAAA,YACvC;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,OAAO,iBAAiB;AAAA,EAC1E;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,WAAW,IAAI,KAAK,WAAW,KAAK,OAAO,iBAAiB;AAAA,EAC1E;AAAA,EACA,aAAa;AACX,WAAO,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS;AAAA,EAC3D;AAAA,EACA,mBAAmB,qBAAqB,UAAU;AAEhD,UAAM,cAAc,IAAI,IAAI,OAAO,SAAS,IAAI;AAChD,gBAAY,OAAO;AACnB,QAAI,KAAK,eAAe,cAAc;AACpC,kBAAY,aAAa,IAAI,wCAAwC,KAAK,eAAe,YAAY;AAAA,IACvG;AACA,UAAM,cAAc,IAAI,OAAO,IAAI,KAAK,iBAAiB,YAAY,CAAC,EAAE;AACxE,QAAI,iBAAiB,KAAK,iBAAiB,KAAK,IAAI,EAAE,QAAQ,aAAa,EAAE;AAC7E,QAAI,CAAC,eAAe,WAAW,GAAG,GAAG;AACnC,uBAAiB,IAAI,cAAc;AAAA,IACrC;AACA,YAAQ,IAAI,8BAA8B,cAAc,8BAA8B,cAAc;AACpG,WAAO,oBAAoB,iBAAiB,EAAE,KAAK,IAAI,YAAU;AAC/D,WAAK,0BAA0B;AAC/B,WAAK,wBAAwB,iBAAiB,KAAK,oBAAoB;AAEvE,WAAK,wBAAwB,cAAc,YAAY,SAAS;AAChE,UAAI,UAAU;AAEZ,aAAK,wBAAwB,cAAc,KAAK,oBAAoB;AAAA,MACtE;AACA,YAAM,aAAa,OAAO,YAAY;AACtC,UAAI,YAAY;AACd,aAAK,eAAe,MAAM,eAAe,KAAK,wBAAwB,WAAW;AACjF,aAAK,eAAe,MAAM,kBAAkB,cAAc;AAAA,MAC5D;AACA,WAAK,wBAAwB,iBAAiB;AAC9C,WAAK,wBAAwB,wBAAwB,YAAY,SAAS;AAAA,IAC5E,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,oCAAoC;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAiB,SAAS,YAAY,GAAM,SAAS,uBAAuB,GAAM,SAAS,eAAe,CAAC,GAAM,SAAc,gBAAgB,CAAC;AAAA,IACnL;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,IACxB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,6BAAN,MAAM,oCAAmC,sBAAsB;AAAA,EAC7D,YAAY,qBAAqB,cAAc,eAAe;AAC5D,UAAM;AACN,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB,MAAM,UAAU,QAAQ,mBAAmB,EAAE,KAAK,OAAO,WAAS,MAAM,OAAO,SAAS,sBAAsB,CAAC,CAAC,GAAG,KAAK,cAAc,kBAAkB,EAAE,KAAK,OAAO,kBAAgB,aAAa,SAAS,WAAW,2BAA2B,aAAa,OAAO,qBAAqB,iBAAiB,aAAa,CAAC,CAAC;AAClV,SAAK,gBAAgB,EAAE,KAAK,UAAU,MAAM,KAAK,oBAAoB,WAAW,CAAC,CAAC,EAAE,UAAU,aAAW,KAAK,UAAU,OAAO;AAC/H,SAAK,gBAAgB,EAAE,KAAK,UAAU,qBAAmB,kBAAkB,KAAK,oBAAoB,sBAAsB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,aAAW,KAAK,iBAAiB,OAAO;AAAA,EAC3L;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,WAAO,KAAK,oBAAoB,kBAAkB,EAAE,SAAS,IAAI,KAAK,oBAAoB,eAAe,EAAE,KAAK,IAAI,WAAS,UAAU,KAAK,OAAO,KAAK,CAAC,IAAI,GAAG,MAAS;AAAA,EAC3K;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,QAAI,CAAC,CAAC,KAAK,SAAS;AAClB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,WAAO,CAAC,CAAC,KAAK,kBAAkB,IAAI,GAAG,KAAK,kBAAkB,EAAE,MAAM,GAAI,IAAI,GAAG,CAAC;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,UAAM,qBAAqB,KAAK,kBAAkB;AAClD,QAAI,CAAC,CAAC,oBAAoB;AACxB,aAAO,mBAAmB,QAAQ,mBAAmB,sBAAsB,mBAAmB;AAAA,IAChG;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,UAAM,qBAAqB,KAAK,kBAAkB;AAClD,QAAI,CAAC,CAAC,oBAAoB;AACxB,aAAO,mBAAmB;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO,KAAK,oBAAoB,UAAU,KAAK,IAAI,UAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,KAAK,WAAW,KAAK,SAAS,QAAQ,CAAC,CAAC,CAAC;AAAA,EAClH;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,oBAAoB,iBAAiB,KAAK,IAAI,YAAU,OAAO,eAAe,GAAG,qBAAqB,CAAC;AAAA,EACrH;AAAA,EACA,QAAQ;AACN,SAAK,aAAa,MAAM,KAAK,mBAAmB;AAAA,EAClD;AAAA,EACA,SAAS;AACP,UAAM,mBAAmB,KAAK,aAAa,OAAO,KAAK,mBAAmB,EAAE,KAAK,IAAI,MAAM,IAAI,GAAG,MAAM,GAAG,YAAY,CAAC,CAAC;AACzH,qBAAiB,UAAU;AAC3B,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,SAAc,mBAAmB,GAAM,SAAS,YAAY,GAAM,SAAc,mBAAmB,CAAC;AAAA,IACtK;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,4BAA2B;AAAA,IACtC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,SAAS,aAAa,OAAO;AAC3B,SAAO,aAAa,KAAK,IAAI,QAAQ,GAAG,KAAK;AAC/C;AASA,SAAS,eAAe,YAAY;AAClC,SAAO,KAAK,UAAY,WAAS,MAAM,GAAG,KAAK,GAAG,MAAM,YAAY,UAAU,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1G;AAKA,IAAM,4BAAN,MAAM,mCAAkC,qBAAqB;AAAA,EAC3D,OAAO;AACL,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO,UAAU,UAAU,UAAU,WAAW;AAC9C,WAAO,KAAK,UAAU,CAAC,SAAS,YAAY,UAAU,WAAW,SAAS,wBAAwB,SAAS,eAAe,SAAS,YAAY,CAAC;AAAA,EAClJ;AAAA,EACA,OAAO,gBAAgB,UAAU,YAAY,UAAU,eAAe,cAAc;AAElF,WAAO,SAAS,eAAe,eAAe,SAAS,2BAA2B,QAAQ,SAAS,2BAA2B,OAAO,SAAS,2BAA2B,cAAc,SAAS,kBAAkB,QAAQ,SAAS,kBAAkB,mBAAmB,SAAS,iBAAiB,QAAQ,SAAS,iBAAiB;AAAA,EACtU;AAAA,EACA,YAAY,QAAQ,uBAAuB,MAAM;AAC/C,UAAM;AACN,SAAK,SAAS;AACd,SAAK,wBAAwB;AAC7B,SAAK,OAAO;AACZ,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,mBAAmB,IAAI,gBAAgB,KAAK;AACjD,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,iBAAiB,IAAI,gBAAgB,MAAM;AAChD,SAAK,gBAAgB,cAAc,CAAC,KAAK,sBAAsB,gBAAgB,EAAE,KAAK,qBAAqB,CAAC,GAAG,aAAa,KAAK,OAAO,qBAAqB,KAAK,OAAO,qBAAqB,EAAE,KAAK,qBAAqB,CAAC,GAAG,aAAa,KAAK,OAAO,oBAAoB,EAAE,KAAK,qBAAqB,CAAC,GAAG,aAAa,KAAK,OAAO,WAAW,gCAAgC,EAAE,KAAK,qBAAqB,CAAC,GAAG,KAAK,cAAc,CAAC,EAAE,KAAK,eAAe,KAAK,OAAO,mBAAmB,GAAG,UAAY,CAAC,CAAC,iBAAiB,eAAe,kBAAkB,OAAO,MAAM;AACtiB,UAAI,iBAAiB;AACnB,cAAM,cAAc,oBAAoB,iBAAiB;AACzD,eAAO,KAAK,KAAK,IAAI,UAAU,EAAE,KAAK,IAAI,cAAY,SAAS,KAAK,GAAG,WAAW,WAAS;AACzF,kBAAQ,MAAM,6BAA6B,KAAK,UAAU,KAAK,CAAC;AAChE,iBAAO;AAAA,QACT,CAAC,CAAC;AAAA,MACJ,OAAO;AACL,eAAO,GAAG,CAAC,CAAC;AAAA,MACd;AAAA,IACF,CAAC,GAAG,IAAI,MAAM,KAAK,iBAAiB,KAAK,IAAI,CAAC,GAAG,UAAU,KAAK,aAAa,GAAG,YAAY,CAAC,CAAC;AAC9F,SAAK,sBAAsB,SAAS,EAAE,KAAK,UAAU,KAAK,aAAa,CAAC,EAAE,UAAU,WAAS,KAAK,QAAQ,KAAK;AAC/G,SAAK,cAAc,KAAK,UAAU,KAAK,aAAa,CAAC,EAAE,UAAU,gBAAc;AAC7E,WAAK,iBAAiB,KAAK,mBAAmB,UAAU;AAAA,IAC1D,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,KAAK;AACxB,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,QAAQ,UAAU,UAAU,eAAe,cAAc;AACvD,WAAO,KAAK,cAAc,UAAU,2BAA0B,gBAAgB,UAAU,eAAe,YAAY;AAAA,EACrH;AAAA,EACA,kBAAkB,UAAU,UAAU,eAAe,cAAc;AACjE,WAAO,KAAK,wBAAwB,UAAU,2BAA0B,gBAAgB,UAAU,eAAe,YAAY;AAAA,EAC/H;AAAA,EACA,OAAO,UAAU,UAAU,eAAe,cAAc;AACtD,WAAO,KAAK,cAAc,UAAU,2BAA0B,eAAe,UAAU,eAAe,YAAY;AAAA,EACpH;AAAA,EACA,iBAAiB,UAAU,UAAU,eAAe,cAAc;AAChE,WAAO,KAAK,wBAAwB,UAAU,2BAA0B,eAAe,UAAU,eAAe,YAAY;AAAA,EAC9H;AAAA,EACA,UAAU,UAAU,UAAU,eAAe,cAAc;AACzD,WAAO,KAAK,cAAc,UAAU,2BAA0B,kBAAkB,UAAU,eAAe,YAAY;AAAA,EACvH;AAAA,EACA,oBAAoB,UAAU,UAAU,eAAe,cAAc;AACnE,WAAO,KAAK,wBAAwB,UAAU,2BAA0B,kBAAkB,UAAU,eAAe,YAAY;AAAA,EACjI;AAAA,EACA,UAAU,UAAU,UAAU,eAAe,cAAc;AACzD,WAAO,KAAK,cAAc,UAAU,2BAA0B,kBAAkB,UAAU,eAAe,YAAY;AAAA,EACvH;AAAA,EACA,oBAAoB,UAAU,UAAU,eAAe,cAAc;AACnE,WAAO,KAAK,wBAAwB,UAAU,2BAA0B,kBAAkB,UAAU,eAAe,YAAY;AAAA,EACjI;AAAA,EACA,WAAW,UAAU,UAAU,eAAe,cAAc;AAC1D,WAAO,KAAK,cAAc,UAAU,2BAA0B,mBAAmB,UAAU,eAAe,YAAY;AAAA,EACxH;AAAA,EACA,qBAAqB,UAAU,UAAU,eAAe,cAAc;AACpE,WAAO,KAAK,wBAAwB,UAAU,2BAA0B,mBAAmB,UAAU,eAAe,YAAY;AAAA,EAClI;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AACf,WAAO,KAAK,iBAAiB,aAAa;AAAA,EAC5C;AAAA,EACA,cAAc,UAAU,WAAW,UAAU,eAAe,cAAc;AACxE,WAAO,KAAK,MAAM,KAAK,cAAY;AAEjC,YAAM,MAAM,KAAK,UAAU,CAAC,UAAU,UAAU,WAAW,UAAU,eAAe,YAAY,CAAC;AACjG,YAAM,OAAO,KAAK,UAAU,CAAC,UAAU,UAAU,WAAW,KAAK,eAAe,YAAY,CAAC;AAC7F,aAAO,KAAK,eAAe,IAAI,GAAG,KAAK,KAAK,eAAe,IAAI,IAAI;AAAA,IACrE,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB,UAAU,WAAW,UAAU,eAAe,cAAc;AAClF,WAAO,cAAc,CAAC,KAAK,eAAe,KAAK,sBAAsB,SAAS,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,YAAY,KAAK,MAAM;AAClH,aAAO,WAAW,KAAK,cAAY,KAAK,yBAAyB,UAAU,UAAU,OAAO,WAAW,UAAU,eAAe,YAAY,CAAC;AAAA,IAC/I,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,yBAAyB,UAAU,YAAY,OAAO,WAAW,UAAU,eAAe,cAAc;AACtG,WAAO,2BAA0B,gBAAgB,UAAU,YAAY,UAAU,eAAe,YAAY,KAAK,SAAS,OAAO,KAAK,WAAS,MAAM,SAAS,MAAM,QAAQ,KAAK,MAAM,WAAW,SAAS,SAAS,CAAC;AAAA,EACvN;AAAA,EACA,mBAAmB,YAAY;AAC7B,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,eAAW,QAAQ,cAAY;AAC7B,eAAS,OAAO,QAAQ,WAAS;AAE/B,YAAI,KAAK,MAAM,SAAS,MAAM,QAAQ,GAAG;AACvC,gBAAM,WAAW,QAAQ,eAAa;AACpC,kBAAM,MAAM,2BAA0B,UAAU,UAAU,MAAM,UAAU,SAAS;AACnF,2BAAe,IAAI,GAAG;AAAA,UACxB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,SAAS,YAAY,GAAM,SAAS,qBAAqB,GAAM,SAAY,UAAU,CAAC;AAAA,IACvJ;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,2BAA0B;AAAA,IACrC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,+BAA+B,iCAChC,0BADgC;AAAA;AAAA,EAGnC,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,WAAW;AAAA;AAAA,EAEX,iBAAiB;AAAA;AAAA,EAEjB,SAAS;AAAA;AAAA,EAET,qCAAqC;AACvC;AAMA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO,sBAAsB,QAAQ,cAAc,gBAAgB,UAAU;AAC3E,mBAAe,uBAAuB,MAAM;AAC5C,WAAO,IAAI,oBAAoB,qBAAoB,8BAA8B,QAAQ,cAAc,gBAAgB,QAAQ,CAAC;AAAA,EAClI;AAAA,EACA,OAAO,8BAA8B,QAAQ,cAAc,gBAAgB,UAAU;AACnF,eAAW,YAAY;AACvB,UAAM,YAAY,IAAI,gBAAgB,OAAO,SAAS,MAAM;AAC5D,UAAM,eAAe,UAAU,IAAI,OAAO,mBAAmB,QAAQ;AAErE,UAAM,aAAa,KAAK,WAAW;AACnC,QAAI,cAAc,aAAa,eAAe,KAAK,OAAO,IAAI;AAC9D,QAAI,gBAAgB,eAAe,gBAAgB,QAAQ;AACzD,oBAAc;AACd,qBAAe,OAAO,OAAO;AAAA,IAC/B;AACA,UAAM,cAAc,KAAK,eAAe,WAAW,cAAc;AACjE,WAAO,cAAc,CAAC,aAAa,OAAO,QAAQ,GAAG,aAAa,OAAO,YAAY,GAAG,aAAa,OAAO,qBAAqB,OAAO,qBAAqB,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,UAAU,cAAc,iBAAiB,MAAM;AAEzN,YAAM,eAAe,gBAAgB,eAAe;AACpD,YAAM,WAAW,iBAAiB,QAAQ;AAE1C,UAAI,CAAC,CAAC,eAAe,iBAAiB,aAAa;AAEjD,YAAI,YAAY;AACd,cAAI,OAAO,qBAAqB;AAC9B,oBAAQ,IAAI,oCAAqC;AAAA,UACnD;AAEA,yBAAe,OAAO,QAAQ;AAAA,QAChC;AAAA,MACF;AAEA,UAAI,YAAY;AACd,uBAAe,MAAM,SAAS,YAAY;AAAA,MAC5C;AACA,UAAI,CAAC,cAAc;AACjB,gBAAQ,MAAM,yGAA+G,OAAO,mBAAmB,0BAAgC;AAAA,MACzL;AACA,mBAAa,SAAS,YAAY;AAClC,YAAM,gBAAgB,KAAK,iBAAiB,mBAAmB,YAAY;AAC3E,YAAM,0BAA0B,CAAC;AACjC,UAAI,eAAe,cAAc;AAC/B,gCAAwB,sCAAsC,IAAI,eAAe;AAAA,MACnF;AACA,cAAQ,IAAI,uCAAuC,aAAa;AAChE,aAAO;AAAA,QACL;AAAA;AAAA,QAEA,WAAW;AAAA,QACX,cAAc,OAAO;AAAA;AAAA;AAAA;AAAA,QAIrB;AAAA,QACA,cAAc;AAAA;AAAA,QAEd,OAAO,OAAO,SAAS,6BAA6B;AAAA,QACpD,mBAAmB;AAAA,QACnB,aAAa,OAAO;AAAA;AAAA,QAEpB,iBAAiB,OAAO;AAAA,QACxB,yBAAyB;AAAA;AAAA,QAEzB,sCAAsC,OAAO;AAAA,QAC7C,gBAAgB,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS,OAAO,WAAW,OAAO,iBAAiB;AAAA,QAC5G,iCAAiC;AAAA;AAAA,QAEjC,gBAAgB;AAAA;AAAA,QAEhB,mBAAmB;AAAA;AAAA,QAEnB,UAAU,OAAO,uBAAuB,6BAA6B,sBAAsB,SAAS,QAAQ,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,QAKrH,4BAA4B;AAAA,QAC5B,mBAAmB;AAAA,QACnB;AAAA,QACA,gBAAgB,eAAe,KAAK,gBAAgB,KAAK,OAAO,SAAS;AAAA,QACzE,iBAAiB,OAAO,SAAS;AAAA,QACjC,uBAAuB,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,QACxD,SAAS;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC,GAAG,YAAY,CAAC,GAAG,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMxB,aAAa,kCAAkC,KAAK;AAAA,KAAS,CAAC;AAAA,EAChE;AAAA,EACA,OAAO,aAAa;AAClB,WAAO,OAAO,YAAY;AAAA,EAC5B;AAAA,EACA,OAAO,eAAe,WAAW,gBAAgB;AAC/C,UAAM,aAAa,KAAK,WAAW;AAEnC,UAAM,qBAAqB,OAAO,SAAS;AAE3C,QAAI,oBAAoB,aAAa,eAAe,KAAK,aAAa,IAAI;AAC1E,QAAI,sBAAsB,eAAe,sBAAsB,QAAQ;AACrE,0BAAoB;AACpB,qBAAe,OAAO,aAAa;AAAA,IACrC;AAEA,UAAM,cAAc,UAAU,IAAI,MAAM,IAAI,oBAAoB;AAEhE,QAAI,YAAY;AACd,qBAAe,MAAM,eAAe,WAAW;AAAA,IACjD;AACA,YAAQ,IAAI,yBAAyB,cAAc,GAAG;AACtD,WAAO;AAAA,EACT;AAAA,EACA,OAAO,iBAAiB,mBAAmB,cAAc;AACvD,QAAI,iBAAiB,QAAQ,iBAAiB,QAAW;AACvD,cAAQ,IAAI,gCAAgC,oBAAoB,oBAAoB;AACpF,aAAO;AAAA,IACT,OAAO;AACL,cAAQ,IAAI,gCAAgC,oBAAoB,iBAAiB,YAAY;AAC7F,aAAO,oBAAoB,kBAAkB;AAAA,IAC/C;AAAA,EACF;AACF;AAKA,IAAM,oBAAoB,CAAC,QAAQ,cAAc,gBAAgB,aAAa;AAC5E,SAAO,oBAAoB,sBAAsB,QAAQ,cAAc,gBAAgB,QAAQ;AACjG;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,cAAc,qBAAqB;AAC7C,SAAK,sBAAsB;AAC3B,QAAI,cAAc;AAChB,YAAM,IAAI,MAAM,kGAAkG;AAAA,IACpH;AACA;AAAA;AAAA,MAEA,UAAU,QAAQ,mBAAmB,EAAE,KAAK,IAAI,WAAS,QAAQ,IAAI,4CAA4C,KAAK,CAAC,GAAG,IAAI,WAAS,MAAM,MAAM,CAAC;AAAA;AAAA;AAAA,MAGpJ,UAAU,QAAQ,SAAS,EAAE;AAAA,QAAK,OAAO,WAAS,MAAM,QAAQ,mBAAmB;AAAA,QAAG,OAAO,WAAS,CAAC,CAAC,MAAM,QAAQ;AAAA,QAAG,IAAI,WAAS,QAAQ,IAAI,sCAAsC,KAAK,CAAC;AAAA;AAAA,QAE9L,OAAO,WAAS;AACd,gBAAM,mBAAmB,CAAC,CAAC,aAAa,QAAQ,MAAM,GAAG;AACzD,cAAI,CAAC,kBAAkB;AACrB,oBAAQ,IAAI,2EAA2E;AAAA,UACzF;AACA,iBAAO;AAAA,QACT,CAAC;AAAA;AAAA,QAED,IAAI,WAAS,aAAa,WAAW,MAAM,GAAG,CAAC;AAAA,QAAG,IAAI,WAAS,MAAM,QAAQ;AAAA,MAAC;AAAA,IAAC,EAAE;AAAA;AAAA,MAEjF,qBAAqB;AAAA,IAAC,EAAE,UAAU,iBAAe;AAC/C,cAAQ,IAAI,oCAAoC,WAAW;AAC3D,WAAK,oBAAoB,UAAU,WAAW,EAAE,KAAK,MAAM,CAAC,EAAE,UAAU,YAAU,QAAQ,IAAI,gBAAgB,MAAM,CAAC;AAAA,IACvH,CAAC;AACD,wBAAoB;AAAA,EACtB;AAAA,EACA,OAAO,QAAQ,SAAS;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,QAAQ,uBAAuB;AAAA,QACzC,SAAS;AAAA,QACT,UAAU,OAAO,OAAO,CAAC,GAAG,8BAA8B,QAAQ,MAAM;AAAA,MAC1E,GAAG,cAAc;AAAA,QACf,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAUD;AAAA,QACV,MAAM,CAAC,cAAc,qBAAqB;AAAA,QAC1C,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAS,kBAAiB,EAAE,GAAM,SAAc,mBAAmB,CAAC;AAAA,IAC3H;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAuB,UAAU;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,MAAM,CAAC,qBAAqB,qBAAqB,cAAc,YAAY;AAAA,QAC3E,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,SAAS,CAAC,kBAAkB,WAAa,QAAQ;AAAA,QAC/C,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,cAAc,cAAc,yBAAyB,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,QAC7F;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,WAAa,QAAQ;AAAA,QAC/C,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,MAAM,CAAC,cAAc,cAAc,yBAAyB,CAAC,IAAI,SAAS,GAAG,aAAa,CAAC;AAAA,QAC7F;AAAA,MACF,CAAC,CAAC;AAAA,MACF,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,MAAM,CAAC,qBAAqB,qBAAqB,cAAc,YAAY;AAAA,QAC3E,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,iBAAiB,qBAAqB,qBAAqB,cAAc,QAAQ;AACxF,SAAO,MAAM,oBAAoB,6BAA6B,EAAE;AAAA,IAAK,UAAU,MAAM,oBAAoB,kBAAkB,CAAC;AAAA,IAAG,OAAO,WAAS,MAAM,SAAS,WAAW,YAAY;AAAA,IAAG,MAAM;AAAA;AAAA,IAE9L,UAAU,MAAM;AACd,YAAM,YAAY,IAAI,gBAAgB,OAAO,SAAS,MAAM;AAC5D,UAAI,UAAU,IAAI,MAAM,GAAG;AACzB,YAAI,OAAO,qBAAqB;AAC9B,kBAAQ,IAAI,kDAAkD;AAAA,QAChE;AACA,eAAO,oBAAoB,UAAU;AAAA,MAGvC,WAAW,CAAC,UAAU,IAAI,MAAM,KAAK,CAAC,UAAU,IAAI,OAAO,GAAG;AAE5D,YAAI,OAAO,qBAAqB;AAC9B,kBAAQ,IAAI,uBAAuB;AAAA,QACrC;AACA,eAAO,oBAAoB,iBAAiB,KAAK,MAAM,GAAG,UAAU,YAAU,OAAO,kBAAkB,GAAG,IAAI,IAAI,oBAAoB,UAAU,EAAE,KAAK,IAAI,mBAAiB,cAAc,eAAe,CAAC,CAAC,GAAG,MAAM,GAAG,UAAU,qBAAmB;AAClP,kBAAQ,IAAI,uBAAuB,eAAe;AAClD,cAAI,CAAC,mBAAmB,OAAO,WAAW;AACxC,gBAAI,OAAO,qBAAqB;AAC9B,sBAAQ,IAAI,sBAAsB;AAAA,YACpC;AACA,yBAAa,MAAM,qBAAqB,KAAK;AAE7C,mBAAO,GAAG,KAAK,EAAE,KAAK,MAAM,EAAE,CAAC;AAAA,UACjC,OAAO;AACL,mBAAO,GAAG,IAAI;AAAA,UAChB;AAAA,QACF,CAAC,CAAC;AAAA,MACJ;AACA,aAAO,GAAG,MAAM;AAAA,IAClB,CAAC;AAAA,EAAC;AACJ;AASA,IAAM,gBAAgB;AACtB,IAAM,qBAAqB;AAK3B,IAAM,qCAAN,MAAM,4CAA2C,sBAAsB;AAAA,EACrE,YAAY,QAAQ,OAAO;AACzB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,gBAAgB,IAAI,gBAAgB,KAAK,kCAAkC,CAAC;AACjF,SAAK,QAAQ,EAAE,UAAU,UAAQ,KAAK,OAAO,IAAI;AACjD,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,MAAM,UAAU,UAAU,QAAQ,aAAa,OAAO;AAEpD,QAAI,CAAC,CAAC,YAAY,SAAS,QAAQ,IAAI,MAAM,IAAI;AAC/C,iBAAW,UAAU;AAAA,IACvB;AACA,SAAK,aAAa;AAClB,WAAO,KAAK,kBAAkB,UAAU,UAAU,QAAQ,UAAU,EAAE,KAAK,SAAS,OAAK,KAAK,WAAW,CAAC,CAAC,GAAG,IAAI,MAAM,KAAK,uBAAuB,CAAC,CAAC;AAAA,EACxJ;AAAA,EACA,SAAS;AACP,SAAK,aAAa;AAClB,WAAO,GAAG,IAAI;AAAA,EAChB;AAAA,EACA,+BAA+B;AAC7B,WAAO,KAAK,cAAc,aAAa;AAAA,EACzC;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK,6BAA6B,EAAE,KAAK,UAAY,WAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK,cAAc,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,SAAS,MAAM;AAC3J,aAAO,CAAC,CAAC,SAAS,OAAO,KAAK,KAAK,EAAE,SAAS,KAAK,aAAY,oBAAI,KAAK,GAAE,QAAQ;AAAA,IACpF,CAAC,GAAG,qBAAqB,CAAC;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACd,UAAM,kBAAkB,aAAa,QAAQ,kBAAkB;AAC/D,QAAI,CAAC,iBAAiB;AACpB,aAAO,GAAG,CAAC;AAAA,IACb;AACA,WAAO,GAAG,OAAO,eAAe,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,WAAO,KAAK,6BAA6B,EAAE,KAAK,IAAI,cAAY,CAAC,CAAC,YAAY,CAAC,CAAC,SAAS,eAAe,SAAS,eAAe,IAAI,CAAC;AAAA,EACvI;AAAA,EACA,WAAW;AACT,WAAO,KAAK,QAAQ,EAAE,KAAK,IAAI,UAAQ,KAAK,KAAK,CAAC;AAAA,EACpD;AAAA,EACA,UAAU;AACR,WAAO,KAAK,6BAA6B,EAAE,KAAK,IAAI,cAAY;AAC9D,UAAI,CAAC,YAAY,CAAC,SAAS,cAAc;AACvC,eAAO,CAAC;AAAA,MACV;AACA,YAAM,cAAc,KAAK,mBAAmB,SAAS,YAAY;AACjE,aAAO;AAAA,QACL,UAAU,YAAY,QAAQ;AAAA,QAC9B,OAAO,YAAY,QAAQ;AAAA,QAC3B,OAAO;AAAA,MACT;AAAA,IACF,CAAC,GAAG,YAAY,CAAC,CAAC;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,WAAO,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,KAAK,KAAK,QAAQ,KAAK,KAAK,MAAM,UAAU;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,CAAC,CAAC,KAAK,OAAO,KAAK,KAAK,WAAW;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,UAAM,iBAAiB,KAAK,kBAAkB;AAC9C,WAAO,CAAC,CAAC,iBAAiB,eAAe,MAAM;AAAA,EACjD;AAAA,EACA,oCAAoC;AAClC,WAAO,KAAK,MAAM,aAAa,QAAQ,aAAa,KAAK,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,WAAO,KAAK,6BAA6B,EAAE,KAAK,MAAM,GAAG,OAAO,WAAS,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,YAAY,GAAG,IAAI,WAAS;AACtH,YAAM,WAAW,KAAK,mBAAmB,MAAM,YAAY;AAC3D,aAAO;AAAA,QACL;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF,CAAC,GAAG,SAAS,WAAS;AACpB,UAAI,UAAU,IAAI,YAAY;AAC9B,UAAI,OAAO,IAAI,WAAW;AAC1B,gBAAU,QAAQ,IAAI,gBAAgB,mCAAmC;AACzE,aAAO,KAAK,IAAI,cAAc,eAAe,EAAE,IAAI,aAAa,MAAM,YAAY,QAAQ,WAAW,EAAE,IAAI,iBAAiB,MAAM,MAAM,aAAa;AACrJ,aAAO,KAAK,uBAAuB,EAAE,KAAK,SAAS,cAAY,KAAK,MAAM,KAAK,UAAU,MAAM;AAAA,QAC7F;AAAA,QACA,iBAAiB;AAAA,MACnB,CAAC,CAAC,GAAG,SAAS,oBAAkB,KAAK,WAAW,cAAc,CAAC,CAAC;AAAA,IAClE,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,kBAAkB,UAAU,UAAU,QAAQ,YAAY;AACxD,QAAI,UAAU,IAAI,YAAY;AAC9B,QAAI,OAAO,IAAI,WAAW;AAC1B,cAAU,QAAQ,IAAI,gBAAgB,mCAAmC;AACzE,WAAO,KAAK,IAAI,cAAc,oBAAoB;AAClD,QAAI,CAAC,YAAY;AACf,gBAAU,QAAQ,IAAI,iBAAiB,WAAW,KAAK,SAAS,mBAAmB,WAAW,MAAM,QAAQ,CAAC,CAAC,CAAC;AAC/G,aAAO,KAAK,IAAI,YAAY,QAAQ,EAAE,IAAI,YAAY,QAAQ;AAC9D,UAAI,QAAQ;AACV,eAAO,KAAK,IAAI,UAAU,MAAM;AAAA,MAClC;AAAA,IACF,OAAO;AACL,gBAAU,QAAQ,IAAI,WAAW,MAAM;AAAA,IACzC;AACA,WAAO,KAAK,uBAAuB,EAAE,KAAK,SAAS,cAAY,KAAK,MAAM,KAAK,UAAU,MAAM;AAAA,MAC7F;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC,CAAC,CAAC;AAAA,EACL;AAAA,EACA,WAAW,OAAO;AAChB,UAAM,aAAY,oBAAI,KAAK,GAAE,QAAQ,IAAI,MAAM,aAAa;AAC5D,iBAAa,QAAQ,oBAAoB,UAAU,SAAS,CAAC;AAC7D,iBAAa,QAAQ,eAAe,KAAK,UAAU,KAAK,CAAC;AACzD,SAAK,cAAc,KAAK,KAAK;AAC7B,WAAO,GAAG,KAAK;AAAA,EACjB;AAAA,EACA,eAAe;AACb,iBAAa,WAAW,aAAa;AACrC,iBAAa,WAAW,kBAAkB;AAC1C,SAAK,cAAc,KAAK,MAAS;AAAA,EACnC;AAAA,EACA,mBAAmB,aAAa;AAC9B,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,IACF;AACA,UAAM,sBAAsB,YAAY,MAAM,GAAG;AACjD,QAAI,oBAAoB,UAAU,GAAG;AACnC,YAAM,SAAS,KAAK,MAAM,KAAK,oBAAoB,CAAC,CAAC,CAAC;AACtD,YAAM,UAAU,KAAK,MAAM,KAAK,oBAAoB,CAAC,CAAC,CAAC;AACvD,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,UAAM,oBAAoB,KAAK,OAAO,qBAAqB,KAAK,OAAO;AACvE,QAAI,EAAE,CAAC,CAAC,qBAAqB,OAAO,sBAAsB,YAAY,kBAAkB,SAAS,KAAK,aAAa,iBAAiB,IAAI;AACtI,YAAM,QAAQ;AACd,cAAQ,MAAM,KAAK;AACnB,aAAO,WAAW,KAAK;AAAA,IACzB;AACA,WAAO,aAAa,iBAAiB,EAAE,KAAK,IAAI,2BAAyB;AACvE,UAAI,CAAC,sBAAsB,SAAS,GAAG,GAAG;AACxC,iCAAyB;AAAA,MAC3B;AACA,+BAAyB;AACzB,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,yBAAyB;AACvB,QAAI,KAAK,OAAO,0BAA0B,MAAM;AAC9C,WAAK,0BAA0B,EAAE,UAAU;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,cAAc,EAAE,KAAK,IAAI,SAAO,OAAO,KAAK,OAAO,6BAA6B,wBAAwB,6BAA6B,GAAI,GAAG,SAAS,gBAAc,MAAM,cAAa,oBAAI,KAAK,GAAE,QAAQ,CAAC,CAAC,GAAG,SAAS,MAAM,KAAK,aAAa,CAAC,GAAG,SAAS,MAAM,KAAK,0BAA0B,CAAC,CAAC;AAAA,EACjT;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,2CAA2C,mBAAmB;AACjF,aAAO,KAAK,qBAAqB,qCAAuC,SAAS,YAAY,GAAM,SAAY,UAAU,CAAC;AAAA,IAC5H;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,oCAAmC;AAAA,IAC9C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,oCAAN,MAAM,2CAA0C,qBAAqB;AAAA,EACnE,YAAY,QAAQ,uBAAuB,aAAa;AACtD,UAAM;AACN,SAAK,SAAS;AACd,SAAK,wBAAwB;AAC7B,SAAK,cAAc;AACnB,SAAK,sBAAsB,SAAS,EAAE,UAAU,WAAS,KAAK,QAAQ,KAAK;AAC3E,SAAK,sBAAsB,gBAAgB,EAAE,KAAK,SAAS,qBAAmB,IAAI,MAAM,iBAAiB,KAAK,iBAAiB,GAAG,GAAG,MAAS,CAAC,CAAC,GAAG,IAAI,kBAAgB,KAAK,cAAc,KAAK,YAAY,CAAC,CAAC,EAAE,UAAU;AAAA,EAC3N;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACjB,UAAM,oBAAoB,KAAK,OAAO,qBAAqB,KAAK,OAAO;AACvE,QAAI,EAAE,CAAC,CAAC,qBAAqB,OAAO,sBAAsB,YAAY,kBAAkB,SAAS,KAAK,aAAa,iBAAiB,IAAI;AACtI,YAAM,QAAQ;AACd,cAAQ,MAAM,KAAK;AACnB,aAAO,WAAW,KAAK;AAAA,IACzB;AACA,UAAM,qCAAqC,aAAa,iBAAiB;AACzE,UAAM,4CAA4C,aAAa,KAAK,OAAO,eAAe;AAC1F,WAAO,SAAS,CAAC,oCAAoC,yCAAyC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,uBAAuB,eAAe,MAAM;AAC3J,UAAI,CAAC,sBAAsB,SAAS,GAAG,GAAG;AACxC,iCAAyB;AAAA,MAC3B;AACA,aAAO,KAAK,YAAY,IAAI,wBAAwB,yBAAyB,iBAAiB;AAAA,QAC5F,SAAS;AAAA,MACX,CAAC;AAAA,IACH,CAAC,GAAG,IAAI,cAAY,SAAS,IAAI,CAAC;AAAA,EACpC;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,0CAA0C,mBAAmB;AAChF,aAAO,KAAK,qBAAqB,oCAAsC,SAAS,YAAY,GAAM,SAAS,qBAAqB,GAAM,SAAY,UAAU,CAAC;AAAA,IAC/J;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mCAAkC;AAAA,IAC7C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mCAAmC,CAAC;AAAA,IAC1G,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,OAAN,MAAW;AAAC;AAKZ,IAAME,cAAN,MAAM,YAAW;AAAA,EACf,YAAY,cAAc;AACxB,QAAI,cAAc;AAChB,YAAM,IAAI,MAAM,6FAA6F;AAAA,IAC/G;AAAA,EACF;AAAA,EACA,OAAO,QAAQ,SAAS;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,QAAQ,uBAAuB;AAAA,QACzC,SAAS;AAAA,QACT,UAAU,OAAO,OAAO,CAAC,GAAG,yBAAyB,QAAQ,MAAM;AAAA,MACrE,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAUF;AAAA,QACV,MAAM,CAAC,cAAc,qBAAqB;AAAA,QAC1C,OAAO;AAAA,MACT,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,aAAO,KAAK,qBAAqB,aAAe,SAAS,aAAY,EAAE,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA,EAEA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkBE,aAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAMA;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;", "names": ["LogLevel", "EventTypes", "ValidationResult", "AuthInterceptor", "PRIVILEGE", "AuthModule"]}