using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using BCI.DocupediaBot.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;

namespace BCI.DocupediaBot.Persistence.EF.Repositories
{
  public class CollectionsInGroupRepository : EntityRepository<CollectionsInGroup>, ICollectionsInGroupRepository
  {
    public CollectionsInGroupRepository(DocupediaBotDbContext dbContext) : base(dbContext)
    {
    }

    public async Task<List<Guid>> GetGroupIdsByCollectionIdAsync(Guid collectionId)
    {
      return await DbContext.Set<CollectionsInGroup>()
        .AsNoTracking()
        .Where(cig => cig.CollectionId == collectionId && !cig.IsDeleted)
        .Select(cig => cig.GroupId)
        .ToListAsync();
    }

    public async Task<List<Guid>> GetCollectionIdsByGroupIdAsync(Guid groupId)
    {
      return await DbContext.Set<CollectionsInGroup>()
        .AsNoTracking()
        .Where(cig => cig.GroupId == groupId && !cig.IsDeleted)
        .Select(cig => cig.CollectionId)
        .ToListAsync();
    }

    public async Task<List<Guid>> GetCollectionIdsByGroupIdsAsync(IEnumerable<Guid> groupIds)
    {
      var groupIdList = groupIds.ToList();
      if (!groupIdList.Any())
      {
        return new List<Guid>();
      }

      return await DbContext.Set<CollectionsInGroup>()
        .AsNoTracking()
        .Where(cig => groupIdList.Contains(cig.GroupId) && !cig.IsDeleted)
        .Select(cig => cig.CollectionId)
        .Distinct()
        .ToListAsync();
    }

    public async Task<bool> ExistsMappingAsync(Guid collectionId, Guid groupId)
    {
      return await DbContext.Set<CollectionsInGroup>()
        .AsNoTracking()
        .AnyAsync(cig => cig.CollectionId == collectionId && cig.GroupId == groupId && !cig.IsDeleted);
    }

    public async Task DeleteByCollectionIdAsync(Guid collectionId)
    {
      var mappings = await DbContext.Set<CollectionsInGroup>()
        .Where(cig => cig.CollectionId == collectionId && !cig.IsDeleted)
        .ToListAsync();

      foreach (var mapping in mappings)
      {
        mapping.IsDeleted = true;
      }

      await DbContext.SaveChangesAsync();
    }

    public async Task DeleteByGroupIdAsync(Guid groupId)
    {
      var mappings = await DbContext.Set<CollectionsInGroup>()
        .Where(cig => cig.GroupId == groupId && !cig.IsDeleted)
        .ToListAsync();

      foreach (var mapping in mappings)
      {
        mapping.IsDeleted = true;
      }

      await DbContext.SaveChangesAsync();
    }

    public async Task DeleteMappingAsync(Guid collectionId, Guid groupId)
    {
      var mapping = await DbContext.Set<CollectionsInGroup>()
        .FirstOrDefaultAsync(cig => cig.CollectionId == collectionId && cig.GroupId == groupId && !cig.IsDeleted);

      if (mapping != null)
      {
        mapping.IsDeleted = true;
        await DbContext.SaveChangesAsync();
      }
    }

    public async Task<Dictionary<Guid, List<Guid>>> GetGroupIdsByCollectionIdsBatchAsync(IEnumerable<Guid> collectionIds)
    {
      var collectionIdList = collectionIds.ToList();
      if (!collectionIdList.Any())
      {
        return new Dictionary<Guid, List<Guid>>();
      }

      var mappings = await DbContext.Set<CollectionsInGroup>()
        .AsNoTracking()
        .Where(cig => collectionIdList.Contains(cig.CollectionId) && !cig.IsDeleted)
        .ToListAsync();

      return collectionIdList.ToDictionary(
        collectionId => collectionId,
        collectionId => mappings.Where(m => m.CollectionId == collectionId).Select(m => m.GroupId).ToList()
      );
    }
  }
}
