/*
 * Copyright (C) 2018 Robert <PERSON> GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import {
  applyPolyfills,
  defineCustomElements,
} from '@bci-web-core/web-components/loader';

import { environment } from './environments/environment';
import { OIDC_INFO } from '@shared/injectors';
import { AppModuleWithPortal } from './app/app.module.portal';
import { AppModuleWithoutPortal } from './app/app.module.noportal';

if (environment.production) {
  enableProdMode();
}

applyPolyfills().then(() => {
  defineCustomElements(window);
});

if (environment.portal) {
  const macmaConfig$ = fetch(
    `${environment.baseUrl}/${environment.apiSuffix}/${environment.apiVersion}/OidcConfiguration`
  ).then((res) => res.json());

  macmaConfig$
    .then((oidcConfig) => {
      platformBrowserDynamic([{ provide: OIDC_INFO, useValue: oidcConfig }])
        .bootstrapModule(AppModuleWithPortal)
        .catch((err) => console.error(err));
    })
    .catch((err) => console.log(err));
} else {
  platformBrowserDynamic()
    .bootstrapModule(AppModuleWithoutPortal)
    .catch((err) => console.error(err));
}
