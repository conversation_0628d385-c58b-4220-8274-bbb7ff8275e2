﻿using System;
using System.Collections.Generic;

namespace BCI.DocupediaBot.Application.Services.Job
{
  public class JobConfig
	{
		public string JobName { get; set; }
		public string JobGroup { get; set; }
		public string JobType { get; set; }
		public JobScheduleConfig Schedule { get; set; }
		public Dictionary<string, object> JobData { get; set; } = new Dictionary<string, object>();
	}

	public class JobScheduleConfig
	{
		public ScheduleType ScheduleType { get; set; }
		public DayOfWeek? DayOfWeek { get; set; }
		public int? Hour { get; set; }
		public int? Minute { get; set; }
		public int? IntervalInMinutes { get; set; }
	}

	public enum ScheduleType
	{
		Daily,
		Weekly,
		Interval
	}
}
