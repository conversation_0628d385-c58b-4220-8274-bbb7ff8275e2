﻿// <auto-generated />
using System;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{
    [DbContext(typeof(DocupediaBotDbContext))]
    partial class DocupediaBotDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.ChatHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Answer")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("CollectionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Prompt")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Question")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TransformedQuestion")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("Question");

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("CreationTime", "TenantId", "IsDeleted");

                    b.HasIndex("Creator", "TenantId", "IsDeleted");

                    b.HasIndex("CollectionId", "CreationTime", "TenantId", "IsDeleted");

                    b.ToTable("ChatHistory", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Collection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ChunkSize")
                        .HasColumnType("integer");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("EmbeddingModel")
                        .HasColumnType("integer");

                    b.Property<int?>("IntervalNumber")
                        .HasColumnType("integer");

                    b.Property<bool>("IsAutomaticUpdate")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<TimeOnly?>("UpdateTime")
                        .HasColumnType("time without time zone");

                    b.Property<int?>("UpdateType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EmbeddingModel");

                    b.HasIndex("IsAutomaticUpdate");

                    b.HasIndex("Name");

                    b.HasIndex("Status");

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("CreationTime", "TenantId", "IsDeleted");

                    b.HasIndex("Creator", "TenantId", "IsDeleted");

                    b.HasIndex("Status", "TenantId", "IsDeleted");

                    b.ToTable("Collection", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.CollectionsInGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CollectionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CollectionsInGroups");
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Content", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("EmbeddingVersionNo")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OriginContent")
                        .HasColumnType("text");

                    b.Property<string>("Path")
                        .HasColumnType("text");

                    b.Property<string>("ProcessedContent")
                        .HasColumnType("text");

                    b.Property<string>("SourceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("SourceModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SummarizedContent")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.Property<int>("VersionNo")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EmbeddingVersionNo");

                    b.HasIndex("SourceId")
                        .IsUnique();

                    b.HasIndex("SourceModificationTime");

                    b.HasIndex("Title");

                    b.HasIndex("VersionNo");

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("CreationTime", "TenantId", "IsDeleted");

                    b.HasIndex("Creator", "TenantId", "IsDeleted");

                    b.HasIndex("SourceId", "VersionNo", "EmbeddingVersionNo");

                    b.HasIndex("VersionNo", "TenantId", "IsDeleted");

                    b.ToTable("Content", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.ContentsInPage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ContentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ContentId");

                    b.HasIndex("PageId");

                    b.HasIndex("PageId", "ContentId")
                        .IsUnique();

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("ContentId", "TenantId", "IsDeleted");

                    b.HasIndex("PageId", "TenantId", "IsDeleted");

                    b.ToTable("ContentsInPage", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.Page", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIncludeChild")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SourceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IsIncludeChild");

                    b.HasIndex("SourceId")
                        .IsUnique();

                    b.HasIndex("Title");

                    b.HasIndex("Url");

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("CreationTime", "TenantId", "IsDeleted");

                    b.HasIndex("Creator", "TenantId", "IsDeleted");

                    b.ToTable("Page", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.PagesInCollection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CollectionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEmbedding")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PageId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CollectionId");

                    b.HasIndex("IsEmbedding");

                    b.HasIndex("PageId");

                    b.HasIndex("CollectionId", "IsEmbedding");

                    b.HasIndex("CollectionId", "PageId")
                        .IsUnique();

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("CollectionId", "TenantId", "IsDeleted");

                    b.HasIndex("PageId", "TenantId", "IsDeleted");

                    b.ToTable("PagesInCollection", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.SysGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Size")
                        .HasColumnType("integer");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("Size");

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("CreationTime", "TenantId", "IsDeleted");

                    b.HasIndex("Creator", "TenantId", "IsDeleted");

                    b.ToTable("SysGroup", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.SysUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Department")
                        .HasColumnType("text");

                    b.Property<string>("DocupediaToken")
                        .HasColumnType("text");

                    b.Property<Guid?>("FavCollecitonId")
                        .HasColumnType("uuid");

                    b.Property<string>("GivenName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Mail")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SN")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserNTAccount")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Department");

                    b.HasIndex("FavCollecitonId");

                    b.HasIndex("Mail");

                    b.HasIndex("Status");

                    b.HasIndex("UserNTAccount")
                        .IsUnique();

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("CreationTime", "TenantId", "IsDeleted");

                    b.HasIndex("Creator", "TenantId", "IsDeleted");

                    b.ToTable("SysUser", (string)null);
                });

            modelBuilder.Entity("BCI.DocupediaBot.Domain.Entities.SysUsersInGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Creator")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("ModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Modifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("GroupId");

                    b.HasIndex("UserId");

                    b.HasIndex("TenantId", "IsDeleted");

                    b.HasIndex("UserId", "GroupId")
                        .IsUnique();

                    b.HasIndex("GroupId", "TenantId", "IsDeleted");

                    b.HasIndex("UserId", "TenantId", "IsDeleted");

                    b.ToTable("SysUsersInGroup", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
