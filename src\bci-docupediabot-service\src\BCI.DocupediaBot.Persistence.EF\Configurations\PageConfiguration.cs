﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class PageConfiguration : IEntityTypeConfiguration<Page>
  {
    public void Configure(EntityTypeBuilder<Page> entityBuilder)
    {
      entityBuilder.ToTable(nameof(Page));


      entityBuilder.HasIndex(x => x.SourceId).IsUnique();
      entityBuilder.HasIndex(x => x.Title);
      entityBuilder.HasIndex(x => x.Url);
      entityBuilder.HasIndex(x => x.IsIncludeChild);


      entityBuilder.HasIndex(x => new { x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.Creator, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.CreationTime, x.TenantId, x.Is<PERSON> });
		}
  }
}
