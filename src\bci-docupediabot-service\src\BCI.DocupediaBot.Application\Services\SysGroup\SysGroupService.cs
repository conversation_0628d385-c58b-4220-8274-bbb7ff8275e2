﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup;
using BCI.DocupediaBot.Application.Services.SysUsersInGroup;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.SysGroup
{
  public class SysGroupService : ISysGroupService
  {
    private readonly ISysGroupRepository _sysGroupRepository;
    private readonly ISysUserRepository _sysUserRepository;
    private readonly ISysUsersInGroupService _sysUsersInGroupService;
    private readonly ICurrentUserAccessor _currentUserAccessor;
    private readonly IMapper _mapper;
    private readonly ILogger<SysGroupService> _logger;

    public SysGroupService(
      ISysGroupRepository sysGroupRepository,
      ISysUserRepository sysUserRepository,
      ISysUsersInGroupService sysUsersInGroupService,
      ICurrentUserAccessor currentUserAccessor,
      IMapper mapper,
      ILogger<SysGroupService> logger)
    {
      _sysGroupRepository = sysGroupRepository ?? throw new ArgumentNullException(nameof(sysGroupRepository));
      _sysUserRepository = sysUserRepository ?? throw new ArgumentNullException(nameof(sysUserRepository));
      _sysUsersInGroupService = sysUsersInGroupService ?? throw new ArgumentNullException(nameof(sysUsersInGroupService));
      _currentUserAccessor = currentUserAccessor ?? throw new ArgumentNullException(nameof(currentUserAccessor));
      _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ResponseResult> AddGroupAsync(SysGroupAddDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("AddGroupAsync received null DTO.");
        return new ResponseResult { IsSuccess = false, Msg = "Group data cannot be null." };
      }

      _logger.LogInformation("Adding new group with name: {Name}", dto.Name);

      var group = _mapper.Map<Domain.Entities.SysGroup>(dto);

      try
      {
        await _sysGroupRepository.CreateAsync(group);
        _logger.LogInformation("Group {GroupId} added successfully.", group.Id);
        return new ResponseResult { IsSuccess = true, Msg = "Group added successfully." };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to add group with name: {Name}", dto.Name);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to add group: {ex.Message}" };
      }
    }

    public async Task<ResponseResult> DeleteGroupByGroupIdAsync(Guid groupId)
    {
      _logger.LogInformation("Deleting group with ID: {GroupId}", groupId);

      var group = await _sysGroupRepository.GetAsync(groupId);
      if (group == null)
      {
        _logger.LogWarning("Group not found for ID: {GroupId}", groupId);
        return new ResponseResult { IsSuccess = false, Msg = "Group not found." };
      }

      try
      {

        var deleteResult = await _sysUsersInGroupService.DeleteMappingsByGroupIdAsync(groupId);
        if (!deleteResult.IsSuccess)
        {
          _logger.LogWarning("Failed to delete user mappings for group ID: {GroupId}. Error: {Error}", groupId, deleteResult.Msg);
          return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete user mappings: {deleteResult.Msg}" };
        }


        await _sysGroupRepository.DeleteAsync(group);

        _logger.LogInformation("Group {GroupId} and its user mappings deleted successfully.", groupId);
        return new ResponseResult { IsSuccess = true, Msg = "Group and its user mappings deleted successfully." };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete group with ID: {GroupId}", groupId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete group: {ex.Message}" };
      }
    }

    public async Task<List<SysGroupResponseDTO>> QueryGroupsAsync()
    {
      _logger.LogInformation("Querying all groups.");

      try
      {
        var groups = await _sysGroupRepository.GetAllAsync();
        if (!groups.Any())
        {
          _logger.LogInformation("No groups found in database.");
          return new List<SysGroupResponseDTO>();
        }


        var groupIds = groups.Select(g => g.Id).ToList();
        var groupUsersDict = await _sysUsersInGroupService.QueryUserIdsByGroupIdsBatchAsync(groupIds);


        var allUserIds = groupUsersDict.Values.SelectMany(userIds => userIds).Distinct().ToList();
        var allUsers = allUserIds.Any()
          ? await _sysUserRepository.QueryAsync(u => allUserIds.Contains(u.Id))
          : new List<Domain.Entities.SysUser>();
        var userDict = allUsers.ToDictionary(u => u.Id, u => u);

        var groupDtos = new List<SysGroupResponseDTO>();

        foreach (var group in groups)
        {
          var groupDto = _mapper.Map<SysGroupResponseDTO>(group);


          if (groupUsersDict.TryGetValue(group.Id, out var userIds) && userIds.Any())
          {
            var users = userIds
              .Where(userId => userDict.ContainsKey(userId))
              .Select(userId => _mapper.Map<BCI.DocupediaBot.Application.Contracts.Dtos.SysUser.SysUserResponseDTO>(userDict[userId]))
              .ToList();
            groupDto.Users = users;
          }
          else
          {
            groupDto.Users = new List<BCI.DocupediaBot.Application.Contracts.Dtos.SysUser.SysUserResponseDTO>();
          }

          groupDtos.Add(groupDto);
        }

        _logger.LogInformation("Retrieved {Count} groups with user information.", groupDtos.Count);
        return groupDtos;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query groups.");
        return new List<SysGroupResponseDTO>();
      }
    }

    public async Task<SysGroupResponseDTO?> QueryGroupWithUsersAsync(Guid groupId)
    {
      _logger.LogInformation("Querying group with users for ID: {GroupId}", groupId);

      var group = await _sysGroupRepository.GetAsync(groupId);
      if (group == null)
      {
        _logger.LogWarning("Group not found for ID: {GroupId}", groupId);
        return null;
      }

      try
      {
        var userIds = await _sysUsersInGroupService.QueryUserIdsByGroupIdAsync(groupId);
        if (userIds == null || !userIds.Any())
        {
          _logger.LogInformation("No users found for group ID: {GroupId}", groupId);
          var emptyDto = _mapper.Map<SysGroupResponseDTO>(group);
          emptyDto.Users = new List<BCI.DocupediaBot.Application.Contracts.Dtos.SysUser.SysUserResponseDTO>();
          return emptyDto;
        }


        var users = await _sysUserRepository.QueryAsync(u => userIds.Contains(u.Id));
        var userDtos = users.Select(u => _mapper.Map<BCI.DocupediaBot.Application.Contracts.Dtos.SysUser.SysUserResponseDTO>(u))
                           .ToList();

        var groupDto = _mapper.Map<SysGroupResponseDTO>(group);
        groupDto.Users = userDtos;

        _logger.LogInformation("Retrieved group {GroupId} with {UserCount} users.", groupId, userDtos.Count);
        return groupDto;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query group with users for ID: {GroupId}", groupId);
        return null;
      }
    }

    public async Task<ResponseResult> UpdateGroupAsync(SysGroupUpdateDTO dto)
    {
      if (dto == null)
      {
        _logger.LogWarning("UpdateGroupAsync received null DTO.");
        return new ResponseResult { IsSuccess = false, Msg = "Group data cannot be null." };
      }

      _logger.LogInformation("Updating group with ID: {GroupId}", dto.Id);

      var group = await _sysGroupRepository.GetAsync(dto.Id);
      if (group == null)
      {
        _logger.LogWarning("Group not found for ID: {GroupId}", dto.Id);
        return new ResponseResult { IsSuccess = false, Msg = $"Group with ID {dto.Id} not found." };
      }

      try
      {
        _mapper.Map(dto, group);
        await _sysGroupRepository.UpdateAsync(group);

        _logger.LogInformation("Group {GroupId} updated successfully.", dto.Id);
        return new ResponseResult { IsSuccess = true, Msg = "Group updated successfully." };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to update group with ID: {GroupId}", dto.Id);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to update group: {ex.Message}" };
      }
    }
  }
}