{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/checkbox-collection.interface-4f1b87dc.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\n/*\n * Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\n */\nvar CollectionType;\n(function (CollectionType) {\n  CollectionType[CollectionType[\"Multi\"] = 0] = \"Multi\";\n  CollectionType[CollectionType[\"Single\"] = 1] = \"Single\";\n})(CollectionType || (CollectionType = {}));\nexport { CollectionType as C };\n\n"], "mappings": ";AAIA,IAAI;AAAA,CACH,SAAUA,iBAAgB;AACzB,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,CAAC,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,CAAC,IAAI;AACjD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;", "names": ["CollectionType"]}