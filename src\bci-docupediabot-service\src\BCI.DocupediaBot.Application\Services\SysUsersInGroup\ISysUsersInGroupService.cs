﻿using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.SysUsersInGroup
{
  public interface ISysUsersInGroupService
  {
    Task<List<Guid>> QueryUserIdsByGroupIdAsync(Guid groupId);
    Task<List<Guid>> QueryGroupIdsByUserIdAsync(Guid userId);


    Task<Dictionary<Guid, List<Guid>>> QueryGroupIdsByUserIdsBatchAsync(IEnumerable<Guid> userIds);
    Task<Dictionary<Guid, List<Guid>>> QueryUserIdsByGroupIdsBatchAsync(IEnumerable<Guid> groupIds);
    Task<List<Guid>> QueryUserIdsByGroupIdsFlattenAsync(IEnumerable<Guid> groupIds);
    Task<List<Guid>> QueryGroupIdsByUserIdsFlattenAsync(IEnumerable<Guid> userIds);


    Task<bool> IsUserInGroupAsync(Guid userId, Guid groupId);
    Task<bool> HasCommonGroupAsync(Guid userId1, Guid userId2);

    Task<ResponseResult> DeleteMappingsByUserIdAsync(Guid userId);
    Task<ResponseResult> DeleteMappingsByGroupIdAsync(Guid groupId);
    Task<ResponseResult> AddMappingAsync(Guid userId, Guid groupId);
    Task<ResponseResult> DeleteMappingAsync(Guid userId, Guid groupId);
  }
}