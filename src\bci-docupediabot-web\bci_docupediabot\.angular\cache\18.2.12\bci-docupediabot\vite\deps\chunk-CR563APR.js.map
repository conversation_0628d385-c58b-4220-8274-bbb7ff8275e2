{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/component-d69b424e.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { _ as __extends, a as __assign, c as __values, M as MDCFoundation, m as matches, b as MDCComponent } from './ponyfill-78459bda.js';\n\n/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * Determine whether the current browser supports passive event listeners, and\n * if so, use them.\n */\nfunction applyPassive(globalObj) {\n  if (globalObj === void 0) {\n    globalObj = window;\n  }\n  return supportsPassiveOption(globalObj) ? {\n    passive: true\n  } : false;\n}\nfunction supportsPassiveOption(globalObj) {\n  if (globalObj === void 0) {\n    globalObj = window;\n  }\n  // See\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener\n  var passiveSupported = false;\n  try {\n    var options = {\n      // This function will be called when the browser\n      // attempts to access the passive property.\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    var handler = function () {};\n    globalObj.document.addEventListener('test', handler, options);\n    globalObj.document.removeEventListener('test', handler, options);\n  } catch (err) {\n    passiveSupported = false;\n  }\n  return passiveSupported;\n}\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses = {\n  // Ripple is a special case where the \"root\" component is really a \"mixin\" of sorts,\n  // given that it's an 'upgrade' to an existing component. That being said it is the root\n  // CSS class that all other CSS classes derive from.\n  BG_FOCUSED: 'mdc-ripple-upgraded--background-focused',\n  FG_ACTIVATION: 'mdc-ripple-upgraded--foreground-activation',\n  FG_DEACTIVATION: 'mdc-ripple-upgraded--foreground-deactivation',\n  ROOT: 'mdc-ripple-upgraded',\n  UNBOUNDED: 'mdc-ripple-upgraded--unbounded'\n};\nvar strings = {\n  VAR_FG_SCALE: '--mdc-ripple-fg-scale',\n  VAR_FG_SIZE: '--mdc-ripple-fg-size',\n  VAR_FG_TRANSLATE_END: '--mdc-ripple-fg-translate-end',\n  VAR_FG_TRANSLATE_START: '--mdc-ripple-fg-translate-start',\n  VAR_LEFT: '--mdc-ripple-left',\n  VAR_TOP: '--mdc-ripple-top'\n};\nvar numbers = {\n  DEACTIVATION_TIMEOUT_MS: 225,\n  FG_DEACTIVATION_MS: 150,\n  INITIAL_ORIGIN_SCALE: 0.6,\n  PADDING: 10,\n  TAP_DELAY_MS: 300 // Delay between touch and simulated mouse events on touch devices\n};\n\n/**\n * Stores result from supportsCssVariables to avoid redundant processing to\n * detect CSS custom variable support.\n */\nvar supportsCssVariables_;\nfunction supportsCssVariables(windowObj, forceRefresh) {\n  if (forceRefresh === void 0) {\n    forceRefresh = false;\n  }\n  var CSS = windowObj.CSS;\n  var supportsCssVars = supportsCssVariables_;\n  if (typeof supportsCssVariables_ === 'boolean' && !forceRefresh) {\n    return supportsCssVariables_;\n  }\n  var supportsFunctionPresent = CSS && typeof CSS.supports === 'function';\n  if (!supportsFunctionPresent) {\n    return false;\n  }\n  var explicitlySupportsCssVars = CSS.supports('--css-vars', 'yes');\n  // See: https://bugs.webkit.org/show_bug.cgi?id=154669\n  // See: README section on Safari\n  var weAreFeatureDetectingSafari10plus = CSS.supports('(--css-vars: yes)') && CSS.supports('color', '#00000000');\n  supportsCssVars = explicitlySupportsCssVars || weAreFeatureDetectingSafari10plus;\n  if (!forceRefresh) {\n    supportsCssVariables_ = supportsCssVars;\n  }\n  return supportsCssVars;\n}\nfunction getNormalizedEventCoords(evt, pageOffset, clientRect) {\n  if (!evt) {\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  var x = pageOffset.x,\n    y = pageOffset.y;\n  var documentX = x + clientRect.left;\n  var documentY = y + clientRect.top;\n  var normalizedX;\n  var normalizedY;\n  // Determine touch point relative to the ripple container.\n  if (evt.type === 'touchstart') {\n    var touchEvent = evt;\n    normalizedX = touchEvent.changedTouches[0].pageX - documentX;\n    normalizedY = touchEvent.changedTouches[0].pageY - documentY;\n  } else {\n    var mouseEvent = evt;\n    normalizedX = mouseEvent.pageX - documentX;\n    normalizedY = mouseEvent.pageY - documentY;\n  }\n  return {\n    x: normalizedX,\n    y: normalizedY\n  };\n}\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n// Activation events registered on the root element of each instance for activation\nvar ACTIVATION_EVENT_TYPES = ['touchstart', 'pointerdown', 'mousedown', 'keydown'];\n// Deactivation events registered on documentElement when a pointer-related down event occurs\nvar POINTER_DEACTIVATION_EVENT_TYPES = ['touchend', 'pointerup', 'mouseup', 'contextmenu'];\n// simultaneous nested activations\nvar activatedTargets = [];\nvar MDCRippleFoundation = /** @class */function (_super) {\n  __extends(MDCRippleFoundation, _super);\n  function MDCRippleFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCRippleFoundation.defaultAdapter), adapter)) || this;\n    _this.activationAnimationHasEnded = false;\n    _this.activationTimer = 0;\n    _this.fgDeactivationRemovalTimer = 0;\n    _this.fgScale = '0';\n    _this.frame = {\n      width: 0,\n      height: 0\n    };\n    _this.initialSize = 0;\n    _this.layoutFrame = 0;\n    _this.maxRadius = 0;\n    _this.unboundedCoords = {\n      left: 0,\n      top: 0\n    };\n    _this.activationState = _this.defaultActivationState();\n    _this.activationTimerCallback = function () {\n      _this.activationAnimationHasEnded = true;\n      _this.runDeactivationUXLogicIfReady();\n    };\n    _this.activateHandler = function (e) {\n      _this.activateImpl(e);\n    };\n    _this.deactivateHandler = function () {\n      _this.deactivateImpl();\n    };\n    _this.focusHandler = function () {\n      _this.handleFocus();\n    };\n    _this.blurHandler = function () {\n      _this.handleBlur();\n    };\n    _this.resizeHandler = function () {\n      _this.layout();\n    };\n    return _this;\n  }\n  Object.defineProperty(MDCRippleFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCRippleFoundation, \"strings\", {\n    get: function () {\n      return strings;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCRippleFoundation, \"numbers\", {\n    get: function () {\n      return numbers;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCRippleFoundation, \"defaultAdapter\", {\n    get: function () {\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        browserSupportsCssVars: function () {\n          return true;\n        },\n        computeBoundingRect: function () {\n          return {\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0,\n            width: 0,\n            height: 0\n          };\n        },\n        containsEventTarget: function () {\n          return true;\n        },\n        deregisterDocumentInteractionHandler: function () {\n          return undefined;\n        },\n        deregisterInteractionHandler: function () {\n          return undefined;\n        },\n        deregisterResizeHandler: function () {\n          return undefined;\n        },\n        getWindowPageOffset: function () {\n          return {\n            x: 0,\n            y: 0\n          };\n        },\n        isSurfaceActive: function () {\n          return true;\n        },\n        isSurfaceDisabled: function () {\n          return true;\n        },\n        isUnbounded: function () {\n          return true;\n        },\n        registerDocumentInteractionHandler: function () {\n          return undefined;\n        },\n        registerInteractionHandler: function () {\n          return undefined;\n        },\n        registerResizeHandler: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        updateCssVariable: function () {\n          return undefined;\n        }\n      };\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCRippleFoundation.prototype.init = function () {\n    var _this = this;\n    var supportsPressRipple = this.supportsPressRipple();\n    this.registerRootHandlers(supportsPressRipple);\n    if (supportsPressRipple) {\n      var _a = MDCRippleFoundation.cssClasses,\n        ROOT_1 = _a.ROOT,\n        UNBOUNDED_1 = _a.UNBOUNDED;\n      requestAnimationFrame(function () {\n        _this.adapter.addClass(ROOT_1);\n        if (_this.adapter.isUnbounded()) {\n          _this.adapter.addClass(UNBOUNDED_1);\n          // Unbounded ripples need layout logic applied immediately to set coordinates for both shade and ripple\n          _this.layoutInternal();\n        }\n      });\n    }\n  };\n  MDCRippleFoundation.prototype.destroy = function () {\n    var _this = this;\n    if (this.supportsPressRipple()) {\n      if (this.activationTimer) {\n        clearTimeout(this.activationTimer);\n        this.activationTimer = 0;\n        this.adapter.removeClass(MDCRippleFoundation.cssClasses.FG_ACTIVATION);\n      }\n      if (this.fgDeactivationRemovalTimer) {\n        clearTimeout(this.fgDeactivationRemovalTimer);\n        this.fgDeactivationRemovalTimer = 0;\n        this.adapter.removeClass(MDCRippleFoundation.cssClasses.FG_DEACTIVATION);\n      }\n      var _a = MDCRippleFoundation.cssClasses,\n        ROOT_2 = _a.ROOT,\n        UNBOUNDED_2 = _a.UNBOUNDED;\n      requestAnimationFrame(function () {\n        _this.adapter.removeClass(ROOT_2);\n        _this.adapter.removeClass(UNBOUNDED_2);\n        _this.removeCssVars();\n      });\n    }\n    this.deregisterRootHandlers();\n    this.deregisterDeactivationHandlers();\n  };\n  /**\n   * @param evt Optional event containing position information.\n   */\n  MDCRippleFoundation.prototype.activate = function (evt) {\n    this.activateImpl(evt);\n  };\n  MDCRippleFoundation.prototype.deactivate = function () {\n    this.deactivateImpl();\n  };\n  MDCRippleFoundation.prototype.layout = function () {\n    var _this = this;\n    if (this.layoutFrame) {\n      cancelAnimationFrame(this.layoutFrame);\n    }\n    this.layoutFrame = requestAnimationFrame(function () {\n      _this.layoutInternal();\n      _this.layoutFrame = 0;\n    });\n  };\n  MDCRippleFoundation.prototype.setUnbounded = function (unbounded) {\n    var UNBOUNDED = MDCRippleFoundation.cssClasses.UNBOUNDED;\n    if (unbounded) {\n      this.adapter.addClass(UNBOUNDED);\n    } else {\n      this.adapter.removeClass(UNBOUNDED);\n    }\n  };\n  MDCRippleFoundation.prototype.handleFocus = function () {\n    var _this = this;\n    requestAnimationFrame(function () {\n      return _this.adapter.addClass(MDCRippleFoundation.cssClasses.BG_FOCUSED);\n    });\n  };\n  MDCRippleFoundation.prototype.handleBlur = function () {\n    var _this = this;\n    requestAnimationFrame(function () {\n      return _this.adapter.removeClass(MDCRippleFoundation.cssClasses.BG_FOCUSED);\n    });\n  };\n  /**\n   * We compute this property so that we are not querying information about the client\n   * until the point in time where the foundation requests it. This prevents scenarios where\n   * client-side feature-detection may happen too early, such as when components are rendered on the server\n   * and then initialized at mount time on the client.\n   */\n  MDCRippleFoundation.prototype.supportsPressRipple = function () {\n    return this.adapter.browserSupportsCssVars();\n  };\n  MDCRippleFoundation.prototype.defaultActivationState = function () {\n    return {\n      activationEvent: undefined,\n      hasDeactivationUXRun: false,\n      isActivated: false,\n      isProgrammatic: false,\n      wasActivatedByPointer: false,\n      wasElementMadeActive: false\n    };\n  };\n  /**\n   * supportsPressRipple Passed from init to save a redundant function call\n   */\n  MDCRippleFoundation.prototype.registerRootHandlers = function (supportsPressRipple) {\n    var e_1, _a;\n    if (supportsPressRipple) {\n      try {\n        for (var ACTIVATION_EVENT_TYPES_1 = __values(ACTIVATION_EVENT_TYPES), ACTIVATION_EVENT_TYPES_1_1 = ACTIVATION_EVENT_TYPES_1.next(); !ACTIVATION_EVENT_TYPES_1_1.done; ACTIVATION_EVENT_TYPES_1_1 = ACTIVATION_EVENT_TYPES_1.next()) {\n          var evtType = ACTIVATION_EVENT_TYPES_1_1.value;\n          this.adapter.registerInteractionHandler(evtType, this.activateHandler);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (ACTIVATION_EVENT_TYPES_1_1 && !ACTIVATION_EVENT_TYPES_1_1.done && (_a = ACTIVATION_EVENT_TYPES_1.return)) _a.call(ACTIVATION_EVENT_TYPES_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      if (this.adapter.isUnbounded()) {\n        this.adapter.registerResizeHandler(this.resizeHandler);\n      }\n    }\n    this.adapter.registerInteractionHandler('focus', this.focusHandler);\n    this.adapter.registerInteractionHandler('blur', this.blurHandler);\n  };\n  MDCRippleFoundation.prototype.registerDeactivationHandlers = function (evt) {\n    var e_2, _a;\n    if (evt.type === 'keydown') {\n      this.adapter.registerInteractionHandler('keyup', this.deactivateHandler);\n    } else {\n      try {\n        for (var POINTER_DEACTIVATION_EVENT_TYPES_1 = __values(POINTER_DEACTIVATION_EVENT_TYPES), POINTER_DEACTIVATION_EVENT_TYPES_1_1 = POINTER_DEACTIVATION_EVENT_TYPES_1.next(); !POINTER_DEACTIVATION_EVENT_TYPES_1_1.done; POINTER_DEACTIVATION_EVENT_TYPES_1_1 = POINTER_DEACTIVATION_EVENT_TYPES_1.next()) {\n          var evtType = POINTER_DEACTIVATION_EVENT_TYPES_1_1.value;\n          this.adapter.registerDocumentInteractionHandler(evtType, this.deactivateHandler);\n        }\n      } catch (e_2_1) {\n        e_2 = {\n          error: e_2_1\n        };\n      } finally {\n        try {\n          if (POINTER_DEACTIVATION_EVENT_TYPES_1_1 && !POINTER_DEACTIVATION_EVENT_TYPES_1_1.done && (_a = POINTER_DEACTIVATION_EVENT_TYPES_1.return)) _a.call(POINTER_DEACTIVATION_EVENT_TYPES_1);\n        } finally {\n          if (e_2) throw e_2.error;\n        }\n      }\n    }\n  };\n  MDCRippleFoundation.prototype.deregisterRootHandlers = function () {\n    var e_3, _a;\n    try {\n      for (var ACTIVATION_EVENT_TYPES_2 = __values(ACTIVATION_EVENT_TYPES), ACTIVATION_EVENT_TYPES_2_1 = ACTIVATION_EVENT_TYPES_2.next(); !ACTIVATION_EVENT_TYPES_2_1.done; ACTIVATION_EVENT_TYPES_2_1 = ACTIVATION_EVENT_TYPES_2.next()) {\n        var evtType = ACTIVATION_EVENT_TYPES_2_1.value;\n        this.adapter.deregisterInteractionHandler(evtType, this.activateHandler);\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (ACTIVATION_EVENT_TYPES_2_1 && !ACTIVATION_EVENT_TYPES_2_1.done && (_a = ACTIVATION_EVENT_TYPES_2.return)) _a.call(ACTIVATION_EVENT_TYPES_2);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n    this.adapter.deregisterInteractionHandler('focus', this.focusHandler);\n    this.adapter.deregisterInteractionHandler('blur', this.blurHandler);\n    if (this.adapter.isUnbounded()) {\n      this.adapter.deregisterResizeHandler(this.resizeHandler);\n    }\n  };\n  MDCRippleFoundation.prototype.deregisterDeactivationHandlers = function () {\n    var e_4, _a;\n    this.adapter.deregisterInteractionHandler('keyup', this.deactivateHandler);\n    try {\n      for (var POINTER_DEACTIVATION_EVENT_TYPES_2 = __values(POINTER_DEACTIVATION_EVENT_TYPES), POINTER_DEACTIVATION_EVENT_TYPES_2_1 = POINTER_DEACTIVATION_EVENT_TYPES_2.next(); !POINTER_DEACTIVATION_EVENT_TYPES_2_1.done; POINTER_DEACTIVATION_EVENT_TYPES_2_1 = POINTER_DEACTIVATION_EVENT_TYPES_2.next()) {\n        var evtType = POINTER_DEACTIVATION_EVENT_TYPES_2_1.value;\n        this.adapter.deregisterDocumentInteractionHandler(evtType, this.deactivateHandler);\n      }\n    } catch (e_4_1) {\n      e_4 = {\n        error: e_4_1\n      };\n    } finally {\n      try {\n        if (POINTER_DEACTIVATION_EVENT_TYPES_2_1 && !POINTER_DEACTIVATION_EVENT_TYPES_2_1.done && (_a = POINTER_DEACTIVATION_EVENT_TYPES_2.return)) _a.call(POINTER_DEACTIVATION_EVENT_TYPES_2);\n      } finally {\n        if (e_4) throw e_4.error;\n      }\n    }\n  };\n  MDCRippleFoundation.prototype.removeCssVars = function () {\n    var _this = this;\n    var rippleStrings = MDCRippleFoundation.strings;\n    var keys = Object.keys(rippleStrings);\n    keys.forEach(function (key) {\n      if (key.indexOf('VAR_') === 0) {\n        _this.adapter.updateCssVariable(rippleStrings[key], null);\n      }\n    });\n  };\n  MDCRippleFoundation.prototype.activateImpl = function (evt) {\n    var _this = this;\n    if (this.adapter.isSurfaceDisabled()) {\n      return;\n    }\n    var activationState = this.activationState;\n    if (activationState.isActivated) {\n      return;\n    }\n    // Avoid reacting to follow-on events fired by touch device after an already-processed user interaction\n    var previousActivationEvent = this.previousActivationEvent;\n    var isSameInteraction = previousActivationEvent && evt !== undefined && previousActivationEvent.type !== evt.type;\n    if (isSameInteraction) {\n      return;\n    }\n    activationState.isActivated = true;\n    activationState.isProgrammatic = evt === undefined;\n    activationState.activationEvent = evt;\n    activationState.wasActivatedByPointer = activationState.isProgrammatic ? false : evt !== undefined && (evt.type === 'mousedown' || evt.type === 'touchstart' || evt.type === 'pointerdown');\n    var hasActivatedChild = evt !== undefined && activatedTargets.length > 0 && activatedTargets.some(function (target) {\n      return _this.adapter.containsEventTarget(target);\n    });\n    if (hasActivatedChild) {\n      // Immediately reset activation state, while preserving logic that prevents touch follow-on events\n      this.resetActivationState();\n      return;\n    }\n    if (evt !== undefined) {\n      activatedTargets.push(evt.target);\n      this.registerDeactivationHandlers(evt);\n    }\n    activationState.wasElementMadeActive = this.checkElementMadeActive(evt);\n    if (activationState.wasElementMadeActive) {\n      this.animateActivation();\n    }\n    requestAnimationFrame(function () {\n      // Reset array on next frame after the current event has had a chance to bubble to prevent ancestor ripples\n      activatedTargets = [];\n      if (!activationState.wasElementMadeActive && evt !== undefined && (evt.key === ' ' || evt.keyCode === 32)) {\n        // If space was pressed, try again within an rAF call to detect :active, because different UAs report\n        // active states inconsistently when they're called within event handling code:\n        // - https://bugs.chromium.org/p/chromium/issues/detail?id=635971\n        // - https://bugzilla.mozilla.org/show_bug.cgi?id=1293741\n        // We try first outside rAF to support Edge, which does not exhibit this problem, but will crash if a CSS\n        // variable is set within a rAF callback for a submit button interaction (#2241).\n        activationState.wasElementMadeActive = _this.checkElementMadeActive(evt);\n        if (activationState.wasElementMadeActive) {\n          _this.animateActivation();\n        }\n      }\n      if (!activationState.wasElementMadeActive) {\n        // Reset activation state immediately if element was not made active.\n        _this.activationState = _this.defaultActivationState();\n      }\n    });\n  };\n  MDCRippleFoundation.prototype.checkElementMadeActive = function (evt) {\n    return evt !== undefined && evt.type === 'keydown' ? this.adapter.isSurfaceActive() : true;\n  };\n  MDCRippleFoundation.prototype.animateActivation = function () {\n    var _this = this;\n    var _a = MDCRippleFoundation.strings,\n      VAR_FG_TRANSLATE_START = _a.VAR_FG_TRANSLATE_START,\n      VAR_FG_TRANSLATE_END = _a.VAR_FG_TRANSLATE_END;\n    var _b = MDCRippleFoundation.cssClasses,\n      FG_DEACTIVATION = _b.FG_DEACTIVATION,\n      FG_ACTIVATION = _b.FG_ACTIVATION;\n    var DEACTIVATION_TIMEOUT_MS = MDCRippleFoundation.numbers.DEACTIVATION_TIMEOUT_MS;\n    this.layoutInternal();\n    var translateStart = '';\n    var translateEnd = '';\n    if (!this.adapter.isUnbounded()) {\n      var _c = this.getFgTranslationCoordinates(),\n        startPoint = _c.startPoint,\n        endPoint = _c.endPoint;\n      translateStart = startPoint.x + \"px, \" + startPoint.y + \"px\";\n      translateEnd = endPoint.x + \"px, \" + endPoint.y + \"px\";\n    }\n    this.adapter.updateCssVariable(VAR_FG_TRANSLATE_START, translateStart);\n    this.adapter.updateCssVariable(VAR_FG_TRANSLATE_END, translateEnd);\n    // Cancel any ongoing activation/deactivation animations\n    clearTimeout(this.activationTimer);\n    clearTimeout(this.fgDeactivationRemovalTimer);\n    this.rmBoundedActivationClasses();\n    this.adapter.removeClass(FG_DEACTIVATION);\n    // Force layout in order to re-trigger the animation.\n    this.adapter.computeBoundingRect();\n    this.adapter.addClass(FG_ACTIVATION);\n    this.activationTimer = setTimeout(function () {\n      _this.activationTimerCallback();\n    }, DEACTIVATION_TIMEOUT_MS);\n  };\n  MDCRippleFoundation.prototype.getFgTranslationCoordinates = function () {\n    var _a = this.activationState,\n      activationEvent = _a.activationEvent,\n      wasActivatedByPointer = _a.wasActivatedByPointer;\n    var startPoint;\n    if (wasActivatedByPointer) {\n      startPoint = getNormalizedEventCoords(activationEvent, this.adapter.getWindowPageOffset(), this.adapter.computeBoundingRect());\n    } else {\n      startPoint = {\n        x: this.frame.width / 2,\n        y: this.frame.height / 2\n      };\n    }\n    // Center the element around the start point.\n    startPoint = {\n      x: startPoint.x - this.initialSize / 2,\n      y: startPoint.y - this.initialSize / 2\n    };\n    var endPoint = {\n      x: this.frame.width / 2 - this.initialSize / 2,\n      y: this.frame.height / 2 - this.initialSize / 2\n    };\n    return {\n      startPoint: startPoint,\n      endPoint: endPoint\n    };\n  };\n  MDCRippleFoundation.prototype.runDeactivationUXLogicIfReady = function () {\n    var _this = this;\n    // This method is called both when a pointing device is released, and when the activation animation ends.\n    // The deactivation animation should only run after both of those occur.\n    var FG_DEACTIVATION = MDCRippleFoundation.cssClasses.FG_DEACTIVATION;\n    var _a = this.activationState,\n      hasDeactivationUXRun = _a.hasDeactivationUXRun,\n      isActivated = _a.isActivated;\n    var activationHasEnded = hasDeactivationUXRun || !isActivated;\n    if (activationHasEnded && this.activationAnimationHasEnded) {\n      this.rmBoundedActivationClasses();\n      this.adapter.addClass(FG_DEACTIVATION);\n      this.fgDeactivationRemovalTimer = setTimeout(function () {\n        _this.adapter.removeClass(FG_DEACTIVATION);\n      }, numbers.FG_DEACTIVATION_MS);\n    }\n  };\n  MDCRippleFoundation.prototype.rmBoundedActivationClasses = function () {\n    var FG_ACTIVATION = MDCRippleFoundation.cssClasses.FG_ACTIVATION;\n    this.adapter.removeClass(FG_ACTIVATION);\n    this.activationAnimationHasEnded = false;\n    this.adapter.computeBoundingRect();\n  };\n  MDCRippleFoundation.prototype.resetActivationState = function () {\n    var _this = this;\n    this.previousActivationEvent = this.activationState.activationEvent;\n    this.activationState = this.defaultActivationState();\n    // Touch devices may fire additional events for the same interaction within a short time.\n    // Store the previous event until it's safe to assume that subsequent events are for new interactions.\n    setTimeout(function () {\n      return _this.previousActivationEvent = undefined;\n    }, MDCRippleFoundation.numbers.TAP_DELAY_MS);\n  };\n  MDCRippleFoundation.prototype.deactivateImpl = function () {\n    var _this = this;\n    var activationState = this.activationState;\n    // This can happen in scenarios such as when you have a keyup event that blurs the element.\n    if (!activationState.isActivated) {\n      return;\n    }\n    var state = __assign({}, activationState);\n    if (activationState.isProgrammatic) {\n      requestAnimationFrame(function () {\n        _this.animateDeactivation(state);\n      });\n      this.resetActivationState();\n    } else {\n      this.deregisterDeactivationHandlers();\n      requestAnimationFrame(function () {\n        _this.activationState.hasDeactivationUXRun = true;\n        _this.animateDeactivation(state);\n        _this.resetActivationState();\n      });\n    }\n  };\n  MDCRippleFoundation.prototype.animateDeactivation = function (_a) {\n    var wasActivatedByPointer = _a.wasActivatedByPointer,\n      wasElementMadeActive = _a.wasElementMadeActive;\n    if (wasActivatedByPointer || wasElementMadeActive) {\n      this.runDeactivationUXLogicIfReady();\n    }\n  };\n  MDCRippleFoundation.prototype.layoutInternal = function () {\n    var _this = this;\n    this.frame = this.adapter.computeBoundingRect();\n    var maxDim = Math.max(this.frame.height, this.frame.width);\n    // Surface diameter is treated differently for unbounded vs. bounded ripples.\n    // Unbounded ripple diameter is calculated smaller since the surface is expected to already be padded appropriately\n    // to extend the hitbox, and the ripple is expected to meet the edges of the padded hitbox (which is typically\n    // square). Bounded ripples, on the other hand, are fully expected to expand beyond the surface's longest diameter\n    // (calculated based on the diagonal plus a constant padding), and are clipped at the surface's border via\n    // `overflow: hidden`.\n    var getBoundedRadius = function () {\n      var hypotenuse = Math.sqrt(Math.pow(_this.frame.width, 2) + Math.pow(_this.frame.height, 2));\n      return hypotenuse + MDCRippleFoundation.numbers.PADDING;\n    };\n    this.maxRadius = this.adapter.isUnbounded() ? maxDim : getBoundedRadius();\n    // Ripple is sized as a fraction of the largest dimension of the surface, then scales up using a CSS scale transform\n    var initialSize = Math.floor(maxDim * MDCRippleFoundation.numbers.INITIAL_ORIGIN_SCALE);\n    // Unbounded ripple size should always be even number to equally center align.\n    if (this.adapter.isUnbounded() && initialSize % 2 !== 0) {\n      this.initialSize = initialSize - 1;\n    } else {\n      this.initialSize = initialSize;\n    }\n    this.fgScale = \"\" + this.maxRadius / this.initialSize;\n    this.updateLayoutCssVars();\n  };\n  MDCRippleFoundation.prototype.updateLayoutCssVars = function () {\n    var _a = MDCRippleFoundation.strings,\n      VAR_FG_SIZE = _a.VAR_FG_SIZE,\n      VAR_LEFT = _a.VAR_LEFT,\n      VAR_TOP = _a.VAR_TOP,\n      VAR_FG_SCALE = _a.VAR_FG_SCALE;\n    this.adapter.updateCssVariable(VAR_FG_SIZE, this.initialSize + \"px\");\n    this.adapter.updateCssVariable(VAR_FG_SCALE, this.fgScale);\n    if (this.adapter.isUnbounded()) {\n      this.unboundedCoords = {\n        left: Math.round(this.frame.width / 2 - this.initialSize / 2),\n        top: Math.round(this.frame.height / 2 - this.initialSize / 2)\n      };\n      this.adapter.updateCssVariable(VAR_LEFT, this.unboundedCoords.left + \"px\");\n      this.adapter.updateCssVariable(VAR_TOP, this.unboundedCoords.top + \"px\");\n    }\n  };\n  return MDCRippleFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCRipple = /** @class */function (_super) {\n  __extends(MDCRipple, _super);\n  function MDCRipple() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.disabled = false;\n    return _this;\n  }\n  MDCRipple.attachTo = function (root, opts) {\n    if (opts === void 0) {\n      opts = {\n        isUnbounded: undefined\n      };\n    }\n    var ripple = new MDCRipple(root);\n    // Only override unbounded behavior if option is explicitly specified\n    if (opts.isUnbounded !== undefined) {\n      ripple.unbounded = opts.isUnbounded;\n    }\n    return ripple;\n  };\n  MDCRipple.createAdapter = function (instance) {\n    return {\n      addClass: function (className) {\n        return instance.root.classList.add(className);\n      },\n      browserSupportsCssVars: function () {\n        return supportsCssVariables(window);\n      },\n      computeBoundingRect: function () {\n        return instance.root.getBoundingClientRect();\n      },\n      containsEventTarget: function (target) {\n        return instance.root.contains(target);\n      },\n      deregisterDocumentInteractionHandler: function (evtType, handler) {\n        return document.documentElement.removeEventListener(evtType, handler, applyPassive());\n      },\n      deregisterInteractionHandler: function (evtType, handler) {\n        return instance.root.removeEventListener(evtType, handler, applyPassive());\n      },\n      deregisterResizeHandler: function (handler) {\n        return window.removeEventListener('resize', handler);\n      },\n      getWindowPageOffset: function () {\n        return {\n          x: window.pageXOffset,\n          y: window.pageYOffset\n        };\n      },\n      isSurfaceActive: function () {\n        return matches(instance.root, ':active');\n      },\n      isSurfaceDisabled: function () {\n        return Boolean(instance.disabled);\n      },\n      isUnbounded: function () {\n        return Boolean(instance.unbounded);\n      },\n      registerDocumentInteractionHandler: function (evtType, handler) {\n        return document.documentElement.addEventListener(evtType, handler, applyPassive());\n      },\n      registerInteractionHandler: function (evtType, handler) {\n        return instance.root.addEventListener(evtType, handler, applyPassive());\n      },\n      registerResizeHandler: function (handler) {\n        return window.addEventListener('resize', handler);\n      },\n      removeClass: function (className) {\n        return instance.root.classList.remove(className);\n      },\n      updateCssVariable: function (varName, value) {\n        return instance.root.style.setProperty(varName, value);\n      }\n    };\n  };\n  Object.defineProperty(MDCRipple.prototype, \"unbounded\", {\n    get: function () {\n      return Boolean(this.isUnbounded);\n    },\n    set: function (unbounded) {\n      this.isUnbounded = Boolean(unbounded);\n      this.setUnbounded();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCRipple.prototype.activate = function () {\n    this.foundation.activate();\n  };\n  MDCRipple.prototype.deactivate = function () {\n    this.foundation.deactivate();\n  };\n  MDCRipple.prototype.layout = function () {\n    this.foundation.layout();\n  };\n  MDCRipple.prototype.getDefaultFoundation = function () {\n    return new MDCRippleFoundation(MDCRipple.createAdapter(this));\n  };\n  MDCRipple.prototype.initialSyncWithDOM = function () {\n    var root = this.root;\n    this.isUnbounded = 'mdcRippleIsUnbounded' in root.dataset;\n  };\n  /**\n   * Closure Compiler throws an access control error when directly accessing a\n   * protected or private property inside a getter/setter, like unbounded above.\n   * By accessing the protected property inside a method, we solve that problem.\n   * That's why this function exists.\n   */\n  MDCRipple.prototype.setUnbounded = function () {\n    this.foundation.setUnbounded(Boolean(this.isUnbounded));\n  };\n  return MDCRipple;\n}(MDCComponent);\nexport { MDCRipple as M, MDCRippleFoundation as a, applyPassive as b };\n\n"], "mappings": ";;;;;;;;;;AA6BA,SAAS,aAAa,WAAW;AAC/B,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AACA,SAAO,sBAAsB,SAAS,IAAI;AAAA,IACxC,SAAS;AAAA,EACX,IAAI;AACN;AACA,SAAS,sBAAsB,WAAW;AACxC,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AAGA,MAAI,mBAAmB;AACvB,MAAI;AACF,QAAI,UAAU;AAAA;AAAA;AAAA,MAGZ,IAAI,UAAU;AACZ,2BAAmB;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,UAAU,WAAY;AAAA,IAAC;AAC3B,cAAU,SAAS,iBAAiB,QAAQ,SAAS,OAAO;AAC5D,cAAU,SAAS,oBAAoB,QAAQ,SAAS,OAAO;AAAA,EACjE,SAAS,KAAK;AACZ,uBAAmB;AAAA,EACrB;AACA,SAAO;AACT;AAwBA,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAIf,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,MAAM;AAAA,EACN,WAAW;AACb;AACA,IAAI,UAAU;AAAA,EACZ,cAAc;AAAA,EACd,aAAa;AAAA,EACb,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,UAAU;AAAA,EACV,SAAS;AACX;AACA,IAAI,UAAU;AAAA,EACZ,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,SAAS;AAAA,EACT,cAAc;AAAA;AAChB;AAMA,IAAI;AACJ,SAAS,qBAAqB,WAAW,cAAc;AACrD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,MAAM,UAAU;AACpB,MAAI,kBAAkB;AACtB,MAAI,OAAO,0BAA0B,aAAa,CAAC,cAAc;AAC/D,WAAO;AAAA,EACT;AACA,MAAI,0BAA0B,OAAO,OAAO,IAAI,aAAa;AAC7D,MAAI,CAAC,yBAAyB;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,4BAA4B,IAAI,SAAS,cAAc,KAAK;AAGhE,MAAI,oCAAoC,IAAI,SAAS,mBAAmB,KAAK,IAAI,SAAS,SAAS,WAAW;AAC9G,oBAAkB,6BAA6B;AAC/C,MAAI,CAAC,cAAc;AACjB,4BAAwB;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,yBAAyB,KAAK,YAAY,YAAY;AAC7D,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,MAAI,IAAI,WAAW,GACjB,IAAI,WAAW;AACjB,MAAI,YAAY,IAAI,WAAW;AAC/B,MAAI,YAAY,IAAI,WAAW;AAC/B,MAAI;AACJ,MAAI;AAEJ,MAAI,IAAI,SAAS,cAAc;AAC7B,QAAI,aAAa;AACjB,kBAAc,WAAW,eAAe,CAAC,EAAE,QAAQ;AACnD,kBAAc,WAAW,eAAe,CAAC,EAAE,QAAQ;AAAA,EACrD,OAAO;AACL,QAAI,aAAa;AACjB,kBAAc,WAAW,QAAQ;AACjC,kBAAc,WAAW,QAAQ;AAAA,EACnC;AACA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAyBA,IAAI,yBAAyB,CAAC,cAAc,eAAe,aAAa,SAAS;AAEjF,IAAI,mCAAmC,CAAC,YAAY,aAAa,WAAW,aAAa;AAEzF,IAAI,mBAAmB,CAAC;AACxB,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACvD,cAAUA,sBAAqB,MAAM;AACrC,aAASA,qBAAoB,SAAS;AACpC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,qBAAoB,cAAc,GAAG,OAAO,CAAC,KAAK;AACtG,YAAM,8BAA8B;AACpC,YAAM,kBAAkB;AACxB,YAAM,6BAA6B;AACnC,YAAM,UAAU;AAChB,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,YAAM,cAAc;AACpB,YAAM,cAAc;AACpB,YAAM,YAAY;AAClB,YAAM,kBAAkB;AAAA,QACtB,MAAM;AAAA,QACN,KAAK;AAAA,MACP;AACA,YAAM,kBAAkB,MAAM,uBAAuB;AACrD,YAAM,0BAA0B,WAAY;AAC1C,cAAM,8BAA8B;AACpC,cAAM,8BAA8B;AAAA,MACtC;AACA,YAAM,kBAAkB,SAAU,GAAG;AACnC,cAAM,aAAa,CAAC;AAAA,MACtB;AACA,YAAM,oBAAoB,WAAY;AACpC,cAAM,eAAe;AAAA,MACvB;AACA,YAAM,eAAe,WAAY;AAC/B,cAAM,YAAY;AAAA,MACpB;AACA,YAAM,cAAc,WAAY;AAC9B,cAAM,WAAW;AAAA,MACnB;AACA,YAAM,gBAAgB,WAAY;AAChC,cAAM,OAAO;AAAA,MACf;AACA,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,sBAAqB,cAAc;AAAA,MACvD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,WAAW;AAAA,MACpD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,WAAW;AAAA,MACpD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,kBAAkB;AAAA,MAC3D,KAAK,WAAY;AACf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,wBAAwB,WAAY;AAClC,mBAAO;AAAA,UACT;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,cACL,KAAK;AAAA,cACL,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,MAAM;AAAA,cACN,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,UACT;AAAA,UACA,sCAAsC,WAAY;AAChD,mBAAO;AAAA,UACT;AAAA,UACA,8BAA8B,WAAY;AACxC,mBAAO;AAAA,UACT;AAAA,UACA,yBAAyB,WAAY;AACnC,mBAAO;AAAA,UACT;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG;AAAA,YACL;AAAA,UACF;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,mBAAmB,WAAY;AAC7B,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,oCAAoC,WAAY;AAC9C,mBAAO;AAAA,UACT;AAAA,UACA,4BAA4B,WAAY;AACtC,mBAAO;AAAA,UACT;AAAA,UACA,uBAAuB,WAAY;AACjC,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,mBAAmB,WAAY;AAC7B,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,qBAAoB,UAAU,OAAO,WAAY;AAC/C,UAAI,QAAQ;AACZ,UAAI,sBAAsB,KAAK,oBAAoB;AACnD,WAAK,qBAAqB,mBAAmB;AAC7C,UAAI,qBAAqB;AACvB,YAAI,KAAKA,qBAAoB,YAC3B,SAAS,GAAG,MACZ,cAAc,GAAG;AACnB,8BAAsB,WAAY;AAChC,gBAAM,QAAQ,SAAS,MAAM;AAC7B,cAAI,MAAM,QAAQ,YAAY,GAAG;AAC/B,kBAAM,QAAQ,SAAS,WAAW;AAElC,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,UAAU,WAAY;AAClD,UAAI,QAAQ;AACZ,UAAI,KAAK,oBAAoB,GAAG;AAC9B,YAAI,KAAK,iBAAiB;AACxB,uBAAa,KAAK,eAAe;AACjC,eAAK,kBAAkB;AACvB,eAAK,QAAQ,YAAYA,qBAAoB,WAAW,aAAa;AAAA,QACvE;AACA,YAAI,KAAK,4BAA4B;AACnC,uBAAa,KAAK,0BAA0B;AAC5C,eAAK,6BAA6B;AAClC,eAAK,QAAQ,YAAYA,qBAAoB,WAAW,eAAe;AAAA,QACzE;AACA,YAAI,KAAKA,qBAAoB,YAC3B,SAAS,GAAG,MACZ,cAAc,GAAG;AACnB,8BAAsB,WAAY;AAChC,gBAAM,QAAQ,YAAY,MAAM;AAChC,gBAAM,QAAQ,YAAY,WAAW;AACrC,gBAAM,cAAc;AAAA,QACtB,CAAC;AAAA,MACH;AACA,WAAK,uBAAuB;AAC5B,WAAK,+BAA+B;AAAA,IACtC;AAIA,IAAAA,qBAAoB,UAAU,WAAW,SAAU,KAAK;AACtD,WAAK,aAAa,GAAG;AAAA,IACvB;AACA,IAAAA,qBAAoB,UAAU,aAAa,WAAY;AACrD,WAAK,eAAe;AAAA,IACtB;AACA,IAAAA,qBAAoB,UAAU,SAAS,WAAY;AACjD,UAAI,QAAQ;AACZ,UAAI,KAAK,aAAa;AACpB,6BAAqB,KAAK,WAAW;AAAA,MACvC;AACA,WAAK,cAAc,sBAAsB,WAAY;AACnD,cAAM,eAAe;AACrB,cAAM,cAAc;AAAA,MACtB,CAAC;AAAA,IACH;AACA,IAAAA,qBAAoB,UAAU,eAAe,SAAU,WAAW;AAChE,UAAI,YAAYA,qBAAoB,WAAW;AAC/C,UAAI,WAAW;AACb,aAAK,QAAQ,SAAS,SAAS;AAAA,MACjC,OAAO;AACL,aAAK,QAAQ,YAAY,SAAS;AAAA,MACpC;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,cAAc,WAAY;AACtD,UAAI,QAAQ;AACZ,4BAAsB,WAAY;AAChC,eAAO,MAAM,QAAQ,SAASA,qBAAoB,WAAW,UAAU;AAAA,MACzE,CAAC;AAAA,IACH;AACA,IAAAA,qBAAoB,UAAU,aAAa,WAAY;AACrD,UAAI,QAAQ;AACZ,4BAAsB,WAAY;AAChC,eAAO,MAAM,QAAQ,YAAYA,qBAAoB,WAAW,UAAU;AAAA,MAC5E,CAAC;AAAA,IACH;AAOA,IAAAA,qBAAoB,UAAU,sBAAsB,WAAY;AAC9D,aAAO,KAAK,QAAQ,uBAAuB;AAAA,IAC7C;AACA,IAAAA,qBAAoB,UAAU,yBAAyB,WAAY;AACjE,aAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,sBAAsB;AAAA,QACtB,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,uBAAuB;AAAA,QACvB,sBAAsB;AAAA,MACxB;AAAA,IACF;AAIA,IAAAA,qBAAoB,UAAU,uBAAuB,SAAU,qBAAqB;AAClF,UAAI,KAAK;AACT,UAAI,qBAAqB;AACvB,YAAI;AACF,mBAAS,2BAA2B,SAAS,sBAAsB,GAAG,6BAA6B,yBAAyB,KAAK,GAAG,CAAC,2BAA2B,MAAM,6BAA6B,yBAAyB,KAAK,GAAG;AAClO,gBAAI,UAAU,2BAA2B;AACzC,iBAAK,QAAQ,2BAA2B,SAAS,KAAK,eAAe;AAAA,UACvE;AAAA,QACF,SAAS,OAAO;AACd,gBAAM;AAAA,YACJ,OAAO;AAAA,UACT;AAAA,QACF,UAAE;AACA,cAAI;AACF,gBAAI,8BAA8B,CAAC,2BAA2B,SAAS,KAAK,yBAAyB,QAAS,IAAG,KAAK,wBAAwB;AAAA,UAChJ,UAAE;AACA,gBAAI,IAAK,OAAM,IAAI;AAAA,UACrB;AAAA,QACF;AACA,YAAI,KAAK,QAAQ,YAAY,GAAG;AAC9B,eAAK,QAAQ,sBAAsB,KAAK,aAAa;AAAA,QACvD;AAAA,MACF;AACA,WAAK,QAAQ,2BAA2B,SAAS,KAAK,YAAY;AAClE,WAAK,QAAQ,2BAA2B,QAAQ,KAAK,WAAW;AAAA,IAClE;AACA,IAAAA,qBAAoB,UAAU,+BAA+B,SAAU,KAAK;AAC1E,UAAI,KAAK;AACT,UAAI,IAAI,SAAS,WAAW;AAC1B,aAAK,QAAQ,2BAA2B,SAAS,KAAK,iBAAiB;AAAA,MACzE,OAAO;AACL,YAAI;AACF,mBAAS,qCAAqC,SAAS,gCAAgC,GAAG,uCAAuC,mCAAmC,KAAK,GAAG,CAAC,qCAAqC,MAAM,uCAAuC,mCAAmC,KAAK,GAAG;AACxS,gBAAI,UAAU,qCAAqC;AACnD,iBAAK,QAAQ,mCAAmC,SAAS,KAAK,iBAAiB;AAAA,UACjF;AAAA,QACF,SAAS,OAAO;AACd,gBAAM;AAAA,YACJ,OAAO;AAAA,UACT;AAAA,QACF,UAAE;AACA,cAAI;AACF,gBAAI,wCAAwC,CAAC,qCAAqC,SAAS,KAAK,mCAAmC,QAAS,IAAG,KAAK,kCAAkC;AAAA,UACxL,UAAE;AACA,gBAAI,IAAK,OAAM,IAAI;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,yBAAyB,WAAY;AACjE,UAAI,KAAK;AACT,UAAI;AACF,iBAAS,2BAA2B,SAAS,sBAAsB,GAAG,6BAA6B,yBAAyB,KAAK,GAAG,CAAC,2BAA2B,MAAM,6BAA6B,yBAAyB,KAAK,GAAG;AAClO,cAAI,UAAU,2BAA2B;AACzC,eAAK,QAAQ,6BAA6B,SAAS,KAAK,eAAe;AAAA,QACzE;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,8BAA8B,CAAC,2BAA2B,SAAS,KAAK,yBAAyB,QAAS,IAAG,KAAK,wBAAwB;AAAA,QAChJ,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AACA,WAAK,QAAQ,6BAA6B,SAAS,KAAK,YAAY;AACpE,WAAK,QAAQ,6BAA6B,QAAQ,KAAK,WAAW;AAClE,UAAI,KAAK,QAAQ,YAAY,GAAG;AAC9B,aAAK,QAAQ,wBAAwB,KAAK,aAAa;AAAA,MACzD;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,iCAAiC,WAAY;AACzE,UAAI,KAAK;AACT,WAAK,QAAQ,6BAA6B,SAAS,KAAK,iBAAiB;AACzE,UAAI;AACF,iBAAS,qCAAqC,SAAS,gCAAgC,GAAG,uCAAuC,mCAAmC,KAAK,GAAG,CAAC,qCAAqC,MAAM,uCAAuC,mCAAmC,KAAK,GAAG;AACxS,cAAI,UAAU,qCAAqC;AACnD,eAAK,QAAQ,qCAAqC,SAAS,KAAK,iBAAiB;AAAA,QACnF;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,wCAAwC,CAAC,qCAAqC,SAAS,KAAK,mCAAmC,QAAS,IAAG,KAAK,kCAAkC;AAAA,QACxL,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,gBAAgB,WAAY;AACxD,UAAI,QAAQ;AACZ,UAAI,gBAAgBA,qBAAoB;AACxC,UAAI,OAAO,OAAO,KAAK,aAAa;AACpC,WAAK,QAAQ,SAAU,KAAK;AAC1B,YAAI,IAAI,QAAQ,MAAM,MAAM,GAAG;AAC7B,gBAAM,QAAQ,kBAAkB,cAAc,GAAG,GAAG,IAAI;AAAA,QAC1D;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAAA,qBAAoB,UAAU,eAAe,SAAU,KAAK;AAC1D,UAAI,QAAQ;AACZ,UAAI,KAAK,QAAQ,kBAAkB,GAAG;AACpC;AAAA,MACF;AACA,UAAI,kBAAkB,KAAK;AAC3B,UAAI,gBAAgB,aAAa;AAC/B;AAAA,MACF;AAEA,UAAI,0BAA0B,KAAK;AACnC,UAAI,oBAAoB,2BAA2B,QAAQ,UAAa,wBAAwB,SAAS,IAAI;AAC7G,UAAI,mBAAmB;AACrB;AAAA,MACF;AACA,sBAAgB,cAAc;AAC9B,sBAAgB,iBAAiB,QAAQ;AACzC,sBAAgB,kBAAkB;AAClC,sBAAgB,wBAAwB,gBAAgB,iBAAiB,QAAQ,QAAQ,WAAc,IAAI,SAAS,eAAe,IAAI,SAAS,gBAAgB,IAAI,SAAS;AAC7K,UAAI,oBAAoB,QAAQ,UAAa,iBAAiB,SAAS,KAAK,iBAAiB,KAAK,SAAU,QAAQ;AAClH,eAAO,MAAM,QAAQ,oBAAoB,MAAM;AAAA,MACjD,CAAC;AACD,UAAI,mBAAmB;AAErB,aAAK,qBAAqB;AAC1B;AAAA,MACF;AACA,UAAI,QAAQ,QAAW;AACrB,yBAAiB,KAAK,IAAI,MAAM;AAChC,aAAK,6BAA6B,GAAG;AAAA,MACvC;AACA,sBAAgB,uBAAuB,KAAK,uBAAuB,GAAG;AACtE,UAAI,gBAAgB,sBAAsB;AACxC,aAAK,kBAAkB;AAAA,MACzB;AACA,4BAAsB,WAAY;AAEhC,2BAAmB,CAAC;AACpB,YAAI,CAAC,gBAAgB,wBAAwB,QAAQ,WAAc,IAAI,QAAQ,OAAO,IAAI,YAAY,KAAK;AAOzG,0BAAgB,uBAAuB,MAAM,uBAAuB,GAAG;AACvE,cAAI,gBAAgB,sBAAsB;AACxC,kBAAM,kBAAkB;AAAA,UAC1B;AAAA,QACF;AACA,YAAI,CAAC,gBAAgB,sBAAsB;AAEzC,gBAAM,kBAAkB,MAAM,uBAAuB;AAAA,QACvD;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAAA,qBAAoB,UAAU,yBAAyB,SAAU,KAAK;AACpE,aAAO,QAAQ,UAAa,IAAI,SAAS,YAAY,KAAK,QAAQ,gBAAgB,IAAI;AAAA,IACxF;AACA,IAAAA,qBAAoB,UAAU,oBAAoB,WAAY;AAC5D,UAAI,QAAQ;AACZ,UAAI,KAAKA,qBAAoB,SAC3B,yBAAyB,GAAG,wBAC5B,uBAAuB,GAAG;AAC5B,UAAI,KAAKA,qBAAoB,YAC3B,kBAAkB,GAAG,iBACrB,gBAAgB,GAAG;AACrB,UAAI,0BAA0BA,qBAAoB,QAAQ;AAC1D,WAAK,eAAe;AACpB,UAAI,iBAAiB;AACrB,UAAI,eAAe;AACnB,UAAI,CAAC,KAAK,QAAQ,YAAY,GAAG;AAC/B,YAAI,KAAK,KAAK,4BAA4B,GACxC,aAAa,GAAG,YAChB,WAAW,GAAG;AAChB,yBAAiB,WAAW,IAAI,SAAS,WAAW,IAAI;AACxD,uBAAe,SAAS,IAAI,SAAS,SAAS,IAAI;AAAA,MACpD;AACA,WAAK,QAAQ,kBAAkB,wBAAwB,cAAc;AACrE,WAAK,QAAQ,kBAAkB,sBAAsB,YAAY;AAEjE,mBAAa,KAAK,eAAe;AACjC,mBAAa,KAAK,0BAA0B;AAC5C,WAAK,2BAA2B;AAChC,WAAK,QAAQ,YAAY,eAAe;AAExC,WAAK,QAAQ,oBAAoB;AACjC,WAAK,QAAQ,SAAS,aAAa;AACnC,WAAK,kBAAkB,WAAW,WAAY;AAC5C,cAAM,wBAAwB;AAAA,MAChC,GAAG,uBAAuB;AAAA,IAC5B;AACA,IAAAA,qBAAoB,UAAU,8BAA8B,WAAY;AACtE,UAAI,KAAK,KAAK,iBACZ,kBAAkB,GAAG,iBACrB,wBAAwB,GAAG;AAC7B,UAAI;AACJ,UAAI,uBAAuB;AACzB,qBAAa,yBAAyB,iBAAiB,KAAK,QAAQ,oBAAoB,GAAG,KAAK,QAAQ,oBAAoB,CAAC;AAAA,MAC/H,OAAO;AACL,qBAAa;AAAA,UACX,GAAG,KAAK,MAAM,QAAQ;AAAA,UACtB,GAAG,KAAK,MAAM,SAAS;AAAA,QACzB;AAAA,MACF;AAEA,mBAAa;AAAA,QACX,GAAG,WAAW,IAAI,KAAK,cAAc;AAAA,QACrC,GAAG,WAAW,IAAI,KAAK,cAAc;AAAA,MACvC;AACA,UAAI,WAAW;AAAA,QACb,GAAG,KAAK,MAAM,QAAQ,IAAI,KAAK,cAAc;AAAA,QAC7C,GAAG,KAAK,MAAM,SAAS,IAAI,KAAK,cAAc;AAAA,MAChD;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,gCAAgC,WAAY;AACxE,UAAI,QAAQ;AAGZ,UAAI,kBAAkBA,qBAAoB,WAAW;AACrD,UAAI,KAAK,KAAK,iBACZ,uBAAuB,GAAG,sBAC1B,cAAc,GAAG;AACnB,UAAI,qBAAqB,wBAAwB,CAAC;AAClD,UAAI,sBAAsB,KAAK,6BAA6B;AAC1D,aAAK,2BAA2B;AAChC,aAAK,QAAQ,SAAS,eAAe;AACrC,aAAK,6BAA6B,WAAW,WAAY;AACvD,gBAAM,QAAQ,YAAY,eAAe;AAAA,QAC3C,GAAG,QAAQ,kBAAkB;AAAA,MAC/B;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,6BAA6B,WAAY;AACrE,UAAI,gBAAgBA,qBAAoB,WAAW;AACnD,WAAK,QAAQ,YAAY,aAAa;AACtC,WAAK,8BAA8B;AACnC,WAAK,QAAQ,oBAAoB;AAAA,IACnC;AACA,IAAAA,qBAAoB,UAAU,uBAAuB,WAAY;AAC/D,UAAI,QAAQ;AACZ,WAAK,0BAA0B,KAAK,gBAAgB;AACpD,WAAK,kBAAkB,KAAK,uBAAuB;AAGnD,iBAAW,WAAY;AACrB,eAAO,MAAM,0BAA0B;AAAA,MACzC,GAAGA,qBAAoB,QAAQ,YAAY;AAAA,IAC7C;AACA,IAAAA,qBAAoB,UAAU,iBAAiB,WAAY;AACzD,UAAI,QAAQ;AACZ,UAAI,kBAAkB,KAAK;AAE3B,UAAI,CAAC,gBAAgB,aAAa;AAChC;AAAA,MACF;AACA,UAAI,QAAQ,SAAS,CAAC,GAAG,eAAe;AACxC,UAAI,gBAAgB,gBAAgB;AAClC,8BAAsB,WAAY;AAChC,gBAAM,oBAAoB,KAAK;AAAA,QACjC,CAAC;AACD,aAAK,qBAAqB;AAAA,MAC5B,OAAO;AACL,aAAK,+BAA+B;AACpC,8BAAsB,WAAY;AAChC,gBAAM,gBAAgB,uBAAuB;AAC7C,gBAAM,oBAAoB,KAAK;AAC/B,gBAAM,qBAAqB;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,sBAAsB,SAAU,IAAI;AAChE,UAAI,wBAAwB,GAAG,uBAC7B,uBAAuB,GAAG;AAC5B,UAAI,yBAAyB,sBAAsB;AACjD,aAAK,8BAA8B;AAAA,MACrC;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,iBAAiB,WAAY;AACzD,UAAI,QAAQ;AACZ,WAAK,QAAQ,KAAK,QAAQ,oBAAoB;AAC9C,UAAI,SAAS,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK;AAOzD,UAAI,mBAAmB,WAAY;AACjC,YAAI,aAAa,KAAK,KAAK,KAAK,IAAI,MAAM,MAAM,OAAO,CAAC,IAAI,KAAK,IAAI,MAAM,MAAM,QAAQ,CAAC,CAAC;AAC3F,eAAO,aAAaA,qBAAoB,QAAQ;AAAA,MAClD;AACA,WAAK,YAAY,KAAK,QAAQ,YAAY,IAAI,SAAS,iBAAiB;AAExE,UAAI,cAAc,KAAK,MAAM,SAASA,qBAAoB,QAAQ,oBAAoB;AAEtF,UAAI,KAAK,QAAQ,YAAY,KAAK,cAAc,MAAM,GAAG;AACvD,aAAK,cAAc,cAAc;AAAA,MACnC,OAAO;AACL,aAAK,cAAc;AAAA,MACrB;AACA,WAAK,UAAU,KAAK,KAAK,YAAY,KAAK;AAC1C,WAAK,oBAAoB;AAAA,IAC3B;AACA,IAAAA,qBAAoB,UAAU,sBAAsB,WAAY;AAC9D,UAAI,KAAKA,qBAAoB,SAC3B,cAAc,GAAG,aACjB,WAAW,GAAG,UACd,UAAU,GAAG,SACb,eAAe,GAAG;AACpB,WAAK,QAAQ,kBAAkB,aAAa,KAAK,cAAc,IAAI;AACnE,WAAK,QAAQ,kBAAkB,cAAc,KAAK,OAAO;AACzD,UAAI,KAAK,QAAQ,YAAY,GAAG;AAC9B,aAAK,kBAAkB;AAAA,UACrB,MAAM,KAAK,MAAM,KAAK,MAAM,QAAQ,IAAI,KAAK,cAAc,CAAC;AAAA,UAC5D,KAAK,KAAK,MAAM,KAAK,MAAM,SAAS,IAAI,KAAK,cAAc,CAAC;AAAA,QAC9D;AACA,aAAK,QAAQ,kBAAkB,UAAU,KAAK,gBAAgB,OAAO,IAAI;AACzE,aAAK,QAAQ,kBAAkB,SAAS,KAAK,gBAAgB,MAAM,IAAI;AAAA,MACzE;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAAyB,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,aAAY;AACnB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,WAAW;AACjB,aAAO;AAAA,IACT;AACA,IAAAA,WAAU,WAAW,SAAU,MAAM,MAAM;AACzC,UAAI,SAAS,QAAQ;AACnB,eAAO;AAAA,UACL,aAAa;AAAA,QACf;AAAA,MACF;AACA,UAAI,SAAS,IAAIA,WAAU,IAAI;AAE/B,UAAI,KAAK,gBAAgB,QAAW;AAClC,eAAO,YAAY,KAAK;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AACA,IAAAA,WAAU,gBAAgB,SAAU,UAAU;AAC5C,aAAO;AAAA,QACL,UAAU,SAAU,WAAW;AAC7B,iBAAO,SAAS,KAAK,UAAU,IAAI,SAAS;AAAA,QAC9C;AAAA,QACA,wBAAwB,WAAY;AAClC,iBAAO,qBAAqB,MAAM;AAAA,QACpC;AAAA,QACA,qBAAqB,WAAY;AAC/B,iBAAO,SAAS,KAAK,sBAAsB;AAAA,QAC7C;AAAA,QACA,qBAAqB,SAAU,QAAQ;AACrC,iBAAO,SAAS,KAAK,SAAS,MAAM;AAAA,QACtC;AAAA,QACA,sCAAsC,SAAU,SAAS,SAAS;AAChE,iBAAO,SAAS,gBAAgB,oBAAoB,SAAS,SAAS,aAAa,CAAC;AAAA,QACtF;AAAA,QACA,8BAA8B,SAAU,SAAS,SAAS;AACxD,iBAAO,SAAS,KAAK,oBAAoB,SAAS,SAAS,aAAa,CAAC;AAAA,QAC3E;AAAA,QACA,yBAAyB,SAAU,SAAS;AAC1C,iBAAO,OAAO,oBAAoB,UAAU,OAAO;AAAA,QACrD;AAAA,QACA,qBAAqB,WAAY;AAC/B,iBAAO;AAAA,YACL,GAAG,OAAO;AAAA,YACV,GAAG,OAAO;AAAA,UACZ;AAAA,QACF;AAAA,QACA,iBAAiB,WAAY;AAC3B,iBAAO,QAAQ,SAAS,MAAM,SAAS;AAAA,QACzC;AAAA,QACA,mBAAmB,WAAY;AAC7B,iBAAO,QAAQ,SAAS,QAAQ;AAAA,QAClC;AAAA,QACA,aAAa,WAAY;AACvB,iBAAO,QAAQ,SAAS,SAAS;AAAA,QACnC;AAAA,QACA,oCAAoC,SAAU,SAAS,SAAS;AAC9D,iBAAO,SAAS,gBAAgB,iBAAiB,SAAS,SAAS,aAAa,CAAC;AAAA,QACnF;AAAA,QACA,4BAA4B,SAAU,SAAS,SAAS;AACtD,iBAAO,SAAS,KAAK,iBAAiB,SAAS,SAAS,aAAa,CAAC;AAAA,QACxE;AAAA,QACA,uBAAuB,SAAU,SAAS;AACxC,iBAAO,OAAO,iBAAiB,UAAU,OAAO;AAAA,QAClD;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,iBAAO,SAAS,KAAK,UAAU,OAAO,SAAS;AAAA,QACjD;AAAA,QACA,mBAAmB,SAAU,SAAS,OAAO;AAC3C,iBAAO,SAAS,KAAK,MAAM,YAAY,SAAS,KAAK;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AACA,WAAO,eAAeA,WAAU,WAAW,aAAa;AAAA,MACtD,KAAK,WAAY;AACf,eAAO,QAAQ,KAAK,WAAW;AAAA,MACjC;AAAA,MACA,KAAK,SAAU,WAAW;AACxB,aAAK,cAAc,QAAQ,SAAS;AACpC,aAAK,aAAa;AAAA,MACpB;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,WAAU,UAAU,WAAW,WAAY;AACzC,WAAK,WAAW,SAAS;AAAA,IAC3B;AACA,IAAAA,WAAU,UAAU,aAAa,WAAY;AAC3C,WAAK,WAAW,WAAW;AAAA,IAC7B;AACA,IAAAA,WAAU,UAAU,SAAS,WAAY;AACvC,WAAK,WAAW,OAAO;AAAA,IACzB;AACA,IAAAA,WAAU,UAAU,uBAAuB,WAAY;AACrD,aAAO,IAAI,oBAAoBA,WAAU,cAAc,IAAI,CAAC;AAAA,IAC9D;AACA,IAAAA,WAAU,UAAU,qBAAqB,WAAY;AACnD,UAAI,OAAO,KAAK;AAChB,WAAK,cAAc,0BAA0B,KAAK;AAAA,IACpD;AAOA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC7C,WAAK,WAAW,aAAa,QAAQ,KAAK,WAAW,CAAC;AAAA,IACxD;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;", "names": ["MDCRippleFoundation", "MDCRipple"]}