﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Auth;
using BCI.DocupediaBot.Application.Services.Auth;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.UIService.Controllers
{
  [Route("api/auth")]
  [ApiController]
  public class AuthController : ControllerBase
  {
    private readonly ILogger<AuthController> _logger;
    private readonly IAuthService _authService;


    public AuthController(ILogger<AuthController> logger, IAuthService authService)
    {
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _authService = authService;
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginDTO request)
    {
      try
      {
        _logger.LogInformation("Attempting login for user: {Username}", request.UserId);
        var result = await _authService.LoginAsync(request);
        if (result != null)
        {
          _logger.LogInformation("User {Username} logged in successfully.", request.UserId);
          return Ok(result);
        }

        _logger.LogWarning("Invalid credentials for user: {Username}", request.UserId);
        return StatusCode(500, ApiResponse<string>.Error("Login failed"));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Login error for user: {Username}", request.UserId);
        return StatusCode(500, ApiResponse<string>.Error("Login failed"));
      }
    }

    [HttpPost("refresh")]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequestDTO request)
    {
      try
      {
        _logger.LogInformation("Attempting token refresh");
        var result = await _authService.RefreshTokenAsync(request);
        if (result != null)
        {
          _logger.LogInformation("Token refreshed successfully");
          return Ok(result);
        }

        _logger.LogWarning("Invalid or expired refresh token");
        return Unauthorized(ApiResponse<string>.Error("Invalid or expired refresh token"));
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Token refresh error");
        return StatusCode(500, ApiResponse<string>.Error("Token refresh failed"));
      }
    }
  }
}