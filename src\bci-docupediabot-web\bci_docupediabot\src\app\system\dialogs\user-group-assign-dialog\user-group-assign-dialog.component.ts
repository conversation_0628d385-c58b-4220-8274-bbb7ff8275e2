import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SysUserResponseDTO, SysGroupResponseDTO } from '@shared/models/system.model';
import { SysGroupService } from '@shared/services/system/sys-group.service';
import { SysUserService } from '@shared/services/system/sys-user.service';
import { DataRefreshService } from '@shared/services/data-refresh.service';
import { forkJoin, Observable } from 'rxjs';

interface DialogData {
  users: SysUserResponseDTO[];
  mode: 'single' | 'batch';
}

@Component({
  selector: 'app-user-group-assign-dialog',
  templateUrl: './user-group-assign-dialog.component.html',
  styleUrls: ['./user-group-assign-dialog.component.scss']
})
export class UserGroupAssignDialogComponent implements OnInit {
  users: SysUserResponseDTO[];
  mode: 'single' | 'batch';
  allGroups: SysGroupResponseDTO[] = [];
  assignedGroupIds: Set<string> = new Set();
  originalAssignedGroupIds: Set<string> = new Set();
  loading = false;
  saving = false;

  constructor(
    private sysGroupService: SysGroupService,
    private sysUserService: SysUserService,
    private dataRefreshService: DataRefreshService,
    private dialogRef: MatDialogRef<UserGroupAssignDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.users = data.users;
    this.mode = data.mode;
  }

  ngOnInit(): void {
    this.loadGroups();
    this.initializeAssignedGroups();
  }

  private loadGroups(): void {
    this.loading = true;
    this.sysGroupService.getGroups().subscribe({
      next: (groups) => {
        this.allGroups = groups;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading groups:', error);
        this.loading = false;
      }
    });
  }

  private initializeAssignedGroups(): void {
    if (this.mode === 'single') {

      this.users[0].groups.forEach(group => {
        this.assignedGroupIds.add(group.id);
        this.originalAssignedGroupIds.add(group.id);
      });
    } else {

      if (this.users.length > 0) {
        const firstUserGroups = new Set(this.users[0].groups.map(g => g.id));


        for (const groupId of firstUserGroups) {
          const allUsersHaveGroup = this.users.every(user =>
            user.groups.some(g => g.id === groupId)
          );
          if (allUsersHaveGroup) {
            this.assignedGroupIds.add(groupId);
            this.originalAssignedGroupIds.add(groupId);
          }
        }
      }
    }
  }

  isGroupAssigned(groupId: string): boolean {
    return this.assignedGroupIds.has(groupId);
  }

  toggleGroupAssignment(group: SysGroupResponseDTO, isAssigned: boolean): void {
    if (isAssigned) {
      this.assignedGroupIds.add(group.id);
    } else {
      this.assignedGroupIds.delete(group.id);
    }
  }

  save(): void {
    this.saving = true;

    const observables: any[] = [];


    const groupsToAssign = Array.from(this.assignedGroupIds).filter(
      groupId => !this.originalAssignedGroupIds.has(groupId)
    );


    const groupsToRemove = Array.from(this.originalAssignedGroupIds).filter(
      groupId => !this.assignedGroupIds.has(groupId)
    );


    this.users.forEach(user => {
      groupsToAssign.forEach(groupId => {
        observables.push(
          this.sysUserService.assignUserToGroup(user.id, groupId)
        );
      });

      groupsToRemove.forEach(groupId => {
        observables.push(
          this.sysUserService.removeUserFromGroup(user.id, groupId)
        );
      });
    });

    if (observables.length === 0) {

      this.saving = false;
      this.close();
      return;
    }


    const requests: { [key: string]: Observable<any> } = {};
    observables.forEach((obs, index) => {
      requests[`request_${index}`] = obs;
    });

    forkJoin(requests).subscribe({
      next: () => {
        this.saving = false;

        this.dataRefreshService.refreshAllData();
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Error updating group assignments:', error);
        this.saving = false;
      }
    });
  }

  close(): void {
    this.dialogRef.close(false);
  }
}
