import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-brochure',
  templateUrl: './brochure.component.html',
  styleUrls: ['./brochure.component.scss']
})
export class BrochureComponent implements OnInit {


  platformStats = {
    users: '1000 +',
    space: '2000 +',
    queries: '10000 +',
    accuracy: '95% +'
  };


  coreFeatures = [
    {
      icon: '🔒',
      title: '企业级安全',
      description: 'SSO单点登录 + JWT令牌 + 多租户隔离',
      techDetails: '基于角色的访问控制(RBAC)，支持AD域集成，数据库级租户隔离',
      color: 'primary'
    },
    {
      icon: '🧠',
      title: '智能检索',
      description: 'PointId差异更新 + 语义理解',
      techDetails: 'Qdrant向量数据库，支持增量/批量更新，避免全量重建，检索精度95%+',
      color: 'accent'
    },
    {
      icon: '🔗',
      title: '平台集成',
      description: 'RAGFlow + n8n + Docupedia + Community',
      techDetails: '开放API架构，支持多模态，Webhook，工作流，可扩展第三方工具集成',
      color: 'warn'
    },
    {
      icon: '⚡',
      title: '高性能',
      description: 'PostgreSQL + Qdrant + 智能缓存',
      techDetails: '分布式架构，Redis缓存，支持100+并发',
      color: 'primary'
    },
    {
      icon: '🌐',
      title: '多语言',
      description: '20多种常见外语语义检索',
      techDetails: '多语言向量模型，跨语言语义理解，支持混合语言查询',
      color: 'accent'
    },
    {
      icon: '☁️',
      title: '混合AI',
      description: 'Azure OpenAI + Ollama本地',
      techDetails: '云端+本地混合部署，数据安全可控，成本优化，离线可用',
      color: 'warn'
    },
    {
      icon: '🏢',
      title: 'SAAS架构',
      description: '多租户 + DDD + 微服务',
      techDetails: 'Domain-Driven Design，清晰业务边界，高内聚低耦合',
      color: 'primary'
    },
    {
      icon: '📊',
      title: '智能洞察',
      description: '内容分析 + 重复检测 + 知识图谱',
      techDetails: 'NLP内容分析，自动去重，知识关联发现，使用趋势分析',
      color: 'accent'
    }
  ];


  techStack = [
    {
      layer: '前端层',
      description: '现代化Web界面，响应式设计，PWA支持',
      technologies: ['Angular 17', 'Material Design', 'TypeScript', 'PWA离线支持'],
      color: '#e3f2fd'
    },
    {
      layer: 'API层',
      description: 'RESTful API设计，JWT安全认证，完整API文档',
      technologies: ['.NET 8.0', 'ASP.NET Core', 'JWT认证', 'Swagger文档'],
      color: '#f3e5f5'
    },
    {
      layer: '业务层',
      description: '领域驱动设计，清晰业务边界，高内聚低耦合',
      technologies: ['DDD架构', '领域服务', '应用服务', '依赖注入'],
      color: '#e8f5e8'
    },
    {
      layer: '数据层',
      description: '企业级数据库，多租户架构，高性能查询优化',
      technologies: ['PostgreSQL', 'Entity Framework', '多租户隔离', '索引优化'],
      color: '#fff3e0'
    },
    {
      layer: '向量层',
      description: '高性能向量检索，增量更新机制，语义搜索',
      technologies: ['Qdrant向量库', 'gRPC通信', 'PointId差异更新', '元数据索引'],
      color: '#fce4ec'
    },
    {
      layer: 'AI层',
      description: '混合AI架构，云端+本地部署，多模型支持',
      technologies: ['Azure LLM', 'Ollama本地', '多模型切换'],
      color: '#e0f2f1'
    }
  ];


  integrations = [
    {
      name: 'Docupedia V1/V2',
      description: '企业知识管理系统',
      features: ['深度API集成', '权限同步', 'URL智能解析', '实时更新'],
      icon: '📚',
      status: '已集成',
      color: '#4caf50'
    },
    {
      name: 'Community / Wikis',
      description: '博世社区知识库',
      features: ['深度API集成', '权限同步', 'URL智能解析', '实时更新'],
      icon: '📚',
      status: '规划中',
      color: '#ff9800'
    },
    {
      name: 'RAGFlow',
      description: '多模态文档处理平台',
      features: ['PDF智能解析', '图像内容提取', '视频转录分析', '表格数据结构化'],
      icon: '📄',
      status: '规划中',
      color: '#ff9800'
    },
    {
      name: 'n8n',
      description: '自动化工作流引擎',
      features: ['网页内容爬取', '定时任务调度', '智能通知推送', 'API接口编排'],
      icon: '🔄',
      status: '规划中',
      color: '#ff9800'
    }
  ];



  constructor(private router: Router) { }

  ngOnInit(): void {

  }

  switchToEnglish(): void {
    this.router.navigate(['/brochure-en']);
  }

}
