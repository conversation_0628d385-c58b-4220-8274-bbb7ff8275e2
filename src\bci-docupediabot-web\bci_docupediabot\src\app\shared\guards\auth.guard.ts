import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '@shared/services/auth.service';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    return this.checkAuth(state.url);
  }

  private checkAuth(url: string): Observable<boolean> | boolean {
    // 首先检查是否有Token
    if (this.authService.isLoggedIn()) {
      return true;
    }

    // 如果没有有效Token，尝试使用Refresh Token
    const refreshToken = this.authService.getRefreshToken();
    if (refreshToken) {
      console.log('Token expired, attempting automatic refresh...');
      return this.authService.refreshToken().pipe(
        map((success: boolean) => {
          if (success) {
            console.log('Token refresh successful - user can continue');
            return true;
          } else {
            console.warn('Token refresh failed - session expired, redirecting to login');
            // 可以在这里添加用户友好的提示
            this.router.navigate(['']);
            return false;
          }
        }),
        catchError((error) => {
          console.error('Token refresh error - network or server issue:', error);
          this.router.navigate(['']);
          return of(false);
        })
      );
    }

    // 没有Refresh Token，直接跳转到登录页
    console.log('No valid token or refresh token, redirecting to login');
    this.router.navigate(['']);
    return false;
  }
}
