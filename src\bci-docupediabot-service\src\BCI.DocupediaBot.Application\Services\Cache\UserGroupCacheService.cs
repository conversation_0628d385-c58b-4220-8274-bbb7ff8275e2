using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Cache
{



  public class UserGroupCacheService : IUserGroupCacheService
  {
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<UserGroupCacheService> _logger;


    private const string USER_GROUPS_PREFIX = "user_groups:";
    private const string GROUP_USERS_PREFIX = "group_users:";
    private const string USER_IN_GROUP_PREFIX = "user_in_group:";


    private static readonly TimeSpan DefaultExpiration = TimeSpan.FromMinutes(30);

    public UserGroupCacheService(IMemoryCache memoryCache, ILogger<UserGroupCacheService> logger)
    {
      _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public Task<List<Guid>?> GetUserGroupsAsync(Guid userId)
    {
      var key = $"{USER_GROUPS_PREFIX}{userId}";
      var result = _memoryCache.Get<List<Guid>>(key);

      _logger.LogDebug("Cache {Status} for user groups: {UserId}", result != null ? "HIT" : "MISS", userId);
      return Task.FromResult(result);
    }

    public Task SetUserGroupsAsync(Guid userId, List<Guid> groupIds, TimeSpan? expiration = null)
    {
      var key = $"{USER_GROUPS_PREFIX}{userId}";
      var options = new MemoryCacheEntryOptions
      {
        AbsoluteExpirationRelativeToNow = expiration ?? DefaultExpiration,
        Priority = CacheItemPriority.Normal
      };

      _memoryCache.Set(key, groupIds, options);
      _logger.LogDebug("Cached user groups for user: {UserId}, Groups: {GroupCount}", userId, groupIds.Count);

      return Task.CompletedTask;
    }

    public Task<List<Guid>?> GetGroupUsersAsync(Guid groupId)
    {
      var key = $"{GROUP_USERS_PREFIX}{groupId}";
      var result = _memoryCache.Get<List<Guid>>(key);

      _logger.LogDebug("Cache {Status} for group users: {GroupId}", result != null ? "HIT" : "MISS", groupId);
      return Task.FromResult(result);
    }

    public Task SetGroupUsersAsync(Guid groupId, List<Guid> userIds, TimeSpan? expiration = null)
    {
      var key = $"{GROUP_USERS_PREFIX}{groupId}";
      var options = new MemoryCacheEntryOptions
      {
        AbsoluteExpirationRelativeToNow = expiration ?? DefaultExpiration,
        Priority = CacheItemPriority.Normal
      };

      _memoryCache.Set(key, userIds, options);
      _logger.LogDebug("Cached group users for group: {GroupId}, Users: {UserCount}", groupId, userIds.Count);

      return Task.CompletedTask;
    }

    public async Task<Dictionary<Guid, List<Guid>>> GetUserGroupsBatchAsync(IEnumerable<Guid> userIds)
    {
      var result = new Dictionary<Guid, List<Guid>>();
      var userIdList = userIds.ToList();

      foreach (var userId in userIdList)
      {
        var groups = await GetUserGroupsAsync(userId);
        if (groups != null)
        {
          result[userId] = groups;
        }
      }

      _logger.LogDebug("Batch cache lookup for {RequestedCount} users, found {FoundCount} in cache",
        userIdList.Count, result.Count);

      return result;
    }

    public async Task SetUserGroupsBatchAsync(Dictionary<Guid, List<Guid>> userGroups, TimeSpan? expiration = null)
    {
      foreach (var kvp in userGroups)
      {
        await SetUserGroupsAsync(kvp.Key, kvp.Value, expiration);
      }

      _logger.LogDebug("Batch cached user groups for {Count} users", userGroups.Count);
    }

    public async Task<Dictionary<Guid, List<Guid>>> GetGroupUsersBatchAsync(IEnumerable<Guid> groupIds)
    {
      var result = new Dictionary<Guid, List<Guid>>();
      var groupIdList = groupIds.ToList();

      foreach (var groupId in groupIdList)
      {
        var users = await GetGroupUsersAsync(groupId);
        if (users != null)
        {
          result[groupId] = users;
        }
      }

      _logger.LogDebug("Batch cache lookup for {RequestedCount} groups, found {FoundCount} in cache",
        groupIdList.Count, result.Count);

      return result;
    }

    public async Task SetGroupUsersBatchAsync(Dictionary<Guid, List<Guid>> groupUsers, TimeSpan? expiration = null)
    {
      foreach (var kvp in groupUsers)
      {
        await SetGroupUsersAsync(kvp.Key, kvp.Value, expiration);
      }

      _logger.LogDebug("Batch cached group users for {Count} groups", groupUsers.Count);
    }

    public Task<bool?> IsUserInGroupAsync(Guid userId, Guid groupId)
    {
      var key = $"{USER_IN_GROUP_PREFIX}{userId}:{groupId}";
      var result = _memoryCache.Get<bool?>(key);

      _logger.LogDebug("Cache {Status} for user in group: {UserId} in {GroupId}",
        result.HasValue ? "HIT" : "MISS", userId, groupId);

      return Task.FromResult(result);
    }

    public Task SetUserInGroupAsync(Guid userId, Guid groupId, bool isInGroup, TimeSpan? expiration = null)
    {
      var key = $"{USER_IN_GROUP_PREFIX}{userId}:{groupId}";
      var options = new MemoryCacheEntryOptions
      {
        AbsoluteExpirationRelativeToNow = expiration ?? DefaultExpiration,
        Priority = CacheItemPriority.Normal
      };

      _memoryCache.Set(key, isInGroup, options);
      _logger.LogDebug("Cached user in group status: {UserId} in {GroupId} = {Status}",
        userId, groupId, isInGroup);

      return Task.CompletedTask;
    }

    public Task ClearUserCacheAsync(Guid userId)
    {

      var userGroupsKey = $"{USER_GROUPS_PREFIX}{userId}";
      _memoryCache.Remove(userGroupsKey);




      _logger.LogDebug("Cleared cache for user: {UserId}", userId);
      return Task.CompletedTask;
    }

    public Task ClearGroupCacheAsync(Guid groupId)
    {

      var groupUsersKey = $"{GROUP_USERS_PREFIX}{groupId}";
      _memoryCache.Remove(groupUsersKey);

      _logger.LogDebug("Cleared cache for group: {GroupId}", groupId);
      return Task.CompletedTask;
    }

    public Task ClearAllCacheAsync()
    {


      _logger.LogWarning("ClearAllCacheAsync called - MemoryCache doesn't support clearing all entries");
      return Task.CompletedTask;
    }
  }
}
