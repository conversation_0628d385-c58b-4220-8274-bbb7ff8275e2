﻿using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Job
{
  public class JobInitializerService : IHostedService
  {
    private readonly IJobConfigService _jobConfigService;
    private readonly JobSchedulerService _jobSchedulerService;
    private readonly ILogger<JobInitializerService> _logger;

    public JobInitializerService(
        IJobConfigService jobConfigService,
        JobSchedulerService jobSchedulerService,
        ILogger<JobInitializerService> logger)
    {
      _jobConfigService = jobConfigService;
      _jobSchedulerService = jobSchedulerService;
      _logger = logger;
    }


    public async Task StartAsync(CancellationToken cancellationToken)
    {
      _logger.LogInformation("Initializing Quartz jobs...");

      try
      {
        var jobConfigs = await _jobConfigService.GetJobConfigsAsync();

        foreach (var jobConfig in jobConfigs)
        {
          await _jobSchedulerService.ScheduleJobAsync(jobConfig);
        }

        _logger.LogInformation($"Successfully initialized {jobConfigs.Count()} jobs");
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error initializing jobs");
        throw;
      }
    }


    public Task StopAsync(CancellationToken cancellationToken)
    {
      _logger.LogInformation("JobInitializerService is stopping...");
      return Task.CompletedTask;
    }
  }
}