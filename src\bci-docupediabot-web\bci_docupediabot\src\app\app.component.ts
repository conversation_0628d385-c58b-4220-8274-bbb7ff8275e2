/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import {
  BciImprintComponent,
  LogoutComponent,
  ModalWindowService,
  NavigationService,
  SidebarNavItem,
} from '@bci-web-core/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@env/environment';
import { Router } from '@angular/router';
import { AuthService } from '@shared/services/auth.service';
import { NavigationFilterService } from '@shared/services/navigation-filter.service';
import { UserProfileComponent } from '@shared/components/user-profile/user-profile.component';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  title = 'GenAI in BCI 2025';
  sidebarLinks: SidebarNavItem[] = [];
  sidebarFooterLinks: SidebarNavItem[] = [
    {
      id: 'brochure',
      cb: () => this.onBrochure(),
      title: 'Brochure',
      icon: 'Bosch-Ic-information',
    },
    {
      title: 'Logout',
      overlay: {
        component: LogoutComponent,
        config: {
          title: 'Do you want to logout?',
          onLogout: () => this.logout(),
        },
      },
      icon: 'Bosch-Ic-user',
    },
    {
      id: 'Profile',
      cb: () => this.onProfile(),
      title: 'Profile',
      icon: 'Bosch-Ic-user',
    },
    {
      id: 'imprint',
      cb: () => this.onAbout(),
      title: 'Imprint',
      icon: 'Bosch-Ic-information',
    },
  ];
  environment = environment;
  isLoginPage = false;
  constructor(
    private titleService: Title,
    private navigationService: NavigationService,
    private modalWindowService: ModalWindowService,
    private router: Router,
    private authService: AuthService,
    private navigationFilterService: NavigationFilterService
  ) {}

  ngOnInit(): void {
    this.titleService.setTitle(this.title);

    this.authService.isAuthenticated$.subscribe((isAuthenticated) => {
      if (isAuthenticated) {
        setTimeout(() => {
          this.loadSidebarLinks();
        }, 100);
      } else {
        this.sidebarLinks = [];
      }
    });

    this.router.events.subscribe(() => {
      this.isLoginPage = this.router.url === '/';
    });
  }

  private loadSidebarLinks(): void {
    this.getSidebarLinks().subscribe(
      (sidebarLinks) => {
        this.sidebarLinks = sidebarLinks;
        if (this.sidebarLinks.length > 0) {
          setTimeout(() => {
            const firstItemWithChildren = sidebarLinks.find((x) => x.title === 'Knowledge Agent');

            if (firstItemWithChildren) {
              firstItemWithChildren.expanded = true;
              if (firstItemWithChildren.cb) {
                firstItemWithChildren.cb();
              }
            }
          }, 100);
        }
      }
    );
  }

  getSidebarLinks(): Observable<SidebarNavItem[]> {
    return this.navigationService.getNavigationItems().pipe(
      map(items => this.navigationFilterService.filterNavigationItems(items))
    );
  }

  logout(): void {
    this.authService.logout();
  }

  onAbout(): void {
    this.modalWindowService.openDialogWithComponent(BciImprintComponent);
  }

  onProfile(): void {
    this.modalWindowService.openDialogWithComponent(UserProfileComponent);
  }

  onBrochure(): void {
    this.router.navigate(['/brochure-en']);
  }
}
