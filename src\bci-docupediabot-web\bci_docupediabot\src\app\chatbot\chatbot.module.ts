import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ChatbotRoutingModule } from './chatbot-routing.module';
import { DocupediaBotComponent } from './docupedia/components/docupedia-bot/docupedia-bot.component';
import { DocupediaTabsComponent } from './docupedia/components/docupedia-tabs/docupedia-tabs.component';
import { DocupediaImportComponent } from './docupedia/components/docupedia-import/docupedia-import.component';
import { SharedModule } from '@shared/shared.module';
import { DocupediaImportAddcollectionDialogComponent } from './docupedia/dialogs/docupedia-import-add-collection-dialog/docupedia-import-add-collection-dialog.component';
import { DocupediaImportAddUrlDialogComponent } from './docupedia/dialogs/docupedia-import-add-url-dialog/docupedia-import-add-url-dialog.component';
import { TokenSetupGuideDialogComponent } from './docupedia/dialogs/token-setup-guide-dialog/token-setup-guide-dialog.component';
import { DeepdocBotComponent } from './deepdoc/components/deepdoc-bot/deepdoc-bot.component';
import { DeepdocTabsComponent } from './deepdoc/components/deepdoc-tabs/deepdoc-tabs.component';
import { DeepdocImportComponent } from './deepdoc/components/deepdoc-import/deepdoc-import.component';


@NgModule({
  declarations: [
    DocupediaBotComponent,
    DocupediaTabsComponent,
    DocupediaImportComponent,
    DocupediaImportAddcollectionDialogComponent,
    DocupediaImportAddUrlDialogComponent,
    TokenSetupGuideDialogComponent,
    DeepdocBotComponent,
    DeepdocTabsComponent,
    DeepdocImportComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    ChatbotRoutingModule
  ],
  providers: [
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ChatbotModule { }
