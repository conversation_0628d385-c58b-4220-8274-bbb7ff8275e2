import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Collection } from '@shared/models/docupedia.model';
import { EmbeddingModel, UpdateType } from '@shared/enums';

@Component({
  selector: 'app-docupedia-import-add-collection-dialog',
  templateUrl: './docupedia-import-add-collection-dialog.component.html',
  styleUrls: ['./docupedia-import-add-collection-dialog.component.scss'],
})
export class DocupediaImportAddcollectionDialogComponent implements OnInit {
  collectionForm!: FormGroup;
  EmbeddingModel = EmbeddingModel;
  UpdateType = UpdateType;

  embeddingModels = Object.keys(EmbeddingModel)
    .filter(key => isNaN(+key))
    .map(key => ({
      value: EmbeddingModel[key as keyof typeof EmbeddingModel],
      label: key,
      tooltip: key === 'Azure' ? 'This is OpenAI Embedding Model' : 'This is MIT ollama deploied model'
    }));

  updateTypes = Object.keys(UpdateType)
    .filter(key => isNaN(+key))
    .map(key => ({
      value: UpdateType[key as keyof typeof UpdateType],
      label: key.replace(/([A-Z])/g, ' $1').trim()
    }));

  submitDisabled = true;
  updateTimeSet = false;

  constructor(
    private fb: FormBuilder,
    private _matDialogRef: MatDialogRef<DocupediaImportAddcollectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'add' | 'edit'; collection?: Collection }
  ) {}

  ngOnInit(): void {
    const initialUpdateTime = this.getInitialUpdateTime();


    this.collectionForm = this.fb.group({
      id: [this.data.collection?.id || ''],
      name: [this.data.collection?.name || '', Validators.required],
      comment: [this.data.collection?.comment || ''],
      embeddingModel: [this.findEmbeddingModelValue(this.data.collection?.embeddingModel)],
      chunkSize: [this.data.collection?.chunkSize || 50, [Validators.required, Validators.min(1)]],
      isAutomaticUpdate: [this.data.collection?.isAutomaticUpdate || false],
      updateType: [this.findUpdateTypeValue(this.data.collection?.updateType)],
      intervalNumber: [this.data.collection?.intervalNumber || 1, [Validators.min(1)]],
      updateTime: [initialUpdateTime],
      status: [this.data.collection?.status || 1],
      isExpend: [this.data.collection?.isExpend || false],
      pages: [this.data.collection?.pages || []]
    });


    this.collectionForm.get('updateType')?.valueChanges.subscribe(() => {
      const intervalControl = this.collectionForm.get('intervalNumber');
      if (this.shouldShowInterval()) {
        intervalControl?.setValidators([Validators.required, Validators.min(1)]);
      } else {
        intervalControl?.clearValidators();
        intervalControl?.setValidators([Validators.min(1)]);
      }
      intervalControl?.updateValueAndValidity();
      this.updateSubmitDisabled();
    });

    this.collectionForm.valueChanges.subscribe(() => this.updateSubmitDisabled());
    this.updateSubmitDisabled();
  }

  private getInitialUpdateTime(): string {
    if (this.data.collection?.updateTime) {
      if (this.data.collection.updateTime instanceof Date) {
        return this.data.collection.updateTime.toISOString().substring(11, 19);
      }
      return /^\d{2}:\d{2}:\d{2}$/.test(this.data.collection.updateTime)
        ? this.data.collection.updateTime
        : '00:00:00';
    }
    return new Date().toISOString().substring(11, 19);
  }

  private findEmbeddingModelValue(value: EmbeddingModel | undefined): EmbeddingModel {
    const found = this.embeddingModels.find(model => model.value === value);
    return found ? found.value : EmbeddingModel.Azure;
  }

  private findUpdateTypeValue(value: UpdateType | undefined | null): UpdateType {
    const found = this.updateTypes.find(type => type.value === value);
    return found ? found.value : UpdateType.Daily;
  }

  private updateSubmitDisabled(): void {
    this.submitDisabled = this.collectionForm.invalid;
  }

  shouldShowInterval(): boolean {
    const updateType = this.collectionForm.get('updateType')?.value;
    return updateType === UpdateType.ByDay ||
           updateType === UpdateType.ByWeek ||
           updateType === UpdateType.ByMonth;
  }

  stepUp(element: HTMLInputElement, event: Event) {
    event.stopPropagation();
    try {
      element.stepUp();
    } catch (e) {
      this.step(element, +1);
    }
  }

  stepDown(element: HTMLInputElement, event: Event) {
    event.stopPropagation();
    try {
      element.stepDown();
    } catch (e) {
      this.step(element, -1);
    }
  }

  private step(element: HTMLInputElement, direction: number) {
    const value = parseFloat(element.value || '0');
    const step = parseFloat(element.step || '1');
    const minValue = isNaN(parseFloat(element.min)) ? -Infinity : parseFloat(element.min);
    const maxValue = isNaN(parseFloat(element.max)) ? Infinity : parseFloat(element.max);
    const precision = this.numberPrecision(step);
    const scalc = (value + step * direction).toFixed(precision);
    const calc = parseFloat(scalc);

    if (calc < minValue) {
      element.value = minValue + '';
    } else if (calc > maxValue) {
      element.value = maxValue + '';
    } else {
      element.value = calc + '';
    }
  }

  numberPrecision(number: number): number {
    if (Math.floor(number.valueOf()) === number.valueOf()) {
      return 0;
    }
    return number.toString().split('.')[1].length || 0;
  }


  save(): void {
    if (this.submitDisabled) return;

    if (!this.updateTimeSet && this.collectionForm.get('isAutomaticUpdate')?.value) {
      this.collectionForm.get('updateTime')?.setValue(new Date().toISOString().substring(11, 19));
    }

    const updatedCollection: Collection = {
      ...this.data.collection,
      ...this.collectionForm.value,
      updateTime: this.collectionForm.get('updateTime')?.value as string,
      status: 1,
    };
    this._matDialogRef.close(updatedCollection);
  }

  close(): void {
    this._matDialogRef.close();
  }

  listenDateChanged(ev: any) {
    const newTime = ev.detail?.timeStart || ev;
    if (newTime) {
      const timeString = typeof newTime === 'string' && /^\d{2}:\d{2}:\d{2}$/.test(newTime)
        ? newTime
        : new Date(newTime).toISOString().substring(11, 19);
      this.collectionForm.get('updateTime')?.setValue(timeString);
      this.updateTimeSet = true;
    }
  }
}
