import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';
import { ApiResponse } from '@shared/models/docupedia.model';
import { ResponseResult } from '@shared/models/share.model';
import { SysUserResponseDTO, SysUserAdd, SysUserUpdate } from '@shared/models/system.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SysUserService {
  private apiUrl = `${environment.baseUrl}/api/SysUser`;

  constructor(private http: HttpClient) {}

  getUsers(): Observable<SysUserResponseDTO[]> {
    return this.http.get<ApiResponse<SysUserResponseDTO[]>>(`${this.apiUrl}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        console.log('SysUserService.getUsers() - API Response:', response.data);
        return response.data;
      })
    );
  }

  getUserWithGroups(userId: string): Observable<SysUserResponseDTO> {
    return this.http.get<ApiResponse<SysUserResponseDTO>>(`${this.apiUrl}/${userId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  getUsersByGroupId(groupId: string): Observable<SysUserResponseDTO[]> {
    return this.http.get<ApiResponse<SysUserResponseDTO[]>>(`${this.apiUrl}/group/${groupId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  addUser(user: SysUserAdd): Observable<ResponseResult> {
    return this.http.post<ApiResponse<ResponseResult>>(`${this.apiUrl}`, user).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  updateUser(user: SysUserUpdate): Observable<ResponseResult> {
    return this.http.put<ApiResponse<ResponseResult>>(`${this.apiUrl}/${user.id}`, user).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  deleteUser(userId: string): Observable<ResponseResult> {
    return this.http.delete<ApiResponse<ResponseResult>>(`${this.apiUrl}/${userId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  assignUserToGroup(userId: string, groupId: string): Observable<ResponseResult> {
    return this.http.post<ApiResponse<ResponseResult>>(`${this.apiUrl}/${userId}/groups/${groupId}`, null).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  removeUserFromGroup(userId: string, groupId: string): Observable<ResponseResult> {
    return this.http.delete<ApiResponse<ResponseResult>>(`${this.apiUrl}/${userId}/groups/${groupId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }
}
