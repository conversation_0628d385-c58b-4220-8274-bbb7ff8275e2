﻿using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace BCI.DocupediaBot.Application.Services.Page
{
  public interface IContentsInPageService
	{
		Task<List<Guid>> QueryContentIdsByPageIdAsync(Guid pageId);
		Task<ResponseResult> DeleteMappingAsync(Guid contentId, Guid pageId);
		Task AddMappingAsync(Guid pageId, Guid contentId);
		Task<List<ContentsInPage>> QueryMappingsAsync();
		Task<ResponseResult> DeleteMappingsByContentIdAsync(Guid contentId);
	}
}
