# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@2.3.0", "@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@angular-devkit/architect@0.1802.12":
  version "0.1802.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular-devkit/architect/-/architect-0.1802.12.tgz"
  integrity sha512-bepVb2/GtJppYKaeW8yTGE6egmoWZ7zagFDsmBdbF+BYp+HmeoPsclARcdryBPVq68zedyTRdvhWSUTbw1AYuw==
  dependencies:
    "@angular-devkit/core" "18.2.12"
    rxjs "7.8.1"

"@angular-devkit/build-angular@^18.2.10":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular-devkit/build-angular/-/build-angular-18.2.12.tgz"
  integrity sha512-quVUi7eqTq9OHumQFNl9Y8t2opm8miu4rlYnuF6rbujmmBDvdUvR6trFChueRczl2p5HWqTOr6NPoDGQm8AyNw==
  dependencies:
    "@ampproject/remapping" "2.3.0"
    "@angular-devkit/architect" "0.1802.12"
    "@angular-devkit/build-webpack" "0.1802.12"
    "@angular-devkit/core" "18.2.12"
    "@angular/build" "18.2.12"
    "@babel/core" "7.25.2"
    "@babel/generator" "7.25.0"
    "@babel/helper-annotate-as-pure" "7.24.7"
    "@babel/helper-split-export-declaration" "7.24.7"
    "@babel/plugin-transform-async-generator-functions" "7.25.0"
    "@babel/plugin-transform-async-to-generator" "7.24.7"
    "@babel/plugin-transform-runtime" "7.24.7"
    "@babel/preset-env" "7.25.3"
    "@babel/runtime" "7.25.0"
    "@discoveryjs/json-ext" "0.6.1"
    "@ngtools/webpack" "18.2.12"
    "@vitejs/plugin-basic-ssl" "1.1.0"
    ansi-colors "4.1.3"
    autoprefixer "10.4.20"
    babel-loader "9.1.3"
    browserslist "^4.21.5"
    copy-webpack-plugin "12.0.2"
    critters "0.0.24"
    css-loader "7.1.2"
    esbuild-wasm "0.23.0"
    fast-glob "3.3.2"
    http-proxy-middleware "3.0.3"
    https-proxy-agent "7.0.5"
    istanbul-lib-instrument "6.0.3"
    jsonc-parser "3.3.1"
    karma-source-map-support "1.4.0"
    less "4.2.0"
    less-loader "12.2.0"
    license-webpack-plugin "4.0.2"
    loader-utils "3.3.1"
    magic-string "0.30.11"
    mini-css-extract-plugin "2.9.0"
    mrmime "2.0.0"
    open "10.1.0"
    ora "5.4.1"
    parse5-html-rewriting-stream "7.0.0"
    picomatch "4.0.2"
    piscina "4.6.1"
    postcss "8.4.41"
    postcss-loader "8.1.1"
    resolve-url-loader "5.0.0"
    rxjs "7.8.1"
    sass "1.77.6"
    sass-loader "16.0.0"
    semver "7.6.3"
    source-map-loader "5.0.0"
    source-map-support "0.5.21"
    terser "5.31.6"
    tree-kill "1.2.2"
    tslib "2.6.3"
    vite "5.4.6"
    watchpack "2.4.1"
    webpack "5.94.0"
    webpack-dev-middleware "7.4.2"
    webpack-dev-server "5.0.4"
    webpack-merge "6.0.1"
    webpack-subresource-integrity "5.1.0"
  optionalDependencies:
    esbuild "0.23.0"

"@angular-devkit/build-webpack@0.1802.12":
  version "0.1802.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular-devkit/build-webpack/-/build-webpack-0.1802.12.tgz"
  integrity sha512-0Z3fdbZVRnjYWE2/VYyfy+uieY+6YZyEp4ylzklVkc+fmLNsnz4Zw6cK1LzzcBqAwKIyh1IdW20Cg7o8b0sONA==
  dependencies:
    "@angular-devkit/architect" "0.1802.12"
    rxjs "7.8.1"

"@angular-devkit/core@18.2.12":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular-devkit/core/-/core-18.2.12.tgz"
  integrity sha512-NtB6ypsaDyPE6/fqWOdfTmACs+yK5RqfH5tStEzWFeeDsIEDYKsJ06ypuRep7qTjYus5Rmttk0Ds+cFgz8JdUQ==
  dependencies:
    ajv "8.17.1"
    ajv-formats "3.0.1"
    jsonc-parser "3.3.1"
    picomatch "4.0.2"
    rxjs "7.8.1"
    source-map "0.7.4"

"@angular-devkit/schematics@18.2.12":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular-devkit/schematics/-/schematics-18.2.12.tgz"
  integrity sha512-mMea9txHbnCX5lXLHlo0RAgfhFHDio45/jMsREM2PA8UtVf2S8ltXz7ZwUrUyMQRv8vaSfn4ijDstF4hDMnRgQ==
  dependencies:
    "@angular-devkit/core" "18.2.12"
    jsonc-parser "3.3.1"
    magic-string "0.30.11"
    ora "5.4.1"
    rxjs "7.8.1"

"@angular/animations@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/animations/-/animations-18.2.12.tgz"
  integrity sha512-XcWH/VFQ1Rddhdqi/iU8lW3Qg96yVx1NPfrO5lhcSSvVUzYWTZ5r+jh3GqYqUgPWyEp1Kpw3FLsOgVcGcBWQkQ==
  dependencies:
    tslib "^2.3.0"

"@angular/build@18.2.12":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/build/-/build-18.2.12.tgz"
  integrity sha512-4Ohz+OSILoL+cCAQ4UTiCT5v6pctu3fXNoNpTEUK46OmxELk9jDITO5rNyNS7TxBn9wY69kjX5VcDf7MenquFQ==
  dependencies:
    "@ampproject/remapping" "2.3.0"
    "@angular-devkit/architect" "0.1802.12"
    "@babel/core" "7.25.2"
    "@babel/helper-annotate-as-pure" "7.24.7"
    "@babel/helper-split-export-declaration" "7.24.7"
    "@babel/plugin-syntax-import-attributes" "7.24.7"
    "@inquirer/confirm" "3.1.22"
    "@vitejs/plugin-basic-ssl" "1.1.0"
    browserslist "^4.23.0"
    critters "0.0.24"
    esbuild "0.23.0"
    fast-glob "3.3.2"
    https-proxy-agent "7.0.5"
    listr2 "8.2.4"
    lmdb "3.0.13"
    magic-string "0.30.11"
    mrmime "2.0.0"
    parse5-html-rewriting-stream "7.0.0"
    picomatch "4.0.2"
    piscina "4.6.1"
    rollup "4.22.4"
    sass "1.77.6"
    semver "7.6.3"
    vite "5.4.6"
    watchpack "2.4.1"

"@angular/cdk@^18.2.0":
  version "18.2.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/cdk/-/cdk-18.2.13.tgz"
  integrity sha512-yBKoqcOwmwXnc5phFMEEMO130/Bz9beQLJrKzIS87f6TXaGCeBs4xrPHq2i7Xx/2TqvMiOD9ucjmlVbtGvNG3w==
  dependencies:
    tslib "^2.3.0"
  optionalDependencies:
    parse5 "^7.1.2"

"@angular/cli@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/cli/-/cli-18.2.12.tgz"
  integrity sha512-xhuZ/b7IhqNw1MgXf+arWf4x+GfUSt/IwbdWU4+CO8A7h0Y46zQywouP/KUK3cMQZfVdHdciTBvlpF3vFacA6Q==
  dependencies:
    "@angular-devkit/architect" "0.1802.12"
    "@angular-devkit/core" "18.2.12"
    "@angular-devkit/schematics" "18.2.12"
    "@inquirer/prompts" "5.3.8"
    "@listr2/prompt-adapter-inquirer" "2.0.15"
    "@schematics/angular" "18.2.12"
    "@yarnpkg/lockfile" "1.1.0"
    ini "4.1.3"
    jsonc-parser "3.3.1"
    listr2 "8.2.4"
    npm-package-arg "11.0.3"
    npm-pick-manifest "9.1.0"
    pacote "18.0.6"
    resolve "1.22.8"
    semver "7.6.3"
    symbol-observable "4.0.0"
    yargs "17.7.2"

"@angular/common@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/common/-/common-18.2.12.tgz"
  integrity sha512-gI5o8Bccsi8ow8Wk2vG4Tw/Rw9LoHEA9j8+qHKNR/55SCBsz68Syg310dSyxy+sApJO2WiqIadr5VP36dlSUFw==
  dependencies:
    tslib "^2.3.0"

"@angular/compiler-cli@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/compiler-cli/-/compiler-cli-18.2.12.tgz"
  integrity sha512-IWimTNq5Q+i2Wxev6HLqnN4iYbPvLz04W1BBycT1LfGUsHcjFYLuUqbeUzHbk2snmBAzXkixgVpo8SF6P4Y5Pg==
  dependencies:
    "@babel/core" "7.25.2"
    "@jridgewell/sourcemap-codec" "^1.4.14"
    chokidar "^4.0.0"
    convert-source-map "^1.5.1"
    reflect-metadata "^0.2.0"
    semver "^7.0.0"
    tslib "^2.3.0"
    yargs "^17.2.1"

"@angular/compiler@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/compiler/-/compiler-18.2.12.tgz"
  integrity sha512-D5d5dLrjQal5DbAXJJNSsCC3UxzjOI2wbc+Iv+LOpRM1gpNwuYfZMX5W7cj62Ce4G2++78CJSppdKBp8D4HErQ==
  dependencies:
    tslib "^2.3.0"

"@angular/core@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/core/-/core-18.2.12.tgz"
  integrity sha512-wCf/OObwS6bpM60rk6bpMpCRGp0DlMLB1WNAMtfcaPNyqimVV5Bm98mWRhkOuRyvU3fU7iHhM/10ePVaoyu9+A==
  dependencies:
    tslib "^2.3.0"

"@angular/forms@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/forms/-/forms-18.2.12.tgz"
  integrity sha512-FsukBJEU6jfAmht7TrODTkct/o4iwCZvGozuThOp0tYUPD/E1rZZzuKjEyTnT5Azpfkf0Wqx1nmpz80cczELOQ==
  dependencies:
    tslib "^2.3.0"

"@angular/material@^18.2.12":
  version "18.2.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/material/-/material-18.2.13.tgz"
  integrity sha512-Gxyyo6G+IXJwgf6zDTjPfFJ2PnjC2YXWKGkKKG2oR0jfiYiovDvNR4oXxhsztTwkaxLwck/gscoVTSQXMkU5fg==
  dependencies:
    tslib "^2.3.0"

"@angular/platform-browser-dynamic@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/platform-browser-dynamic/-/platform-browser-dynamic-18.2.12.tgz"
  integrity sha512-dv1QEjYpcFno6+oUeGEDRWpB5g2Ufb0XkUbLJQIgrOk1Qbyzb8tmpDpTjok8jcKdquigMRWolr6Y1EOicfRlLw==
  dependencies:
    tslib "^2.3.0"

"@angular/platform-browser@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/platform-browser/-/platform-browser-18.2.12.tgz"
  integrity sha512-DRSMznuxuecrs+v5BRyd60/R4vjkQtuYUEPfzdo+rqxM83Dmr3PGtnqPRgd5oAFUbATxf02hQXijRD27K7rZRg==
  dependencies:
    tslib "^2.3.0"

"@angular/router@^18.2.0":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@angular/router/-/router-18.2.12.tgz"
  integrity sha512-cz/1YWOZadAT35PPPYmpK3HSzKOE56nlUHue5bFkw73VSZr2iBn03ALLpd9YKzWgRmx3y7DqnlQtCkDu9JPGKQ==
  dependencies:
    tslib "^2.3.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.24.7", "@babel/code-frame@^7.25.9":
  version "7.26.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/code-frame/-/code-frame-7.26.2.tgz"
  integrity sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.25.2", "@babel/compat-data@^7.25.9":
  version "7.26.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/compat-data/-/compat-data-7.26.2.tgz"
  integrity sha512-Z0WgzSEa+aUcdiJuCIqgujCshpMWgUpgOxXotrYPSA53hA3qopNaqcJpyr0hVb1FeWdnqFA35/fUtXgBK8srQg==

"@babel/core@7.25.2", "@babel/core@^7.12.3", "@babel/core@^7.23.9":
  version "7.25.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/core/-/core-7.25.2.tgz"
  integrity sha512-BBt3opiCOxUr9euZ5/ro/Xv8/V7yJ5bjYMqG/C1YAo8MIKAnumZalCN+msbci3Pigy4lIQfPUpfMM27HMGaYEA==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.25.0"
    "@babel/helper-compilation-targets" "^7.25.2"
    "@babel/helper-module-transforms" "^7.25.2"
    "@babel/helpers" "^7.25.0"
    "@babel/parser" "^7.25.0"
    "@babel/template" "^7.25.0"
    "@babel/traverse" "^7.25.2"
    "@babel/types" "^7.25.2"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@7.25.0", "@babel/generator@^7.25.0":
  version "7.25.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/generator/-/generator-7.25.0.tgz"
  integrity sha512-3LEEcj3PVW8pW2R1SR1M89g/qrYk/m/mB/tLqn7dn4sbBUQyTqnlod+II2U4dqiGtUmkcnAmkMDralTFZttRiw==
  dependencies:
    "@babel/types" "^7.25.0"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/generator@^7.25.9":
  version "7.26.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/generator/-/generator-7.26.2.tgz"
  integrity sha512-zevQbhbau95nkoxSq3f/DC/SC+EEOUZd3DYqfSkMhY2/wfSeaHV1Ew4vk8e+x8lja31IbyuUa2uQ3JONqKbysw==
  dependencies:
    "@babel/parser" "^7.26.2"
    "@babel/types" "^7.26.0"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@7.24.7":
  version "7.24.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.24.7.tgz"
  integrity sha512-BaDeOonYvhdKw+JoMVkAixAAJzG2jVPIwWoKBPdYuY9b452e2rPuI9QPYh3KpofZ3pW2akOmwZLOiOsHMiqRAg==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz"
  integrity sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.25.9.tgz"
  integrity sha512-C47lC7LIDCnz0h4vai/tpNOI95tCd5ZT3iBt/DBH5lXKHZsyNQv18yf1wIIg2ntiQNgmAvA+DgZ82iW8Qdym8g==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.25.2", "@babel/helper-compilation-targets@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-compilation-targets/-/helper-compilation-targets-7.25.9.tgz"
  integrity sha512-j9Db8Suy6yV/VHa4qzrj9yZfZxhLWQdVnRlXxmKLYlhWUVB1sB2G5sxuWYXk/whHD9iW76PmNzxZ4UCnTQTVEQ==
  dependencies:
    "@babel/compat-data" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.25.9.tgz"
  integrity sha512-UTZQMvt0d/rSz6KI+qdu7GQze5TIajwTS++GUozlw8VBJDEOAqSXwm1WvmYEZwqdqSGQshRocPDqrt4HBZB3fQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.25.9.tgz"
  integrity sha512-ORPNZ3h6ZRkOyAa/SaHU+XsLZr0UQzRwuDQ0cczIA17nAzZ+85G5cVkOJIj7QavLZGSe8QXUmNFxSZzjcZF9bw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    regexpu-core "^6.1.1"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.2", "@babel/helper-define-polyfill-provider@^0.6.3":
  version "0.6.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.3.tgz"
  integrity sha512-HK7Bi+Hj6H+VTHA3ZvBis7V/6hu9QuTrnMXNybfUf2iiuU/N97I8VjB+KbhFF8Rld/Lx5MzoCwPCpPjfK+n8Cg==
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-member-expression-to-functions@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.25.9.tgz"
  integrity sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.24.7", "@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz"
  integrity sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-transforms@^7.25.2", "@babel/helper-module-transforms@^7.25.9":
  version "7.26.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz"
  integrity sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-optimise-call-expression@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.9.tgz"
  integrity sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.7", "@babel/helper-plugin-utils@^7.24.8", "@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-plugin-utils/-/helper-plugin-utils-7.25.9.tgz"
  integrity sha512-kSMlyUVdWe25rEsRGviIgOWnoT/nfABVWlqt9N19/dIPWViAOW2s9wznP5tURbs/IDuNk4gPy3YdYRgH3uxhBw==

"@babel/helper-remap-async-to-generator@^7.24.7", "@babel/helper-remap-async-to-generator@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.25.9.tgz"
  integrity sha512-IZtukuUeBbhgOcaW2s06OXTzVNJR0ybm4W5xC1opWFFJMZbwRj5LCk+ByYH7WdZPZTt8KnFwA8pvjN2yqcPlgw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-wrap-function" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-replace-supers@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-replace-supers/-/helper-replace-supers-7.25.9.tgz"
  integrity sha512-IiDqTOTBQy0sWyeXyGSC5TBJpGFXBkRynjBeXsvbhQFKj2viwJC76Epz35YLU1fpe/Am6Vppb7W7zM4fPQzLsQ==
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-simple-access@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-simple-access/-/helper-simple-access-7.25.9.tgz"
  integrity sha512-c6WHXuiaRsJTyHYLJV75t9IqsmTbItYfdj99PnzYGQZkYKvan5/2jKJ7gu31J3/BJ/A18grImSPModuyG/Eo0Q==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-skip-transparent-expression-wrappers@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz"
  integrity sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-split-export-declaration@7.24.7":
  version "7.24.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.24.7.tgz"
  integrity sha512-oy5V7pD+UvfkEATUKvIjvIAH/xCzfsFVw7ygW2SI6NClZzquT+mwdTfgfdbUiceh6iQO0CHtCPsyze/MZ2YbAA==
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz"
  integrity sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz"
  integrity sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==

"@babel/helper-validator-option@^7.24.8", "@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz"
  integrity sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==

"@babel/helper-wrap-function@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helper-wrap-function/-/helper-wrap-function-7.25.9.tgz"
  integrity sha512-ETzz9UTjQSTmw39GboatdymDq4XIQbR8ySgVrylRhPOFpsd+JrKHIuF0de7GCWmem+T4uC5z7EZguod7Wj4A4g==
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helpers@^7.25.0":
  version "7.26.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/helpers/-/helpers-7.26.0.tgz"
  integrity sha512-tbhNuIxNcVb21pInl3ZSjksLCvgdZy9KwJ8brv993QtIVKJBBkYXz4q4ZbAv31GdnC+R90np23L5FbEBlthAEw==
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.0"

"@babel/parser@^7.14.7", "@babel/parser@^7.23.9", "@babel/parser@^7.25.0", "@babel/parser@^7.25.9", "@babel/parser@^7.26.2":
  version "7.26.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/parser/-/parser-7.26.2.tgz"
  integrity sha512-DWMCZH9WA4Maitz2q21SRKHo9QXZxkDsbNZoVD62gusNtNBBqDg9i7uOhASfTfIGNzW+O+r7+jAlM8dwphcJKQ==
  dependencies:
    "@babel/types" "^7.26.0"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.25.3":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.25.9.tgz"
  integrity sha512-ZkRyVkThtxQ/J6nv3JFYv1RYY+JT5BvU0y3k5bWrmuG4woXypRa4PXmm9RhOwodRkYFWqC0C0cqcJ4OqR7kW+g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.25.9.tgz"
  integrity sha512-MrGRLZxLD/Zjj0gdU15dfs+HH/OXvnw/U4jJD8vpcP2CJQapPEv1IWwjc/qMg7ItBlPwSv1hRBbb7LeuANdcnw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.9.tgz"
  integrity sha512-2qUwwfAFpJLZqxd02YW9btUCZHl+RFvdDkNfZwaIJrvB8Tesjsk8pEQkTvGwZXLqXUx/2oyY3ySRhm6HOXuCug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.25.9.tgz"
  integrity sha512-6xWgLZTJXwilVjlnV7ospI3xi+sl8lN8rXXbBD6vYn3UYDlGsag8wrZkKcSI8G6KgqKP7vNFaDgeDnfAABq61g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.25.9.tgz"
  integrity sha512-aLnMXYPnzwwqhYSCyXfKkIkYgJ8zv9RK+roo9DkTXz38ynIhd9XCbN08s3MGvqL2MYGVUGdRQLL/JqBIeJhJBg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  integrity sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  integrity sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.24.7":
  version "7.26.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.26.0.tgz"
  integrity sha512-QCWT5Hh830hK5EQa7XzuqIkQU9tT/whqbDz7kuaZMHFl1inRRg7JnuAEOQ0Ur0QUl0NufCk1msK2BeY79Aj/eg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-attributes@7.24.7", "@babel/plugin-syntax-import-attributes@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.24.7.tgz"
  integrity sha512-hbX+lKKeUMGihnK8nvKqmXBInriT3GVjzXKFriV3YC6APGxMbP8RZNFwy91+hocLXq90Mta+HshoB31802bb8A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  integrity sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.25.9.tgz"
  integrity sha512-6jmooXYIwn9ca5/RylZADJ+EnSxVUS5sjeJ9UPk6RWRzXCmOJCy6dqItPJFpw2cuCangPK4OYr5uhGKcmrm5Qg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-async-generator-functions@7.25.0", "@babel/plugin-transform-async-generator-functions@^7.25.0":
  version "7.25.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.25.0.tgz"
  integrity sha512-uaIi2FdqzjpAMvVqvB51S42oC2JEVgh0LDsGfZVDysWE8LrJtQC2jvKmOqEYThKyB7bDEb7BP1GYWDm7tABA0Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/helper-remap-async-to-generator" "^7.25.0"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/traverse" "^7.25.0"

"@babel/plugin-transform-async-to-generator@7.24.7", "@babel/plugin-transform-async-to-generator@^7.24.7":
  version "7.24.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.24.7.tgz"
  integrity sha512-SQY01PcJfmQ+4Ash7NE+rpbLFbmqA2GPIgqzxfFTL4t1FKRq4zTms/7htKpoCUI9OcFYgzqfmCdH53s6/jn5fA==
  dependencies:
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/helper-remap-async-to-generator" "^7.24.7"

"@babel/plugin-transform-block-scoped-functions@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.25.9.tgz"
  integrity sha512-toHc9fzab0ZfenFpsyYinOX0J/5dgJVA2fm64xPewu7CoYHWEivIWKxkK2rMi4r3yQqLnVmheMXRdG+k239CgA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-block-scoping@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.25.9.tgz"
  integrity sha512-1F05O7AYjymAtqbsFETboN1NvBdcnzMerO+zlMyJBEz6WkMdejvGWw9p05iTSjC85RLlBseHHQpYaM4gzJkBGg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-properties@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.25.9.tgz"
  integrity sha512-bbMAII8GRSkcd0h0b4X+36GksxuheLFjP65ul9w6C3KgAamI3JqErNgSrosX6ZPj+Mpim5VvEbawXxJCyEUV3Q==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-static-block@^7.24.7":
  version "7.26.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.26.0.tgz"
  integrity sha512-6J2APTs7BDDm+UMqP1useWqhcRAXo0WIoVj26N7kPFB6S73Lgvyka4KTZYIxtgYXiN5HTyRObA72N2iu628iTQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-classes@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-classes/-/plugin-transform-classes-7.25.9.tgz"
  integrity sha512-mD8APIXmseE7oZvZgGABDyM34GUmK45Um2TXiBUt7PnuAxrgoSVf123qUzPxEr/+/BHrRn5NMZCdE2m/1F8DGg==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.25.9.tgz"
  integrity sha512-HnBegGqXZR12xbcTHlJ9HGxw1OniltT26J5YpfruGqtUHlz/xKf/G2ak9e+t0rVqrjXa9WOhvYPz1ERfMj23AA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/template" "^7.25.9"

"@babel/plugin-transform-destructuring@^7.24.8":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.25.9.tgz"
  integrity sha512-WkCGb/3ZxXepmMiX101nnGiU+1CAdut8oHyEOHxkKuS1qKpU2SMXE2uSvfz8PBuLd49V6LEsbtyPhWC7fnkgvQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dotall-regex@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.25.9.tgz"
  integrity sha512-t7ZQ7g5trIgSRYhI9pIJtRl64KHotutUJsh4Eze5l7olJv+mRSg4/MmbZ0tv1eeqRbdvo/+trvJD/Oc5DmW2cA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-keys@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.25.9.tgz"
  integrity sha512-LZxhJ6dvBb/f3x8xwWIuyiAHy56nrRG3PeYTpBkkzkYRRQ6tJLu68lEF5VIqMUZiAV7a8+Tb78nEoMCMcqjXBw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.25.9.tgz"
  integrity sha512-0UfuJS0EsXbRvKnwcLjFtJy/Sxc5J5jhLHnFhy7u4zih97Hz6tJkLU+O+FMMrNZrosUPxDi6sYxJ/EA8jDiAog==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dynamic-import@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.25.9.tgz"
  integrity sha512-GCggjexbmSLaFhqsojeugBpeaRIgWNTcgKVq/0qIteFEqY2A+b9QidYadrWlnbWQUrW5fn+mCvf3tr7OeBFTyg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-exponentiation-operator@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.25.9.tgz"
  integrity sha512-KRhdhlVk2nObA5AYa7QMgTMTVJdfHprfpAk4DjZVtllqRg9qarilstTKEhpVjyt+Npi8ThRyiV8176Am3CodPA==
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-export-namespace-from@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.25.9.tgz"
  integrity sha512-2NsEz+CxzJIVOPx2o9UsW1rXLqtChtLoVnwYHHiB04wS5sgn7mrV45fWMBX0Kk+ub9uXytVYfNP2HjbVbCB3Ww==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-for-of@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.25.9.tgz"
  integrity sha512-LqHxduHoaGELJl2uhImHwRQudhCM50pT46rIBNvtT/Oql3nqiS3wOwP+5ten7NpYSXrrVLgtZU3DZmPtWZo16A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-function-name@^7.25.1":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.25.9.tgz"
  integrity sha512-8lP+Yxjv14Vc5MuWBpJsoUCd3hD6V9DgBon2FVYL4jJgbnVQ9fTgYmonchzZJOVNgzEgbxp4OwAf6xz6M/14XA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-json-strings@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.25.9.tgz"
  integrity sha512-xoTMk0WXceiiIvsaquQQUaLLXSW1KJ159KP87VilruQm0LNNGxWzahxSS6T6i4Zg3ezp4vA4zuwiNUR53qmQAw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-literals@^7.25.2":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-literals/-/plugin-transform-literals-7.25.9.tgz"
  integrity sha512-9N7+2lFziW8W9pBl2TzaNht3+pgMIRP74zizeCSrtnSKVdUl8mAjjOP2OOVQAfZ881P2cNjDj1uAMEdeD50nuQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-logical-assignment-operators@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.25.9.tgz"
  integrity sha512-wI4wRAzGko551Y8eVf6iOY9EouIDTtPb0ByZx+ktDGHwv6bHFimrgJM/2T021txPZ2s4c7bqvHbd+vXG6K948Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-member-expression-literals@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.25.9.tgz"
  integrity sha512-PYazBVfofCQkkMzh2P6IdIUaCEWni3iYEerAsRWuVd8+jlM1S9S9cz1dF9hIzyoZ8IA3+OwVYIp9v9e+GbgZhA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-amd@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.25.9.tgz"
  integrity sha512-g5T11tnI36jVClQlMlt4qKDLlWnG5pP9CSM4GhdRciTNMRgkfpo5cR6b4rGIOYPgRRuFAvwjPQ/Yk+ql4dyhbw==
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-commonjs@^7.24.8":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.25.9.tgz"
  integrity sha512-dwh2Ol1jWwL2MgkCzUSOvfmKElqQcuswAZypBSUsScMXvgdT8Ekq5YA6TtqpTVWH+4903NmboMuH1o9i8Rxlyg==
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-simple-access" "^7.25.9"

"@babel/plugin-transform-modules-systemjs@^7.25.0":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.25.9.tgz"
  integrity sha512-hyss7iIlH/zLHaehT+xwiymtPOpsiwIIRlCAOwBB04ta5Tt+lNItADdlXw3jAWZ96VJ2jlhl/c+PNIQPKNfvcA==
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-modules-umd@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.25.9.tgz"
  integrity sha512-bS9MVObUgE7ww36HEfwe6g9WakQ0KF07mQF74uuXdkoziUPfKyu/nIm663kz//e5O1nPInPFx36z7WJmJ4yNEw==
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-named-capturing-groups-regex@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.25.9.tgz"
  integrity sha512-oqB6WHdKTGl3q/ItQhpLSnWWOpjUJLsOCLVyeFgeTktkBSCiurvPOsyt93gibI9CmuKvTUEtWmG5VhZD+5T/KA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-new-target@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.25.9.tgz"
  integrity sha512-U/3p8X1yCSoKyUj2eOBIx3FOn6pElFOKvAAGf8HTtItuPyB+ZeOqfn+mvTtg9ZlOAjsPdK3ayQEjqHjU/yLeVQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-nullish-coalescing-operator@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.25.9.tgz"
  integrity sha512-ENfftpLZw5EItALAD4WsY/KUWvhUlZndm5GC7G3evUsVeSJB6p0pBeLQUnRnBCBx7zV0RKQjR9kCuwrsIrjWog==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-numeric-separator@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.25.9.tgz"
  integrity sha512-TlprrJ1GBZ3r6s96Yq8gEQv82s8/5HnCVHtEJScUj90thHQbwe+E5MLhi2bbNHBEJuzrvltXSru+BUxHDoog7Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-object-rest-spread@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.25.9.tgz"
  integrity sha512-fSaXafEE9CVHPweLYw4J0emp1t8zYTXyzN3UuG+lylqkvYd7RMrsOQ8TYx5RF231be0vqtFC6jnx3UmpJmKBYg==
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"

"@babel/plugin-transform-object-super@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.25.9.tgz"
  integrity sha512-Kj/Gh+Rw2RNLbCK1VAWj2U48yxxqL2x0k10nPtSdRa0O2xnHXalD0s+o1A6a0W43gJ00ANo38jxkQreckOzv5A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"

"@babel/plugin-transform-optional-catch-binding@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.25.9.tgz"
  integrity sha512-qM/6m6hQZzDcZF3onzIhZeDHDO43bkNNlOX0i8n3lR6zLbu0GN2d8qfM/IERJZYauhAHSLHy39NF0Ctdvcid7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-optional-chaining@^7.24.8", "@babel/plugin-transform-optional-chaining@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.25.9.tgz"
  integrity sha512-6AvV0FsLULbpnXeBjrY4dmWF8F7gf8QnvTEoO/wX/5xm/xE1Xo8oPuD3MPS+KS9f9XBEAWN7X1aWr4z9HdOr7A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-parameters@^7.24.7", "@babel/plugin-transform-parameters@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.25.9.tgz"
  integrity sha512-wzz6MKwpnshBAiRmn4jR8LYz/g8Ksg0o80XmwZDlordjwEk9SxBzTWC7F5ef1jhbrbOW2DJ5J6ayRukrJmnr0g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-methods@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.25.9.tgz"
  integrity sha512-D/JUozNpQLAPUVusvqMxyvjzllRaF8/nSrP1s2YGQT/W4LHK4xxsMcHjhOGTS01mp9Hda8nswb+FblLdJornQw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-property-in-object@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.25.9.tgz"
  integrity sha512-Evf3kcMqzXA3xfYJmZ9Pg1OvKdtqsDMSWBDzZOPLvHiTt36E75jLDQo5w1gtRU95Q4E5PDttrTf25Fw8d/uWLw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-property-literals@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.25.9.tgz"
  integrity sha512-IvIUeV5KrS/VPavfSM/Iu+RE6llrHrYIKY1yfCzyO/lMXHQ+p7uGhonmGVisv6tSBSVgWzMBohTcvkC9vQcQFA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-regenerator@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.25.9.tgz"
  integrity sha512-vwDcDNsgMPDGP0nMqzahDWE5/MLcX8sv96+wfX7as7LoF/kr97Bo/7fI00lXY4wUXYfVmwIIyG80fGZ1uvt2qg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-reserved-words@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.25.9.tgz"
  integrity sha512-7DL7DKYjn5Su++4RXu8puKZm2XBPHyjWLUidaPEkCUBbE7IPcsrkRHggAOOKydH1dASWdcUBxrkOGNxUv5P3Jg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-runtime@7.24.7":
  version "7.24.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.24.7.tgz"
  integrity sha512-YqXjrk4C+a1kZjewqt+Mmu2UuV1s07y8kqcUf4qYLnoqemhR4gRQikhdAhSVJioMjVTu6Mo6pAbaypEA3jY6fw==
  dependencies:
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.1"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.25.9.tgz"
  integrity sha512-MUv6t0FhO5qHnS/W8XCbHmiRWOphNufpE1IVxhK5kuN3Td9FT1x4rx4K42s3RYdMXCXpfWkGSbCSd0Z64xA7Ng==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-spread@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-spread/-/plugin-transform-spread-7.25.9.tgz"
  integrity sha512-oNknIB0TbURU5pqJFVbOOFspVlrpVwo2H1+HUIsVDvp5VauGGDP1ZEvO8Nn5xyMEs3dakajOxlmkNW7kNgSm6A==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-sticky-regex@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.25.9.tgz"
  integrity sha512-WqBUSgeVwucYDP9U/xNRQam7xV8W5Zf+6Eo7T2SRVUFlhRiMNFdFz58u0KZmCVVqs2i7SHgpRnAhzRNmKfi2uA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-template-literals@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.25.9.tgz"
  integrity sha512-o97AE4syN71M/lxrCtQByzphAdlYluKPDBzDVzMmfCobUjjhAryZV0AIpRPrxN0eAkxXO6ZLEScmt+PNhj2OTw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typeof-symbol@^7.24.8":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.25.9.tgz"
  integrity sha512-v61XqUMiueJROUv66BVIOi0Fv/CUuZuZMl5NkRoCVxLAnMexZ0A3kMe7vvZ0nulxMuMp0Mk6S5hNh48yki08ZA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-escapes@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.25.9.tgz"
  integrity sha512-s5EDrE6bW97LtxOcGj1Khcx5AaXwiMmi4toFWRDP9/y0Woo6pXC+iyPu/KuhKtfSrNFd7jJB+/fkOtZy6aIC6Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-property-regex@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.25.9.tgz"
  integrity sha512-Jt2d8Ga+QwRluxRQ307Vlxa6dMrYEMZCgGxoPR8V52rxPyldHu3hdlHspxaqYmE7oID5+kB+UKUB/eWS+DkkWg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-regex@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.25.9.tgz"
  integrity sha512-yoxstj7Rg9dlNn9UQxzk4fcNivwv4nUYz7fYXBaKxvw/lnmPuOm/ikoELygbYq68Bls3D/D+NBPHiLwZdZZ4HA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-sets-regex@^7.24.7":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.25.9.tgz"
  integrity sha512-8BYqO3GeVNHtx69fdPshN3fnzUNLrWdHhk/icSwigksJGczKSizZ+Z6SBCxTs723Fr5VSNorTIK7a+R2tISvwQ==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/preset-env@7.25.3":
  version "7.25.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/preset-env/-/preset-env-7.25.3.tgz"
  integrity sha512-QsYW7UeAaXvLPX9tdVliMJE7MD7M6MLYVTovRTIwhoYQVFHR1rM4wO8wqAezYi3/BpSD+NzVCZ69R6smWiIi8g==
  dependencies:
    "@babel/compat-data" "^7.25.2"
    "@babel/helper-compilation-targets" "^7.25.2"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/helper-validator-option" "^7.24.8"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.25.3"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.25.0"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.25.0"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.24.7"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.25.0"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.24.7"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.24.7"
    "@babel/plugin-transform-async-generator-functions" "^7.25.0"
    "@babel/plugin-transform-async-to-generator" "^7.24.7"
    "@babel/plugin-transform-block-scoped-functions" "^7.24.7"
    "@babel/plugin-transform-block-scoping" "^7.25.0"
    "@babel/plugin-transform-class-properties" "^7.24.7"
    "@babel/plugin-transform-class-static-block" "^7.24.7"
    "@babel/plugin-transform-classes" "^7.25.0"
    "@babel/plugin-transform-computed-properties" "^7.24.7"
    "@babel/plugin-transform-destructuring" "^7.24.8"
    "@babel/plugin-transform-dotall-regex" "^7.24.7"
    "@babel/plugin-transform-duplicate-keys" "^7.24.7"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.25.0"
    "@babel/plugin-transform-dynamic-import" "^7.24.7"
    "@babel/plugin-transform-exponentiation-operator" "^7.24.7"
    "@babel/plugin-transform-export-namespace-from" "^7.24.7"
    "@babel/plugin-transform-for-of" "^7.24.7"
    "@babel/plugin-transform-function-name" "^7.25.1"
    "@babel/plugin-transform-json-strings" "^7.24.7"
    "@babel/plugin-transform-literals" "^7.25.2"
    "@babel/plugin-transform-logical-assignment-operators" "^7.24.7"
    "@babel/plugin-transform-member-expression-literals" "^7.24.7"
    "@babel/plugin-transform-modules-amd" "^7.24.7"
    "@babel/plugin-transform-modules-commonjs" "^7.24.8"
    "@babel/plugin-transform-modules-systemjs" "^7.25.0"
    "@babel/plugin-transform-modules-umd" "^7.24.7"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.24.7"
    "@babel/plugin-transform-new-target" "^7.24.7"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.24.7"
    "@babel/plugin-transform-numeric-separator" "^7.24.7"
    "@babel/plugin-transform-object-rest-spread" "^7.24.7"
    "@babel/plugin-transform-object-super" "^7.24.7"
    "@babel/plugin-transform-optional-catch-binding" "^7.24.7"
    "@babel/plugin-transform-optional-chaining" "^7.24.8"
    "@babel/plugin-transform-parameters" "^7.24.7"
    "@babel/plugin-transform-private-methods" "^7.24.7"
    "@babel/plugin-transform-private-property-in-object" "^7.24.7"
    "@babel/plugin-transform-property-literals" "^7.24.7"
    "@babel/plugin-transform-regenerator" "^7.24.7"
    "@babel/plugin-transform-reserved-words" "^7.24.7"
    "@babel/plugin-transform-shorthand-properties" "^7.24.7"
    "@babel/plugin-transform-spread" "^7.24.7"
    "@babel/plugin-transform-sticky-regex" "^7.24.7"
    "@babel/plugin-transform-template-literals" "^7.24.7"
    "@babel/plugin-transform-typeof-symbol" "^7.24.8"
    "@babel/plugin-transform-unicode-escapes" "^7.24.7"
    "@babel/plugin-transform-unicode-property-regex" "^7.24.7"
    "@babel/plugin-transform-unicode-regex" "^7.24.7"
    "@babel/plugin-transform-unicode-sets-regex" "^7.24.7"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.4"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.37.1"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz"
  integrity sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/runtime@7.25.0", "@babel/runtime@^7.21.0", "@babel/runtime@^7.8.4":
  version "7.25.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/runtime/-/runtime-7.25.0.tgz"
  integrity sha512-7dRy4DwXwtzBrPbZflqxnvfxLF8kdZXPkhymtDeFoFqE6ldzjQFgYTtYIFARcLEYDrqfBfYcZt1WqFxRoyC9Rw==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.25.0", "@babel/template@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/template/-/template-7.25.9.tgz"
  integrity sha512-9DGttpmPvIxBb/2uwpVo3dqJ+O6RooAFOS+lB+xDqoE2PVCE8nfoHMdZLpfCQRLwvohzXISPZcgxt80xLfsuwg==
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/traverse@^7.25.0", "@babel/traverse@^7.25.2", "@babel/traverse@^7.25.9":
  version "7.25.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/traverse/-/traverse-7.25.9.tgz"
  integrity sha512-ZCuvfwOwlz/bawvAuvcj8rrithP2/N55Tzz342AkTvq4qaWbGfmCk/tKhNaV2cthijKrPAA8SRJV5WWe7IBMJw==
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/generator" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.25.9"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.24.7", "@babel/types@^7.25.0", "@babel/types@^7.25.2", "@babel/types@^7.25.9", "@babel/types@^7.26.0", "@babel/types@^7.4.4":
  version "7.26.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@babel/types/-/types-7.26.0.tgz"
  integrity sha512-Z/yiTPj+lDVnF7lWeKCIJzaIkI0vYO87dMpZ4bg4TDrFe4XXLFWL1TbXU27gBP3QccxV9mZICCrnjnYlJjXHOA==
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@bci-portal/iframe-integration@5.17.0":
  version "5.17.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@bci-portal/iframe-integration/-/@bci-portal/iframe-integration-5.17.0.tgz"
  integrity sha512-KoNUOvuI158zdW3pxcRhSChJjqBr6OB+GR95D/4+dUBLYiitlPy2yjJ825Aa1O6CsNJ5QPkAAJDumLoopYSnHg==

"@bci-web-core/auth@18.1.2":
  version "18.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@bci-web-core/auth/-/@bci-web-core/auth-18.1.2.tgz"
  integrity sha512-7Fb8fm6anyuHkzROZXPnoshaP4yotq4qlEjynaFbuxcgkcQzQOTWMqX8n3GySlykNRjLD2EI+f+8fsxdzZ13ew==
  dependencies:
    tslib "^2.3.0"

"@bci-web-core/common-styles@8.1.2":
  version "8.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@bci-web-core/common-styles/-/@bci-web-core/common-styles-8.1.2.tgz"
  integrity sha512-nPw//+XYPxmmb8KlqFJDKk18a2j4TM0H6TMe9L12rYAHDF83l0mwjWbISmORWleMOiJ1gZYLetYEz2EcRCWH/w==

"@bci-web-core/core@18.1.2":
  version "18.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@bci-web-core/core/-/@bci-web-core/core-18.1.2.tgz"
  integrity sha512-2M2QHBdbcn0jqTz4M3LNvAVW+/sOJKodhcRIWy/fVFHXjG8L64wo1rotX9Vite9aFSMmBRedVmohCXZKwalLxQ==
  dependencies:
    tslib "^2.3.0"

"@bci-web-core/material-theme@18.1.1":
  version "18.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@bci-web-core/material-theme/-/@bci-web-core/material-theme-18.1.1.tgz"
  integrity sha512-7TEiXn7BcqIsgztRvM4dExbQlRTTb29jclOytjaJVlkqqNriFT/u8TUVVtRUCu48z3Nulg0MLJkriy/zB+wEYw==

"@bci-web-core/web-components@11.1.1":
  version "11.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@bci-web-core/web-components/-/@bci-web-core/web-components-11.1.1.tgz"
  integrity sha512-787OVO4FMiF/smkcMrrgST1jijI0XZ+2mkcm3dwogOEB/FRTdwx5asYvbEt/uz+pVKhILEF0np/i5XX/PEYs5w==
  dependencies:
    "@bci-web-core/common-styles" "8.1.2"
    "@material/button" "^14.0.0"
    "@material/checkbox" "^14.0.0"
    "@material/dialog" "^14.0.0"
    "@material/form-field" "^14.0.0"
    "@material/icon-button" "^14.0.0"
    "@material/menu" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/select" "^14.0.0"
    "@material/snackbar" "^14.0.0"
    "@spectrum-web-components/overlay" "^0.42.3"
    "@stencil/store" "^2.0.10"
    date-fns "^2.28.0"
    date-fns-tz "^1.3.0"
    fuzzysort "^1.2.1"
    lscache "^1.3.0"

"@colors/colors@1.5.0":
  version "1.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@colors/colors/-/colors-1.5.0.tgz"
  integrity sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==

"@discoveryjs/json-ext@0.6.1":
  version "0.6.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@discoveryjs/json-ext/-/json-ext-0.6.1.tgz"
  integrity sha512-boghen8F0Q8D+0/Q1/1r6DUEieUJ8w2a1gIknExMSHBsJFOr2+0KUfHiVYBvucPwl3+RU5PFBK833FjFCh3BhA==

"@esbuild/aix-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f"
  integrity sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==

"@esbuild/aix-ppc64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/aix-ppc64/-/aix-ppc64-0.23.0.tgz#145b74d5e4a5223489cabdc238d8dad902df5259"
  integrity sha512-3sG8Zwa5fMcA9bgqB8AfWPQ+HFke6uD3h1s3RIwUNK8EG7a4buxvuFTs3j1IMs2NXAk9F30C/FF4vxRgQCcmoQ==

"@esbuild/android-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052"
  integrity sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==

"@esbuild/android-arm64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/android-arm64/-/android-arm64-0.23.0.tgz#453bbe079fc8d364d4c5545069e8260228559832"
  integrity sha512-EuHFUYkAVfU4qBdyivULuu03FhJO4IJN9PGuABGrFy4vUuzk91P2d+npxHcFdpUnfYKy0PuV+n6bKIpHOB3prQ==

"@esbuild/android-arm@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/android-arm/-/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28"
  integrity sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==

"@esbuild/android-arm@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/android-arm/-/android-arm-0.23.0.tgz#26c806853aa4a4f7e683e519cd9d68e201ebcf99"
  integrity sha512-+KuOHTKKyIKgEEqKbGTK8W7mPp+hKinbMBeEnNzjJGyFcWsfrXjSTNluJHCY1RqhxFurdD8uNXQDei7qDlR6+g==

"@esbuild/android-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/android-x64/-/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e"
  integrity sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==

"@esbuild/android-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/android-x64/-/android-x64-0.23.0.tgz#1e51af9a6ac1f7143769f7ee58df5b274ed202e6"
  integrity sha512-WRrmKidLoKDl56LsbBMhzTTBxrsVwTKdNbKDalbEZr0tcsBgCLbEtoNthOW6PX942YiYq8HzEnb4yWQMLQuipQ==

"@esbuild/darwin-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz#e495b539660e51690f3928af50a76fb0a6ccff2a"
  integrity sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==

"@esbuild/darwin-arm64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/darwin-arm64/-/darwin-arm64-0.23.0.tgz#d996187a606c9534173ebd78c58098a44dd7ef9e"
  integrity sha512-YLntie/IdS31H54Ogdn+v50NuoWF5BDkEUFpiOChVa9UnKpftgwzZRrI4J132ETIi+D8n6xh9IviFV3eXdxfow==

"@esbuild/darwin-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22"
  integrity sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==

"@esbuild/darwin-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/darwin-x64/-/darwin-x64-0.23.0.tgz#30c8f28a7ef4e32fe46501434ebe6b0912e9e86c"
  integrity sha512-IMQ6eme4AfznElesHUPDZ+teuGwoRmVuuixu7sv92ZkdQcPbsNHzutd+rAfaBKo8YK3IrBEi9SLLKWJdEvJniQ==

"@esbuild/freebsd-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e"
  integrity sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==

"@esbuild/freebsd-arm64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/freebsd-arm64/-/freebsd-arm64-0.23.0.tgz#30f4fcec8167c08a6e8af9fc14b66152232e7fb4"
  integrity sha512-0muYWCng5vqaxobq6LB3YNtevDFSAZGlgtLoAc81PjUfiFz36n4KMpwhtAd4he8ToSI3TGyuhyx5xmiWNYZFyw==

"@esbuild/freebsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261"
  integrity sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==

"@esbuild/freebsd-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/freebsd-x64/-/freebsd-x64-0.23.0.tgz#1003a6668fe1f5d4439e6813e5b09a92981bc79d"
  integrity sha512-XKDVu8IsD0/q3foBzsXGt/KjD/yTKBCIwOHE1XwiXmrRwrX6Hbnd5Eqn/WvDekddK21tfszBSrE/WMaZh+1buQ==

"@esbuild/linux-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b"
  integrity sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==

"@esbuild/linux-arm64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-arm64/-/linux-arm64-0.23.0.tgz#3b9a56abfb1410bb6c9138790f062587df3e6e3a"
  integrity sha512-j1t5iG8jE7BhonbsEg5d9qOYcVZv/Rv6tghaXM/Ug9xahM0nX/H2gfu6X6z11QRTMT6+aywOMA8TDkhPo8aCGw==

"@esbuild/linux-arm@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9"
  integrity sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==

"@esbuild/linux-arm@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-arm/-/linux-arm-0.23.0.tgz#237a8548e3da2c48cd79ae339a588f03d1889aad"
  integrity sha512-SEELSTEtOFu5LPykzA395Mc+54RMg1EUgXP+iw2SJ72+ooMwVsgfuwXo5Fn0wXNgWZsTVHwY2cg4Vi/bOD88qw==

"@esbuild/linux-ia32@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2"
  integrity sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==

"@esbuild/linux-ia32@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-ia32/-/linux-ia32-0.23.0.tgz#4269cd19cb2de5de03a7ccfc8855dde3d284a238"
  integrity sha512-P7O5Tkh2NbgIm2R6x1zGJJsnacDzTFcRWZyTTMgFdVit6E98LTxO+v8LCCLWRvPrjdzXHx9FEOA8oAZPyApWUA==

"@esbuild/linux-loong64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df"
  integrity sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==

"@esbuild/linux-loong64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-loong64/-/linux-loong64-0.23.0.tgz#82b568f5658a52580827cc891cb69d2cb4f86280"
  integrity sha512-InQwepswq6urikQiIC/kkx412fqUZudBO4SYKu0N+tGhXRWUqAx+Q+341tFV6QdBifpjYgUndV1hhMq3WeJi7A==

"@esbuild/linux-mips64el@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe"
  integrity sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==

"@esbuild/linux-mips64el@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-mips64el/-/linux-mips64el-0.23.0.tgz#9a57386c926262ae9861c929a6023ed9d43f73e5"
  integrity sha512-J9rflLtqdYrxHv2FqXE2i1ELgNjT+JFURt/uDMoPQLcjWQA5wDKgQA4t/dTqGa88ZVECKaD0TctwsUfHbVoi4w==

"@esbuild/linux-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4"
  integrity sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==

"@esbuild/linux-ppc64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-ppc64/-/linux-ppc64-0.23.0.tgz#f3a79fd636ba0c82285d227eb20ed8e31b4444f6"
  integrity sha512-cShCXtEOVc5GxU0fM+dsFD10qZ5UpcQ8AM22bYj0u/yaAykWnqXJDpd77ublcX6vdDsWLuweeuSNZk4yUxZwtw==

"@esbuild/linux-riscv64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc"
  integrity sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==

"@esbuild/linux-riscv64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-riscv64/-/linux-riscv64-0.23.0.tgz#f9d2ef8356ce6ce140f76029680558126b74c780"
  integrity sha512-HEtaN7Y5UB4tZPeQmgz/UhzoEyYftbMXrBCUjINGjh3uil+rB/QzzpMshz3cNUxqXN7Vr93zzVtpIDL99t9aRw==

"@esbuild/linux-s390x@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de"
  integrity sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==

"@esbuild/linux-s390x@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-s390x/-/linux-s390x-0.23.0.tgz#45390f12e802201f38a0229e216a6aed4351dfe8"
  integrity sha512-WDi3+NVAuyjg/Wxi+o5KPqRbZY0QhI9TjrEEm+8dmpY9Xir8+HE/HNx2JoLckhKbFopW0RdO2D72w8trZOV+Wg==

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0"
  integrity sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==

"@esbuild/linux-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/linux-x64/-/linux-x64-0.23.0.tgz#c8409761996e3f6db29abcf9b05bee8d7d80e910"
  integrity sha512-a3pMQhUEJkITgAw6e0bWA+F+vFtCciMjW/LPtoj99MhVt+Mfb6bbL9hu2wmTZgNd994qTAEw+U/r6k3qHWWaOQ==

"@esbuild/netbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047"
  integrity sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==

"@esbuild/netbsd-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/netbsd-x64/-/netbsd-x64-0.23.0.tgz#ba70db0114380d5f6cfb9003f1d378ce989cd65c"
  integrity sha512-cRK+YDem7lFTs2Q5nEv/HHc4LnrfBCbH5+JHu6wm2eP+d8OZNoSMYgPZJq78vqQ9g+9+nMuIsAO7skzphRXHyw==

"@esbuild/openbsd-arm64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/openbsd-arm64/-/openbsd-arm64-0.23.0.tgz#72fc55f0b189f7a882e3cf23f332370d69dfd5db"
  integrity sha512-suXjq53gERueVWu0OKxzWqk7NxiUWSUlrxoZK7usiF50C6ipColGR5qie2496iKGYNLhDZkPxBI3erbnYkU0rQ==

"@esbuild/openbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70"
  integrity sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==

"@esbuild/openbsd-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/openbsd-x64/-/openbsd-x64-0.23.0.tgz#b6ae7a0911c18fe30da3db1d6d17a497a550e5d8"
  integrity sha512-6p3nHpby0DM/v15IFKMjAaayFhqnXV52aEmv1whZHX56pdkK+MEaLoQWj+H42ssFarP1PcomVhbsR4pkz09qBg==

"@esbuild/sunos-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b"
  integrity sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==

"@esbuild/sunos-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/sunos-x64/-/sunos-x64-0.23.0.tgz#58f0d5e55b9b21a086bfafaa29f62a3eb3470ad8"
  integrity sha512-BFelBGfrBwk6LVrmFzCq1u1dZbG4zy/Kp93w2+y83Q5UGYF1d8sCzeLI9NXjKyujjBBniQa8R8PzLFAUrSM9OA==

"@esbuild/win32-arm64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d"
  integrity sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==

"@esbuild/win32-arm64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/win32-arm64/-/win32-arm64-0.23.0.tgz#b858b2432edfad62e945d5c7c9e5ddd0f528ca6d"
  integrity sha512-lY6AC8p4Cnb7xYHuIxQ6iYPe6MfO2CC43XXKo9nBXDb35krYt7KGhQnOkRGar5psxYkircpCqfbNDB4uJbS2jQ==

"@esbuild/win32-ia32@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b"
  integrity sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==

"@esbuild/win32-ia32@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/win32-ia32/-/win32-ia32-0.23.0.tgz#167ef6ca22a476c6c0c014a58b4f43ae4b80dec7"
  integrity sha512-7L1bHlOTcO4ByvI7OXVI5pNN6HSu6pUQq9yodga8izeuB1KcT2UkHaH6118QJwopExPn0rMHIseCTx1CRo/uNA==

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz"
  integrity sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==

"@esbuild/win32-x64@0.23.0":
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@esbuild/win32-x64/-/win32-x64-0.23.0.tgz"
  integrity sha512-Arm+WgUFLUATuoxCJcahGuk6Yj9Pzxd6l11Zb/2aAuv5kWWvvfhLFo2fni4uSK5vzlUdCGZ/BdV5tH8klj8p8g==

"@floating-ui/core@^1.6.0":
  version "1.6.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@floating-ui/core/-/core-1.6.8.tgz"
  integrity sha512-7XJ9cPU+yI2QeLS+FCSlqNFZJq8arvswefkZrYI1yQBbftw6FyrZOxYSh+9S7z7TpeWlRt9zJ5IhM1WIL334jA==
  dependencies:
    "@floating-ui/utils" "^0.2.8"

"@floating-ui/dom@^1.6.1":
  version "1.6.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@floating-ui/dom/-/dom-1.6.12.tgz"
  integrity sha512-NP83c0HjokcGVEMeoStg317VD9W7eDlGK7457dMBANbKA6GJZdc7rjujdgqzTaz93jkGgc5P/jeWbaCHnMNc+w==
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.8"

"@floating-ui/utils@^0.2.1", "@floating-ui/utils@^0.2.8":
  version "0.2.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@floating-ui/utils/-/utils-0.2.8.tgz"
  integrity sha512-kym7SodPp8/wloecOpcmSnWJsK7M0E5Wg8UcFA+uO4B9s5d0ywXOEro/8HM9x0rW+TljRzul/14UYz3TleT3ig==

"@inquirer/checkbox@^2.4.7":
  version "2.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/checkbox/-/checkbox-2.5.0.tgz"
  integrity sha512-sMgdETOfi2dUHT8r7TT1BTKOwNvdDGFDXYWtQ2J69SvlYNntk9I/gJe7r5yvMwwsuKnYbuRs3pNhx4tgNck5aA==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/figures" "^1.0.5"
    "@inquirer/type" "^1.5.3"
    ansi-escapes "^4.3.2"
    yoctocolors-cjs "^2.1.2"

"@inquirer/confirm@3.1.22", "@inquirer/confirm@^3.1.22":
  version "3.1.22"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/confirm/-/confirm-3.1.22.tgz"
  integrity sha512-gsAKIOWBm2Q87CDfs9fEo7wJT3fwWIJfnDGMn9Qy74gBnNFOACDNfhUzovubbJjWnKLGBln7/NcSmZwj5DuEXg==
  dependencies:
    "@inquirer/core" "^9.0.10"
    "@inquirer/type" "^1.5.2"

"@inquirer/core@^9.0.10", "@inquirer/core@^9.1.0":
  version "9.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/core/-/core-9.2.1.tgz"
  integrity sha512-F2VBt7W/mwqEU4bL0RnHNZmC/OxzNx9cOYxHqnXX3MP6ruYvZUZAW9imgN9+h/uBT/oP8Gh888J2OZSbjSeWcg==
  dependencies:
    "@inquirer/figures" "^1.0.6"
    "@inquirer/type" "^2.0.0"
    "@types/mute-stream" "^0.0.4"
    "@types/node" "^22.5.5"
    "@types/wrap-ansi" "^3.0.0"
    ansi-escapes "^4.3.2"
    cli-width "^4.1.0"
    mute-stream "^1.0.0"
    signal-exit "^4.1.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^6.2.0"
    yoctocolors-cjs "^2.1.2"

"@inquirer/editor@^2.1.22":
  version "2.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/editor/-/editor-2.2.0.tgz"
  integrity sha512-9KHOpJ+dIL5SZli8lJ6xdaYLPPzB8xB9GZItg39MBybzhxA16vxmszmQFrRwbOA918WA2rvu8xhDEg/p6LXKbw==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/type" "^1.5.3"
    external-editor "^3.1.0"

"@inquirer/expand@^2.1.22":
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/expand/-/expand-2.3.0.tgz"
  integrity sha512-qnJsUcOGCSG1e5DTOErmv2BPQqrtT6uzqn1vI/aYGiPKq+FgslGZmtdnXbhuI7IlT7OByDoEEqdnhUnVR2hhLw==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/type" "^1.5.3"
    yoctocolors-cjs "^2.1.2"

"@inquirer/figures@^1.0.5", "@inquirer/figures@^1.0.6":
  version "1.0.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/figures/-/figures-1.0.8.tgz"
  integrity sha512-tKd+jsmhq21AP1LhexC0pPwsCxEhGgAkg28byjJAd+xhmIs8LUX8JbUc3vBf3PhLxWiB5EvyBE5X7JSPAqMAqg==

"@inquirer/input@^2.2.9":
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/input/-/input-2.3.0.tgz"
  integrity sha512-XfnpCStx2xgh1LIRqPXrTNEEByqQWoxsWYzNRSEUxJ5c6EQlhMogJ3vHKu8aXuTacebtaZzMAHwEL0kAflKOBw==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/type" "^1.5.3"

"@inquirer/number@^1.0.10":
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/number/-/number-1.1.0.tgz"
  integrity sha512-ilUnia/GZUtfSZy3YEErXLJ2Sljo/mf9fiKc08n18DdwdmDbOzRcTv65H1jjDvlsAuvdFXf4Sa/aL7iw/NanVA==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/type" "^1.5.3"

"@inquirer/password@^2.1.22":
  version "2.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/password/-/password-2.2.0.tgz"
  integrity sha512-5otqIpgsPYIshqhgtEwSspBQE40etouR8VIxzpJkv9i0dVHIpyhiivbkH9/dGiMLdyamT54YRdGJLfl8TFnLHg==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/type" "^1.5.3"
    ansi-escapes "^4.3.2"

"@inquirer/prompts@5.3.8":
  version "5.3.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/prompts/-/prompts-5.3.8.tgz"
  integrity sha512-b2BudQY/Si4Y2a0PdZZL6BeJtl8llgeZa7U2j47aaJSCeAl1e4UI7y8a9bSkO3o/ZbZrgT5muy/34JbsjfIWxA==
  dependencies:
    "@inquirer/checkbox" "^2.4.7"
    "@inquirer/confirm" "^3.1.22"
    "@inquirer/editor" "^2.1.22"
    "@inquirer/expand" "^2.1.22"
    "@inquirer/input" "^2.2.9"
    "@inquirer/number" "^1.0.10"
    "@inquirer/password" "^2.1.22"
    "@inquirer/rawlist" "^2.2.4"
    "@inquirer/search" "^1.0.7"
    "@inquirer/select" "^2.4.7"

"@inquirer/rawlist@^2.2.4":
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/rawlist/-/rawlist-2.3.0.tgz"
  integrity sha512-zzfNuINhFF7OLAtGHfhwOW2TlYJyli7lOUoJUXw/uyklcwalV6WRXBXtFIicN8rTRK1XTiPWB4UY+YuW8dsnLQ==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/type" "^1.5.3"
    yoctocolors-cjs "^2.1.2"

"@inquirer/search@^1.0.7":
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/search/-/search-1.1.0.tgz"
  integrity sha512-h+/5LSj51dx7hp5xOn4QFnUaKeARwUCLs6mIhtkJ0JYPBLmEYjdHSYh7I6GrLg9LwpJ3xeX0FZgAG1q0QdCpVQ==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/figures" "^1.0.5"
    "@inquirer/type" "^1.5.3"
    yoctocolors-cjs "^2.1.2"

"@inquirer/select@^2.4.7":
  version "2.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/select/-/select-2.5.0.tgz"
  integrity sha512-YmDobTItPP3WcEI86GvPo+T2sRHkxxOq/kXmsBjHS5BVXUgvgZ5AfJjkvQvZr03T81NnI3KrrRuMzeuYUQRFOA==
  dependencies:
    "@inquirer/core" "^9.1.0"
    "@inquirer/figures" "^1.0.5"
    "@inquirer/type" "^1.5.3"
    ansi-escapes "^4.3.2"
    yoctocolors-cjs "^2.1.2"

"@inquirer/type@^1.5.1", "@inquirer/type@^1.5.2", "@inquirer/type@^1.5.3":
  version "1.5.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/type/-/type-1.5.5.tgz"
  integrity sha512-MzICLu4yS7V8AA61sANROZ9vT1H3ooca5dSmI1FjZkzq7o/koMsRfQSzRtFo+F3Ao4Sf1C0bpLKejpKB/+j6MA==
  dependencies:
    mute-stream "^1.0.0"

"@inquirer/type@^2.0.0":
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@inquirer/type/-/type-2.0.0.tgz"
  integrity sha512-XvJRx+2KR3YXyYtPUUy+qd9i7p+GO9Ko6VIIpWlBrpWwXDv8WLFeHTxz35CfQFUiBMLXlGHhGzys7lqit9gWag==
  dependencies:
    mute-stream "^1.0.0"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@istanbuljs/schema/-/schema-0.1.3.tgz"
  integrity sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jridgewell/source-map/-/source-map-0.3.6.tgz"
  integrity sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.20", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jsonjoy.com/base64@^1.1.1":
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jsonjoy.com/base64/-/base64-1.1.2.tgz"
  integrity sha512-q6XAnWQDIMA3+FTiOYajoYqySkO+JSat0ytXGSuRdq9uXE7o92gzuQwQM14xaCRlBLGq3v5miDGC4vkVTn54xA==

"@jsonjoy.com/json-pack@^1.0.3":
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jsonjoy.com/json-pack/-/json-pack-1.1.0.tgz"
  integrity sha512-zlQONA+msXPPwHWZMKFVS78ewFczIll5lXiVPwFPCZUsrOKdxc2AvxU1HoNBmMRhqDZUR9HkC3UOm+6pME6Xsg==
  dependencies:
    "@jsonjoy.com/base64" "^1.1.1"
    "@jsonjoy.com/util" "^1.1.2"
    hyperdyperid "^1.2.0"
    thingies "^1.20.0"

"@jsonjoy.com/util@^1.1.2", "@jsonjoy.com/util@^1.3.0":
  version "1.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@jsonjoy.com/util/-/util-1.5.0.tgz"
  integrity sha512-ojoNsrIuPI9g6o8UxhraZQSyF2ByJanAY4cTFbc8Mf2AXEF4aQRGY1dJxyJpuyav8r9FGflEt/Ff3u5Nt6YMPA==

"@juggle/resize-observer@^3.3.1":
  version "3.4.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@juggle/resize-observer/-/resize-observer-3.4.0.tgz"
  integrity sha512-dfLbk+PwWvFzSxwk3n5ySL0hfBog779o8h68wK/7/APo/7cgyWp5jcXockbxdk5kFRkbeXWm4Fbi9FrdN381sA==

"@leichtgewicht/ip-codec@^2.0.1":
  version "2.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz"
  integrity sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw==

"@listr2/prompt-adapter-inquirer@2.0.15":
  version "2.0.15"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@listr2/prompt-adapter-inquirer/-/prompt-adapter-inquirer-2.0.15.tgz"
  integrity sha512-MZrGem/Ujjd4cPTLYDfCZK2iKKeiO/8OX13S6jqxldLs0Prf2aGqVlJ77nMBqMv7fzqgXEgjrNHLXcKR8l9lOg==
  dependencies:
    "@inquirer/type" "^1.5.1"

"@lit-labs/observers@^2.0.2":
  version "2.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lit-labs/observers/-/observers-2.0.4.tgz"
  integrity sha512-x95jhDPGb+HtYU3hEdqkcLxb6v2JBP3tcajaiOijs1F/ZmOgRT0pRPn0v+jhhk8mAAbEO12SZJjPCmuysunssQ==
  dependencies:
    "@lit/reactive-element" "^1.0.0 || ^2.0.0"
    lit-html "^3.2.0"

"@lit-labs/ssr-dom-shim@^1.2.0":
  version "1.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lit-labs/ssr-dom-shim/-/ssr-dom-shim-1.2.1.tgz"
  integrity sha512-wx4aBmgeGvFmOKucFKY+8VFJSYZxs9poN3SDNQFF6lT6NrQUnHiPB2PWz2sc4ieEcAaYYzN+1uWahEeTq2aRIQ==

"@lit/reactive-element@^1.0.0 || ^2.0.0", "@lit/reactive-element@^2.0.4":
  version "2.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lit/reactive-element/-/reactive-element-2.0.4.tgz"
  integrity sha512-GFn91inaUa2oHLak8awSIigYz0cU0Payr1rcFsrkf5OJ5eSPxElyZfKh0f2p9FsTiZWXQdWGJeXZICEfXXYSXQ==
  dependencies:
    "@lit-labs/ssr-dom-shim" "^1.2.0"

"@lmdb/lmdb-darwin-arm64@3.0.13":
  version "3.0.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lmdb/lmdb-darwin-arm64/-/lmdb-darwin-arm64-3.0.13.tgz#b09af72bde5a9933abfab34dc5c10e5b6c16d4a5"
  integrity sha512-uiKPB0Fv6WEEOZjruu9a6wnW/8jrjzlZbxXscMB8kuCJ1k6kHpcBnuvaAWcqhbI7rqX5GKziwWEdD+wi2gNLfA==

"@lmdb/lmdb-darwin-x64@3.0.13":
  version "3.0.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lmdb/lmdb-darwin-x64/-/lmdb-darwin-x64-3.0.13.tgz#bc27aadf475954254b787ef609a9f598adc8ac6b"
  integrity sha512-bEVIIfK5mSQoG1R19qA+fJOvCB+0wVGGnXHT3smchBVahYBdlPn2OsZZKzlHWfb1E+PhLBmYfqB5zQXFP7hJig==

"@lmdb/lmdb-linux-arm64@3.0.13":
  version "3.0.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lmdb/lmdb-linux-arm64/-/lmdb-linux-arm64-3.0.13.tgz#fd49c447e1e8304fc4101bb7e6b641f08d69ac28"
  integrity sha512-afbVrsMgZ9dUTNUchFpj5VkmJRxvht/u335jUJ7o23YTbNbnpmXif3VKQGCtnjSh+CZaqm6N3CPG8KO3zwyZ1Q==

"@lmdb/lmdb-linux-arm@3.0.13":
  version "3.0.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lmdb/lmdb-linux-arm/-/lmdb-linux-arm-3.0.13.tgz#14ebce383cbc9d51cbe12581dfec3ef8ffc6b685"
  integrity sha512-Yml1KlMzOnXj/tnW7yX8U78iAzTk39aILYvCPbqeewAq1kSzl+w59k/fiVkTBfvDi/oW/5YRxL+Fq+Y1Fr1r2Q==

"@lmdb/lmdb-linux-x64@3.0.13":
  version "3.0.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lmdb/lmdb-linux-x64/-/lmdb-linux-x64-3.0.13.tgz#49b9bfcd52df55ccc9e466cf27c0651e5434b320"
  integrity sha512-vOtxu0xC0SLdQ2WRXg8Qgd8T32ak4SPqk5zjItRszrJk2BdeXqfGxBJbP7o4aOvSPSmSSv46Lr1EP4HXU8v7Kg==

"@lmdb/lmdb-win32-x64@3.0.13":
  version "3.0.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@lmdb/lmdb-win32-x64/-/lmdb-win32-x64-3.0.13.tgz"
  integrity sha512-UCrMJQY/gJnOl3XgbWRZZUvGGBuKy6i0YNSptgMzHBjs+QYDYR1Mt/RLTOPy4fzzves65O1EDmlL//OzEqoLlA==

"@material/animation@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/animation/-/animation-14.0.0.tgz"
  integrity sha512-VlYSfUaIj/BBVtRZI8Gv0VvzikFf+XgK0Zdgsok5c1v5DDnNz5tpB8mnGrveWz0rHbp1X4+CWLKrTwNmjrw3Xw==
  dependencies:
    tslib "^2.1.0"

"@material/base@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/base/-/base-14.0.0.tgz"
  integrity sha512-Ou7vS7n1H4Y10MUZyYAbt6H0t67c6urxoCgeVT7M38aQlaNUwFMODp7KT/myjYz2YULfhu3PtfSV3Sltgac9mA==
  dependencies:
    tslib "^2.1.0"

"@material/button@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/button/-/button-14.0.0.tgz"
  integrity sha512-dqqHaJq0peyXBZupFzCjmvScrfljyVU66ZCS3oldsaaj5iz8sn33I/45Z4zPzdR5F5z8ExToHkRcXhakj1UEAA==
  dependencies:
    "@material/density" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/elevation" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/focus-ring" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/shape" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/tokens" "^14.0.0"
    "@material/touch-target" "^14.0.0"
    "@material/typography" "^14.0.0"
    tslib "^2.1.0"

"@material/checkbox@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/checkbox/-/checkbox-14.0.0.tgz"
  integrity sha512-OoqwysCqvj1d0cRmEwVWPvg5OqYAiCFpE6Wng6me/Cahfe4xgRxSPa37WWqsClw20W7PG/5RrYRCBtc6bUUUZA==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/density" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/focus-ring" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/touch-target" "^14.0.0"
    tslib "^2.1.0"

"@material/density@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/density/-/density-14.0.0.tgz"
  integrity sha512-NlxXBV5XjNsKd8UXF4K/+fOXLxoFNecKbsaQO6O2u+iG8QBfFreKRmkhEBb2hPPwC3w8nrODwXX0lHV+toICQw==
  dependencies:
    tslib "^2.1.0"

"@material/dialog@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/dialog/-/dialog-14.0.0.tgz"
  integrity sha512-E07NEE4jP8jHaw/y2Il2R1a3f4wDFh2sgfCBtRO/Xh0xxJUMuQ7YXo/F3SAA8jfMbbkUv/PHdJUM3I3HmI9mAA==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/button" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/elevation" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/icon-button" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/shape" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/tokens" "^14.0.0"
    "@material/touch-target" "^14.0.0"
    "@material/typography" "^14.0.0"
    tslib "^2.1.0"

"@material/dom@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/dom/-/dom-14.0.0.tgz"
  integrity sha512-8t88XyacclTj8qsIw9q0vEj4PI2KVncLoIsIMzwuMx49P2FZg6TsLjor262MI3Qs00UWAifuLMrhnOnfyrbe7Q==
  dependencies:
    "@material/feature-targeting" "^14.0.0"
    tslib "^2.1.0"

"@material/elevation@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/elevation/-/elevation-14.0.0.tgz"
  integrity sha512-Di3tkxTpXwvf1GJUmaC8rd+zVh5dB2SWMBGagL4+kT8UmjSISif/OPRGuGnXs3QhF6nmEjkdC0ijdZLcYQkepw==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/feature-targeting@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/feature-targeting/-/feature-targeting-14.0.0.tgz"
  integrity sha512-a5WGgHEq5lJeeNL5yevtgoZjBjXWy6+klfVWQEh8oyix/rMJygGgO7gEc52uv8fB8uAIoYEB3iBMOv8jRq8FeA==
  dependencies:
    tslib "^2.1.0"

"@material/floating-label@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/floating-label/-/floating-label-14.0.0.tgz"
  integrity sha512-Aq8BboP1sbNnOtsV72AfaYirHyOrQ/GKFoLrZ1Jt+ZGIAuXPETcj9z7nQDznst0ZeKcz420PxNn9tsybTbeL/Q==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/typography" "^14.0.0"
    tslib "^2.1.0"

"@material/focus-ring@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/focus-ring/-/focus-ring-14.0.0.tgz"
  integrity sha512-fqqka6iSfQGJG3Le48RxPCtnOiaLGPDPikhktGbxlyW9srBVMgeCiONfHM7IT/1eu80O0Y67Lh/4ohu5+C+VAQ==
  dependencies:
    "@material/dom" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/rtl" "^14.0.0"

"@material/form-field@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/form-field/-/form-field-14.0.0.tgz"
  integrity sha512-k1GNBj6Sp8A7Xsn5lTMp5DkUkg60HX7YkQIRyFz1qCDCKJRWh/ou7Z45GMMgKmG3aF6LfjIavc7SjyCl8e5yVg==
  dependencies:
    "@material/base" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/typography" "^14.0.0"
    tslib "^2.1.0"

"@material/icon-button@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/icon-button/-/icon-button-14.0.0.tgz"
  integrity sha512-wHMqzm7Q/UwbWLoWv32Li1r2iVYxadIrwTNxT0+p+7NdfI3lEwMN3NoB0CvoJnHTljjXDzce0KJ3nZloa0P0gA==
  dependencies:
    "@material/base" "^14.0.0"
    "@material/density" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/elevation" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/focus-ring" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/touch-target" "^14.0.0"
    tslib "^2.1.0"

"@material/line-ripple@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/line-ripple/-/line-ripple-14.0.0.tgz"
  integrity sha512-Rx9eSnfp3FcsNz4O+fobNNq2PSm5tYHC3hRpY2ZK3ghTvgp3Y40/soaGEi/Vdg0F7jJXRaBSNOe6p5t9CVfy8Q==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/list@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/list/-/list-14.0.0.tgz"
  integrity sha512-AFaBGV9vQyfnG8BT2R3UGVdF5w2SigQqBH+qbOSxQhk4BgVvhDfJUIKT415poLNMdnaDtcuYz+ZWvVNoRDaL7w==
  dependencies:
    "@material/base" "^14.0.0"
    "@material/density" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/shape" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/typography" "^14.0.0"
    tslib "^2.1.0"

"@material/menu-surface@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/menu-surface/-/menu-surface-14.0.0.tgz"
  integrity sha512-wRz3UCrhJ4kRrijJEbvIPRa0mqA5qkQmKXjBH4Xu1ApedZruP+OM3Qb2Bj4XugCA3eCXpiohg+gdyTAX3dVQyw==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/elevation" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/shape" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/menu@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/menu/-/menu-14.0.0.tgz"
  integrity sha512-oU6GjbYnkG6a5nX9HUSege5OQByf6yUteEij8fpf0ci3f5BWf/gr39dnQ+rfl+q119cW0WIEmVK2YJ/BFxMzEQ==
  dependencies:
    "@material/base" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/elevation" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/list" "^14.0.0"
    "@material/menu-surface" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/notched-outline@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/notched-outline/-/notched-outline-14.0.0.tgz"
  integrity sha512-6S58DlWmhCDr4RQF2RuwqANxlmLdHtWy2mF4JQLD9WOiCg4qY9eCQnMXu3Tbhr7f/nOZ0vzc7AtA3vfJoZmCSw==
  dependencies:
    "@material/base" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/floating-label" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/shape" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/ripple@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/ripple/-/ripple-14.0.0.tgz"
  integrity sha512-9XoGBFd5JhFgELgW7pqtiLy+CnCIcV2s9cQ2BWbOQeA8faX9UZIDUx/g76nHLZ7UzKFtsULJxZTwORmsEt2zvw==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/rtl@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/rtl/-/rtl-14.0.0.tgz"
  integrity sha512-xl6OZYyRjuiW2hmbjV2omMV8sQtfmKAjeWnD1RMiAPLCTyOW9Lh/PYYnXjxUrNa0cRwIIbOn5J7OYXokja8puA==
  dependencies:
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/select@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/select/-/select-14.0.0.tgz"
  integrity sha512-4aY1kUHEnbOCRG3Tkuuk8yFfyNYSvOstBbjiYE/Z1ZGF3P1z+ON35iLatP84LvNteX4F1EMO2QAta2QbLRMAkw==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/density" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/elevation" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/floating-label" "^14.0.0"
    "@material/line-ripple" "^14.0.0"
    "@material/list" "^14.0.0"
    "@material/menu" "^14.0.0"
    "@material/menu-surface" "^14.0.0"
    "@material/notched-outline" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/shape" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/tokens" "^14.0.0"
    "@material/typography" "^14.0.0"
    tslib "^2.1.0"

"@material/shape@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/shape/-/shape-14.0.0.tgz"
  integrity sha512-o0mJB0+feOv473KckI8gFnUo8IQAaEA6ynXzw3VIYFjPi48pJwrxa0mZcJP/OoTXrCbDzDeFJfDPXEmRioBb9A==
  dependencies:
    "@material/feature-targeting" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@material/snackbar@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/snackbar/-/snackbar-14.0.0.tgz"
  integrity sha512-28uQBj9bw7BalNarK9j8/aVW4Ys5aRaGHoWH+CeYvAjqQUJkrYoqM52aiKhBwqrjBPMJHk1aXthe3YbzMBm6vA==
  dependencies:
    "@material/animation" "^14.0.0"
    "@material/base" "^14.0.0"
    "@material/button" "^14.0.0"
    "@material/dom" "^14.0.0"
    "@material/elevation" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/icon-button" "^14.0.0"
    "@material/ripple" "^14.0.0"
    "@material/rtl" "^14.0.0"
    "@material/shape" "^14.0.0"
    "@material/theme" "^14.0.0"
    "@material/typography" "^14.0.0"
    tslib "^2.1.0"

"@material/theme@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/theme/-/theme-14.0.0.tgz"
  integrity sha512-6/SENWNIFuXzeHMPHrYwbsXKgkvCtWuzzQ3cUu4UEt3KcQ5YpViazIM6h8ByYKZP8A9d8QpkJ0WGX5btGDcVoA==
  dependencies:
    "@material/feature-targeting" "^14.0.0"
    tslib "^2.1.0"

"@material/tokens@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/tokens/-/tokens-14.0.0.tgz"
  integrity sha512-SXgB9VwsKW4DFkHmJfDIS0x0cGdMWC1D06m6z/WQQ5P5j6/m0pKrbHVlrLzXcRjau+mFhXGvj/KyPo9Pp/Rc8Q==
  dependencies:
    "@material/elevation" "^14.0.0"

"@material/touch-target@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/touch-target/-/touch-target-14.0.0.tgz"
  integrity sha512-o3kvxmS4HkmZoQTvtzLJrqSG+ezYXkyINm3Uiwio1PTg67pDgK5FRwInkz0VNaWPcw9+5jqjUQGjuZMtjQMq8w==
  dependencies:
    "@material/base" "^14.0.0"
    "@material/feature-targeting" "^14.0.0"
    "@material/rtl" "^14.0.0"
    tslib "^2.1.0"

"@material/typography@^14.0.0":
  version "14.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@material/typography/-/typography-14.0.0.tgz"
  integrity sha512-/QtHBYiTR+TPMryM/CT386B2WlAQf/Ae32V324Z7P40gHLKY/YBXx7FDutAWZFeOerq/two4Nd2aAHBcMM2wMw==
  dependencies:
    "@material/feature-targeting" "^14.0.0"
    "@material/theme" "^14.0.0"
    tslib "^2.1.0"

"@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3":
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@msgpackr-extract/msgpackr-extract-darwin-arm64/-/msgpackr-extract-darwin-arm64-3.0.3.tgz#9edec61b22c3082018a79f6d1c30289ddf3d9d11"
  integrity sha512-QZHtlVgbAdy2zAqNA9Gu1UpIuI8Xvsd1v8ic6B2pZmeFnFcMWiPLfWXh7TVw4eGEZ/C9TH281KwhVoeQUKbyjw==

"@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3":
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@msgpackr-extract/msgpackr-extract-darwin-x64/-/msgpackr-extract-darwin-x64-3.0.3.tgz#33677a275204898ad8acbf62734fc4dc0b6a4855"
  integrity sha512-mdzd3AVzYKuUmiWOQ8GNhl64/IoFGol569zNRdkLReh6LRLHOXxU4U8eq0JwaD8iFHdVGqSy4IjFL4reoWCDFw==

"@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3":
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@msgpackr-extract/msgpackr-extract-linux-arm64/-/msgpackr-extract-linux-arm64-3.0.3.tgz#19edf7cdc2e7063ee328403c1d895a86dd28f4bb"
  integrity sha512-YxQL+ax0XqBJDZiKimS2XQaf+2wDGVa1enVRGzEvLLVFeqa5kx2bWbtcSXgsxjQB7nRqqIGFIcLteF/sHeVtQg==

"@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3":
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@msgpackr-extract/msgpackr-extract-linux-arm/-/msgpackr-extract-linux-arm-3.0.3.tgz#94fb0543ba2e28766c3fc439cabbe0440ae70159"
  integrity sha512-fg0uy/dG/nZEXfYilKoRe7yALaNmHoYeIoJuJ7KJ+YyU2bvY8vPv27f7UKhGRpY6euFYqEVhxCFZgAUNQBM3nw==

"@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3":
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@msgpackr-extract/msgpackr-extract-linux-x64/-/msgpackr-extract-linux-x64-3.0.3.tgz#4a0609ab5fe44d07c9c60a11e4484d3c38bbd6e3"
  integrity sha512-cvwNfbP07pKUfq1uH+S6KJ7dT9K8WOE4ZiAcsrSes+UY55E/0jLYc+vq+DO7jlmqRb5zAggExKm0H7O/CBaesg==

"@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3":
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@msgpackr-extract/msgpackr-extract-win32-x64/-/msgpackr-extract-win32-x64-3.0.3.tgz"
  integrity sha512-x0fWaQtYp4E6sktbsdAqnehxDgEc/VwM7uLsRCYWaiGu0ykYdZPiS8zCWdnjHwyiumousxfBm4SO31eXqwEZhQ==

"@ngtools/webpack@18.2.12":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@ngtools/webpack/-/webpack-18.2.12.tgz"
  integrity sha512-FFJAwtWbtpncMOVNuULPBwFJB7GSjiUwO93eGTzRp8O4EPQ8lCQeFbezQm/NP34+T0+GBLGzPSuQT+muob8YKw==

"@ngx-translate/core@^15.0.0":
  version "15.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@ngx-translate/core/-/core-15.0.0.tgz"
  integrity sha512-Am5uiuR0bOOxyoercDnAA3rJVizo4RRqJHo8N3RqJ+XfzVP/I845yEnMADykOHvM6HkVm4SZSnJBOiz0Anx5BA==

"@ngx-translate/http-loader@^8.0.0":
  version "8.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@ngx-translate/http-loader/-/http-loader-8.0.0.tgz"
  integrity sha512-SFMsdUcmHF5OdZkL1CHEoSAwbP5EbAOPTLLboOCRRoOg21P4GJx+51jxGdJeGve6LSKLf4Pay7BkTwmE6vxYlg==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@npmcli/agent@^2.0.0":
  version "2.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/agent/-/agent-2.2.2.tgz"
  integrity sha512-OrcNPXdpSl9UX7qPVRWbmWMCSXrcDa2M9DvrbOTj7ao1S4PlqVFYv9/yLKMkrJKZ/V5A/kDBC690or307i26Og==
  dependencies:
    agent-base "^7.1.0"
    http-proxy-agent "^7.0.0"
    https-proxy-agent "^7.0.1"
    lru-cache "^10.0.1"
    socks-proxy-agent "^8.0.3"

"@npmcli/fs@^3.1.0":
  version "3.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/fs/-/fs-3.1.1.tgz"
  integrity sha512-q9CRWjpHCMIh5sVyefoD1cA7PkvILqCZsnSOEUUivORLjxCO/Irmue2DprETiNgEqktDBZaM1Bi+jrarx1XdCg==
  dependencies:
    semver "^7.3.5"

"@npmcli/git@^5.0.0":
  version "5.0.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/git/-/git-5.0.8.tgz"
  integrity sha512-liASfw5cqhjNW9UFd+ruwwdEf/lbOAQjLL2XY2dFW/bkJheXDYZgOyul/4gVvEV4BWkTXjYGmDqMw9uegdbJNQ==
  dependencies:
    "@npmcli/promise-spawn" "^7.0.0"
    ini "^4.1.3"
    lru-cache "^10.0.1"
    npm-pick-manifest "^9.0.0"
    proc-log "^4.0.0"
    promise-inflight "^1.0.1"
    promise-retry "^2.0.1"
    semver "^7.3.5"
    which "^4.0.0"

"@npmcli/installed-package-contents@^2.0.1":
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/installed-package-contents/-/installed-package-contents-2.1.0.tgz"
  integrity sha512-c8UuGLeZpm69BryRykLuKRyKFZYJsZSCT4aVY5ds4omyZqJ172ApzgfKJ5eV/r3HgLdUYgFVe54KSFVjKoe27w==
  dependencies:
    npm-bundled "^3.0.0"
    npm-normalize-package-bin "^3.0.0"

"@npmcli/node-gyp@^3.0.0":
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/node-gyp/-/node-gyp-3.0.0.tgz"
  integrity sha512-gp8pRXC2oOxu0DUE1/M3bYtb1b3/DbJ5aM113+XJBgfXdussRAsX0YOrOhdd8WvnAR6auDBvJomGAkLKA5ydxA==

"@npmcli/package-json@^5.0.0", "@npmcli/package-json@^5.1.0":
  version "5.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/package-json/-/package-json-5.2.1.tgz"
  integrity sha512-f7zYC6kQautXHvNbLEWgD/uGu1+xCn9izgqBfgItWSx22U0ZDekxN08A1vM8cTxj/cRVe0Q94Ode+tdoYmIOOQ==
  dependencies:
    "@npmcli/git" "^5.0.0"
    glob "^10.2.2"
    hosted-git-info "^7.0.0"
    json-parse-even-better-errors "^3.0.0"
    normalize-package-data "^6.0.0"
    proc-log "^4.0.0"
    semver "^7.5.3"

"@npmcli/promise-spawn@^7.0.0":
  version "7.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/promise-spawn/-/promise-spawn-7.0.2.tgz"
  integrity sha512-xhfYPXoV5Dy4UkY0D+v2KkwvnDfiA/8Mt3sWCGI/hM03NsYIH8ZaG6QzS9x7pje5vHZBZJ2v6VRFVTWACnqcmQ==
  dependencies:
    which "^4.0.0"

"@npmcli/redact@^2.0.0":
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/redact/-/redact-2.0.1.tgz"
  integrity sha512-YgsR5jCQZhVmTJvjduTOIHph0L73pK8xwMVaDY0PatySqVM9AZj93jpoXYSJqfHFxFkN9dmqTw6OiqExsS3LPw==

"@npmcli/run-script@^8.0.0":
  version "8.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@npmcli/run-script/-/run-script-8.1.0.tgz"
  integrity sha512-y7efHHwghQfk28G2z3tlZ67pLG0XdfYbcVG26r7YIXALRsrVQcTq4/tdenSmdOrEsNahIYA/eh8aEVROWGFUDg==
  dependencies:
    "@npmcli/node-gyp" "^3.0.0"
    "@npmcli/package-json" "^5.0.0"
    "@npmcli/promise-spawn" "^7.0.0"
    node-gyp "^10.0.0"
    proc-log "^4.0.0"
    which "^4.0.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@rollup/rollup-android-arm-eabi@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.4.tgz#8b613b9725e8f9479d142970b106b6ae878610d5"
  integrity sha512-Fxamp4aEZnfPOcGA8KSNEohV8hX7zVHOemC8jVBoBUHu5zpJK/Eu3uJwt6BMgy9fkvzxDaurgj96F/NiLukF2w==

"@rollup/rollup-android-arm64@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.4.tgz#654ca1049189132ff602bfcf8df14c18da1f15fb"
  integrity sha512-VXoK5UMrgECLYaMuGuVTOx5kcuap1Jm8g/M83RnCHBKOqvPPmROFJGQaZhGccnsFtfXQ3XYa4/jMCJvZnbJBdA==

"@rollup/rollup-darwin-arm64@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.22.4.tgz#6d241d099d1518ef0c2205d96b3fa52e0fe1954b"
  integrity sha512-xMM9ORBqu81jyMKCDP+SZDhnX2QEVQzTcC6G18KlTQEzWK8r/oNZtKuZaCcHhnsa6fEeOBionoyl5JsAbE/36Q==

"@rollup/rollup-darwin-x64@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.22.4.tgz#42bd19d292a57ee11734c980c4650de26b457791"
  integrity sha512-aJJyYKQwbHuhTUrjWjxEvGnNNBCnmpHDvrb8JFDbeSH3m2XdHcxDd3jthAzvmoI8w/kSjd2y0udT+4okADsZIw==

"@rollup/rollup-linux-arm-gnueabihf@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.22.4.tgz#f23555ee3d8fe941c5c5fd458cd22b65eb1c2232"
  integrity sha512-j63YtCIRAzbO+gC2L9dWXRh5BFetsv0j0va0Wi9epXDgU/XUi5dJKo4USTttVyK7fGw2nPWK0PbAvyliz50SCQ==

"@rollup/rollup-linux-arm-musleabihf@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.22.4.tgz#f3bbd1ae2420f5539d40ac1fde2b38da67779baa"
  integrity sha512-dJnWUgwWBX1YBRsuKKMOlXCzh2Wu1mlHzv20TpqEsfdZLb3WoJW2kIEsGwLkroYf24IrPAvOT/ZQ2OYMV6vlrg==

"@rollup/rollup-linux-arm64-gnu@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.22.4.tgz#7abe900120113e08a1f90afb84c7c28774054d15"
  integrity sha512-AdPRoNi3NKVLolCN/Sp4F4N1d98c4SBnHMKoLuiG6RXgoZ4sllseuGioszumnPGmPM2O7qaAX/IJdeDU8f26Aw==

"@rollup/rollup-linux-arm64-musl@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.22.4.tgz#9e655285c8175cd44f57d6a1e8e5dedfbba1d820"
  integrity sha512-Gl0AxBtDg8uoAn5CCqQDMqAx22Wx22pjDOjBdmG0VIWX3qUBHzYmOKh8KXHL4UpogfJ14G4wk16EQogF+v8hmA==

"@rollup/rollup-linux-powerpc64le-gnu@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.22.4.tgz#9a79ae6c9e9d8fe83d49e2712ecf4302db5bef5e"
  integrity sha512-3aVCK9xfWW1oGQpTsYJJPF6bfpWfhbRnhdlyhak2ZiyFLDaayz0EP5j9V1RVLAAxlmWKTDfS9wyRyY3hvhPoOg==

"@rollup/rollup-linux-riscv64-gnu@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.22.4.tgz#67ac70eca4ace8e2942fabca95164e8874ab8128"
  integrity sha512-ePYIir6VYnhgv2C5Xe9u+ico4t8sZWXschR6fMgoPUK31yQu7hTEJb7bCqivHECwIClJfKgE7zYsh1qTP3WHUA==

"@rollup/rollup-linux-s390x-gnu@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.22.4.tgz#9f883a7440f51a22ed7f99e1d070bd84ea5005fc"
  integrity sha512-GqFJ9wLlbB9daxhVlrTe61vJtEY99/xB3C8e4ULVsVfflcpmR6c8UZXjtkMA6FhNONhj2eA5Tk9uAVw5orEs4Q==

"@rollup/rollup-linux-x64-gnu@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.22.4.tgz#70116ae6c577fe367f58559e2cffb5641a1dd9d0"
  integrity sha512-87v0ol2sH9GE3cLQLNEy0K/R0pz1nvg76o8M5nhMR0+Q+BBGLnb35P0fVz4CQxHYXaAOhE8HhlkaZfsdUOlHwg==

"@rollup/rollup-linux-x64-musl@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.22.4.tgz#f473f88219feb07b0b98b53a7923be716d1d182f"
  integrity sha512-UV6FZMUgePDZrFjrNGIWzDo/vABebuXBhJEqrHxrGiU6HikPy0Z3LfdtciIttEUQfuDdCn8fqh7wiFJjCNwO+g==

"@rollup/rollup-win32-arm64-msvc@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.22.4.tgz#4349482d17f5d1c58604d1c8900540d676f420e0"
  integrity sha512-BjI+NVVEGAXjGWYHz/vv0pBqfGoUH0IGZ0cICTn7kB9PyjrATSkX+8WkguNjWoj2qSr1im/+tTGRaY+4/PdcQw==

"@rollup/rollup-win32-ia32-msvc@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.22.4.tgz#a6fc39a15db618040ec3c2a24c1e26cb5f4d7422"
  integrity sha512-SiWG/1TuUdPvYmzmYnmd3IEifzR61Tragkbx9D3+R8mzQqDBz8v+BvZNDlkiTtI9T15KYZhP0ehn3Dld4n9J5g==

"@rollup/rollup-win32-x64-msvc@4.22.4":
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.22.4.tgz"
  integrity sha512-j8pPKp53/lq9lMXN57S8cFz0MynJk8OWNuUnXct/9KCpKU7DgU3bYMJhwWmcqC0UU29p8Lr0/7KEVcaM6bf47Q==

"@schematics/angular@18.2.12":
  version "18.2.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@schematics/angular/-/angular-18.2.12.tgz"
  integrity sha512-sIoeipsisK5eTLW3XuNZYcal83AfslBbgI7LnV+3VrXwpasKPGHwo2ZdwhCd2IXAkuJ02Iyu7MyV0aQRM9i/3g==
  dependencies:
    "@angular-devkit/core" "18.2.12"
    "@angular-devkit/schematics" "18.2.12"
    jsonc-parser "3.3.1"

"@sigstore/bundle@^2.3.2":
  version "2.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@sigstore/bundle/-/bundle-2.3.2.tgz"
  integrity sha512-wueKWDk70QixNLB363yHc2D2ItTgYiMTdPwK8D9dKQMR3ZQ0c35IxP5xnwQ8cNLoCgCRcHf14kE+CLIvNX1zmA==
  dependencies:
    "@sigstore/protobuf-specs" "^0.3.2"

"@sigstore/core@^1.0.0", "@sigstore/core@^1.1.0":
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@sigstore/core/-/core-1.1.0.tgz"
  integrity sha512-JzBqdVIyqm2FRQCulY6nbQzMpJJpSiJ8XXWMhtOX9eKgaXXpfNOF53lzQEjIydlStnd/eFtuC1dW4VYdD93oRg==

"@sigstore/protobuf-specs@^0.3.2":
  version "0.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@sigstore/protobuf-specs/-/protobuf-specs-0.3.2.tgz"
  integrity sha512-c6B0ehIWxMI8wiS/bj6rHMPqeFvngFV7cDU/MY+B16P9Z3Mp9k8L93eYZ7BYzSickzuqAQqAq0V956b3Ju6mLw==

"@sigstore/sign@^2.3.2":
  version "2.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@sigstore/sign/-/sign-2.3.2.tgz"
  integrity sha512-5Vz5dPVuunIIvC5vBb0APwo7qKA4G9yM48kPWJT+OEERs40md5GoUR1yedwpekWZ4m0Hhw44m6zU+ObsON+iDA==
  dependencies:
    "@sigstore/bundle" "^2.3.2"
    "@sigstore/core" "^1.0.0"
    "@sigstore/protobuf-specs" "^0.3.2"
    make-fetch-happen "^13.0.1"
    proc-log "^4.2.0"
    promise-retry "^2.0.1"

"@sigstore/tuf@^2.3.4":
  version "2.3.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@sigstore/tuf/-/tuf-2.3.4.tgz"
  integrity sha512-44vtsveTPUpqhm9NCrbU8CWLe3Vck2HO1PNLw7RIajbB7xhtn5RBPm1VNSCMwqGYHhDsBJG8gDF0q4lgydsJvw==
  dependencies:
    "@sigstore/protobuf-specs" "^0.3.2"
    tuf-js "^2.2.1"

"@sigstore/verify@^1.2.1":
  version "1.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@sigstore/verify/-/verify-1.2.1.tgz"
  integrity sha512-8iKx79/F73DKbGfRf7+t4dqrc0bRr0thdPrxAtCKWRm/F0tG71i6O1rvlnScncJLLBZHn3h8M3c1BSUAb9yu8g==
  dependencies:
    "@sigstore/bundle" "^2.3.2"
    "@sigstore/core" "^1.1.0"
    "@sigstore/protobuf-specs" "^0.3.2"

"@sindresorhus/merge-streams@^2.1.0":
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@sindresorhus/merge-streams/-/merge-streams-2.3.0.tgz"
  integrity sha512-LtoMMhxAlorcGhmFYI+LhPgbPZCkgP6ra1YL604EeF6U98pLlQ3iWIGMdWSC+vWmPBWBNgmDBAhnAobLROJmwg==

"@socket.io/component-emitter@~3.1.0":
  version "3.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz"
  integrity sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA==

"@spectrum-web-components/action-button@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/action-button/-/action-button-0.42.5.tgz"
  integrity sha512-kQG+fdZE0NN6ggso8rnYplPbZ+2pj7q2OmqqH1TpJKBDiQogGKr4Wp4GoODVlWAU4AyLs6DDUFZj7y0lj7lVvA==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"
    "@spectrum-web-components/button" "^0.42.5"
    "@spectrum-web-components/icon" "^0.42.5"
    "@spectrum-web-components/icons-ui" "^0.42.5"
    "@spectrum-web-components/shared" "^0.42.5"

"@spectrum-web-components/base@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/base/-/base-0.42.5.tgz"
  integrity sha512-0Xbn8HOVh1qLf90RGKpj49Bu0kMe2tu1bWmKAOM6JC5NgIefjXl+Fq5lxF3QRgHQ1l0ZjKjqBFGrMqBTptWlZw==
  dependencies:
    lit "^2.5.0 || ^3.1.3"

"@spectrum-web-components/button@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/button/-/button-0.42.5.tgz"
  integrity sha512-+mOOLd/GerTjkqfTQFXcqenD27cqdecQyuDacLyZ8Mx/CNJ9gUSD4cpkcUiItIUJsS2NytjTVWCa9ucQ89fD8Q==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"
    "@spectrum-web-components/clear-button" "^0.42.5"
    "@spectrum-web-components/close-button" "^0.42.5"
    "@spectrum-web-components/icon" "^0.42.5"
    "@spectrum-web-components/icons-ui" "^0.42.5"
    "@spectrum-web-components/progress-circle" "^0.42.5"
    "@spectrum-web-components/shared" "^0.42.5"

"@spectrum-web-components/clear-button@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/clear-button/-/clear-button-0.42.5.tgz"
  integrity sha512-5ikX6mviAfh40LpXKqBUvx4nH3bNU5bMIbicSaroJVC5t1x+p1Y/nUBppbl3t9unXvjcAqQiUCbmz0bVVIFPWQ==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"

"@spectrum-web-components/close-button@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/close-button/-/close-button-0.42.5.tgz"
  integrity sha512-sfq43FOTzxim8XlpK+Vad0v1qcIaLQEKMD/5oJ0pDGHIe34dtGYswpJA9Qe5g2ZYZQks29JIdWZGCX4YkVNUTw==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"

"@spectrum-web-components/icon@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/icon/-/icon-0.42.5.tgz"
  integrity sha512-7fECP297EgEu1XvBPLxfPRSuEM6dmkkEeuPpHXwhjbicqiOsAFVDLALB/LUghOhQBXI7Hd7Vl1eQiYYaVLKKfw==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"
    "@spectrum-web-components/iconset" "^0.42.5"

"@spectrum-web-components/icons-ui@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/icons-ui/-/icons-ui-0.42.5.tgz"
  integrity sha512-Jif910xJFC0JhmeCYWQUubI/C3kB2INHomNye2y4rWYatVE/ZMjJc4BLcs2RTRYRvt/2yJ7LUwi2SZi7q3eJmQ==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"
    "@spectrum-web-components/icon" "^0.42.5"
    "@spectrum-web-components/iconset" "^0.42.5"

"@spectrum-web-components/iconset@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/iconset/-/iconset-0.42.5.tgz"
  integrity sha512-jdmydViHhSXVStfpweFgTyU5bC9KmX1rC03eN4fyUI/E7JES22V+SMi2jvT71gb7MHswqsS7mlPyUBlcJGrz1Q==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"

"@spectrum-web-components/overlay@^0.42.3":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/overlay/-/overlay-0.42.5.tgz"
  integrity sha512-SFD0wyTUWglPW9Hth3pT17xQLdcggWOw8EkiYoOp0Eth6/LKCcJ0a63EFlzEXyY/DqgC24/KzE+mF2Kqcne01A==
  dependencies:
    "@floating-ui/dom" "^1.6.1"
    "@floating-ui/utils" "^0.2.1"
    "@spectrum-web-components/action-button" "^0.42.5"
    "@spectrum-web-components/base" "^0.42.5"
    "@spectrum-web-components/reactive-controllers" "^0.42.5"
    "@spectrum-web-components/shared" "^0.42.5"
    "@spectrum-web-components/theme" "^0.42.5"

"@spectrum-web-components/progress-circle@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/progress-circle/-/progress-circle-0.42.5.tgz"
  integrity sha512-XROdnKCYjkKP0Fzoa21zzAqr/xwJ6dyQRgBafw1aMpA8gMVlcW0JHz6ZPAukWTMV6szIfU3DjP0eLmMdMPqDwg==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"
    "@spectrum-web-components/shared" "^0.42.5"

"@spectrum-web-components/reactive-controllers@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/reactive-controllers/-/reactive-controllers-0.42.5.tgz"
  integrity sha512-xKg1rhGklcZ6TwYlSmtwCaLiITb6Tdl3vKkLEzCe4dfYDD0FO0kIUuI9uCVonTz6bVBTtEd1/taXJCSneUuDJg==
  dependencies:
    lit "^3.1.3"

"@spectrum-web-components/shared@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/shared/-/shared-0.42.5.tgz"
  integrity sha512-daqAnp1KBJ/j0P3I30Z1wuK6wu7p5Bij+0ofCdopo4DK4F3GntfUmRb6a9C4TNV+mWP4K+VxMtZ55aHuLG99Gw==
  dependencies:
    "@lit-labs/observers" "^2.0.2"
    "@spectrum-web-components/base" "^0.42.5"
    focus-visible "^5.1.0"

"@spectrum-web-components/styles@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/styles/-/styles-0.42.5.tgz"
  integrity sha512-e2ZFmMTAUMg4VZVR3CpqdeZoLhqu6SN+FiQE7bDspdOAE9V4vIM4nFHVq7FntxEZYjWge3LWqHdOT75BhYG9lQ==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"

"@spectrum-web-components/theme@^0.42.5":
  version "0.42.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@spectrum-web-components/theme/-/theme-0.42.5.tgz"
  integrity sha512-6E+j4RfHRlxwrK+t6Sy7az2Pw2N0AaYidcJbXcZZw/luYr1NtvNWMuJq6Jg5bbusTSSNSrAXDgsi4e09MVHuNg==
  dependencies:
    "@spectrum-web-components/base" "^0.42.5"
    "@spectrum-web-components/styles" "^0.42.5"

"@stencil/store@^2.0.10":
  version "2.0.16"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@stencil/store/-/store-2.0.16.tgz"
  integrity sha512-ET3EByKlmNyTA8O+tcp5YWePOiVnPIiuoiIaxTrf3zFFVo7JWVsVoak9IE0UTn3MkIM0ubR9lgxvi70uN588/A==

"@tufjs/canonical-json@2.0.0":
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@tufjs/canonical-json/-/canonical-json-2.0.0.tgz"
  integrity sha512-yVtV8zsdo8qFHe+/3kw81dSLyF7D576A5cCFCi4X7B39tWT7SekaEFUnvnWJHz+9qO7qJTah1JbrDjWKqFtdWA==

"@tufjs/models@2.0.1":
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@tufjs/models/-/models-2.0.1.tgz"
  integrity sha512-92F7/SFyufn4DXsha9+QfKnN03JGqtMFMXgSHbZOo8JG59WkTni7UzAouNQDf7AuP9OAMxVOPQcqG3sB7w+kkg==
  dependencies:
    "@tufjs/canonical-json" "2.0.0"
    minimatch "^9.0.4"

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/body-parser/-/body-parser-1.19.5.tgz"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.13":
  version "3.5.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/bonjour/-/bonjour-3.5.13.tgz"
  integrity sha512-z9fJ5Im06zvUL548KvYNecEVlA7cVDkGUi6kZusb04mpyEFKCIZJvloCcmpmLaIahDpOQGHaHmG6imtPMmPXGQ==
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.5.4":
  version "1.5.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.4.tgz"
  integrity sha512-n6Cr2xS1h4uAulPRdlw6Jl6s1oG8KrVilPN2yUITEs+K48EzMJJ3W1xy8K5eWuFvjp3R74AOIGSmp2UfBJ8HFw==
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/connect/-/connect-3.4.38.tgz"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/cookie@^0.4.1":
  version "0.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/cookie/-/cookie-0.4.1.tgz"
  integrity sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==

"@types/cors@^2.8.12":
  version "2.8.17"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/cors/-/cors-2.8.17.tgz"
  integrity sha512-8CGDvrBj1zgo2qE+oS3pOCyYNqCPryMWY2bGfwA0dcfopWGgxs+78df0Rs3rc9THP4JkOhLsAa+15VdpAqkcUA==
  dependencies:
    "@types/node" "*"

"@types/estree@1.0.5", "@types/estree@^1.0.5":
  version "1.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/estree/-/estree-1.0.5.tgz"
  integrity sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==

"@types/express-serve-static-core@*":
  version "5.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/express-serve-static-core/-/express-serve-static-core-5.0.1.tgz"
  integrity sha512-CRICJIl0N5cXDONAdlTv5ShATZ4HEwk6kDDIW2/w9qOWKg+NU/5F8wYRWCrONad0/UKkloNSmmyN/wX4rtpbVA==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express-serve-static-core@^4.17.33":
  version "4.19.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz"
  integrity sha512-N4LZ2xG7DatVqhCZzOGb1Yi5lMbXSZcmdLDe9EzSndPV2HpWYWzRbaerl2n27irrm94EPpprqa8KpskPT085+A==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.21":
  version "4.17.21"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/express/-/express-4.17.21.tgz"
  integrity sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/http-errors/-/http-errors-2.0.4.tgz"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/http-proxy@^1.17.15", "@types/http-proxy@^1.17.8":
  version "1.17.15"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/http-proxy/-/http-proxy-1.17.15.tgz"
  integrity sha512-25g5atgiVNTIv0LBDTg1H74Hvayx0ajtJPLLcYE3whFv75J0pWNtOBzaXJQgDTmrX1bx5U9YC2w/n65BN1HwRQ==
  dependencies:
    "@types/node" "*"

"@types/jasmine@~5.1.0":
  version "5.1.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/jasmine/-/jasmine-5.1.4.tgz"
  integrity sha512-px7OMFO/ncXxixDe1zR13V1iycqWae0MxTaw62RpFlksUi5QuNWgQJFkTQjIOvrmutJbI7Fp2Y2N1F6D2R4G6w==

"@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/mime@^1":
  version "1.3.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/mime/-/mime-1.3.5.tgz"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/mute-stream@^0.0.4":
  version "0.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/mute-stream/-/mute-stream-0.0.4.tgz"
  integrity sha512-CPM9nzrCPPJHQNA9keH9CVkVI+WR5kMa+7XEs5jcGQ0VoAGnLv242w8lIVgwAEfmE4oufJRaTc9PNLQl0ioAow==
  dependencies:
    "@types/node" "*"

"@types/node-forge@^1.3.0":
  version "1.3.11"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/node-forge/-/node-forge-1.3.11.tgz"
  integrity sha512-FQx220y22OKNTqaByeBGqHWYz4cl94tpcxeFdvBo3wjG6XPBuZ0BNgNZRV5J5TFmmcsJ4IzsLkmGRiQbnYsBEQ==
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@>=10.0.0", "@types/node@^22.5.5":
  version "22.9.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/node/-/node-22.9.0.tgz"
  integrity sha512-vuyHg81vvWA1Z1ELfvLko2c8f34gyA0zaic0+Rllc5lbCnbSyuvb2Oxpm6TAUAC/2xZN3QGqxBNggD1nNR2AfQ==
  dependencies:
    undici-types "~6.19.8"

"@types/prismjs@^1.26.4":
  version "1.26.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/prismjs/-/prismjs-1.26.5.tgz#72499abbb4c4ec9982446509d2f14fb8483869d6"
  integrity sha512-AUZTa7hQ2KY5L7AmtSiqxlhWxb4ina0yd8hNbl4TWuqnv/pFP0nDMb3YrfSBf4hJVGLh2YEIBfKaBW/9UEl6IQ==

"@types/qs@*":
  version "6.9.17"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/qs/-/qs-6.9.17.tgz"
  integrity sha512-rX4/bPcfmvxHDv0XjfJELTTr+iB+tn032nPILqHm5wbthUUUuVtNGGqzhya9XUxjTP8Fpr0qYgSZZKxGY++svQ==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/range-parser/-/range-parser-1.2.7.tgz"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/retry@0.12.2":
  version "0.12.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/retry/-/retry-0.12.2.tgz"
  integrity sha512-XISRgDJ2Tc5q4TRqvgJtzsRkFYNJzZrhTdtMoGVBttwzzQJkPnS3WWTFc7kuDRoPtPakl+T+OfdEUjYJj7Jbow==

"@types/send@*":
  version "0.17.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/send/-/send-0.17.4.tgz"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.4":
  version "1.9.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/serve-index/-/serve-index-1.9.4.tgz"
  integrity sha512-qLpGZ/c2fhSs5gnYsQxtDEq3Oy8SXPClIXkW5ghvAvsNuVSA8k+gCONcUCS/UjLEYvYps+e8uBtfgXgvhwfNug==
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.15.5":
  version "1.15.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/serve-static/-/serve-static-1.15.7.tgz"
  integrity sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/sockjs@^0.3.36":
  version "0.3.36"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/sockjs/-/sockjs-0.3.36.tgz"
  integrity sha512-MK9V6NzAS1+Ud7JV9lJLFqW85VbC9dq3LmwZCuBe4wBDgKC0Kj/jd8Xl+nSviU+Qc3+m7umHHyHg//2KSa0a0Q==
  dependencies:
    "@types/node" "*"

"@types/trusted-types@^2.0.2":
  version "2.0.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/trusted-types/-/trusted-types-2.0.7.tgz"
  integrity sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==

"@types/wrap-ansi@^3.0.0":
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/wrap-ansi/-/wrap-ansi-3.0.0.tgz"
  integrity sha512-ltIpx+kM7g/MLRZfkbL7EsCEjfzCcScLpkg37eXEtx5kmrAKBkTJwd1GIAjDSL8wTpM6Hzn5YO4pSb91BEwu1g==

"@types/ws@^8.5.10":
  version "8.5.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@types/ws/-/ws-8.5.13.tgz"
  integrity sha512-osM/gWBTPKgHV8XkTunnegTRIsvF6owmf5w+JtAfOw472dptdm0dlGv4xCt6GwQRcC2XVOvvRE/0bAoQcL2QkA==
  dependencies:
    "@types/node" "*"

"@vitejs/plugin-basic-ssl@1.1.0":
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@vitejs/plugin-basic-ssl/-/plugin-basic-ssl-1.1.0.tgz"
  integrity sha512-wO4Dk/rm8u7RNhOf95ZzcEmC9rYOncYgvq4z3duaJrCgjN8BxAnDVyndanfcJZ0O6XZzHz6Q0hTimxTg8Y9g/A==

"@webassemblyjs/ast@1.14.1", "@webassemblyjs/ast@^1.12.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/ast/-/ast-1.14.1.tgz"
  integrity sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  version "1.13.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz"
  integrity sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==

"@webassemblyjs/helper-api-error@1.13.2":
  version "1.13.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz"
  integrity sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==

"@webassemblyjs/helper-buffer@1.14.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz"
  integrity sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==

"@webassemblyjs/helper-numbers@1.13.2":
  version "1.13.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz"
  integrity sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  version "1.13.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz"
  integrity sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==

"@webassemblyjs/helper-wasm-section@1.14.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz"
  integrity sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  version "1.13.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz"
  integrity sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  version "1.13.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/leb128/-/leb128-1.13.2.tgz"
  integrity sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  version "1.13.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/utf8/-/utf8-1.13.2.tgz"
  integrity sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==

"@webassemblyjs/wasm-edit@^1.12.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz"
  integrity sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz"
  integrity sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz"
  integrity sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@1.14.1", "@webassemblyjs/wasm-parser@^1.12.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz"
  integrity sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  version "1.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz"
  integrity sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

"@yarnpkg/lockfile@1.1.0":
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/@yarnpkg/lockfile/-/lockfile-1.1.0.tgz"
  integrity sha512-GpSwvyXOcOOlV70vbnzjj4fW5xW/FdUF6nQEt1ENy7m4ZCczi1+/buVUPAqmGfqznsORNFzUMjctTIp8a9tuCQ==

abbrev@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/abbrev/-/abbrev-2.0.0.tgz"
  integrity sha512-6/mh1E2u2YgEsCHdY0Yx5oW+61gZU+1vXaoiHHrpKeuRNNgFvS+/jrwHiQhB5apAf5oB7UB7E19ol2R2LKH8hQ==

accepts@~1.3.4, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-attributes@^1.9.5:
  version "1.9.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/acorn-import-attributes/-/acorn-import-attributes-1.9.5.tgz"
  integrity sha512-n02Vykv5uA3eHGM/Z2dQrcD56kL8TyDb2p1+0P83PClMnC/nc+anbQRhIOWnSq4Ke/KvDPrY3C9hDtC/A3eHnQ==

acorn@^8.7.1, acorn@^8.8.2:
  version "8.14.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/acorn/-/acorn-8.14.0.tgz"
  integrity sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==

adjust-sourcemap-loader@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz"
  integrity sha512-OXwN5b9pCUXNQHJpwwD2qP40byEmSgzj8B4ydSN0uMNYWiFmJ6x6KwUllMmfk8Rwu/HJDFR7U8ubsWBoN0Xp0A==
  dependencies:
    loader-utils "^2.0.0"
    regex-parser "^2.2.11"

agent-base@^7.0.2, agent-base@^7.1.0, agent-base@^7.1.1:
  version "7.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/agent-base/-/agent-base-7.1.1.tgz"
  integrity sha512-H0TSyFNDMomMNJQBn8wFV5YC/2eJ+VXECwOadZJT554xP6cODZHPX3H9QMQECxvrgiSOP1pHjy1sMWQVYJOUOA==
  dependencies:
    debug "^4.3.4"

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/aggregate-error/-/aggregate-error-3.1.0.tgz"
  integrity sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-formats@3.0.1:
  version "3.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ajv-formats/-/ajv-formats-3.0.1.tgz"
  integrity sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==
  dependencies:
    ajv "^8.0.0"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ajv-formats/-/ajv-formats-2.1.1.tgz"
  integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==

ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  integrity sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@8.17.1, ajv@^8.0.0, ajv@^8.9.0:
  version "8.17.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ajv/-/ajv-8.17.1.tgz"
  integrity sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ajv@^6.12.5:
  version "6.12.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

angular-auth-oidc-client@18.0.1:
  version "18.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/angular-auth-oidc-client/-/angular-auth-oidc-client-18.0.1.tgz"
  integrity sha512-r+PWZuni5msVEKFyA8HQ1lTTbSrrrsgFnU3qGK6P3TMl6+G3d2KdsXztBaRCNFU4oZq8mbDnmwShyvvVYSvxig==
  dependencies:
    rfc4648 "^1.5.0"
    tslib "^2.3.0"

ansi-colors@4.1.3:
  version "4.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-colors/-/ansi-colors-4.1.3.tgz"
  integrity sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==

ansi-escapes@^4.3.2:
  version "4.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-escapes@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-escapes/-/ansi-escapes-7.0.0.tgz"
  integrity sha512-GdYO7a61mR0fOlAsvC9/rIHf7L96sBc6dEWzeOu+KAea5bZyQRPIpojrVoI4AXGJS/ycu/fBTdLrUkA4ODrvjw==
  dependencies:
    environment "^1.0.0"

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  integrity sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.0.0, ansi-styles@^6.1.0, ansi-styles@^6.2.1:
  version "6.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==

autoprefixer@10.4.20:
  version "10.4.20"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/autoprefixer/-/autoprefixer-10.4.20.tgz"
  integrity sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==
  dependencies:
    browserslist "^4.23.3"
    caniuse-lite "^1.0.30001646"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.1"
    postcss-value-parser "^4.2.0"

babel-loader@9.1.3:
  version "9.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/babel-loader/-/babel-loader-9.1.3.tgz"
  integrity sha512-xG3ST4DglodGf8qSwv0MdeWLhrDsw/32QMdTO5T1ZIp9gQur0HkCyFs7Awskr10JKXFXwpAhiCuYX5oGXnRGbw==
  dependencies:
    find-cache-dir "^4.0.0"
    schema-utils "^4.0.0"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.12"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.12.tgz"
  integrity sha512-CPWT6BwvhrTO2d8QVorhTCQw9Y43zOu7G9HigcfxvepOU6b8o3tcWad6oVgZIsZCTt42FFv97aA7ZJsbM4+8og==
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.10.1, babel-plugin-polyfill-corejs3@^0.10.4:
  version "0.10.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.6.tgz"
  integrity sha512-b37+KR2i/khY5sKmWNVQAnitvquQbNdWy6lJdsr0kmquCKEEUgMKK4SboVM3HtfnZilfjr4MMQ7vY58FVWDtIA==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    core-js-compat "^3.38.0"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.3.tgz"
  integrity sha512-LiWSbl4CRSIa5x/JAU6jZiG9eit9w6mz+yVMFwDE83LAWvt0AfGBoZ7HS/mkhrKuh2ZlzfVZYKoLjXdqw6Yt7Q==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

base64id@2.0.0, base64id@~2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/base64id/-/base64id-2.0.0.tgz"
  integrity sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==

batch@0.6.1:
  version "0.6.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/batch/-/batch-0.6.1.tgz"
  integrity sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/big.js/-/big.js-5.2.2.tgz"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

bl@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/bl/-/bl-4.1.0.tgz"
  integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

body-parser@1.20.3, body-parser@^1.19.0:
  version "1.20.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/body-parser/-/body-parser-1.20.3.tgz"
  integrity sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.13.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour-service@^1.2.1:
  version "1.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/bonjour-service/-/bonjour-service-1.2.1.tgz"
  integrity sha512-oSzCS2zV14bh2kji6vNe7vrpJYCHGvcZnlffFQ1MEoX/WOeQ/teD8SYWKR942OI3INjq8OMNJlbPK5LLLUxFDw==
  dependencies:
    fast-deep-equal "^3.1.3"
    multicast-dns "^7.2.5"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/boolbase/-/boolbase-1.0.0.tgz"
  integrity sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.21.10, browserslist@^4.21.5, browserslist@^4.23.0, browserslist@^4.23.3, browserslist@^4.24.0, browserslist@^4.24.2:
  version "4.24.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/browserslist/-/browserslist-4.24.2.tgz"
  integrity sha512-ZIc+Q62revdMcqC6aChtW4jz3My3klmCO1fEmINZY/8J3EpBg5/A/D0AKmBveUh6pgoeycoMkVMko84tuYS+Gg==
  dependencies:
    caniuse-lite "^1.0.30001669"
    electron-to-chromium "^1.5.41"
    node-releases "^2.0.18"
    update-browserslist-db "^1.1.1"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

bundle-name@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/bundle-name/-/bundle-name-4.1.0.tgz"
  integrity sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q==
  dependencies:
    run-applescript "^7.0.0"

bytes@3.1.2:
  version "3.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cacache@^18.0.0:
  version "18.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cacache/-/cacache-18.0.4.tgz"
  integrity sha512-B+L5iIa9mgcjLbliir2th36yEwPftrzteHYujzsx3dFP/31GCHcIeS8f5MGd80odLOjaOvSpU3EEAmRQptkxLQ==
  dependencies:
    "@npmcli/fs" "^3.1.0"
    fs-minipass "^3.0.0"
    glob "^10.2.2"
    lru-cache "^10.0.1"
    minipass "^7.0.3"
    minipass-collect "^2.0.1"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    p-map "^4.0.0"
    ssri "^10.0.0"
    tar "^6.1.11"
    unique-filename "^3.0.0"

call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/call-bind/-/call-bind-1.0.7.tgz"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

caniuse-lite@^1.0.30001646, caniuse-lite@^1.0.30001669:
  version "1.0.30001680"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/caniuse-lite/-/caniuse-lite-1.0.30001680.tgz"
  integrity sha512-rPQy70G6AGUMnbwS1z6Xg+RkHYPAi18ihs47GH0jcxIG7wArmPgY3XbS2sRdBbxJljp3thdT8BIqv9ccCypiPA==

chalk@^4.1.0:
  version "4.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/chardet/-/chardet-0.7.0.tgz"
  integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==

"chokidar@>=3.0.0 <4.0.0", chokidar@^3.5.1, chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.0:
  version "4.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/chokidar/-/chokidar-4.0.1.tgz"
  integrity sha512-n8enUVCED/KVRQlab1hr3MVpcVMvxtZjmEa956u+4YijlmQED223XMSYj2tLuKvr4jcCTzNNMpQDUer72MMmzA==
  dependencies:
    readdirp "^4.0.1"

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

chrome-trace-event@^1.0.2:
  version "1.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  integrity sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/clean-stack/-/clean-stack-2.2.0.tgz"
  integrity sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
  dependencies:
    restore-cursor "^3.1.0"

cli-cursor@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cli-cursor/-/cli-cursor-5.0.0.tgz"
  integrity sha512-aCj4O5wKyszjMmDT4tZj93kxyydN/K5zPWSCe6/0AV/AA1pqe5ZBIw0a2ZfPQV7lL5/yb5HsUreJ6UFAF1tEQw==
  dependencies:
    restore-cursor "^5.0.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==

cli-truncate@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cli-truncate/-/cli-truncate-4.0.0.tgz"
  integrity sha512-nPdaFdQ0h/GEigbPClz11D0v/ZJEwxmeVZGeMo3Z5StPtUTkA9o1lD6QwoirYiSDzbcwn2XcjwmCp68W1IS4TA==
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^7.0.0"

cli-width@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cli-width/-/cli-width-4.1.0.tgz"
  integrity sha512-ouuZd4/dm2Sw5Gmqy6bGyNNNe1qt9RpmxveLSO7KcgsTnU7RXfsw+/bukWGo1abgBiMAic068rclZsO4IWmmxQ==

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cliui/-/cliui-7.0.4.tgz"
  integrity sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cliui/-/cliui-8.0.1.tgz"
  integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/clone-deep/-/clone-deep-4.0.1.tgz"
  integrity sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

colorette@^2.0.10, colorette@^2.0.20:
  version "2.0.20"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/colorette/-/colorette-2.0.20.tgz"
  integrity sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==

commander@^2.20.0:
  version "2.20.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

common-path-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/common-path-prefix/-/common-path-prefix-3.0.0.tgz"
  integrity sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w==

compressible@~2.0.18:
  version "2.0.18"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/compressible/-/compressible-2.0.18.tgz"
  integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/compression/-/compression-1.7.5.tgz"
  integrity sha512-bQJ0YRck5ak3LgtnpKkiabX5pNF7tMUh1BSy2ZBOTh0Dim0BUu6aPPwByIns6/A5Prh8PufSPerMDUklpzes2Q==
  dependencies:
    bytes "3.1.2"
    compressible "~2.0.18"
    debug "2.6.9"
    negotiator "~0.6.4"
    on-headers "~1.0.2"
    safe-buffer "5.2.1"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

connect-history-api-fallback@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz"
  integrity sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==

connect@^3.7.0:
  version "3.7.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/connect/-/connect-3.7.0.tgz"
  integrity sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ==
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.2"
    parseurl "~1.3.3"
    utils-merge "1.0.1"

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/content-type/-/content-type-1.0.5.tgz"
  integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==

convert-source-map@^1.5.1, convert-source-map@^1.7.0:
  version "1.9.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==

cookie@0.7.1:
  version "0.7.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cookie/-/cookie-0.7.1.tgz"
  integrity sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==

cookie@~0.7.2:
  version "0.7.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cookie/-/cookie-0.7.2.tgz"
  integrity sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==

copy-anything@^2.0.1:
  version "2.0.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/copy-anything/-/copy-anything-2.0.6.tgz"
  integrity sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==
  dependencies:
    is-what "^3.14.1"

copy-webpack-plugin@12.0.2:
  version "12.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/copy-webpack-plugin/-/copy-webpack-plugin-12.0.2.tgz"
  integrity sha512-SNwdBeHyII+rWvee/bTnAYyO8vfVdcSTud4EIb6jcZ8inLeWucJE0DnxXQBjlQ5zlteuuvooGQy3LIyGxhvlOA==
  dependencies:
    fast-glob "^3.3.2"
    glob-parent "^6.0.1"
    globby "^14.0.0"
    normalize-path "^3.0.0"
    schema-utils "^4.2.0"
    serialize-javascript "^6.0.2"

core-js-compat@^3.37.1, core-js-compat@^3.38.0:
  version "3.39.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/core-js-compat/-/core-js-compat-3.39.0.tgz"
  integrity sha512-VgEUx3VwlExr5no0tXlBt+silBvhTryPwCXRI2Id1PN8WTKu7MreethvddqOubrYxkFdv/RnYrqlv1sFNAUelw==
  dependencies:
    browserslist "^4.24.2"

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cors@~2.8.5:
  version "2.8.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cors/-/cors-2.8.5.tgz"
  integrity sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==
  dependencies:
    object-assign "^4"
    vary "^1"

cosmiconfig@^9.0.0:
  version "9.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cosmiconfig/-/cosmiconfig-9.0.0.tgz"
  integrity sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg==
  dependencies:
    env-paths "^2.2.1"
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"

critters@0.0.24:
  version "0.0.24"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/critters/-/critters-0.0.24.tgz"
  integrity sha512-Oyqew0FGM0wYUSNqR0L6AteO5MpMoUU0rhKRieXeiKs+PmRTxiJMyaunYB2KF6fQ3dzChXKCpbFOEJx3OQ1v/Q==
  dependencies:
    chalk "^4.1.0"
    css-select "^5.1.0"
    dom-serializer "^2.0.0"
    domhandler "^5.0.2"
    htmlparser2 "^8.0.2"
    postcss "^8.4.23"
    postcss-media-query-parser "^0.2.3"

cross-spawn@^7.0.0, cross-spawn@^7.0.3:
  version "7.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cross-spawn/-/cross-spawn-7.0.5.tgz"
  integrity sha512-ZVJrKKYunU38/76t0RMOulHOnUcbU9GbpWKAOZ0mhjr7CX6FVrH+4FrAapSOekrgFQ3f/8gwMEuIft0aKq6Hug==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-loader@7.1.2:
  version "7.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/css-loader/-/css-loader-7.1.2.tgz"
  integrity sha512-6WvYYn7l/XEGN8Xu2vWFt9nVzrCn39vKyTEFf/ExEyoksJjjSZV/0/35XPlMbpnr6VGhZIUg5yJrL8tGfes/FA==
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.33"
    postcss-modules-extract-imports "^3.1.0"
    postcss-modules-local-by-default "^4.0.5"
    postcss-modules-scope "^3.2.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.5.4"

css-select@^5.1.0:
  version "5.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/css-select/-/css-select-5.1.0.tgz"
  integrity sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-what@^6.1.0:
  version "6.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/css-what/-/css-what-6.1.0.tgz"
  integrity sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

custom-event@~1.0.0:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/custom-event/-/custom-event-1.0.1.tgz"
  integrity sha512-GAj5FOq0Hd+RsCGVJxZuKaIDXDf3h6GQoNEjFgbLLI/trgtavwUbSnZ5pVfg27DVCaWjIohryS0JFwIJyT2cMg==

date-fns-tz@^1.3.0:
  version "1.3.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/date-fns-tz/-/date-fns-tz-1.3.8.tgz"
  integrity sha512-qwNXUFtMHTTU6CFSFjoJ80W8Fzzp24LntbjFFBgL/faqds4e5mo9mftoRLgr3Vi1trISsg4awSpYVsOQCRnapQ==

date-fns@^2.28.0:
  version "2.30.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/date-fns/-/date-fns-2.30.0.tgz"
  integrity sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==
  dependencies:
    "@babel/runtime" "^7.21.0"

date-format@^4.0.14:
  version "4.0.14"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/date-format/-/date-format-4.0.14.tgz"
  integrity sha512-39BOQLs9ZjKh0/patS9nrT8wc3ioX3/eA/zgbKNopnF2wCqJEoxywwwElATYvRsXdnOxA/OQeQoFZ3rFjVajhg==

debug@2.6.9:
  version "2.6.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@4, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.4, debug@^4.3.6, debug@~4.3.1, debug@~4.3.2, debug@~4.3.4:
  version "4.3.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/debug/-/debug-4.3.7.tgz"
  integrity sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==
  dependencies:
    ms "^2.1.3"

default-browser-id@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/default-browser-id/-/default-browser-id-5.0.0.tgz"
  integrity sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA==

default-browser@^5.2.1:
  version "5.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/default-browser/-/default-browser-5.2.1.tgz"
  integrity sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg==
  dependencies:
    bundle-name "^4.1.0"
    default-browser-id "^5.0.0"

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/default-gateway/-/default-gateway-6.0.3.tgz"
  integrity sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==
  dependencies:
    execa "^5.0.0"

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/defaults/-/defaults-1.0.4.tgz"
  integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
  dependencies:
    clone "^1.0.2"

define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz"
  integrity sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg==

depd@2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

depd@~1.1.2:
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/depd/-/depd-1.1.2.tgz"
  integrity sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==

destroy@1.2.0:
  version "1.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detect-libc@^2.0.1:
  version "2.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/detect-libc/-/detect-libc-2.0.3.tgz"
  integrity sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/detect-node/-/detect-node-2.1.0.tgz"
  integrity sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==

di@^0.0.1:
  version "0.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/di/-/di-0.0.1.tgz"
  integrity sha512-uJaamHkagcZtHPqCIHZxnFrXlunQXgBOsZSUOWwFw31QJCAbyTBoHMW75YOTur5ZNx8pIeAKgf6GWIgaqqiLhA==

dns-packet@^5.2.2:
  version "5.6.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/dns-packet/-/dns-packet-5.6.1.tgz"
  integrity sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

dom-serialize@^2.2.1:
  version "2.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/dom-serialize/-/dom-serialize-2.2.1.tgz"
  integrity sha512-Yra4DbvoW7/Z6LBN560ZwXMjoNOSAN2wRsKFGc4iBeso+mpIA6qj1vfdf9HpMaKAqG6wXTy+1SYEzmNpKXOSsQ==
  dependencies:
    custom-event "~1.0.0"
    ent "~2.2.0"
    extend "^3.0.0"
    void-elements "^2.0.0"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/dom-serializer/-/dom-serializer-2.0.0.tgz"
  integrity sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/domelementtype/-/domelementtype-2.3.0.tgz"
  integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/domhandler/-/domhandler-5.0.3.tgz"
  integrity sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==
  dependencies:
    domelementtype "^2.3.0"

domutils@^3.0.1:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/domutils/-/domutils-3.1.0.tgz"
  integrity sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==

electron-to-chromium@^1.5.41:
  version "1.5.62"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/electron-to-chromium/-/electron-to-chromium-1.5.62.tgz"
  integrity sha512-t8c+zLmJHa9dJy96yBZRXGQYoiCEnHYgFwn1asvSPZSUdVxnB62A4RASd7k41ytG3ErFBA0TpHlKg9D9SQBmLg==

emoji-regex@^10.3.0:
  version "10.4.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/emoji-regex/-/emoji-regex-10.4.0.tgz"
  integrity sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==

encodeurl@~2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/encodeurl/-/encodeurl-2.0.0.tgz"
  integrity sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==

encoding@^0.1.13:
  version "0.1.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/encoding/-/encoding-0.1.13.tgz"
  integrity sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==
  dependencies:
    iconv-lite "^0.6.2"

engine.io-parser@~5.2.1:
  version "5.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/engine.io-parser/-/engine.io-parser-5.2.3.tgz"
  integrity sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q==

engine.io@~6.6.0:
  version "6.6.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/engine.io/-/engine.io-6.6.2.tgz"
  integrity sha512-gmNvsYi9C8iErnZdVcJnvCpSKbWTt1E8+JZo8b+daLninywUWi5NQ5STSHZ9rFjFO7imNcvb8Pc5pe/wMR5xEw==
  dependencies:
    "@types/cookie" "^0.4.1"
    "@types/cors" "^2.8.12"
    "@types/node" ">=10.0.0"
    accepts "~1.3.4"
    base64id "2.0.0"
    cookie "~0.7.2"
    cors "~2.8.5"
    debug "~4.3.1"
    engine.io-parser "~5.2.1"
    ws "~8.17.1"

enhanced-resolve@^5.17.1:
  version "5.17.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/enhanced-resolve/-/enhanced-resolve-5.17.1.tgz"
  integrity sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

ent@~2.2.0:
  version "2.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ent/-/ent-2.2.1.tgz"
  integrity sha512-QHuXVeZx9d+tIQAz/XztU0ZwZf2Agg9CcXcgE1rurqvdBeDBrpSwjl8/6XUqMg7tw2Y7uAdKb2sRv+bSEFqQ5A==
  dependencies:
    punycode "^1.4.1"

entities@^4.2.0, entities@^4.3.0, entities@^4.4.0, entities@^4.5.0:
  version "4.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

env-paths@^2.2.0, env-paths@^2.2.1:
  version "2.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/env-paths/-/env-paths-2.2.1.tgz"
  integrity sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==

environment@^1.0.0:
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/environment/-/environment-1.1.0.tgz"
  integrity sha512-xUtoPkMggbz0MPyPiIWr1Kp4aeWJjDZ6SMvURhimjdZgsRuDplF5/s9hcgGhyXMhs+6vpnuoiZ2kFiu3FMnS8Q==

err-code@^2.0.2:
  version "2.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/err-code/-/err-code-2.0.3.tgz"
  integrity sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==

errno@^0.1.1:
  version "0.1.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/errno/-/errno-0.1.8.tgz"
  integrity sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/es-define-property/-/es-define-property-1.0.0.tgz"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-module-lexer@^1.2.1:
  version "1.5.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/es-module-lexer/-/es-module-lexer-1.5.4.tgz"
  integrity sha512-MVNK56NiMrOwitFB7cqDwq0CQutbw+0BvLshJSse0MUNU+y1FC3bUS/AQg7oUng+/wKrrki7JfmwtVHkVfPLlw==

esbuild-wasm@0.23.0:
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/esbuild-wasm/-/esbuild-wasm-0.23.0.tgz"
  integrity sha512-6jP8UmWy6R6TUUV8bMuC3ZyZ6lZKI56x0tkxyCIqWwRRJ/DgeQKneh/Oid5EoGoPFLrGNkz47ZEtWAYuiY/u9g==

esbuild@0.23.0:
  version "0.23.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/esbuild/-/esbuild-0.23.0.tgz"
  integrity sha512-1lvV17H2bMYda/WaFb2jLPeHU3zml2k4/yagNMG8Q/YtfMjCwEUZa2eXXMgZTVSL5q1n4H7sQ0X6CdJDqqeCFA==
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.23.0"
    "@esbuild/android-arm" "0.23.0"
    "@esbuild/android-arm64" "0.23.0"
    "@esbuild/android-x64" "0.23.0"
    "@esbuild/darwin-arm64" "0.23.0"
    "@esbuild/darwin-x64" "0.23.0"
    "@esbuild/freebsd-arm64" "0.23.0"
    "@esbuild/freebsd-x64" "0.23.0"
    "@esbuild/linux-arm" "0.23.0"
    "@esbuild/linux-arm64" "0.23.0"
    "@esbuild/linux-ia32" "0.23.0"
    "@esbuild/linux-loong64" "0.23.0"
    "@esbuild/linux-mips64el" "0.23.0"
    "@esbuild/linux-ppc64" "0.23.0"
    "@esbuild/linux-riscv64" "0.23.0"
    "@esbuild/linux-s390x" "0.23.0"
    "@esbuild/linux-x64" "0.23.0"
    "@esbuild/netbsd-x64" "0.23.0"
    "@esbuild/openbsd-arm64" "0.23.0"
    "@esbuild/openbsd-x64" "0.23.0"
    "@esbuild/sunos-x64" "0.23.0"
    "@esbuild/win32-arm64" "0.23.0"
    "@esbuild/win32-ia32" "0.23.0"
    "@esbuild/win32-x64" "0.23.0"

esbuild@^0.21.3:
  version "0.21.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/esbuild/-/esbuild-0.21.5.tgz"
  integrity sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/etag/-/etag-1.8.1.tgz"
  integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/eventemitter3/-/eventemitter3-5.0.1.tgz"
  integrity sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==

events@^3.2.0:
  version "3.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^5.0.0:
  version "5.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exponential-backoff@^3.1.1:
  version "3.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/exponential-backoff/-/exponential-backoff-3.1.1.tgz"
  integrity sha512-dX7e/LHVJ6W3DE1MHWi9S1EYzDESENfLrYohG2G++ovZrYOkm4Knwa0mc1cn84xJOR4KEU0WSchhLbd0UklbHw==

express@^4.17.3:
  version "4.21.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/express/-/express-4.21.1.tgz"
  integrity sha512-YSFlK1Ee0/GC8QaO91tHcDxJiE/X4FbpAyQWkxAvG6AXCuR65YzK8ua6D9hvi/TzUfZMpc+BwuM1IPw8fmQBiQ==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.3"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.7.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.3.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.3"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.10"
    proxy-addr "~2.0.7"
    qs "6.13.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.19.0"
    serve-static "1.16.2"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend@^3.0.0:
  version "3.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/extend/-/extend-3.0.2.tgz"
  integrity sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==

external-editor@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@3.3.2, fast-glob@^3.3.2:
  version "3.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-uri@^3.0.1:
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fast-uri/-/fast-uri-3.0.3.tgz"
  integrity sha512-aLrHthzCjH5He4Z2H9YZ+v6Ujb9ocRuW6ZzkJQOrTxleEijANq4v1TsaPaVG1PZcuurEzrLcWRyYBYXD5cEiaw==

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fastq/-/fastq-1.17.1.tgz"
  integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/faye-websocket/-/faye-websocket-0.11.4.tgz"
  integrity sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==
  dependencies:
    websocket-driver ">=0.5.1"

filesize@^10.0.6:
  version "10.1.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/filesize/-/filesize-10.1.6.tgz"
  integrity sha512-sJslQKU2uM33qH5nqewAwVB2QgR6w1aMNsYUp3aN5rMRyXEwJGmZvaWzeJFNTOXWlHQyBFCWrdj3fV/fsTOX8w==

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.1.2:
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/finalhandler/-/finalhandler-1.1.2.tgz"
  integrity sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

finalhandler@1.3.1:
  version "1.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/finalhandler/-/finalhandler-1.3.1.tgz"
  integrity sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==
  dependencies:
    debug "2.6.9"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/find-cache-dir/-/find-cache-dir-4.0.0.tgz"
  integrity sha512-9ZonPT4ZAK4a+1pUPVPZJapbi7O5qbbJPdYw/NOQWZZbVLdDTYM3A4R9z/DpAM08IDaFGsvPgiGZ82WEwUDWjg==
  dependencies:
    common-path-prefix "^3.0.0"
    pkg-dir "^7.0.0"

find-up@^6.3.0:
  version "6.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/find-up/-/find-up-6.3.0.tgz"
  integrity sha512-v2ZsoEuVHYy8ZIlYqwPe/39Cy+cFDzp4dXPaxNvkEuouymu+2Jbz0PxpKarJHYJTmv2HWT3O382qY8l4jMWthw==
  dependencies:
    locate-path "^7.1.0"
    path-exists "^5.0.0"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/flat/-/flat-5.0.2.tgz"
  integrity sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==

flatted@^3.2.7:
  version "3.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/flatted/-/flatted-3.3.1.tgz"
  integrity sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==

focus-visible@^5.1.0:
  version "5.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/focus-visible/-/focus-visible-5.2.1.tgz"
  integrity sha512-8Bx950VD1bWTQJEH/AM6SpEk+SU55aVnp4Ujhuuxy3eMEBCRwBnTBnVXr9YAPvZL3/CNjCa8u4IWfNmEO53whA==

follow-redirects@^1.0.0:
  version "1.15.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/follow-redirects/-/follow-redirects-1.15.9.tgz"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

foreground-child@^3.1.0:
  version "3.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/foreground-child/-/foreground-child-3.3.0.tgz"
  integrity sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fresh/-/fresh-0.5.2.tgz"
  integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fs-extra/-/fs-extra-8.1.0.tgz"
  integrity sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs-minipass@^3.0.0:
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fs-minipass/-/fs-minipass-3.0.3.tgz"
  integrity sha512-XUBA9XClHbnJWSfBzjkm6RvPsyg3sryZt06BEQoXcF7EK/xpGaQYJgQKDJSUH5SGZ76Y7pFx1QBnXz09rU5Fbw==
  dependencies:
    minipass "^7.0.3"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

fuzzysort@^1.2.1:
  version "1.9.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/fuzzysort/-/fuzzysort-1.9.0.tgz"
  integrity sha512-MOxCT0qLTwLqmEwc7UtU045RKef7mc8Qz8eR4r2bLNEq9dy/c3ZKMEFp6IEst69otkQdFZ4FfgH2dmZD+ddX1g==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-east-asian-width@^1.0.0:
  version "1.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/get-east-asian-width/-/get-east-asian-width-1.3.0.tgz"
  integrity sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==

glob@^10.2.2, glob@^10.3.10, glob@^10.3.7:
  version "10.4.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/glob/-/glob-10.4.5.tgz"
  integrity sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3, glob@^7.1.7:
  version "7.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globby@^14.0.0:
  version "14.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/globby/-/globby-14.0.2.tgz"
  integrity sha512-s3Fq41ZVh7vbbe2PN3nrW7yC7U7MFVc5c98/iTl9c2GawNMKx/J648KQRW6WKkuU8GIbbh2IXfIRQjOZnXcTnw==
  dependencies:
    "@sindresorhus/merge-streams" "^2.1.0"
    fast-glob "^3.3.2"
    ignore "^5.2.4"
    path-type "^5.0.0"
    slash "^5.1.0"
    unicorn-magic "^0.1.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/gopd/-/gopd-1.0.1.tgz"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11, graceful-fs@^4.2.4, graceful-fs@^4.2.6:
  version "4.2.11"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/handle-thing/-/handle-thing-2.0.1.tgz"
  integrity sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/has-proto/-/has-proto-1.0.3.tgz"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

hosted-git-info@^7.0.0:
  version "7.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/hosted-git-info/-/hosted-git-info-7.0.2.tgz"
  integrity sha512-puUZAUKT5m8Zzvs72XWy3HtvVbTWljRE66cP60bxJzAqf2DgICo7lYTY2IHUmLnNpjYvw5bvmoHvPc0QO2a62w==
  dependencies:
    lru-cache "^10.0.1"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/hpack.js/-/hpack.js-2.1.6.tgz"
  integrity sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.4.0:
  version "2.5.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/html-entities/-/html-entities-2.5.2.tgz"
  integrity sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==

htmlparser2@^8.0.2:
  version "8.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/htmlparser2/-/htmlparser2-8.0.2.tgz"
  integrity sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

http-cache-semantics@^4.1.1:
  version "4.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-cache-semantics/-/http-cache-semantics-4.1.1.tgz"
  integrity sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-deceiver/-/http-deceiver-1.2.7.tgz"
  integrity sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-errors/-/http-errors-1.6.3.tgz"
  integrity sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-parser-js/-/http-parser-js-0.5.8.tgz"
  integrity sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==

http-proxy-agent@^7.0.0:
  version "7.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz"
  integrity sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

http-proxy-middleware@3.0.3:
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-proxy-middleware/-/http-proxy-middleware-3.0.3.tgz"
  integrity sha512-usY0HG5nyDUwtqpiZdETNbmKtw3QQ1jwYFZ9wi5iHzX2BcILwQKtYDJPo7XHTsu5Z0B2Hj3W9NNnbd+AjFWjqg==
  dependencies:
    "@types/http-proxy" "^1.17.15"
    debug "^4.3.6"
    http-proxy "^1.18.1"
    is-glob "^4.0.3"
    is-plain-object "^5.0.0"
    micromatch "^4.0.8"

http-proxy-middleware@^2.0.3:
  version "2.0.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-proxy-middleware/-/http-proxy-middleware-2.0.7.tgz"
  integrity sha512-fgVY8AV7qU7z/MmXJ/rxwbrtQH4jBQ9m7kp3llF0liB7glmFeVZFBepQb32T3y8n8k2+AEYuMPCpinYW+/CuRA==
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/http-proxy/-/http-proxy-1.18.1.tgz"
  integrity sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

https-proxy-agent@7.0.5, https-proxy-agent@^7.0.1:
  version "7.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/https-proxy-agent/-/https-proxy-agent-7.0.5.tgz"
  integrity sha512-1e4Wqeblerz+tMKPIq2EMGiiWW1dIjZOksyHWSUm1rmuvw/how9hBHZ38lAGj5ID4Ik6EdkOw7NmWPy6LAwalw==
  dependencies:
    agent-base "^7.0.2"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

hyperdyperid@^1.2.0:
  version "1.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/hyperdyperid/-/hyperdyperid-1.2.0.tgz"
  integrity sha512-Y93lCzHYgGWdrJ66yIktxiaGULYc6oGiABxhcO5AufBeOyoIdZF7bIfLaOrbM0iGIOXQQgxxRrFEnb+Y6w1n4A==

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.2, iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/iconv-lite/-/iconv-lite-0.6.3.tgz"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/icss-utils/-/icss-utils-5.1.0.tgz"
  integrity sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==

ieee754@^1.1.13:
  version "1.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore-walk@^6.0.4:
  version "6.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ignore-walk/-/ignore-walk-6.0.5.tgz"
  integrity sha512-VuuG0wCnjhnylG1ABXT3dAuIpTNDs/G8jlpmwXY03fXoXy/8ZK8/T+hMzt8L4WnrLCJgdybqgPagnF/f97cg3A==
  dependencies:
    minimatch "^9.0.0"

ignore@^5.2.4:
  version "5.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ignore/-/ignore-5.3.2.tgz"
  integrity sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==

image-size@~0.5.0:
  version "0.5.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/image-size/-/image-size-0.5.5.tgz"
  integrity sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==

immutable@^4.0.0:
  version "4.3.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/immutable/-/immutable-4.3.7.tgz"
  integrity sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==

import-fresh@^3.3.0:
  version "3.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/indent-string/-/indent-string-4.0.0.tgz"
  integrity sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inherits@2.0.3:
  version "2.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/inherits/-/inherits-2.0.3.tgz"
  integrity sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==

ini@4.1.3, ini@^4.1.3:
  version "4.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ini/-/ini-4.1.3.tgz"
  integrity sha512-X7rqawQBvfdjS10YU1y1YVreA3SsLrW9dX2CewP2EbBJM4ypVNLDkO5y04gejPwKIY9lR+7r9gn3rFPt/kmWFg==

ip-address@^9.0.5:
  version "9.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ip-address/-/ip-address-9.0.5.tgz"
  integrity sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g==
  dependencies:
    jsbn "1.1.0"
    sprintf-js "^1.1.3"

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

ipaddr.js@^2.1.0:
  version "2.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ipaddr.js/-/ipaddr.js-2.2.0.tgz"
  integrity sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-core-module@^2.13.0:
  version "2.15.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-core-module/-/is-core-module-2.15.1.tgz"
  integrity sha512-z0vtXSwucUJtANQWldhbtbt7BnL0vxiFjIdDLAatwhDYty2bad6s+rijD6Ri4YuYJubLzIJLUidCh09e1djEVQ==
  dependencies:
    hasown "^2.0.2"

is-docker@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-docker/-/is-docker-3.0.0.tgz"
  integrity sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz"
  integrity sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==

is-fullwidth-code-point@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-5.0.0.tgz"
  integrity sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==
  dependencies:
    get-east-asian-width "^1.0.0"

is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-inside-container@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-inside-container/-/is-inside-container-1.0.0.tgz"
  integrity sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==
  dependencies:
    is-docker "^3.0.0"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-interactive/-/is-interactive-1.0.0.tgz"
  integrity sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==

is-lambda@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-lambda/-/is-lambda-1.0.1.tgz"
  integrity sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==

is-network-error@^1.0.0:
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-network-error/-/is-network-error-1.1.0.tgz"
  integrity sha512-tUdRRAnhT+OtCZR/LxZelH/C7QtjtFrTu5tXCA8pl55eTUElUHT+GPYV8MBMBvea/j+NxQqVt3LbWMRir7Gx9g==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  integrity sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-plain-object/-/is-plain-object-2.0.4.tgz"
  integrity sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==
  dependencies:
    isobject "^3.0.1"

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-plain-object/-/is-plain-object-5.0.0.tgz"
  integrity sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  integrity sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==

is-what@^3.14.1:
  version "3.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-what/-/is-what-3.14.1.tgz"
  integrity sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==

is-wsl@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/is-wsl/-/is-wsl-3.1.0.tgz"
  integrity sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==
  dependencies:
    is-inside-container "^1.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isbinaryfile@^4.0.8:
  version "4.0.10"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/isbinaryfile/-/isbinaryfile-4.0.10.tgz"
  integrity sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

isexe@^3.1.1:
  version "3.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/isexe/-/isexe-3.1.1.tgz"
  integrity sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==

isobject@^3.0.1:
  version "3.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/isobject/-/isobject-3.0.1.tgz"
  integrity sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz"
  integrity sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==

istanbul-lib-instrument@6.0.3:
  version "6.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz"
  integrity sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-instrument@^5.1.0:
  version "5.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  integrity sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  integrity sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  integrity sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.0.5:
  version "3.1.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/istanbul-reports/-/istanbul-reports-3.1.7.tgz"
  integrity sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jasmine-core@^4.1.0:
  version "4.6.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jasmine-core/-/jasmine-core-4.6.1.tgz"
  integrity sha512-VYz/BjjmC3klLJlLwA4Kw8ytk0zDSmbbDLNs794VnWmkcCB7I9aAL/D48VNQtmITyPvea2C3jdUMfc3kAoy0PQ==

jasmine-core@~5.2.0:
  version "5.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jasmine-core/-/jasmine-core-5.2.0.tgz"
  integrity sha512-tSAtdrvWybZkQmmaIoDgnvHG8ORUNw5kEVlO5CvrXj02Jjr9TZrmjFq7FUiOUzJiOP2wLGYT6PgrQgQF4R1xiw==

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jiti@^1.20.0:
  version "1.21.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jiti/-/jiti-1.21.6.tgz"
  integrity sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

jsbn@1.1.0:
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jsbn/-/jsbn-1.1.0.tgz"
  integrity sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A==

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

jsesc@^3.0.2, jsesc@~3.0.2:
  version "3.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jsesc/-/jsesc-3.0.2.tgz"
  integrity sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-parse-even-better-errors@^3.0.0:
  version "3.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/json-parse-even-better-errors/-/json-parse-even-better-errors-3.0.2.tgz"
  integrity sha512-fi0NG4bPjCHunUJffmLd0gxssIgkNmArMvis4iNah6Owg1MCJjWhEcDLmsK6iGkJq3tHwbDkTlce70/tmXN4cQ==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonc-parser@3.3.1:
  version "3.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jsonc-parser/-/jsonc-parser-3.3.1.tgz"
  integrity sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jsonfile/-/jsonfile-4.0.0.tgz"
  integrity sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonparse@^1.3.1:
  version "1.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/jsonparse/-/jsonparse-1.3.1.tgz"
  integrity sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==

karma-chrome-launcher@~3.2.0:
  version "3.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/karma-chrome-launcher/-/karma-chrome-launcher-3.2.0.tgz"
  integrity sha512-rE9RkUPI7I9mAxByQWkGJFXfFD6lE4gC5nPuZdobf/QdTEJI6EU4yIay/cfU/xV4ZxlM5JiTv7zWYgA64NpS5Q==
  dependencies:
    which "^1.2.1"

karma-coverage@~2.2.0:
  version "2.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/karma-coverage/-/karma-coverage-2.2.1.tgz"
  integrity sha512-yj7hbequkQP2qOSb20GuNSIyE//PgJWHwC2IydLE6XRtsnaflv+/OSGNssPjobYUlhVVagy99TQpqUt3vAUG7A==
  dependencies:
    istanbul-lib-coverage "^3.2.0"
    istanbul-lib-instrument "^5.1.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.1"
    istanbul-reports "^3.0.5"
    minimatch "^3.0.4"

karma-jasmine-html-reporter@~2.1.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/karma-jasmine-html-reporter/-/karma-jasmine-html-reporter-2.1.0.tgz"
  integrity sha512-sPQE1+nlsn6Hwb5t+HHwyy0A1FNCVKuL1192b+XNauMYWThz2kweiBVW1DqloRpVvZIJkIoHVB7XRpK78n1xbQ==

karma-jasmine@~5.1.0:
  version "5.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/karma-jasmine/-/karma-jasmine-5.1.0.tgz"
  integrity sha512-i/zQLFrfEpRyQoJF9fsCdTMOF5c2dK7C7OmsuKg2D0YSsuZSfQDiLuaiktbuio6F2wiCsZSnSnieIQ0ant/uzQ==
  dependencies:
    jasmine-core "^4.1.0"

karma-source-map-support@1.4.0:
  version "1.4.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/karma-source-map-support/-/karma-source-map-support-1.4.0.tgz"
  integrity sha512-RsBECncGO17KAoJCYXjv+ckIz+Ii9NCi+9enk+rq6XC81ezYkb4/RHE6CTXdA7IOJqoF3wcaLfVG0CPmE5ca6A==
  dependencies:
    source-map-support "^0.5.5"

karma@~6.4.0:
  version "6.4.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/karma/-/karma-6.4.4.tgz"
  integrity sha512-LrtUxbdvt1gOpo3gxG+VAJlJAEMhbWlM4YrFQgql98FwF7+K8K12LYO4hnDdUkNjeztYrOXEMqgTajSWgmtI/w==
  dependencies:
    "@colors/colors" "1.5.0"
    body-parser "^1.19.0"
    braces "^3.0.2"
    chokidar "^3.5.1"
    connect "^3.7.0"
    di "^0.0.1"
    dom-serialize "^2.2.1"
    glob "^7.1.7"
    graceful-fs "^4.2.6"
    http-proxy "^1.18.1"
    isbinaryfile "^4.0.8"
    lodash "^4.17.21"
    log4js "^6.4.1"
    mime "^2.5.2"
    minimatch "^3.0.4"
    mkdirp "^0.5.5"
    qjobs "^1.2.0"
    range-parser "^1.2.1"
    rimraf "^3.0.2"
    socket.io "^4.7.2"
    source-map "^0.6.1"
    tmp "^0.2.1"
    ua-parser-js "^0.7.30"
    yargs "^16.1.1"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==

launch-editor@^2.6.1:
  version "2.9.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/launch-editor/-/launch-editor-2.9.1.tgz"
  integrity sha512-Gcnl4Bd+hRO9P9icCP/RVVT2o8SFlPXofuCxvA2SaZuH45whSvf5p8x5oih5ftLiVhEI4sp5xDY+R+b3zJBh5w==
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.8.1"

less-loader@12.2.0:
  version "12.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/less-loader/-/less-loader-12.2.0.tgz"
  integrity sha512-MYUxjSQSBUQmowc0l5nPieOYwMzGPUaTzB6inNW/bdPEG9zOL3eAAD1Qw5ZxSPk7we5dMojHwNODYMV1hq4EVg==

less@4.2.0:
  version "4.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/less/-/less-4.2.0.tgz"
  integrity sha512-P3b3HJDBtSzsXUl0im2L7gTO5Ubg8mEN6G8qoTS77iXxXX4Hvu4Qj540PZDvQ8V6DmX6iXo98k7Md0Cm1PrLaA==
  dependencies:
    copy-anything "^2.0.1"
    parse-node-version "^1.0.1"
    tslib "^2.3.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    needle "^3.1.0"
    source-map "~0.6.0"

license-webpack-plugin@4.0.2:
  version "4.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/license-webpack-plugin/-/license-webpack-plugin-4.0.2.tgz"
  integrity sha512-771TFWFD70G1wLTC4oU2Cw4qvtmNrIw+wRvBtn+okgHl7slJVi7zfNcdmqDL72BojM30VNJ2UHylr1o77U37Jw==
  dependencies:
    webpack-sources "^3.0.0"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

listr2@8.2.4:
  version "8.2.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/listr2/-/listr2-8.2.4.tgz"
  integrity sha512-opevsywziHd3zHCVQGAj8zu+Z3yHNkkoYhWIGnq54RrCVwLz0MozotJEDnKsIBLvkfLGN6BLOyAeRrYI0pKA4g==
  dependencies:
    cli-truncate "^4.0.0"
    colorette "^2.0.20"
    eventemitter3 "^5.0.1"
    log-update "^6.1.0"
    rfdc "^1.4.1"
    wrap-ansi "^9.0.0"

lit-element@^4.1.0:
  version "4.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lit-element/-/lit-element-4.1.1.tgz"
  integrity sha512-HO9Tkkh34QkTeUmEdNYhMT8hzLid7YlMlATSi1q4q17HE5d9mrrEHJ/o8O2D0cMi182zK1F3v7x0PWFjrhXFew==
  dependencies:
    "@lit-labs/ssr-dom-shim" "^1.2.0"
    "@lit/reactive-element" "^2.0.4"
    lit-html "^3.2.0"

lit-html@^3.2.0:
  version "3.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lit-html/-/lit-html-3.2.1.tgz"
  integrity sha512-qI/3lziaPMSKsrwlxH/xMgikhQ0EGOX2ICU73Bi/YHFvz2j/yMCIrw4+puF2IpQ4+upd3EWbvnHM9+PnJn48YA==
  dependencies:
    "@types/trusted-types" "^2.0.2"

"lit@^2.5.0 || ^3.1.3", lit@^3.1.3:
  version "3.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lit/-/lit-3.2.1.tgz"
  integrity sha512-1BBa1E/z0O9ye5fZprPtdqnc0BFzxIxTTOO/tQFmyC/hj1O3jL4TfmLBw0WEwjAokdLwpclkvGgDJwTIh0/22w==
  dependencies:
    "@lit/reactive-element" "^2.0.4"
    lit-element "^4.1.0"
    lit-html "^3.2.0"

lmdb@3.0.13:
  version "3.0.13"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lmdb/-/lmdb-3.0.13.tgz"
  integrity sha512-UGe+BbaSUQtAMZobTb4nHvFMrmvuAQKSeaqAX2meTEQjfsbpl5sxdHD8T72OnwD4GU9uwNhYXIVe4QGs8N9Zyw==
  dependencies:
    msgpackr "^1.10.2"
    node-addon-api "^6.1.0"
    node-gyp-build-optional-packages "5.2.2"
    ordered-binary "^1.4.1"
    weak-lru-cache "^1.2.2"
  optionalDependencies:
    "@lmdb/lmdb-darwin-arm64" "3.0.13"
    "@lmdb/lmdb-darwin-x64" "3.0.13"
    "@lmdb/lmdb-linux-arm" "3.0.13"
    "@lmdb/lmdb-linux-arm64" "3.0.13"
    "@lmdb/lmdb-linux-x64" "3.0.13"
    "@lmdb/lmdb-win32-x64" "3.0.13"

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/loader-runner/-/loader-runner-4.3.0.tgz"
  integrity sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==

loader-utils@3.3.1:
  version "3.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/loader-utils/-/loader-utils-3.3.1.tgz"
  integrity sha512-FMJTLMXfCLMLfJxcX9PFqX5qD88Z5MRGaZCVzfuqeZSPsyiBzs+pahDQjbIWz2QIzPZz0NX9Zy4FX3lmK6YHIg==

loader-utils@^2.0.0:
  version "2.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/loader-utils/-/loader-utils-2.0.4.tgz"
  integrity sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^7.1.0:
  version "7.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/locate-path/-/locate-path-7.2.0.tgz"
  integrity sha512-gvVijfZvn7R+2qyPX8mAuKcFGDf6Nc61GdvGafQsHL0sBIxfKzA+usWn4GFC/bk+QdwPUD4kWFJLhElipq+0VA==
  dependencies:
    p-locate "^6.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash@^4.17.21:
  version "4.17.21"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/log-symbols/-/log-symbols-4.1.0.tgz"
  integrity sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^6.1.0:
  version "6.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/log-update/-/log-update-6.1.0.tgz"
  integrity sha512-9ie8ItPR6tjY5uYJh8K/Zrv/RMZ5VOlOWvtZdEHYSTFKZfIBPQa9tOAEeAWhd+AnIneLJ22w5fjOYtoutpWq5w==
  dependencies:
    ansi-escapes "^7.0.0"
    cli-cursor "^5.0.0"
    slice-ansi "^7.1.0"
    strip-ansi "^7.1.0"
    wrap-ansi "^9.0.0"

log4js@^6.4.1:
  version "6.9.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/log4js/-/log4js-6.9.1.tgz"
  integrity sha512-1somDdy9sChrr9/f4UlzhdaGfDR2c/SaD2a4T7qEkG4jTS57/B3qmnjLYePwQ8cqWnUHZI0iAKxMBpCZICiZ2g==
  dependencies:
    date-format "^4.0.14"
    debug "^4.3.4"
    flatted "^3.2.7"
    rfdc "^1.3.0"
    streamroller "^3.1.5"

lru-cache@^10.0.1, lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lscache@^1.3.0:
  version "1.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/lscache/-/lscache-1.3.2.tgz"
  integrity sha512-CBZT/pDcaK3I3XGwDLaszDe8hj0pCgbuxd3W79gvHApBSdKVXvR9fillbp6eLvp7dLgtaWm3a1mvmhAqn9uCXQ==

magic-string@0.30.11:
  version "0.30.11"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/magic-string/-/magic-string-0.30.11.tgz"
  integrity sha512-+Wri9p0QHMy+545hKww7YAu5NyzF8iomPL/RQazugQ9+Ez4Ic3mERMd8ZTX5rfK944j+560ZJi8iAwgak1Ac7A==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/make-dir/-/make-dir-2.1.0.tgz"
  integrity sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/make-dir/-/make-dir-4.0.0.tgz"
  integrity sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==
  dependencies:
    semver "^7.5.3"

make-fetch-happen@^13.0.0, make-fetch-happen@^13.0.1:
  version "13.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/make-fetch-happen/-/make-fetch-happen-13.0.1.tgz"
  integrity sha512-cKTUFc/rbKUd/9meOvgrpJ2WrNzymt6jfRDdwg5UCnVzv9dTpEj9JS5m3wtziXVCjluIXyL8pcaukYqezIzZQA==
  dependencies:
    "@npmcli/agent" "^2.0.0"
    cacache "^18.0.0"
    http-cache-semantics "^4.1.1"
    is-lambda "^1.0.1"
    minipass "^7.0.2"
    minipass-fetch "^3.0.0"
    minipass-flush "^1.0.5"
    minipass-pipeline "^1.2.4"
    negotiator "^0.6.3"
    proc-log "^4.2.0"
    promise-retry "^2.0.1"
    ssri "^10.0.0"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==

memfs@^4.6.0:
  version "4.14.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/memfs/-/memfs-4.14.0.tgz"
  integrity sha512-JUeY0F/fQZgIod31Ja1eJgiSxLn7BfQlCnqhwXFBzFHEw63OdLK7VJUJ7bnzNsWgCyoUP5tEp1VRY8rDaYzqOA==
  dependencies:
    "@jsonjoy.com/json-pack" "^1.0.3"
    "@jsonjoy.com/util" "^1.3.0"
    tree-dump "^1.0.1"
    tslib "^2.0.0"

merge-descriptors@1.0.3:
  version "1.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/merge-descriptors/-/merge-descriptors-1.0.3.tgz"
  integrity sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@~1.1.2:
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/methods/-/methods-1.1.2.tgz"
  integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==

micromatch@^4.0.2, micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2":
  version "1.52.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@1.6.0, mime@^1.4.1:
  version "1.6.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mime@^2.5.2:
  version "2.6.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mime/-/mime-2.6.0.tgz"
  integrity sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mimic-function@^5.0.0:
  version "5.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mimic-function/-/mimic-function-5.0.1.tgz"
  integrity sha512-VP79XUPxV2CigYP3jWwAUFSku2aKqBH7uTAapFWCBqutsbmDo96KY5o8uh6U+/YSIn5OxJnXp73beVkpqMIGhA==

mini-css-extract-plugin@2.9.0:
  version "2.9.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mini-css-extract-plugin/-/mini-css-extract-plugin-2.9.0.tgz"
  integrity sha512-Zs1YsZVfemekSZG+44vBsYTLQORkPMwnlv+aehcxK/NLKC+EGhDB39/YePYYqx/sTk6NnYpuqikhSn7+JIevTA==
  dependencies:
    schema-utils "^4.0.0"
    tapable "^2.2.1"

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  integrity sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.0, minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.6:
  version "1.2.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minipass-collect@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass-collect/-/minipass-collect-2.0.1.tgz"
  integrity sha512-D7V8PO9oaz7PWGLbCACuI1qEOsq7UKfLotx/C0Aet43fCUB/wfQ7DYeq2oR/svFJGYDHPr38SHATeaj/ZoKHKw==
  dependencies:
    minipass "^7.0.3"

minipass-fetch@^3.0.0:
  version "3.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass-fetch/-/minipass-fetch-3.0.5.tgz"
  integrity sha512-2N8elDQAtSnFV0Dk7gt15KHsS0Fyz6CbYZ360h0WTYV1Ty46li3rAXVOQj1THMNLdmrD9Vt5pBPtWtVkpwGBqg==
  dependencies:
    minipass "^7.0.3"
    minipass-sized "^1.0.3"
    minizlib "^2.1.2"
  optionalDependencies:
    encoding "^0.1.13"

minipass-flush@^1.0.5:
  version "1.0.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass-flush/-/minipass-flush-1.0.5.tgz"
  integrity sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==
  dependencies:
    minipass "^3.0.0"

minipass-pipeline@^1.2.4:
  version "1.2.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz"
  integrity sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==
  dependencies:
    minipass "^3.0.0"

minipass-sized@^1.0.3:
  version "1.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass-sized/-/minipass-sized-1.0.3.tgz"
  integrity sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==
  dependencies:
    minipass "^3.0.0"

minipass@^3.0.0:
  version "3.3.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass/-/minipass-3.3.6.tgz"
  integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
  dependencies:
    yallist "^4.0.0"

minipass@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass/-/minipass-5.0.0.tgz"
  integrity sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.0.2, minipass@^7.0.3, minipass@^7.1.2:
  version "7.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

minizlib@^2.1.1, minizlib@^2.1.2:
  version "2.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@^0.5.5:
  version "0.5.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mkdirp/-/mkdirp-0.5.6.tgz"
  integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
  dependencies:
    minimist "^1.2.6"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

mrmime@2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mrmime/-/mrmime-2.0.0.tgz"
  integrity sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==

ms@2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

ms@2.1.3, ms@^2.1.3:
  version "2.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

msgpackr-extract@^3.0.2:
  version "3.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/msgpackr-extract/-/msgpackr-extract-3.0.3.tgz"
  integrity sha512-P0efT1C9jIdVRefqjzOQ9Xml57zpOXnIuS+csaB4MdZbTdmGDLo8XhzBG1N7aO11gKDDkJvBLULeFTo46wwreA==
  dependencies:
    node-gyp-build-optional-packages "5.2.2"
  optionalDependencies:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64" "3.0.3"
    "@msgpackr-extract/msgpackr-extract-darwin-x64" "3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm" "3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm64" "3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-x64" "3.0.3"
    "@msgpackr-extract/msgpackr-extract-win32-x64" "3.0.3"

msgpackr@^1.10.2:
  version "1.11.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/msgpackr/-/msgpackr-1.11.2.tgz"
  integrity sha512-F9UngXRlPyWCDEASDpTf6c9uNhGPTqnTeLVt7bN+bU1eajoR/8V9ys2BRaV5C/e5ihE6sJ9uPIKaYt6bFuO32g==
  optionalDependencies:
    msgpackr-extract "^3.0.2"

multicast-dns@^7.2.5:
  version "7.2.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/multicast-dns/-/multicast-dns-7.2.5.tgz"
  integrity sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==
  dependencies:
    dns-packet "^5.2.2"
    thunky "^1.0.2"

mute-stream@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/mute-stream/-/mute-stream-1.0.0.tgz"
  integrity sha512-avsJQhyd+680gKXyG/sQc0nXaC6rBkPOfyHYcFb9+hdkqQkR9bdnkJ0AMZhke0oesPqIO+mFFJ+IdBc7mst4IA==

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/nanoid/-/nanoid-3.3.7.tgz"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

needle@^3.1.0:
  version "3.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/needle/-/needle-3.3.1.tgz"
  integrity sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==
  dependencies:
    iconv-lite "^0.6.3"
    sax "^1.2.4"

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

negotiator@^0.6.3, negotiator@~0.6.4:
  version "0.6.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/negotiator/-/negotiator-0.6.4.tgz"
  integrity sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

nice-napi@^1.0.2:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/nice-napi/-/nice-napi-1.0.2.tgz#dc0ab5a1eac20ce548802fc5686eaa6bc654927b"
  integrity sha512-px/KnJAJZf5RuBGcfD+Sp2pAKq0ytz8j+1NehvgIGFkvtvFrDM3T8E4x/JJODXK9WZow8RRGrbA9QQ3hs+pDhA==
  dependencies:
    node-addon-api "^3.0.0"
    node-gyp-build "^4.2.2"

node-addon-api@^3.0.0:
  version "3.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/node-addon-api/-/node-addon-api-3.2.1.tgz#81325e0a2117789c0128dab65e7e38f07ceba161"
  integrity sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A==

node-addon-api@^6.1.0:
  version "6.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/node-addon-api/-/node-addon-api-6.1.0.tgz"
  integrity sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==

node-forge@^1:
  version "1.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/node-forge/-/node-forge-1.3.1.tgz"
  integrity sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==

node-gyp-build-optional-packages@5.2.2:
  version "5.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/node-gyp-build-optional-packages/-/node-gyp-build-optional-packages-5.2.2.tgz"
  integrity sha512-s+w+rBWnpTMwSFbaE0UXsRlg7hU4FjekKU4eyAih5T8nJuNZT1nNsskXpxmeqSK9UzkBl6UgRlnKc8hz8IEqOw==
  dependencies:
    detect-libc "^2.0.1"

node-gyp-build@^4.2.2:
  version "4.8.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/node-gyp-build/-/node-gyp-build-4.8.4.tgz#8a70ee85464ae52327772a90d66c6077a900cfc8"
  integrity sha512-LA4ZjwlnUblHVgq0oBF3Jl/6h/Nvs5fzBLwdEF4nuxnFdsfajde4WfxtJr3CaiH+F6ewcIB/q4jQ4UzPyid+CQ==

node-gyp@^10.0.0:
  version "10.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/node-gyp/-/node-gyp-10.2.0.tgz"
  integrity sha512-sp3FonBAaFe4aYTcFdZUn2NYkbP7xroPGYvQmP4Nl5PxamznItBnNCgjrVTKrEfQynInMsJvZrdmqUnysCJ8rw==
  dependencies:
    env-paths "^2.2.0"
    exponential-backoff "^3.1.1"
    glob "^10.3.10"
    graceful-fs "^4.2.6"
    make-fetch-happen "^13.0.0"
    nopt "^7.0.0"
    proc-log "^4.1.0"
    semver "^7.3.5"
    tar "^6.2.1"
    which "^4.0.0"

node-releases@^2.0.18:
  version "2.0.18"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/node-releases/-/node-releases-2.0.18.tgz"
  integrity sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==

nopt@^7.0.0:
  version "7.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/nopt/-/nopt-7.2.1.tgz"
  integrity sha512-taM24ViiimT/XntxbPyJQzCG+p4EKOpgD3mxFwW38mGjVUrfERQOeY4EDHjdnptttfHuHQXFx+lTP08Q+mLa/w==
  dependencies:
    abbrev "^2.0.0"

normalize-package-data@^6.0.0:
  version "6.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/normalize-package-data/-/normalize-package-data-6.0.2.tgz"
  integrity sha512-V6gygoYb/5EmNI+MEGrWkC+e6+Rr7mTmfHrxDbLzxQogBkgzo76rkok0Am6thgSF7Mv2nLOajAJj5vDJZEFn7g==
  dependencies:
    hosted-git-info "^7.0.0"
    semver "^7.3.5"
    validate-npm-package-license "^3.0.4"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

npm-bundled@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-bundled/-/npm-bundled-3.0.1.tgz"
  integrity sha512-+AvaheE/ww1JEwRHOrn4WHNzOxGtVp+adrg2AeZS/7KuxGUYFuBta98wYpfHBbJp6Tg6j1NKSEVHNcfZzJHQwQ==
  dependencies:
    npm-normalize-package-bin "^3.0.0"

npm-install-checks@^6.0.0:
  version "6.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-install-checks/-/npm-install-checks-6.3.0.tgz"
  integrity sha512-W29RiK/xtpCGqn6f3ixfRYGk+zRyr+Ew9F2E20BfXxT5/euLdA/Nm7fO7OeTGuAmTs30cpgInyJ0cYe708YTZw==
  dependencies:
    semver "^7.1.1"

npm-normalize-package-bin@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-normalize-package-bin/-/npm-normalize-package-bin-3.0.1.tgz"
  integrity sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==

npm-package-arg@11.0.3, npm-package-arg@^11.0.0:
  version "11.0.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-package-arg/-/npm-package-arg-11.0.3.tgz"
  integrity sha512-sHGJy8sOC1YraBywpzQlIKBE4pBbGbiF95U6Auspzyem956E0+FtDtsx1ZxlOJkQCZ1AFXAY/yuvtFYrOxF+Bw==
  dependencies:
    hosted-git-info "^7.0.0"
    proc-log "^4.0.0"
    semver "^7.3.5"
    validate-npm-package-name "^5.0.0"

npm-packlist@^8.0.0:
  version "8.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-packlist/-/npm-packlist-8.0.2.tgz"
  integrity sha512-shYrPFIS/JLP4oQmAwDyk5HcyysKW8/JLTEA32S0Z5TzvpaeeX2yMFfoK1fjEBnCBvVyIB/Jj/GBFdm0wsgzbA==
  dependencies:
    ignore-walk "^6.0.4"

npm-pick-manifest@9.1.0, npm-pick-manifest@^9.0.0:
  version "9.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-pick-manifest/-/npm-pick-manifest-9.1.0.tgz"
  integrity sha512-nkc+3pIIhqHVQr085X9d2JzPzLyjzQS96zbruppqC9aZRm/x8xx6xhI98gHtsfELP2bE+loHq8ZaHFHhe+NauA==
  dependencies:
    npm-install-checks "^6.0.0"
    npm-normalize-package-bin "^3.0.0"
    npm-package-arg "^11.0.0"
    semver "^7.3.5"

npm-registry-fetch@^17.0.0:
  version "17.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-registry-fetch/-/npm-registry-fetch-17.1.0.tgz"
  integrity sha512-5+bKQRH0J1xG1uZ1zMNvxW0VEyoNWgJpY9UDuluPFLKDfJ9u2JmmjmTJV1srBGQOROfdBMiVvnH2Zvpbm+xkVA==
  dependencies:
    "@npmcli/redact" "^2.0.0"
    jsonparse "^1.3.1"
    make-fetch-happen "^13.0.0"
    minipass "^7.0.2"
    minipass-fetch "^3.0.0"
    minizlib "^2.1.2"
    npm-package-arg "^11.0.0"
    proc-log "^4.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/nth-check/-/nth-check-2.1.1.tgz"
  integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
  dependencies:
    boolbase "^1.0.0"

object-assign@^4:
  version "4.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-inspect@^1.13.1:
  version "1.13.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/object-inspect/-/object-inspect-1.13.3.tgz"
  integrity sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/obuf/-/obuf-1.1.2.tgz"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

on-finished@2.4.1, on-finished@^2.4.1:
  version "2.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/on-finished/-/on-finished-2.3.0.tgz"
  integrity sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/on-headers/-/on-headers-1.0.2.tgz"
  integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==

once@^1.3.0:
  version "1.4.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

onetime@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/onetime/-/onetime-7.0.0.tgz"
  integrity sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==
  dependencies:
    mimic-function "^5.0.0"

open@10.1.0, open@^10.0.3:
  version "10.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/open/-/open-10.1.0.tgz"
  integrity sha512-mnkeQ1qP5Ue2wd+aivTD3NHd/lZ96Lu0jgf0pwktLPtx6cTZiH7tyeGRRHs0zX0rbrahXPnXlUnbeXyaBBuIaw==
  dependencies:
    default-browser "^5.2.1"
    define-lazy-prop "^3.0.0"
    is-inside-container "^1.0.0"
    is-wsl "^3.1.0"

ora@5.4.1:
  version "5.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ora/-/ora-5.4.1.tgz"
  integrity sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

ordered-binary@^1.4.1:
  version "1.5.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ordered-binary/-/ordered-binary-1.5.3.tgz"
  integrity sha512-oGFr3T+pYdTGJ+YFEILMpS3es+GiIbs9h/XQrclBXUtd44ey7XwfsMzM31f64I1SQOawDoDr/D823kNCADI8TA==

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==

p-limit@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/p-limit/-/p-limit-4.0.0.tgz"
  integrity sha512-5b0R4txpzjPWVw/cXXUResoD4hb6U/x9BH08L7nw+GN1sezDzPdxeRvpc9c433fZhBan/wusjbCsqwqm4EIBIQ==
  dependencies:
    yocto-queue "^1.0.0"

p-locate@^6.0.0:
  version "6.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/p-locate/-/p-locate-6.0.0.tgz"
  integrity sha512-wPrq66Llhl7/4AGC6I+cqxT07LhXvWL08LNXz1fENOw0Ap4sRZZ/gZpTTJ5jpurzzzfS2W/Ge9BY3LgLjCShcw==
  dependencies:
    p-limit "^4.0.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/p-map/-/p-map-4.0.0.tgz"
  integrity sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^6.2.0:
  version "6.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/p-retry/-/p-retry-6.2.1.tgz"
  integrity sha512-hEt02O4hUct5wtwg4H4KcWgDdm+l1bOaEy/hWzd8xtXB9BqxTWBBhb+2ImAtH4Cv4rPjV76xN3Zumqk3k3AhhQ==
  dependencies:
    "@types/retry" "0.12.2"
    is-network-error "^1.0.0"
    retry "^0.13.1"

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==

pacote@18.0.6:
  version "18.0.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/pacote/-/pacote-18.0.6.tgz"
  integrity sha512-+eK3G27SMwsB8kLIuj4h1FUhHtwiEUo21Tw8wNjmvdlpOEr613edv+8FUsTj/4F/VN5ywGE19X18N7CC2EJk6A==
  dependencies:
    "@npmcli/git" "^5.0.0"
    "@npmcli/installed-package-contents" "^2.0.1"
    "@npmcli/package-json" "^5.1.0"
    "@npmcli/promise-spawn" "^7.0.0"
    "@npmcli/run-script" "^8.0.0"
    cacache "^18.0.0"
    fs-minipass "^3.0.0"
    minipass "^7.0.2"
    npm-package-arg "^11.0.0"
    npm-packlist "^8.0.0"
    npm-pick-manifest "^9.0.0"
    npm-registry-fetch "^17.0.0"
    proc-log "^4.0.0"
    promise-retry "^2.0.1"
    sigstore "^2.2.0"
    ssri "^10.0.0"
    tar "^6.1.11"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-node-version@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/parse-node-version/-/parse-node-version-1.0.1.tgz"
  integrity sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==

parse5-html-rewriting-stream@7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/parse5-html-rewriting-stream/-/parse5-html-rewriting-stream-7.0.0.tgz"
  integrity sha512-mazCyGWkmCRWDI15Zp+UiCqMp/0dgEmkZRvhlsqqKYr4SsVm/TvnSpD9fCvqCA2zoWJcfRym846ejWBBHRiYEg==
  dependencies:
    entities "^4.3.0"
    parse5 "^7.0.0"
    parse5-sax-parser "^7.0.0"

parse5-sax-parser@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/parse5-sax-parser/-/parse5-sax-parser-7.0.0.tgz"
  integrity sha512-5A+v2SNsq8T6/mG3ahcz8ZtQ0OUFTatxPbeidoMB7tkJSGDY3tdfl4MHovtLQHkEn5CGxijNWRQHhRQ6IRpXKg==
  dependencies:
    parse5 "^7.0.0"

parse5@^7.0.0, parse5@^7.1.2:
  version "7.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/parse5/-/parse5-7.2.1.tgz"
  integrity sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==
  dependencies:
    entities "^4.5.0"

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-exists@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/path-exists/-/path-exists-5.0.0.tgz"
  integrity sha512-RjhtfwJOxzcFmNOi6ltcbcu4Iu+FL3zEj83dk4kAS+fVpTxXLO1b38RvJgT/0QwvV/L3aY9TAnyv0EOqW4GoMQ==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@0.1.10:
  version "0.1.10"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/path-to-regexp/-/path-to-regexp-0.1.10.tgz"
  integrity sha512-7lf7qcQidTku0Gu3YDPc8DJ1q7OOucfa/BSsIwjuh56VU7katFvuM8hULfkwB3Fns/rsVF7PwPKVw1sl5KQS9w==

path-type@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/path-type/-/path-type-5.0.0.tgz"
  integrity sha512-5HviZNaZcfqP95rwpv+1HDgUamezbqdSYTyzjTvwtJSnIH+3vnbmWsItli8OFEndS984VT55M3jduxZbX351gg==

picocolors@^1.0.0, picocolors@^1.0.1, picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@4.0.2:
  version "4.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/picomatch/-/picomatch-4.0.2.tgz"
  integrity sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^4.0.1:
  version "4.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/pify/-/pify-4.0.1.tgz"
  integrity sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==

piscina@4.6.1:
  version "4.6.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/piscina/-/piscina-4.6.1.tgz"
  integrity sha512-z30AwWGtQE+Apr+2WBZensP2lIvwoaMcOPkQlIEmSGMJNUvaYACylPYrQM6wSdUNJlnDVMSpLv7xTMJqlVshOA==
  optionalDependencies:
    nice-napi "^1.0.2"

pkg-dir@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/pkg-dir/-/pkg-dir-7.0.0.tgz"
  integrity sha512-Ie9z/WINcxxLp27BKOCHGde4ITq9UklYKDzVo1nhk5sqGEXU3FpkwP5GM2voTGJkGd9B3Otl+Q4uwSOeSUtOBA==
  dependencies:
    find-up "^6.3.0"

postcss-loader@8.1.1:
  version "8.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-loader/-/postcss-loader-8.1.1.tgz"
  integrity sha512-0IeqyAsG6tYiDRCYKQJLAmgQr47DX6N7sFSWvQxt6AcupX8DIdmykuk/o/tx0Lze3ErGHJEp5OSRxrelC6+NdQ==
  dependencies:
    cosmiconfig "^9.0.0"
    jiti "^1.20.0"
    semver "^7.5.4"

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-media-query-parser/-/postcss-media-query-parser-0.2.3.tgz"
  integrity sha512-3sOlxmbKcSHMjlUXQZKQ06jOswE7oVkXPxmZdoB1r5l0q6gTFTQSHxNxOrCccElbW7dxNytifNEo8qidX2Vsig==

postcss-modules-extract-imports@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz"
  integrity sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==

postcss-modules-local-by-default@^4.0.5:
  version "4.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.1.0.tgz"
  integrity sha512-rm0bdSv4jC3BDma3s9H19ZddW0aHX6EoqwDYU2IfZhRN+53QrufTRo2IdkAbRqLx4R2IYbZnbjKKxg4VN5oU9Q==
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^7.0.0"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.2.0:
  version "3.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-modules-scope/-/postcss-modules-scope-3.2.1.tgz"
  integrity sha512-m9jZstCVaqGjTAuny8MdgE88scJnCiQSlSrOWcTQgM2t32UBe+MUmFSO5t7VMSfAf/FJKImAxBav8ooCHJXCJA==
  dependencies:
    postcss-selector-parser "^7.0.0"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  integrity sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==
  dependencies:
    icss-utils "^5.0.0"

postcss-selector-parser@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-selector-parser/-/postcss-selector-parser-7.0.0.tgz"
  integrity sha512-9RbEr1Y7FFfptd/1eEdntyjMwLeghW1bHX9GWjXo19vx4ytPQhANltvVxDggzJl7mnWM+dX28kb6cyS/4iQjlQ==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@8.4.41, postcss@^8.2.14, postcss@^8.4.23, postcss@^8.4.33:
  version "8.4.41"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss/-/postcss-8.4.41.tgz"
  integrity sha512-TesUflQ0WKZqAvg52PWL6kHgLKP6xB6heTOdoYM0Wt2UHyxNa4K25EZZMgKns3BH1RLVbZCREPpLY0rhnNoHVQ==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.1"
    source-map-js "^1.2.0"

postcss@^8.4.43:
  version "8.4.49"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/postcss/-/postcss-8.4.49.tgz"
  integrity sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prismjs@^1.29.0:
  version "1.30.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/prismjs/-/prismjs-1.30.0.tgz#d9709969d9d4e16403f6f348c63553b19f0975a9"
  integrity sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw==

proc-log@^4.0.0, proc-log@^4.1.0, proc-log@^4.2.0:
  version "4.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/proc-log/-/proc-log-4.2.0.tgz"
  integrity sha512-g8+OnU/L2v+wyiVK+D5fA34J7EH8jZ8DDlvwhRCMxmMj7UCBvxiO1mGeN+36JXIKF4zevU4kRBd8lVgG9vLelA==

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/promise-inflight/-/promise-inflight-1.0.1.tgz"
  integrity sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==

promise-retry@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/promise-retry/-/promise-retry-2.0.1.tgz"
  integrity sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==
  dependencies:
    err-code "^2.0.2"
    retry "^0.12.0"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/prr/-/prr-1.0.1.tgz"
  integrity sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==

punycode@^1.4.1:
  version "1.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/punycode/-/punycode-1.4.1.tgz"
  integrity sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

qjobs@^1.2.0:
  version "1.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/qjobs/-/qjobs-1.2.0.tgz"
  integrity sha512-8YOJEHtxpySA3fFDyCRxA+UUV+fA+rTWnuWvylOK/NCjhY+b4ocCtmu8TtsWb+mYeU+GCHf/S66KZF/AsteKHg==

qs@6.13.0:
  version "6.13.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/qs/-/qs-6.13.0.tgz"
  integrity sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==
  dependencies:
    side-channel "^1.0.6"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

readable-stream@^2.0.1:
  version "2.3.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.4.0:
  version "3.6.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^4.0.1:
  version "4.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/readdirp/-/readdirp-4.0.2.tgz"
  integrity sha512-yDMz9g+VaZkqBYS/ozoBJwaBhTbZo3UNYQHNRw1D3UFQB8oHB4uS/tAODO+ZLjGWmUbKnIlOWO+aaIiAxrUWHA==

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

reflect-metadata@^0.2.0:
  version "0.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/reflect-metadata/-/reflect-metadata-0.2.2.tgz"
  integrity sha512-urBwgfrvVP/eAyXx4hluJivBKzuEbSQs9rKWCrCkbSxNv8mxPcUZKeuoF3Uy4mJl3Lwprp6yy5/39VWigZ4K6Q==

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz"
  integrity sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regenerate/-/regenerate-1.4.2.tgz"
  integrity sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regenerator-transform@^0.15.2:
  version "0.15.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regenerator-transform/-/regenerator-transform-0.15.2.tgz"
  integrity sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-parser@^2.2.11:
  version "2.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regex-parser/-/regex-parser-2.3.0.tgz"
  integrity sha512-TVILVSz2jY5D47F4mA4MppkBrafEaiUWJO/TcZHEIuI13AqoZMkK1WMA4Om1YkYbTx+9Ki1/tSUXbceyr9saRg==

regexpu-core@^6.1.1:
  version "6.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regexpu-core/-/regexpu-core-6.1.1.tgz"
  integrity sha512-k67Nb9jvwJcJmVpw0jPttR1/zVfnKf8Km0IPatrU/zJ5XeG3+Slx0xLXs9HByJSzXzrlz5EDvN6yLNMDc2qdnw==
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.11.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regjsgen/-/regjsgen-0.8.0.tgz"
  integrity sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==

regjsparser@^0.11.0:
  version "0.11.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/regjsparser/-/regjsparser-0.11.2.tgz"
  integrity sha512-3OGZZ4HoLJkkAZx/48mTXJNlmqTGOzc0o9OWQPuWpkOlXXPbyN6OafCcoXUnBqE2D3f/T5L+pWc1kdEmnfnRsA==
  dependencies:
    jsesc "~3.0.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-url-loader@5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/resolve-url-loader/-/resolve-url-loader-5.0.0.tgz"
  integrity sha512-uZtduh8/8srhBoMx//5bwqjQ+rfYOUq8zC9NrMUGtjBiGTtFJM42s58/36+hTqeqINcnYe08Nj3LkK9lW4N8Xg==
  dependencies:
    adjust-sourcemap-loader "^4.0.0"
    convert-source-map "^1.7.0"
    loader-utils "^2.0.0"
    postcss "^8.2.14"
    source-map "0.6.1"

resolve@1.22.8, resolve@^1.14.2:
  version "1.22.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

restore-cursor@^5.0.0:
  version "5.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/restore-cursor/-/restore-cursor-5.1.0.tgz"
  integrity sha512-oMA2dcrw6u0YfxJQXm342bFKX/E4sG9rbTzO9ptUcR/e8A33cHuvStiYOwH7fszkZlZ1z/ta9AAoPk2F4qIOHA==
  dependencies:
    onetime "^7.0.0"
    signal-exit "^4.1.0"

retry@^0.12.0:
  version "0.12.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/retry/-/retry-0.12.0.tgz"
  integrity sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==

retry@^0.13.1:
  version "0.13.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/retry/-/retry-0.13.1.tgz"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rfc4648@^1.5.0:
  version "1.5.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/rfc4648/-/rfc4648-1.5.3.tgz"
  integrity sha512-MjOWxM065+WswwnmNONOT+bD1nXzY9Km6u3kzvnx8F8/HXGZdz3T6e6vZJ8Q/RIMUSp/nxqjH3GwvJDy8ijeQQ==

rfdc@^1.3.0, rfdc@^1.4.1:
  version "1.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/rfdc/-/rfdc-1.4.1.tgz"
  integrity sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rimraf@^5.0.5:
  version "5.0.10"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/rimraf/-/rimraf-5.0.10.tgz"
  integrity sha512-l0OE8wL34P4nJH/H2ffoaniAokM2qSmrtXHmlpvYr5AVVX8msAyW0l8NVJFDxlSK4u3Uh/f41cQheDVdnYijwQ==
  dependencies:
    glob "^10.3.7"

rollup@4.22.4, rollup@^4.20.0:
  version "4.22.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/rollup/-/rollup-4.22.4.tgz"
  integrity sha512-vD8HJ5raRcWOyymsR6Z3o6+RzfEPCnVLMFJ6vRslO1jt4LO6dUo5Qnpg7y4RkZFM2DMe3WUirkI5c16onjrc6A==
  dependencies:
    "@types/estree" "1.0.5"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.22.4"
    "@rollup/rollup-android-arm64" "4.22.4"
    "@rollup/rollup-darwin-arm64" "4.22.4"
    "@rollup/rollup-darwin-x64" "4.22.4"
    "@rollup/rollup-linux-arm-gnueabihf" "4.22.4"
    "@rollup/rollup-linux-arm-musleabihf" "4.22.4"
    "@rollup/rollup-linux-arm64-gnu" "4.22.4"
    "@rollup/rollup-linux-arm64-musl" "4.22.4"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.22.4"
    "@rollup/rollup-linux-riscv64-gnu" "4.22.4"
    "@rollup/rollup-linux-s390x-gnu" "4.22.4"
    "@rollup/rollup-linux-x64-gnu" "4.22.4"
    "@rollup/rollup-linux-x64-musl" "4.22.4"
    "@rollup/rollup-win32-arm64-msvc" "4.22.4"
    "@rollup/rollup-win32-ia32-msvc" "4.22.4"
    "@rollup/rollup-win32-x64-msvc" "4.22.4"
    fsevents "~2.3.2"

run-applescript@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/run-applescript/-/run-applescript-7.0.0.tgz"
  integrity sha512-9by4Ij99JUr/MCFBUkDKLWK3G9HVXmabKz9U5MlIAIuvuzkiOicRYs8XJLxX+xahD+mLiiCYDqF9dKAgtzKP1A==

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

rxjs@7.8.1, rxjs@~7.8.0:
  version "7.8.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/rxjs/-/rxjs-7.8.1.tgz"
  integrity sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==
  dependencies:
    tslib "^2.1.0"

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.1.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sass-loader@16.0.0:
  version "16.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/sass-loader/-/sass-loader-16.0.0.tgz"
  integrity sha512-n13Z+3rU9A177dk4888czcVFiC8CL9dii4qpXWUg3YIIgZEvi9TCFKjOQcbK0kJM7DJu9VucrZFddvNfYCPwtw==
  dependencies:
    neo-async "^2.6.2"

sass@1.77.6:
  version "1.77.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/sass/-/sass-1.77.6.tgz"
  integrity sha512-ByXE1oLD79GVq9Ht1PeHWCPMPB8XHpBuz1r85oByKHjZY6qV6rWnQovQzXJXuQ/XyE1Oj3iPk3lo28uzaRA2/Q==
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

sax@^1.2.4:
  version "1.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/sax/-/sax-1.4.1.tgz"
  integrity sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==

schema-utils@^3.1.1, schema-utils@^3.2.0:
  version "3.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0, schema-utils@^4.2.0:
  version "4.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/schema-utils/-/schema-utils-4.2.0.tgz"
  integrity sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/select-hose/-/select-hose-2.0.0.tgz"
  integrity sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==

selfsigned@^2.4.1:
  version "2.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/selfsigned/-/selfsigned-2.4.1.tgz"
  integrity sha512-th5B4L2U+eGLq1TVh7zNRGBapioSORUeymIydxgFpwww9d2qyKvtuPU2jJuHvYAwwqi2Y596QBL3eEqcPEYL8Q==
  dependencies:
    "@types/node-forge" "^1.3.0"
    node-forge "^1"

semver@7.6.3, semver@^7.0.0, semver@^7.1.1, semver@^7.3.5, semver@^7.5.3, semver@^7.5.4:
  version "7.6.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/semver/-/semver-7.6.3.tgz"
  integrity sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==

semver@^5.6.0:
  version "5.7.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/semver/-/semver-5.7.2.tgz"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

send@0.19.0:
  version "0.19.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/send/-/send-0.19.0.tgz"
  integrity sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.1, serialize-javascript@^6.0.2:
  version "6.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  integrity sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/serve-index/-/serve-index-1.9.1.tgz"
  integrity sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.16.2:
  version "1.16.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/serve-static/-/serve-static-1.16.2.tgz"
  integrity sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==
  dependencies:
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.19.0"

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/setprototypeof/-/setprototypeof-1.1.0.tgz"
  integrity sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/shallow-clone/-/shallow-clone-3.0.1.tgz"
  integrity sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==
  dependencies:
    kind-of "^6.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shell-quote@^1.8.1:
  version "1.8.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/shell-quote/-/shell-quote-1.8.1.tgz"
  integrity sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==

side-channel@^1.0.6:
  version "1.0.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/side-channel/-/side-channel-1.0.6.tgz"
  integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1, signal-exit@^4.1.0:
  version "4.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

sigstore@^2.2.0:
  version "2.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/sigstore/-/sigstore-2.3.1.tgz"
  integrity sha512-8G+/XDU8wNsJOQS5ysDVO0Etg9/2uA5gR9l4ZwijjlwxBcrU6RPfwi2+jJmbP+Ap1Hlp/nVAaEO4Fj22/SL2gQ==
  dependencies:
    "@sigstore/bundle" "^2.3.2"
    "@sigstore/core" "^1.0.0"
    "@sigstore/protobuf-specs" "^0.3.2"
    "@sigstore/sign" "^2.3.2"
    "@sigstore/tuf" "^2.3.4"
    "@sigstore/verify" "^1.2.1"

slash@^5.1.0:
  version "5.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/slash/-/slash-5.1.0.tgz"
  integrity sha512-ZA6oR3T/pEyuqwMgAKT0/hAv8oAXckzbkmR0UkUosQ+Mc4RxGoJkRmwHgHufaenlyAgE1Mxgpdcrf75y6XcnDg==

slice-ansi@^5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/slice-ansi/-/slice-ansi-5.0.0.tgz"
  integrity sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

slice-ansi@^7.1.0:
  version "7.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/slice-ansi/-/slice-ansi-7.1.0.tgz"
  integrity sha512-bSiSngZ/jWeX93BqeIAbImyTbEihizcwNjFoRUIY/T1wWQsfsm2Vw1agPKylXvQTU7iASGdHhyqRlqQzfz+Htg==
  dependencies:
    ansi-styles "^6.2.1"
    is-fullwidth-code-point "^5.0.0"

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/smart-buffer/-/smart-buffer-4.2.0.tgz"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socket.io-adapter@~2.5.2:
  version "2.5.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/socket.io-adapter/-/socket.io-adapter-2.5.5.tgz"
  integrity sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg==
  dependencies:
    debug "~4.3.4"
    ws "~8.17.1"

socket.io-parser@~4.2.4:
  version "4.2.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/socket.io-parser/-/socket.io-parser-4.2.4.tgz"
  integrity sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    debug "~4.3.1"

socket.io@^4.7.2:
  version "4.8.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/socket.io/-/socket.io-4.8.1.tgz"
  integrity sha512-oZ7iUCxph8WYRHHcjBEc9unw3adt5CmSNlppj/5Q4k2RIrhl8Z5yY2Xr4j9zj0+wzVZ0bxmYoGSzKJnRl6A4yg==
  dependencies:
    accepts "~1.3.4"
    base64id "~2.0.0"
    cors "~2.8.5"
    debug "~4.3.2"
    engine.io "~6.6.0"
    socket.io-adapter "~2.5.2"
    socket.io-parser "~4.2.4"

sockjs@^0.3.24:
  version "0.3.24"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/sockjs/-/sockjs-0.3.24.tgz"
  integrity sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

socks-proxy-agent@^8.0.3:
  version "8.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/socks-proxy-agent/-/socks-proxy-agent-8.0.4.tgz"
  integrity sha512-GNAq/eg8Udq2x0eNiFkr9gRg5bA7PXEWagQdeRX4cPSG+X/8V38v637gim9bjFptMk1QWsCTr0ttrJEiXbNnRw==
  dependencies:
    agent-base "^7.1.1"
    debug "^4.3.4"
    socks "^2.8.3"

socks@^2.8.3:
  version "2.8.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/socks/-/socks-2.8.3.tgz"
  integrity sha512-l5x7VUUWbjVFbafGLxPWkYsHIhEvmF85tbIeFZWc8ZPtoMyybuEhL7Jye/ooC4/d48FgOjSJXgsF/AJPYCW8Zw==
  dependencies:
    ip-address "^9.0.5"
    smart-buffer "^4.2.0"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.0.2, source-map-js@^1.2.0, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map-loader@5.0.0:
  version "5.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/source-map-loader/-/source-map-loader-5.0.0.tgz"
  integrity sha512-k2Dur7CbSLcAH73sBcIkV5xjPV4SzqO1NJ7+XaQl8if3VODDUj3FNchNGpqgJSKbvUfJuhVdv8K2Eu8/TNl2eA==
  dependencies:
    iconv-lite "^0.6.3"
    source-map-js "^1.0.2"

source-map-support@0.5.21, source-map-support@^0.5.5, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@0.6.1, source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0:
  version "0.6.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@0.7.4:
  version "0.7.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/source-map/-/source-map-0.7.4.tgz"
  integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/spdx-correct/-/spdx-correct-3.2.0.tgz"
  integrity sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz"
  integrity sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.20"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/spdx-license-ids/-/spdx-license-ids-3.0.20.tgz"
  integrity sha512-jg25NiDV/1fLtSgEgyvVyDunvaNHbuwF9lfNV17gSmPFAlYzdfNBlLtLzXTevwkPj7DhGbmN9VnmJIgLnhvaBw==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/spdy-transport/-/spdy-transport-3.0.0.tgz"
  integrity sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/spdy/-/spdy-4.0.2.tgz"
  integrity sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@^1.1.3:
  version "1.1.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/sprintf-js/-/sprintf-js-1.1.3.tgz"
  integrity sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==

ssri@^10.0.0:
  version "10.0.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ssri/-/ssri-10.0.6.tgz"
  integrity sha512-MGrFH9Z4NP9Iyhqn16sDtBpRRNJ0Y2hNa6D65h736fVSaPCHr4DM4sWUNvVaSuC+0OBGhwsrydQwmgfg5LncqQ==
  dependencies:
    minipass "^7.0.3"

statuses@2.0.1:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

"statuses@>= 1.4.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/statuses/-/statuses-1.5.0.tgz"
  integrity sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==

streamroller@^3.1.5:
  version "3.1.5"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/streamroller/-/streamroller-3.1.5.tgz"
  integrity sha512-KFxaM7XT+irxvdqSP1LGLgNWbYN7ay5owZ3r/8t77p+EtSUAfUgtl7be3xtqtOmGUl9K9YPO2ca8133RlTjvKw==
  dependencies:
    date-format "^4.0.14"
    debug "^4.3.4"
    fs-extra "^8.1.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^7.0.0:
  version "7.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/string-width/-/string-width-7.2.0.tgz"
  integrity sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==
  dependencies:
    emoji-regex "^10.3.0"
    get-east-asian-width "^1.0.0"
    strip-ansi "^7.1.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1, strip-ansi@^7.1.0:
  version "7.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

symbol-observable@4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/symbol-observable/-/symbol-observable-4.0.0.tgz"
  integrity sha512-b19dMThMV4HVFynSAM1++gBHAbk2Tc/osgLIBZMKsyqh34jb2e8Os7T6ZW/Bt3pJFdBTd2JwAnAAEQV7rSNvcQ==

tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

tar@^6.1.11, tar@^6.2.1:
  version "6.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tar/-/tar-6.2.1.tgz"
  integrity sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

terser-webpack-plugin@^5.3.10:
  version "5.3.10"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/terser-webpack-plugin/-/terser-webpack-plugin-5.3.10.tgz"
  integrity sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.20"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.26.0"

terser@5.31.6, terser@^5.26.0:
  version "5.31.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/terser/-/terser-5.31.6.tgz"
  integrity sha512-PQ4DAriWzKj+qgehQ7LK5bQqCFNMmlhjR2PFFLuqGCpuCAauxemVBWwWOxo3UIwWQx8+Pr61Df++r76wDmkQBg==
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

thingies@^1.20.0:
  version "1.21.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/thingies/-/thingies-1.21.0.tgz"
  integrity sha512-hsqsJsFMsV+aD4s3CWKk85ep/3I9XzYV/IXaSouJMYIoDlgyi11cBhsqYe9/geRfB0YIikBQg6raRaM+nIMP9g==

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/thunky/-/thunky-1.1.0.tgz"
  integrity sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tmp/-/tmp-0.0.33.tgz"
  integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
  dependencies:
    os-tmpdir "~1.0.2"

tmp@^0.2.1:
  version "0.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tmp/-/tmp-0.2.3.tgz"
  integrity sha512-nZD7m9iCPC5g0pYmcaxogYKggSfLsdxl8of3Q/oIbqCqLLIO9IAF0GWjX1z9NZRHPiXv8Wex4yDCaZsgEw0Y8w==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

tree-dump@^1.0.1:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tree-dump/-/tree-dump-1.0.2.tgz"
  integrity sha512-dpev9ABuLWdEubk+cIaI9cHwRNNDjkBBLXTwI4UCUFdQ5xXKqNXoK4FEciw/vxf+NQ7Cb7sGUyeUtORvHIdRXQ==

tree-kill@1.2.2:
  version "1.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tree-kill/-/tree-kill-1.2.2.tgz"
  integrity sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==

tslib@2.6.3:
  version "2.6.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tslib/-/tslib-2.6.3.tgz"
  integrity sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==

tslib@^2.0.0, tslib@^2.1.0, tslib@^2.3.0:
  version "2.8.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tuf-js@^2.2.1:
  version "2.2.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/tuf-js/-/tuf-js-2.2.1.tgz"
  integrity sha512-GwIJau9XaA8nLVbUXsN3IlFi7WmQ48gBUrl3FTkkL/XLu/POhBzfmX9hd33FNMX1qAsfl6ozO1iMmW9NC8YniA==
  dependencies:
    "@tufjs/models" "2.0.1"
    debug "^4.3.4"
    make-fetch-happen "^13.0.1"

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-assert@^1.0.8:
  version "1.0.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/typed-assert/-/typed-assert-1.0.9.tgz"
  integrity sha512-KNNZtayBCtmnNmbo5mG47p1XsCyrx6iVqomjcZnec/1Y5GGARaxPs6r49RnSPeUP3YjNYiU9sQHAtY4BBvnZwg==

typescript@~5.5.2:
  version "5.5.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/typescript/-/typescript-5.5.4.tgz"
  integrity sha512-Mtq29sKDAEYP7aljRgtPOpTvOfbwRWlS6dPRzwjdE+C0R4brX/GUyhHSecbHMFLNBLcJIPt9nl9yG5TZ1weH+Q==

ua-parser-js@^0.7.30:
  version "0.7.39"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ua-parser-js/-/ua-parser-js-0.7.39.tgz"
  integrity sha512-IZ6acm6RhQHNibSt7+c09hhvsKy9WUr4DVbeq9U8o71qxyYtJpQeDxQnMrVqnIFMLcQjHO0I9wgfO2vIahht4w==

undici-types@~6.19.8:
  version "6.19.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/undici-types/-/undici-types-6.19.8.tgz"
  integrity sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz"
  integrity sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz"
  integrity sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  integrity sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==

unicorn-magic@^0.1.0:
  version "0.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unicorn-magic/-/unicorn-magic-0.1.0.tgz"
  integrity sha512-lRfVq8fE8gz6QMBuDM6a+LO3IAzTi05H6gCVaUpir2E1Rwpo4ZUog45KpNXKC/Mn3Yb9UDuHumeFTo9iV/D9FQ==

unique-filename@^3.0.0:
  version "3.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unique-filename/-/unique-filename-3.0.0.tgz"
  integrity sha512-afXhuC55wkAmZ0P18QsVE6kp8JaxrEokN2HGIoIVv2ijHQd419H0+6EigAFcIzXeMIkcIkNBpB3L/DXB3cTS/g==
  dependencies:
    unique-slug "^4.0.0"

unique-slug@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unique-slug/-/unique-slug-4.0.0.tgz"
  integrity sha512-WrcA6AyEfqDX5bWige/4NQfPZMtASNVxdmWR76WESYQVAACSgWcR6e9i0mofqqBxYFtL4oAxPIptY73/0YE1DQ==
  dependencies:
    imurmurhash "^0.1.4"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/universalify/-/universalify-0.1.2.tgz"
  integrity sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==

update-browserslist-db@^1.1.1:
  version "1.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/update-browserslist-db/-/update-browserslist-db-1.1.1.tgz"
  integrity sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

validate-npm-package-license@^3.0.4:
  version "3.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

validate-npm-package-name@^5.0.0:
  version "5.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/validate-npm-package-name/-/validate-npm-package-name-5.0.1.tgz"
  integrity sha512-OljLrQ9SQdOUqTaQxqL5dEfZWrXExyyWsozYlAWFawPVNuD83igl7uJD2RTkNMbniIYgt8l81eCJGIdQF7avLQ==

vary@^1, vary@~1.1.2:
  version "1.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/vary/-/vary-1.1.2.tgz"
  integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==

vite@5.4.6:
  version "5.4.6"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/vite/-/vite-5.4.6.tgz"
  integrity sha512-IeL5f8OO5nylsgzd9tq4qD2QqI0k2CQLGrWD0rCN0EQJZpBK5vJAx0I+GDkMOXxQX/OfFHMuLIx6ddAxGX/k+Q==
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.43"
    rollup "^4.20.0"
  optionalDependencies:
    fsevents "~2.3.3"

void-elements@^2.0.0:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/void-elements/-/void-elements-2.0.1.tgz"
  integrity sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==

watchpack@2.4.1, watchpack@^2.4.1:
  version "2.4.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/watchpack/-/watchpack-2.4.1.tgz"
  integrity sha512-8wrBCMtVhqcXP2Sup1ctSkga6uc2Bx0IIvKyT7yTFier5AXHooSI+QyQQAtTb7+E0IUCCKyTFmXqdqgum2XWGg==
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wbuf/-/wbuf-1.7.3.tgz"
  integrity sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

weak-lru-cache@^1.2.2:
  version "1.2.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/weak-lru-cache/-/weak-lru-cache-1.2.2.tgz"
  integrity sha512-DEAoo25RfSYMuTGc9vPJzZcZullwIqRDSI9LOy+fkCJPi6hykCnfKaXTuPBDuXAUcqHXyOgFtHNp/kB2FjYHbw==

webpack-dev-middleware@7.4.2, webpack-dev-middleware@^7.1.0:
  version "7.4.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/webpack-dev-middleware/-/webpack-dev-middleware-7.4.2.tgz"
  integrity sha512-xOO8n6eggxnwYpy1NlzUKpvrjfJTvae5/D6WOK0S2LSo7vjmo5gCM1DbLUmFqrMTJP+W/0YZNctm7jasWvLuBA==
  dependencies:
    colorette "^2.0.10"
    memfs "^4.6.0"
    mime-types "^2.1.31"
    on-finished "^2.4.1"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@5.0.4:
  version "5.0.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/webpack-dev-server/-/webpack-dev-server-5.0.4.tgz"
  integrity sha512-dljXhUgx3HqKP2d8J/fUMvhxGhzjeNVarDLcbO/EWMSgRizDkxHQDZQaLFL5VJY9tRBj2Gz+rvCEYYvhbqPHNA==
  dependencies:
    "@types/bonjour" "^3.5.13"
    "@types/connect-history-api-fallback" "^1.5.4"
    "@types/express" "^4.17.21"
    "@types/serve-index" "^1.9.4"
    "@types/serve-static" "^1.15.5"
    "@types/sockjs" "^0.3.36"
    "@types/ws" "^8.5.10"
    ansi-html-community "^0.0.8"
    bonjour-service "^1.2.1"
    chokidar "^3.6.0"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^2.0.0"
    default-gateway "^6.0.3"
    express "^4.17.3"
    graceful-fs "^4.2.6"
    html-entities "^2.4.0"
    http-proxy-middleware "^2.0.3"
    ipaddr.js "^2.1.0"
    launch-editor "^2.6.1"
    open "^10.0.3"
    p-retry "^6.2.0"
    rimraf "^5.0.5"
    schema-utils "^4.2.0"
    selfsigned "^2.4.1"
    serve-index "^1.9.1"
    sockjs "^0.3.24"
    spdy "^4.0.2"
    webpack-dev-middleware "^7.1.0"
    ws "^8.16.0"

webpack-merge@6.0.1:
  version "6.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/webpack-merge/-/webpack-merge-6.0.1.tgz"
  integrity sha512-hXXvrjtx2PLYx4qruKl+kyRSLc52V+cCvMxRjmKwoA+CBbbF5GfIBtR6kCvl0fYGqTUPKB+1ktVmTHqMOzgCBg==
  dependencies:
    clone-deep "^4.0.1"
    flat "^5.0.2"
    wildcard "^2.0.1"

webpack-sources@^3.0.0, webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack-subresource-integrity@5.1.0:
  version "5.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/webpack-subresource-integrity/-/webpack-subresource-integrity-5.1.0.tgz"
  integrity sha512-sacXoX+xd8r4WKsy9MvH/q/vBtEHr86cpImXwyg74pFIpERKt6FmB8cXpeuh0ZLgclOlHI4Wcll7+R5L02xk9Q==
  dependencies:
    typed-assert "^1.0.8"

webpack@5.94.0:
  version "5.94.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/webpack/-/webpack-5.94.0.tgz"
  integrity sha512-KcsGn50VT+06JH/iunZJedYGUJS5FGjow8wb9c0v5n1Om8O1g4L6LjtfxwlXIATopoQu+vOXXa7gYisWxCoPyg==
  dependencies:
    "@types/estree" "^1.0.5"
    "@webassemblyjs/ast" "^1.12.1"
    "@webassemblyjs/wasm-edit" "^1.12.1"
    "@webassemblyjs/wasm-parser" "^1.12.1"
    acorn "^8.7.1"
    acorn-import-attributes "^1.9.5"
    browserslist "^4.21.10"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.17.1"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.10"
    watchpack "^2.4.1"
    webpack-sources "^3.2.3"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/websocket-driver/-/websocket-driver-0.7.4.tgz"
  integrity sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  integrity sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==

which@^1.2.1:
  version "1.3.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

which@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/which/-/which-4.0.0.tgz"
  integrity sha512-GlaYyEb07DPxYCKhKzplCWBJtvxZcZMrL+4UkrTSJHHPyZU4mYYTv3qaOe77H7EODLSSopAUFAc6W8U4yqvscg==
  dependencies:
    isexe "^3.1.1"

wildcard@^2.0.1:
  version "2.0.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wildcard/-/wildcard-2.0.1.tgz"
  integrity sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrap-ansi@^9.0.0:
  version "9.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wrap-ansi/-/wrap-ansi-9.0.0.tgz"
  integrity sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==
  dependencies:
    ansi-styles "^6.2.1"
    string-width "^7.0.0"
    strip-ansi "^7.1.0"

wrappy@1:
  version "1.0.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@^8.16.0, ws@~8.17.1:
  version "8.17.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/ws/-/ws-8.17.1.tgz"
  integrity sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yargs-parser/-/yargs-parser-20.2.9.tgz"
  integrity sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==

yargs@17.7.2, yargs@^17.2.1:
  version "17.7.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yargs/-/yargs-17.7.2.tgz"
  integrity sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yargs@^16.1.1:
  version "16.2.0"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yargs/-/yargs-16.2.0.tgz"
  integrity sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yocto-queue@^1.0.0:
  version "1.1.1"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yocto-queue/-/yocto-queue-1.1.1.tgz"
  integrity sha512-b4JR1PFR10y1mKjhHY9LaGo6tmrgjit7hxVIeAmyMw3jegXR4dhYqLaQF5zMXZxY7tLpMyJeLjr1C4rLmkVe8g==

yoctocolors-cjs@^2.1.2:
  version "2.1.2"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/yoctocolors-cjs/-/yoctocolors-cjs-2.1.2.tgz"
  integrity sha512-cYVsTjKl8b+FrnidjibDWskAv7UKOfcwaVZdp/it9n1s9fU3IkgDbhdIRKCW4JDsAlECJY0ytoVPT3sK6kideA==

zone.js@~0.14.10:
  version "0.14.10"
  resolved "https://artifactory.boschdevcloud.com/artifactory/api/npm/lab000003-bci-npm-virtual/zone.js/-/zone.js-0.14.10.tgz"
  integrity sha512-YGAhaO7J5ywOXW6InXNlLmfU194F8lVgu7bRntUF3TiG8Y3nBK0x1UJJuHUP/e8IyihkjCYqhCScpSwnlaSRkQ==
