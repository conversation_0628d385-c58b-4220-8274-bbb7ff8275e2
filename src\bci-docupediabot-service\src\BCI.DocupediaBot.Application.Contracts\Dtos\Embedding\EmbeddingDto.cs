﻿using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Embedding
{
  public class OllamaEmbeddingResponse
  {
    [JsonPropertyName("embedding")]
    public float[] Embedding { get; set; }
  }

  public class OllamaChatResponse
  {
    [JsonPropertyName("message")]
    public OllamaMessage Message { get; set; }
  }

  public class OllamaMessage
  {
    [JsonPropertyName("role")]
    public string Role { get; set; }

    [JsonPropertyName("content")]
    public string Content { get; set; }
  }

  public class Id
  {
    [JsonPropertyName("num")]
    public string Num { get; set; }
  }

  public class Metadata
  {
    [JsonPropertyName("stringValue")]
    public string StringValue { get; set; }
  }

  public class url
  {
    [JsonPropertyName("stringValue")]
    public string StringValue { get; set; }
  }

  public class pointId
  {
    [JsonPropertyName("integerValue")]
    public string IntegerValue { get; set; }
  }

	public class contentId
	{
		[JsonPropertyName("stringValue")]
		public string StringValue { get; set; }
	}

	public class title
	{
		[JsonPropertyName("stringValue")]
		public string StringValue { get; set; }
	}

	public class versionNo
	{
		[JsonPropertyName("stringValue")]
		public string StringValue { get; set; }
	}

	public class Payload
  {
    [JsonPropertyName("metadata")]
    public Metadata Metadata { get; set; }
    public url url { get; set; }
		public pointId pointId { get; set; }
    public contentId contentId { get; set; }
    public title title { get; set; }
    public versionNo versionNo { get; set; }
  }

  public class DataItem
  {
    [JsonPropertyName("id")]
    public Id Id { get; set; }

    [JsonPropertyName("payload")]
    public Payload Payload { get; set; }

    [JsonPropertyName("score")]
    public double Score { get; set; }

    [JsonPropertyName("version")]
    public string Version { get; set; }
  }

  public class Result
  {
    public string Status { get; set; }
    public string OptimizerStatus { get; set; }
    public int SegmentsCount { get; set; }
    public Config Config { get; set; }
    public PayloadSchema PayloadSchema { get; set; }
    public int VectorsCount { get; set; }
    public int IndexedVectorsCount { get; set; }
    public int PointsCount { get; set; }
  }

  public class Config
  {
    public Params Params { get; set; }
    public HnswConfig HnswConfig { get; set; }
    public OptimizerConfig OptimizerConfig { get; set; }
    public WalConfig WalConfig { get; set; }
    public QuantizationConfig QuantizationConfig { get; set; }
  }

  public class Params
  {

  }

  public class HnswConfig
  {
    public int M { get; set; }
    public int EfConstruct { get; set; }
    public int FullScanThreshold { get; set; }
  }

  public class OptimizerConfig
  {
    public double DeletedThreshold { get; set; }
    public int VacuumMinVectorNumber { get; set; }
    public int DefaultSegmentNumber { get; set; }
    public int FlushIntervalSec { get; set; }
  }

  public class WalConfig
  {
    public int WalCapacityMb { get; set; }
    public int WalSegmentsAhead { get; set; }
  }

  public class QuantizationConfig
  {
    public Scalar Scalar { get; set; }
  }

  public class Scalar
  {
    public string Type { get; set; }
  }

  public class PayloadSchema
  {
    public Key Key { get; set; }
  }

  public class Key
  {
    public string DataType { get; set; }
    public int Points { get; set; }
  }

  public class PointId
  {
    [JsonPropertyName("result")]
    public List<Result> Result { get; set; }

    [JsonPropertyName("time")]
    public double Time { get; set; }
  }

}
