import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ApiResponse, ChatCompletionDto } from '@shared/models/docupedia.model';
import { ChatModel, EmbeddingModel } from '@shared/enums';

@Injectable({
  providedIn: 'root'
})
export class ChatbotService {
  private apiUrl = `${environment.baseUrl}/api`;

  constructor(private http: HttpClient) {}

  sendChatMessage(userMessage: string, chatModel: ChatModel, embeddingModel: EmbeddingModel, collectionId: string, context: string): Observable<string> {
    const payload: ChatCompletionDto & { context: string } = {
      userMessage: userMessage,
      chatModel: chatModel,
      embeddingModel: embeddingModel,
      collectionId: collectionId,
      context: context,
    };

    return this.http.post<ApiResponse<string>>(`${this.apiUrl}/chat/ask`, payload).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }
}
