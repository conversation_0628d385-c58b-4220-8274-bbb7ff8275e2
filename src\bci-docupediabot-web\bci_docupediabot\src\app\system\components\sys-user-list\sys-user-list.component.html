<bci-page-content>
  <bci-master-view [isPadded]="false">
    <bci-commandbar
      [itemCount]="itemCount"
      [selectedItemCount]="selectedUsers.length"
      [showSearch]="true"
      [showAdd]="false"
      [showDelete]="false"
      class="commandbar-padding"
      (search)="onSearchChange($event)">
    </bci-commandbar>

    <div *ngIf="selectedUsers.length > 0" class="batch-actions">
      <button type="button" mat-raised-button color="primary" (click)="assignGroupsToSelected()">
        Assign Groups to Selected Users ({{ selectedUsers.length }})
      </button>
    </div>

    <h6>User Management: Manage system users and their group assignments.</h6>

    <mat-table [dataSource]="filteredUsers" class="user-table">
      <!-- Selection Column -->
      <ng-container matColumnDef="select">
        <mat-header-cell *matHeaderCellDef>
          <mat-checkbox
            (change)="$event ? masterToggle() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()">
          </mat-checkbox>
        </mat-header-cell>
        <mat-cell *matCellDef="let user">
          <mat-checkbox
            (click)="$event.stopPropagation()"
            (change)="$event ? selection.toggle(user) : null"
            [checked]="selection.isSelected(user)">
          </mat-checkbox>
        </mat-cell>
      </ng-container>

      <!-- User NT Account Column -->
      <ng-container matColumnDef="userNTAccount">
        <mat-header-cell *matHeaderCellDef>NT Account</mat-header-cell>
        <mat-cell *matCellDef="let user">{{ user.userNTAccount }}</mat-cell>
      </ng-container>

      <!-- User Name Column -->
      <ng-container matColumnDef="userName">
        <mat-header-cell *matHeaderCellDef>User Name</mat-header-cell>
        <mat-cell *matCellDef="let user">{{ user.userName }}</mat-cell>
      </ng-container>

      <!-- Given Name Column -->
      <ng-container matColumnDef="givenName">
        <mat-header-cell *matHeaderCellDef>Given Name</mat-header-cell>
        <mat-cell *matCellDef="let user">{{ user.givenName }}</mat-cell>
      </ng-container>



      <!-- Department Column -->
      <ng-container matColumnDef="department">
        <mat-header-cell *matHeaderCellDef>Department</mat-header-cell>
        <mat-cell *matCellDef="let user">{{ user.department }}</mat-cell>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <mat-header-cell *matHeaderCellDef>Status</mat-header-cell>
        <mat-cell *matCellDef="let user">
          <span class="status-badge" [class.active]="user.status === 1" [class.inactive]="user.status !== 1">
            {{ user.status === 1 ? 'Active' : 'Inactive' }}
          </span>
        </mat-cell>
      </ng-container>

      <!-- Groups Column -->
      <ng-container matColumnDef="groups">
        <mat-header-cell *matHeaderCellDef>Groups</mat-header-cell>
        <mat-cell *matCellDef="let user">
          <div class="groups-container">
            <span *ngFor="let group of user.groups; let last = last" class="group-tag">
              {{ group.name }}<span *ngIf="!last">, </span>
            </span>
          </div>
        </mat-cell>
      </ng-container>



      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
    </mat-table>

    <div *ngIf="loading" class="loading-container">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div *ngIf="!loading && filteredUsers.length === 0" class="no-data-container">
      <p>No users found.</p>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container" *ngIf="itemCount > 0">
      <bci-paginator [showPageSizeSelector]="true" [length]="itemCount || 0" [pageIndex]="pageNumber || 0"
        [pageSize]="pageSize || 1" [pageSizeOptions]="pageSizeOptions" (page)="onPageChange($any($event).detail)">
      </bci-paginator>
    </div>
  </bci-master-view>
</bci-page-content>
