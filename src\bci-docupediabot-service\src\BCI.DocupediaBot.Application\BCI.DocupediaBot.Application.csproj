<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.61" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Qdrant.Client" Version="1.13.0" />
    <PackageReference Include="Quartz" Version="3.14.0" />
    <PackageReference Include="SharpToken" Version="2.0.3" />
    <PackageReference Include="System.DirectoryServices" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BCI.DocupediaBot.Application.Contracts\BCI.DocupediaBot.Application.Contracts.csproj" />
    <ProjectReference Include="..\BCI.DocupediaBot.Domain\BCI.DocupediaBot.Domain.csproj" />
    <ProjectReference Include="..\BCI.DocupediaBot.Infrastructure\BCI.DocupediaBot.Infrastructure.csproj" />
    <ProjectReference Include="..\BCI.DocupediaBot.Persistence.EF\BCI.DocupediaBot.Persistence.EF.csproj" />
  </ItemGroup>

</Project>
