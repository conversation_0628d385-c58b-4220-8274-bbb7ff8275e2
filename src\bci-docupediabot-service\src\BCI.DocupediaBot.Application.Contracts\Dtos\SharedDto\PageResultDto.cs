﻿using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.SharedDto
{
  [ExcludeFromCodeCoverage]
  public class PageResultDTO<T>
  {
    public IList<T> Items { get; set; }
    public int? PageNumber { get; set; }
    public int? TotalPages
    {
      get
      {
        if (!TotalCount.HasValue || !PageSize.HasValue)
        {
          return null;
        }

        if (PageSize <= 0 || TotalCount <= 0)
        {
          return 0;
        }

        double pageCount = (double)TotalCount.Value / PageSize.Value;
        pageCount = Math.Ceiling(pageCount);

        return (int)pageCount;
      }
    }
    public int? PageSize { get; set; }
    public long? TotalCount { get; set; }

  }
}
