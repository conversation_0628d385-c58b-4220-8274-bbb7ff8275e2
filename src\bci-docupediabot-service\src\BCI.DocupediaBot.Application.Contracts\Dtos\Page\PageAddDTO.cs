﻿using System;

namespace BCI.DocupediaBot.Application.Contracts.Dtos.Page
{
  public class PageAddDTO
	{
		public string CollectionId { get; set; } = default!;
		public string Title { get; set; } = default!;
		public string Url { get; set; } = default!;
		public bool IsIncludeChild { get; set; } = true;
		public string? SourceId { get; set; }

		public string? UrlType { get; set; }
		public string? UrlData { get; set; }
		public string? UserToken { get; set; }
	}
}
