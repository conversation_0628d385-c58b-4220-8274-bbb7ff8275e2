{"version": 3, "sources": ["../../../../../../node_modules/prismjs/prism.js"], "sourcesContent": ["/* **********************************************\n     Begin prism-core.js\n********************************************** */\n\n/// <reference lib=\"WebWorker\"/>\n\nvar _self = typeof window !== 'undefined' ? window // if in browser\n: typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope ? self // if in worker\n: {} // if in node js\n;\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = function (_self) {\n  // Private helper vars\n  var lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n  var uniqueId = 0;\n\n  // The grammar object for plaintext\n  var plainTextGrammar = {};\n  var _ = {\n    /**\n     * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n     * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n     * additional languages or plugins yourself.\n     *\n     * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n     *\n     * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n     * empty Prism object into the global scope before loading the Prism script like this:\n     *\n     * ```js\n     * window.Prism = window.Prism || {};\n     * Prism.manual = true;\n     * // add a new <script> to load Prism's script\n     * ```\n     *\n     * @default false\n     * @type {boolean}\n     * @memberof Prism\n     * @public\n     */\n    manual: _self.Prism && _self.Prism.manual,\n    /**\n     * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses\n     * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your\n     * own worker, you don't want it to do this.\n     *\n     * By setting this value to `true`, Prism will not add its own listeners to the worker.\n     *\n     * You obviously have to change this value before Prism executes. To do this, you can add an\n     * empty Prism object into the global scope before loading the Prism script like this:\n     *\n     * ```js\n     * window.Prism = window.Prism || {};\n     * Prism.disableWorkerMessageHandler = true;\n     * // Load Prism's script\n     * ```\n     *\n     * @default false\n     * @type {boolean}\n     * @memberof Prism\n     * @public\n     */\n    disableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n    /**\n     * A namespace for utility methods.\n     *\n     * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n     * change or disappear at any time.\n     *\n     * @namespace\n     * @memberof Prism\n     */\n    util: {\n      encode: function encode(tokens) {\n        if (tokens instanceof Token) {\n          return new Token(tokens.type, encode(tokens.content), tokens.alias);\n        } else if (Array.isArray(tokens)) {\n          return tokens.map(encode);\n        } else {\n          return tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n        }\n      },\n      /**\n       * Returns the name of the type of the given value.\n       *\n       * @param {any} o\n       * @returns {string}\n       * @example\n       * type(null)      === 'Null'\n       * type(undefined) === 'Undefined'\n       * type(123)       === 'Number'\n       * type('foo')     === 'String'\n       * type(true)      === 'Boolean'\n       * type([1, 2])    === 'Array'\n       * type({})        === 'Object'\n       * type(String)    === 'Function'\n       * type(/abc+/)    === 'RegExp'\n       */\n      type: function (o) {\n        return Object.prototype.toString.call(o).slice(8, -1);\n      },\n      /**\n       * Returns a unique number for the given object. Later calls will still return the same number.\n       *\n       * @param {Object} obj\n       * @returns {number}\n       */\n      objId: function (obj) {\n        if (!obj['__id']) {\n          Object.defineProperty(obj, '__id', {\n            value: ++uniqueId\n          });\n        }\n        return obj['__id'];\n      },\n      /**\n       * Creates a deep clone of the given object.\n       *\n       * The main intended use of this function is to clone language definitions.\n       *\n       * @param {T} o\n       * @param {Record<number, any>} [visited]\n       * @returns {T}\n       * @template T\n       */\n      clone: function deepClone(o, visited) {\n        visited = visited || {};\n        var clone;\n        var id;\n        switch (_.util.type(o)) {\n          case 'Object':\n            id = _.util.objId(o);\n            if (visited[id]) {\n              return visited[id];\n            }\n            clone = /** @type {Record<string, any>} */{};\n            visited[id] = clone;\n            for (var key in o) {\n              if (o.hasOwnProperty(key)) {\n                clone[key] = deepClone(o[key], visited);\n              }\n            }\n            return /** @type {any} */clone;\n          case 'Array':\n            id = _.util.objId(o);\n            if (visited[id]) {\n              return visited[id];\n            }\n            clone = [];\n            visited[id] = clone;\n            ( /** @type {Array} */ /** @type {any} */o).forEach(function (v, i) {\n              clone[i] = deepClone(v, visited);\n            });\n            return /** @type {any} */clone;\n          default:\n            return o;\n        }\n      },\n      /**\n       * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n       *\n       * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n       *\n       * @param {Element} element\n       * @returns {string}\n       */\n      getLanguage: function (element) {\n        while (element) {\n          var m = lang.exec(element.className);\n          if (m) {\n            return m[1].toLowerCase();\n          }\n          element = element.parentElement;\n        }\n        return 'none';\n      },\n      /**\n       * Sets the Prism `language-xxxx` class of the given element.\n       *\n       * @param {Element} element\n       * @param {string} language\n       * @returns {void}\n       */\n      setLanguage: function (element, language) {\n        // remove all `language-xxxx` classes\n        // (this might leave behind a leading space)\n        element.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n        // add the new `language-xxxx` class\n        // (using `classList` will automatically clean up spaces for us)\n        element.classList.add('language-' + language);\n      },\n      /**\n       * Returns the script element that is currently executing.\n       *\n       * This does __not__ work for line script element.\n       *\n       * @returns {HTMLScriptElement | null}\n       */\n      currentScript: function () {\n        if (typeof document === 'undefined') {\n          return null;\n        }\n        if (document.currentScript && document.currentScript.tagName === 'SCRIPT' && 1 < 2 /* hack to trip TS' flow analysis */) {\n          return /** @type {any} */document.currentScript;\n        }\n\n        // IE11 workaround\n        // we'll get the src of the current script by parsing IE11's error stack trace\n        // this will not work for inline scripts\n\n        try {\n          throw new Error();\n        } catch (err) {\n          // Get file src url from stack. Specifically works with the format of stack traces in IE.\n          // A stack will look like this:\n          //\n          // Error\n          //    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n          //    at Global code (http://localhost/components/prism-core.js:606:1)\n\n          var src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n          if (src) {\n            var scripts = document.getElementsByTagName('script');\n            for (var i in scripts) {\n              if (scripts[i].src == src) {\n                return scripts[i];\n              }\n            }\n          }\n          return null;\n        }\n      },\n      /**\n       * Returns whether a given class is active for `element`.\n       *\n       * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n       * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n       * given class is just the given class with a `no-` prefix.\n       *\n       * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n       * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n       * ancestors have the given class or the negated version of it, then the default activation will be returned.\n       *\n       * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n       * version of it, the class is considered active.\n       *\n       * @param {Element} element\n       * @param {string} className\n       * @param {boolean} [defaultActivation=false]\n       * @returns {boolean}\n       */\n      isActive: function (element, className, defaultActivation) {\n        var no = 'no-' + className;\n        while (element) {\n          var classList = element.classList;\n          if (classList.contains(className)) {\n            return true;\n          }\n          if (classList.contains(no)) {\n            return false;\n          }\n          element = element.parentElement;\n        }\n        return !!defaultActivation;\n      }\n    },\n    /**\n     * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n     *\n     * @namespace\n     * @memberof Prism\n     * @public\n     */\n    languages: {\n      /**\n       * The grammar for plain, unformatted text.\n       */\n      plain: plainTextGrammar,\n      plaintext: plainTextGrammar,\n      text: plainTextGrammar,\n      txt: plainTextGrammar,\n      /**\n       * Creates a deep copy of the language with the given id and appends the given tokens.\n       *\n       * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n       * will be overwritten at its original position.\n       *\n       * ## Best practices\n       *\n       * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n       * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n       * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n       *\n       * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n       * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n       *\n       * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n       * @param {Grammar} redef The new tokens to append.\n       * @returns {Grammar} The new language created.\n       * @public\n       * @example\n       * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n       *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n       *     // at its original position\n       *     'comment': { ... },\n       *     // CSS doesn't have a 'color' token, so this token will be appended\n       *     'color': /\\b(?:red|green|blue)\\b/\n       * });\n       */\n      extend: function (id, redef) {\n        var lang = _.util.clone(_.languages[id]);\n        for (var key in redef) {\n          lang[key] = redef[key];\n        }\n        return lang;\n      },\n      /**\n       * Inserts tokens _before_ another token in a language definition or any other grammar.\n       *\n       * ## Usage\n       *\n       * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n       * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n       * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n       * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n       * this:\n       *\n       * ```js\n       * Prism.languages.markup.style = {\n       *     // token\n       * };\n       * ```\n       *\n       * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n       * before existing tokens. For the CSS example above, you would use it like this:\n       *\n       * ```js\n       * Prism.languages.insertBefore('markup', 'cdata', {\n       *     'style': {\n       *         // token\n       *     }\n       * });\n       * ```\n       *\n       * ## Special cases\n       *\n       * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n       * will be ignored.\n       *\n       * This behavior can be used to insert tokens after `before`:\n       *\n       * ```js\n       * Prism.languages.insertBefore('markup', 'comment', {\n       *     'comment': Prism.languages.markup.comment,\n       *     // tokens after 'comment'\n       * });\n       * ```\n       *\n       * ## Limitations\n       *\n       * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n       * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n       * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n       * deleting properties which is necessary to insert at arbitrary positions.\n       *\n       * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n       * Instead, it will create a new object and replace all references to the target object with the new one. This\n       * can be done without temporarily deleting properties, so the iteration order is well-defined.\n       *\n       * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n       * you hold the target object in a variable, then the value of the variable will not change.\n       *\n       * ```js\n       * var oldMarkup = Prism.languages.markup;\n       * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n       *\n       * assert(oldMarkup !== Prism.languages.markup);\n       * assert(newMarkup === Prism.languages.markup);\n       * ```\n       *\n       * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n       * object to be modified.\n       * @param {string} before The key to insert before.\n       * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n       * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n       * object to be modified.\n       *\n       * Defaults to `Prism.languages`.\n       * @returns {Grammar} The new grammar object.\n       * @public\n       */\n      insertBefore: function (inside, before, insert, root) {\n        root = root || ( /** @type {any} */_.languages);\n        var grammar = root[inside];\n        /** @type {Grammar} */\n        var ret = {};\n        for (var token in grammar) {\n          if (grammar.hasOwnProperty(token)) {\n            if (token == before) {\n              for (var newToken in insert) {\n                if (insert.hasOwnProperty(newToken)) {\n                  ret[newToken] = insert[newToken];\n                }\n              }\n            }\n\n            // Do not insert token which also occur in insert. See #1525\n            if (!insert.hasOwnProperty(token)) {\n              ret[token] = grammar[token];\n            }\n          }\n        }\n        var old = root[inside];\n        root[inside] = ret;\n\n        // Update references in other language definitions\n        _.languages.DFS(_.languages, function (key, value) {\n          if (value === old && key != inside) {\n            this[key] = ret;\n          }\n        });\n        return ret;\n      },\n      // Traverse a language definition with Depth First Search\n      DFS: function DFS(o, callback, type, visited) {\n        visited = visited || {};\n        var objId = _.util.objId;\n        for (var i in o) {\n          if (o.hasOwnProperty(i)) {\n            callback.call(o, i, o[i], type || i);\n            var property = o[i];\n            var propertyType = _.util.type(property);\n            if (propertyType === 'Object' && !visited[objId(property)]) {\n              visited[objId(property)] = true;\n              DFS(property, callback, null, visited);\n            } else if (propertyType === 'Array' && !visited[objId(property)]) {\n              visited[objId(property)] = true;\n              DFS(property, callback, i, visited);\n            }\n          }\n        }\n      }\n    },\n    plugins: {},\n    /**\n     * This is the most high-level function in Prism’s API.\n     * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n     * each one of them.\n     *\n     * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n     *\n     * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n     * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n     * @memberof Prism\n     * @public\n     */\n    highlightAll: function (async, callback) {\n      _.highlightAllUnder(document, async, callback);\n    },\n    /**\n     * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n     * {@link Prism.highlightElement} on each one of them.\n     *\n     * The following hooks will be run:\n     * 1. `before-highlightall`\n     * 2. `before-all-elements-highlight`\n     * 3. All hooks of {@link Prism.highlightElement} for each element.\n     *\n     * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n     * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n     * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n     * @memberof Prism\n     * @public\n     */\n    highlightAllUnder: function (container, async, callback) {\n      var env = {\n        callback: callback,\n        container: container,\n        selector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n      };\n      _.hooks.run('before-highlightall', env);\n      env.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n      _.hooks.run('before-all-elements-highlight', env);\n      for (var i = 0, element; element = env.elements[i++];) {\n        _.highlightElement(element, async === true, env.callback);\n      }\n    },\n    /**\n     * Highlights the code inside a single element.\n     *\n     * The following hooks will be run:\n     * 1. `before-sanity-check`\n     * 2. `before-highlight`\n     * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n     * 4. `before-insert`\n     * 5. `after-highlight`\n     * 6. `complete`\n     *\n     * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n     * the element's language.\n     *\n     * @param {Element} element The element containing the code.\n     * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n     * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n     * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n     * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n     *\n     * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n     * asynchronous highlighting to work. You can build your own bundle on the\n     * [Download page](https://prismjs.com/download.html).\n     * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n     * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n     * @memberof Prism\n     * @public\n     */\n    highlightElement: function (element, async, callback) {\n      // Find language\n      var language = _.util.getLanguage(element);\n      var grammar = _.languages[language];\n\n      // Set language on the element, if not present\n      _.util.setLanguage(element, language);\n\n      // Set language on the parent, for styling\n      var parent = element.parentElement;\n      if (parent && parent.nodeName.toLowerCase() === 'pre') {\n        _.util.setLanguage(parent, language);\n      }\n      var code = element.textContent;\n      var env = {\n        element: element,\n        language: language,\n        grammar: grammar,\n        code: code\n      };\n      function insertHighlightedCode(highlightedCode) {\n        env.highlightedCode = highlightedCode;\n        _.hooks.run('before-insert', env);\n        env.element.innerHTML = env.highlightedCode;\n        _.hooks.run('after-highlight', env);\n        _.hooks.run('complete', env);\n        callback && callback.call(env.element);\n      }\n      _.hooks.run('before-sanity-check', env);\n\n      // plugins may change/add the parent/element\n      parent = env.element.parentElement;\n      if (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n        parent.setAttribute('tabindex', '0');\n      }\n      if (!env.code) {\n        _.hooks.run('complete', env);\n        callback && callback.call(env.element);\n        return;\n      }\n      _.hooks.run('before-highlight', env);\n      if (!env.grammar) {\n        insertHighlightedCode(_.util.encode(env.code));\n        return;\n      }\n      if (async && _self.Worker) {\n        var worker = new Worker(_.filename);\n        worker.onmessage = function (evt) {\n          insertHighlightedCode(evt.data);\n        };\n        worker.postMessage(JSON.stringify({\n          language: env.language,\n          code: env.code,\n          immediateClose: true\n        }));\n      } else {\n        insertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n      }\n    },\n    /**\n     * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n     * and the language definitions to use, and returns a string with the HTML produced.\n     *\n     * The following hooks will be run:\n     * 1. `before-tokenize`\n     * 2. `after-tokenize`\n     * 3. `wrap`: On each {@link Token}.\n     *\n     * @param {string} text A string with the code to be highlighted.\n     * @param {Grammar} grammar An object containing the tokens to use.\n     *\n     * Usually a language definition like `Prism.languages.markup`.\n     * @param {string} language The name of the language definition passed to `grammar`.\n     * @returns {string} The highlighted HTML.\n     * @memberof Prism\n     * @public\n     * @example\n     * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n     */\n    highlight: function (text, grammar, language) {\n      var env = {\n        code: text,\n        grammar: grammar,\n        language: language\n      };\n      _.hooks.run('before-tokenize', env);\n      if (!env.grammar) {\n        throw new Error('The language \"' + env.language + '\" has no grammar.');\n      }\n      env.tokens = _.tokenize(env.code, env.grammar);\n      _.hooks.run('after-tokenize', env);\n      return Token.stringify(_.util.encode(env.tokens), env.language);\n    },\n    /**\n     * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n     * and the language definitions to use, and returns an array with the tokenized code.\n     *\n     * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n     *\n     * This method could be useful in other contexts as well, as a very crude parser.\n     *\n     * @param {string} text A string with the code to be highlighted.\n     * @param {Grammar} grammar An object containing the tokens to use.\n     *\n     * Usually a language definition like `Prism.languages.markup`.\n     * @returns {TokenStream} An array of strings and tokens, a token stream.\n     * @memberof Prism\n     * @public\n     * @example\n     * let code = `var foo = 0;`;\n     * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n     * tokens.forEach(token => {\n     *     if (token instanceof Prism.Token && token.type === 'number') {\n     *         console.log(`Found numeric literal: ${token.content}`);\n     *     }\n     * });\n     */\n    tokenize: function (text, grammar) {\n      var rest = grammar.rest;\n      if (rest) {\n        for (var token in rest) {\n          grammar[token] = rest[token];\n        }\n        delete grammar.rest;\n      }\n      var tokenList = new LinkedList();\n      addAfter(tokenList, tokenList.head, text);\n      matchGrammar(text, tokenList, grammar, tokenList.head, 0);\n      return toArray(tokenList);\n    },\n    /**\n     * @namespace\n     * @memberof Prism\n     * @public\n     */\n    hooks: {\n      all: {},\n      /**\n       * Adds the given callback to the list of callbacks for the given hook.\n       *\n       * The callback will be invoked when the hook it is registered for is run.\n       * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n       *\n       * One callback function can be registered to multiple hooks and the same hook multiple times.\n       *\n       * @param {string} name The name of the hook.\n       * @param {HookCallback} callback The callback function which is given environment variables.\n       * @public\n       */\n      add: function (name, callback) {\n        var hooks = _.hooks.all;\n        hooks[name] = hooks[name] || [];\n        hooks[name].push(callback);\n      },\n      /**\n       * Runs a hook invoking all registered callbacks with the given environment variables.\n       *\n       * Callbacks will be invoked synchronously and in the order in which they were registered.\n       *\n       * @param {string} name The name of the hook.\n       * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n       * @public\n       */\n      run: function (name, env) {\n        var callbacks = _.hooks.all[name];\n        if (!callbacks || !callbacks.length) {\n          return;\n        }\n        for (var i = 0, callback; callback = callbacks[i++];) {\n          callback(env);\n        }\n      }\n    },\n    Token: Token\n  };\n  _self.Prism = _;\n\n  // Typescript note:\n  // The following can be used to import the Token type in JSDoc:\n  //\n  //   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n  /**\n   * Creates a new token.\n   *\n   * @param {string} type See {@link Token#type type}\n   * @param {string | TokenStream} content See {@link Token#content content}\n   * @param {string|string[]} [alias] The alias(es) of the token.\n   * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n   * @class\n   * @global\n   * @public\n   */\n  function Token(type, content, alias, matchedStr) {\n    /**\n     * The type of the token.\n     *\n     * This is usually the key of a pattern in a {@link Grammar}.\n     *\n     * @type {string}\n     * @see GrammarToken\n     * @public\n     */\n    this.type = type;\n    /**\n     * The strings or tokens contained by this token.\n     *\n     * This will be a token stream if the pattern matched also defined an `inside` grammar.\n     *\n     * @type {string | TokenStream}\n     * @public\n     */\n    this.content = content;\n    /**\n     * The alias(es) of the token.\n     *\n     * @type {string|string[]}\n     * @see GrammarToken\n     * @public\n     */\n    this.alias = alias;\n    // Copy of the full string this token was created from\n    this.length = (matchedStr || '').length | 0;\n  }\n\n  /**\n   * A token stream is an array of strings and {@link Token Token} objects.\n   *\n   * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n   * them.\n   *\n   * 1. No adjacent strings.\n   * 2. No empty strings.\n   *\n   *    The only exception here is the token stream that only contains the empty string and nothing else.\n   *\n   * @typedef {Array<string | Token>} TokenStream\n   * @global\n   * @public\n   */\n\n  /**\n   * Converts the given token or token stream to an HTML representation.\n   *\n   * The following hooks will be run:\n   * 1. `wrap`: On each {@link Token}.\n   *\n   * @param {string | Token | TokenStream} o The token or token stream to be converted.\n   * @param {string} language The name of current language.\n   * @returns {string} The HTML representation of the token or token stream.\n   * @memberof Token\n   * @static\n   */\n  Token.stringify = function stringify(o, language) {\n    if (typeof o == 'string') {\n      return o;\n    }\n    if (Array.isArray(o)) {\n      var s = '';\n      o.forEach(function (e) {\n        s += stringify(e, language);\n      });\n      return s;\n    }\n    var env = {\n      type: o.type,\n      content: stringify(o.content, language),\n      tag: 'span',\n      classes: ['token', o.type],\n      attributes: {},\n      language: language\n    };\n    var aliases = o.alias;\n    if (aliases) {\n      if (Array.isArray(aliases)) {\n        Array.prototype.push.apply(env.classes, aliases);\n      } else {\n        env.classes.push(aliases);\n      }\n    }\n    _.hooks.run('wrap', env);\n    var attributes = '';\n    for (var name in env.attributes) {\n      attributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n    }\n    return '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n  };\n\n  /**\n   * @param {RegExp} pattern\n   * @param {number} pos\n   * @param {string} text\n   * @param {boolean} lookbehind\n   * @returns {RegExpExecArray | null}\n   */\n  function matchPattern(pattern, pos, text, lookbehind) {\n    pattern.lastIndex = pos;\n    var match = pattern.exec(text);\n    if (match && lookbehind && match[1]) {\n      // change the match to remove the text matched by the Prism lookbehind group\n      var lookbehindLength = match[1].length;\n      match.index += lookbehindLength;\n      match[0] = match[0].slice(lookbehindLength);\n    }\n    return match;\n  }\n\n  /**\n   * @param {string} text\n   * @param {LinkedList<string | Token>} tokenList\n   * @param {any} grammar\n   * @param {LinkedListNode<string | Token>} startNode\n   * @param {number} startPos\n   * @param {RematchOptions} [rematch]\n   * @returns {void}\n   * @private\n   *\n   * @typedef RematchOptions\n   * @property {string} cause\n   * @property {number} reach\n   */\n  function matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n    for (var token in grammar) {\n      if (!grammar.hasOwnProperty(token) || !grammar[token]) {\n        continue;\n      }\n      var patterns = grammar[token];\n      patterns = Array.isArray(patterns) ? patterns : [patterns];\n      for (var j = 0; j < patterns.length; ++j) {\n        if (rematch && rematch.cause == token + ',' + j) {\n          return;\n        }\n        var patternObj = patterns[j];\n        var inside = patternObj.inside;\n        var lookbehind = !!patternObj.lookbehind;\n        var greedy = !!patternObj.greedy;\n        var alias = patternObj.alias;\n        if (greedy && !patternObj.pattern.global) {\n          // Without the global flag, lastIndex won't work\n          var flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n          patternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n        }\n\n        /** @type {RegExp} */\n        var pattern = patternObj.pattern || patternObj;\n        for (\n        // iterate the token list and keep track of the current token/string position\n        var currentNode = startNode.next, pos = startPos; currentNode !== tokenList.tail; pos += currentNode.value.length, currentNode = currentNode.next) {\n          if (rematch && pos >= rematch.reach) {\n            break;\n          }\n          var str = currentNode.value;\n          if (tokenList.length > text.length) {\n            // Something went terribly wrong, ABORT, ABORT!\n            return;\n          }\n          if (str instanceof Token) {\n            continue;\n          }\n          var removeCount = 1; // this is the to parameter of removeBetween\n          var match;\n          if (greedy) {\n            match = matchPattern(pattern, pos, text, lookbehind);\n            if (!match || match.index >= text.length) {\n              break;\n            }\n            var from = match.index;\n            var to = match.index + match[0].length;\n            var p = pos;\n\n            // find the node that contains the match\n            p += currentNode.value.length;\n            while (from >= p) {\n              currentNode = currentNode.next;\n              p += currentNode.value.length;\n            }\n            // adjust pos (and p)\n            p -= currentNode.value.length;\n            pos = p;\n\n            // the current node is a Token, then the match starts inside another Token, which is invalid\n            if (currentNode.value instanceof Token) {\n              continue;\n            }\n\n            // find the last node which is affected by this match\n            for (var k = currentNode; k !== tokenList.tail && (p < to || typeof k.value === 'string'); k = k.next) {\n              removeCount++;\n              p += k.value.length;\n            }\n            removeCount--;\n\n            // replace with the new match\n            str = text.slice(pos, p);\n            match.index -= pos;\n          } else {\n            match = matchPattern(pattern, 0, str, lookbehind);\n            if (!match) {\n              continue;\n            }\n          }\n\n          // eslint-disable-next-line no-redeclare\n          var from = match.index;\n          var matchStr = match[0];\n          var before = str.slice(0, from);\n          var after = str.slice(from + matchStr.length);\n          var reach = pos + str.length;\n          if (rematch && reach > rematch.reach) {\n            rematch.reach = reach;\n          }\n          var removeFrom = currentNode.prev;\n          if (before) {\n            removeFrom = addAfter(tokenList, removeFrom, before);\n            pos += before.length;\n          }\n          removeRange(tokenList, removeFrom, removeCount);\n          var wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n          currentNode = addAfter(tokenList, removeFrom, wrapped);\n          if (after) {\n            addAfter(tokenList, currentNode, after);\n          }\n          if (removeCount > 1) {\n            // at least one Token object was removed, so we have to do some rematching\n            // this can only happen if the current pattern is greedy\n\n            /** @type {RematchOptions} */\n            var nestedRematch = {\n              cause: token + ',' + j,\n              reach: reach\n            };\n            matchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n            // the reach might have been extended because of the rematching\n            if (rematch && nestedRematch.reach > rematch.reach) {\n              rematch.reach = nestedRematch.reach;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * @typedef LinkedListNode\n   * @property {T} value\n   * @property {LinkedListNode<T> | null} prev The previous node.\n   * @property {LinkedListNode<T> | null} next The next node.\n   * @template T\n   * @private\n   */\n\n  /**\n   * @template T\n   * @private\n   */\n  function LinkedList() {\n    /** @type {LinkedListNode<T>} */\n    var head = {\n      value: null,\n      prev: null,\n      next: null\n    };\n    /** @type {LinkedListNode<T>} */\n    var tail = {\n      value: null,\n      prev: head,\n      next: null\n    };\n    head.next = tail;\n\n    /** @type {LinkedListNode<T>} */\n    this.head = head;\n    /** @type {LinkedListNode<T>} */\n    this.tail = tail;\n    this.length = 0;\n  }\n\n  /**\n   * Adds a new node with the given value to the list.\n   *\n   * @param {LinkedList<T>} list\n   * @param {LinkedListNode<T>} node\n   * @param {T} value\n   * @returns {LinkedListNode<T>} The added node.\n   * @template T\n   */\n  function addAfter(list, node, value) {\n    // assumes that node != list.tail && values.length >= 0\n    var next = node.next;\n    var newNode = {\n      value: value,\n      prev: node,\n      next: next\n    };\n    node.next = newNode;\n    next.prev = newNode;\n    list.length++;\n    return newNode;\n  }\n  /**\n   * Removes `count` nodes after the given node. The given node will not be removed.\n   *\n   * @param {LinkedList<T>} list\n   * @param {LinkedListNode<T>} node\n   * @param {number} count\n   * @template T\n   */\n  function removeRange(list, node, count) {\n    var next = node.next;\n    for (var i = 0; i < count && next !== list.tail; i++) {\n      next = next.next;\n    }\n    node.next = next;\n    next.prev = node;\n    list.length -= i;\n  }\n  /**\n   * @param {LinkedList<T>} list\n   * @returns {T[]}\n   * @template T\n   */\n  function toArray(list) {\n    var array = [];\n    var node = list.head.next;\n    while (node !== list.tail) {\n      array.push(node.value);\n      node = node.next;\n    }\n    return array;\n  }\n  if (!_self.document) {\n    if (!_self.addEventListener) {\n      // in Node.js\n      return _;\n    }\n    if (!_.disableWorkerMessageHandler) {\n      // In worker\n      _self.addEventListener('message', function (evt) {\n        var message = JSON.parse(evt.data);\n        var lang = message.language;\n        var code = message.code;\n        var immediateClose = message.immediateClose;\n        _self.postMessage(_.highlight(code, _.languages[lang], lang));\n        if (immediateClose) {\n          _self.close();\n        }\n      }, false);\n    }\n    return _;\n  }\n\n  // Get current script and highlight\n  var script = _.util.currentScript();\n  if (script) {\n    _.filename = script.src;\n    if (script.hasAttribute('data-manual')) {\n      _.manual = true;\n    }\n  }\n  function highlightAutomaticallyCallback() {\n    if (!_.manual) {\n      _.highlightAll();\n    }\n  }\n  if (!_.manual) {\n    // If the document state is \"loading\", then we'll use DOMContentLoaded.\n    // If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n    // DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n    // might take longer one animation frame to execute which can create a race condition where only some plugins have\n    // been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n    // See https://github.com/PrismJS/prism/issues/2102\n    var readyState = document.readyState;\n    if (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n      document.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n    } else {\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(highlightAutomaticallyCallback);\n      } else {\n        window.setTimeout(highlightAutomaticallyCallback, 16);\n      }\n    }\n  }\n  return _;\n}(_self);\nif (typeof module !== 'undefined' && module.exports) {\n  module.exports = Prism;\n}\n\n// hack for components to work correctly in node.js\nif (typeof global !== 'undefined') {\n  global.Prism = Prism;\n}\n\n// some additional documentation/types\n\n/**\n * The expansion of a simple `RegExp` literal to support additional properties.\n *\n * @typedef GrammarToken\n * @property {RegExp} pattern The regular expression of the token.\n * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n * @property {boolean} [greedy=false] Whether the token is greedy.\n * @property {string|string[]} [alias] An optional alias or list of aliases.\n * @property {Grammar} [inside] The nested grammar of this token.\n *\n * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n *\n * This can be used to make nested and even recursive language definitions.\n *\n * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n * each another.\n * @global\n * @public\n */\n\n/**\n * @typedef Grammar\n * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n * @global\n * @public\n */\n\n/**\n * A function which will invoked after an element was successfully highlighted.\n *\n * @callback HighlightCallback\n * @param {Element} element The element successfully highlighted.\n * @returns {void}\n * @global\n * @public\n */\n\n/**\n * @callback HookCallback\n * @param {Object<string, any>} env The environment variables of the hook.\n * @returns {void}\n * @global\n * @public\n */\n\n/* **********************************************\n     Begin prism-markup.js\n********************************************** */\n\nPrism.languages.markup = {\n  'comment': {\n    pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n    greedy: true\n  },\n  'prolog': {\n    pattern: /<\\?[\\s\\S]+?\\?>/,\n    greedy: true\n  },\n  'doctype': {\n    // https://www.w3.org/TR/xml/#NT-doctypedecl\n    pattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n    greedy: true,\n    inside: {\n      'internal-subset': {\n        pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n        lookbehind: true,\n        greedy: true,\n        inside: null // see below\n      },\n      'string': {\n        pattern: /\"[^\"]*\"|'[^']*'/,\n        greedy: true\n      },\n      'punctuation': /^<!|>$|[[\\]]/,\n      'doctype-tag': /^DOCTYPE/i,\n      'name': /[^\\s<>'\"]+/\n    }\n  },\n  'cdata': {\n    pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n    greedy: true\n  },\n  'tag': {\n    pattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n    greedy: true,\n    inside: {\n      'tag': {\n        pattern: /^<\\/?[^\\s>\\/]+/,\n        inside: {\n          'punctuation': /^<\\/?/,\n          'namespace': /^[^\\s>\\/:]+:/\n        }\n      },\n      'special-attr': [],\n      'attr-value': {\n        pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n        inside: {\n          'punctuation': [{\n            pattern: /^=/,\n            alias: 'attr-equals'\n          }, {\n            pattern: /^(\\s*)[\"']|[\"']$/,\n            lookbehind: true\n          }]\n        }\n      },\n      'punctuation': /\\/?>/,\n      'attr-name': {\n        pattern: /[^\\s>\\/]+/,\n        inside: {\n          'namespace': /^[^\\s>\\/:]+:/\n        }\n      }\n    }\n  },\n  'entity': [{\n    pattern: /&[\\da-z]{1,8};/i,\n    alias: 'named-entity'\n  }, /&#x?[\\da-f]{1,8};/i]\n};\nPrism.languages.markup['tag'].inside['attr-value'].inside['entity'] = Prism.languages.markup['entity'];\nPrism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n// Plugin to make entity title show the real entity, idea by Roman Komarov\nPrism.hooks.add('wrap', function (env) {\n  if (env.type === 'entity') {\n    env.attributes['title'] = env.content.replace(/&amp;/, '&');\n  }\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n  /**\n   * Adds an inlined language to markup.\n   *\n   * An example of an inlined language is CSS with `<style>` tags.\n   *\n   * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n   * case insensitive.\n   * @param {string} lang The language key.\n   * @example\n   * addInlined('style', 'css');\n   */\n  value: function addInlined(tagName, lang) {\n    var includedCdataInside = {};\n    includedCdataInside['language-' + lang] = {\n      pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n      lookbehind: true,\n      inside: Prism.languages[lang]\n    };\n    includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n    var inside = {\n      'included-cdata': {\n        pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n        inside: includedCdataInside\n      }\n    };\n    inside['language-' + lang] = {\n      pattern: /[\\s\\S]+/,\n      inside: Prism.languages[lang]\n    };\n    var def = {};\n    def[tagName] = {\n      pattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () {\n        return tagName;\n      }), 'i'),\n      lookbehind: true,\n      greedy: true,\n      inside: inside\n    };\n    Prism.languages.insertBefore('markup', 'cdata', def);\n  }\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n  /**\n   * Adds an pattern to highlight languages embedded in HTML attributes.\n   *\n   * An example of an inlined language is CSS with `style` attributes.\n   *\n   * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n   * case insensitive.\n   * @param {string} lang The language key.\n   * @example\n   * addAttribute('style', 'css');\n   */\n  value: function (attrName, lang) {\n    Prism.languages.markup.tag.inside['special-attr'].push({\n      pattern: RegExp(/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source, 'i'),\n      lookbehind: true,\n      inside: {\n        'attr-name': /^[^\\s=]+/,\n        'attr-value': {\n          pattern: /=[\\s\\S]+/,\n          inside: {\n            'value': {\n              pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n              lookbehind: true,\n              alias: [lang, 'language-' + lang],\n              inside: Prism.languages[lang]\n            },\n            'punctuation': [{\n              pattern: /^=/,\n              alias: 'attr-equals'\n            }, /\"|'/]\n          }\n        }\n      }\n    });\n  }\n});\nPrism.languages.html = Prism.languages.markup;\nPrism.languages.mathml = Prism.languages.markup;\nPrism.languages.svg = Prism.languages.markup;\nPrism.languages.xml = Prism.languages.extend('markup', {});\nPrism.languages.ssml = Prism.languages.xml;\nPrism.languages.atom = Prism.languages.xml;\nPrism.languages.rss = Prism.languages.xml;\n\n/* **********************************************\n     Begin prism-css.js\n********************************************** */\n\n(function (Prism) {\n  var string = /(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/;\n  Prism.languages.css = {\n    'comment': /\\/\\*[\\s\\S]*?\\*\\//,\n    'atrule': {\n      pattern: RegExp('@[\\\\w-](?:' + /[^;{\\s\"']|\\s+(?!\\s)/.source + '|' + string.source + ')*?' + /(?:;|(?=\\s*\\{))/.source),\n      inside: {\n        'rule': /^@[\\w-]+/,\n        'selector-function-argument': {\n          pattern: /(\\bselector\\s*\\(\\s*(?![\\s)]))(?:[^()\\s]|\\s+(?![\\s)])|\\((?:[^()]|\\([^()]*\\))*\\))+(?=\\s*\\))/,\n          lookbehind: true,\n          alias: 'selector'\n        },\n        'keyword': {\n          pattern: /(^|[^\\w-])(?:and|not|only|or)(?![\\w-])/,\n          lookbehind: true\n        }\n        // See rest below\n      }\n    },\n    'url': {\n      // https://drafts.csswg.org/css-values-3/#urls\n      pattern: RegExp('\\\\burl\\\\((?:' + string.source + '|' + /(?:[^\\\\\\r\\n()\"']|\\\\[\\s\\S])*/.source + ')\\\\)', 'i'),\n      greedy: true,\n      inside: {\n        'function': /^url/i,\n        'punctuation': /^\\(|\\)$/,\n        'string': {\n          pattern: RegExp('^' + string.source + '$'),\n          alias: 'url'\n        }\n      }\n    },\n    'selector': {\n      pattern: RegExp('(^|[{}\\\\s])[^{}\\\\s](?:[^{};\"\\'\\\\s]|\\\\s+(?![\\\\s{])|' + string.source + ')*(?=\\\\s*\\\\{)'),\n      lookbehind: true\n    },\n    'string': {\n      pattern: string,\n      greedy: true\n    },\n    'property': {\n      pattern: /(^|[^-\\w\\xA0-\\uFFFF])(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*(?=\\s*:)/i,\n      lookbehind: true\n    },\n    'important': /!important\\b/i,\n    'function': {\n      pattern: /(^|[^-a-z0-9])[-a-z0-9]+(?=\\()/i,\n      lookbehind: true\n    },\n    'punctuation': /[(){};:,]/\n  };\n  Prism.languages.css['atrule'].inside.rest = Prism.languages.css;\n  var markup = Prism.languages.markup;\n  if (markup) {\n    markup.tag.addInlined('style', 'css');\n    markup.tag.addAttribute('style', 'css');\n  }\n})(Prism);\n\n/* **********************************************\n     Begin prism-clike.js\n********************************************** */\n\nPrism.languages.clike = {\n  'comment': [{\n    pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    lookbehind: true,\n    greedy: true\n  }, {\n    pattern: /(^|[^\\\\:])\\/\\/.*/,\n    lookbehind: true,\n    greedy: true\n  }],\n  'string': {\n    pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    greedy: true\n  },\n  'class-name': {\n    pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|trait)\\s+|\\bcatch\\s+\\()[\\w.\\\\]+/i,\n    lookbehind: true,\n    inside: {\n      'punctuation': /[.\\\\]/\n    }\n  },\n  'keyword': /\\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\\b/,\n  'boolean': /\\b(?:false|true)\\b/,\n  'function': /\\b\\w+(?=\\()/,\n  'number': /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n  'operator': /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/,\n  'punctuation': /[{}[\\];(),.:]/\n};\n\n/* **********************************************\n     Begin prism-javascript.js\n********************************************** */\n\nPrism.languages.javascript = Prism.languages.extend('clike', {\n  'class-name': [Prism.languages.clike['class-name'], {\n    pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$A-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\.(?:constructor|prototype))/,\n    lookbehind: true\n  }],\n  'keyword': [{\n    pattern: /((?:^|\\})\\s*)catch\\b/,\n    lookbehind: true\n  }, {\n    pattern: /(^|[^.]|\\.\\.\\.\\s*)\\b(?:as|assert(?=\\s*\\{)|async(?=\\s*(?:function\\b|\\(|[$\\w\\xA0-\\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\\s*(?:\\{|$))|for|from(?=\\s*(?:['\"]|$))|function|(?:get|set)(?=\\s*(?:[#\\[$\\w\\xA0-\\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\\b/,\n    lookbehind: true\n  }],\n  // Allow for all non-ASCII characters (See http://stackoverflow.com/a/2008444)\n  'function': /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*(?:\\.\\s*(?:apply|bind|call)\\s*)?\\()/,\n  'number': {\n    pattern: RegExp(/(^|[^\\w$])/.source + '(?:' + (\n    // constant\n    /NaN|Infinity/.source + '|' +\n    // binary integer\n    /0[bB][01]+(?:_[01]+)*n?/.source + '|' +\n    // octal integer\n    /0[oO][0-7]+(?:_[0-7]+)*n?/.source + '|' +\n    // hexadecimal integer\n    /0[xX][\\dA-Fa-f]+(?:_[\\dA-Fa-f]+)*n?/.source + '|' +\n    // decimal bigint\n    /\\d+(?:_\\d+)*n/.source + '|' +\n    // decimal number (integer or float) but no bigint\n    /(?:\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[Ee][+-]?\\d+(?:_\\d+)*)?/.source) + ')' + /(?![\\w$])/.source),\n    lookbehind: true\n  },\n  'operator': /--|\\+\\+|\\*\\*=?|=>|&&=?|\\|\\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\\.{3}|\\?\\?=?|\\?\\.?|[~:]/\n});\nPrism.languages.javascript['class-name'][0].pattern = /(\\b(?:class|extends|implements|instanceof|interface|new)\\s+)[\\w.\\\\]+/;\nPrism.languages.insertBefore('javascript', 'keyword', {\n  'regex': {\n    pattern: RegExp(\n    // lookbehind\n    // eslint-disable-next-line regexp/no-dupe-characters-character-class\n    /((?:^|[^$\\w\\xA0-\\uFFFF.\"'\\])\\s]|\\b(?:return|yield))\\s*)/.source +\n    // Regex pattern:\n    // There are 2 regex patterns here. The RegExp set notation proposal added support for nested character\n    // classes if the `v` flag is present. Unfortunately, nested CCs are both context-free and incompatible\n    // with the only syntax, so we have to define 2 different regex patterns.\n    /\\//.source + '(?:' + /(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}/.source + '|' +\n    // `v` flag syntax. This supports 3 levels of nested character classes.\n    /(?:\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.|\\[(?:[^[\\]\\\\\\r\\n]|\\\\.)*\\])*\\])*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source + ')' +\n    // lookahead\n    /(?=(?:\\s|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*(?:$|[\\r\\n,.;:})\\]]|\\/\\/))/.source),\n    lookbehind: true,\n    greedy: true,\n    inside: {\n      'regex-source': {\n        pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n        lookbehind: true,\n        alias: 'language-regex',\n        inside: Prism.languages.regex\n      },\n      'regex-delimiter': /^\\/|\\/$/,\n      'regex-flags': /^[a-z]+$/\n    }\n  },\n  // This must be declared before keyword because we use \"function\" inside the look-forward\n  'function-variable': {\n    pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*[=:]\\s*(?:async\\s*)?(?:\\bfunction\\b|(?:\\((?:[^()]|\\([^()]*\\))*\\)|(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/,\n    alias: 'function'\n  },\n  'parameter': [{\n    pattern: /(function(?:\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)?\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\))/,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }, {\n    pattern: /(^|[^$\\w\\xA0-\\uFFFF])(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=>)/i,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }, {\n    pattern: /(\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*=>)/,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }, {\n    pattern: /((?:\\b|\\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\\w\\xA0-\\uFFFF]))(?:(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*)\\(\\s*|\\]\\s*\\(\\s*)(?!\\s)(?:[^()\\s]|\\s+(?![\\s)])|\\([^()]*\\))+(?=\\s*\\)\\s*\\{)/,\n    lookbehind: true,\n    inside: Prism.languages.javascript\n  }],\n  'constant': /\\b[A-Z](?:[A-Z_]|\\dx?)*\\b/\n});\nPrism.languages.insertBefore('javascript', 'string', {\n  'hashbang': {\n    pattern: /^#!.*/,\n    greedy: true,\n    alias: 'comment'\n  },\n  'template-string': {\n    pattern: /`(?:\\\\[\\s\\S]|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}|(?!\\$\\{)[^\\\\`])*`/,\n    greedy: true,\n    inside: {\n      'template-punctuation': {\n        pattern: /^`|`$/,\n        alias: 'string'\n      },\n      'interpolation': {\n        pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^}]*\\})*\\})+\\}/,\n        lookbehind: true,\n        inside: {\n          'interpolation-punctuation': {\n            pattern: /^\\$\\{|\\}$/,\n            alias: 'punctuation'\n          },\n          rest: Prism.languages.javascript\n        }\n      },\n      'string': /[\\s\\S]+/\n    }\n  },\n  'string-property': {\n    pattern: /((?:^|[,{])[ \\t]*)([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\2)[^\\\\\\r\\n])*\\2(?=\\s*:)/m,\n    lookbehind: true,\n    greedy: true,\n    alias: 'property'\n  }\n});\nPrism.languages.insertBefore('javascript', 'operator', {\n  'literal-property': {\n    pattern: /((?:^|[,{])[ \\t]*)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*:)/m,\n    lookbehind: true,\n    alias: 'property'\n  }\n});\nif (Prism.languages.markup) {\n  Prism.languages.markup.tag.addInlined('script', 'javascript');\n\n  // add attribute support for all DOM events.\n  // https://developer.mozilla.org/en-US/docs/Web/Events#Standard_events\n  Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source, 'javascript');\n}\nPrism.languages.js = Prism.languages.javascript;\n\n/* **********************************************\n     Begin prism-file-highlight.js\n********************************************** */\n\n(function () {\n  if (typeof Prism === 'undefined' || typeof document === 'undefined') {\n    return;\n  }\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/matches#Polyfill\n  if (!Element.prototype.matches) {\n    Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\n  }\n  var LOADING_MESSAGE = 'Loading…';\n  var FAILURE_MESSAGE = function (status, message) {\n    return '✖ Error ' + status + ' while fetching file: ' + message;\n  };\n  var FAILURE_EMPTY_MESSAGE = '✖ Error: File does not exist or is empty';\n  var EXTENSIONS = {\n    'js': 'javascript',\n    'py': 'python',\n    'rb': 'ruby',\n    'ps1': 'powershell',\n    'psm1': 'powershell',\n    'sh': 'bash',\n    'bat': 'batch',\n    'h': 'c',\n    'tex': 'latex'\n  };\n  var STATUS_ATTR = 'data-src-status';\n  var STATUS_LOADING = 'loading';\n  var STATUS_LOADED = 'loaded';\n  var STATUS_FAILED = 'failed';\n  var SELECTOR = 'pre[data-src]:not([' + STATUS_ATTR + '=\"' + STATUS_LOADED + '\"])' + ':not([' + STATUS_ATTR + '=\"' + STATUS_LOADING + '\"])';\n\n  /**\n   * Loads the given file.\n   *\n   * @param {string} src The URL or path of the source file to load.\n   * @param {(result: string) => void} success\n   * @param {(reason: string) => void} error\n   */\n  function loadFile(src, success, error) {\n    var xhr = new XMLHttpRequest();\n    xhr.open('GET', src, true);\n    xhr.onreadystatechange = function () {\n      if (xhr.readyState == 4) {\n        if (xhr.status < 400 && xhr.responseText) {\n          success(xhr.responseText);\n        } else {\n          if (xhr.status >= 400) {\n            error(FAILURE_MESSAGE(xhr.status, xhr.statusText));\n          } else {\n            error(FAILURE_EMPTY_MESSAGE);\n          }\n        }\n      }\n    };\n    xhr.send(null);\n  }\n\n  /**\n   * Parses the given range.\n   *\n   * This returns a range with inclusive ends.\n   *\n   * @param {string | null | undefined} range\n   * @returns {[number, number | undefined] | undefined}\n   */\n  function parseRange(range) {\n    var m = /^\\s*(\\d+)\\s*(?:(,)\\s*(?:(\\d+)\\s*)?)?$/.exec(range || '');\n    if (m) {\n      var start = Number(m[1]);\n      var comma = m[2];\n      var end = m[3];\n      if (!comma) {\n        return [start, start];\n      }\n      if (!end) {\n        return [start, undefined];\n      }\n      return [start, Number(end)];\n    }\n    return undefined;\n  }\n  Prism.hooks.add('before-highlightall', function (env) {\n    env.selector += ', ' + SELECTOR;\n  });\n  Prism.hooks.add('before-sanity-check', function (env) {\n    var pre = /** @type {HTMLPreElement} */env.element;\n    if (pre.matches(SELECTOR)) {\n      env.code = ''; // fast-path the whole thing and go to complete\n\n      pre.setAttribute(STATUS_ATTR, STATUS_LOADING); // mark as loading\n\n      // add code element with loading message\n      var code = pre.appendChild(document.createElement('CODE'));\n      code.textContent = LOADING_MESSAGE;\n      var src = pre.getAttribute('data-src');\n      var language = env.language;\n      if (language === 'none') {\n        // the language might be 'none' because there is no language set;\n        // in this case, we want to use the extension as the language\n        var extension = (/\\.(\\w+)$/.exec(src) || [, 'none'])[1];\n        language = EXTENSIONS[extension] || extension;\n      }\n\n      // set language classes\n      Prism.util.setLanguage(code, language);\n      Prism.util.setLanguage(pre, language);\n\n      // preload the language\n      var autoloader = Prism.plugins.autoloader;\n      if (autoloader) {\n        autoloader.loadLanguages(language);\n      }\n\n      // load file\n      loadFile(src, function (text) {\n        // mark as loaded\n        pre.setAttribute(STATUS_ATTR, STATUS_LOADED);\n\n        // handle data-range\n        var range = parseRange(pre.getAttribute('data-range'));\n        if (range) {\n          var lines = text.split(/\\r\\n?|\\n/g);\n\n          // the range is one-based and inclusive on both ends\n          var start = range[0];\n          var end = range[1] == null ? lines.length : range[1];\n          if (start < 0) {\n            start += lines.length;\n          }\n          start = Math.max(0, Math.min(start - 1, lines.length));\n          if (end < 0) {\n            end += lines.length;\n          }\n          end = Math.max(0, Math.min(end, lines.length));\n          text = lines.slice(start, end).join('\\n');\n\n          // add data-start for line numbers\n          if (!pre.hasAttribute('data-start')) {\n            pre.setAttribute('data-start', String(start + 1));\n          }\n        }\n\n        // highlight code\n        code.textContent = text;\n        Prism.highlightElement(code);\n      }, function (error) {\n        // mark as failed\n        pre.setAttribute(STATUS_ATTR, STATUS_FAILED);\n        code.textContent = error;\n      });\n    }\n  });\n  Prism.plugins.fileHighlight = {\n    /**\n     * Executes the File Highlight plugin for all matching `pre` elements under the given container.\n     *\n     * Note: Elements which are already loaded or currently loading will not be touched by this method.\n     *\n     * @param {ParentNode} [container=document]\n     */\n    highlight: function highlight(container) {\n      var elements = (container || document).querySelectorAll(SELECTOR);\n      for (var i = 0, element; element = elements[i++];) {\n        Prism.highlightElement(element);\n      }\n    }\n  };\n  var logged = false;\n  /** @deprecated Use `Prism.plugins.fileHighlight.highlight` instead. */\n  Prism.fileHighlight = function () {\n    if (!logged) {\n      console.warn('Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead.');\n      logged = true;\n    }\n    Prism.plugins.fileHighlight.highlight.apply(this, arguments);\n  };\n})();"], "mappings": ";;;;;AAAA;AAAA;AAMA,QAAI,QAAQ,OAAO,WAAW,cAAc,SAC1C,OAAO,sBAAsB,eAAe,gBAAgB,oBAAoB,OAChF,CAAC;AAWH,QAAI,QAAQ,SAAUA,QAAO;AAE3B,UAAI,OAAO;AACX,UAAI,WAAW;AAGf,UAAI,mBAAmB,CAAC;AACxB,UAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBN,QAAQA,OAAM,SAASA,OAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBnC,6BAA6BA,OAAM,SAASA,OAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAUxD,MAAM;AAAA,UACJ,QAAQ,SAAS,OAAO,QAAQ;AAC9B,gBAAI,kBAAkB,OAAO;AAC3B,qBAAO,IAAI,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,KAAK;AAAA,YACpE,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,qBAAO,OAAO,IAAI,MAAM;AAAA,YAC1B,OAAO;AACL,qBAAO,OAAO,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,WAAW,GAAG;AAAA,YACnF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAiBA,MAAM,SAAU,GAAG;AACjB,mBAAO,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,UACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,OAAO,SAAU,KAAK;AACpB,gBAAI,CAAC,IAAI,MAAM,GAAG;AAChB,qBAAO,eAAe,KAAK,QAAQ;AAAA,gBACjC,OAAO,EAAE;AAAA,cACX,CAAC;AAAA,YACH;AACA,mBAAO,IAAI,MAAM;AAAA,UACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWA,OAAO,SAAS,UAAU,GAAG,SAAS;AACpC,sBAAU,WAAW,CAAC;AACtB,gBAAI;AACJ,gBAAI;AACJ,oBAAQ,EAAE,KAAK,KAAK,CAAC,GAAG;AAAA,cACtB,KAAK;AACH,qBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,oBAAI,QAAQ,EAAE,GAAG;AACf,yBAAO,QAAQ,EAAE;AAAA,gBACnB;AACA;AAAA,gBAA0C,CAAC;AAC3C,wBAAQ,EAAE,IAAI;AACd,yBAAS,OAAO,GAAG;AACjB,sBAAI,EAAE,eAAe,GAAG,GAAG;AACzB,0BAAM,GAAG,IAAI,UAAU,EAAE,GAAG,GAAG,OAAO;AAAA,kBACxC;AAAA,gBACF;AACA;AAAA;AAAA,kBAAyB;AAAA;AAAA,cAC3B,KAAK;AACH,qBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,oBAAI,QAAQ,EAAE,GAAG;AACf,yBAAO,QAAQ,EAAE;AAAA,gBACnB;AACA,wBAAQ,CAAC;AACT,wBAAQ,EAAE,IAAI;AACd;AAAA;AAAA,gBAAyC,EAAG,QAAQ,SAAU,GAAG,GAAG;AAClE,wBAAM,CAAC,IAAI,UAAU,GAAG,OAAO;AAAA,gBACjC,CAAC;AACD;AAAA;AAAA,kBAAyB;AAAA;AAAA,cAC3B;AACE,uBAAO;AAAA,YACX;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASA,aAAa,SAAU,SAAS;AAC9B,mBAAO,SAAS;AACd,kBAAI,IAAI,KAAK,KAAK,QAAQ,SAAS;AACnC,kBAAI,GAAG;AACL,uBAAO,EAAE,CAAC,EAAE,YAAY;AAAA,cAC1B;AACA,wBAAU,QAAQ;AAAA,YACpB;AACA,mBAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,aAAa,SAAU,SAAS,UAAU;AAGxC,oBAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,EAAE;AAIpE,oBAAQ,UAAU,IAAI,cAAc,QAAQ;AAAA,UAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,eAAe,WAAY;AACzB,gBAAI,OAAO,aAAa,aAAa;AACnC,qBAAO;AAAA,YACT;AACA,gBAAI,SAAS,iBAAiB,SAAS,cAAc,YAAY,YAAY,IAAI,GAAwC;AACvH;AAAA;AAAA,gBAAyB,SAAS;AAAA;AAAA,YACpC;AAMA,gBAAI;AACF,oBAAM,IAAI,MAAM;AAAA,YAClB,SAAS,KAAK;AAQZ,kBAAI,OAAO,qCAAqC,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC;AACxE,kBAAI,KAAK;AACP,oBAAI,UAAU,SAAS,qBAAqB,QAAQ;AACpD,yBAAS,KAAK,SAAS;AACrB,sBAAI,QAAQ,CAAC,EAAE,OAAO,KAAK;AACzB,2BAAO,QAAQ,CAAC;AAAA,kBAClB;AAAA,gBACF;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAoBA,UAAU,SAAU,SAAS,WAAW,mBAAmB;AACzD,gBAAI,KAAK,QAAQ;AACjB,mBAAO,SAAS;AACd,kBAAI,YAAY,QAAQ;AACxB,kBAAI,UAAU,SAAS,SAAS,GAAG;AACjC,uBAAO;AAAA,cACT;AACA,kBAAI,UAAU,SAAS,EAAE,GAAG;AAC1B,uBAAO;AAAA,cACT;AACA,wBAAU,QAAQ;AAAA,YACpB;AACA,mBAAO,CAAC,CAAC;AAAA,UACX;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,WAAW;AAAA;AAAA;AAAA;AAAA,UAIT,OAAO;AAAA,UACP,WAAW;AAAA,UACX,MAAM;AAAA,UACN,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA6BL,QAAQ,SAAU,IAAI,OAAO;AAC3B,gBAAIC,QAAO,EAAE,KAAK,MAAM,EAAE,UAAU,EAAE,CAAC;AACvC,qBAAS,OAAO,OAAO;AACrB,cAAAA,MAAK,GAAG,IAAI,MAAM,GAAG;AAAA,YACvB;AACA,mBAAOA;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA4EA,cAAc,SAAU,QAAQ,QAAQ,QAAQ,MAAM;AACpD,mBAAO;AAAA,YAA4B,EAAE;AACrC,gBAAI,UAAU,KAAK,MAAM;AAEzB,gBAAI,MAAM,CAAC;AACX,qBAAS,SAAS,SAAS;AACzB,kBAAI,QAAQ,eAAe,KAAK,GAAG;AACjC,oBAAI,SAAS,QAAQ;AACnB,2BAAS,YAAY,QAAQ;AAC3B,wBAAI,OAAO,eAAe,QAAQ,GAAG;AACnC,0BAAI,QAAQ,IAAI,OAAO,QAAQ;AAAA,oBACjC;AAAA,kBACF;AAAA,gBACF;AAGA,oBAAI,CAAC,OAAO,eAAe,KAAK,GAAG;AACjC,sBAAI,KAAK,IAAI,QAAQ,KAAK;AAAA,gBAC5B;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM,KAAK,MAAM;AACrB,iBAAK,MAAM,IAAI;AAGf,cAAE,UAAU,IAAI,EAAE,WAAW,SAAU,KAAK,OAAO;AACjD,kBAAI,UAAU,OAAO,OAAO,QAAQ;AAClC,qBAAK,GAAG,IAAI;AAAA,cACd;AAAA,YACF,CAAC;AACD,mBAAO;AAAA,UACT;AAAA;AAAA,UAEA,KAAK,SAAS,IAAI,GAAG,UAAU,MAAM,SAAS;AAC5C,sBAAU,WAAW,CAAC;AACtB,gBAAI,QAAQ,EAAE,KAAK;AACnB,qBAAS,KAAK,GAAG;AACf,kBAAI,EAAE,eAAe,CAAC,GAAG;AACvB,yBAAS,KAAK,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;AACnC,oBAAI,WAAW,EAAE,CAAC;AAClB,oBAAI,eAAe,EAAE,KAAK,KAAK,QAAQ;AACvC,oBAAI,iBAAiB,YAAY,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AAC1D,0BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,sBAAI,UAAU,UAAU,MAAM,OAAO;AAAA,gBACvC,WAAW,iBAAiB,WAAW,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AAChE,0BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,sBAAI,UAAU,UAAU,GAAG,OAAO;AAAA,gBACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAaV,cAAc,SAAU,OAAO,UAAU;AACvC,YAAE,kBAAkB,UAAU,OAAO,QAAQ;AAAA,QAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAgBA,mBAAmB,SAAU,WAAW,OAAO,UAAU;AACvD,cAAI,MAAM;AAAA,YACR;AAAA,YACA;AAAA,YACA,UAAU;AAAA,UACZ;AACA,YAAE,MAAM,IAAI,uBAAuB,GAAG;AACtC,cAAI,WAAW,MAAM,UAAU,MAAM,MAAM,IAAI,UAAU,iBAAiB,IAAI,QAAQ,CAAC;AACvF,YAAE,MAAM,IAAI,iCAAiC,GAAG;AAChD,mBAAS,IAAI,GAAG,SAAS,UAAU,IAAI,SAAS,GAAG,KAAI;AACrD,cAAE,iBAAiB,SAAS,UAAU,MAAM,IAAI,QAAQ;AAAA,UAC1D;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QA6BA,kBAAkB,SAAU,SAAS,OAAO,UAAU;AAEpD,cAAI,WAAW,EAAE,KAAK,YAAY,OAAO;AACzC,cAAI,UAAU,EAAE,UAAU,QAAQ;AAGlC,YAAE,KAAK,YAAY,SAAS,QAAQ;AAGpC,cAAI,SAAS,QAAQ;AACrB,cAAI,UAAU,OAAO,SAAS,YAAY,MAAM,OAAO;AACrD,cAAE,KAAK,YAAY,QAAQ,QAAQ;AAAA,UACrC;AACA,cAAI,OAAO,QAAQ;AACnB,cAAI,MAAM;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,mBAAS,sBAAsB,iBAAiB;AAC9C,gBAAI,kBAAkB;AACtB,cAAE,MAAM,IAAI,iBAAiB,GAAG;AAChC,gBAAI,QAAQ,YAAY,IAAI;AAC5B,cAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,cAAE,MAAM,IAAI,YAAY,GAAG;AAC3B,wBAAY,SAAS,KAAK,IAAI,OAAO;AAAA,UACvC;AACA,YAAE,MAAM,IAAI,uBAAuB,GAAG;AAGtC,mBAAS,IAAI,QAAQ;AACrB,cAAI,UAAU,OAAO,SAAS,YAAY,MAAM,SAAS,CAAC,OAAO,aAAa,UAAU,GAAG;AACzF,mBAAO,aAAa,YAAY,GAAG;AAAA,UACrC;AACA,cAAI,CAAC,IAAI,MAAM;AACb,cAAE,MAAM,IAAI,YAAY,GAAG;AAC3B,wBAAY,SAAS,KAAK,IAAI,OAAO;AACrC;AAAA,UACF;AACA,YAAE,MAAM,IAAI,oBAAoB,GAAG;AACnC,cAAI,CAAC,IAAI,SAAS;AAChB,kCAAsB,EAAE,KAAK,OAAO,IAAI,IAAI,CAAC;AAC7C;AAAA,UACF;AACA,cAAI,SAASD,OAAM,QAAQ;AACzB,gBAAI,SAAS,IAAI,OAAO,EAAE,QAAQ;AAClC,mBAAO,YAAY,SAAU,KAAK;AAChC,oCAAsB,IAAI,IAAI;AAAA,YAChC;AACA,mBAAO,YAAY,KAAK,UAAU;AAAA,cAChC,UAAU,IAAI;AAAA,cACd,MAAM,IAAI;AAAA,cACV,gBAAgB;AAAA,YAClB,CAAC,CAAC;AAAA,UACJ,OAAO;AACL,kCAAsB,EAAE,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,QAAQ,CAAC;AAAA,UACxE;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAqBA,WAAW,SAAU,MAAM,SAAS,UAAU;AAC5C,cAAI,MAAM;AAAA,YACR,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACF;AACA,YAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,cAAI,CAAC,IAAI,SAAS;AAChB,kBAAM,IAAI,MAAM,mBAAmB,IAAI,WAAW,mBAAmB;AAAA,UACvE;AACA,cAAI,SAAS,EAAE,SAAS,IAAI,MAAM,IAAI,OAAO;AAC7C,YAAE,MAAM,IAAI,kBAAkB,GAAG;AACjC,iBAAO,MAAM,UAAU,EAAE,KAAK,OAAO,IAAI,MAAM,GAAG,IAAI,QAAQ;AAAA,QAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAyBA,UAAU,SAAU,MAAM,SAAS;AACjC,cAAI,OAAO,QAAQ;AACnB,cAAI,MAAM;AACR,qBAAS,SAAS,MAAM;AACtB,sBAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,YAC7B;AACA,mBAAO,QAAQ;AAAA,UACjB;AACA,cAAI,YAAY,IAAI,WAAW;AAC/B,mBAAS,WAAW,UAAU,MAAM,IAAI;AACxC,uBAAa,MAAM,WAAW,SAAS,UAAU,MAAM,CAAC;AACxD,iBAAO,QAAQ,SAAS;AAAA,QAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,OAAO;AAAA,UACL,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAaN,KAAK,SAAU,MAAM,UAAU;AAC7B,gBAAI,QAAQ,EAAE,MAAM;AACpB,kBAAM,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAC9B,kBAAM,IAAI,EAAE,KAAK,QAAQ;AAAA,UAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUA,KAAK,SAAU,MAAM,KAAK;AACxB,gBAAI,YAAY,EAAE,MAAM,IAAI,IAAI;AAChC,gBAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACnC;AAAA,YACF;AACA,qBAAS,IAAI,GAAG,UAAU,WAAW,UAAU,GAAG,KAAI;AACpD,uBAAS,GAAG;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,MACF;AACA,MAAAA,OAAM,QAAQ;AAkBd,eAAS,MAAM,MAAM,SAAS,OAAO,YAAY;AAU/C,aAAK,OAAO;AASZ,aAAK,UAAU;AAQf,aAAK,QAAQ;AAEb,aAAK,UAAU,cAAc,IAAI,SAAS;AAAA,MAC5C;AA8BA,YAAM,YAAY,SAAS,UAAU,GAAG,UAAU;AAChD,YAAI,OAAO,KAAK,UAAU;AACxB,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,cAAI,IAAI;AACR,YAAE,QAAQ,SAAU,GAAG;AACrB,iBAAK,UAAU,GAAG,QAAQ;AAAA,UAC5B,CAAC;AACD,iBAAO;AAAA,QACT;AACA,YAAI,MAAM;AAAA,UACR,MAAM,EAAE;AAAA,UACR,SAAS,UAAU,EAAE,SAAS,QAAQ;AAAA,UACtC,KAAK;AAAA,UACL,SAAS,CAAC,SAAS,EAAE,IAAI;AAAA,UACzB,YAAY,CAAC;AAAA,UACb;AAAA,QACF;AACA,YAAI,UAAU,EAAE;AAChB,YAAI,SAAS;AACX,cAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,kBAAM,UAAU,KAAK,MAAM,IAAI,SAAS,OAAO;AAAA,UACjD,OAAO;AACL,gBAAI,QAAQ,KAAK,OAAO;AAAA,UAC1B;AAAA,QACF;AACA,UAAE,MAAM,IAAI,QAAQ,GAAG;AACvB,YAAI,aAAa;AACjB,iBAAS,QAAQ,IAAI,YAAY;AAC/B,wBAAc,MAAM,OAAO,QAAQ,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,MAAM,QAAQ,IAAI;AAAA,QAC3F;AACA,eAAO,MAAM,IAAI,MAAM,aAAa,IAAI,QAAQ,KAAK,GAAG,IAAI,MAAM,aAAa,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM;AAAA,MACtH;AASA,eAAS,aAAa,SAAS,KAAK,MAAM,YAAY;AACpD,gBAAQ,YAAY;AACpB,YAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,YAAI,SAAS,cAAc,MAAM,CAAC,GAAG;AAEnC,cAAI,mBAAmB,MAAM,CAAC,EAAE;AAChC,gBAAM,SAAS;AACf,gBAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,gBAAgB;AAAA,QAC5C;AACA,eAAO;AAAA,MACT;AAgBA,eAAS,aAAa,MAAM,WAAW,SAAS,WAAW,UAAU,SAAS;AAC5E,iBAAS,SAAS,SAAS;AACzB,cAAI,CAAC,QAAQ,eAAe,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;AACrD;AAAA,UACF;AACA,cAAI,WAAW,QAAQ,KAAK;AAC5B,qBAAW,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AACzD,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,gBAAI,WAAW,QAAQ,SAAS,QAAQ,MAAM,GAAG;AAC/C;AAAA,YACF;AACA,gBAAI,aAAa,SAAS,CAAC;AAC3B,gBAAI,SAAS,WAAW;AACxB,gBAAI,aAAa,CAAC,CAAC,WAAW;AAC9B,gBAAI,SAAS,CAAC,CAAC,WAAW;AAC1B,gBAAI,QAAQ,WAAW;AACvB,gBAAI,UAAU,CAAC,WAAW,QAAQ,QAAQ;AAExC,kBAAI,QAAQ,WAAW,QAAQ,SAAS,EAAE,MAAM,WAAW,EAAE,CAAC;AAC9D,yBAAW,UAAU,OAAO,WAAW,QAAQ,QAAQ,QAAQ,GAAG;AAAA,YACpE;AAGA,gBAAI,UAAU,WAAW,WAAW;AACpC,qBAEI,cAAc,UAAU,MAAM,MAAM,UAAU,gBAAgB,UAAU,MAAM,OAAO,YAAY,MAAM,QAAQ,cAAc,YAAY,MAAM;AACjJ,kBAAI,WAAW,OAAO,QAAQ,OAAO;AACnC;AAAA,cACF;AACA,kBAAI,MAAM,YAAY;AACtB,kBAAI,UAAU,SAAS,KAAK,QAAQ;AAElC;AAAA,cACF;AACA,kBAAI,eAAe,OAAO;AACxB;AAAA,cACF;AACA,kBAAI,cAAc;AAClB,kBAAI;AACJ,kBAAI,QAAQ;AACV,wBAAQ,aAAa,SAAS,KAAK,MAAM,UAAU;AACnD,oBAAI,CAAC,SAAS,MAAM,SAAS,KAAK,QAAQ;AACxC;AAAA,gBACF;AACA,oBAAI,OAAO,MAAM;AACjB,oBAAI,KAAK,MAAM,QAAQ,MAAM,CAAC,EAAE;AAChC,oBAAI,IAAI;AAGR,qBAAK,YAAY,MAAM;AACvB,uBAAO,QAAQ,GAAG;AAChB,gCAAc,YAAY;AAC1B,uBAAK,YAAY,MAAM;AAAA,gBACzB;AAEA,qBAAK,YAAY,MAAM;AACvB,sBAAM;AAGN,oBAAI,YAAY,iBAAiB,OAAO;AACtC;AAAA,gBACF;AAGA,yBAAS,IAAI,aAAa,MAAM,UAAU,SAAS,IAAI,MAAM,OAAO,EAAE,UAAU,WAAW,IAAI,EAAE,MAAM;AACrG;AACA,uBAAK,EAAE,MAAM;AAAA,gBACf;AACA;AAGA,sBAAM,KAAK,MAAM,KAAK,CAAC;AACvB,sBAAM,SAAS;AAAA,cACjB,OAAO;AACL,wBAAQ,aAAa,SAAS,GAAG,KAAK,UAAU;AAChD,oBAAI,CAAC,OAAO;AACV;AAAA,gBACF;AAAA,cACF;AAGA,kBAAI,OAAO,MAAM;AACjB,kBAAI,WAAW,MAAM,CAAC;AACtB,kBAAI,SAAS,IAAI,MAAM,GAAG,IAAI;AAC9B,kBAAI,QAAQ,IAAI,MAAM,OAAO,SAAS,MAAM;AAC5C,kBAAI,QAAQ,MAAM,IAAI;AACtB,kBAAI,WAAW,QAAQ,QAAQ,OAAO;AACpC,wBAAQ,QAAQ;AAAA,cAClB;AACA,kBAAI,aAAa,YAAY;AAC7B,kBAAI,QAAQ;AACV,6BAAa,SAAS,WAAW,YAAY,MAAM;AACnD,uBAAO,OAAO;AAAA,cAChB;AACA,0BAAY,WAAW,YAAY,WAAW;AAC9C,kBAAI,UAAU,IAAI,MAAM,OAAO,SAAS,EAAE,SAAS,UAAU,MAAM,IAAI,UAAU,OAAO,QAAQ;AAChG,4BAAc,SAAS,WAAW,YAAY,OAAO;AACrD,kBAAI,OAAO;AACT,yBAAS,WAAW,aAAa,KAAK;AAAA,cACxC;AACA,kBAAI,cAAc,GAAG;AAKnB,oBAAI,gBAAgB;AAAA,kBAClB,OAAO,QAAQ,MAAM;AAAA,kBACrB;AAAA,gBACF;AACA,6BAAa,MAAM,WAAW,SAAS,YAAY,MAAM,KAAK,aAAa;AAG3E,oBAAI,WAAW,cAAc,QAAQ,QAAQ,OAAO;AAClD,0BAAQ,QAAQ,cAAc;AAAA,gBAChC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAeA,eAAS,aAAa;AAEpB,YAAI,OAAO;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAEA,YAAI,OAAO;AAAA,UACT,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AACA,aAAK,OAAO;AAGZ,aAAK,OAAO;AAEZ,aAAK,OAAO;AACZ,aAAK,SAAS;AAAA,MAChB;AAWA,eAAS,SAAS,MAAM,MAAM,OAAO;AAEnC,YAAI,OAAO,KAAK;AAChB,YAAI,UAAU;AAAA,UACZ;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACF;AACA,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK;AACL,eAAO;AAAA,MACT;AASA,eAAS,YAAY,MAAM,MAAM,OAAO;AACtC,YAAI,OAAO,KAAK;AAChB,iBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,KAAK,MAAM,KAAK;AACpD,iBAAO,KAAK;AAAA,QACd;AACA,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,UAAU;AAAA,MACjB;AAMA,eAAS,QAAQ,MAAM;AACrB,YAAI,QAAQ,CAAC;AACb,YAAI,OAAO,KAAK,KAAK;AACrB,eAAO,SAAS,KAAK,MAAM;AACzB,gBAAM,KAAK,KAAK,KAAK;AACrB,iBAAO,KAAK;AAAA,QACd;AACA,eAAO;AAAA,MACT;AACA,UAAI,CAACA,OAAM,UAAU;AACnB,YAAI,CAACA,OAAM,kBAAkB;AAE3B,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,EAAE,6BAA6B;AAElC,UAAAA,OAAM,iBAAiB,WAAW,SAAU,KAAK;AAC/C,gBAAI,UAAU,KAAK,MAAM,IAAI,IAAI;AACjC,gBAAIC,QAAO,QAAQ;AACnB,gBAAI,OAAO,QAAQ;AACnB,gBAAI,iBAAiB,QAAQ;AAC7B,YAAAD,OAAM,YAAY,EAAE,UAAU,MAAM,EAAE,UAAUC,KAAI,GAAGA,KAAI,CAAC;AAC5D,gBAAI,gBAAgB;AAClB,cAAAD,OAAM,MAAM;AAAA,YACd;AAAA,UACF,GAAG,KAAK;AAAA,QACV;AACA,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,EAAE,KAAK,cAAc;AAClC,UAAI,QAAQ;AACV,UAAE,WAAW,OAAO;AACpB,YAAI,OAAO,aAAa,aAAa,GAAG;AACtC,YAAE,SAAS;AAAA,QACb;AAAA,MACF;AACA,eAAS,iCAAiC;AACxC,YAAI,CAAC,EAAE,QAAQ;AACb,YAAE,aAAa;AAAA,QACjB;AAAA,MACF;AACA,UAAI,CAAC,EAAE,QAAQ;AAOb,YAAI,aAAa,SAAS;AAC1B,YAAI,eAAe,aAAa,eAAe,iBAAiB,UAAU,OAAO,OAAO;AACtF,mBAAS,iBAAiB,oBAAoB,8BAA8B;AAAA,QAC9E,OAAO;AACL,cAAI,OAAO,uBAAuB;AAChC,mBAAO,sBAAsB,8BAA8B;AAAA,UAC7D,OAAO;AACL,mBAAO,WAAW,gCAAgC,EAAE;AAAA,UACtD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT,EAAE,KAAK;AACP,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACnD,aAAO,UAAU;AAAA,IACnB;AAGA,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,QAAQ;AAAA,IACjB;AAuDA,UAAM,UAAU,SAAS;AAAA,MACvB,WAAW;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,WAAW;AAAA;AAAA,QAET,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,mBAAmB;AAAA,YACjB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA;AAAA,UACV;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,eAAe;AAAA,UACf,eAAe;AAAA,UACf,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,MACA,SAAS;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,eAAe;AAAA,cACf,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,gBAAgB,CAAC;AAAA,UACjB,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,eAAe,CAAC;AAAA,gBACd,SAAS;AAAA,gBACT,OAAO;AAAA,cACT,GAAG;AAAA,gBACD,SAAS;AAAA,gBACT,YAAY;AAAA,cACd,CAAC;AAAA,YACH;AAAA,UACF;AAAA,UACA,eAAe;AAAA,UACf,aAAa;AAAA,YACX,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,UAAU,CAAC;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,MACT,GAAG,oBAAoB;AAAA,IACzB;AACA,UAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,QAAQ,IAAI,MAAM,UAAU,OAAO,QAAQ;AACrG,UAAM,UAAU,OAAO,SAAS,EAAE,OAAO,iBAAiB,EAAE,SAAS,MAAM,UAAU;AAGrF,UAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,UAAI,IAAI,SAAS,UAAU;AACzB,YAAI,WAAW,OAAO,IAAI,IAAI,QAAQ,QAAQ,SAAS,GAAG;AAAA,MAC5D;AAAA,IACF,CAAC;AACD,WAAO,eAAe,MAAM,UAAU,OAAO,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAY9D,OAAO,SAAS,WAAW,SAAS,MAAM;AACxC,YAAI,sBAAsB,CAAC;AAC3B,4BAAoB,cAAc,IAAI,IAAI;AAAA,UACxC,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,MAAM,UAAU,IAAI;AAAA,QAC9B;AACA,4BAAoB,OAAO,IAAI;AAC/B,YAAI,SAAS;AAAA,UACX,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF;AACA,eAAO,cAAc,IAAI,IAAI;AAAA,UAC3B,SAAS;AAAA,UACT,QAAQ,MAAM,UAAU,IAAI;AAAA,QAC9B;AACA,YAAI,MAAM,CAAC;AACX,YAAI,OAAO,IAAI;AAAA,UACb,SAAS,OAAO,wFAAwF,OAAO,QAAQ,OAAO,WAAY;AACxI,mBAAO;AAAA,UACT,CAAC,GAAG,GAAG;AAAA,UACP,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR;AAAA,QACF;AACA,cAAM,UAAU,aAAa,UAAU,SAAS,GAAG;AAAA,MACrD;AAAA,IACF,CAAC;AACD,WAAO,eAAe,MAAM,UAAU,OAAO,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYhE,OAAO,SAAU,UAAU,MAAM;AAC/B,cAAM,UAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK;AAAA,UACrD,SAAS,OAAO,aAAa,SAAS,QAAQ,WAAW,MAAM,iDAAiD,QAAQ,GAAG;AAAA,UAC3H,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,YACb,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO,CAAC,MAAM,cAAc,IAAI;AAAA,kBAChC,QAAQ,MAAM,UAAU,IAAI;AAAA,gBAC9B;AAAA,gBACA,eAAe,CAAC;AAAA,kBACd,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT,GAAG,KAAK;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,UAAM,UAAU,OAAO,MAAM,UAAU;AACvC,UAAM,UAAU,SAAS,MAAM,UAAU;AACzC,UAAM,UAAU,MAAM,MAAM,UAAU;AACtC,UAAM,UAAU,MAAM,MAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AACzD,UAAM,UAAU,OAAO,MAAM,UAAU;AACvC,UAAM,UAAU,OAAO,MAAM,UAAU;AACvC,UAAM,UAAU,MAAM,MAAM,UAAU;AAMtC,KAAC,SAAUE,QAAO;AAChB,UAAI,SAAS;AACb,MAAAA,OAAM,UAAU,MAAM;AAAA,QACpB,WAAW;AAAA,QACX,UAAU;AAAA,UACR,SAAS,OAAO,eAAe,sBAAsB,SAAS,MAAM,OAAO,SAAS,QAAQ,kBAAkB,MAAM;AAAA,UACpH,QAAQ;AAAA,YACN,QAAQ;AAAA,YACR,8BAA8B;AAAA,cAC5B,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA;AAAA,UAEF;AAAA,QACF;AAAA,QACA,OAAO;AAAA;AAAA,UAEL,SAAS,OAAO,iBAAiB,OAAO,SAAS,MAAM,8BAA8B,SAAS,QAAQ,GAAG;AAAA,UACzG,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,YAAY;AAAA,YACZ,eAAe;AAAA,YACf,UAAU;AAAA,cACR,SAAS,OAAO,MAAM,OAAO,SAAS,GAAG;AAAA,cACzC,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA,YAAY;AAAA,UACV,SAAS,OAAO,sDAAuD,OAAO,SAAS,eAAe;AAAA,UACtG,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,aAAa;AAAA,QACb,YAAY;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,eAAe;AAAA,MACjB;AACA,MAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,OAAOA,OAAM,UAAU;AAC5D,UAAI,SAASA,OAAM,UAAU;AAC7B,UAAI,QAAQ;AACV,eAAO,IAAI,WAAW,SAAS,KAAK;AACpC,eAAO,IAAI,aAAa,SAAS,KAAK;AAAA,MACxC;AAAA,IACF,GAAG,KAAK;AAMR,UAAM,UAAU,QAAQ;AAAA,MACtB,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,UAAU;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,cAAc;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,MACA,WAAW;AAAA,MACX,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAMA,UAAM,UAAU,aAAa,MAAM,UAAU,OAAO,SAAS;AAAA,MAC3D,cAAc,CAAC,MAAM,UAAU,MAAM,YAAY,GAAG;AAAA,QAClD,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA,MACD,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,MACd,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAC;AAAA;AAAA,MAED,YAAY;AAAA,MACZ,UAAU;AAAA,QACR,SAAS,OAAO,aAAa,SAAS;AAAA,SAEtC,eAAe,SAAS;AAAA,QAExB,0BAA0B,SAAS;AAAA,QAEnC,4BAA4B,SAAS;AAAA,QAErC,sCAAsC,SAAS;AAAA,QAE/C,gBAAgB,SAAS;AAAA,QAEzB,oFAAoF,UAAU,MAAM,YAAY,MAAM;AAAA,QACtH,YAAY;AAAA,MACd;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AACD,UAAM,UAAU,WAAW,YAAY,EAAE,CAAC,EAAE,UAAU;AACtD,UAAM,UAAU,aAAa,cAAc,WAAW;AAAA,MACpD,SAAS;AAAA,QACP,SAAS;AAAA;AAAA;AAAA,UAGT,0DAA0D;AAAA;AAAA;AAAA;AAAA,UAK1D,KAAK,SAAS,QAAQ,iEAAiE,SAAS;AAAA,UAEhG,qIAAqI,SAAS;AAAA,UAE9I,kEAAkE;AAAA,QAAM;AAAA,QACxE,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ,MAAM,UAAU;AAAA,UAC1B;AAAA,UACA,mBAAmB;AAAA,UACnB,eAAe;AAAA,QACjB;AAAA,MACF;AAAA;AAAA,MAEA,qBAAqB;AAAA,QACnB,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,aAAa,CAAC;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ,MAAM,UAAU;AAAA,MAC1B,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ,MAAM,UAAU;AAAA,MAC1B,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ,MAAM,UAAU;AAAA,MAC1B,GAAG;AAAA,QACD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ,MAAM,UAAU;AAAA,MAC1B,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AACD,UAAM,UAAU,aAAa,cAAc,UAAU;AAAA,MACnD,YAAY;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,wBAAwB;AAAA,YACtB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,6BAA6B;AAAA,gBAC3B,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,MAAM,MAAM,UAAU;AAAA,YACxB;AAAA,UACF;AAAA,UACA,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,mBAAmB;AAAA,QACjB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,UAAM,UAAU,aAAa,cAAc,YAAY;AAAA,MACrD,oBAAoB;AAAA,QAClB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,QAAI,MAAM,UAAU,QAAQ;AAC1B,YAAM,UAAU,OAAO,IAAI,WAAW,UAAU,YAAY;AAI5D,YAAM,UAAU,OAAO,IAAI,aAAa,yNAAyN,QAAQ,YAAY;AAAA,IACvR;AACA,UAAM,UAAU,KAAK,MAAM,UAAU;AAMrC,KAAC,WAAY;AACX,UAAI,OAAO,UAAU,eAAe,OAAO,aAAa,aAAa;AACnE;AAAA,MACF;AAGA,UAAI,CAAC,QAAQ,UAAU,SAAS;AAC9B,gBAAQ,UAAU,UAAU,QAAQ,UAAU,qBAAqB,QAAQ,UAAU;AAAA,MACvF;AACA,UAAI,kBAAkB;AACtB,UAAI,kBAAkB,SAAU,QAAQ,SAAS;AAC/C,eAAO,aAAa,SAAS,2BAA2B;AAAA,MAC1D;AACA,UAAI,wBAAwB;AAC5B,UAAI,aAAa;AAAA,QACf,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AACA,UAAI,cAAc;AAClB,UAAI,iBAAiB;AACrB,UAAI,gBAAgB;AACpB,UAAI,gBAAgB;AACpB,UAAI,WAAW,wBAAwB,cAAc,OAAO,gBAAgB,cAAmB,cAAc,OAAO,iBAAiB;AASrI,eAAS,SAAS,KAAK,SAAS,OAAO;AACrC,YAAI,MAAM,IAAI,eAAe;AAC7B,YAAI,KAAK,OAAO,KAAK,IAAI;AACzB,YAAI,qBAAqB,WAAY;AACnC,cAAI,IAAI,cAAc,GAAG;AACvB,gBAAI,IAAI,SAAS,OAAO,IAAI,cAAc;AACxC,sBAAQ,IAAI,YAAY;AAAA,YAC1B,OAAO;AACL,kBAAI,IAAI,UAAU,KAAK;AACrB,sBAAM,gBAAgB,IAAI,QAAQ,IAAI,UAAU,CAAC;AAAA,cACnD,OAAO;AACL,sBAAM,qBAAqB;AAAA,cAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,IAAI;AAAA,MACf;AAUA,eAAS,WAAW,OAAO;AACzB,YAAI,IAAI,wCAAwC,KAAK,SAAS,EAAE;AAChE,YAAI,GAAG;AACL,cAAI,QAAQ,OAAO,EAAE,CAAC,CAAC;AACvB,cAAI,QAAQ,EAAE,CAAC;AACf,cAAI,MAAM,EAAE,CAAC;AACb,cAAI,CAAC,OAAO;AACV,mBAAO,CAAC,OAAO,KAAK;AAAA,UACtB;AACA,cAAI,CAAC,KAAK;AACR,mBAAO,CAAC,OAAO,MAAS;AAAA,UAC1B;AACA,iBAAO,CAAC,OAAO,OAAO,GAAG,CAAC;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AACA,YAAM,MAAM,IAAI,uBAAuB,SAAU,KAAK;AACpD,YAAI,YAAY,OAAO;AAAA,MACzB,CAAC;AACD,YAAM,MAAM,IAAI,uBAAuB,SAAU,KAAK;AACpD,YAAI;AAAA;AAAA,UAAmC,IAAI;AAAA;AAC3C,YAAI,IAAI,QAAQ,QAAQ,GAAG;AACzB,cAAI,OAAO;AAEX,cAAI,aAAa,aAAa,cAAc;AAG5C,cAAI,OAAO,IAAI,YAAY,SAAS,cAAc,MAAM,CAAC;AACzD,eAAK,cAAc;AACnB,cAAI,MAAM,IAAI,aAAa,UAAU;AACrC,cAAI,WAAW,IAAI;AACnB,cAAI,aAAa,QAAQ;AAGvB,gBAAI,aAAa,WAAW,KAAK,GAAG,KAAK,CAAC,EAAE,MAAM,GAAG,CAAC;AACtD,uBAAW,WAAW,SAAS,KAAK;AAAA,UACtC;AAGA,gBAAM,KAAK,YAAY,MAAM,QAAQ;AACrC,gBAAM,KAAK,YAAY,KAAK,QAAQ;AAGpC,cAAI,aAAa,MAAM,QAAQ;AAC/B,cAAI,YAAY;AACd,uBAAW,cAAc,QAAQ;AAAA,UACnC;AAGA,mBAAS,KAAK,SAAU,MAAM;AAE5B,gBAAI,aAAa,aAAa,aAAa;AAG3C,gBAAI,QAAQ,WAAW,IAAI,aAAa,YAAY,CAAC;AACrD,gBAAI,OAAO;AACT,kBAAI,QAAQ,KAAK,MAAM,WAAW;AAGlC,kBAAI,QAAQ,MAAM,CAAC;AACnB,kBAAI,MAAM,MAAM,CAAC,KAAK,OAAO,MAAM,SAAS,MAAM,CAAC;AACnD,kBAAI,QAAQ,GAAG;AACb,yBAAS,MAAM;AAAA,cACjB;AACA,sBAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,QAAQ,GAAG,MAAM,MAAM,CAAC;AACrD,kBAAI,MAAM,GAAG;AACX,uBAAO,MAAM;AAAA,cACf;AACA,oBAAM,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC;AAC7C,qBAAO,MAAM,MAAM,OAAO,GAAG,EAAE,KAAK,IAAI;AAGxC,kBAAI,CAAC,IAAI,aAAa,YAAY,GAAG;AACnC,oBAAI,aAAa,cAAc,OAAO,QAAQ,CAAC,CAAC;AAAA,cAClD;AAAA,YACF;AAGA,iBAAK,cAAc;AACnB,kBAAM,iBAAiB,IAAI;AAAA,UAC7B,GAAG,SAAU,OAAO;AAElB,gBAAI,aAAa,aAAa,aAAa;AAC3C,iBAAK,cAAc;AAAA,UACrB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,YAAM,QAAQ,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQ5B,WAAW,SAAS,UAAU,WAAW;AACvC,cAAI,YAAY,aAAa,UAAU,iBAAiB,QAAQ;AAChE,mBAAS,IAAI,GAAG,SAAS,UAAU,SAAS,GAAG,KAAI;AACjD,kBAAM,iBAAiB,OAAO;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS;AAEb,YAAM,gBAAgB,WAAY;AAChC,YAAI,CAAC,QAAQ;AACX,kBAAQ,KAAK,yFAAyF;AACtG,mBAAS;AAAA,QACX;AACA,cAAM,QAAQ,cAAc,UAAU,MAAM,MAAM,SAAS;AAAA,MAC7D;AAAA,IACF,GAAG;AAAA;AAAA;", "names": ["_self", "lang", "Prism"]}