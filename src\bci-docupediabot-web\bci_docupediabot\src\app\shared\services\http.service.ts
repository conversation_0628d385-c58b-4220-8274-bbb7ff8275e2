/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { Injectable } from '@angular/core';
import { HttpClient, HttpRequest, HttpEvent, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class HttpService {
  private httpHeader: HttpHeaders;

  constructor(private http: HttpClient) {
    this.httpHeader = new HttpHeaders({
      'Content-Type': 'application/json;charset=UTF-8',
    });
  }

  get<T>(url: string, parameters?: HttpParams, httpHeaders?: HttpHeaders, httpResponseType?: 'json'): Observable<T> {
    return this.http.get<T>(url, {
      headers: httpHeaders ?? this.httpHeader,
      params: parameters,
      responseType: httpResponseType ?? 'json',
    });
  }

  post<T>(
    url: string,
    data: unknown,
    httpHeaders?: HttpHeaders,
    httpResponseType?: 'json',
    httpObserving?: 'body'
  ): Observable<T> {
    return this.http.post<T>(url, data, {
      headers: httpHeaders ?? this.httpHeader,
      responseType: httpResponseType ?? 'json',
      observe: httpObserving ?? 'body',
    });
  }

  put<T>(url: string, model?: unknown): Observable<T> {
    return this.http.put<T>(url, model);
  }

  delete<T>(url: string): Observable<T> {
    return this.http.delete<T>(url);
  }

  upload(url: string, file: File): Observable<HttpEvent<any>> {
    const formData: FormData = new FormData();
    formData.append('file', file);

    const req = new HttpRequest('POST', url, formData, {
      reportProgress: true,
      responseType: 'json',
    });

    return this.http.request(req);
  }
}
