import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Page } from '@shared/models/docupedia.model';
import { AuthService } from '@shared/services/auth.service';
import { SysUserService } from '@shared/services/system/sys-user.service';
import { SysUserUpdate } from '@shared/models/system.model';

@Component({
  selector: 'app-docupedia-import-add-url-dialog',
  templateUrl: './docupedia-import-add-url-dialog.component.html',
  styleUrls: ['./docupedia-import-add-url-dialog.component.scss']
})
export class DocupediaImportAddUrlDialogComponent {
  pageForm: FormGroup;
  submitDisabled = true;

  constructor(
    private fb: FormBuilder,
    private _matDialogRef: MatDialogRef<DocupediaImportAddUrlDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { collectionId: string },
    private authService: AuthService,
    private sysUserService: SysUserService
  ) {
    const currentUser = this.authService.getCurrentUser();
    this.pageForm = this.fb.group({
      url: ['', [Validators.required, this.confluenceUrlValidator]],
      isIncludeChild: [true],
      docupediaToken: [currentUser?.docupediaToken || '', Validators.required]
    });

    this.pageForm.valueChanges.subscribe(() => this.updateSubmitDisabled());
    this.updateSubmitDisabled();
  }


  private confluenceUrlValidator(control: any) {
    if (!control.value) return null;

    const url = control.value.trim();


    try {
      new URL(url);
    } catch {
      return { invalidUrl: true };
    }


    if (!url.includes('inside-docupedia.bosch.com/confluence')) {
      return { invalidConfluenceDomain: true };
    }


    const urlPatterns = {
      prettyUrl: /\/display\/([^/]+)\/(.+)$/, // Pretty URL: /display/SPACE/PageTitle
      pageIdUrl: /\/pages\/viewpage\.action\?pageId=(\d+)/ // PageId URL: ?pageId=123456
    };

    const isValidType = Object.values(urlPatterns).some(pattern => pattern.test(url));

    if (!isValidType) {
      return { unsupportedUrlType: true };
    }

    return null;
  }


  private extractPageInfo(url: string): { type: string; data: any } | null {
    const urlPatterns = {
      prettyUrl: /\/display\/([^/]+)\/(.+)$/,
      pageIdUrl: /\/pages\/viewpage\.action\?pageId=(\d+)/
    };

    for (const [type, pattern] of Object.entries(urlPatterns)) {
      const match = pattern.exec(url);
      if (match) {
        switch (type) {
          case 'prettyUrl':
            return {
              type: 'prettyUrl',
              data: { spaceKey: match[1], pageTitle: decodeURIComponent(match[2].replace(/\+/g, ' ')) }
            };
          case 'pageIdUrl':
            return {
              type: 'pageIdUrl',
              data: { pageId: match[1] }
            };
        }
      }
    }
    return null;
  }

  private updateSubmitDisabled(): void {
    this.submitDisabled = this.pageForm.invalid;
  }

  save(): void {
    if (this.submitDisabled) return;

    const url = this.pageForm.get('url')?.value;
    const docupediaToken = this.pageForm.get('docupediaToken')?.value;
    const pageInfo = this.extractPageInfo(url);


    this.saveTokenToUser(docupediaToken);

    const newPage: Page = {
      collectionId: this.data.collectionId,
      title: '',
      url: url,
      isIncludeChild: this.pageForm.get('isIncludeChild')?.value,

      urlType: pageInfo?.type,
      urlData: JSON.stringify(pageInfo?.data),
      userToken: docupediaToken
    };
    this._matDialogRef.close(newPage);
  }

  private saveTokenToUser(token: string): void {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && token && token !== currentUser.docupediaToken) {
      const updatedUser: SysUserUpdate = {
        id: currentUser.id,
        userNTAccount: currentUser.userNTAccount,
        userName: currentUser.userName,
        givenName: currentUser.givenName,
        sn: currentUser.sn,
        mail: currentUser.mail,
        department: currentUser.department,
        favCollecitonId: currentUser.favCollecitonId,
        docupediaToken: token,
        status: currentUser.status
      };

      this.sysUserService.updateUser(updatedUser).subscribe({
        next: () => {

          const updatedUserDTO = {
            ...currentUser,
            docupediaToken: token
          };
          this.authService.updateCurrentUser(updatedUserDTO);
        },
        error: (error) => {
          console.error('Failed to save token to user profile', error);
        }
      });
    }
  }

  close(): void {
    this._matDialogRef.close();
  }
}
