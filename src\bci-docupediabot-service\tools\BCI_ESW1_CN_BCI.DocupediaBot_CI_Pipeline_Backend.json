{"options": [{"enabled": false, "definition": {"id": "5d58cc01-7c75-450c-be18-a388ddb129ec"}, "inputs": {"branchFilters": "[\"+refs/heads/*\"]", "additionalFields": "{}"}}, {"enabled": false, "definition": {"id": "a9db38f9-9fdc-478c-b0f9-464221e58316"}, "inputs": {"workItemType": "Bug", "assignToRequestor": "true", "additionalFields": "{}"}}], "triggers": [{"branchFilters": ["+refs/heads/develop"], "pathFilters": [], "batchChanges": false, "maxConcurrentBuildsPerBranch": 1, "pollingInterval": 0, "triggerType": 2}], "variables": {"BinaryBuildArtifactName": {"value": "BCI.DocupediaBot_linux-x64"}, "BuildConfiguration": {"value": "Release"}, "FOSSIDProjectName": {"value": "BCI-ESW-CN-BCI.DocupediaBot-Service"}, "MajorVersion": {"value": "0", "allowOverride": true}, "MinorVersion": {"value": "1", "allowOverride": true}, "ProductName": {"value": "BCI-ESW-CN-BCI.DocupediaBot-Service"}, "RunOSSCheck": {"value": "true"}, "RunSonarQube": {"value": "true"}, "SonarProductName": {"value": "BCI_CN_BCI.DocupediaBot_Service"}, "SpVersion": {"value": "0", "allowOverride": true}, "system.debug": {"value": "false", "allowOverride": true}, "WhiteSourceProductName": {"value": "BCI-ESW-CN-BCI.DocupediaBot"}, "WhiteSourceProjectName": {"value": "BCI-ESW-CN-BCI.DocupediaBot-Service"}}, "variableGroups": [{"variables": {"ArtifactoryServerApiKey": {"value": null, "isSecret": true}, "ArtifactoryServerUrl": {"value": "https://rb-artifactory.bosch.com"}, "ArtifactoryServerUser": {"value": "iin5imb"}}, "type": "Vsts", "name": "01-Artifactory variable group", "description": "", "id": 27}, {"variables": {"FossIDUrl": {"value": "https://rb-fossid.de.bosch.com/BCI"}, "FossIDUser": {"value": "bci_cicd_fossid"}, "FossidApikey": {"value": null, "isSecret": true}}, "type": "Vsts", "name": "02-FOSSID Variable group", "description": "", "id": 29}, {"variables": {"WhiteSourceUserKey": {"value": null, "isSecret": true}}, "type": "Vsts", "name": "03-Whitesource variable group", "description": "Whitesource variable group", "id": 26}], "properties": {}, "tags": [], "_links": {"self": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_apis/build/Definitions/242?revision=52"}, "web": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_build/definition?definitionId=242"}, "editor": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_build/designer?id=242&_a=edit-build-definition"}, "badge": {"href": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_apis/build/status/242"}}, "jobAuthorizationScope": 2, "jobTimeoutInMinutes": 60, "jobCancelTimeoutInMinutes": 5, "process": {"phases": [{"steps": [{"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Use .NET Core sdk", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "b0ce7256-7898-45d3-9cb5-176b752bfea6", "versionSpec": "2.*", "definitionType": "task"}, "inputs": {"packageType": "sdk", "useGlobalJson": "false", "workingDirectory": "", "version": "8.x", "vsVersion": "", "includePreviewVersions": "false", "installationPath": "$(Agent.ToolsDirectory)/dotnet", "performMultiLevelLookup": "false"}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-10] BuildType Selector ", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "2785459d-80e3-4017-9669-df1ac30cd341", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-20] Copy for OSS Check Pre-check $(build.sourcesdirectory)/_OSS", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "598e9527-98ca-4819-a39c-f9e07b9a13ce", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"ScanTargetFolder": "$(build.sourcesdirectory)/_OSS", "SourceFolder": "$(build.sourcesDirectory)"}}, {"alwaysRun": true, "condition": "succeededOrFailed()", "continueOnError": true, "displayName": "Task group: [MES 30] Restore Nuget (only new csproj+.net core format) **/*.sln", "enabled": true, "environment": {}, "inputs": {"PathToSolution": ""}, "retryCountOnTaskFailure": 0, "task": {"definitionType": "metaTask", "id": "54e29456-a109-4e01-b11c-0929b6b42353", "versionSpec": "1.*"}, "timeoutInMinutes": 0}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-40] SonarQube Pre-Build $(SonarProductName)", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "0078761f-fcf0-4bfb-b585-8168d33c7fc3", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"AdditionalProperties": "", "Sonar-ProjectKey": "$(SonarProductName)", "Sonar-ProjectName": "$(SonarProductName)", "sonarExclusions": ""}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "build", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "5541a522-603c-47ad-91fc-a4b1d163081b", "versionSpec": "2.*", "definitionType": "task"}, "inputs": {"command": "build", "publishWebProjects": "true", "projects": "**/*.c<PERSON><PERSON>j", "custom": "", "arguments": "--configuration $(BuildConfiguration)", "restoreArguments": "", "publishTestResults": "true", "testRunTitle": "", "zipAfterPublish": "true", "modifyOutputPath": "true", "selectOrConfig": "select", "feedRestore": "", "includeNuGetOrg": "true", "nugetConfigPath": "", "externalEndpoints": "", "noCache": "false", "packagesDirectory": "", "verbosityRestore": "Detailed", "searchPatternPush": "$(Build.ArtifactStagingDirectory)/*.nupkg", "nuGetFeedType": "internal", "feedPublish": "", "publishPackageMetadata": "true", "externalEndpoint": "", "searchPatternPack": "**/*.c<PERSON><PERSON>j", "configurationToPack": "$(BuildConfiguration)", "outputDir": "$(Build.ArtifactStagingDirectory)", "nobuild": "false", "includesymbols": "false", "includesource": "false", "versioningScheme": "off", "versionEnvVar": "", "requestedMajorVersion": "1", "requestedMinorVersion": "0", "requestedPatchVersion": "0", "buildProperties": "", "verbosityPack": "Detailed", "workingDirectory": ""}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "dotnet test", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "5541a522-603c-47ad-91fc-a4b1d163081b", "versionSpec": "2.*", "definitionType": "task"}, "inputs": {"command": "test", "publishWebProjects": "true", "projects": "**/*Test*.csproj", "custom": "", "arguments": "--configuration $(BuildConfiguration) --no-build --collect \"XPlat Code coverage\" --collect \"Code coverage\"", "restoreArguments": "", "publishTestResults": "true", "testRunTitle": "", "zipAfterPublish": "true", "modifyOutputPath": "true", "selectOrConfig": "select", "feedRestore": "", "includeNuGetOrg": "true", "nugetConfigPath": "", "externalEndpoints": "", "noCache": "false", "packagesDirectory": "", "verbosityRestore": "Detailed", "searchPatternPush": "$(Build.ArtifactStagingDirectory)/*.nupkg", "nuGetFeedType": "internal", "feedPublish": "", "publishPackageMetadata": "true", "externalEndpoint": "", "searchPatternPack": "**/*.c<PERSON><PERSON>j", "configurationToPack": "$(BuildConfiguration)", "outputDir": "$(Build.ArtifactStagingDirectory)", "nobuild": "false", "includesymbols": "false", "includesource": "false", "versioningScheme": "off", "versionEnvVar": "", "requestedMajorVersion": "1", "requestedMinorVersion": "0", "requestedPatchVersion": "0", "buildProperties": "", "verbosityPack": "Detailed", "workingDirectory": ""}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "dotnet publish", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "5541a522-603c-47ad-91fc-a4b1d163081b", "versionSpec": "2.*", "definitionType": "task"}, "inputs": {"command": "publish", "publishWebProjects": "true", "projects": "**/BCI.DocupediaBot.UIService.csproj", "custom": "", "arguments": " -c Release -o $(build.artifactstagingdirectory)/$(BinaryBuildArtifactName) -r linux-x64", "restoreArguments": "", "publishTestResults": "true", "testRunTitle": "", "zipAfterPublish": "false", "modifyOutputPath": "false", "selectOrConfig": "select", "feedRestore": "", "includeNuGetOrg": "true", "nugetConfigPath": "", "externalEndpoints": "", "noCache": "false", "packagesDirectory": "", "verbosityRestore": "Detailed", "searchPatternPush": "$(Build.ArtifactStagingDirectory)/*.nupkg", "nuGetFeedType": "internal", "feedPublish": "", "publishPackageMetadata": "true", "externalEndpoint": "", "searchPatternPack": "**/*.c<PERSON><PERSON>j", "configurationToPack": "$(BuildConfiguration)", "outputDir": "$(Build.ArtifactStagingDirectory)", "nobuild": "false", "includesymbols": "false", "includesource": "false", "versioningScheme": "off", "versionEnvVar": "", "requestedMajorVersion": "1", "requestedMinorVersion": "0", "requestedPatchVersion": "0", "buildProperties": "", "verbosityPack": "Detailed", "workingDirectory": ""}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "ReportGenerator", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "be803a55-9253-4895-a525-be570d86f161", "versionSpec": "5.*", "definitionType": "task"}, "inputs": {"reports": "$(Agent.TempDirectory)/*/coverage.cobertura.xml", "targetdir": "$(Build.SourcesDirectory)/CodeCoverage", "reporttypes": "HtmlInline_AzurePipelines;Cobertura", "sourcedirs": "", "historydir": "", "plugins": "", "assemblyfilters": "+*", "classfilters": "+*", "filefilters": "+*", "verbosity": "Info", "title": "", "tag": "$(build.buildnumber)_#$(build.buildid)", "license": "", "customSettings": ""}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Publish code coverage from $(Build.SourcesDirectory)/CodeCoverage/Cobertura.xml", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "2a7ebc54-c13e-490e-81a5-d7561ab7cd97", "versionSpec": "1.*", "definitionType": "task"}, "inputs": {"codeCoverageTool": "Cobertura", "summaryFileLocation": "$(Build.SourcesDirectory)/CodeCoverage/Cobertura.xml", "pathToSources": "", "reportDirectory": "$(Build.SourcesDirectory)/CodeCoverage", "additionalCodeCoverageFiles": "", "failIfCoverageEmpty": "false"}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-60] OSS Check for .net core with FOSSID and Whitesource $(ArtifactoryServerApiKey)", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "900aeaab-61d8-40a5-af36-d9bbe83c1728", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"ArtifactoryServerApiKey": "$(ArtifactoryServerApiKey)", "BreakBuildOnFindings": "false", "FossIDApiKey": "$(FossIDApiKey)", "FOSSIDProjectName": "$(FOSSIDProjectName)", "FossIDUser": "$(FossIDUser)", "ProjectIdNexusIQ": "$(WhiteSourceProjectName)", "RemoveUsings": "true", "ScanTarget": "$(build.sourcesdirectory)/_OSS", "WhiteSourceProductName": "$(WhiteSourceProductName)", "WhiteSourceProjectName": "$(WhiteSourceProjectName)", "WhiteSourceScanFolder": "$(build.artifactstagingdirectory)", "WhiteSourceUserKey": "$(WhiteSourceUserKey)"}}, {"environment": {}, "enabled": true, "continueOnError": true, "alwaysRun": true, "displayName": "Task group: [CI-70] SonarQube Post-Build true", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeededOrFailed()", "task": {"id": "79eb5356-4629-4d65-8aa6-c344cfb957ed", "versionSpec": "1.*", "definitionType": "metaTask"}, "inputs": {"PublishGateResult": "true"}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Archive $(build.artifactstagingdirectory)/$(BinaryBuildArtifactName)", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "d8b84976-e99a-4b86-b885-4849694435b0", "versionSpec": "2.*", "definitionType": "task"}, "inputs": {"rootFolderOrFile": "$(build.artifactstagingdirectory)/$(BinaryBuildArtifactName)", "includeRootFolder": "true", "archiveType": "zip", "sevenZipCompression": "normal", "tarCompression": "gz", "archiveFile": "$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip", "replaceExistingArchive": "true", "verbose": "false", "quiet": "false"}}, {"environment": {}, "enabled": true, "continueOnError": false, "alwaysRun": false, "displayName": "Publish Pipeline Artifact", "timeoutInMinutes": 0, "retryCountOnTaskFailure": 0, "condition": "succeeded()", "task": {"id": "ecdc45f6-832d-4ad9-b52b-ee49e94659be", "versionSpec": "1.*", "definitionType": "task"}, "inputs": {"path": "$(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip", "artifactName": "$(BinaryBuildArtifactName)", "artifactType": "pipeline", "fileSharePath": "", "parallel": "false", "parallelCount": "8", "properties": ""}}], "name": "Agent job 1", "refName": "Job_1", "condition": "succeeded()", "target": {"executionOptions": {"type": 0}, "allowScriptsAuthAccessOption": true, "type": 1}, "jobAuthorizationScope": 2}], "type": 1}, "repository": {"properties": {"cleanOptions": "3", "labelSources": "0", "labelSourcesFormat": "$(build.buildNumber)", "reportBuildStatus": "true", "fetchDepth": "0", "gitLfsSupport": "false", "skipSyncSource": "false", "checkoutNestedSubmodules": "false"}, "id": "c459ec08-884c-4aa7-8862-f49daea02062", "type": "TfsGit", "name": "bosch-bci-ste", "url": "https://dev.azure.com/bosch-bci-cn/BCI_ESW1_CN_ProjectCollection/_git/bosch-bci-ste", "defaultBranch": "refs/heads/develop", "clean": "true", "checkoutSubmodules": false}, "processParameters": {}, "quality": 1, "id": 242, "name": "BCI.DocupediaBot_Pipeline_Backend", "url": "https://dev.azure.com/bosch-bci-cn/416d2eff-46cd-468f-829e-07d113b877db/_apis/build/Definitions/242?revision=52", "uri": "vstfs:///Build/Definition/242", "path": "\\BCI.DocupediaBot", "type": 2, "queueStatus": 0, "revision": 52, "createdDate": "2024-04-29T08:08:53.023Z", "project": {"id": "416d2eff-46cd-468f-829e-07d113b877db", "name": "BCI_ESW1_CN_ProjectCollection", "url": "https://dev.azure.com/bosch-bci-cn/_apis/projects/416d2eff-46cd-468f-829e-07d113b877db", "state": 1, "revision": 1490, "visibility": 0, "lastUpdateTime": "2024-04-29T09:23:13.480Z"}}