﻿using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using BCI.DocupediaBot.Infrastructure.Repositories;

namespace BCI.DocupediaBot.Persistence.EF.Repositories
{
  public class ContentRepository : EntityRepository<Content>, IContentRepository
  {
    public ContentRepository(DocupediaBotDbContext dbContext) : base(dbContext)
    {
    }
  }
}
