import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';
import { ApiResponse } from '@shared/models/docupedia.model';
import { ResponseResult } from '@shared/models/share.model';
import { SysGroupResponseDTO, SysGroupAdd, SysGroupUpdate } from '@shared/models/system.model';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SysGroupService {
  private apiUrl = `${environment.baseUrl}/api/SysGroup`;

  constructor(private http: HttpClient) {}

  getGroups(): Observable<SysGroupResponseDTO[]> {
    return this.http.get<ApiResponse<SysGroupResponseDTO[]>>(`${this.apiUrl}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        console.log('SysGroupService.getGroups() - API Response:', response.data);
        return response.data;
      })
    );
  }

  getGroupWithUsers(groupId: string): Observable<SysGroupResponseDTO> {
    return this.http.get<ApiResponse<SysGroupResponseDTO>>(`${this.apiUrl}/${groupId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  addGroup(group: SysGroupAdd): Observable<ResponseResult> {
    return this.http.post<ApiResponse<ResponseResult>>(`${this.apiUrl}`, group).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  updateGroup(group: SysGroupUpdate): Observable<ResponseResult> {
    return this.http.put<ApiResponse<ResponseResult>>(`${this.apiUrl}/${group.id}`, group).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  deleteGroup(groupId: string): Observable<ResponseResult> {
    return this.http.delete<ApiResponse<ResponseResult>>(`${this.apiUrl}/${groupId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }
}
