﻿using BCI.DocupediaBot.Application.Contracts.Dtos.SysUser;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.SysUser
{
  public interface ISysUserService
  {
    Task<SysUserResponseDTO?> QueryUserWithGroupsAsync(Guid userId);
    Task<SysUserResponseDTO?> QueryUserByNTAccountAsync(string ntAccount);
    Task<List<SysUserResponseDTO>> QueryUsersAsync();
    Task<ResponseResult> DeleteUserByUserIdAsync(Guid userId);
    Task<ResponseResult> UpdateUserAsync(SysUserUpdateDTO dto);
    Task<ResponseResult> AddUserAsync(SysUserAddDTO dto);
    Task<ResponseResult> AssignUserToGroupAsync(Guid userId, Guid groupId);
    Task<ResponseResult> RemoveUserFromGroupAsync(Guid userId, Guid groupId);
    Task<List<SysUserResponseDTO>> QueryUsersByGroupIdAsync(Guid groupId);
    Task<List<SysUserResponseDTO>> QueryUsersWithFilterAsync(SysUserFilterDTO filter);

  }
}