import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SystemRoutingModule } from './system-routing.module';
import { SharedModule } from '@shared/shared.module';
import { SysUserListComponent } from './components/sys-user-list/sys-user-list.component';
import { SysGroupListComponent } from './components/sys-group-list/sys-group-list.component';
import { SystemTabsComponent } from './components/system-tabs/system-tabs.component';
import { SysUserFormDialogComponent } from './dialogs/sys-user-form-dialog/sys-user-form-dialog.component';
import { SysGroupFormDialogComponent } from './dialogs/sys-group-form-dialog/sys-group-form-dialog.component';
import { UserGroupAssignDialogComponent } from './dialogs/user-group-assign-dialog/user-group-assign-dialog.component';

@NgModule({
  declarations: [
    SysUserListComponent,
    SysGroupListComponent,
    SystemTabsComponent,
    SysUserFormDialogComponent,
    SysGroupFormDialogComponent,
    UserGroupAssignDialogComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    SystemRoutingModule
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SystemModule { }
