{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/util-40cc7805.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssPropertyNameMap = {\n  animation: {\n    prefixed: '-webkit-animation',\n    standard: 'animation'\n  },\n  transform: {\n    prefixed: '-webkit-transform',\n    standard: 'transform'\n  },\n  transition: {\n    prefixed: '-webkit-transition',\n    standard: 'transition'\n  }\n};\nvar jsEventTypeMap = {\n  animationend: {\n    cssProperty: 'animation',\n    prefixed: 'webkitAnimationEnd',\n    standard: 'animationend'\n  },\n  animationiteration: {\n    cssProperty: 'animation',\n    prefixed: 'webkitAnimationIteration',\n    standard: 'animationiteration'\n  },\n  animationstart: {\n    cssProperty: 'animation',\n    prefixed: 'webkitAnimationStart',\n    standard: 'animationstart'\n  },\n  transitionend: {\n    cssProperty: 'transition',\n    prefixed: 'webkitTransitionEnd',\n    standard: 'transitionend'\n  }\n};\nfunction isWindow(windowObj) {\n  return Boolean(windowObj.document) && typeof windowObj.document.createElement === 'function';\n}\nfunction getCorrectPropertyName(windowObj, cssProperty) {\n  if (isWindow(windowObj) && cssProperty in cssPropertyNameMap) {\n    var el = windowObj.document.createElement('div');\n    var _a = cssPropertyNameMap[cssProperty],\n      standard = _a.standard,\n      prefixed = _a.prefixed;\n    var isStandard = standard in el.style;\n    return isStandard ? standard : prefixed;\n  }\n  return cssProperty;\n}\nfunction getCorrectEventName(windowObj, eventType) {\n  if (isWindow(windowObj) && eventType in jsEventTypeMap) {\n    var el = windowObj.document.createElement('div');\n    var _a = jsEventTypeMap[eventType],\n      standard = _a.standard,\n      prefixed = _a.prefixed,\n      cssProperty = _a.cssProperty;\n    var isStandard = cssProperty in el.style;\n    return isStandard ? standard : prefixed;\n  }\n  return eventType;\n}\nexport { getCorrectEventName as a, getCorrectPropertyName as g };\n\n"], "mappings": ";AAuBA,IAAI,qBAAqB;AAAA,EACvB,WAAW;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AAAA,EACA,WAAW;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AAAA,EACA,YAAY;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AACF;AACA,IAAI,iBAAiB;AAAA,EACnB,cAAc;AAAA,IACZ,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AAAA,EACA,oBAAoB;AAAA,IAClB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AAAA,EACA,gBAAgB;AAAA,IACd,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AAAA,EACA,eAAe;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,EACZ;AACF;AACA,SAAS,SAAS,WAAW;AAC3B,SAAO,QAAQ,UAAU,QAAQ,KAAK,OAAO,UAAU,SAAS,kBAAkB;AACpF;AACA,SAAS,uBAAuB,WAAW,aAAa;AACtD,MAAI,SAAS,SAAS,KAAK,eAAe,oBAAoB;AAC5D,QAAI,KAAK,UAAU,SAAS,cAAc,KAAK;AAC/C,QAAI,KAAK,mBAAmB,WAAW,GACrC,WAAW,GAAG,UACd,WAAW,GAAG;AAChB,QAAI,aAAa,YAAY,GAAG;AAChC,WAAO,aAAa,WAAW;AAAA,EACjC;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,WAAW,WAAW;AACjD,MAAI,SAAS,SAAS,KAAK,aAAa,gBAAgB;AACtD,QAAI,KAAK,UAAU,SAAS,cAAc,KAAK;AAC/C,QAAI,KAAK,eAAe,SAAS,GAC/B,WAAW,GAAG,UACd,WAAW,GAAG,UACd,cAAc,GAAG;AACnB,QAAI,aAAa,eAAe,GAAG;AACnC,WAAO,aAAa,WAAW;AAAA,EACjC;AACA,SAAO;AACT;", "names": []}