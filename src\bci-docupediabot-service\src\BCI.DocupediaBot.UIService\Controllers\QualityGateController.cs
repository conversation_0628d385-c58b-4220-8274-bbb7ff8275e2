using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using BCI.DocupediaBot.Application.Contracts.Dtos.QualityGate;
using BCI.DocupediaBot.Application.Services.QualityGate;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Infrastructure.Constants;
using System;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.UIService.Controllers
{
    [Authorize]
    [Route("api/qualitygate")]
    [ApiController]
    public class QualityGateController : ControllerBase
    {
        private readonly ILogger<QualityGateController> _logger;
        private readonly IQualityGateService _qualityGateService;

        public QualityGateController(
            ILogger<QualityGateController> logger,
            IQualityGateService qualityGateService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _qualityGateService = qualityGateService ?? throw new ArgumentNullException(nameof(qualityGateService));
        }

        [HttpPost]
        public async Task<IActionResult> CreateAsync([FromBody] QualityGateRequestDTO request)
        {
            if (request == null)
            {
                _logger.LogWarning("Invalid quality gate request: {Message}", ErrorMessages.QualityGateDataEmpty);
                return BadRequest(ApiResponse<QualityGateResponseDTO>.Error(ErrorMessages.QualityGateDataEmpty));
            }

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid quality gate request: {ModelState}", ModelState);
                return BadRequest(ApiResponse<QualityGateResponseDTO>.Error(ErrorMessages.InvalidRequestData));
            }

            try
            {
                _logger.LogInformation("Starting Quality Gate page copy for project: {ProjectTitle}", request.ProjectTitle);

                var result = await _qualityGateService.CopyQualityGatePagesAsync(request);

                _logger.LogInformation("Quality Gate page copy completed for project: {ProjectTitle}, Success: {Success}",
                    request.ProjectTitle, result.Success);

                return Ok(ApiResponse<QualityGateResponseDTO>.Ok(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy Quality Gate pages for project: {ProjectTitle}", request.ProjectTitle);
                return StatusCode(500, ApiResponse<QualityGateResponseDTO>.Error(ErrorMessages.InternalServerError));
            }
        }
    }
}
