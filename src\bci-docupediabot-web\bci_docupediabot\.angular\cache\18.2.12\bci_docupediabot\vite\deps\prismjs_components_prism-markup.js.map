{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-markup.js"], "sourcesContent": ["Prism.languages.markup = {\n  'comment': {\n    pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n    greedy: true\n  },\n  'prolog': {\n    pattern: /<\\?[\\s\\S]+?\\?>/,\n    greedy: true\n  },\n  'doctype': {\n    // https://www.w3.org/TR/xml/#NT-doctypedecl\n    pattern: /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n    greedy: true,\n    inside: {\n      'internal-subset': {\n        pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n        lookbehind: true,\n        greedy: true,\n        inside: null // see below\n      },\n      'string': {\n        pattern: /\"[^\"]*\"|'[^']*'/,\n        greedy: true\n      },\n      'punctuation': /^<!|>$|[[\\]]/,\n      'doctype-tag': /^DOCTYPE/i,\n      'name': /[^\\s<>'\"]+/\n    }\n  },\n  'cdata': {\n    pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n    greedy: true\n  },\n  'tag': {\n    pattern: /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n    greedy: true,\n    inside: {\n      'tag': {\n        pattern: /^<\\/?[^\\s>\\/]+/,\n        inside: {\n          'punctuation': /^<\\/?/,\n          'namespace': /^[^\\s>\\/:]+:/\n        }\n      },\n      'special-attr': [],\n      'attr-value': {\n        pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n        inside: {\n          'punctuation': [{\n            pattern: /^=/,\n            alias: 'attr-equals'\n          }, {\n            pattern: /^(\\s*)[\"']|[\"']$/,\n            lookbehind: true\n          }]\n        }\n      },\n      'punctuation': /\\/?>/,\n      'attr-name': {\n        pattern: /[^\\s>\\/]+/,\n        inside: {\n          'namespace': /^[^\\s>\\/:]+:/\n        }\n      }\n    }\n  },\n  'entity': [{\n    pattern: /&[\\da-z]{1,8};/i,\n    alias: 'named-entity'\n  }, /&#x?[\\da-f]{1,8};/i]\n};\nPrism.languages.markup['tag'].inside['attr-value'].inside['entity'] = Prism.languages.markup['entity'];\nPrism.languages.markup['doctype'].inside['internal-subset'].inside = Prism.languages.markup;\n\n// Plugin to make entity title show the real entity, idea by Roman Komarov\nPrism.hooks.add('wrap', function (env) {\n  if (env.type === 'entity') {\n    env.attributes['title'] = env.content.replace(/&amp;/, '&');\n  }\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n  /**\n   * Adds an inlined language to markup.\n   *\n   * An example of an inlined language is CSS with `<style>` tags.\n   *\n   * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n   * case insensitive.\n   * @param {string} lang The language key.\n   * @example\n   * addInlined('style', 'css');\n   */\n  value: function addInlined(tagName, lang) {\n    var includedCdataInside = {};\n    includedCdataInside['language-' + lang] = {\n      pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n      lookbehind: true,\n      inside: Prism.languages[lang]\n    };\n    includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i;\n    var inside = {\n      'included-cdata': {\n        pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n        inside: includedCdataInside\n      }\n    };\n    inside['language-' + lang] = {\n      pattern: /[\\s\\S]+/,\n      inside: Prism.languages[lang]\n    };\n    var def = {};\n    def[tagName] = {\n      pattern: RegExp(/(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(/__/g, function () {\n        return tagName;\n      }), 'i'),\n      lookbehind: true,\n      greedy: true,\n      inside: inside\n    };\n    Prism.languages.insertBefore('markup', 'cdata', def);\n  }\n});\nObject.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n  /**\n   * Adds an pattern to highlight languages embedded in HTML attributes.\n   *\n   * An example of an inlined language is CSS with `style` attributes.\n   *\n   * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n   * case insensitive.\n   * @param {string} lang The language key.\n   * @example\n   * addAttribute('style', 'css');\n   */\n  value: function (attrName, lang) {\n    Prism.languages.markup.tag.inside['special-attr'].push({\n      pattern: RegExp(/(^|[\"'\\s])/.source + '(?:' + attrName + ')' + /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source, 'i'),\n      lookbehind: true,\n      inside: {\n        'attr-name': /^[^\\s=]+/,\n        'attr-value': {\n          pattern: /=[\\s\\S]+/,\n          inside: {\n            'value': {\n              pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n              lookbehind: true,\n              alias: [lang, 'language-' + lang],\n              inside: Prism.languages[lang]\n            },\n            'punctuation': [{\n              pattern: /^=/,\n              alias: 'attr-equals'\n            }, /\"|'/]\n          }\n        }\n      }\n    });\n  }\n});\nPrism.languages.html = Prism.languages.markup;\nPrism.languages.mathml = Prism.languages.markup;\nPrism.languages.svg = Prism.languages.markup;\nPrism.languages.xml = Prism.languages.extend('markup', {});\nPrism.languages.ssml = Prism.languages.xml;\nPrism.languages.atom = Prism.languages.xml;\nPrism.languages.rss = Prism.languages.xml;"], "mappings": ";AAAA,MAAM,UAAU,SAAS;AAAA,EACvB,WAAW;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,WAAW;AAAA;AAAA,IAET,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,mBAAmB;AAAA,QACjB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,MACV;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,MACA,eAAe;AAAA,MACf,eAAe;AAAA,MACf,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,OAAO;AAAA,QACL,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,eAAe;AAAA,UACf,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,gBAAgB,CAAC;AAAA,MACjB,cAAc;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,eAAe,CAAC;AAAA,YACd,SAAS;AAAA,YACT,OAAO;AAAA,UACT,GAAG;AAAA,YACD,SAAS;AAAA,YACT,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,aAAa;AAAA,QACX,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,aAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,CAAC;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,EACT,GAAG,oBAAoB;AACzB;AACA,MAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,QAAQ,IAAI,MAAM,UAAU,OAAO,QAAQ;AACrG,MAAM,UAAU,OAAO,SAAS,EAAE,OAAO,iBAAiB,EAAE,SAAS,MAAM,UAAU;AAGrF,MAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,MAAI,IAAI,SAAS,UAAU;AACzB,QAAI,WAAW,OAAO,IAAI,IAAI,QAAQ,QAAQ,SAAS,GAAG;AAAA,EAC5D;AACF,CAAC;AACD,OAAO,eAAe,MAAM,UAAU,OAAO,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY9D,OAAO,SAAS,WAAW,SAAS,MAAM;AACxC,QAAI,sBAAsB,CAAC;AAC3B,wBAAoB,cAAc,IAAI,IAAI;AAAA,MACxC,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ,MAAM,UAAU,IAAI;AAAA,IAC9B;AACA,wBAAoB,OAAO,IAAI;AAC/B,QAAI,SAAS;AAAA,MACX,kBAAkB;AAAA,QAChB,SAAS;AAAA,QACT,QAAQ;AAAA,MACV;AAAA,IACF;AACA,WAAO,cAAc,IAAI,IAAI;AAAA,MAC3B,SAAS;AAAA,MACT,QAAQ,MAAM,UAAU,IAAI;AAAA,IAC9B;AACA,QAAI,MAAM,CAAC;AACX,QAAI,OAAO,IAAI;AAAA,MACb,SAAS,OAAO,wFAAwF,OAAO,QAAQ,OAAO,WAAY;AACxI,eAAO;AAAA,MACT,CAAC,GAAG,GAAG;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR;AAAA,IACF;AACA,UAAM,UAAU,aAAa,UAAU,SAAS,GAAG;AAAA,EACrD;AACF,CAAC;AACD,OAAO,eAAe,MAAM,UAAU,OAAO,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYhE,OAAO,SAAU,UAAU,MAAM;AAC/B,UAAM,UAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK;AAAA,MACrD,SAAS,OAAO,aAAa,SAAS,QAAQ,WAAW,MAAM,iDAAiD,QAAQ,GAAG;AAAA,MAC3H,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,aAAa;AAAA,QACb,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO,CAAC,MAAM,cAAc,IAAI;AAAA,cAChC,QAAQ,MAAM,UAAU,IAAI;AAAA,YAC9B;AAAA,YACA,eAAe,CAAC;AAAA,cACd,SAAS;AAAA,cACT,OAAO;AAAA,YACT,GAAG,KAAK;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC;AACD,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,SAAS,MAAM,UAAU;AACzC,MAAM,UAAU,MAAM,MAAM,UAAU;AACtC,MAAM,UAAU,MAAM,MAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AACzD,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,OAAO,MAAM,UAAU;AACvC,MAAM,UAAU,MAAM,MAAM,UAAU;", "names": []}