﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class CollectionConfiguration : IEntityTypeConfiguration<Collection>
  {
    public void Configure(EntityTypeBuilder<Collection> entityBuilder)
    {
      entityBuilder.ToTable(nameof(Collection));


      entityBuilder.HasIndex(x => x.Name);
      entityBuilder.HasIndex(x => x.Status);
      entityBuilder.HasIndex(x => x.EmbeddingModel);
      entityBuilder.HasIndex(x => x.IsAutomaticUpdate);


      entityBuilder.HasIndex(x => new { x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.Creator, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.Status, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.CreationTime, x.TenantId, x.Is<PERSON>eleted });
    }
  }
}
