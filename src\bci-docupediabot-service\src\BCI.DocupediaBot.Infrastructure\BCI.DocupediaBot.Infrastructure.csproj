﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bosch.Foundation.Abstractions" Version="2402.7.2" />
    <PackageReference Include="Bosch.Foundation.Database.EfHelpers" Version="2402.5.2" />
    <PackageReference Include="Bosch.Foundation.Exceptions" Version="2402.6.1" />
    <PackageReference Include="FastText.NetWrapper" Version="1.3.1" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.61" />
    <PackageReference Include="Markdig" Version="0.41.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <None Update="Lang\lid.176.ftz">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Lang\lid.176.bin">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Extensions\" />
  </ItemGroup>

</Project>
