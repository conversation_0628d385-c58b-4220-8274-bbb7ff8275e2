﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;

namespace BCI.DocupediaBot.Infrastructure.Configuration
{
  public static class ChatbotSettings
  {
    private static string _spaDevServerUrl;
    private static VectorDBSettings _vectorDB;
    private static DocupediaSettings _docupedia;
    private static ChunkSettings _chunk;
    private static ModelSettings _model;
    private static ProxySettings _proxy;
    private static QueryOptions _queryOptions;
    private static DataGenerationSettings _dataGeneration;

    public static string SpaDevServerUrl => _spaDevServerUrl;
    public static VectorDBSettings VectorDB => _vectorDB;
    public static DocupediaSettings Docupedia => _docupedia;
    public static ChunkSettings Chunk => _chunk;
    public static ModelSettings Model => _model;
    public static ProxySettings Proxy => _proxy;
    public static QueryOptions QueryOptions => _queryOptions;
    public static DataGenerationSettings DataGeneration => _dataGeneration;

    public static void Initialize(IConfiguration configuration)
    {
        var settings = new InternalChatbotSettings();
        var chatbotSection = configuration.GetSection("Chatbot");
        Console.WriteLine($"Chatbot Section Exists: {chatbotSection.Exists()}");
        Console.WriteLine($"Model Section Exists: {chatbotSection.GetSection("Model").Exists()}");

        chatbotSection.Bind(settings);
        Console.WriteLine($"Providers Count: {settings.Model?.Providers?.Count ?? 0}");

        var dataGenerationSection = configuration.GetSection("DataGeneration");
        if (dataGenerationSection.Exists())
        {
            _dataGeneration = dataGenerationSection.Get<DataGenerationSettings>();
            _dataGeneration?.Validate();
        }

        settings.Validate();

        _spaDevServerUrl = settings.SpaDevServerUrl;
        _vectorDB = settings.VectorDB;
        _docupedia = settings.Docupedia;
        _chunk = settings.Chunk;
        _model = settings.Model;
        _proxy = settings.Proxy;
        _queryOptions = settings.QueryOptions;
    }




    public static T GetModel<T>(string provider) where T : class
    {
      var providerSettings = Model?.GetProvider(provider);
      if (providerSettings == null) return null;

      return typeof(T) switch
      {
        Type t when t == typeof(CompletionSettings) => providerSettings.Completion as T,
        Type t when t == typeof(EmbeddingSettings) => providerSettings.Embedding as T,
        Type t when t == typeof(ModelProviderSettings) => providerSettings as T,
        _ => throw new ArgumentException($"Unsupported model type: {typeof(T).Name}")
      };
    }

    private class InternalChatbotSettings
    {
      public string SpaDevServerUrl { get; set; }
      public VectorDBSettings VectorDB { get; set; }
      public DocupediaSettings Docupedia { get; set; }
      public ChunkSettings Chunk { get; set; }
      public ModelSettings Model { get; set; }
      public ProxySettings Proxy { get; set; }
      public QueryOptions QueryOptions { get; set; }

      public void Validate()
      {
        if (string.IsNullOrWhiteSpace(SpaDevServerUrl) || !Uri.TryCreate(SpaDevServerUrl, UriKind.Absolute, out _))
          throw new ArgumentException("SpaDevServerUrl must be a valid URI.");
        VectorDB?.Validate();
        Docupedia?.Validate();
        Chunk?.Validate();
        Model?.Validate();
        Proxy?.Validate();
        QueryOptions?.Validate();
      }
    }
  }

  public class VectorDBSettings
  {
    public string Active { get; set; }
    public Dictionary<string, VectorDBInstance> Instances { get; set; }

    public void Validate()
    {
      if (string.IsNullOrWhiteSpace(Active) || Instances == null || !Instances.ContainsKey(Active))
        throw new ArgumentException("Active must be a valid key in Instances.");
      foreach (var instance in Instances.Values)
      {
        if (string.IsNullOrWhiteSpace(instance.GrpcAddress) || !Uri.TryCreate(instance.GrpcAddress, UriKind.Absolute, out _))
          throw new ArgumentException("GrpcAddress must be a valid URI.");
      }
    }
  }

  public class VectorDBInstance
  {
    public string GrpcAddress { get; set; }
  }

  public class DocupediaSettings
  {
    public string? BaseUrl { get; set; }
    public string? Token { get; set; }
    public string ApiVersion { get; set; }
    public DocupediaAPISettings API { get; set; }
    public DocupediaExpandSettings DefaultExpands { get; set; }
    public int PageLimit { get; set; }

    public void Validate()
    {
      // BaseUrl and Token are now optional as they will be extracted from user input
      if (string.IsNullOrWhiteSpace(ApiVersion))
        throw new ArgumentException("ApiVersion cannot be empty.");
      if (PageLimit <= 0)
        throw new ArgumentException("PageLimit must be positive.");
      API?.Validate();
      DefaultExpands?.Validate();
    }
  }

  public class DocupediaAPISettings
  {
    public string GetContentbyId { get; set; }
    public string SearchPageBySpaceAndTitle { get; set; }
    public string CreatePage { get; set; }
    public string GetChildPages { get; set; }

    public void Validate()
    {
      if (string.IsNullOrWhiteSpace(GetContentbyId) || !GetContentbyId.Contains("{0}"))
        throw new ArgumentException("GetContentbyId must be a valid format string containing {0}.");
      if (string.IsNullOrWhiteSpace(SearchPageBySpaceAndTitle) || !SearchPageBySpaceAndTitle.Contains("{0}") || !SearchPageBySpaceAndTitle.Contains("{1}"))
        throw new ArgumentException("SearchPageBySpaceAndTitle must be a valid format string containing {0} and {1}.");
    }
  }

  public class DocupediaExpandSettings
  {
    public string FullContent { get; set; }
    public string Basic { get; set; }

    public void Validate()
    {
      // FullContent and Basic can be empty strings, so no validation needed
    }
  }

  public class ChunkSettings
  {
    public int MaxTokensPerChunk { get; set; }
    public double OverlapRatio { get; set; }

    public void Validate()
    {
      if (MaxTokensPerChunk <= 0)
        throw new ArgumentException("MaxTokensPerChunk must be positive.");
      if (OverlapRatio < 0 || OverlapRatio > 1)
        throw new ArgumentException("OverlapRatio must be between 0 and 1.");
    }
  }

  public class ModelSettings
  {
    public Dictionary<string, ModelProviderSettings> Providers { get; set; } = new Dictionary<string, ModelProviderSettings>(StringComparer.OrdinalIgnoreCase);

    public ModelProviderSettings GetProvider(string providerName)
    {
      return Providers.ContainsKey(providerName) ? Providers[providerName] : null;
    }

    public void Validate()
    {
      if (Providers == null || Providers.Count == 0)
        throw new ArgumentException("At least one model provider must be configured.");
      foreach (var provider in Providers.Values)
      {
        provider?.Validate();
      }
    }
  }

  public class ModelProviderSettings
  {
    public string Endpoint { get; set; }
    public string ApiKey { get; set; }
    public CompletionSettings Completion { get; set; }
    public EmbeddingSettings Embedding { get; set; }

    public void Validate()
    {
      if (string.IsNullOrWhiteSpace(Endpoint) || !Uri.TryCreate(Endpoint, UriKind.Absolute, out _))
        throw new ArgumentException("Endpoint must be a valid URI.");
      if (!string.IsNullOrWhiteSpace(ApiKey) && ApiKey.Length < 1)
        throw new ArgumentException("ApiKey cannot be empty if specified.");
      Completion?.Validate();
      Embedding?.Validate();
    }
  }

  public class CompletionSettings
  {
    public string Active { get; set; }
    public Dictionary<string, CompletionDeployment> Deployments { get; set; }

    public void Validate()
    {
      if (string.IsNullOrWhiteSpace(Active) || Deployments == null || !Deployments.ContainsKey(Active))
        throw new ArgumentException("Active must be a valid key in Deployments.");
      foreach (var deployment in Deployments.Values)
      {
        if (string.IsNullOrWhiteSpace(deployment.Model))
          throw new ArgumentException("Model cannot be empty.");
        if (deployment.MaxOutputTokens <= 0)
          throw new ArgumentException("MaxOutputTokens must be positive.");
        if (deployment.MaxInputTokens <= 0)
          throw new ArgumentException("MaxInputTokens must be positive.");
        if (deployment.Temperature < 0 || deployment.Temperature > 1)
          throw new ArgumentException("Temperature must be between 0 and 1.");
        if (deployment.TopP < 0 || deployment.TopP > 1)
          throw new ArgumentException("TopP must be between 0 and 1.");
      }
    }
  }

  public class CompletionDeployment
  {
    public string Model { get; set; }
    public string DeploymentName { get; set; }
    public int MaxOutputTokens { get; set; }
    public int MaxInputTokens { get; set; }
    public double Temperature { get; set; }
    public double TopP { get; set; }
  }

  public class EmbeddingSettings
  {
    public string Active { get; set; }
    public Dictionary<string, EmbeddingDeployment> Deployments { get; set; }

    public void Validate()
    {
      if (string.IsNullOrWhiteSpace(Active) || Deployments == null || !Deployments.ContainsKey(Active))
        throw new ArgumentException("Active must be a valid key in Deployments.");
      foreach (var deployment in Deployments.Values)
      {
        deployment.Validate();
      }
    }
  }

  public class EmbeddingDeployment
  {
    public string Model { get; set; }
    public string DeploymentName { get; set; }
    public int Dimensions { get; set; }
    public int MaxTokens { get; set; }

    public void Validate()
    {
      if (string.IsNullOrWhiteSpace(Model))
        throw new ArgumentException("Model cannot be empty.");
      if (Dimensions <= 0)
        throw new ArgumentException("Dimensions must be a positive integer.");
      if (MaxTokens <= 0)
        throw new ArgumentException("MaxTokens must be a positive integer.");
    }
  }

  public class ProxySettings
  {
    public string Url { get; set; }
    public string Username { get; set; }
    public string Password { get; set; }
    public int TimeoutSeconds { get; set; }

    public void Validate()
    {
      if (!string.IsNullOrWhiteSpace(Url))
      {
        if (!Uri.TryCreate(Url, UriKind.Absolute, out _))
          throw new ArgumentException("Proxy Url must be a valid URI.");
        if (string.IsNullOrWhiteSpace(Username))
          throw new ArgumentException("Proxy Username cannot be empty when Url is specified.");
        if (string.IsNullOrWhiteSpace(Password))
          throw new ArgumentException("Proxy Password cannot be empty when Url is specified.");
      }
      if (TimeoutSeconds <= 0)
        throw new ArgumentException("Proxy TimeoutSeconds must be positive.");
    }
  }

  public class QueryOptions
  {
    public bool UseSpelling { get; set; }
    public bool UseRewrite { get; set; }
    public bool UseSimple { get; set; }
    public bool UseMultiLanguage { get; set; }
    public int Limit { get; set; }
    public double ScoreThreshold { get; set; }
    public double InputTokenReserveRatio { get; set; }

    public void Validate()
    {
      if (Limit <= 0)
        throw new ArgumentException("QueryOptions Limit must be positive.");
      if (ScoreThreshold < 0 || ScoreThreshold > 1)
        throw new ArgumentException("QueryOptions ScoreThreshold must be between 0 and 1.");
      if (InputTokenReserveRatio <= 0 || InputTokenReserveRatio > 1)
        throw new ArgumentException("QueryOptions InputTokenReserveRatio must be between 0 and 1.");
    }
  }

  public class DataGenerationSettings
  {
    public QualityGateSettings QualityGate { get; set; }

    public void Validate()
    {
      QualityGate?.Validate();
    }
  }

  public class QualityGateSettings
  {
    public string SourceUrl { get; set; }
    public string TargetUrl { get; set; }

    public void Validate()
    {
      if (string.IsNullOrWhiteSpace(SourceUrl) || !Uri.TryCreate(SourceUrl, UriKind.Absolute, out _))
        throw new ArgumentException("QualityGate SourceUrl must be a valid URI.");
      if (string.IsNullOrWhiteSpace(TargetUrl) || !Uri.TryCreate(TargetUrl, UriKind.Absolute, out _))
        throw new ArgumentException("QualityGate TargetUrl must be a valid URI.");
    }
  }
}