﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Content
{
  public interface IContentService
  {
		Task<List<ContentResponseDTO>> QueryContentsByPageIdAsync(Guid pageId);
		Task<ResponseResult> DeleteContentByContentIdAsync(Guid contentId);
		Task<ResponseResult> UpdateContentAsync(ContentUpdateDTO dto);
		Task<ResponseResult> UpdateContentsAsync(IList<ContentUpdateDTO> dtos);
		Task<ResponseResult> AddContentsAsync(IEnumerable<ContentAddDTO> dtos, Guid pageId);
		Task<ContentDocupediaResponseDTO> QueryContentByContentId(Guid contentId);
		Task<List<Guid>> QueryContentIdsBySourceIdsAsync(List<string> sourceIds);
		Task<(int? VersionNo, int? EmbeddingVersionNo)> GetVersionInfoBySourceIdAsync(string sourceId);
		Task<Dictionary<string, (int? VersionNo, int? EmbeddingVersionNo)>> GetVersionInfosBySourceIdsAsync(List<string> sourceIds);
  }
}
