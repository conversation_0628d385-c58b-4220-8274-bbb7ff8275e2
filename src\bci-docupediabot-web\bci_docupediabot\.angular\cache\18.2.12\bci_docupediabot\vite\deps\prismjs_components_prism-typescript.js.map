{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-typescript.js"], "sourcesContent": ["(function (Prism) {\n  Prism.languages.typescript = Prism.languages.extend('javascript', {\n    'class-name': {\n      pattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n      lookbehind: true,\n      greedy: true,\n      inside: null // see below\n    },\n    'builtin': /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/\n  });\n\n  // The keywords TypeScript adds to JavaScript\n  Prism.languages.typescript.keyword.push(/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,\n  // keywords that have to be followed by an identifier\n  /\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,\n  // This is for `import type *, {}`\n  /\\btype\\b(?=\\s*(?:[\\{*]|$))/);\n\n  // doesn't work with TS because TS is too complex\n  delete Prism.languages.typescript['parameter'];\n  delete Prism.languages.typescript['literal-property'];\n\n  // a version of typescript specifically for highlighting types\n  var typeInside = Prism.languages.extend('typescript', {});\n  delete typeInside['class-name'];\n  Prism.languages.typescript['class-name'].inside = typeInside;\n  Prism.languages.insertBefore('typescript', 'function', {\n    'decorator': {\n      pattern: /@[$\\w\\xA0-\\uFFFF]+/,\n      inside: {\n        'at': {\n          pattern: /^@/,\n          alias: 'operator'\n        },\n        'function': /^[\\s\\S]+/\n      }\n    },\n    'generic-function': {\n      // e.g. foo<T extends \"bar\" | \"baz\">( ...\n      pattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n      greedy: true,\n      inside: {\n        'function': /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n        'generic': {\n          pattern: /<[\\s\\S]+/,\n          // everything after the first <\n          alias: 'class-name',\n          inside: typeInside\n        }\n      }\n    }\n  });\n  Prism.languages.ts = Prism.languages.typescript;\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAChB,EAAAA,OAAM,UAAU,aAAaA,OAAM,UAAU,OAAO,cAAc;AAAA,IAChE,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,QAAQ;AAAA;AAAA,IACV;AAAA,IACA,WAAW;AAAA,EACb,CAAC;AAGD,EAAAA,OAAM,UAAU,WAAW,QAAQ;AAAA,IAAK;AAAA;AAAA,IAExC;AAAA;AAAA,IAEA;AAAA,EAA4B;AAG5B,SAAOA,OAAM,UAAU,WAAW,WAAW;AAC7C,SAAOA,OAAM,UAAU,WAAW,kBAAkB;AAGpD,MAAI,aAAaA,OAAM,UAAU,OAAO,cAAc,CAAC,CAAC;AACxD,SAAO,WAAW,YAAY;AAC9B,EAAAA,OAAM,UAAU,WAAW,YAAY,EAAE,SAAS;AAClD,EAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,IACrD,aAAa;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA;AAAA,MAElB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,WAAW;AAAA,UACT,SAAS;AAAA;AAAA,UAET,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACvC,GAAG,KAAK;", "names": ["Prism"]}