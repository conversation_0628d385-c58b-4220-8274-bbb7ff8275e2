<bci-modal-component [title]="'Confluence Token Setup Required'" [closeIcon]="true" (closeHandler)="close()" class="modal">
  <div class="token-guide-content">
    <div class="warning-section">
      <mat-icon class="warning-icon">warning</mat-icon>
      <p class="warning-text">
        You need to configure your Confluence Personal Access Token (PAT) to add pages from URLs.
      </p>
    </div>

    <div class="guide-section">
      <h3>How to get your Confluence Personal Access Token:</h3>
      <ol class="guide-steps">
        <li>
          <strong>Go to Confluence Token Settings:</strong>
          <p>Navigate to <a href="https://inside-docupedia.bosch.com/confluence/plugins/personalaccesstokens/usertokens.action" target="_blank">Personal Access Tokens</a></p>
        </li>
        <li>
          <strong>Create Token:</strong>
          <p>Click "Create token" → Enter token name "DocupediaBot" → Choose expiry date → click create button</p>
        </li>
        <li>
          <strong>Copy Token:</strong>
          <p class="important-note">⚠️ <strong>Important:</strong> Copy the token immediately as it won't be shown again!</p>
        </li>
        <li>
          <strong>Paste Token Below:</strong>
          <p>Enter your token in the field below and click Save</p>
        </li>
      </ol>
    </div>

    <!-- Token Input Section -->
    <div class="token-input-section">
      <form [formGroup]="tokenForm" (ngSubmit)="saveToken()">
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Confluence Personal Access Token</mat-label>
          <input matInput formControlName="docupediaToken"
                 placeholder="Paste your Confluence PAT here"
                 [disabled]="isLoading" />
          <mat-hint>Enter the token you copied from Confluence</mat-hint>
          <mat-error *ngIf="tokenForm.get('docupediaToken')?.hasError('required')">
            Token is required
          </mat-error>
          <mat-error *ngIf="tokenForm.get('docupediaToken')?.hasError('tokenTooShort')">
            Token is too short (minimum 10 characters)
          </mat-error>
          <mat-error *ngIf="tokenForm.get('docupediaToken')?.hasError('invalidTokenFormat')">
            Invalid token format
          </mat-error>
        </mat-form-field>

        <!-- Success/Error Messages -->
        <div *ngIf="saveSuccess" class="success-message">
          <mat-icon>check_circle</mat-icon>
          <span>Token saved successfully!</span>
        </div>

        <div *ngIf="saveError" class="error-message">
          <mat-icon>error</mat-icon>
          <span>{{ saveError }}</span>
        </div>
      </form>
    </div>
  </div>

  <div actions class="dialog-actions">
    <button bciPrimaryButton
            [disabled]="tokenForm.invalid || isLoading || saveSuccess"
            (click)="saveToken()"
            type="button">
      <mat-icon *ngIf="isLoading">hourglass_empty</mat-icon>
      <mat-icon *ngIf="saveSuccess">check</mat-icon>
      {{ isLoading ? 'Saving...' : (saveSuccess ? 'Saved!' : 'Save Token') }}
    </button>
    <button bciSecondaryButton (click)="close()" type="button" [disabled]="isLoading">
      Cancel
    </button>
  </div>
</bci-modal-component>
