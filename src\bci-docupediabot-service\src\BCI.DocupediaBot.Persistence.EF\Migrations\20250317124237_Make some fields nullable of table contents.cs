﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{

    public partial class Makesomefieldsnullableoftablecontents : Migration
    {

        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ProcessedContent",
                table: "Content",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "OriginContent",
                table: "Content",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text");
        }


        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ProcessedContent",
                table: "Content",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "OriginContent",
                table: "Content",
                type: "text",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);
        }
    }
}
