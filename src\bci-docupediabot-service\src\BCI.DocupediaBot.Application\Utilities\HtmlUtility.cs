﻿using HtmlAgilityPack;
using Markdig;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace BCI.DocupediaBot.Infrastructure
{
  public static class HtmlUtility
  {
    public static string[] PreprocessHtmlForRag(string htmlContent)
    {
      var doc = new HtmlDocument();
      doc.LoadHtml(htmlContent);

      CleanUnnecessaryTags(doc);
      ProtectSpecialContent(doc);

      string processedContent = doc.DocumentNode.InnerText;
      processedContent = Regex.Replace(processedContent, @"\n\s*\n+", "\n\n").Trim();
      string summary = GenerateSummary(doc.DocumentNode.InnerHtml);

      return new[] { processedContent, summary };
    }

    private static void CleanUnnecessaryTags(HtmlDocument doc)
    {

      var emptyParagraphs = doc.DocumentNode.SelectNodes("//p[not(normalize-space()) and not(*)]");
      if (emptyParagraphs != null)
      {
        foreach (var node in emptyParagraphs.ToList())
        {
          node.Remove();
        }
      }


      var macroDivs = doc.DocumentNode.SelectNodes("//div[contains(@class, 'toc-macro')]");
      if (macroDivs != null)
      {
        foreach (var node in macroDivs.ToList())
        {
          node.Remove();
        }
      }


      var spans = doc.DocumentNode.SelectNodes("//span[not(contains(@class, 'confluence-embedded-file-wrapper')) and not(.//img) and not(contains(@class, 'plugin_pagetree_children_span'))]");
      if (spans != null)
      {
        foreach (var span in spans.ToList())
        {
          string innerText = span.InnerText.Trim();
          if (!string.IsNullOrEmpty(innerText))
          {
            span.ParentNode.ReplaceChild(HtmlNode.CreateNode(innerText), span);
          }
          else
          {
            span.Remove();
          }
        }
      }
    }

    private static void ProtectSpecialContent(HtmlDocument doc)
    {
      var nodesToProcess = new List<(string xpath, Action<HtmlNode> action)>
    {

        ("//img | //span[contains(@class, 'confluence-embedded-file-wrapper')]//img", node =>
        {
            string src = node.GetAttributeValue("data-image-src", node.GetAttributeValue("src", "unknown"));
            string alt = node.GetAttributeValue("alt", "no-alt");
            string protectedContent = $"[IMAGE: {src} | ALT: {alt}]";
            node.ParentNode.ReplaceChild(HtmlNode.CreateNode(protectedContent), node);
        }),

        ("//div[contains(@class, 'code')]", node =>
        {
            var pre = node.SelectSingleNode(".//pre");
            if (pre != null)
            {
                string code = pre.InnerText.Trim();
                string language = ExtractCodeLanguage(pre) ?? "unknown";
                string protectedContent = $"[CODE: {language}]\n{code}\n[/CODE]";
                node.ParentNode.ReplaceChild(HtmlNode.CreateNode(protectedContent), node);
            }
        }),

        ("//a[not(ancestor::div[contains(@class, 'code')])]", node =>
        {
            string href = node.GetAttributeValue("href", "unknown");
            string text = node.InnerText.Trim();
            string protectedContent = $"[LINK: {href} | TEXT: {text}]";
            node.ParentNode.ReplaceChild(HtmlNode.CreateNode(protectedContent), node);
        }),

        ("//ul | //ol", node =>
        {
            string listContent = ProcessListNode(node).Trim();
            string protectedContent = $"[LIST]\n{listContent}\n[/LIST]";
            node.ParentNode.ReplaceChild(HtmlNode.CreateNode(protectedContent), node);
        }),

        ("//div[contains(@class, 'table-wrap')]//table[contains(@class, 'confluenceTable')]", node =>
        {
            string tableContent = FormatTable(node);
            string protectedContent = $"[TABLE]\n{tableContent}\n[/TABLE]";
            node.ParentNode.ReplaceChild(HtmlNode.CreateNode(protectedContent), node);
        }),

        ("//div[contains(@class, 'confluence-information-macro') and not(contains(@class, 'code'))]", node =>
        {
            string panelType = ExtractPanelType(node) ?? "info";
            var bodyNode = node.SelectSingleNode(".//div[contains(@class, 'confluence-information-macro-body')]");
            string panelContent = bodyNode != null ? ProcessNode(bodyNode).Trim() : node.InnerText.Trim();
            string protectedContent = $"[PANEL: {panelType}]\n{panelContent}\n[/PANEL]";
            node.ParentNode.ReplaceChild(HtmlNode.CreateNode(protectedContent), node);
        })
    };

      foreach (var (xpath, action) in nodesToProcess)
      {
        var nodes = doc.DocumentNode.SelectNodes(xpath);
        if (nodes != null)
        {
          foreach (var node in nodes.ToList())
          {
            action(node);
          }
        }
      }
    }

    private static string ProcessNode(HtmlNode node)
    {
      var result = new StringBuilder();


      if (node.NodeType == HtmlNodeType.Text)
      {
        string text = node.InnerText.Trim();
        if (!string.IsNullOrEmpty(text))
        {

          if (Regex.IsMatch(text, @"\[(CODE|TABLE|LIST|PANEL|IMAGE|LINK):.*?\]"))
          {
            result.AppendLine(text);
            return result.ToString().Trim();
          }

          text = Regex.Replace(text, @"\s+", " ");
          result.AppendLine(text);
        }
      }
      else if (node.NodeType == HtmlNodeType.Element)
      {

        if (Regex.IsMatch(node.Name, @"^h[1-6]$"))
        {
          string level = node.Name.Substring(1);
          string title = node.InnerText.Trim();
          result.AppendLine($"#{'#'.Repeat(int.Parse(level))} {title}");
        }

        else
        {
          foreach (var child in node.ChildNodes)
          {
            string childContent = ProcessNode(child).Trim();
            if (!string.IsNullOrEmpty(childContent))
            {
              result.AppendLine(childContent);
            }
          }
        }
      }

      return result.ToString().Trim();
    }

    private static string ProcessListNode(HtmlNode node)
    {
      var result = new StringBuilder();
      var items = node.SelectNodes(".//li");
      if (items != null)
      {
        foreach (var item in items)
        {
          string itemContent = item.InnerText.Trim();
          result.AppendLine($"- {itemContent}");
        }
      }
      return result.ToString().Trim();
    }

    private static string GenerateSummary(string innerHtml)
    {

      var doc = new HtmlDocument();
      doc.LoadHtml(innerHtml);


      var headerNodes = doc.DocumentNode.SelectNodes("//h1|//h2|//h3|//h4|//h5|//h6");
      if (headerNodes != null && headerNodes.Count > 0)
      {
        var headerTexts = headerNodes
            .Select(node => Regex.Replace(node.InnerText.Trim(), @"\s+", " "))
            .Where(text => !string.IsNullOrEmpty(text))
            .ToList();

        if (headerTexts.Any())
        {
          string headerSummary = string.Join(";", headerTexts);

          if (headerSummary.Length > 200)
          {
            headerSummary = headerSummary.Substring(0, 200);

            int lastSemicolon = headerSummary.LastIndexOf(';');
            if (lastSemicolon > 0)
            {
              headerSummary = headerSummary.Substring(0, lastSemicolon);
            }
          }
          return headerSummary;
        }
      }


      string cleanedContent = Regex.Replace(innerHtml, @"\[(TABLE|PANEL|CODE|LIST|LINK|IMAGE):?.*?\](.*?)\[/\1\]", "$2", RegexOptions.Singleline);
      cleanedContent = Regex.Replace(cleanedContent, @"<[^>]+>", "");
      cleanedContent = Regex.Replace(cleanedContent, @"\s+", " ");
      cleanedContent = cleanedContent.Trim();


      string[] lines = cleanedContent.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries)
          .Select(line => line.Trim())
          .Where(line => !string.IsNullOrEmpty(line))
          .ToArray();


      var summaryBuilder = new StringBuilder();
      foreach (string line in lines)
      {
        if (summaryBuilder.Length == 0)
        {
          summaryBuilder.Append(line);
        }
        else
        {

          if (summaryBuilder.Length + line.Length + 1 > 200)
          {

            string current = summaryBuilder.ToString();
            if (current.Length > 200)
            {
              current = current.Substring(0, 200);
              int lastSemicolon = current.LastIndexOf(';');
              if (lastSemicolon > 0)
              {
                current = current.Substring(0, lastSemicolon);
              }
            }
            return current;
          }
          summaryBuilder.Append(";" + line);
        }
      }


      string result = summaryBuilder.ToString();
      if (result.Length > 200)
      {
        result = result.Substring(0, 200);
        int lastSemicolon = result.LastIndexOf(';');
        if (lastSemicolon > 0)
        {
          result = result.Substring(0, lastSemicolon);
        }
      }
      return result;
    }

    private static string FormatTable(HtmlNode table)
    {
      var tableText = new StringBuilder();
      var rows = table.SelectNodes(".//tr");
      if (rows == null) return "";


      var headerRow = rows.FirstOrDefault()?.SelectNodes(".//th");
      if (headerRow != null)
      {
        tableText.AppendLine($"| {string.Join(" | ", headerRow.Select(h => h.InnerText.Trim().Replace("|", "\\|")))} |");
        tableText.AppendLine($"| {string.Join(" | ", headerRow.Select(_ => "---"))} |");
      }


      foreach (var row in rows.Skip(headerRow != null ? 1 : 0))
      {
        var cells = row.SelectNodes(".//td");
        if (cells != null)
        {
          var cellContents = cells.Select(c =>
          {
            var text = c.InnerText.Trim().Replace("|", "\\|");

            var links = c.SelectNodes(".//a");
            if (links != null)
            {
              foreach (var link in links)
              {
                string href = link.GetAttributeValue("href", "unknown");
                string linkText = link.InnerText.Trim();
                text = text.Replace(link.InnerText, $"[LINK: {href} | TEXT: {linkText}]");
              }
            }
            return text;
          });
          tableText.AppendLine($"| {string.Join(" | ", cellContents)} |");
        }
      }

      return tableText.ToString().Trim();
    }

    private static string ExtractCodeLanguage(HtmlNode preNode)
    {
      string paramsAttr = preNode.GetAttributeValue("data-syntaxhighlighter-params", "");
      if (!string.IsNullOrEmpty(paramsAttr))
      {
        var match = Regex.Match(paramsAttr, @"brush:\s*([^;\s]+)");
        if (match.Success)
        {
          string lang = match.Groups[1].Value.Trim();

          var languageMap = new Dictionary<string, string>
            {
               { "c#", "csharp" },
                { "vb", "vbnet" },
                { "f#", "fsharp" },
                { "js", "javascript" },
                { "ts", "typescript" },
                { "html", "markup" },
                { "xml", "markup" },
                { "sh", "bash" },
                { "shell", "bash" },
                { "yml", "yaml" },
                { "sql", "sql" },
                { "java", "java" }
            };
          return languageMap.TryGetValue(lang.ToLower(), out var mappedLang) ? mappedLang : lang.ToLower();
        }
      }
      return "unknown";
    }

    private static string ExtractPanelType(HtmlNode panelNode)
    {
      string classAttr = panelNode.GetAttributeValue("class", "").ToLower();
      if (classAttr.Contains("warning")) return "warning";
      if (classAttr.Contains("note")) return "note";
      if (classAttr.Contains("success")) return "success";
      if (classAttr.Contains("tip")) return "tip";
      if (classAttr.Contains("info")) return "info";
      return "info";
    }

    public static string ConvertMarkdownToHtml(string markdownText)
    {
      var pipeline = new MarkdownPipelineBuilder()
          .UseAdvancedExtensions()
          .Build();

      string html = Markdown.ToHtml(markdownText, pipeline);

      return html;
    }
  }
}