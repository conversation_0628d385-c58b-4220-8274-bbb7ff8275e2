@import "@bci-web-core/common-styles/sass/bci/bci-variables";

.batch-actions {
  padding: $grid-size * 2 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: $grid-size * 2;
}

.user-table {
  width: 100%;
  margin-top: $grid-size * 2;
}

mat-header-cell,
mat-cell {
  padding: 8px 16px;
}


.mat-column-select {
  width: 60px;
  min-width: 60px;
  max-width: 60px;
}

.mat-column-userNTAccount {
  width: 15%;
  min-width: 120px;
}

.mat-column-userName {
  width: 15%;
  min-width: 120px;
}

.mat-column-givenName {
  width: 15%;
  min-width: 120px;
}

.mat-column-department {
  width: 20%;
  min-width: 150px;
}

.mat-column-status {
  width: 10%;
  min-width: 80px;
}

.mat-column-groups {
  width: 25%;
  min-width: 200px;
}



.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.active {
    background-color: #e8f5e8;
    color: #2e7d32;
  }

  &.inactive {
    background-color: #ffebee;
    color: #d32f2f;
  }
}

.groups-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.group-tag {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $grid-size * 4;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $grid-size * 4;
  color: #666;
}

mat-row {
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }
}




@media screen and (max-width: 1200px) {
  .user-table {
    .mat-column-department,
    .mat-column-groups {
      display: none;
    }
  }
}

@media screen and (max-width: 900px) {
  .user-table {
    .mat-column-givenName {
      display: none;
    }
  }
}

@media screen and (max-width: 600px) {
  .user-table {
    .mat-column-status {
      display: none;
    }
  }
}
