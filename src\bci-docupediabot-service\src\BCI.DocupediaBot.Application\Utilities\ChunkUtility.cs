﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BCI.DocupediaBot.Application.Services.VectorDb
{

  public static class ChunkUtility
  {

    public static List<string> ChunkText(string text, int maxTokensPerChunk, double overlapRatio)
    {
      if (string.IsNullOrEmpty(text)) return new List<string>();
      if (maxTokensPerChunk <= 0) throw new ArgumentException("MaxTokensPerChunk must be positive.");
      if (overlapRatio < 0 || overlapRatio > 1) throw new ArgumentException("OverlapRatio must be between 0 and 1.");

      var (processedText, structureMap) = ExtractStructuredBlocks(text);
      var initialChunks = BuildInitialChunks(processedText, maxTokensPerChunk, overlapRatio);
      return ReplaceStructuredBlocks(initialChunks, structureMap);
    }

    private static (string processedText, Dictionary<string, string> structureMap) ExtractStructuredBlocks(string text)
    {
      var structureMap = new Dictionary<string, string>();
      int structureIndex = 0;
      var sb = new StringBuilder(text);

      var patterns = new List<(string startTag, string endTag, bool isMultiLine)>
      {
        ("[TABLE", "[/TABLE]", true),
        ("[CODE:", "[/CODE]", true),
        ("[LIST", "[/LIST]", true),
        ("[PANEL", "[/PANEL]", true),
        ("[IMAGE:", "]", false),
        ("[LINK:", "]", false)
      };

      foreach (var (startTag, endTag, isMultiLine) in patterns)
      {
        int pos = 0;
        while (pos < sb.Length)
        {
          int startIndex = sb.ToString().IndexOf(startTag, pos, StringComparison.Ordinal);
          if (startIndex == -1) break;

          int endIndex;
          if (isMultiLine)
          {
            endIndex = FindMatchingEndTag(sb.ToString(), startIndex, startTag, endTag);
          }
          else
          {
            int tempEndIndex = sb.ToString().IndexOf(endTag, startIndex + startTag.Length, StringComparison.Ordinal);
            endIndex = tempEndIndex >= 0 ? tempEndIndex + endTag.Length - 1 : -1;
          }

          if (endIndex == -1) break;

          string structureContent = sb.ToString(startIndex, endIndex - startIndex + 1);
          string placeholder = $"{{STRUCTURE_{structureIndex++}}}";
          structureMap[placeholder] = structureContent;

          sb.Remove(startIndex, endIndex - startIndex + 1);
          sb.Insert(startIndex, placeholder);
          pos = startIndex + placeholder.Length;
        }
      }

      return (sb.ToString(), structureMap);
    }

    private static int FindMatchingEndTag(string text, int startIndex, string startTag, string endTag)
    {
      int openCount = 0;
      int pos = startIndex;

      while (pos < text.Length)
      {
        if (text[pos..].StartsWith(startTag)) { openCount++; pos += startTag.Length; }
        else if (text[pos..].StartsWith(endTag))
        {
          openCount--;
          if (openCount == 0) return pos + endTag.Length - 1;
          pos += endTag.Length;
        }
        else { pos++; }
      }

      return -1;
    }

    private static List<string> BuildInitialChunks(string text, int maxTokensPerChunk, double overlapRatio)
    {
      var paragraphs = SplitIntoParagraphs(text);
      var chunks = new List<string>();
      var currentChunk = new StringBuilder();
      int currentTokenCount = 0;

      foreach (var paragraph in paragraphs)
      {
        int paragraphTokenCount = CountTokens(paragraph);

        if (paragraphTokenCount > maxTokensPerChunk)
        {
          if (currentChunk.Length > 0)
          {
            chunks.Add(currentChunk.ToString().Trim());
            currentChunk.Clear();
            currentTokenCount = 0;
          }
          chunks.AddRange(SplitLargeParagraph(paragraph, maxTokensPerChunk));
          continue;
        }

        if (currentTokenCount + paragraphTokenCount <= maxTokensPerChunk)
        {
          currentChunk.Append(paragraph).Append(' ');
          currentTokenCount += paragraphTokenCount;
        }
        else
        {
          chunks.Add(currentChunk.ToString().Trim());
          currentChunk.Clear().Append(paragraph).Append(' ');
          currentTokenCount = paragraphTokenCount;
        }
      }

      if (currentChunk.Length > 0)
        chunks.Add(currentChunk.ToString().Trim());

      return AddOverlapToChunks(chunks, maxTokensPerChunk, overlapRatio);
    }

    private static List<string> SplitLargeParagraph(string paragraph, int maxTokensPerChunk)
    {
      var subChunks = new List<string>();
      var words = paragraph.Split(' ', StringSplitOptions.RemoveEmptyEntries);
      var currentSubChunk = new StringBuilder();
      int currentTokenCount = 0;

      foreach (var word in words)
      {
        int wordTokenCount = CountTokens(word);
        if (currentTokenCount + wordTokenCount > maxTokensPerChunk)
        {
          subChunks.Add(currentSubChunk.ToString().Trim());
          currentSubChunk.Clear();
          currentTokenCount = 0;
        }
        currentSubChunk.Append(word).Append(' ');
        currentTokenCount += wordTokenCount;
      }

      if (currentSubChunk.Length > 0)
        subChunks.Add(currentSubChunk.ToString().Trim());

      return subChunks;
    }

    private static List<string> AddOverlapToChunks(List<string> initialChunks, int maxTokensPerChunk, double overlapRatio)
    {
      var finalChunks = new List<string>();
      int overlapTokens = (int)(maxTokensPerChunk * overlapRatio);

      for (int i = 0; i < initialChunks.Count; i++)
      {
        var chunkBuilder = new StringBuilder(initialChunks[i]);

        if (i > 0)
        {
          string prevOverlap = GetOverlapText(initialChunks[i - 1], overlapTokens, fromEnd: true);
          if (!string.IsNullOrEmpty(prevOverlap))
            chunkBuilder.Insert(0, prevOverlap + " ");
        }

        if (i < initialChunks.Count - 1)
        {
          string nextOverlap = GetOverlapText(initialChunks[i + 1], overlapTokens, fromEnd: false);
          if (!string.IsNullOrEmpty(nextOverlap))
            chunkBuilder.Append(" ").Append(nextOverlap);
        }

        finalChunks.Add(chunkBuilder.ToString().Trim());
      }

      return finalChunks;
    }

    private static List<string> ReplaceStructuredBlocks(List<string> chunks, Dictionary<string, string> structureMap)
    {
      return chunks.Select(chunk => structureMap.Aggregate(chunk, (current, kvp) => current.Replace(kvp.Key, kvp.Value))).ToList();
    }

    private static List<string> SplitIntoParagraphs(string text)
    {
      return text.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                 .Select(p => p.Trim())
                 .Where(p => !string.IsNullOrEmpty(p))
                 .ToList();
    }

    private static int CountTokens(string text)
    {
      return text.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
    }

    private static string GetOverlapText(string text, int overlapTokens, bool fromEnd)
    {
      var words = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
      int totalTokens = words.Length;

      if (totalTokens <= overlapTokens)
        return text;

      return fromEnd
          ? string.Join(" ", words[^overlapTokens..])
          : string.Join(" ", words[..overlapTokens]);
    }
  }
}