import { NotificationType } from '@shared/enums/notification-type';

/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */
export class Notification {
  text: string;
  messageDetails: string;
  type: number;
  keepAfterNavigationChange: boolean;
  duration: number;

  constructor(type: NotificationType, message: string) {
    this.text = message;
    this.type = type;
  }
}
