/*
 * Copyright (c) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';

@Injectable({ providedIn: 'root' })
export class HttpUtilService {
  /**
   * Removes all parameters, which are either null or empty.
   *
   * @param params, which are supposed to be used for query parameter of a http request
   */
  removeNullOrEmptyParams(params: Record<string, string | number | boolean | string[]>): HttpParams {
    let httpParams: HttpParams = new HttpParams();
    Object.keys(params).forEach(param => {
      if (params[param]) {
        if (Array.isArray(params[param])) {
          (params[param] as string[]).forEach(value => {
            httpParams = httpParams.append(param, value);
          });
        } else {
          httpParams = httpParams.set(param, params[param] as string | number | boolean);
        }
      }
    });

    return httpParams;
  }
}
