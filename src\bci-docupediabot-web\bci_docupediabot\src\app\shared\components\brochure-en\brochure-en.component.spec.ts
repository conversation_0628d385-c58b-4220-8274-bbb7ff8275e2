import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { BrochureEnComponent } from './brochure-en.component';

describe('BrochureEnComponent', () => {
  let component: BrochureEnComponent;
  let fixture: ComponentFixture<BrochureEnComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BrochureEnComponent],
      imports: [
        MatToolbarModule,
        MatIconModule,
        MatCardModule,
        MatChipsModule,
        MatTableModule,
        MatButtonModule,
        NoopAnimationsModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BrochureEnComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have platform stats', () => {
    expect(component.platformStats).toBeDefined();
    expect(component.platformStats.users).toBe('10000 +');
    expect(component.platformStats.space).toBe('100 +');
    expect(component.platformStats.queries).toBe('100000 +');
    expect(component.platformStats.accuracy).toBe('95% +');
  });

  it('should have core features', () => {
    expect(component.coreFeatures).toBeDefined();
    expect(component.coreFeatures.length).toBe(8);
    expect(component.coreFeatures[0].title).toBe('Enterprise Security');
  });

  it('should have tech stack', () => {
    expect(component.techStack).toBeDefined();
    expect(component.techStack.length).toBe(6);
    expect(component.techStack[0].layer).toBe('Frontend Layer');
  });

  it('should have integrations', () => {
    expect(component.integrations).toBeDefined();
    expect(component.integrations.length).toBe(4);
    expect(component.integrations.find(i => i.name === 'RAGFlow')).toBeDefined();
    expect(component.integrations.find(i => i.name === 'n8n')).toBeDefined();
  });
});
