{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-bash.js"], "sourcesContent": ["(function (Prism) {\n  // $ set | grep '^[A-Z][^[:space:]]*=' | cut -d= -f1 | tr '\\n' '|'\n  // + LC_ALL, RANDOM, REPLY, SECONDS.\n  // + make sure PS1..4 are here as they are not always set,\n  // - some useless things.\n  var envVars = '\\\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\\\b';\n  var commandAfterHeredoc = {\n    pattern: /(^([\"']?)\\w+\\2)[ \\t]+\\S.*/,\n    lookbehind: true,\n    alias: 'punctuation',\n    // this looks reasonably well in all themes\n    inside: null // see below\n  };\n  var insideString = {\n    'bash': commandAfterHeredoc,\n    'environment': {\n      pattern: RegExp('\\\\$' + envVars),\n      alias: 'constant'\n    },\n    'variable': [\n    // [0]: Arithmetic Environment\n    {\n      pattern: /\\$?\\(\\([\\s\\S]+?\\)\\)/,\n      greedy: true,\n      inside: {\n        // If there is a $ sign at the beginning highlight $(( and )) as variable\n        'variable': [{\n          pattern: /(^\\$\\(\\([\\s\\S]+)\\)\\)/,\n          lookbehind: true\n        }, /^\\$\\(\\(/],\n        'number': /\\b0x[\\dA-Fa-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee]-?\\d+)?/,\n        // Operators according to https://www.gnu.org/software/bash/manual/bashref.html#Shell-Arithmetic\n        'operator': /--|\\+\\+|\\*\\*=?|<<=?|>>=?|&&|\\|\\||[=!+\\-*/%<>^&|]=?|[?~:]/,\n        // If there is no $ sign at the beginning highlight (( and )) as punctuation\n        'punctuation': /\\(\\(?|\\)\\)?|,|;/\n      }\n    },\n    // [1]: Command Substitution\n    {\n      pattern: /\\$\\((?:\\([^)]+\\)|[^()])+\\)|`[^`]+`/,\n      greedy: true,\n      inside: {\n        'variable': /^\\$\\(|^`|\\)$|`$/\n      }\n    },\n    // [2]: Brace expansion\n    {\n      pattern: /\\$\\{[^}]+\\}/,\n      greedy: true,\n      inside: {\n        'operator': /:[-=?+]?|[!\\/]|##?|%%?|\\^\\^?|,,?/,\n        'punctuation': /[\\[\\]]/,\n        'environment': {\n          pattern: RegExp('(\\\\{)' + envVars),\n          lookbehind: true,\n          alias: 'constant'\n        }\n      }\n    }, /\\$(?:\\w+|[#?*!@$])/],\n    // Escape sequences from echo and printf's manuals, and escaped quotes.\n    'entity': /\\\\(?:[abceEfnrtv\\\\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/\n  };\n  Prism.languages.bash = {\n    'shebang': {\n      pattern: /^#!\\s*\\/.*/,\n      alias: 'important'\n    },\n    'comment': {\n      pattern: /(^|[^\"{\\\\$])#.*/,\n      lookbehind: true\n    },\n    'function-name': [\n    // a) function foo {\n    // b) foo() {\n    // c) function foo() {\n    // but not “foo {”\n    {\n      // a) and c)\n      pattern: /(\\bfunction\\s+)[\\w-]+(?=(?:\\s*\\(?:\\s*\\))?\\s*\\{)/,\n      lookbehind: true,\n      alias: 'function'\n    }, {\n      // b)\n      pattern: /\\b[\\w-]+(?=\\s*\\(\\s*\\)\\s*\\{)/,\n      alias: 'function'\n    }],\n    // Highlight variable names as variables in for and select beginnings.\n    'for-or-select': {\n      pattern: /(\\b(?:for|select)\\s+)\\w+(?=\\s+in\\s)/,\n      alias: 'variable',\n      lookbehind: true\n    },\n    // Highlight variable names as variables in the left-hand part\n    // of assignments (“=” and “+=”).\n    'assign-left': {\n      pattern: /(^|[\\s;|&]|[<>]\\()\\w+(?:\\.\\w+)*(?=\\+?=)/,\n      inside: {\n        'environment': {\n          pattern: RegExp('(^|[\\\\s;|&]|[<>]\\\\()' + envVars),\n          lookbehind: true,\n          alias: 'constant'\n        }\n      },\n      alias: 'variable',\n      lookbehind: true\n    },\n    // Highlight parameter names as variables\n    'parameter': {\n      pattern: /(^|\\s)-{1,2}(?:\\w+:[+-]?)?\\w+(?:\\.\\w+)*(?=[=\\s]|$)/,\n      alias: 'variable',\n      lookbehind: true\n    },\n    'string': [\n    // Support for Here-documents https://en.wikipedia.org/wiki/Here_document\n    {\n      pattern: /((?:^|[^<])<<-?\\s*)(\\w+)\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\2/,\n      lookbehind: true,\n      greedy: true,\n      inside: insideString\n    },\n    // Here-document with quotes around the tag\n    // → No expansion (so no “inside”).\n    {\n      pattern: /((?:^|[^<])<<-?\\s*)([\"'])(\\w+)\\2\\s[\\s\\S]*?(?:\\r?\\n|\\r)\\3/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        'bash': commandAfterHeredoc\n      }\n    },\n    // “Normal” string\n    {\n      // https://www.gnu.org/software/bash/manual/html_node/Double-Quotes.html\n      pattern: /(^|[^\\\\](?:\\\\\\\\)*)\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: insideString\n    }, {\n      // https://www.gnu.org/software/bash/manual/html_node/Single-Quotes.html\n      pattern: /(^|[^$\\\\])'[^']*'/,\n      lookbehind: true,\n      greedy: true\n    }, {\n      // https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html\n      pattern: /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      inside: {\n        'entity': insideString.entity\n      }\n    }],\n    'environment': {\n      pattern: RegExp('\\\\$?' + envVars),\n      alias: 'constant'\n    },\n    'variable': insideString.variable,\n    'function': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cargo|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|java|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|sysctl|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\\s;|&])/,\n      lookbehind: true\n    },\n    'keyword': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\\s;|&])/,\n      lookbehind: true\n    },\n    // https://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n    'builtin': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:\\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\\s;|&])/,\n      lookbehind: true,\n      // Alias added to make those easier to distinguish from strings.\n      alias: 'class-name'\n    },\n    'boolean': {\n      pattern: /(^|[\\s;|&]|[<>]\\()(?:false|true)(?=$|[)\\s;|&])/,\n      lookbehind: true\n    },\n    'file-descriptor': {\n      pattern: /\\B&\\d\\b/,\n      alias: 'important'\n    },\n    'operator': {\n      // Lots of redirections here, but not just that.\n      pattern: /\\d?<>|>\\||\\+=|=[=~]?|!=?|<<[<-]?|[&\\d]?>>|\\d[<>]&?|[<>][&=]?|&[>&]?|\\|[&|]?/,\n      inside: {\n        'file-descriptor': {\n          pattern: /^\\d/,\n          alias: 'important'\n        }\n      }\n    },\n    'punctuation': /\\$?\\(\\(?|\\)\\)?|\\.\\.|[{}[\\];\\\\]/,\n    'number': {\n      pattern: /(^|\\s)(?:[1-9]\\d*|0)(?:[.,]\\d+)?\\b/,\n      lookbehind: true\n    }\n  };\n  commandAfterHeredoc.inside = Prism.languages.bash;\n\n  /* Patterns in command substitution. */\n  var toBeCopied = ['comment', 'function-name', 'for-or-select', 'assign-left', 'parameter', 'string', 'environment', 'function', 'keyword', 'builtin', 'boolean', 'file-descriptor', 'operator', 'punctuation', 'number'];\n  var inside = insideString.variable[1].inside;\n  for (var i = 0; i < toBeCopied.length; i++) {\n    inside[toBeCopied[i]] = Prism.languages.bash[toBeCopied[i]];\n  }\n  Prism.languages.sh = Prism.languages.bash;\n  Prism.languages.shell = Prism.languages.bash;\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAKhB,MAAI,UAAU;AACd,MAAI,sBAAsB;AAAA,IACxB,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,OAAO;AAAA;AAAA,IAEP,QAAQ;AAAA;AAAA,EACV;AACA,MAAI,eAAe;AAAA,IACjB,QAAQ;AAAA,IACR,eAAe;AAAA,MACb,SAAS,OAAO,QAAQ,OAAO;AAAA,MAC/B,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA;AAAA,MAEZ;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA;AAAA,UAEN,YAAY,CAAC;AAAA,YACX,SAAS;AAAA,YACT,YAAY;AAAA,UACd,GAAG,SAAS;AAAA,UACZ,UAAU;AAAA;AAAA,UAEV,YAAY;AAAA;AAAA,UAEZ,eAAe;AAAA,QACjB;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,YAAY;AAAA,QACd;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,eAAe;AAAA,YACb,SAAS,OAAO,UAAU,OAAO;AAAA,YACjC,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,MAAG;AAAA,IAAoB;AAAA;AAAA,IAEvB,UAAU;AAAA,EACZ;AACA,EAAAA,OAAM,UAAU,OAAO;AAAA,IACrB,WAAW;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjB;AAAA;AAAA,QAEE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,OAAO;AAAA,MACT;AAAA,MAAG;AAAA;AAAA,QAED,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IAAC;AAAA;AAAA,IAED,iBAAiB;AAAA,MACf,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA;AAAA;AAAA,IAGA,eAAe;AAAA,MACb,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,eAAe;AAAA,UACb,SAAS,OAAO,yBAAyB,OAAO;AAAA,UAChD,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA;AAAA,IAEA,aAAa;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY;AAAA,IACd;AAAA,IACA,UAAU;AAAA;AAAA,MAEV;AAAA,QACE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA;AAAA;AAAA,MAGA;AAAA,QACE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,QAAQ;AAAA,QACV;AAAA,MACF;AAAA;AAAA,MAEA;AAAA;AAAA,QAEE,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MAAG;AAAA;AAAA,QAED,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA,MAAG;AAAA;AAAA,QAED,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,UAAU,aAAa;AAAA,QACzB;AAAA,MACF;AAAA,IAAC;AAAA,IACD,eAAe;AAAA,MACb,SAAS,OAAO,SAAS,OAAO;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,IACA,YAAY,aAAa;AAAA,IACzB,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA;AAAA,IAEA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA;AAAA,MAEZ,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,mBAAmB;AAAA,MACjB,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA;AAAA,MAEV,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,IACA,eAAe;AAAA,IACf,UAAU;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,EACF;AACA,sBAAoB,SAASA,OAAM,UAAU;AAG7C,MAAI,aAAa,CAAC,WAAW,iBAAiB,iBAAiB,eAAe,aAAa,UAAU,eAAe,YAAY,WAAW,WAAW,WAAW,mBAAmB,YAAY,eAAe,QAAQ;AACvN,MAAI,SAAS,aAAa,SAAS,CAAC,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,WAAO,WAAW,CAAC,CAAC,IAAIA,OAAM,UAAU,KAAK,WAAW,CAAC,CAAC;AAAA,EAC5D;AACA,EAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACrC,EAAAA,OAAM,UAAU,QAAQA,OAAM,UAAU;AAC1C,GAAG,KAAK;", "names": ["Prism"]}