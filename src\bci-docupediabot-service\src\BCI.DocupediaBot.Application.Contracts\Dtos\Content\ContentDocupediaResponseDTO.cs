﻿namespace BCI.DocupediaBot.Application.Contracts.Dtos.Content

{
  using System;
  using System.Collections.Generic;
  using System.Text.Json.Serialization;

  public class ContentDocupediaResponseDTO
	{




		public string Path { get; set; }

		[JsonPropertyName("id")]
		public string Id { get; set; }

		[JsonPropertyName("type")]
		public string Type { get; set; }

		[JsonPropertyName("status")]
		public string Status { get; set; }

		[JsonPropertyName("title")]
		public string Title { get; set; }

		[JsonPropertyName("version")]
		public Version Version { get; set; }

		[JsonPropertyName("children")]
		public Children? Children { get; set; }

		[JsonPropertyName("body")]
		public Body Body { get; set; }

		[JsonPropertyName("metadata")]
		public Metadata Metadata { get; set; }

		[JsonPropertyName("extensions")]
		public Extensions Extensions { get; set; }

		[JsonPropertyName("_links")]
		public Links Links { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}


	public class Version
	{
		[JsonPropertyName("by")]
		public By By { get; set; }

		[JsonPropertyName("when")]
		public DateTimeOffset When { get; set; }

		[JsonPropertyName("number")]
		public int Number { get; set; }

		[JsonPropertyName("minorEdit")]
		public bool MinorEdit { get; set; }

		[JsonPropertyName("hidden")]
		public bool Hidden { get; set; }

		[JsonPropertyName("_links")]
		public Links Links { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}

	public class By
	{
		[JsonPropertyName("type")]
		public string Type { get; set; }

		[JsonPropertyName("username")]
		public string Username { get; set; }

		[JsonPropertyName("userKey")]
		public string UserKey { get; set; }

		[JsonPropertyName("profilePicture")]
		public ProfilePicture ProfilePicture { get; set; }

		[JsonPropertyName("displayName")]
		public string DisplayName { get; set; }

		[JsonPropertyName("_links")]
		public Links Links { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}

	public class ProfilePicture
	{
		[JsonPropertyName("path")]
		public string Path { get; set; }

		[JsonPropertyName("width")]
		public int Width { get; set; }

		[JsonPropertyName("height")]
		public int Height { get; set; }

		[JsonPropertyName("isDefault")]
		public bool IsDefault { get; set; }
	}

	public class Children
	{
		[JsonPropertyName("page")]
		public ChildrenPage Page { get; set; }

		[JsonPropertyName("_links")]
		public Links Links { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}

	public class ChildrenPage
	{
		[JsonPropertyName("results")]
		public List<ContentDocupediaResponseDTO> Results { get; set; }

		[JsonPropertyName("start")]
		public int Start { get; set; } = 0;

		[JsonPropertyName("limit")]
		public int Limit { get; } = 25;

		[JsonPropertyName("size")]
		public int Size { get; set; }
	}

	public class Result
	{
		[JsonPropertyName("id")]
		public string Id { get; set; }

		[JsonPropertyName("type")]
		public string Type { get; set; }

		[JsonPropertyName("status")]
		public string Status { get; set; }

		[JsonPropertyName("title")]
		public string Title { get; set; }

		[JsonPropertyName("extensions")]
		public Extensions Extensions { get; set; }

		[JsonPropertyName("_links")]
		public Links Links { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}

	public class Body
	{
		[JsonPropertyName("view")]
		public View View { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}

	public class View
	{
		[JsonPropertyName("value")]
		public string Value { get; set; }

		[JsonPropertyName("representation")]
		public string Representation { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}

	public class Metadata
	{
		[JsonPropertyName("labels")]
		public Labels Labels { get; set; }

		[JsonPropertyName("_expandable")]
		public Expandable Expandable { get; set; }
	}
	public class Labels
	{
		[JsonPropertyName("start")]
		public int Start { get; set; }

		[JsonPropertyName("limit")]
		public int Limit { get; set; }

		[JsonPropertyName("size")]
		public int Size { get; set; }

		[JsonPropertyName("_links")]
		public Links Links { get; set; }
	}

	public class Extensions
	{



	}


	public class Links
	{
		[JsonPropertyName("self")]
		public string Self { get; set; }

		[JsonPropertyName("webui")]
		public string Webui { get; set; }

		[JsonPropertyName("edit")]
		public string Edit { get; set; }

		[JsonPropertyName("tinyui")]
		public string Tinyui { get; set; }

		[JsonPropertyName("Page")]
		public string Page { get; set; }

		[JsonPropertyName("base")]
		public string Base { get; set; }

		[JsonPropertyName("context")]
		public string Context { get; set; }
	}

	public class Expandable
	{
		[JsonPropertyName("container")]
		public string Container { get; set; }

		[JsonPropertyName("metadata")]
		public string Metadata { get; set; }

		[JsonPropertyName("operations")]
		public string Operations { get; set; }

		[JsonPropertyName("children")]
		public string Children { get; set; }

		[JsonPropertyName("restrictions")]
		public string Restrictions { get; set; }

		[JsonPropertyName("history")]
		public string History { get; set; }

		[JsonPropertyName("ancestors")]
		public string Ancestors { get; set; }

		[JsonPropertyName("body")]
		public string Body { get; set; }

		[JsonPropertyName("version")]
		public string Version { get; set; }

		[JsonPropertyName("descendants")]
		public string Descendants { get; set; }

		[JsonPropertyName("space")]
		public string Space { get; set; }

		[JsonPropertyName("attachment")]
		public string Attachment { get; set; }

		[JsonPropertyName("com_k15t_scroll_scroll_platform__scroll_search_proxy_content_type")]
		public string ComK15tScrollScrollPlatformScrollSearchProxyContentType { get; set; }

		[JsonPropertyName("comment")]
		public string Comment { get; set; }
	}

}
