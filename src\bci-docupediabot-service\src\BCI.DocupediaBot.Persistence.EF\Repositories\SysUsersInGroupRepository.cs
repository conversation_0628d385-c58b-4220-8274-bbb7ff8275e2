﻿using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using BCI.DocupediaBot.Infrastructure.Database.Context;
using BCI.DocupediaBot.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Persistence.EF.Repositories
{
  public class SysUsersInGroupRepository : EntityRepository<SysUsersInGroup>, ISysUsersInGroupRepository
  {
    public SysUsersInGroupRepository(DocupediaBotDbContext dbContext) : base(dbContext)
    {
    }


    public async Task<Dictionary<Guid, List<Guid>>> GetGroupIdsByUserIdsBatchAsync(IEnumerable<Guid> userIds)
    {
      var userIdList = userIds.ToList();
      if (!userIdList.Any())
      {
        return new Dictionary<Guid, List<Guid>>();
      }

      var mappings = await DbContext.Set<SysUsersInGroup>()
        .AsNoTracking()
        .Where(m => userIdList.Contains(m.UserId))
        .ToListAsync();

      return userIdList.ToDictionary(
        userId => userId,
        userId => mappings.Where(m => m.UserId == userId).Select(m => m.GroupId).ToList()
      );
    }

    public async Task<Dictionary<Guid, List<Guid>>> GetUserIdsByGroupIdsBatchAsync(IEnumerable<Guid> groupIds)
    {
      var groupIdList = groupIds.ToList();
      if (!groupIdList.Any())
      {
        return new Dictionary<Guid, List<Guid>>();
      }

      var mappings = await DbContext.Set<SysUsersInGroup>()
        .AsNoTracking()
        .Where(m => groupIdList.Contains(m.GroupId))
        .ToListAsync();

      return groupIdList.ToDictionary(
        groupId => groupId,
        groupId => mappings.Where(m => m.GroupId == groupId).Select(m => m.UserId).ToList()
      );
    }

    public async Task<List<Guid>> GetGroupIdsByUserIdAsync(Guid userId)
    {
      return await DbContext.Set<SysUsersInGroup>()
        .AsNoTracking()
        .Where(m => m.UserId == userId)
        .Select(m => m.GroupId)
        .ToListAsync();
    }

    public async Task<List<Guid>> GetUserIdsByGroupIdAsync(Guid groupId)
    {
      return await DbContext.Set<SysUsersInGroup>()
        .AsNoTracking()
        .Where(m => m.GroupId == groupId)
        .Select(m => m.UserId)
        .ToListAsync();
    }

    public async Task<bool> ExistsMappingAsync(Guid userId, Guid groupId)
    {
      return await DbContext.Set<SysUsersInGroup>()
        .AsNoTracking()
        .AnyAsync(m => m.UserId == userId && m.GroupId == groupId);
    }
  }
}
