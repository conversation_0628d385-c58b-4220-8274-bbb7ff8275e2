﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Infrastructure.Entities
{
  public interface IEntity
  {
    Guid Id { get; set; }
    DateTime? CreationTime { get; set; }
    string Creator { get; set; }
    DateTime? ModificationTime { get; set; }
    string Modifier { get; set; }
    bool IsDeleted { get; set; }
    string TenantId { get; set; }
  }
}
