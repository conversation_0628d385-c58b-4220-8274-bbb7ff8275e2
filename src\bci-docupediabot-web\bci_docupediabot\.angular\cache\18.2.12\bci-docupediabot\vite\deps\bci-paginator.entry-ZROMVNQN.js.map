{"version": 3, "sources": ["../../../../../../node_modules/@bci-web-core/web-components/dist/esm/bci-paginator.entry.js"], "sourcesContent": ["/* Copyright (C) 2024. <PERSON> GmbH Copyright (C) 2024. Robert <PERSON> Manufacturing Solutions GmbH, Germany. All rights reserved. */\nimport { r as registerInstance, c as createEvent, h, H as Host } from './index-93dc8059.js';\nimport { n as normalizeKey, K as KEY, C as Corner, s as strings$4, a as strings$5, M as MDCMenu } from './component-655cd5b3.js';\nimport { s as setLocale, t as translate } from './utils-636fa948.js';\nimport { _ as __extends, a as __assign, M as MDCFoundation, b as MDCComponent, e as estimateScrollWidth, c as __values } from './ponyfill-78459bda.js';\nimport { M as MDCRipple, a as MDCRippleFoundation } from './component-d69b424e.js';\nimport './util-40cc7805.js';\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses$4 = {\n  LABEL_FLOAT_ABOVE: 'mdc-floating-label--float-above',\n  LABEL_REQUIRED: 'mdc-floating-label--required',\n  LABEL_SHAKE: 'mdc-floating-label--shake',\n  ROOT: 'mdc-floating-label'\n};\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCFloatingLabelFoundation = /** @class */function (_super) {\n  __extends(MDCFloatingLabelFoundation, _super);\n  function MDCFloatingLabelFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCFloatingLabelFoundation.defaultAdapter), adapter)) || this;\n    _this.shakeAnimationEndHandler = function () {\n      _this.handleShakeAnimationEnd();\n    };\n    return _this;\n  }\n  Object.defineProperty(MDCFloatingLabelFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses$4;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCFloatingLabelFoundation, \"defaultAdapter\", {\n    /**\n     * See {@link MDCFloatingLabelAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        getWidth: function () {\n          return 0;\n        },\n        registerInteractionHandler: function () {\n          return undefined;\n        },\n        deregisterInteractionHandler: function () {\n          return undefined;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCFloatingLabelFoundation.prototype.init = function () {\n    this.adapter.registerInteractionHandler('animationend', this.shakeAnimationEndHandler);\n  };\n  MDCFloatingLabelFoundation.prototype.destroy = function () {\n    this.adapter.deregisterInteractionHandler('animationend', this.shakeAnimationEndHandler);\n  };\n  /**\n   * Returns the width of the label element.\n   */\n  MDCFloatingLabelFoundation.prototype.getWidth = function () {\n    return this.adapter.getWidth();\n  };\n  /**\n   * Styles the label to produce a shake animation to indicate an error.\n   * @param shouldShake If true, adds the shake CSS class; otherwise, removes shake class.\n   */\n  MDCFloatingLabelFoundation.prototype.shake = function (shouldShake) {\n    var LABEL_SHAKE = MDCFloatingLabelFoundation.cssClasses.LABEL_SHAKE;\n    if (shouldShake) {\n      this.adapter.addClass(LABEL_SHAKE);\n    } else {\n      this.adapter.removeClass(LABEL_SHAKE);\n    }\n  };\n  /**\n   * Styles the label to float or dock.\n   * @param shouldFloat If true, adds the float CSS class; otherwise, removes float and shake classes to dock the label.\n   */\n  MDCFloatingLabelFoundation.prototype.float = function (shouldFloat) {\n    var _a = MDCFloatingLabelFoundation.cssClasses,\n      LABEL_FLOAT_ABOVE = _a.LABEL_FLOAT_ABOVE,\n      LABEL_SHAKE = _a.LABEL_SHAKE;\n    if (shouldFloat) {\n      this.adapter.addClass(LABEL_FLOAT_ABOVE);\n    } else {\n      this.adapter.removeClass(LABEL_FLOAT_ABOVE);\n      this.adapter.removeClass(LABEL_SHAKE);\n    }\n  };\n  /**\n   * Styles the label as required.\n   * @param isRequired If true, adds an asterisk to the label, indicating that it is required.\n   */\n  MDCFloatingLabelFoundation.prototype.setRequired = function (isRequired) {\n    var LABEL_REQUIRED = MDCFloatingLabelFoundation.cssClasses.LABEL_REQUIRED;\n    if (isRequired) {\n      this.adapter.addClass(LABEL_REQUIRED);\n    } else {\n      this.adapter.removeClass(LABEL_REQUIRED);\n    }\n  };\n  MDCFloatingLabelFoundation.prototype.handleShakeAnimationEnd = function () {\n    var LABEL_SHAKE = MDCFloatingLabelFoundation.cssClasses.LABEL_SHAKE;\n    this.adapter.removeClass(LABEL_SHAKE);\n  };\n  return MDCFloatingLabelFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCFloatingLabel = /** @class */function (_super) {\n  __extends(MDCFloatingLabel, _super);\n  function MDCFloatingLabel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCFloatingLabel.attachTo = function (root) {\n    return new MDCFloatingLabel(root);\n  };\n  /**\n   * Styles the label to produce the label shake for errors.\n   * @param shouldShake If true, shakes the label by adding a CSS class; otherwise, stops shaking by removing the class.\n   */\n  MDCFloatingLabel.prototype.shake = function (shouldShake) {\n    this.foundation.shake(shouldShake);\n  };\n  /**\n   * Styles the label to float/dock.\n   * @param shouldFloat If true, floats the label by adding a CSS class; otherwise, docks it by removing the class.\n   */\n  MDCFloatingLabel.prototype.float = function (shouldFloat) {\n    this.foundation.float(shouldFloat);\n  };\n  /**\n   * Styles the label as required.\n   * @param isRequired If true, adds an asterisk to the label, indicating that it is required.\n   */\n  MDCFloatingLabel.prototype.setRequired = function (isRequired) {\n    this.foundation.setRequired(isRequired);\n  };\n  MDCFloatingLabel.prototype.getWidth = function () {\n    return this.foundation.getWidth();\n  };\n  MDCFloatingLabel.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = {\n      addClass: function (className) {\n        return _this.root.classList.add(className);\n      },\n      removeClass: function (className) {\n        return _this.root.classList.remove(className);\n      },\n      getWidth: function () {\n        return estimateScrollWidth(_this.root);\n      },\n      registerInteractionHandler: function (evtType, handler) {\n        return _this.listen(evtType, handler);\n      },\n      deregisterInteractionHandler: function (evtType, handler) {\n        return _this.unlisten(evtType, handler);\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n    return new MDCFloatingLabelFoundation(adapter);\n  };\n  return MDCFloatingLabel;\n}(MDCComponent);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses$3 = {\n  LINE_RIPPLE_ACTIVE: 'mdc-line-ripple--active',\n  LINE_RIPPLE_DEACTIVATING: 'mdc-line-ripple--deactivating'\n};\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCLineRippleFoundation = /** @class */function (_super) {\n  __extends(MDCLineRippleFoundation, _super);\n  function MDCLineRippleFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCLineRippleFoundation.defaultAdapter), adapter)) || this;\n    _this.transitionEndHandler = function (evt) {\n      _this.handleTransitionEnd(evt);\n    };\n    return _this;\n  }\n  Object.defineProperty(MDCLineRippleFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses$3;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCLineRippleFoundation, \"defaultAdapter\", {\n    /**\n     * See {@link MDCLineRippleAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        hasClass: function () {\n          return false;\n        },\n        setStyle: function () {\n          return undefined;\n        },\n        registerEventHandler: function () {\n          return undefined;\n        },\n        deregisterEventHandler: function () {\n          return undefined;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCLineRippleFoundation.prototype.init = function () {\n    this.adapter.registerEventHandler('transitionend', this.transitionEndHandler);\n  };\n  MDCLineRippleFoundation.prototype.destroy = function () {\n    this.adapter.deregisterEventHandler('transitionend', this.transitionEndHandler);\n  };\n  MDCLineRippleFoundation.prototype.activate = function () {\n    this.adapter.removeClass(cssClasses$3.LINE_RIPPLE_DEACTIVATING);\n    this.adapter.addClass(cssClasses$3.LINE_RIPPLE_ACTIVE);\n  };\n  MDCLineRippleFoundation.prototype.setRippleCenter = function (xCoordinate) {\n    this.adapter.setStyle('transform-origin', xCoordinate + \"px center\");\n  };\n  MDCLineRippleFoundation.prototype.deactivate = function () {\n    this.adapter.addClass(cssClasses$3.LINE_RIPPLE_DEACTIVATING);\n  };\n  MDCLineRippleFoundation.prototype.handleTransitionEnd = function (evt) {\n    // Wait for the line ripple to be either transparent or opaque\n    // before emitting the animation end event\n    var isDeactivating = this.adapter.hasClass(cssClasses$3.LINE_RIPPLE_DEACTIVATING);\n    if (evt.propertyName === 'opacity') {\n      if (isDeactivating) {\n        this.adapter.removeClass(cssClasses$3.LINE_RIPPLE_ACTIVE);\n        this.adapter.removeClass(cssClasses$3.LINE_RIPPLE_DEACTIVATING);\n      }\n    }\n  };\n  return MDCLineRippleFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCLineRipple = /** @class */function (_super) {\n  __extends(MDCLineRipple, _super);\n  function MDCLineRipple() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCLineRipple.attachTo = function (root) {\n    return new MDCLineRipple(root);\n  };\n  /**\n   * Activates the line ripple\n   */\n  MDCLineRipple.prototype.activate = function () {\n    this.foundation.activate();\n  };\n  /**\n   * Deactivates the line ripple\n   */\n  MDCLineRipple.prototype.deactivate = function () {\n    this.foundation.deactivate();\n  };\n  /**\n   * Sets the transform origin given a user's click location.\n   * The `rippleCenter` is the x-coordinate of the middle of the ripple.\n   */\n  MDCLineRipple.prototype.setRippleCenter = function (xCoordinate) {\n    this.foundation.setRippleCenter(xCoordinate);\n  };\n  MDCLineRipple.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = {\n      addClass: function (className) {\n        return _this.root.classList.add(className);\n      },\n      removeClass: function (className) {\n        return _this.root.classList.remove(className);\n      },\n      hasClass: function (className) {\n        return _this.root.classList.contains(className);\n      },\n      setStyle: function (propertyName, value) {\n        return _this.root.style.setProperty(propertyName, value);\n      },\n      registerEventHandler: function (evtType, handler) {\n        return _this.listen(evtType, handler);\n      },\n      deregisterEventHandler: function (evtType, handler) {\n        return _this.unlisten(evtType, handler);\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n    return new MDCLineRippleFoundation(adapter);\n  };\n  return MDCLineRipple;\n}(MDCComponent);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar strings$3 = {\n  NOTCH_ELEMENT_SELECTOR: '.mdc-notched-outline__notch'\n};\nvar numbers$1 = {\n  // This should stay in sync with $mdc-notched-outline-padding * 2.\n  NOTCH_ELEMENT_PADDING: 8\n};\nvar cssClasses$2 = {\n  NO_LABEL: 'mdc-notched-outline--no-label',\n  OUTLINE_NOTCHED: 'mdc-notched-outline--notched',\n  OUTLINE_UPGRADED: 'mdc-notched-outline--upgraded'\n};\n\n/**\n * @license\n * Copyright 2017 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCNotchedOutlineFoundation = /** @class */function (_super) {\n  __extends(MDCNotchedOutlineFoundation, _super);\n  function MDCNotchedOutlineFoundation(adapter) {\n    return _super.call(this, __assign(__assign({}, MDCNotchedOutlineFoundation.defaultAdapter), adapter)) || this;\n  }\n  Object.defineProperty(MDCNotchedOutlineFoundation, \"strings\", {\n    get: function () {\n      return strings$3;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCNotchedOutlineFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses$2;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCNotchedOutlineFoundation, \"numbers\", {\n    get: function () {\n      return numbers$1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCNotchedOutlineFoundation, \"defaultAdapter\", {\n    /**\n     * See {@link MDCNotchedOutlineAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        setNotchWidthProperty: function () {\n          return undefined;\n        },\n        removeNotchWidthProperty: function () {\n          return undefined;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Adds the outline notched selector and updates the notch width calculated based off of notchWidth.\n   */\n  MDCNotchedOutlineFoundation.prototype.notch = function (notchWidth) {\n    var OUTLINE_NOTCHED = MDCNotchedOutlineFoundation.cssClasses.OUTLINE_NOTCHED;\n    if (notchWidth > 0) {\n      notchWidth += numbers$1.NOTCH_ELEMENT_PADDING; // Add padding from left/right.\n    }\n    this.adapter.setNotchWidthProperty(notchWidth);\n    this.adapter.addClass(OUTLINE_NOTCHED);\n  };\n  /**\n   * Removes notched outline selector to close the notch in the outline.\n   */\n  MDCNotchedOutlineFoundation.prototype.closeNotch = function () {\n    var OUTLINE_NOTCHED = MDCNotchedOutlineFoundation.cssClasses.OUTLINE_NOTCHED;\n    this.adapter.removeClass(OUTLINE_NOTCHED);\n    this.adapter.removeNotchWidthProperty();\n  };\n  return MDCNotchedOutlineFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2017 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCNotchedOutline = /** @class */function (_super) {\n  __extends(MDCNotchedOutline, _super);\n  function MDCNotchedOutline() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCNotchedOutline.attachTo = function (root) {\n    return new MDCNotchedOutline(root);\n  };\n  MDCNotchedOutline.prototype.initialSyncWithDOM = function () {\n    this.notchElement = this.root.querySelector(strings$3.NOTCH_ELEMENT_SELECTOR);\n    var label = this.root.querySelector('.' + MDCFloatingLabelFoundation.cssClasses.ROOT);\n    if (label) {\n      label.style.transitionDuration = '0s';\n      this.root.classList.add(cssClasses$2.OUTLINE_UPGRADED);\n      requestAnimationFrame(function () {\n        label.style.transitionDuration = '';\n      });\n    } else {\n      this.root.classList.add(cssClasses$2.NO_LABEL);\n    }\n  };\n  /**\n   * Updates classes and styles to open the notch to the specified width.\n   * @param notchWidth The notch width in the outline.\n   */\n  MDCNotchedOutline.prototype.notch = function (notchWidth) {\n    this.foundation.notch(notchWidth);\n  };\n  /**\n   * Updates classes and styles to close the notch.\n   */\n  MDCNotchedOutline.prototype.closeNotch = function () {\n    this.foundation.closeNotch();\n  };\n  MDCNotchedOutline.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = {\n      addClass: function (className) {\n        return _this.root.classList.add(className);\n      },\n      removeClass: function (className) {\n        return _this.root.classList.remove(className);\n      },\n      setNotchWidthProperty: function (width) {\n        _this.notchElement.style.setProperty('width', width + 'px');\n      },\n      removeNotchWidthProperty: function () {\n        _this.notchElement.style.removeProperty('width');\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n    return new MDCNotchedOutlineFoundation(adapter);\n  };\n  return MDCNotchedOutline;\n}(MDCComponent);\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses$1 = {\n  ACTIVATED: 'mdc-select--activated',\n  DISABLED: 'mdc-select--disabled',\n  FOCUSED: 'mdc-select--focused',\n  INVALID: 'mdc-select--invalid',\n  MENU_INVALID: 'mdc-select__menu--invalid',\n  OUTLINED: 'mdc-select--outlined',\n  REQUIRED: 'mdc-select--required',\n  ROOT: 'mdc-select',\n  WITH_LEADING_ICON: 'mdc-select--with-leading-icon'\n};\nvar strings$2 = {\n  ARIA_CONTROLS: 'aria-controls',\n  ARIA_DESCRIBEDBY: 'aria-describedby',\n  ARIA_SELECTED_ATTR: 'aria-selected',\n  CHANGE_EVENT: 'MDCSelect:change',\n  HIDDEN_INPUT_SELECTOR: 'input[type=\"hidden\"]',\n  LABEL_SELECTOR: '.mdc-floating-label',\n  LEADING_ICON_SELECTOR: '.mdc-select__icon',\n  LINE_RIPPLE_SELECTOR: '.mdc-line-ripple',\n  MENU_SELECTOR: '.mdc-select__menu',\n  OUTLINE_SELECTOR: '.mdc-notched-outline',\n  SELECTED_TEXT_SELECTOR: '.mdc-select__selected-text',\n  SELECT_ANCHOR_SELECTOR: '.mdc-select__anchor',\n  VALUE_ATTR: 'data-value'\n};\nvar numbers = {\n  LABEL_SCALE: 0.75,\n  UNSET_INDEX: -1,\n  CLICK_DEBOUNCE_TIMEOUT_MS: 330\n};\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCSelectFoundation = /** @class */function (_super) {\n  __extends(MDCSelectFoundation, _super);\n  /* istanbul ignore next: optional argument is not a branch statement */\n  /**\n   * @param adapter\n   * @param foundationMap Map from subcomponent names to their subfoundations.\n   */\n  function MDCSelectFoundation(adapter, foundationMap) {\n    if (foundationMap === void 0) {\n      foundationMap = {};\n    }\n    var _this = _super.call(this, __assign(__assign({}, MDCSelectFoundation.defaultAdapter), adapter)) || this;\n    // Disabled state\n    _this.disabled = false;\n    // isMenuOpen is used to track the state of the menu by listening to the\n    // MDCMenuSurface:closed event For reference, menu.open will return false if\n    // the menu is still closing, but isMenuOpen returns false only after the menu\n    // has closed\n    _this.isMenuOpen = false;\n    // By default, select is invalid if it is required but no value is selected.\n    _this.useDefaultValidation = true;\n    _this.customValidity = true;\n    _this.lastSelectedIndex = numbers.UNSET_INDEX;\n    _this.clickDebounceTimeout = 0;\n    _this.recentlyClicked = false;\n    _this.leadingIcon = foundationMap.leadingIcon;\n    _this.helperText = foundationMap.helperText;\n    return _this;\n  }\n  Object.defineProperty(MDCSelectFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses$1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelectFoundation, \"numbers\", {\n    get: function () {\n      return numbers;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelectFoundation, \"strings\", {\n    get: function () {\n      return strings$2;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelectFoundation, \"defaultAdapter\", {\n    /**\n     * See {@link MDCSelectAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        hasClass: function () {\n          return false;\n        },\n        activateBottomLine: function () {\n          return undefined;\n        },\n        deactivateBottomLine: function () {\n          return undefined;\n        },\n        getSelectedIndex: function () {\n          return -1;\n        },\n        setSelectedIndex: function () {\n          return undefined;\n        },\n        hasLabel: function () {\n          return false;\n        },\n        floatLabel: function () {\n          return undefined;\n        },\n        getLabelWidth: function () {\n          return 0;\n        },\n        setLabelRequired: function () {\n          return undefined;\n        },\n        hasOutline: function () {\n          return false;\n        },\n        notchOutline: function () {\n          return undefined;\n        },\n        closeOutline: function () {\n          return undefined;\n        },\n        setRippleCenter: function () {\n          return undefined;\n        },\n        notifyChange: function () {\n          return undefined;\n        },\n        setSelectedText: function () {\n          return undefined;\n        },\n        isSelectAnchorFocused: function () {\n          return false;\n        },\n        getSelectAnchorAttr: function () {\n          return '';\n        },\n        setSelectAnchorAttr: function () {\n          return undefined;\n        },\n        removeSelectAnchorAttr: function () {\n          return undefined;\n        },\n        addMenuClass: function () {\n          return undefined;\n        },\n        removeMenuClass: function () {\n          return undefined;\n        },\n        openMenu: function () {\n          return undefined;\n        },\n        closeMenu: function () {\n          return undefined;\n        },\n        getAnchorElement: function () {\n          return null;\n        },\n        setMenuAnchorElement: function () {\n          return undefined;\n        },\n        setMenuAnchorCorner: function () {\n          return undefined;\n        },\n        setMenuWrapFocus: function () {\n          return undefined;\n        },\n        focusMenuItemAtIndex: function () {\n          return undefined;\n        },\n        getMenuItemCount: function () {\n          return 0;\n        },\n        getMenuItemValues: function () {\n          return [];\n        },\n        getMenuItemTextAtIndex: function () {\n          return '';\n        },\n        isTypeaheadInProgress: function () {\n          return false;\n        },\n        typeaheadMatchItem: function () {\n          return -1;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /** Returns the index of the currently selected menu item, or -1 if none. */\n  MDCSelectFoundation.prototype.getSelectedIndex = function () {\n    return this.adapter.getSelectedIndex();\n  };\n  MDCSelectFoundation.prototype.setSelectedIndex = function (index, closeMenu, skipNotify) {\n    if (closeMenu === void 0) {\n      closeMenu = false;\n    }\n    if (skipNotify === void 0) {\n      skipNotify = false;\n    }\n    if (index >= this.adapter.getMenuItemCount()) {\n      return;\n    }\n    if (index === numbers.UNSET_INDEX) {\n      this.adapter.setSelectedText('');\n    } else {\n      this.adapter.setSelectedText(this.adapter.getMenuItemTextAtIndex(index).trim());\n    }\n    this.adapter.setSelectedIndex(index);\n    if (closeMenu) {\n      this.adapter.closeMenu();\n    }\n    if (!skipNotify && this.lastSelectedIndex !== index) {\n      this.handleChange();\n    }\n    this.lastSelectedIndex = index;\n  };\n  MDCSelectFoundation.prototype.setValue = function (value, skipNotify) {\n    if (skipNotify === void 0) {\n      skipNotify = false;\n    }\n    var index = this.adapter.getMenuItemValues().indexOf(value);\n    this.setSelectedIndex(index, /** closeMenu */false, skipNotify);\n  };\n  MDCSelectFoundation.prototype.getValue = function () {\n    var index = this.adapter.getSelectedIndex();\n    var menuItemValues = this.adapter.getMenuItemValues();\n    return index !== numbers.UNSET_INDEX ? menuItemValues[index] : '';\n  };\n  MDCSelectFoundation.prototype.getDisabled = function () {\n    return this.disabled;\n  };\n  MDCSelectFoundation.prototype.setDisabled = function (isDisabled) {\n    this.disabled = isDisabled;\n    if (this.disabled) {\n      this.adapter.addClass(cssClasses$1.DISABLED);\n      this.adapter.closeMenu();\n    } else {\n      this.adapter.removeClass(cssClasses$1.DISABLED);\n    }\n    if (this.leadingIcon) {\n      this.leadingIcon.setDisabled(this.disabled);\n    }\n    if (this.disabled) {\n      // Prevent click events from focusing select. Simply pointer-events: none\n      // is not enough since screenreader clicks may bypass this.\n      this.adapter.removeSelectAnchorAttr('tabindex');\n    } else {\n      this.adapter.setSelectAnchorAttr('tabindex', '0');\n    }\n    this.adapter.setSelectAnchorAttr('aria-disabled', this.disabled.toString());\n  };\n  /** Opens the menu. */\n  MDCSelectFoundation.prototype.openMenu = function () {\n    this.adapter.addClass(cssClasses$1.ACTIVATED);\n    this.adapter.openMenu();\n    this.isMenuOpen = true;\n    this.adapter.setSelectAnchorAttr('aria-expanded', 'true');\n  };\n  /**\n   * @param content Sets the content of the helper text.\n   */\n  MDCSelectFoundation.prototype.setHelperTextContent = function (content) {\n    if (this.helperText) {\n      this.helperText.setContent(content);\n    }\n  };\n  /**\n   * Re-calculates if the notched outline should be notched and if the label\n   * should float.\n   */\n  MDCSelectFoundation.prototype.layout = function () {\n    if (this.adapter.hasLabel()) {\n      var optionHasValue = this.getValue().length > 0;\n      var isFocused = this.adapter.hasClass(cssClasses$1.FOCUSED);\n      var shouldFloatAndNotch = optionHasValue || isFocused;\n      var isRequired = this.adapter.hasClass(cssClasses$1.REQUIRED);\n      this.notchOutline(shouldFloatAndNotch);\n      this.adapter.floatLabel(shouldFloatAndNotch);\n      this.adapter.setLabelRequired(isRequired);\n    }\n  };\n  /**\n   * Synchronizes the list of options with the state of the foundation. Call\n   * this whenever menu options are dynamically updated.\n   */\n  MDCSelectFoundation.prototype.layoutOptions = function () {\n    var menuItemValues = this.adapter.getMenuItemValues();\n    var selectedIndex = menuItemValues.indexOf(this.getValue());\n    this.setSelectedIndex(selectedIndex, /** closeMenu */false, /** skipNotify */true);\n  };\n  MDCSelectFoundation.prototype.handleMenuOpened = function () {\n    if (this.adapter.getMenuItemValues().length === 0) {\n      return;\n    }\n    // Menu should open to the last selected element, should open to first menu item otherwise.\n    var selectedIndex = this.getSelectedIndex();\n    var focusItemIndex = selectedIndex >= 0 ? selectedIndex : 0;\n    this.adapter.focusMenuItemAtIndex(focusItemIndex);\n  };\n  MDCSelectFoundation.prototype.handleMenuClosing = function () {\n    this.adapter.setSelectAnchorAttr('aria-expanded', 'false');\n  };\n  MDCSelectFoundation.prototype.handleMenuClosed = function () {\n    this.adapter.removeClass(cssClasses$1.ACTIVATED);\n    this.isMenuOpen = false;\n    // Unfocus the select if menu is closed without a selection\n    if (!this.adapter.isSelectAnchorFocused()) {\n      this.blur();\n    }\n  };\n  /**\n   * Handles value changes, via change event or programmatic updates.\n   */\n  MDCSelectFoundation.prototype.handleChange = function () {\n    this.layout();\n    this.adapter.notifyChange(this.getValue());\n    var isRequired = this.adapter.hasClass(cssClasses$1.REQUIRED);\n    if (isRequired && this.useDefaultValidation) {\n      this.setValid(this.isValid());\n    }\n  };\n  MDCSelectFoundation.prototype.handleMenuItemAction = function (index) {\n    this.setSelectedIndex(index, /** closeMenu */true);\n  };\n  /**\n   * Handles focus events from select element.\n   */\n  MDCSelectFoundation.prototype.handleFocus = function () {\n    this.adapter.addClass(cssClasses$1.FOCUSED);\n    this.layout();\n    this.adapter.activateBottomLine();\n  };\n  /**\n   * Handles blur events from select element.\n   */\n  MDCSelectFoundation.prototype.handleBlur = function () {\n    if (this.isMenuOpen) {\n      return;\n    }\n    this.blur();\n  };\n  MDCSelectFoundation.prototype.handleClick = function (normalizedX) {\n    if (this.disabled || this.recentlyClicked) {\n      return;\n    }\n    this.setClickDebounceTimeout();\n    if (this.isMenuOpen) {\n      this.adapter.closeMenu();\n      return;\n    }\n    this.adapter.setRippleCenter(normalizedX);\n    this.openMenu();\n  };\n  /**\n   * Handles keydown events on select element. Depending on the type of\n   * character typed, does typeahead matching or opens menu.\n   */\n  MDCSelectFoundation.prototype.handleKeydown = function (event) {\n    if (this.isMenuOpen || !this.adapter.hasClass(cssClasses$1.FOCUSED)) {\n      return;\n    }\n    var isEnter = normalizeKey(event) === KEY.ENTER;\n    var isSpace = normalizeKey(event) === KEY.SPACEBAR;\n    var arrowUp = normalizeKey(event) === KEY.ARROW_UP;\n    var arrowDown = normalizeKey(event) === KEY.ARROW_DOWN;\n    var isModifier = event.ctrlKey || event.metaKey;\n    // Typeahead\n    if (!isModifier && (!isSpace && event.key && event.key.length === 1 || isSpace && this.adapter.isTypeaheadInProgress())) {\n      var key = isSpace ? ' ' : event.key;\n      var typeaheadNextIndex = this.adapter.typeaheadMatchItem(key, this.getSelectedIndex());\n      if (typeaheadNextIndex >= 0) {\n        this.setSelectedIndex(typeaheadNextIndex);\n      }\n      event.preventDefault();\n      return;\n    }\n    if (!isEnter && !isSpace && !arrowUp && !arrowDown) {\n      return;\n    }\n    this.openMenu();\n    event.preventDefault();\n  };\n  /**\n   * Opens/closes the notched outline.\n   */\n  MDCSelectFoundation.prototype.notchOutline = function (openNotch) {\n    if (!this.adapter.hasOutline()) {\n      return;\n    }\n    var isFocused = this.adapter.hasClass(cssClasses$1.FOCUSED);\n    if (openNotch) {\n      var labelScale = numbers.LABEL_SCALE;\n      var labelWidth = this.adapter.getLabelWidth() * labelScale;\n      this.adapter.notchOutline(labelWidth);\n    } else if (!isFocused) {\n      this.adapter.closeOutline();\n    }\n  };\n  /**\n   * Sets the aria label of the leading icon.\n   */\n  MDCSelectFoundation.prototype.setLeadingIconAriaLabel = function (label) {\n    if (this.leadingIcon) {\n      this.leadingIcon.setAriaLabel(label);\n    }\n  };\n  /**\n   * Sets the text content of the leading icon.\n   */\n  MDCSelectFoundation.prototype.setLeadingIconContent = function (content) {\n    if (this.leadingIcon) {\n      this.leadingIcon.setContent(content);\n    }\n  };\n  MDCSelectFoundation.prototype.getUseDefaultValidation = function () {\n    return this.useDefaultValidation;\n  };\n  MDCSelectFoundation.prototype.setUseDefaultValidation = function (useDefaultValidation) {\n    this.useDefaultValidation = useDefaultValidation;\n  };\n  MDCSelectFoundation.prototype.setValid = function (isValid) {\n    if (!this.useDefaultValidation) {\n      this.customValidity = isValid;\n    }\n    this.adapter.setSelectAnchorAttr('aria-invalid', (!isValid).toString());\n    if (isValid) {\n      this.adapter.removeClass(cssClasses$1.INVALID);\n      this.adapter.removeMenuClass(cssClasses$1.MENU_INVALID);\n    } else {\n      this.adapter.addClass(cssClasses$1.INVALID);\n      this.adapter.addMenuClass(cssClasses$1.MENU_INVALID);\n    }\n    this.syncHelperTextValidity(isValid);\n  };\n  MDCSelectFoundation.prototype.isValid = function () {\n    if (this.useDefaultValidation && this.adapter.hasClass(cssClasses$1.REQUIRED) && !this.adapter.hasClass(cssClasses$1.DISABLED)) {\n      // See notes for required attribute under https://www.w3.org/TR/html52/sec-forms.html#the-select-element\n      // TL;DR: Invalid if no index is selected, or if the first index is selected and has an empty value.\n      return this.getSelectedIndex() !== numbers.UNSET_INDEX && (this.getSelectedIndex() !== 0 || Boolean(this.getValue()));\n    }\n    return this.customValidity;\n  };\n  MDCSelectFoundation.prototype.setRequired = function (isRequired) {\n    if (isRequired) {\n      this.adapter.addClass(cssClasses$1.REQUIRED);\n    } else {\n      this.adapter.removeClass(cssClasses$1.REQUIRED);\n    }\n    this.adapter.setSelectAnchorAttr('aria-required', isRequired.toString());\n    this.adapter.setLabelRequired(isRequired);\n  };\n  MDCSelectFoundation.prototype.getRequired = function () {\n    return this.adapter.getSelectAnchorAttr('aria-required') === 'true';\n  };\n  MDCSelectFoundation.prototype.init = function () {\n    var anchorEl = this.adapter.getAnchorElement();\n    if (anchorEl) {\n      this.adapter.setMenuAnchorElement(anchorEl);\n      this.adapter.setMenuAnchorCorner(Corner.BOTTOM_START);\n    }\n    this.adapter.setMenuWrapFocus(false);\n    this.setDisabled(this.adapter.hasClass(cssClasses$1.DISABLED));\n    this.syncHelperTextValidity(!this.adapter.hasClass(cssClasses$1.INVALID));\n    this.layout();\n    this.layoutOptions();\n  };\n  /**\n   * Unfocuses the select component.\n   */\n  MDCSelectFoundation.prototype.blur = function () {\n    this.adapter.removeClass(cssClasses$1.FOCUSED);\n    this.layout();\n    this.adapter.deactivateBottomLine();\n    var isRequired = this.adapter.hasClass(cssClasses$1.REQUIRED);\n    if (isRequired && this.useDefaultValidation) {\n      this.setValid(this.isValid());\n    }\n  };\n  MDCSelectFoundation.prototype.syncHelperTextValidity = function (isValid) {\n    if (!this.helperText) {\n      return;\n    }\n    this.helperText.setValidity(isValid);\n    var helperTextVisible = this.helperText.isVisible();\n    var helperTextId = this.helperText.getId();\n    if (helperTextVisible && helperTextId) {\n      this.adapter.setSelectAnchorAttr(strings$2.ARIA_DESCRIBEDBY, helperTextId);\n    } else {\n      // Needed because screenreaders will read labels pointed to by\n      // `aria-describedby` even if they are `aria-hidden`.\n      this.adapter.removeSelectAnchorAttr(strings$2.ARIA_DESCRIBEDBY);\n    }\n  };\n  MDCSelectFoundation.prototype.setClickDebounceTimeout = function () {\n    var _this = this;\n    clearTimeout(this.clickDebounceTimeout);\n    this.clickDebounceTimeout = setTimeout(function () {\n      _this.recentlyClicked = false;\n    }, numbers.CLICK_DEBOUNCE_TIMEOUT_MS);\n    this.recentlyClicked = true;\n  };\n  return MDCSelectFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar strings$1 = {\n  ARIA_HIDDEN: 'aria-hidden',\n  ROLE: 'role'\n};\nvar cssClasses = {\n  HELPER_TEXT_VALIDATION_MSG: 'mdc-select-helper-text--validation-msg',\n  HELPER_TEXT_VALIDATION_MSG_PERSISTENT: 'mdc-select-helper-text--validation-msg-persistent'\n};\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCSelectHelperTextFoundation = /** @class */function (_super) {\n  __extends(MDCSelectHelperTextFoundation, _super);\n  function MDCSelectHelperTextFoundation(adapter) {\n    return _super.call(this, __assign(__assign({}, MDCSelectHelperTextFoundation.defaultAdapter), adapter)) || this;\n  }\n  Object.defineProperty(MDCSelectHelperTextFoundation, \"cssClasses\", {\n    get: function () {\n      return cssClasses;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelectHelperTextFoundation, \"strings\", {\n    get: function () {\n      return strings$1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelectHelperTextFoundation, \"defaultAdapter\", {\n    /**\n     * See {@link MDCSelectHelperTextAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        addClass: function () {\n          return undefined;\n        },\n        removeClass: function () {\n          return undefined;\n        },\n        hasClass: function () {\n          return false;\n        },\n        setAttr: function () {\n          return undefined;\n        },\n        getAttr: function () {\n          return null;\n        },\n        removeAttr: function () {\n          return undefined;\n        },\n        setContent: function () {\n          return undefined;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * @return The ID of the helper text, or null if none is set.\n   */\n  MDCSelectHelperTextFoundation.prototype.getId = function () {\n    return this.adapter.getAttr('id');\n  };\n  /**\n   * @return Whether the helper text is currently visible.\n   */\n  MDCSelectHelperTextFoundation.prototype.isVisible = function () {\n    return this.adapter.getAttr(strings$1.ARIA_HIDDEN) !== 'true';\n  };\n  /**\n   * Sets the content of the helper text field.\n   */\n  MDCSelectHelperTextFoundation.prototype.setContent = function (content) {\n    this.adapter.setContent(content);\n  };\n  /**\n   * Sets the helper text to act as a validation message.\n   * By default, validation messages are hidden when the select is valid and\n   * visible when the select is invalid.\n   *\n   * @param isValidation True to make the helper text act as an error validation\n   *     message.\n   */\n  MDCSelectHelperTextFoundation.prototype.setValidation = function (isValidation) {\n    if (isValidation) {\n      this.adapter.addClass(cssClasses.HELPER_TEXT_VALIDATION_MSG);\n    } else {\n      this.adapter.removeClass(cssClasses.HELPER_TEXT_VALIDATION_MSG);\n    }\n  };\n  /**\n   * Sets the persistency of the validation helper text.\n   * This keeps the validation message visible even if the select is valid,\n   * though it will be displayed in the normal (grey) color.\n   */\n  MDCSelectHelperTextFoundation.prototype.setValidationMsgPersistent = function (isPersistent) {\n    if (isPersistent) {\n      this.adapter.addClass(cssClasses.HELPER_TEXT_VALIDATION_MSG_PERSISTENT);\n    } else {\n      this.adapter.removeClass(cssClasses.HELPER_TEXT_VALIDATION_MSG_PERSISTENT);\n    }\n  };\n  /**\n   * @return Whether the helper text acts as a validation message.\n   * By default, validation messages are hidden when the select is valid and\n   * visible when the select is invalid.\n   */\n  MDCSelectHelperTextFoundation.prototype.getIsValidation = function () {\n    return this.adapter.hasClass(cssClasses.HELPER_TEXT_VALIDATION_MSG);\n  };\n  /**\n   * @return Whether the validation helper text persists even if the input is\n   * valid. If it is, it will be displayed in the normal (grey) color.\n   */\n  MDCSelectHelperTextFoundation.prototype.getIsValidationMsgPersistent = function () {\n    return this.adapter.hasClass(cssClasses.HELPER_TEXT_VALIDATION_MSG_PERSISTENT);\n  };\n  /**\n   * When acting as a validation message, shows/hides the helper text and\n   * triggers alerts as necessary based on the select's validity.\n   */\n  MDCSelectHelperTextFoundation.prototype.setValidity = function (selectIsValid) {\n    var isValidationMsg = this.adapter.hasClass(cssClasses.HELPER_TEXT_VALIDATION_MSG);\n    if (!isValidationMsg) {\n      // Non-validating helper-text is always displayed and does not participate\n      // in validation logic.\n      return;\n    }\n    var isPersistentValidationMsg = this.adapter.hasClass(cssClasses.HELPER_TEXT_VALIDATION_MSG_PERSISTENT);\n    // Validating helper text is displayed if select is invalid, unless it is\n    // set as persistent, in which case it always displays.\n    var msgShouldDisplay = !selectIsValid || isPersistentValidationMsg;\n    if (msgShouldDisplay) {\n      this.showToScreenReader();\n      // In addition to displaying, also trigger an alert if the select\n      // has become invalid.\n      if (!selectIsValid) {\n        this.adapter.setAttr(strings$1.ROLE, 'alert');\n      } else {\n        this.adapter.removeAttr(strings$1.ROLE);\n      }\n      return;\n    }\n    // Hide everything.\n    this.adapter.removeAttr(strings$1.ROLE);\n    this.hide();\n  };\n  /**\n   * Makes the helper text visible to screen readers.\n   */\n  MDCSelectHelperTextFoundation.prototype.showToScreenReader = function () {\n    this.adapter.removeAttr(strings$1.ARIA_HIDDEN);\n  };\n  /**\n   * Hides the help text from screen readers.\n   */\n  MDCSelectHelperTextFoundation.prototype.hide = function () {\n    this.adapter.setAttr(strings$1.ARIA_HIDDEN, 'true');\n  };\n  return MDCSelectHelperTextFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCSelectHelperText = /** @class */function (_super) {\n  __extends(MDCSelectHelperText, _super);\n  function MDCSelectHelperText() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCSelectHelperText.attachTo = function (root) {\n    return new MDCSelectHelperText(root);\n  };\n  Object.defineProperty(MDCSelectHelperText.prototype, \"foundationForSelect\", {\n    // Provided for access by MDCSelect component\n    get: function () {\n      return this.foundation;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCSelectHelperText.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = {\n      addClass: function (className) {\n        return _this.root.classList.add(className);\n      },\n      removeClass: function (className) {\n        return _this.root.classList.remove(className);\n      },\n      hasClass: function (className) {\n        return _this.root.classList.contains(className);\n      },\n      getAttr: function (attr) {\n        return _this.root.getAttribute(attr);\n      },\n      setAttr: function (attr, value) {\n        return _this.root.setAttribute(attr, value);\n      },\n      removeAttr: function (attr) {\n        return _this.root.removeAttribute(attr);\n      },\n      setContent: function (content) {\n        _this.root.textContent = content;\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n    return new MDCSelectHelperTextFoundation(adapter);\n  };\n  return MDCSelectHelperText;\n}(MDCComponent);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar strings = {\n  ICON_EVENT: 'MDCSelect:icon',\n  ICON_ROLE: 'button'\n};\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar INTERACTION_EVENTS = ['click', 'keydown'];\nvar MDCSelectIconFoundation = /** @class */function (_super) {\n  __extends(MDCSelectIconFoundation, _super);\n  function MDCSelectIconFoundation(adapter) {\n    var _this = _super.call(this, __assign(__assign({}, MDCSelectIconFoundation.defaultAdapter), adapter)) || this;\n    _this.savedTabIndex = null;\n    _this.interactionHandler = function (evt) {\n      _this.handleInteraction(evt);\n    };\n    return _this;\n  }\n  Object.defineProperty(MDCSelectIconFoundation, \"strings\", {\n    get: function () {\n      return strings;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelectIconFoundation, \"defaultAdapter\", {\n    /**\n     * See {@link MDCSelectIconAdapter} for typing information on parameters and return types.\n     */\n    get: function () {\n      // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n      return {\n        getAttr: function () {\n          return null;\n        },\n        setAttr: function () {\n          return undefined;\n        },\n        removeAttr: function () {\n          return undefined;\n        },\n        setContent: function () {\n          return undefined;\n        },\n        registerInteractionHandler: function () {\n          return undefined;\n        },\n        deregisterInteractionHandler: function () {\n          return undefined;\n        },\n        notifyIconAction: function () {\n          return undefined;\n        }\n      };\n      // tslint:enable:object-literal-sort-keys\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCSelectIconFoundation.prototype.init = function () {\n    var e_1, _a;\n    this.savedTabIndex = this.adapter.getAttr('tabindex');\n    try {\n      for (var INTERACTION_EVENTS_1 = __values(INTERACTION_EVENTS), INTERACTION_EVENTS_1_1 = INTERACTION_EVENTS_1.next(); !INTERACTION_EVENTS_1_1.done; INTERACTION_EVENTS_1_1 = INTERACTION_EVENTS_1.next()) {\n        var evtType = INTERACTION_EVENTS_1_1.value;\n        this.adapter.registerInteractionHandler(evtType, this.interactionHandler);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (INTERACTION_EVENTS_1_1 && !INTERACTION_EVENTS_1_1.done && (_a = INTERACTION_EVENTS_1.return)) _a.call(INTERACTION_EVENTS_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  };\n  MDCSelectIconFoundation.prototype.destroy = function () {\n    var e_2, _a;\n    try {\n      for (var INTERACTION_EVENTS_2 = __values(INTERACTION_EVENTS), INTERACTION_EVENTS_2_1 = INTERACTION_EVENTS_2.next(); !INTERACTION_EVENTS_2_1.done; INTERACTION_EVENTS_2_1 = INTERACTION_EVENTS_2.next()) {\n        var evtType = INTERACTION_EVENTS_2_1.value;\n        this.adapter.deregisterInteractionHandler(evtType, this.interactionHandler);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (INTERACTION_EVENTS_2_1 && !INTERACTION_EVENTS_2_1.done && (_a = INTERACTION_EVENTS_2.return)) _a.call(INTERACTION_EVENTS_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n  };\n  MDCSelectIconFoundation.prototype.setDisabled = function (disabled) {\n    if (!this.savedTabIndex) {\n      return;\n    }\n    if (disabled) {\n      this.adapter.setAttr('tabindex', '-1');\n      this.adapter.removeAttr('role');\n    } else {\n      this.adapter.setAttr('tabindex', this.savedTabIndex);\n      this.adapter.setAttr('role', strings.ICON_ROLE);\n    }\n  };\n  MDCSelectIconFoundation.prototype.setAriaLabel = function (label) {\n    this.adapter.setAttr('aria-label', label);\n  };\n  MDCSelectIconFoundation.prototype.setContent = function (content) {\n    this.adapter.setContent(content);\n  };\n  MDCSelectIconFoundation.prototype.handleInteraction = function (evt) {\n    var isEnterKey = evt.key === 'Enter' || evt.keyCode === 13;\n    if (evt.type === 'click' || isEnterKey) {\n      this.adapter.notifyIconAction();\n    }\n  };\n  return MDCSelectIconFoundation;\n}(MDCFoundation);\n\n/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCSelectIcon = /** @class */function (_super) {\n  __extends(MDCSelectIcon, _super);\n  function MDCSelectIcon() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCSelectIcon.attachTo = function (root) {\n    return new MDCSelectIcon(root);\n  };\n  Object.defineProperty(MDCSelectIcon.prototype, \"foundationForSelect\", {\n    // Provided for access by MDCSelect component\n    get: function () {\n      return this.foundation;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCSelectIcon.prototype.getDefaultFoundation = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = {\n      getAttr: function (attr) {\n        return _this.root.getAttribute(attr);\n      },\n      setAttr: function (attr, value) {\n        return _this.root.setAttribute(attr, value);\n      },\n      removeAttr: function (attr) {\n        return _this.root.removeAttribute(attr);\n      },\n      setContent: function (content) {\n        _this.root.textContent = content;\n      },\n      registerInteractionHandler: function (evtType, handler) {\n        return _this.listen(evtType, handler);\n      },\n      deregisterInteractionHandler: function (evtType, handler) {\n        return _this.unlisten(evtType, handler);\n      },\n      notifyIconAction: function () {\n        return _this.emit(MDCSelectIconFoundation.strings.ICON_EVENT, {} /* evtData */, true /* shouldBubble */);\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n    return new MDCSelectIconFoundation(adapter);\n  };\n  return MDCSelectIcon;\n}(MDCComponent);\n\n/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCSelect = /** @class */function (_super) {\n  __extends(MDCSelect, _super);\n  function MDCSelect() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  MDCSelect.attachTo = function (root) {\n    return new MDCSelect(root);\n  };\n  MDCSelect.prototype.initialize = function (labelFactory, lineRippleFactory, outlineFactory, menuFactory, iconFactory, helperTextFactory) {\n    if (labelFactory === void 0) {\n      labelFactory = function (el) {\n        return new MDCFloatingLabel(el);\n      };\n    }\n    if (lineRippleFactory === void 0) {\n      lineRippleFactory = function (el) {\n        return new MDCLineRipple(el);\n      };\n    }\n    if (outlineFactory === void 0) {\n      outlineFactory = function (el) {\n        return new MDCNotchedOutline(el);\n      };\n    }\n    if (menuFactory === void 0) {\n      menuFactory = function (el) {\n        return new MDCMenu(el);\n      };\n    }\n    if (iconFactory === void 0) {\n      iconFactory = function (el) {\n        return new MDCSelectIcon(el);\n      };\n    }\n    if (helperTextFactory === void 0) {\n      helperTextFactory = function (el) {\n        return new MDCSelectHelperText(el);\n      };\n    }\n    this.selectAnchor = this.root.querySelector(strings$2.SELECT_ANCHOR_SELECTOR);\n    this.selectedText = this.root.querySelector(strings$2.SELECTED_TEXT_SELECTOR);\n    this.hiddenInput = this.root.querySelector(strings$2.HIDDEN_INPUT_SELECTOR);\n    if (!this.selectedText) {\n      throw new Error('MDCSelect: Missing required element: The following selector must be present: ' + (\"'\" + strings$2.SELECTED_TEXT_SELECTOR + \"'\"));\n    }\n    if (this.selectAnchor.hasAttribute(strings$2.ARIA_CONTROLS)) {\n      var helperTextElement = document.getElementById(this.selectAnchor.getAttribute(strings$2.ARIA_CONTROLS));\n      if (helperTextElement) {\n        this.helperText = helperTextFactory(helperTextElement);\n      }\n    }\n    this.menuSetup(menuFactory);\n    var labelElement = this.root.querySelector(strings$2.LABEL_SELECTOR);\n    this.label = labelElement ? labelFactory(labelElement) : null;\n    var lineRippleElement = this.root.querySelector(strings$2.LINE_RIPPLE_SELECTOR);\n    this.lineRipple = lineRippleElement ? lineRippleFactory(lineRippleElement) : null;\n    var outlineElement = this.root.querySelector(strings$2.OUTLINE_SELECTOR);\n    this.outline = outlineElement ? outlineFactory(outlineElement) : null;\n    var leadingIcon = this.root.querySelector(strings$2.LEADING_ICON_SELECTOR);\n    if (leadingIcon) {\n      this.leadingIcon = iconFactory(leadingIcon);\n    }\n    if (!this.root.classList.contains(cssClasses$1.OUTLINED)) {\n      this.ripple = this.createRipple();\n    }\n  };\n  /**\n   * Initializes the select's event listeners and internal state based\n   * on the environment's state.\n   */\n  MDCSelect.prototype.initialSyncWithDOM = function () {\n    var _this = this;\n    this.handleFocus = function () {\n      _this.foundation.handleFocus();\n    };\n    this.handleBlur = function () {\n      _this.foundation.handleBlur();\n    };\n    this.handleClick = function (evt) {\n      _this.selectAnchor.focus();\n      _this.foundation.handleClick(_this.getNormalizedXCoordinate(evt));\n    };\n    this.handleKeydown = function (evt) {\n      _this.foundation.handleKeydown(evt);\n    };\n    this.handleMenuItemAction = function (evt) {\n      _this.foundation.handleMenuItemAction(evt.detail.index);\n    };\n    this.handleMenuOpened = function () {\n      _this.foundation.handleMenuOpened();\n    };\n    this.handleMenuClosed = function () {\n      _this.foundation.handleMenuClosed();\n    };\n    this.handleMenuClosing = function () {\n      _this.foundation.handleMenuClosing();\n    };\n    this.selectAnchor.addEventListener('focus', this.handleFocus);\n    this.selectAnchor.addEventListener('blur', this.handleBlur);\n    this.selectAnchor.addEventListener('click', this.handleClick);\n    this.selectAnchor.addEventListener('keydown', this.handleKeydown);\n    this.menu.listen(strings$4.CLOSED_EVENT, this.handleMenuClosed);\n    this.menu.listen(strings$4.CLOSING_EVENT, this.handleMenuClosing);\n    this.menu.listen(strings$4.OPENED_EVENT, this.handleMenuOpened);\n    this.menu.listen(strings$5.SELECTED_EVENT, this.handleMenuItemAction);\n    if (this.hiddenInput) {\n      if (this.hiddenInput.value) {\n        // If the hidden input already has a value, use it to restore the\n        // select's value. This can happen e.g. if the user goes back or (in\n        // some browsers) refreshes the page.\n        this.foundation.setValue(this.hiddenInput.value, /** skipNotify */true);\n        this.foundation.layout();\n        return;\n      }\n      this.hiddenInput.value = this.value;\n    }\n  };\n  MDCSelect.prototype.destroy = function () {\n    this.selectAnchor.removeEventListener('focus', this.handleFocus);\n    this.selectAnchor.removeEventListener('blur', this.handleBlur);\n    this.selectAnchor.removeEventListener('keydown', this.handleKeydown);\n    this.selectAnchor.removeEventListener('click', this.handleClick);\n    this.menu.unlisten(strings$4.CLOSED_EVENT, this.handleMenuClosed);\n    this.menu.unlisten(strings$4.OPENED_EVENT, this.handleMenuOpened);\n    this.menu.unlisten(strings$5.SELECTED_EVENT, this.handleMenuItemAction);\n    this.menu.destroy();\n    if (this.ripple) {\n      this.ripple.destroy();\n    }\n    if (this.outline) {\n      this.outline.destroy();\n    }\n    if (this.leadingIcon) {\n      this.leadingIcon.destroy();\n    }\n    if (this.helperText) {\n      this.helperText.destroy();\n    }\n    _super.prototype.destroy.call(this);\n  };\n  Object.defineProperty(MDCSelect.prototype, \"value\", {\n    get: function () {\n      return this.foundation.getValue();\n    },\n    set: function (value) {\n      this.foundation.setValue(value);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCSelect.prototype.setValue = function (value, skipNotify) {\n    if (skipNotify === void 0) {\n      skipNotify = false;\n    }\n    this.foundation.setValue(value, skipNotify);\n  };\n  Object.defineProperty(MDCSelect.prototype, \"selectedIndex\", {\n    get: function () {\n      return this.foundation.getSelectedIndex();\n    },\n    set: function (selectedIndex) {\n      this.foundation.setSelectedIndex(selectedIndex, /* closeMenu */true);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  MDCSelect.prototype.setSelectedIndex = function (selectedIndex, skipNotify) {\n    if (skipNotify === void 0) {\n      skipNotify = false;\n    }\n    this.foundation.setSelectedIndex(selectedIndex, /* closeMenu */true, skipNotify);\n  };\n  Object.defineProperty(MDCSelect.prototype, \"disabled\", {\n    get: function () {\n      return this.foundation.getDisabled();\n    },\n    set: function (disabled) {\n      this.foundation.setDisabled(disabled);\n      if (this.hiddenInput) {\n        this.hiddenInput.disabled = disabled;\n      }\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelect.prototype, \"leadingIconAriaLabel\", {\n    set: function (label) {\n      this.foundation.setLeadingIconAriaLabel(label);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelect.prototype, \"leadingIconContent\", {\n    /**\n     * Sets the text content of the leading icon.\n     */\n    set: function (content) {\n      this.foundation.setLeadingIconContent(content);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelect.prototype, \"helperTextContent\", {\n    /**\n     * Sets the text content of the helper text.\n     */\n    set: function (content) {\n      this.foundation.setHelperTextContent(content);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelect.prototype, \"useDefaultValidation\", {\n    /**\n     * Enables or disables the default validation scheme where a required select\n     * must be non-empty. Set to false for custom validation.\n     * @param useDefaultValidation Set this to false to ignore default\n     *     validation scheme.\n     */\n    set: function (useDefaultValidation) {\n      this.foundation.setUseDefaultValidation(useDefaultValidation);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelect.prototype, \"valid\", {\n    /**\n     * Checks if the select is in a valid state.\n     */\n    get: function () {\n      return this.foundation.isValid();\n    },\n    /**\n     * Sets the current invalid state of the select.\n     */\n    set: function (isValid) {\n      this.foundation.setValid(isValid);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(MDCSelect.prototype, \"required\", {\n    /**\n     * Returns whether the select is required.\n     */\n    get: function () {\n      return this.foundation.getRequired();\n    },\n    /**\n     * Sets the control to the required state.\n     */\n    set: function (isRequired) {\n      this.foundation.setRequired(isRequired);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Re-calculates if the notched outline should be notched and if the label\n   * should float.\n   */\n  MDCSelect.prototype.layout = function () {\n    this.foundation.layout();\n  };\n  /**\n   * Synchronizes the list of options with the state of the foundation. Call\n   * this whenever menu options are dynamically updated.\n   */\n  MDCSelect.prototype.layoutOptions = function () {\n    this.foundation.layoutOptions();\n    this.menu.layout();\n    // Update cached menuItemValues for adapter.\n    this.menuItemValues = this.menu.items.map(function (el) {\n      return el.getAttribute(strings$2.VALUE_ATTR) || '';\n    });\n    if (this.hiddenInput) {\n      this.hiddenInput.value = this.value;\n    }\n  };\n  MDCSelect.prototype.getDefaultFoundation = function () {\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    var adapter = __assign(__assign(__assign(__assign({}, this.getSelectAdapterMethods()), this.getCommonAdapterMethods()), this.getOutlineAdapterMethods()), this.getLabelAdapterMethods());\n    return new MDCSelectFoundation(adapter, this.getFoundationMap());\n  };\n  /**\n   * Handles setup for the menu.\n   */\n  MDCSelect.prototype.menuSetup = function (menuFactory) {\n    this.menuElement = this.root.querySelector(strings$2.MENU_SELECTOR);\n    this.menu = menuFactory(this.menuElement);\n    this.menu.hasTypeahead = true;\n    this.menu.singleSelection = true;\n    this.menuItemValues = this.menu.items.map(function (el) {\n      return el.getAttribute(strings$2.VALUE_ATTR) || '';\n    });\n  };\n  MDCSelect.prototype.createRipple = function () {\n    var _this = this;\n    // DO NOT INLINE this variable. For backward compatibility, foundations take a Partial<MDCFooAdapter>.\n    // To ensure we don't accidentally omit any methods, we need a separate, strongly typed adapter variable.\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    var adapter = __assign(__assign({}, MDCRipple.createAdapter({\n      root: this.selectAnchor\n    })), {\n      registerInteractionHandler: function (evtType, handler) {\n        _this.selectAnchor.addEventListener(evtType, handler);\n      },\n      deregisterInteractionHandler: function (evtType, handler) {\n        _this.selectAnchor.removeEventListener(evtType, handler);\n      }\n    });\n    // tslint:enable:object-literal-sort-keys\n    return new MDCRipple(this.selectAnchor, new MDCRippleFoundation(adapter));\n  };\n  MDCSelect.prototype.getSelectAdapterMethods = function () {\n    var _this = this;\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    return {\n      getMenuItemAttr: function (menuItem, attr) {\n        return menuItem.getAttribute(attr);\n      },\n      setSelectedText: function (text) {\n        _this.selectedText.textContent = text;\n      },\n      isSelectAnchorFocused: function () {\n        return document.activeElement === _this.selectAnchor;\n      },\n      getSelectAnchorAttr: function (attr) {\n        return _this.selectAnchor.getAttribute(attr);\n      },\n      setSelectAnchorAttr: function (attr, value) {\n        _this.selectAnchor.setAttribute(attr, value);\n      },\n      removeSelectAnchorAttr: function (attr) {\n        _this.selectAnchor.removeAttribute(attr);\n      },\n      addMenuClass: function (className) {\n        _this.menuElement.classList.add(className);\n      },\n      removeMenuClass: function (className) {\n        _this.menuElement.classList.remove(className);\n      },\n      openMenu: function () {\n        _this.menu.open = true;\n      },\n      closeMenu: function () {\n        _this.menu.open = false;\n      },\n      getAnchorElement: function () {\n        return _this.root.querySelector(strings$2.SELECT_ANCHOR_SELECTOR);\n      },\n      setMenuAnchorElement: function (anchorEl) {\n        _this.menu.setAnchorElement(anchorEl);\n      },\n      setMenuAnchorCorner: function (anchorCorner) {\n        _this.menu.setAnchorCorner(anchorCorner);\n      },\n      setMenuWrapFocus: function (wrapFocus) {\n        _this.menu.wrapFocus = wrapFocus;\n      },\n      getSelectedIndex: function () {\n        var index = _this.menu.selectedIndex;\n        return index instanceof Array ? index[0] : index;\n      },\n      setSelectedIndex: function (index) {\n        _this.menu.selectedIndex = index;\n      },\n      focusMenuItemAtIndex: function (index) {\n        _this.menu.items[index].focus();\n      },\n      getMenuItemCount: function () {\n        return _this.menu.items.length;\n      },\n      // Cache menu item values. layoutOptions() updates this cache.\n      getMenuItemValues: function () {\n        return _this.menuItemValues;\n      },\n      getMenuItemTextAtIndex: function (index) {\n        return _this.menu.getPrimaryTextAtIndex(index);\n      },\n      isTypeaheadInProgress: function () {\n        return _this.menu.typeaheadInProgress;\n      },\n      typeaheadMatchItem: function (nextChar, startingIndex) {\n        return _this.menu.typeaheadMatchItem(nextChar, startingIndex);\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n  };\n  MDCSelect.prototype.getCommonAdapterMethods = function () {\n    var _this = this;\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    return {\n      addClass: function (className) {\n        _this.root.classList.add(className);\n      },\n      removeClass: function (className) {\n        _this.root.classList.remove(className);\n      },\n      hasClass: function (className) {\n        return _this.root.classList.contains(className);\n      },\n      setRippleCenter: function (normalizedX) {\n        _this.lineRipple && _this.lineRipple.setRippleCenter(normalizedX);\n      },\n      activateBottomLine: function () {\n        _this.lineRipple && _this.lineRipple.activate();\n      },\n      deactivateBottomLine: function () {\n        _this.lineRipple && _this.lineRipple.deactivate();\n      },\n      notifyChange: function (value) {\n        if (_this.hiddenInput) {\n          _this.hiddenInput.value = value;\n        }\n        var index = _this.selectedIndex;\n        _this.emit(strings$2.CHANGE_EVENT, {\n          value: value,\n          index: index\n        }, true /* shouldBubble  */);\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n  };\n  MDCSelect.prototype.getOutlineAdapterMethods = function () {\n    var _this = this;\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    return {\n      hasOutline: function () {\n        return Boolean(_this.outline);\n      },\n      notchOutline: function (labelWidth) {\n        _this.outline && _this.outline.notch(labelWidth);\n      },\n      closeOutline: function () {\n        _this.outline && _this.outline.closeNotch();\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n  };\n  MDCSelect.prototype.getLabelAdapterMethods = function () {\n    var _this = this;\n    // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.\n    return {\n      hasLabel: function () {\n        return !!_this.label;\n      },\n      floatLabel: function (shouldFloat) {\n        _this.label && _this.label.float(shouldFloat);\n      },\n      getLabelWidth: function () {\n        return _this.label ? _this.label.getWidth() : 0;\n      },\n      setLabelRequired: function (isRequired) {\n        _this.label && _this.label.setRequired(isRequired);\n      }\n    };\n    // tslint:enable:object-literal-sort-keys\n  };\n  /**\n   * Calculates where the line ripple should start based on the x coordinate within the component.\n   */\n  MDCSelect.prototype.getNormalizedXCoordinate = function (evt) {\n    var targetClientRect = evt.target.getBoundingClientRect();\n    var xCoordinate = this.isTouchEvent(evt) ? evt.touches[0].clientX : evt.clientX;\n    return xCoordinate - targetClientRect.left;\n  };\n  MDCSelect.prototype.isTouchEvent = function (evt) {\n    return Boolean(evt.touches);\n  };\n  /**\n   * Returns a map of all subcomponents to subfoundations.\n   */\n  MDCSelect.prototype.getFoundationMap = function () {\n    return {\n      helperText: this.helperText ? this.helperText.foundationForSelect : undefined,\n      leadingIcon: this.leadingIcon ? this.leadingIcon.foundationForSelect : undefined\n    };\n  };\n  return MDCSelect;\n}(MDCComponent);\nconst paginatorComponentCss = \"@charset \\\"UTF-8\\\";.mdc-deprecated-list{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);line-height:1.75rem;line-height:var(--mdc-typography-subtitle1-line-height, 1.75rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);line-height:1.5rem;margin:0;padding:8px 0;list-style-type:none;color:rgba(0, 0, 0, 0.87);color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mdc-deprecated-list:focus{outline:none}.mdc-deprecated-list-item{height:48px}.mdc-deprecated-list-item__secondary-text{color:rgba(0, 0, 0, 0.54);color:var(--mdc-theme-text-secondary-on-background, rgba(0, 0, 0, 0.54))}.mdc-deprecated-list-item__graphic{background-color:transparent}.mdc-deprecated-list-item__graphic{color:rgba(0, 0, 0, 0.38);color:var(--mdc-theme-text-icon-on-background, rgba(0, 0, 0, 0.38))}.mdc-deprecated-list-item__meta{color:rgba(0, 0, 0, 0.38);color:var(--mdc-theme-text-hint-on-background, rgba(0, 0, 0, 0.38))}.mdc-deprecated-list-group__subheader{color:rgba(0, 0, 0, 0.87);color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__text{opacity:0.38}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__text,.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__primary-text,.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__secondary-text{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-deprecated-list-item--selected,.mdc-deprecated-list-item--activated{color:#6200ee;color:var(--mdc-theme-primary, #6200ee)}.mdc-deprecated-list-item--selected .mdc-deprecated-list-item__graphic,.mdc-deprecated-list-item--activated .mdc-deprecated-list-item__graphic{color:#6200ee;color:var(--mdc-theme-primary, #6200ee)}.mdc-deprecated-list--dense{padding-top:4px;padding-bottom:4px;font-size:0.812rem}.mdc-deprecated-list-item__wrapper{display:block}.mdc-deprecated-list-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;height:48px}.mdc-deprecated-list-item:focus{outline:none}.mdc-deprecated-list-item:not(.mdc-deprecated-list-item--selected):focus::before,.mdc-deprecated-list-item.mdc-ripple-upgraded--background-focused::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid transparent;border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-deprecated-list-item:not(.mdc-deprecated-list-item--selected):focus::before,.mdc-deprecated-list-item.mdc-ripple-upgraded--background-focused::before{border-color:CanvasText}}.mdc-deprecated-list-item.mdc-deprecated-list-item--selected::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:3px double transparent;border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-deprecated-list-item.mdc-deprecated-list-item--selected::before{border-color:CanvasText}}[dir=rtl] .mdc-deprecated-list-item,.mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-deprecated-list--icon-list .mdc-deprecated-list-item{padding-left:16px;padding-right:16px;height:56px}[dir=rtl] .mdc-deprecated-list--icon-list .mdc-deprecated-list-item,.mdc-deprecated-list--icon-list .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item{padding-left:16px;padding-right:16px;height:56px}[dir=rtl] .mdc-deprecated-list--avatar-list .mdc-deprecated-list-item,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item{padding-left:16px;padding-right:16px;height:56px}[dir=rtl] .mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-deprecated-list--image-list .mdc-deprecated-list-item{padding-left:16px;padding-right:16px;height:72px}[dir=rtl] .mdc-deprecated-list--image-list .mdc-deprecated-list-item,.mdc-deprecated-list--image-list .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-deprecated-list--video-list .mdc-deprecated-list-item{padding-left:0px;padding-right:16px;height:72px}[dir=rtl] .mdc-deprecated-list--video-list .mdc-deprecated-list-item,.mdc-deprecated-list--video-list .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:0px;}.mdc-deprecated-list--dense .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:16px;width:20px;height:20px}[dir=rtl] .mdc-deprecated-list--dense .mdc-deprecated-list-item__graphic,.mdc-deprecated-list--dense .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:16px;margin-right:0;}.mdc-deprecated-list-item__graphic{flex-shrink:0;align-items:center;justify-content:center;fill:currentColor;object-fit:cover;margin-left:0;margin-right:32px;width:24px;height:24px}[dir=rtl] .mdc-deprecated-list-item__graphic,.mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:32px;margin-right:0;}.mdc-deprecated-list--icon-list .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:32px;width:24px;height:24px}[dir=rtl] .mdc-deprecated-list--icon-list .mdc-deprecated-list-item__graphic,.mdc-deprecated-list--icon-list .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:32px;margin-right:0;}.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:16px;width:40px;height:40px;border-radius:50%}[dir=rtl] .mdc-deprecated-list--avatar-list .mdc-deprecated-list-item__graphic,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:16px;margin-right:0;}.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:16px;width:40px;height:40px}[dir=rtl] .mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item__graphic,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:16px;margin-right:0;}.mdc-deprecated-list--image-list .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:16px;width:56px;height:56px}[dir=rtl] .mdc-deprecated-list--image-list .mdc-deprecated-list-item__graphic,.mdc-deprecated-list--image-list .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:16px;margin-right:0;}.mdc-deprecated-list--video-list .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:16px;width:100px;height:56px}[dir=rtl] .mdc-deprecated-list--video-list .mdc-deprecated-list-item__graphic,.mdc-deprecated-list--video-list .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:16px;margin-right:0;}.mdc-deprecated-list .mdc-deprecated-list-item__graphic{display:inline-flex}.mdc-deprecated-list-item__meta{margin-left:auto;margin-right:0}.mdc-deprecated-list-item__meta:not(.material-icons){-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-caption-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.75rem;font-size:var(--mdc-typography-caption-font-size, 0.75rem);line-height:1.25rem;line-height:var(--mdc-typography-caption-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-caption-font-weight, 400);letter-spacing:0.0333333333em;letter-spacing:var(--mdc-typography-caption-letter-spacing, 0.0333333333em);text-decoration:inherit;text-decoration:var(--mdc-typography-caption-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-caption-text-transform, inherit)}.mdc-deprecated-list-item[dir=rtl] .mdc-deprecated-list-item__meta,[dir=rtl] .mdc-deprecated-list-item .mdc-deprecated-list-item__meta{margin-left:0;margin-right:auto}.mdc-deprecated-list-item__text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mdc-deprecated-list-item__text[for]{pointer-events:none}.mdc-deprecated-list-item__primary-text{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-deprecated-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-deprecated-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-deprecated-list--video-list .mdc-deprecated-list-item__primary-text,.mdc-deprecated-list--image-list .mdc-deprecated-list-item__primary-text,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item__primary-text,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item__primary-text,.mdc-deprecated-list--icon-list .mdc-deprecated-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-deprecated-list--video-list .mdc-deprecated-list-item__primary-text::before,.mdc-deprecated-list--image-list .mdc-deprecated-list-item__primary-text::before,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item__primary-text::before,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item__primary-text::before,.mdc-deprecated-list--icon-list .mdc-deprecated-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-deprecated-list--video-list .mdc-deprecated-list-item__primary-text::after,.mdc-deprecated-list--image-list .mdc-deprecated-list-item__primary-text::after,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item__primary-text::after,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item__primary-text::after,.mdc-deprecated-list--icon-list .mdc-deprecated-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-deprecated-list--dense .mdc-deprecated-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-deprecated-list--dense .mdc-deprecated-list-item__primary-text::before{display:inline-block;width:0;height:24px;content:\\\"\\\";vertical-align:0}.mdc-deprecated-list--dense .mdc-deprecated-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-deprecated-list-item__secondary-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.875rem;font-size:var(--mdc-typography-body2-font-size, 0.875rem);line-height:1.25rem;line-height:var(--mdc-typography-body2-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:0.0178571429em;letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:inherit;text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-body2-text-transform, inherit);text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;line-height:normal}.mdc-deprecated-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-deprecated-list--dense .mdc-deprecated-list-item__secondary-text{font-size:inherit}.mdc-deprecated-list--dense .mdc-deprecated-list-item{height:40px}.mdc-deprecated-list--two-line .mdc-deprecated-list-item__text{align-self:flex-start}.mdc-deprecated-list--two-line .mdc-deprecated-list-item{height:64px}.mdc-deprecated-list--two-line.mdc-deprecated-list--video-list .mdc-deprecated-list-item,.mdc-deprecated-list--two-line.mdc-deprecated-list--image-list .mdc-deprecated-list-item,.mdc-deprecated-list--two-line.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-item,.mdc-deprecated-list--two-line.mdc-deprecated-list--avatar-list .mdc-deprecated-list-item,.mdc-deprecated-list--two-line.mdc-deprecated-list--icon-list .mdc-deprecated-list-item{height:72px}.mdc-deprecated-list--two-line.mdc-deprecated-list--icon-list .mdc-deprecated-list-item__graphic{align-self:flex-start;margin-top:16px}.mdc-deprecated-list--two-line.mdc-deprecated-list--dense .mdc-deprecated-list-item,.mdc-deprecated-list--avatar-list.mdc-deprecated-list--dense .mdc-deprecated-list-item{height:60px}.mdc-deprecated-list--avatar-list.mdc-deprecated-list--dense .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:16px;width:36px;height:36px}[dir=rtl] .mdc-deprecated-list--avatar-list.mdc-deprecated-list--dense .mdc-deprecated-list-item__graphic,.mdc-deprecated-list--avatar-list.mdc-deprecated-list--dense .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:16px;margin-right:0;}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item{cursor:pointer}a.mdc-deprecated-list-item{color:inherit;text-decoration:none}.mdc-deprecated-list-divider{height:0;margin:0;border:none;border-bottom-width:1px;border-bottom-style:solid}.mdc-deprecated-list-divider{border-bottom-color:rgba(0, 0, 0, 0.12)}.mdc-deprecated-list-divider--padded{margin-left:16px;margin-right:0;width:calc(100% - 32px)}[dir=rtl] .mdc-deprecated-list-divider--padded,.mdc-deprecated-list-divider--padded[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list-divider--inset{margin-left:72px;margin-right:0;width:calc(100% - 72px)}[dir=rtl] .mdc-deprecated-list-divider--inset,.mdc-deprecated-list-divider--inset[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list-divider--inset.mdc-deprecated-list-divider--padded{margin-left:72px;margin-right:0;width:calc(100% - 88px)}[dir=rtl] .mdc-deprecated-list-divider--inset.mdc-deprecated-list-divider--padded,.mdc-deprecated-list-divider--inset.mdc-deprecated-list-divider--padded[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading{margin-left:16px;margin-right:0;width:calc(100% - 16px)}[dir=rtl] .mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading,.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list .mdc-deprecated-list-divider--inset-trailing{width:calc(100% - 16px)}.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing{margin-left:16px;margin-right:0;width:calc(100% - 32px)}[dir=rtl] .mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing,.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding{margin-left:16px;margin-right:0;width:calc(100% - 16px)}[dir=rtl] .mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding,.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding{margin-left:16px;margin-right:0;width:calc(100% - 32px)}[dir=rtl] .mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding,.mdc-deprecated-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading{margin-left:72px;margin-right:0;width:calc(100% - 72px)}[dir=rtl] .mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading,.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-trailing{width:calc(100% - 16px)}.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing{margin-left:72px;margin-right:0;width:calc(100% - 88px)}[dir=rtl] .mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing,.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding{margin-left:16px;margin-right:0;width:calc(100% - 16px)}[dir=rtl] .mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding,.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding{margin-left:16px;margin-right:0;width:calc(100% - 32px)}[dir=rtl] .mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding,.mdc-deprecated-list--icon-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading{margin-left:72px;margin-right:0;width:calc(100% - 72px)}[dir=rtl] .mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-trailing{width:calc(100% - 16px)}.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing{margin-left:72px;margin-right:0;width:calc(100% - 88px)}[dir=rtl] .mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding{margin-left:16px;margin-right:0;width:calc(100% - 16px)}[dir=rtl] .mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding{margin-left:16px;margin-right:0;width:calc(100% - 32px)}[dir=rtl] .mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding,.mdc-deprecated-list--avatar-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading{margin-left:72px;margin-right:0;width:calc(100% - 72px)}[dir=rtl] .mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-trailing{width:calc(100% - 16px)}.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing{margin-left:72px;margin-right:0;width:calc(100% - 88px)}[dir=rtl] .mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing[dir=rtl]{margin-left:0;margin-right:72px;}.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding{margin-left:16px;margin-right:0;width:calc(100% - 16px)}[dir=rtl] .mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding{margin-left:16px;margin-right:0;width:calc(100% - 32px)}[dir=rtl] .mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding,.mdc-deprecated-list--thumbnail-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading{margin-left:88px;margin-right:0;width:calc(100% - 88px)}[dir=rtl] .mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading,.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading[dir=rtl]{margin-left:0;margin-right:88px;}.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-trailing{width:calc(100% - 16px)}.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing{margin-left:88px;margin-right:0;width:calc(100% - 104px)}[dir=rtl] .mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing,.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing[dir=rtl]{margin-left:0;margin-right:88px;}.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding{margin-left:16px;margin-right:0;width:calc(100% - 16px)}[dir=rtl] .mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding,.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding{margin-left:16px;margin-right:0;width:calc(100% - 32px)}[dir=rtl] .mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding,.mdc-deprecated-list--image-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding[dir=rtl]{margin-left:0;margin-right:16px;}.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading{margin-left:116px;margin-right:0;width:calc(100% - 116px)}[dir=rtl] .mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading,.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading[dir=rtl]{margin-left:0;margin-right:116px;}.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-trailing{width:calc(100% - 16px)}.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing{margin-left:116px;margin-right:0;width:calc(100% - 132px)}[dir=rtl] .mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing,.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing[dir=rtl]{margin-left:0;margin-right:116px;}.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding{margin-left:0px;margin-right:0;width:calc(100% - 0px)}[dir=rtl] .mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding,.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--padding[dir=rtl]{margin-left:0;margin-right:0px;}.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding{margin-left:0px;margin-right:0;width:calc(100% - 16px)}[dir=rtl] .mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding,.mdc-deprecated-list--video-list .mdc-deprecated-list-divider--inset-leading.mdc-deprecated-list-divider--inset-trailing.mdc-deprecated-list-divider--inset-padding[dir=rtl]{margin-left:0;margin-right:0px;}.mdc-deprecated-list-group .mdc-deprecated-list{padding:0}.mdc-deprecated-list-group__subheader{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);line-height:1.75rem;line-height:var(--mdc-typography-subtitle1-line-height, 1.75rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);margin:calc((3rem - 1.5rem) / 2) 16px}.mdc-list-item__primary-text{color:rgba(0, 0, 0, 0.87);color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}.mdc-list-item__secondary-text{color:rgba(0, 0, 0, 0.54);color:var(--mdc-theme-text-secondary-on-background, rgba(0, 0, 0, 0.54))}.mdc-list-item__overline-text{color:rgba(0, 0, 0, 0.38);color:var(--mdc-theme-text-hint-on-background, rgba(0, 0, 0, 0.38))}.mdc-list-item--with-leading-icon .mdc-list-item__start,.mdc-list-item--with-trailing-icon .mdc-list-item__end{background-color:transparent}.mdc-list-item--with-leading-icon .mdc-list-item__start,.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:rgba(0, 0, 0, 0.38);color:var(--mdc-theme-text-icon-on-background, rgba(0, 0, 0, 0.38))}.mdc-list-item__end{color:rgba(0, 0, 0, 0.38);color:var(--mdc-theme-text-hint-on-background, rgba(0, 0, 0, 0.38))}.mdc-list-item--disabled .mdc-list-item__start,.mdc-list-item--disabled .mdc-list-item__content,.mdc-list-item--disabled .mdc-list-item__end{opacity:0.38}.mdc-list-item--disabled .mdc-list-item__primary-text{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-list-item--disabled .mdc-list-item__secondary-text{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-list-item--disabled .mdc-list-item__overline-text{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-list-item--disabled.mdc-list-item--with-leading-icon .mdc-list-item__start{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-list-item--disabled.mdc-list-item--with-trailing-icon .mdc-list-item__end{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-list-item--disabled.mdc-list-item--with-trailing-meta .mdc-list-item__end{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-list-item--selected .mdc-list-item__primary-text,.mdc-list-item--activated .mdc-list-item__primary-text{color:#6200ee;color:var(--mdc-theme-primary, #6200ee)}.mdc-list-item--selected.mdc-list-item--with-leading-icon .mdc-list-item__start,.mdc-list-item--activated.mdc-list-item--with-leading-icon .mdc-list-item__start{color:#6200ee;color:var(--mdc-theme-primary, #6200ee)}.mdc-deprecated-list-group__subheader{color:rgba(0, 0, 0, 0.87);color:var(--mdc-theme-text-primary-on-background, rgba(0, 0, 0, 0.87))}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-list-divider::after{content:\\\"\\\";display:block;border-bottom-width:1px;border-bottom-style:solid;border-bottom-color:white}}.mdc-list{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);line-height:1.75rem;line-height:var(--mdc-typography-subtitle1-line-height, 1.75rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);line-height:1.5rem;margin:0;padding:8px 0;list-style-type:none}.mdc-list:focus{outline:none}.mdc-list-item__wrapper{display:block}.mdc-list-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;align-items:stretch;cursor:pointer}.mdc-list-item:focus{outline:none}.mdc-list-item.mdc-list-item--with-one-line{height:48px}.mdc-list-item.mdc-list-item--with-two-lines{height:64px}.mdc-list-item.mdc-list-item--with-three-lines{height:88px}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__start{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--with-one-line .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:center;margin-top:0}.mdc-list-item.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item.mdc-list-item--disabled,.mdc-list-item.mdc-list-item--non-interactive{cursor:auto}.mdc-list-item:not(.mdc-list-item--selected):focus::before,.mdc-list-item.mdc-ripple-upgraded--background-focused::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid transparent;border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-list-item:not(.mdc-list-item--selected):focus::before,.mdc-list-item.mdc-ripple-upgraded--background-focused::before{border-color:CanvasText}}.mdc-list-item.mdc-list-item--selected::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:3px double transparent;border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-list-item.mdc-list-item--selected::before{border-color:CanvasText}}.mdc-list-item.mdc-list-item--selected:focus::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:3px solid transparent;border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-list-item.mdc-list-item--selected:focus::before{border-color:CanvasText}}a.mdc-list-item{color:inherit;text-decoration:none}.mdc-list-item__start{fill:currentColor;flex-shrink:0;pointer-events:none}.mdc-list-item__end{flex-shrink:0;pointer-events:none}.mdc-list-item__content{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;align-self:center;flex:1;pointer-events:none}.mdc-list-item--with-two-lines .mdc-list-item__content,.mdc-list-item--with-three-lines .mdc-list-item__content{align-self:stretch}.mdc-list-item__content[for]{pointer-events:none}.mdc-list-item__primary-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);line-height:1.75rem;line-height:var(--mdc-typography-subtitle1-line-height, 1.75rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mdc-list-item--with-two-lines .mdc-list-item__primary-text,.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after,.mdc-list-item--with-three-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item__secondary-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-body2-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.875rem;font-size:var(--mdc-typography-body2-font-size, 0.875rem);line-height:1.25rem;line-height:var(--mdc-typography-body2-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-body2-font-weight, 400);letter-spacing:0.0178571429em;letter-spacing:var(--mdc-typography-body2-letter-spacing, 0.0178571429em);text-decoration:inherit;text-decoration:var(--mdc-typography-body2-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-body2-text-transform, inherit);text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;margin-top:0;line-height:normal}.mdc-list-item__secondary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__secondary-text{white-space:normal;line-height:20px}.mdc-list-item--with-overline .mdc-list-item__secondary-text{white-space:nowrap;line-height:auto}.mdc-list-item__overline-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-overline-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.75rem;font-size:var(--mdc-typography-overline-font-size, 0.75rem);line-height:2rem;line-height:var(--mdc-typography-overline-line-height, 2rem);font-weight:500;font-weight:var(--mdc-typography-overline-font-weight, 500);letter-spacing:0.1666666667em;letter-spacing:var(--mdc-typography-overline-letter-spacing, 0.1666666667em);text-decoration:none;text-decoration:var(--mdc-typography-overline-text-decoration, none);text-transform:uppercase;text-transform:var(--mdc-typography-overline-text-transform, uppercase);text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:24px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-three-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-three-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-three-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-avatar.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-avatar.mdc-list-item,.mdc-list-item--with-leading-avatar.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-avatar .mdc-list-item__start{margin-left:16px;margin-right:16px}[dir=rtl] .mdc-list-item--with-leading-avatar .mdc-list-item__start,.mdc-list-item--with-leading-avatar .mdc-list-item__start[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-list-item--with-leading-avatar .mdc-list-item__start{width:40px;height:40px}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-avatar.mdc-list-item--with-one-line{height:56px}.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-leading-avatar .mdc-list-item__start{border-radius:50%}.mdc-list-item--with-leading-icon .mdc-list-item__start{width:24px;height:24px}.mdc-list-item--with-leading-icon.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-icon.mdc-list-item,.mdc-list-item--with-leading-icon.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-icon .mdc-list-item__start{margin-left:16px;margin-right:32px}[dir=rtl] .mdc-list-item--with-leading-icon .mdc-list-item__start,.mdc-list-item--with-leading-icon .mdc-list-item__start[dir=rtl]{margin-left:32px;margin-right:16px;}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-icon.mdc-list-item--with-one-line{height:56px}.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-leading-thumbnail.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-thumbnail.mdc-list-item,.mdc-list-item--with-leading-thumbnail.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-thumbnail .mdc-list-item__start{margin-left:16px;margin-right:16px}[dir=rtl] .mdc-list-item--with-leading-thumbnail .mdc-list-item__start,.mdc-list-item--with-leading-thumbnail .mdc-list-item__start[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-list-item--with-leading-thumbnail .mdc-list-item__start{width:40px;height:40px}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-one-line{height:56px}.mdc-list-item--with-leading-thumbnail.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-leading-image.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-image.mdc-list-item,.mdc-list-item--with-leading-image.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-image .mdc-list-item__start{margin-left:16px;margin-right:16px}[dir=rtl] .mdc-list-item--with-leading-image .mdc-list-item__start,.mdc-list-item--with-leading-image .mdc-list-item__start[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-list-item--with-leading-image .mdc-list-item__start{width:56px;height:56px}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-image.mdc-list-item--with-one-line{height:72px}.mdc-list-item--with-leading-image.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-leading-video.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-video.mdc-list-item,.mdc-list-item--with-leading-video.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-video .mdc-list-item__start{margin-left:0;margin-right:16px}[dir=rtl] .mdc-list-item--with-leading-video .mdc-list-item__start,.mdc-list-item--with-leading-video .mdc-list-item__start[dir=rtl]{margin-left:16px;margin-right:0;}.mdc-list-item--with-leading-video .mdc-list-item__start{width:100px;height:56px}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-video.mdc-list-item--with-one-line{height:72px}.mdc-list-item--with-leading-video.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-leading-checkbox.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-checkbox.mdc-list-item,.mdc-list-item--with-leading-checkbox.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-checkbox .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-checkbox .mdc-list-item__start,.mdc-list-item--with-leading-checkbox .mdc-list-item__start[dir=rtl]{margin-left:24px;margin-right:8px;}.mdc-list-item--with-leading-checkbox .mdc-list-item__start{width:40px;height:40px}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-one-line{height:56px}.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-leading-radio.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-radio.mdc-list-item,.mdc-list-item--with-leading-radio.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-radio .mdc-list-item__start{margin-left:8px;margin-right:24px}[dir=rtl] .mdc-list-item--with-leading-radio .mdc-list-item__start,.mdc-list-item--with-leading-radio .mdc-list-item__start[dir=rtl]{margin-left:24px;margin-right:8px;}.mdc-list-item--with-leading-radio .mdc-list-item__start{width:40px;height:40px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:8px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-radio.mdc-list-item--with-one-line{height:56px}.mdc-list-item--with-leading-radio.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-leading-switch.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-list-item--with-leading-switch.mdc-list-item,.mdc-list-item--with-leading-switch.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-list-item--with-leading-switch .mdc-list-item__start{margin-left:16px;margin-right:16px}[dir=rtl] .mdc-list-item--with-leading-switch .mdc-list-item__start,.mdc-list-item--with-leading-switch .mdc-list-item__start[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-list-item--with-leading-switch .mdc-list-item__start{width:36px;height:20px}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines .mdc-list-item__start{align-self:flex-start;margin-top:16px}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines .mdc-list-item__overline-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines .mdc-list-item__overline-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines .mdc-list-item__overline-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:32px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-leading-switch.mdc-list-item--with-one-line{height:56px}.mdc-list-item--with-leading-switch.mdc-list-item--with-two-lines{height:72px}.mdc-list-item--with-trailing-icon.mdc-list-item{padding-left:auto;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-icon.mdc-list-item,.mdc-list-item--with-trailing-icon.mdc-list-item[dir=rtl]{padding-left:0;padding-right:auto;}.mdc-list-item--with-trailing-icon .mdc-list-item__end{margin-left:16px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-icon .mdc-list-item__end,.mdc-list-item--with-trailing-icon .mdc-list-item__end[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-list-item--with-trailing-icon .mdc-list-item__end{width:24px;height:24px}.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:0}.mdc-list-item--with-trailing-meta.mdc-list-item{padding-left:auto;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-meta.mdc-list-item,.mdc-list-item--with-trailing-meta.mdc-list-item[dir=rtl]{padding-left:0;padding-right:auto;}.mdc-list-item--with-trailing-meta .mdc-list-item__end{margin-left:28px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-meta .mdc-list-item__end,.mdc-list-item--with-trailing-meta .mdc-list-item__end[dir=rtl]{margin-left:16px;margin-right:28px;}.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-trailing-meta.mdc-list-item--with-two-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-trailing-meta.mdc-list-item--with-three-lines .mdc-list-item__end::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-trailing-meta .mdc-list-item__end{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-caption-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.75rem;font-size:var(--mdc-typography-caption-font-size, 0.75rem);line-height:1.25rem;line-height:var(--mdc-typography-caption-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-caption-font-weight, 400);letter-spacing:0.0333333333em;letter-spacing:var(--mdc-typography-caption-letter-spacing, 0.0333333333em);text-decoration:inherit;text-decoration:var(--mdc-typography-caption-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-caption-text-transform, inherit)}.mdc-list-item--with-trailing-checkbox.mdc-list-item{padding-left:auto;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-checkbox.mdc-list-item,.mdc-list-item--with-trailing-checkbox.mdc-list-item[dir=rtl]{padding-left:0;padding-right:auto;}.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-checkbox .mdc-list-item__end,.mdc-list-item--with-trailing-checkbox .mdc-list-item__end[dir=rtl]{margin-left:8px;margin-right:24px;}.mdc-list-item--with-trailing-checkbox .mdc-list-item__end{width:40px;height:40px}.mdc-list-item--with-trailing-checkbox.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-radio.mdc-list-item{padding-left:auto;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-radio.mdc-list-item,.mdc-list-item--with-trailing-radio.mdc-list-item[dir=rtl]{padding-left:0;padding-right:auto;}.mdc-list-item--with-trailing-radio .mdc-list-item__end{margin-left:24px;margin-right:8px}[dir=rtl] .mdc-list-item--with-trailing-radio .mdc-list-item__end,.mdc-list-item--with-trailing-radio .mdc-list-item__end[dir=rtl]{margin-left:8px;margin-right:24px;}.mdc-list-item--with-trailing-radio .mdc-list-item__end{width:40px;height:40px}.mdc-list-item--with-trailing-radio.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:8px}.mdc-list-item--with-trailing-switch.mdc-list-item{padding-left:auto;padding-right:0}[dir=rtl] .mdc-list-item--with-trailing-switch.mdc-list-item,.mdc-list-item--with-trailing-switch.mdc-list-item[dir=rtl]{padding-left:0;padding-right:auto;}.mdc-list-item--with-trailing-switch .mdc-list-item__end{margin-left:16px;margin-right:16px}[dir=rtl] .mdc-list-item--with-trailing-switch .mdc-list-item__end,.mdc-list-item--with-trailing-switch .mdc-list-item__end[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-list-item--with-trailing-switch .mdc-list-item__end{width:36px;height:20px}.mdc-list-item--with-trailing-switch.mdc-list-item--with-three-lines .mdc-list-item__end{align-self:flex-start;margin-top:16px}.mdc-list-item--with-overline.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-overline.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item--with-overline.mdc-list-item--with-three-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal}.mdc-list-item--with-overline.mdc-list-item--with-three-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:0}.mdc-list-item{padding-left:16px;padding-right:16px}[dir=rtl] .mdc-list-item,.mdc-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-list-group .mdc-deprecated-list{padding:0}.mdc-list-group__subheader{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);line-height:1.75rem;line-height:var(--mdc-typography-subtitle1-line-height, 1.75rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);margin:calc((3rem - 1.5rem) / 2) 16px}.mdc-list-divider{background-color:rgba(0, 0, 0, 0.12)}.mdc-list-divider{height:1px}.mdc-list-divider{padding:0;background-clip:content-box}.mdc-list-divider.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-text.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-icon.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-image.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-thumbnail.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-avatar.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-checkbox.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-switch.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-radio.mdc-list-divider--with-leading-inset{padding-left:16px;padding-right:auto}[dir=rtl] .mdc-list-divider.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-text.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-icon.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-image.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-thumbnail.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-avatar.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-checkbox.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-switch.mdc-list-divider--with-leading-inset,[dir=rtl] .mdc-list-divider--with-leading-radio.mdc-list-divider--with-leading-inset,.mdc-list-divider.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-text.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-icon.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-image.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-thumbnail.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-avatar.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-checkbox.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-switch.mdc-list-divider--with-leading-inset[dir=rtl],.mdc-list-divider--with-leading-radio.mdc-list-divider--with-leading-inset[dir=rtl]{padding-left:auto;padding-right:16px;}.mdc-list-divider.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-text.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-icon.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-image.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-thumbnail.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-avatar.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-checkbox.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-switch.mdc-list-divider--with-trailing-inset,.mdc-list-divider--with-leading-radio.mdc-list-divider--with-trailing-inset{padding-left:auto;padding-right:16px}[dir=rtl] .mdc-list-divider.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-text.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-icon.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-image.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-thumbnail.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-avatar.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-checkbox.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-switch.mdc-list-divider--with-trailing-inset,[dir=rtl] .mdc-list-divider--with-leading-radio.mdc-list-divider--with-trailing-inset,.mdc-list-divider.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-text.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-icon.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-image.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-thumbnail.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-avatar.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-checkbox.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-switch.mdc-list-divider--with-trailing-inset[dir=rtl],.mdc-list-divider--with-leading-radio.mdc-list-divider--with-trailing-inset[dir=rtl]{padding-left:16px;padding-right:auto;}.mdc-list-divider--with-leading-video.mdc-list-divider--with-leading-inset{padding-left:0px;padding-right:auto}[dir=rtl] .mdc-list-divider--with-leading-video.mdc-list-divider--with-leading-inset,.mdc-list-divider--with-leading-video.mdc-list-divider--with-leading-inset[dir=rtl]{padding-left:auto;padding-right:0px;}[dir=rtl] .mdc-list-divider,.mdc-list-divider[dir=rtl]{padding:0;}@keyframes mdc-ripple-fg-radius-in{from{animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transform:translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1)}to{transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}}@keyframes mdc-ripple-fg-opacity-in{from{animation-timing-function:linear;opacity:0}to{opacity:var(--mdc-ripple-fg-opacity, 0)}}@keyframes mdc-ripple-fg-opacity-out{from{animation-timing-function:linear;opacity:var(--mdc-ripple-fg-opacity, 0)}to{opacity:0}}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity;--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded .mdc-deprecated-list-item__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded .mdc-deprecated-list-item__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--unbounded .mdc-deprecated-list-item__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--foreground-activation .mdc-deprecated-list-item__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--foreground-deactivation .mdc-deprecated-list-item__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded .mdc-list-item__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded .mdc-list-item__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--unbounded .mdc-list-item__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--foreground-activation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--foreground-deactivation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded .mdc-deprecated-list-item__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded .mdc-list-item__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:hover .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-surface--hover .mdc-deprecated-list-item__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--background-focused .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:not(.mdc-ripple-upgraded):focus .mdc-deprecated-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:not(.mdc-ripple-upgraded) .mdc-deprecated-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:not(.mdc-ripple-upgraded):active .mdc-deprecated-list-item__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:hover .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated .mdc-deprecated-list-item__ripple::before{opacity:0.12;opacity:var(--mdc-ripple-activated-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated .mdc-deprecated-list-item__ripple::after{background-color:#6200ee;background-color:var(--mdc-ripple-color, var(--mdc-theme-primary, #6200ee))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:hover .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated.mdc-ripple-surface--hover .mdc-deprecated-list-item__ripple::before{opacity:0.16;opacity:var(--mdc-ripple-hover-opacity, 0.16)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated.mdc-ripple-upgraded--background-focused .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:not(.mdc-ripple-upgraded):focus .mdc-deprecated-list-item__ripple::before{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-ripple-focus-opacity, 0.24)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:not(.mdc-ripple-upgraded) .mdc-deprecated-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:not(.mdc-ripple-upgraded):active .mdc-deprecated-list-item__ripple::after{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-ripple-press-opacity, 0.24)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.24)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated .mdc-list-item__ripple::before{opacity:0.12;opacity:var(--mdc-ripple-activated-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated .mdc-list-item__ripple::after{background-color:#6200ee;background-color:var(--mdc-ripple-color, var(--mdc-theme-primary, #6200ee))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:hover .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.16;opacity:var(--mdc-ripple-hover-opacity, 0.16)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-ripple-focus-opacity, 0.24)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-ripple-press-opacity, 0.24)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--activated.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.24)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected .mdc-deprecated-list-item__ripple::before{opacity:0.08;opacity:var(--mdc-ripple-selected-opacity, 0.08)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected .mdc-deprecated-list-item__ripple::after{background-color:#6200ee;background-color:var(--mdc-ripple-color, var(--mdc-theme-primary, #6200ee))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:hover .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected.mdc-ripple-surface--hover .mdc-deprecated-list-item__ripple::before{opacity:0.12;opacity:var(--mdc-ripple-hover-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected.mdc-ripple-upgraded--background-focused .mdc-deprecated-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):focus .mdc-deprecated-list-item__ripple::before{transition-duration:75ms;opacity:0.2;opacity:var(--mdc-ripple-focus-opacity, 0.2)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded) .mdc-deprecated-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):active .mdc-deprecated-list-item__ripple::after{transition-duration:75ms;opacity:0.2;opacity:var(--mdc-ripple-press-opacity, 0.2)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.2)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected .mdc-list-item__ripple::before{opacity:0.08;opacity:var(--mdc-ripple-selected-opacity, 0.08)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected .mdc-list-item__ripple::after{background-color:#6200ee;background-color:var(--mdc-ripple-color, var(--mdc-theme-primary, #6200ee))}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:hover .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.12;opacity:var(--mdc-ripple-hover-opacity, 0.12)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.2;opacity:var(--mdc-ripple-focus-opacity, 0.2)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.2;opacity:var(--mdc-ripple-press-opacity, 0.2)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.2)}:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-deprecated-list-item__ripple,:not(.mdc-deprecated-list-item--disabled).mdc-deprecated-list-item .mdc-list-item__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.mdc-deprecated-list-item--disabled{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity;--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::before,.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded .mdc-deprecated-list-item__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded .mdc-deprecated-list-item__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--unbounded .mdc-deprecated-list-item__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--foreground-activation .mdc-deprecated-list-item__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--foreground-deactivation .mdc-deprecated-list-item__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::before,.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded .mdc-list-item__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded .mdc-list-item__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--unbounded .mdc-list-item__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--foreground-activation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--foreground-deactivation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::before,.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded .mdc-deprecated-list-item__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::before,.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded .mdc-list-item__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::before,.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::before,.mdc-deprecated-list-item--disabled .mdc-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--background-focused .mdc-deprecated-list-item__ripple::before,.mdc-deprecated-list-item--disabled:not(.mdc-ripple-upgraded):focus .mdc-deprecated-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-deprecated-list-item--disabled.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,.mdc-deprecated-list-item--disabled:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-deprecated-list-item--disabled .mdc-deprecated-list-item__ripple,.mdc-deprecated-list-item--disabled .mdc-list-item__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}:not(.mdc-list-item--disabled).mdc-list-item{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded .mdc-list-item__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded .mdc-list-item__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded--unbounded .mdc-list-item__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded--foreground-activation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded--foreground-deactivation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded .mdc-list-item__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}:not(.mdc-list-item--disabled).mdc-list-item:hover .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}:not(.mdc-list-item--disabled).mdc-list-item:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-list-item--disabled).mdc-list-item:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}:not(.mdc-list-item--disabled).mdc-list-item.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}:not(.mdc-list-item--disabled).mdc-list-item--activated .mdc-list-item__ripple::before{opacity:0.12;opacity:var(--mdc-ripple-activated-opacity, 0.12)}:not(.mdc-list-item--disabled).mdc-list-item--activated .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item--activated .mdc-list-item__ripple::after{background-color:#6200ee;background-color:var(--mdc-ripple-color, var(--mdc-theme-primary, #6200ee))}:not(.mdc-list-item--disabled).mdc-list-item--activated:hover .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item--activated.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.16;opacity:var(--mdc-ripple-hover-opacity, 0.16)}:not(.mdc-list-item--disabled).mdc-list-item--activated.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item--activated:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-ripple-focus-opacity, 0.24)}:not(.mdc-list-item--disabled).mdc-list-item--activated:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-list-item--disabled).mdc-list-item--activated:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-ripple-press-opacity, 0.24)}:not(.mdc-list-item--disabled).mdc-list-item--activated.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.24)}:not(.mdc-list-item--disabled).mdc-list-item--selected .mdc-list-item__ripple::before{opacity:0.08;opacity:var(--mdc-ripple-selected-opacity, 0.08)}:not(.mdc-list-item--disabled).mdc-list-item--selected .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item--selected .mdc-list-item__ripple::after{background-color:#6200ee;background-color:var(--mdc-ripple-color, var(--mdc-theme-primary, #6200ee))}:not(.mdc-list-item--disabled).mdc-list-item--selected:hover .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item--selected.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.12;opacity:var(--mdc-ripple-hover-opacity, 0.12)}:not(.mdc-list-item--disabled).mdc-list-item--selected.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,:not(.mdc-list-item--disabled).mdc-list-item--selected:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.2;opacity:var(--mdc-ripple-focus-opacity, 0.2)}:not(.mdc-list-item--disabled).mdc-list-item--selected:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}:not(.mdc-list-item--disabled).mdc-list-item--selected:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.2;opacity:var(--mdc-ripple-press-opacity, 0.2)}:not(.mdc-list-item--disabled).mdc-list-item--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.2)}:not(.mdc-list-item--disabled).mdc-list-item .mdc-list-item__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.mdc-list-item--disabled{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}.mdc-list-item--disabled .mdc-list-item__ripple::before,.mdc-list-item--disabled .mdc-list-item__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-list-item--disabled .mdc-list-item__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-list-item--disabled .mdc-list-item__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-list-item--disabled.mdc-ripple-upgraded .mdc-list-item__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-list-item--disabled.mdc-ripple-upgraded .mdc-list-item__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-list-item--disabled.mdc-ripple-upgraded--unbounded .mdc-list-item__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-list-item--disabled.mdc-ripple-upgraded--foreground-activation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-list-item--disabled.mdc-ripple-upgraded--foreground-deactivation .mdc-list-item__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-list-item--disabled .mdc-list-item__ripple::before,.mdc-list-item--disabled .mdc-list-item__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}.mdc-list-item--disabled.mdc-ripple-upgraded .mdc-list-item__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-list-item--disabled .mdc-list-item__ripple::before,.mdc-list-item--disabled .mdc-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, #000)}.mdc-list-item--disabled.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,.mdc-list-item--disabled:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-list-item--disabled .mdc-list-item__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.mdc-menu-surface{display:none;position:absolute;box-sizing:border-box;max-width:calc(100vw - 32px);max-width:var(--mdc-menu-max-width, calc(100vw - 32px));max-height:calc(100vh - 32px);max-height:var(--mdc-menu-max-height, calc(100vh - 32px));margin:0;padding:0;transform:scale(1);transform-origin:top left;opacity:0;overflow:auto;will-change:transform, opacity;z-index:8;transition:opacity 0.03s linear, transform 0.12s cubic-bezier(0, 0, 0.2, 1), height 250ms cubic-bezier(0, 0, 0.2, 1);box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);background-color:#fff;background-color:var(--mdc-theme-surface, #fff);color:#000;color:var(--mdc-theme-on-surface, #000);border-radius:4px;border-radius:var(--mdc-shape-medium, 4px);transform-origin-left:top left;transform-origin-right:top right}.mdc-menu-surface:focus{outline:none}.mdc-menu-surface--animating-open{display:inline-block;transform:scale(0.8);opacity:0}.mdc-menu-surface--open{display:inline-block;transform:scale(1);opacity:1}.mdc-menu-surface--animating-closed{display:inline-block;opacity:0;transition:opacity 0.075s linear}[dir=rtl] .mdc-menu-surface,.mdc-menu-surface[dir=rtl]{transform-origin-left:top right;transform-origin-right:top left;}.mdc-menu-surface--anchor{position:relative;overflow:visible}.mdc-menu-surface--fixed{position:fixed}.mdc-menu-surface--fullwidth{width:100%}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:0;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:#fff;background-color:var(--mdc-elevation-overlay-color, #fff)}.mdc-menu{min-width:112px;min-width:var(--mdc-menu-min-width, 112px)}.mdc-menu .mdc-deprecated-list-item__meta{color:rgba(0, 0, 0, 0.87)}.mdc-menu .mdc-deprecated-list-item__graphic{color:rgba(0, 0, 0, 0.87)}.mdc-menu .mdc-menu-item--submenu-open .mdc-deprecated-list-item__ripple::before{opacity:0.04}.mdc-menu .mdc-menu-item--submenu-open .mdc-list-item__ripple::before{opacity:0.04}.mdc-menu .mdc-deprecated-list{color:rgba(0, 0, 0, 0.87)}.mdc-menu .mdc-deprecated-list,.mdc-menu .mdc-list{position:relative}.mdc-menu .mdc-deprecated-list .mdc-elevation-overlay,.mdc-menu .mdc-list .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-menu .mdc-deprecated-list-divider{margin:8px 0}.mdc-menu .mdc-deprecated-list-item{user-select:none}.mdc-menu .mdc-deprecated-list-item--disabled{cursor:auto}.mdc-menu a.mdc-deprecated-list-item .mdc-deprecated-list-item__text,.mdc-menu a.mdc-deprecated-list-item .mdc-deprecated-list-item__graphic{pointer-events:none}.mdc-menu__selection-group{padding:0;fill:currentColor}.mdc-menu__selection-group .mdc-deprecated-list-item{padding-left:56px;padding-right:16px}[dir=rtl] .mdc-menu__selection-group .mdc-deprecated-list-item,.mdc-menu__selection-group .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:56px;}.mdc-menu__selection-group .mdc-menu__selection-group-icon{left:16px;right:initial;display:none;position:absolute;top:50%;transform:translateY(-50%)}[dir=rtl] .mdc-menu__selection-group .mdc-menu__selection-group-icon,.mdc-menu__selection-group .mdc-menu__selection-group-icon[dir=rtl]{left:initial;right:16px;}.mdc-menu-item--selected .mdc-menu__selection-group-icon{display:inline}.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform;transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1)}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right;}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required::after{margin-left:1px;margin-right:0px;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required,.mdc-floating-label--required[dir=rtl]{}[dir=rtl] .mdc-floating-label--required::after,.mdc-floating-label--required[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0 - 0%)) translateY(-106%) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(-106%) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(-106%) scale(0.75)}100%{transform:translateX(calc(0 - 0%)) translateY(-106%) scale(0.75)}}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);border-bottom-width:2px;opacity:0;z-index:2}.mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1), opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right;}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;border-top:1px solid;border-bottom:1px solid;pointer-events:none}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid;}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid;flex-grow:1}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none;}.mdc-notched-outline__notch{flex:0 0 auto;width:auto;max-width:calc(100% - 12px * 2)}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0;}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-select{display:inline-flex;position:relative}.mdc-select:not(.mdc-select--disabled) .mdc-select__selected-text{color:rgba(0, 0, 0, 0.87)}.mdc-select.mdc-select--disabled .mdc-select__selected-text{color:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled) .mdc-floating-label{color:rgba(0, 0, 0, 0.6)}.mdc-select:not(.mdc-select--disabled).mdc-select--focused .mdc-floating-label{color:rgba(98, 0, 238, 0.87)}.mdc-select.mdc-select--disabled .mdc-floating-label{color:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled) .mdc-select__dropdown-icon{fill:rgba(0, 0, 0, 0.54)}.mdc-select:not(.mdc-select--disabled).mdc-select--focused .mdc-select__dropdown-icon{fill:#6200ee;fill:var(--mdc-theme-primary, #6200ee)}.mdc-select.mdc-select--disabled .mdc-select__dropdown-icon{fill:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled)+.mdc-select-helper-text{color:rgba(0, 0, 0, 0.6)}.mdc-select.mdc-select--disabled+.mdc-select-helper-text{color:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled) .mdc-select__icon{color:rgba(0, 0, 0, 0.54)}.mdc-select.mdc-select--disabled .mdc-select__icon{color:rgba(0, 0, 0, 0.38)}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-select.mdc-select--disabled .mdc-select__selected-text{color:GrayText}.mdc-select.mdc-select--disabled .mdc-select__dropdown-icon{fill:red}.mdc-select.mdc-select--disabled .mdc-floating-label{color:GrayText}.mdc-select.mdc-select--disabled .mdc-line-ripple::before{border-bottom-color:GrayText}.mdc-select.mdc-select--disabled .mdc-notched-outline__leading,.mdc-select.mdc-select--disabled .mdc-notched-outline__notch,.mdc-select.mdc-select--disabled .mdc-notched-outline__trailing{border-color:GrayText}.mdc-select.mdc-select--disabled .mdc-select__icon{color:GrayText}.mdc-select.mdc-select--disabled+.mdc-select-helper-text{color:GrayText}}.mdc-select .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-select .mdc-select__anchor{padding-left:16px;padding-right:0}[dir=rtl] .mdc-select .mdc-select__anchor,.mdc-select .mdc-select__anchor[dir=rtl]{padding-left:0;padding-right:16px;}.mdc-select.mdc-select--with-leading-icon .mdc-select__anchor{padding-left:0;padding-right:0}[dir=rtl] .mdc-select.mdc-select--with-leading-icon .mdc-select__anchor,.mdc-select.mdc-select--with-leading-icon .mdc-select__anchor[dir=rtl]{padding-left:0;padding-right:0;}.mdc-select .mdc-select__icon{width:24px;height:24px;font-size:24px}.mdc-select .mdc-select__dropdown-icon{width:24px;height:24px}.mdc-select .mdc-select__menu .mdc-deprecated-list-item{padding-left:16px;padding-right:16px}[dir=rtl] .mdc-select .mdc-select__menu .mdc-deprecated-list-item,.mdc-select .mdc-select__menu .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-select .mdc-select__menu .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:12px}[dir=rtl] .mdc-select .mdc-select__menu .mdc-deprecated-list-item__graphic,.mdc-select .mdc-select__menu .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:12px;margin-right:0;}.mdc-select__dropdown-icon{margin-left:12px;margin-right:12px;display:inline-flex;position:relative;align-self:center;align-items:center;justify-content:center;flex-shrink:0;pointer-events:none}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-active,.mdc-select__dropdown-icon .mdc-select__dropdown-icon-inactive{position:absolute;top:0;left:0}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-graphic{width:41.6666666667%;height:20.8333333333%}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-inactive{opacity:1;transition:opacity 75ms linear 75ms}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-active{opacity:0;transition:opacity 75ms linear}[dir=rtl] .mdc-select__dropdown-icon,.mdc-select__dropdown-icon[dir=rtl]{margin-left:12px;margin-right:12px;}.mdc-select--activated .mdc-select__dropdown-icon .mdc-select__dropdown-icon-inactive{opacity:0;transition:opacity 49.5ms linear}.mdc-select--activated .mdc-select__dropdown-icon .mdc-select__dropdown-icon-active{opacity:1;transition:opacity 100.5ms linear 49.5ms}.mdc-select__anchor{width:200px;min-width:0;flex:1 1 auto;position:relative;box-sizing:border-box;overflow:hidden;outline:none;cursor:pointer}.mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-select__selected-text-container{display:flex;appearance:none;pointer-events:none;box-sizing:border-box;width:auto;min-width:0;flex-grow:1;height:28px;border:none;outline:none;padding:0;background-color:transparent;color:inherit}.mdc-select__selected-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);line-height:1.75rem;line-height:var(--mdc-typography-subtitle1-line-height, 1.75rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;width:100%;text-align:left}[dir=rtl] .mdc-select__selected-text,.mdc-select__selected-text[dir=rtl]{text-align:right;}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-floating-label{color:#b00020;color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-floating-label{color:#b00020;color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--invalid+.mdc-select-helper-text--validation-msg{color:#b00020;color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-select__dropdown-icon{fill:#b00020;fill:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-select__dropdown-icon{fill:#b00020;fill:var(--mdc-theme-error, #b00020)}.mdc-select--disabled{cursor:default;pointer-events:none}.mdc-select--with-leading-icon .mdc-select__menu .mdc-deprecated-list-item{padding-left:12px;padding-right:12px}[dir=rtl] .mdc-select--with-leading-icon .mdc-select__menu .mdc-deprecated-list-item,.mdc-select--with-leading-icon .mdc-select__menu .mdc-deprecated-list-item[dir=rtl]{padding-left:12px;padding-right:12px;}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-select__menu::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid transparent;border-radius:inherit;content:\\\"\\\";pointer-events:none}}@media screen and (forced-colors: active) and (forced-colors: active), screen and (-ms-high-contrast: active) and (forced-colors: active){.mdc-select__menu::before{border-color:CanvasText}}.mdc-select__menu .mdc-deprecated-list .mdc-select__icon,.mdc-select__menu .mdc-list .mdc-select__icon{margin-left:0;margin-right:0}[dir=rtl] .mdc-select__menu .mdc-deprecated-list .mdc-select__icon,[dir=rtl] .mdc-select__menu .mdc-list .mdc-select__icon,.mdc-select__menu .mdc-deprecated-list .mdc-select__icon[dir=rtl],.mdc-select__menu .mdc-list .mdc-select__icon[dir=rtl]{margin-left:0;margin-right:0;}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--activated,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--selected,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--activated{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__graphic,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--activated .mdc-deprecated-list-item__graphic,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__graphic,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--activated .mdc-deprecated-list-item__graphic{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-select__menu .mdc-list-item__start{display:inline-flex;align-items:center}.mdc-select__option{padding-left:16px;padding-right:16px}[dir=rtl] .mdc-select__option,.mdc-select__option[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-select__one-line-option.mdc-list-item--with-one-line{height:48px}.mdc-select__two-line-option.mdc-list-item--with-two-lines{height:64px}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__start{margin-top:20px}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-select__two-line-option.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-select__two-line-option.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:36px;content:\\\"\\\";vertical-align:0}.mdc-select__option-with-leading-content{padding-left:0;padding-right:12px}.mdc-select__option-with-leading-content.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-select__option-with-leading-content.mdc-list-item,.mdc-select__option-with-leading-content.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-select__option-with-leading-content .mdc-list-item__start{margin-left:12px;margin-right:0}[dir=rtl] .mdc-select__option-with-leading-content .mdc-list-item__start,.mdc-select__option-with-leading-content .mdc-list-item__start[dir=rtl]{margin-left:0;margin-right:12px;}.mdc-select__option-with-leading-content .mdc-list-item__start{width:36px;height:24px}[dir=rtl] .mdc-select__option-with-leading-content,.mdc-select__option-with-leading-content[dir=rtl]{padding-left:12px;padding-right:0;}.mdc-select__option-with-meta.mdc-list-item{padding-left:auto;padding-right:0}[dir=rtl] .mdc-select__option-with-meta.mdc-list-item,.mdc-select__option-with-meta.mdc-list-item[dir=rtl]{padding-left:0;padding-right:auto;}.mdc-select__option-with-meta .mdc-list-item__end{margin-left:12px;margin-right:12px}[dir=rtl] .mdc-select__option-with-meta .mdc-list-item__end,.mdc-select__option-with-meta .mdc-list-item__end[dir=rtl]{margin-left:12px;margin-right:12px;}.mdc-select--filled .mdc-select__anchor{height:56px;display:flex;align-items:baseline}.mdc-select--filled .mdc-select__anchor::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}.mdc-select--filled.mdc-select--no-label .mdc-select__anchor .mdc-select__selected-text::before{content:\\\"​\\\"}.mdc-select--filled.mdc-select--no-label .mdc-select__anchor .mdc-select__selected-text-container{height:100%;display:inline-flex;align-items:center}.mdc-select--filled.mdc-select--no-label .mdc-select__anchor::before{display:none}.mdc-select--filled .mdc-select__anchor{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-select--filled:not(.mdc-select--disabled) .mdc-select__anchor{background-color:whitesmoke}.mdc-select--filled.mdc-select--disabled .mdc-select__anchor{background-color:#fafafa}.mdc-select--filled:not(.mdc-select--disabled) .mdc-line-ripple::before{border-bottom-color:rgba(0, 0, 0, 0.42)}.mdc-select--filled:not(.mdc-select--disabled):hover .mdc-line-ripple::before{border-bottom-color:rgba(0, 0, 0, 0.87)}.mdc-select--filled:not(.mdc-select--disabled) .mdc-line-ripple::after{border-bottom-color:#6200ee;border-bottom-color:var(--mdc-theme-primary, #6200ee)}.mdc-select--filled.mdc-select--disabled .mdc-line-ripple::before{border-bottom-color:rgba(0, 0, 0, 0.06)}.mdc-select--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-select--filled .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 64px / 0.75)}.mdc-select--filled .mdc-menu-surface--is-open-below{border-top-left-radius:0px;border-top-right-radius:0px}.mdc-select--filled.mdc-select--focused.mdc-line-ripple::after{transform:scale(1, 2);opacity:1}.mdc-select--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-select--filled .mdc-floating-label,.mdc-select--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px;}.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label{left:48px;right:initial}[dir=rtl] .mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label,.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label[dir=rtl]{left:initial;right:48px;}.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 96px / 0.75)}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-line-ripple::before{border-bottom-color:#b00020;border-bottom-color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled):hover .mdc-line-ripple::before{border-bottom-color:#b00020;border-bottom-color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-line-ripple::after{border-bottom-color:#b00020;border-bottom-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined{border:none}.mdc-select--outlined .mdc-select__anchor{height:56px}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{font-size:0.75rem}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined-56px 250ms 1}@keyframes mdc-floating-label-shake-float-above-select-outlined-56px{0%{transform:translateX(calc(0 - 0%)) translateY(-34.75px) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(-34.75px) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(-34.75px) scale(0.75)}100%{transform:translateX(calc(0 - 0%)) translateY(-34.75px) scale(0.75)}}.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0;}@supports (top: max(0%)){.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px, var(--mdc-shape-small, 4px))}}@supports (top: max(0%)){.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px, var(--mdc-shape-small, 4px)) * 2)}}.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px);}@supports (top: max(0%)){.mdc-select--outlined .mdc-select__anchor{padding-left:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-select--outlined .mdc-select__anchor,.mdc-select--outlined .mdc-select__anchor[dir=rtl]{padding-left:0;}@supports (top: max(0%)){[dir=rtl] .mdc-select--outlined .mdc-select__anchor,.mdc-select--outlined .mdc-select__anchor[dir=rtl]{padding-right:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}@supports (top: max(0%)){.mdc-select--outlined+.mdc-select-helper-text{margin-left:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-select--outlined+.mdc-select-helper-text,.mdc-select--outlined+.mdc-select-helper-text[dir=rtl]{margin-left:0;}@supports (top: max(0%)){[dir=rtl] .mdc-select--outlined+.mdc-select-helper-text,.mdc-select--outlined+.mdc-select-helper-text[dir=rtl]{margin-right:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-select--outlined:not(.mdc-select--disabled) .mdc-select__anchor{background-color:transparent}.mdc-select--outlined.mdc-select--disabled .mdc-select__anchor{background-color:transparent}.mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__trailing{border-color:rgba(0, 0, 0, 0.38)}.mdc-select--outlined:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:rgba(0, 0, 0, 0.87)}.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:2px}.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-color:#6200ee;border-color:var(--mdc-theme-primary, #6200ee)}.mdc-select--outlined.mdc-select--disabled .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--disabled .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--disabled .mdc-notched-outline__trailing{border-color:rgba(0, 0, 0, 0.06)}.mdc-select--outlined .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-select--outlined .mdc-select__anchor{display:flex;align-items:baseline;overflow:visible}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined 250ms 1}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{font-size:0.75rem}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-select--outlined .mdc-select__anchor .mdc-select__selected-text::before{content:\\\"​\\\"}.mdc-select--outlined .mdc-select__anchor .mdc-select__selected-text-container{height:100%;display:inline-flex;align-items:center}.mdc-select--outlined .mdc-select__anchor::before{display:none}.mdc-select--outlined .mdc-select__selected-text-container{display:flex;border:none;z-index:1;background-color:transparent}.mdc-select--outlined .mdc-select__icon{z-index:2}.mdc-select--outlined .mdc-floating-label{line-height:1.15rem;left:4px;right:initial}[dir=rtl] .mdc-select--outlined .mdc-floating-label,.mdc-select--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px;}.mdc-select--outlined.mdc-select--focused .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled) .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled) .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled) .mdc-notched-outline__trailing{border-color:#b00020;border-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:#b00020;border-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:2px}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-color:#b00020;border-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label[dir=rtl]{left:initial;right:36px;}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1);}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above{font-size:0.75rem}.mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75);}.mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px 250ms 1}@keyframes mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px{0%{transform:translateX(calc(0 - 32px)) translateY(-34.75px) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(-34.75px) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(-34.75px) scale(0.75)}100%{transform:translateX(calc(0 - 32px)) translateY(-34.75px) scale(0.75)}}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon,.mdc-select--outlined.mdc-select--with-leading-icon[dir=rtl]{}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--shake,.mdc-select--outlined.mdc-select--with-leading-icon[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px 250ms 1}@keyframes mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px-rtl{0%{transform:translateX(calc(0 - -32px)) translateY(-34.75px) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(-34.75px) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(-34.75px) scale(0.75)}100%{transform:translateX(calc(0 - -32px)) translateY(-34.75px) scale(0.75)}}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 96px)}.mdc-select--outlined .mdc-menu-surface{margin-bottom:8px}.mdc-select--outlined.mdc-select--no-label .mdc-menu-surface,.mdc-select--outlined .mdc-menu-surface--is-open-below{margin-bottom:0}.mdc-select__anchor{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}.mdc-select__anchor .mdc-select__ripple::before,.mdc-select__anchor .mdc-select__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-select__anchor .mdc-select__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-select__anchor .mdc-select__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-select__anchor.mdc-ripple-upgraded .mdc-select__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-select__anchor.mdc-ripple-upgraded .mdc-select__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-select__anchor.mdc-ripple-upgraded--unbounded .mdc-select__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-select__anchor.mdc-ripple-upgraded--foreground-activation .mdc-select__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-select__anchor.mdc-ripple-upgraded--foreground-deactivation .mdc-select__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-select__anchor .mdc-select__ripple::before,.mdc-select__anchor .mdc-select__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}.mdc-select__anchor.mdc-ripple-upgraded .mdc-select__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-select__anchor .mdc-select__ripple::before,.mdc-select__anchor .mdc-select__ripple::after{background-color:rgba(0, 0, 0, 0.87);background-color:var(--mdc-ripple-color, rgba(0, 0, 0, 0.87))}.mdc-select__anchor:hover .mdc-select__ripple::before,.mdc-select__anchor.mdc-ripple-surface--hover .mdc-select__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-select__anchor.mdc-ripple-upgraded--background-focused .mdc-select__ripple::before,.mdc-select__anchor:not(.mdc-ripple-upgraded):focus .mdc-select__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-select__anchor .mdc-select__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, var(--mdc-theme-on-surface, #000))}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:hover .mdc-deprecated-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-surface--hover .mdc-deprecated-list-item__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded--background-focused .mdc-deprecated-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):focus .mdc-deprecated-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded) .mdc-deprecated-list-item__ripple::after{transition:opacity 150ms linear}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):active .mdc-deprecated-list-item__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, var(--mdc-theme-on-surface, #000))}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:hover .mdc-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select-helper-text{margin:0;margin-left:16px;margin-right:16px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-caption-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.75rem;font-size:var(--mdc-typography-caption-font-size, 0.75rem);line-height:1.25rem;line-height:var(--mdc-typography-caption-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-caption-font-weight, 400);letter-spacing:0.0333333333em;letter-spacing:var(--mdc-typography-caption-letter-spacing, 0.0333333333em);text-decoration:inherit;text-decoration:var(--mdc-typography-caption-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-caption-text-transform, inherit);display:block;margin-top:0;line-height:normal}[dir=rtl] .mdc-select-helper-text,.mdc-select-helper-text[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-select-helper-text::before{display:inline-block;width:0;height:16px;content:\\\"\\\";vertical-align:0}.mdc-select-helper-text--validation-msg{opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-select--invalid+.mdc-select-helper-text--validation-msg,.mdc-select-helper-text--validation-msg-persistent{opacity:1}.mdc-select--with-leading-icon .mdc-select__icon{display:inline-block;box-sizing:border-box;border:none;text-decoration:none;cursor:pointer;user-select:none;flex-shrink:0;align-self:center;background-color:transparent;fill:currentColor}.mdc-select--with-leading-icon .mdc-select__icon{margin-left:12px;margin-right:12px}[dir=rtl] .mdc-select--with-leading-icon .mdc-select__icon,.mdc-select--with-leading-icon .mdc-select__icon[dir=rtl]{margin-left:12px;margin-right:12px;}.mdc-select__icon:not([tabindex]),.mdc-select__icon[tabindex=\\\"-1\\\"]{cursor:default;pointer-events:none}.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform;transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1), color 150ms cubic-bezier(0.4, 0, 0.2, 1)}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right;}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required::after{margin-left:1px;margin-right:0px;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required,.mdc-floating-label--required[dir=rtl]{}[dir=rtl] .mdc-floating-label--required::after,.mdc-floating-label--required[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0 - 0%)) translateY(-106%) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(-106%) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(-106%) scale(0.75)}100%{transform:translateX(calc(0 - 0%)) translateY(-106%) scale(0.75)}}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);border-bottom-width:2px;opacity:0;z-index:2}.mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1), opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right;}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;border-top:1px solid;border-bottom:1px solid;pointer-events:none}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid;}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid;flex-grow:1}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none;}.mdc-notched-outline__notch{flex:0 0 auto;width:auto;max-width:calc(100% - 12px * 2)}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0;}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-select{display:inline-flex;position:relative}.mdc-select:not(.mdc-select--disabled) .mdc-select__selected-text{color:rgba(0, 0, 0, 0.87)}.mdc-select.mdc-select--disabled .mdc-select__selected-text{color:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled) .mdc-floating-label{color:rgba(0, 0, 0, 0.6)}.mdc-select:not(.mdc-select--disabled).mdc-select--focused .mdc-floating-label{color:rgba(98, 0, 238, 0.87)}.mdc-select.mdc-select--disabled .mdc-floating-label{color:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled) .mdc-select__dropdown-icon{fill:rgba(0, 0, 0, 0.54)}.mdc-select:not(.mdc-select--disabled).mdc-select--focused .mdc-select__dropdown-icon{fill:#6200ee;fill:var(--mdc-theme-primary, #6200ee)}.mdc-select.mdc-select--disabled .mdc-select__dropdown-icon{fill:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled)+.mdc-select-helper-text{color:rgba(0, 0, 0, 0.6)}.mdc-select.mdc-select--disabled+.mdc-select-helper-text{color:rgba(0, 0, 0, 0.38)}.mdc-select:not(.mdc-select--disabled) .mdc-select__icon{color:rgba(0, 0, 0, 0.54)}.mdc-select.mdc-select--disabled .mdc-select__icon{color:rgba(0, 0, 0, 0.38)}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-select.mdc-select--disabled .mdc-select__selected-text{color:GrayText}.mdc-select.mdc-select--disabled .mdc-select__dropdown-icon{fill:red}.mdc-select.mdc-select--disabled .mdc-floating-label{color:GrayText}.mdc-select.mdc-select--disabled .mdc-line-ripple::before{border-bottom-color:GrayText}.mdc-select.mdc-select--disabled .mdc-notched-outline__leading,.mdc-select.mdc-select--disabled .mdc-notched-outline__notch,.mdc-select.mdc-select--disabled .mdc-notched-outline__trailing{border-color:GrayText}.mdc-select.mdc-select--disabled .mdc-select__icon{color:GrayText}.mdc-select.mdc-select--disabled+.mdc-select-helper-text{color:GrayText}}.mdc-select .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-select .mdc-select__anchor{padding-left:16px;padding-right:0}[dir=rtl] .mdc-select .mdc-select__anchor,.mdc-select .mdc-select__anchor[dir=rtl]{padding-left:0;padding-right:16px;}.mdc-select.mdc-select--with-leading-icon .mdc-select__anchor{padding-left:0;padding-right:0}[dir=rtl] .mdc-select.mdc-select--with-leading-icon .mdc-select__anchor,.mdc-select.mdc-select--with-leading-icon .mdc-select__anchor[dir=rtl]{padding-left:0;padding-right:0;}.mdc-select .mdc-select__icon{width:24px;height:24px;font-size:24px}.mdc-select .mdc-select__dropdown-icon{width:24px;height:24px}.mdc-select .mdc-select__menu .mdc-deprecated-list-item{padding-left:16px;padding-right:16px}[dir=rtl] .mdc-select .mdc-select__menu .mdc-deprecated-list-item,.mdc-select .mdc-select__menu .mdc-deprecated-list-item[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-select .mdc-select__menu .mdc-deprecated-list-item__graphic{margin-left:0;margin-right:12px}[dir=rtl] .mdc-select .mdc-select__menu .mdc-deprecated-list-item__graphic,.mdc-select .mdc-select__menu .mdc-deprecated-list-item__graphic[dir=rtl]{margin-left:12px;margin-right:0;}.mdc-select__dropdown-icon{margin-left:12px;margin-right:12px;display:inline-flex;position:relative;align-self:center;align-items:center;justify-content:center;flex-shrink:0;pointer-events:none}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-active,.mdc-select__dropdown-icon .mdc-select__dropdown-icon-inactive{position:absolute;top:0;left:0}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-graphic{width:41.6666666667%;height:20.8333333333%}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-inactive{opacity:1;transition:opacity 75ms linear 75ms}.mdc-select__dropdown-icon .mdc-select__dropdown-icon-active{opacity:0;transition:opacity 75ms linear}[dir=rtl] .mdc-select__dropdown-icon,.mdc-select__dropdown-icon[dir=rtl]{margin-left:12px;margin-right:12px;}.mdc-select--activated .mdc-select__dropdown-icon .mdc-select__dropdown-icon-inactive{opacity:0;transition:opacity 49.5ms linear}.mdc-select--activated .mdc-select__dropdown-icon .mdc-select__dropdown-icon-active{opacity:1;transition:opacity 100.5ms linear 49.5ms}.mdc-select__anchor{width:200px;min-width:0;flex:1 1 auto;position:relative;box-sizing:border-box;overflow:hidden;outline:none;cursor:pointer}.mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-select__selected-text-container{display:flex;appearance:none;pointer-events:none;box-sizing:border-box;width:auto;min-width:0;flex-grow:1;height:28px;border:none;outline:none;padding:0;background-color:transparent;color:inherit}.mdc-select__selected-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-subtitle1-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:1rem;font-size:var(--mdc-typography-subtitle1-font-size, 1rem);line-height:1.75rem;line-height:var(--mdc-typography-subtitle1-line-height, 1.75rem);font-weight:400;font-weight:var(--mdc-typography-subtitle1-font-weight, 400);letter-spacing:0.009375em;letter-spacing:var(--mdc-typography-subtitle1-letter-spacing, 0.009375em);text-decoration:inherit;text-decoration:var(--mdc-typography-subtitle1-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-subtitle1-text-transform, inherit);text-overflow:ellipsis;white-space:nowrap;overflow:hidden;display:block;width:100%;text-align:left}[dir=rtl] .mdc-select__selected-text,.mdc-select__selected-text[dir=rtl]{text-align:right;}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-floating-label{color:#b00020;color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-floating-label{color:#b00020;color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--invalid+.mdc-select-helper-text--validation-msg{color:#b00020;color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-select__dropdown-icon{fill:#b00020;fill:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-select__dropdown-icon{fill:#b00020;fill:var(--mdc-theme-error, #b00020)}.mdc-select--disabled{cursor:default;pointer-events:none}.mdc-select--with-leading-icon .mdc-select__menu .mdc-deprecated-list-item{padding-left:12px;padding-right:12px}[dir=rtl] .mdc-select--with-leading-icon .mdc-select__menu .mdc-deprecated-list-item,.mdc-select--with-leading-icon .mdc-select__menu .mdc-deprecated-list-item[dir=rtl]{padding-left:12px;padding-right:12px;}@media screen and (forced-colors: active), (-ms-high-contrast: active){.mdc-select__menu::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid transparent;border-radius:inherit;content:\\\"\\\";pointer-events:none}}@media screen and (forced-colors: active) and (forced-colors: active), screen and (-ms-high-contrast: active) and (forced-colors: active){.mdc-select__menu::before{border-color:CanvasText}}.mdc-select__menu .mdc-deprecated-list .mdc-select__icon,.mdc-select__menu .mdc-list .mdc-select__icon{margin-left:0;margin-right:0}[dir=rtl] .mdc-select__menu .mdc-deprecated-list .mdc-select__icon,[dir=rtl] .mdc-select__menu .mdc-list .mdc-select__icon,.mdc-select__menu .mdc-deprecated-list .mdc-select__icon[dir=rtl],.mdc-select__menu .mdc-list .mdc-select__icon[dir=rtl]{margin-left:0;margin-right:0;}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--activated,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--selected,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--activated{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__graphic,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--activated .mdc-deprecated-list-item__graphic,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__graphic,.mdc-select__menu .mdc-list .mdc-deprecated-list-item--activated .mdc-deprecated-list-item__graphic{color:#000;color:var(--mdc-theme-on-surface, #000)}.mdc-select__menu .mdc-list-item__start{display:inline-flex;align-items:center}.mdc-select__option{padding-left:16px;padding-right:16px}[dir=rtl] .mdc-select__option,.mdc-select__option[dir=rtl]{padding-left:16px;padding-right:16px;}.mdc-select__one-line-option.mdc-list-item--with-one-line{height:48px}.mdc-select__two-line-option.mdc-list-item--with-two-lines{height:64px}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__start{margin-top:20px}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__primary-text{display:block;margin-top:0;line-height:normal;margin-bottom:-20px}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__primary-text::before{display:inline-block;width:0;height:28px;content:\\\"\\\";vertical-align:0}.mdc-select__two-line-option.mdc-list-item--with-two-lines .mdc-list-item__primary-text::after{display:inline-block;width:0;height:20px;content:\\\"\\\";vertical-align:-20px}.mdc-select__two-line-option.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end{display:block;margin-top:0;line-height:normal}.mdc-select__two-line-option.mdc-list-item--with-two-lines.mdc-list-item--with-trailing-meta .mdc-list-item__end::before{display:inline-block;width:0;height:36px;content:\\\"\\\";vertical-align:0}.mdc-select__option-with-leading-content{padding-left:0;padding-right:12px}.mdc-select__option-with-leading-content.mdc-list-item{padding-left:0;padding-right:auto}[dir=rtl] .mdc-select__option-with-leading-content.mdc-list-item,.mdc-select__option-with-leading-content.mdc-list-item[dir=rtl]{padding-left:auto;padding-right:0;}.mdc-select__option-with-leading-content .mdc-list-item__start{margin-left:12px;margin-right:0}[dir=rtl] .mdc-select__option-with-leading-content .mdc-list-item__start,.mdc-select__option-with-leading-content .mdc-list-item__start[dir=rtl]{margin-left:0;margin-right:12px;}.mdc-select__option-with-leading-content .mdc-list-item__start{width:36px;height:24px}[dir=rtl] .mdc-select__option-with-leading-content,.mdc-select__option-with-leading-content[dir=rtl]{padding-left:12px;padding-right:0;}.mdc-select__option-with-meta.mdc-list-item{padding-left:auto;padding-right:0}[dir=rtl] .mdc-select__option-with-meta.mdc-list-item,.mdc-select__option-with-meta.mdc-list-item[dir=rtl]{padding-left:0;padding-right:auto;}.mdc-select__option-with-meta .mdc-list-item__end{margin-left:12px;margin-right:12px}[dir=rtl] .mdc-select__option-with-meta .mdc-list-item__end,.mdc-select__option-with-meta .mdc-list-item__end[dir=rtl]{margin-left:12px;margin-right:12px;}.mdc-select--filled .mdc-select__anchor{height:56px;display:flex;align-items:baseline}.mdc-select--filled .mdc-select__anchor::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}.mdc-select--filled.mdc-select--no-label .mdc-select__anchor .mdc-select__selected-text::before{content:\\\"​\\\"}.mdc-select--filled.mdc-select--no-label .mdc-select__anchor .mdc-select__selected-text-container{height:100%;display:inline-flex;align-items:center}.mdc-select--filled.mdc-select--no-label .mdc-select__anchor::before{display:none}.mdc-select--filled .mdc-select__anchor{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-select--filled:not(.mdc-select--disabled) .mdc-select__anchor{background-color:whitesmoke}.mdc-select--filled.mdc-select--disabled .mdc-select__anchor{background-color:#fafafa}.mdc-select--filled:not(.mdc-select--disabled) .mdc-line-ripple::before{border-bottom-color:rgba(0, 0, 0, 0.42)}.mdc-select--filled:not(.mdc-select--disabled):hover .mdc-line-ripple::before{border-bottom-color:rgba(0, 0, 0, 0.87)}.mdc-select--filled:not(.mdc-select--disabled) .mdc-line-ripple::after{border-bottom-color:#6200ee;border-bottom-color:var(--mdc-theme-primary, #6200ee)}.mdc-select--filled.mdc-select--disabled .mdc-line-ripple::before{border-bottom-color:rgba(0, 0, 0, 0.06)}.mdc-select--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-select--filled .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 64px / 0.75)}.mdc-select--filled .mdc-menu-surface--is-open-below{border-top-left-radius:0px;border-top-right-radius:0px}.mdc-select--filled.mdc-select--focused.mdc-line-ripple::after{transform:scale(1, 2);opacity:1}.mdc-select--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-select--filled .mdc-floating-label,.mdc-select--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px;}.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label{left:48px;right:initial}[dir=rtl] .mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label,.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label[dir=rtl]{left:initial;right:48px;}.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-select--filled.mdc-select--with-leading-icon .mdc-floating-label--float-above{max-width:calc(100% / 0.75 - 96px / 0.75)}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-line-ripple::before{border-bottom-color:#b00020;border-bottom-color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled):hover .mdc-line-ripple::before{border-bottom-color:#b00020;border-bottom-color:var(--mdc-theme-error, #b00020)}.mdc-select--invalid:not(.mdc-select--disabled) .mdc-line-ripple::after{border-bottom-color:#b00020;border-bottom-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined{border:none}.mdc-select--outlined .mdc-select__anchor{height:56px}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{font-size:0.75rem}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined-56px 250ms 1}@keyframes mdc-floating-label-shake-float-above-select-outlined-56px{0%{transform:translateX(calc(0 - 0%)) translateY(-34.75px) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(-34.75px) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(-34.75px) scale(0.75)}100%{transform:translateX(calc(0 - 0%)) translateY(-34.75px) scale(0.75)}}.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0;}@supports (top: max(0%)){.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px, var(--mdc-shape-small, 4px))}}@supports (top: max(0%)){.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px, var(--mdc-shape-small, 4px)) * 2)}}.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-select--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px);}@supports (top: max(0%)){.mdc-select--outlined .mdc-select__anchor{padding-left:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-select--outlined .mdc-select__anchor,.mdc-select--outlined .mdc-select__anchor[dir=rtl]{padding-left:0;}@supports (top: max(0%)){[dir=rtl] .mdc-select--outlined .mdc-select__anchor,.mdc-select--outlined .mdc-select__anchor[dir=rtl]{padding-right:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}@supports (top: max(0%)){.mdc-select--outlined+.mdc-select-helper-text{margin-left:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-select--outlined+.mdc-select-helper-text,.mdc-select--outlined+.mdc-select-helper-text[dir=rtl]{margin-left:0;}@supports (top: max(0%)){[dir=rtl] .mdc-select--outlined+.mdc-select-helper-text,.mdc-select--outlined+.mdc-select-helper-text[dir=rtl]{margin-right:max(16px, calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-select--outlined:not(.mdc-select--disabled) .mdc-select__anchor{background-color:transparent}.mdc-select--outlined.mdc-select--disabled .mdc-select__anchor{background-color:transparent}.mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled) .mdc-notched-outline__trailing{border-color:rgba(0, 0, 0, 0.38)}.mdc-select--outlined:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:rgba(0, 0, 0, 0.87)}.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:2px}.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-color:#6200ee;border-color:var(--mdc-theme-primary, #6200ee)}.mdc-select--outlined.mdc-select--disabled .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--disabled .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--disabled .mdc-notched-outline__trailing{border-color:rgba(0, 0, 0, 0.06)}.mdc-select--outlined .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-select--outlined .mdc-select__anchor{display:flex;align-items:baseline;overflow:visible}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined 250ms 1}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-select--outlined .mdc-select__anchor .mdc-floating-label--float-above{font-size:0.75rem}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-select--outlined .mdc-select__anchor.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-select--outlined .mdc-select__anchor .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-select--outlined .mdc-select__anchor .mdc-select__selected-text::before{content:\\\"​\\\"}.mdc-select--outlined .mdc-select__anchor .mdc-select__selected-text-container{height:100%;display:inline-flex;align-items:center}.mdc-select--outlined .mdc-select__anchor::before{display:none}.mdc-select--outlined .mdc-select__selected-text-container{display:flex;border:none;z-index:1;background-color:transparent}.mdc-select--outlined .mdc-select__icon{z-index:2}.mdc-select--outlined .mdc-floating-label{line-height:1.15rem;left:4px;right:initial}[dir=rtl] .mdc-select--outlined .mdc-floating-label,.mdc-select--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px;}.mdc-select--outlined.mdc-select--focused .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled) .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled) .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled) .mdc-notched-outline__trailing{border-color:#b00020;border-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled):not(.mdc-select--focused) .mdc-select__anchor:hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:#b00020;border-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:2px}.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-select--outlined.mdc-select--invalid:not(.mdc-select--disabled).mdc-select--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-color:#b00020;border-color:var(--mdc-theme-error, #b00020)}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label[dir=rtl]{left:initial;right:36px;}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1);}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--float-above{font-size:0.75rem}.mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75);}.mdc-select--outlined.mdc-select--with-leading-icon.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-select--outlined.mdc-select--with-leading-icon .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px 250ms 1}@keyframes mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px{0%{transform:translateX(calc(0 - 32px)) translateY(-34.75px) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(-34.75px) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(-34.75px) scale(0.75)}100%{transform:translateX(calc(0 - 32px)) translateY(-34.75px) scale(0.75)}}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon,.mdc-select--outlined.mdc-select--with-leading-icon[dir=rtl]{}[dir=rtl] .mdc-select--outlined.mdc-select--with-leading-icon .mdc-floating-label--shake,.mdc-select--outlined.mdc-select--with-leading-icon[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px 250ms 1}@keyframes mdc-floating-label-shake-float-above-select-outlined-leading-icon-56px-rtl{0%{transform:translateX(calc(0 - -32px)) translateY(-34.75px) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(-34.75px) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(-34.75px) scale(0.75)}100%{transform:translateX(calc(0 - -32px)) translateY(-34.75px) scale(0.75)}}.mdc-select--outlined.mdc-select--with-leading-icon .mdc-select__anchor :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 96px)}.mdc-select--outlined .mdc-menu-surface{margin-bottom:8px}.mdc-select--outlined.mdc-select--no-label .mdc-menu-surface,.mdc-select--outlined .mdc-menu-surface--is-open-below{margin-bottom:0}.mdc-select__anchor{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}.mdc-select__anchor .mdc-select__ripple::before,.mdc-select__anchor .mdc-select__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-select__anchor .mdc-select__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-select__anchor .mdc-select__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-select__anchor.mdc-ripple-upgraded .mdc-select__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-select__anchor.mdc-ripple-upgraded .mdc-select__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-select__anchor.mdc-ripple-upgraded--unbounded .mdc-select__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-select__anchor.mdc-ripple-upgraded--foreground-activation .mdc-select__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-select__anchor.mdc-ripple-upgraded--foreground-deactivation .mdc-select__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-select__anchor .mdc-select__ripple::before,.mdc-select__anchor .mdc-select__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}.mdc-select__anchor.mdc-ripple-upgraded .mdc-select__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-select__anchor .mdc-select__ripple::before,.mdc-select__anchor .mdc-select__ripple::after{background-color:rgba(0, 0, 0, 0.87);background-color:var(--mdc-ripple-color, rgba(0, 0, 0, 0.87))}.mdc-select__anchor:hover .mdc-select__ripple::before,.mdc-select__anchor.mdc-ripple-surface--hover .mdc-select__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-select__anchor.mdc-ripple-upgraded--background-focused .mdc-select__ripple::before,.mdc-select__anchor:not(.mdc-ripple-upgraded):focus .mdc-select__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-select__anchor .mdc-select__ripple{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-deprecated-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, var(--mdc-theme-on-surface, #000))}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:hover .mdc-deprecated-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-surface--hover .mdc-deprecated-list-item__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded--background-focused .mdc-deprecated-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):focus .mdc-deprecated-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded) .mdc-deprecated-list-item__ripple::after{transition:opacity 150ms linear}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):active .mdc-deprecated-list-item__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected .mdc-list-item__ripple::after{background-color:#000;background-color:var(--mdc-ripple-color, var(--mdc-theme-on-surface, #000))}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:hover .mdc-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-surface--hover .mdc-list-item__ripple::before{opacity:0.04;opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded--background-focused .mdc-list-item__ripple::before,.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):focus .mdc-list-item__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded) .mdc-list-item__ripple::after{transition:opacity 150ms linear}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected:not(.mdc-ripple-upgraded):active .mdc-list-item__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select__menu .mdc-deprecated-list .mdc-deprecated-list-item--selected.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mdc-select-helper-text{margin:0;margin-left:16px;margin-right:16px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-caption-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));font-size:0.75rem;font-size:var(--mdc-typography-caption-font-size, 0.75rem);line-height:1.25rem;line-height:var(--mdc-typography-caption-line-height, 1.25rem);font-weight:400;font-weight:var(--mdc-typography-caption-font-weight, 400);letter-spacing:0.0333333333em;letter-spacing:var(--mdc-typography-caption-letter-spacing, 0.0333333333em);text-decoration:inherit;text-decoration:var(--mdc-typography-caption-text-decoration, inherit);text-transform:inherit;text-transform:var(--mdc-typography-caption-text-transform, inherit);display:block;margin-top:0;line-height:normal}[dir=rtl] .mdc-select-helper-text,.mdc-select-helper-text[dir=rtl]{margin-left:16px;margin-right:16px;}.mdc-select-helper-text::before{display:inline-block;width:0;height:16px;content:\\\"\\\";vertical-align:0}.mdc-select-helper-text--validation-msg{opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-select--invalid+.mdc-select-helper-text--validation-msg,.mdc-select-helper-text--validation-msg-persistent{opacity:1}.mdc-select--with-leading-icon .mdc-select__icon{display:inline-block;box-sizing:border-box;border:none;text-decoration:none;cursor:pointer;user-select:none;flex-shrink:0;align-self:center;background-color:transparent;fill:currentColor}.mdc-select--with-leading-icon .mdc-select__icon{margin-left:12px;margin-right:12px}[dir=rtl] .mdc-select--with-leading-icon .mdc-select__icon,.mdc-select--with-leading-icon .mdc-select__icon[dir=rtl]{margin-left:12px;margin-right:12px;}.mdc-select__icon:not([tabindex]),.mdc-select__icon[tabindex=\\\"-1\\\"]{cursor:default;pointer-events:none}.mdc-touch-target-wrapper{display:inline}.mdc-button{position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;user-select:none;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:transparent}.mdc-button .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button .mdc-button__icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top}[dir=rtl] .mdc-button .mdc-button__icon,.mdc-button .mdc-button__icon[dir=rtl]{margin-left:8px;margin-right:0;}.mdc-button .mdc-button__label{position:relative}.mdc-button .mdc-button__focus-ring{display:none}@media screen and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{pointer-events:none;border:2px solid transparent;border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(\\n      100% + 4px\\n    );width:calc(\\n      100% + 4px\\n    );display:block}}@media screen and (forced-colors: active) and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{border-color:CanvasText}}@media screen and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring::after,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring::after{content:\\\"\\\";border:2px solid transparent;border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}}@media screen and (forced-colors: active) and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring::after,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring::after{border-color:CanvasText}}.mdc-button .mdc-button__touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:0}[dir=rtl] .mdc-button__label+.mdc-button__icon,.mdc-button__label+.mdc-button__icon[dir=rtl]{margin-left:0;margin-right:8px;}svg.mdc-button__icon{fill:currentColor}.mdc-button--touch{margin-top:6px;margin-bottom:6px}.mdc-button{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:Roboto, sans-serif;font-family:var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));text-decoration:none;text-decoration:var(--mdc-typography-button-text-decoration, none)}.mdc-button{padding:0 8px 0 8px}.mdc-button--unelevated{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--unelevated.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--unelevated.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--raised{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--raised.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--raised.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--outlined{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button--outlined .mdc-button__ripple{border-style:solid;border-color:transparent}.mdc-button{--mdc-ripple-fg-size:0;--mdc-ripple-left:0;--mdc-ripple-top:0;--mdc-ripple-fg-scale:1;--mdc-ripple-fg-translate-end:0;--mdc-ripple-fg-translate-start:0;-webkit-tap-highlight-color:rgba(0, 0, 0, 0);will-change:transform, opacity}.mdc-button .mdc-button__ripple::before,.mdc-button .mdc-button__ripple::after{position:absolute;border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\"}.mdc-button .mdc-button__ripple::before{transition:opacity 15ms linear, background-color 15ms linear;z-index:1;z-index:var(--mdc-ripple-z-index, 1)}.mdc-button .mdc-button__ripple::after{z-index:0;z-index:var(--mdc-ripple-z-index, 0)}.mdc-button.mdc-ripple-upgraded .mdc-button__ripple::before{transform:scale(var(--mdc-ripple-fg-scale, 1))}.mdc-button.mdc-ripple-upgraded .mdc-button__ripple::after{top:0;left:0;transform:scale(0);transform-origin:center center}.mdc-button.mdc-ripple-upgraded--unbounded .mdc-button__ripple::after{top:var(--mdc-ripple-top, 0);left:var(--mdc-ripple-left, 0)}.mdc-button.mdc-ripple-upgraded--foreground-activation .mdc-button__ripple::after{animation:mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards}.mdc-button.mdc-ripple-upgraded--foreground-deactivation .mdc-button__ripple::after{animation:mdc-ripple-fg-opacity-out 150ms;transform:translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1))}.mdc-button .mdc-button__ripple::before,.mdc-button .mdc-button__ripple::after{top:calc(50% - 100%);left:calc(50% - 100%);width:200%;height:200%}.mdc-button.mdc-ripple-upgraded .mdc-button__ripple::after{width:var(--mdc-ripple-fg-size, 100%);height:var(--mdc-ripple-fg-size, 100%)}.mdc-button__ripple{position:absolute;box-sizing:content-box;overflow:hidden;z-index:0;top:0;left:0;bottom:0;right:0}.mdc-button{font-family:Roboto, sans-serif;font-family:var(--mdc-text-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));font-size:0.875rem;font-size:var(--mdc-text-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));letter-spacing:0.0892857143em;letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));font-weight:500;font-weight:var(--mdc-text-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));text-transform:uppercase;text-transform:var(--mdc-text-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));height:36px;height:var(--mdc-text-button-container-height, 36px);border-radius:4px;border-radius:var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px))}.mdc-button:not(:disabled){color:#6200ee;color:var(--mdc-text-button-label-text-color, var(--mdc-theme-primary, #6200ee))}.mdc-button:disabled{color:rgba(0, 0, 0, 0.38);color:var(--mdc-text-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mdc-button .mdc-button__icon{font-size:1.125rem;font-size:var(--mdc-text-button-with-icon-icon-size, 1.125rem);width:1.125rem;width:var(--mdc-text-button-with-icon-icon-size, 1.125rem);height:1.125rem;height:var(--mdc-text-button-with-icon-icon-size, 1.125rem)}.mdc-button .mdc-button__ripple::before,.mdc-button .mdc-button__ripple::after{background-color:#6200ee;background-color:var(--mdc-text-button-hover-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mdc-button:hover .mdc-button__ripple::before,.mdc-button.mdc-ripple-surface--hover .mdc-button__ripple::before{opacity:0.04;opacity:var(--mdc-text-button-hover-state-layer-opacity, 0.04)}.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-text-button-focus-state-layer-opacity, 0.12)}.mdc-button:not(.mdc-ripple-upgraded) .mdc-button__ripple::after{transition:opacity 150ms linear}.mdc-button:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-text-button-pressed-state-layer-opacity, 0.12)}.mdc-button.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-text-button-pressed-state-layer-opacity, 0.12)}.mdc-button .mdc-button__ripple{border-radius:4px;border-radius:var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px))}.mdc-button--unelevated{font-family:Roboto, sans-serif;font-family:var(--mdc-filled-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));font-size:0.875rem;font-size:var(--mdc-filled-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));letter-spacing:0.0892857143em;letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));font-weight:500;font-weight:var(--mdc-filled-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));text-transform:uppercase;text-transform:var(--mdc-filled-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));height:36px;height:var(--mdc-filled-button-container-height, 36px);border-radius:4px;border-radius:var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px))}.mdc-button--unelevated:not(:disabled){background-color:#6200ee;background-color:var(--mdc-filled-button-container-color, var(--mdc-theme-primary, #6200ee))}.mdc-button--unelevated:disabled{background-color:rgba(0, 0, 0, 0.12);background-color:var(--mdc-filled-button-disabled-container-color, rgba(0, 0, 0, 0.12))}.mdc-button--unelevated:not(:disabled){color:#fff;color:var(--mdc-filled-button-label-text-color, var(--mdc-theme-on-primary, #fff))}.mdc-button--unelevated:disabled{color:rgba(0, 0, 0, 0.38);color:var(--mdc-filled-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mdc-button--unelevated .mdc-button__icon{font-size:1.125rem;font-size:var(--mdc-filled-button-with-icon-icon-size, 1.125rem);width:1.125rem;width:var(--mdc-filled-button-with-icon-icon-size, 1.125rem);height:1.125rem;height:var(--mdc-filled-button-with-icon-icon-size, 1.125rem)}.mdc-button--unelevated .mdc-button__ripple::before,.mdc-button--unelevated .mdc-button__ripple::after{background-color:#fff;background-color:var(--mdc-filled-button-hover-state-layer-color, var(--mdc-theme-on-primary, #fff))}.mdc-button--unelevated:hover .mdc-button__ripple::before,.mdc-button--unelevated.mdc-ripple-surface--hover .mdc-button__ripple::before{opacity:0.08;opacity:var(--mdc-filled-button-hover-state-layer-opacity, 0.08)}.mdc-button--unelevated.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before,.mdc-button--unelevated:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-filled-button-focus-state-layer-opacity, 0.24)}.mdc-button--unelevated:not(.mdc-ripple-upgraded) .mdc-button__ripple::after{transition:opacity 150ms linear}.mdc-button--unelevated:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-filled-button-pressed-state-layer-opacity, 0.24)}.mdc-button--unelevated.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-filled-button-pressed-state-layer-opacity, 0.24)}.mdc-button--unelevated .mdc-button__ripple{border-radius:4px;border-radius:var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px))}.mdc-button--raised{font-family:Roboto, sans-serif;font-family:var(--mdc-protected-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));font-size:0.875rem;font-size:var(--mdc-protected-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));letter-spacing:0.0892857143em;letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));font-weight:500;font-weight:var(--mdc-protected-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));text-transform:uppercase;text-transform:var(--mdc-protected-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));height:36px;height:var(--mdc-protected-button-container-height, 36px);border-radius:4px;border-radius:var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px));--mdc-elevation-box-shadow-for-gss:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:var(--mdc-protected-button-container-elevation, var(--mdc-elevation-box-shadow-for-gss))}.mdc-button--raised:not(:disabled){background-color:#6200ee;background-color:var(--mdc-protected-button-container-color, var(--mdc-theme-primary, #6200ee))}.mdc-button--raised:disabled{background-color:rgba(0, 0, 0, 0.12);background-color:var(--mdc-protected-button-disabled-container-color, rgba(0, 0, 0, 0.12))}.mdc-button--raised:not(:disabled){color:#fff;color:var(--mdc-protected-button-label-text-color, var(--mdc-theme-on-primary, #fff))}.mdc-button--raised:disabled{color:rgba(0, 0, 0, 0.38);color:var(--mdc-protected-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mdc-button--raised .mdc-button__icon{font-size:1.125rem;font-size:var(--mdc-protected-button-with-icon-icon-size, 1.125rem);width:1.125rem;width:var(--mdc-protected-button-with-icon-icon-size, 1.125rem);height:1.125rem;height:var(--mdc-protected-button-with-icon-icon-size, 1.125rem)}.mdc-button--raised .mdc-button__ripple::before,.mdc-button--raised .mdc-button__ripple::after{background-color:#fff;background-color:var(--mdc-protected-button-hover-state-layer-color, var(--mdc-theme-on-primary, #fff))}.mdc-button--raised:hover .mdc-button__ripple::before,.mdc-button--raised.mdc-ripple-surface--hover .mdc-button__ripple::before{opacity:0.08;opacity:var(--mdc-protected-button-hover-state-layer-opacity, 0.08)}.mdc-button--raised.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before,.mdc-button--raised:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-protected-button-focus-state-layer-opacity, 0.24)}.mdc-button--raised:not(.mdc-ripple-upgraded) .mdc-button__ripple::after{transition:opacity 150ms linear}.mdc-button--raised:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after{transition-duration:75ms;opacity:0.24;opacity:var(--mdc-protected-button-pressed-state-layer-opacity, 0.24)}.mdc-button--raised.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-protected-button-pressed-state-layer-opacity, 0.24)}.mdc-button--raised .mdc-button__ripple{border-radius:4px;border-radius:var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px))}.mdc-button--raised.mdc-ripple-upgraded--background-focused,.mdc-button--raised:not(.mdc-ripple-upgraded):focus{--mdc-elevation-box-shadow-for-gss:0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);box-shadow:var(--mdc-protected-button-focus-container-elevation, var(--mdc-elevation-box-shadow-for-gss))}.mdc-button--raised:hover{--mdc-elevation-box-shadow-for-gss:0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);box-shadow:var(--mdc-protected-button-hover-container-elevation, var(--mdc-elevation-box-shadow-for-gss))}.mdc-button--raised:not(:disabled):active{--mdc-elevation-box-shadow-for-gss:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);box-shadow:var(--mdc-protected-button-pressed-container-elevation, var(--mdc-elevation-box-shadow-for-gss))}.mdc-button--raised:disabled{--mdc-elevation-box-shadow-for-gss:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);box-shadow:var(--mdc-protected-button-disabled-container-elevation, var(--mdc-elevation-box-shadow-for-gss))}.mdc-button--outlined{font-family:Roboto, sans-serif;font-family:var(--mdc-outlined-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));font-size:0.875rem;font-size:var(--mdc-outlined-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));letter-spacing:0.0892857143em;letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));font-weight:500;font-weight:var(--mdc-outlined-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));text-transform:uppercase;text-transform:var(--mdc-outlined-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));height:36px;height:var(--mdc-outlined-button-container-height, 36px);border-radius:4px;border-radius:var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px));padding:0 15px 0 15px;border-width:1px;border-width:var(--mdc-outlined-button-outline-width, 1px)}.mdc-button--outlined:not(:disabled){color:#6200ee;color:var(--mdc-outlined-button-label-text-color, var(--mdc-theme-primary, #6200ee))}.mdc-button--outlined:disabled{color:rgba(0, 0, 0, 0.38);color:var(--mdc-outlined-button-disabled-label-text-color, rgba(0, 0, 0, 0.38))}.mdc-button--outlined .mdc-button__icon{font-size:1.125rem;font-size:var(--mdc-outlined-button-with-icon-icon-size, 1.125rem);width:1.125rem;width:var(--mdc-outlined-button-with-icon-icon-size, 1.125rem);height:1.125rem;height:var(--mdc-outlined-button-with-icon-icon-size, 1.125rem)}.mdc-button--outlined .mdc-button__ripple::before,.mdc-button--outlined .mdc-button__ripple::after{background-color:#6200ee;background-color:var(--mdc-outlined-button-hover-state-layer-color, var(--mdc-theme-primary, #6200ee))}.mdc-button--outlined:hover .mdc-button__ripple::before,.mdc-button--outlined.mdc-ripple-surface--hover .mdc-button__ripple::before{opacity:0.04;opacity:var(--mdc-outlined-button-hover-state-layer-opacity, 0.04)}.mdc-button--outlined.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before,.mdc-button--outlined:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-outlined-button-focus-state-layer-opacity, 0.12)}.mdc-button--outlined:not(.mdc-ripple-upgraded) .mdc-button__ripple::after{transition:opacity 150ms linear}.mdc-button--outlined:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after{transition-duration:75ms;opacity:0.12;opacity:var(--mdc-outlined-button-pressed-state-layer-opacity, 0.12)}.mdc-button--outlined.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-outlined-button-pressed-state-layer-opacity, 0.12)}.mdc-button--outlined .mdc-button__ripple{border-radius:4px;border-radius:var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px))}.mdc-button--outlined:not(:disabled){border-color:rgba(0, 0, 0, 0.12);border-color:var(--mdc-outlined-button-outline-color, rgba(0, 0, 0, 0.12))}.mdc-button--outlined:disabled{border-color:rgba(0, 0, 0, 0.12);border-color:var(--mdc-outlined-button-disabled-outline-color, rgba(0, 0, 0, 0.12))}.mdc-button--outlined.mdc-button--icon-trailing{padding:0 11px 0 15px}.mdc-button--outlined.mdc-button--icon-leading{padding:0 15px 0 11px}.mdc-button--outlined .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:1px;border-width:var(--mdc-outlined-button-outline-width, 1px)}.mdc-button--outlined .mdc-button__touch{left:calc(-1 * 1px);left:calc(-1 * var(--mdc-outlined-button-outline-width, 1px));width:calc(100% + 2 * 1px);width:calc(100% + 2 * var(--mdc-outlined-button-outline-width, 1px))}.mdc-button--raised .mdc-button__icon,.mdc-button--unelevated .mdc-button__icon,.mdc-button--outlined .mdc-button__icon{margin-left:-4px;margin-right:8px}[dir=rtl] .mdc-button--raised .mdc-button__icon,[dir=rtl] .mdc-button--unelevated .mdc-button__icon,[dir=rtl] .mdc-button--outlined .mdc-button__icon,.mdc-button--raised .mdc-button__icon[dir=rtl],.mdc-button--unelevated .mdc-button__icon[dir=rtl],.mdc-button--outlined .mdc-button__icon[dir=rtl]{margin-left:8px;margin-right:-4px;}.mdc-button--raised .mdc-button__label+.mdc-button__icon,.mdc-button--unelevated .mdc-button__label+.mdc-button__icon,.mdc-button--outlined .mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:-4px}[dir=rtl] .mdc-button--raised .mdc-button__label+.mdc-button__icon,[dir=rtl] .mdc-button--unelevated .mdc-button__label+.mdc-button__icon,[dir=rtl] .mdc-button--outlined .mdc-button__label+.mdc-button__icon,.mdc-button--raised .mdc-button__label+.mdc-button__icon[dir=rtl],.mdc-button--unelevated .mdc-button__label+.mdc-button__icon[dir=rtl],.mdc-button--outlined .mdc-button__label+.mdc-button__icon[dir=rtl]{margin-left:-4px;margin-right:8px;}/*!\\n *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */@font-face{font-family:var(--bci-global-font-family);font-weight:400;src:url(\\\"..//fonts/BoschSans-Regular.eot\\\");src:url(\\\"..//fonts/BoschSans-Regular.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Regular.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Regular.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Regular.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-RegularItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Regular.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:300;src:url(\\\"..//fonts/BoschSans-Light.eot\\\");src:url(\\\"..//fonts/BoschSans-Light.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Light.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Light.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Light.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-LightItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-LightItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-LightItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Light.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:500;src:url(\\\"..//fonts/BoschSans-Medium.eot\\\");src:url(\\\"..//fonts/BoschSans-Medium.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Medium.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Medium.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Medium.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-MediumItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Medium.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:700;src:url(\\\"..//fonts/BoschSans-Bold.eot\\\");src:url(\\\"..//fonts/BoschSans-Bold.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Bold.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Bold.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Bold.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-BoldItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Bold.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:var(--bci-global-font-family);font-weight:900;src:url(\\\"..//fonts/BoschSans-Black.eot\\\");src:url(\\\"..//fonts/BoschSans-Black.eot?#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/BoschSans-Black.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-Black.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-Black.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.woff2\\\") format(\\\"woff2\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.woff\\\") format(\\\"woff\\\"), url(\\\"..//fonts/BoschSans-BlackItalic.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSans-Black.svg#svgFontName\\\") format(\\\"svg\\\");}@font-face{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-stretch:normal;font-weight:normal;font-display:block;src:url(\\\"..//fonts/Bosch-Icon.eot?mh5qa9\\\");src:url(\\\"..//fonts/Bosch-Icon.eot?mh5qa9#iefix\\\") format(\\\"embedded-opentype\\\"), url(\\\"..//fonts/Bosch-Icon.ttf?mh5qa9\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/Bosch-Icon.woff?mh5qa9\\\") format(\\\"woff\\\"), url(\\\"..//fonts/Bosch-Icon.svg?mh5qa9#Bosch-Icon\\\") format(\\\"svg\\\")}@font-face{font-family:\\\"Bosch-Sans-Condensed\\\";font-weight:300;src:url(\\\"..//fonts/BoschSansCond-Regular.otf\\\"), url(\\\"..//fonts/BoschSansCondensed-Regular.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSansCondensed-RegularItalic.ttf\\\") format(\\\"truetype\\\")}@font-face{font-family:\\\"Bosch-Sans-Condensed\\\";font-weight:700;src:url(\\\"..//fonts/BoschSansCond-Bold.otf\\\"), url(\\\"..//fonts/BoschSansCondensed-Bold.ttf\\\") format(\\\"truetype\\\"), url(\\\"..//fonts/BoschSansCondensed-BoldItalic.ttf\\\") format(\\\"truetype\\\")}/*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n*//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2018 Robert Bosch GmbH Copyright (C) 2018 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2019 Robert Bosch GmbH Copyright (C) 2019 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2020 Robert Bosch GmbH Copyright (C) 2020 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2022 Robert Bosch GmbH Copyright (C) 2022 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2023 Robert Bosch GmbH Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n *//*!\\n  *  Copyright (C) 2024 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */.flex-column{display:flex;flex-direction:column;box-sizing:border-box}.flex-row{display:flex;flex-direction:row;box-sizing:border-box}.flex-none{flex:0 0 auto}.flex-grow{flex:1 1 100%;height:100%}.flex-layout-start-strech{place-content:stretch flex-start;align-items:stretch}.flex-layout-center-center{place-content:center;align-items:center}.flex-fill{width:100%;box-sizing:border-box;max-width:100%}.flex-row-wrap{flex-flow:row wrap;box-sizing:border-box;display:flex}.flex-row-wrap.grow{flex:1 1 100%;max-width:100%}.flex-center{display:flex;justify-content:center;align-items:center}.flex-space-between{display:flex;justify-content:space-between;align-items:center}/*!\\n  *  Copyright (C) 2017 Robert Bosch GmbH Copyright (C) 2017 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.\\n */body{margin:0;display:flex;flex-direction:column}main,*{font-family:var(--bci-global-font-family)}::selection,::-moz-selection{background-color:#007bc0}a::-moz-selection{color:#ffffff}.lead{margin-bottom:24px;font-size:18px;font-weight:300;line-height:1.4}@media (min-width: 768px){.lead{font-size:24px}}.bosch-ic,.Bosch-Ic{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic:before,.Bosch-Ic:before{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium,.Bosch-Ic-Medium{line-height:1;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-medium:before,.Bosch-Ic-Medium:before{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bosch-ic-clickable,.Bosch-Ic-Clickable{cursor:pointer}.bosch-ic-clickable:disabled,.bosch-ic-clickable.disabled,.Bosch-Ic-Clickable:disabled,.Bosch-Ic-Clickable.disabled{color:#a4abb3;cursor:not-allowed}.bosch-ic-down:before{content:\\\"\\\\e147\\\"}:host(.hidden){display:none;visibility:hidden}:host(.hidden-page-size-selector){justify-content:flex-end}:host(.hidden-page-size-selector) .mdc-select{display:none}:host{display:flex;flex-flow:row wrap;justify-content:space-between;align-items:center;margin-top:24px}:host .mdc-select{border-radius:0}:host .mdc-select:not(.mdc-select--disabled) .mdc-floating-label{color:#000000}:host .mdc-select.hidden{visibility:hidden}:host .mdc-select>.mdc-select__anchor{border-radius:0}:host .mdc-select div.bci-core-paginator-dropdown{background-color:#e0e2e5;width:192px;height:48px}:host .mdc-select div.bci-core-paginator-dropdown:hover{background-color:#c1c7cc}:host .mdc-select div.bci-core-paginator-dropdown:hover{background-color:#a4abb3}:host .mdc-select div.bci-core-paginator-dropdown>.mdc-select__dropdown-icon.bosch-ic.bosch-ic-down{height:20px;width:20px}:host .mdc-select div.bci-core-paginator-dropdown>.mdc-floating-label{font-family:var(--bci-global-font-family);font-size:12px;font-weight:500;line-height:18px;transform:translateY(-106%) scale(1);will-change:inherit}:host .mdc-select div.bci-core-paginator-dropdown>.mdc-select__selected-text{font-family:var(--bci-global-font-family);font-size:16px;font-weight:400;line-height:24px}:host .mdc-select .mdc-select__dropdown-icon{transform:rotate(0);transition:transform ease-in-out 200ms}:host .mdc-select.mdc-select--activated .mdc-select__dropdown-icon{transform:rotate(-180deg)}:host .mdc-select div.bci-core-paginator-dropdown-select{width:192px;background:white;border-radius:0}:host .mdc-select div.bci-core-paginator-dropdown-select>.mdc-list{padding:0}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item{height:48px}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item:focus{background-color:#007bc0;color:#ffffff}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item.mdc-list-item--selected{background-color:#007bc0;color:#ffffff}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item.mdc-list-item--selected:hover{background-color:#00629a;color:#ffffff}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item.mdc-list-item--selected:active{background-color:#004975;color:#ffffff}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item:hover{background-color:#e0e2e5;color:#000000}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item:active{background-color:#c1c7cc}:host .mdc-select div.bci-core-paginator-dropdown-select .mdc-list-item .mdc-list-item__text{margin:auto 0}:host .bci-core-paginator-pages-container.hidden{visibility:hidden}@media (min-width: 480px){:host .bci-core-paginator-pages-container{display:flex;flex-flow:row nowrap;justify-content:flex-end;min-width:auto;height:32px}:host .bci-core-paginator-pages-container *,:host .bci-core-paginator-pages-container *:hover,:host .bci-core-paginator-pages-container *:active,:host .bci-core-paginator-pages-container *:focus{margin:0;padding:0;line-height:1.5;color:#000000;border:none;background:none;text-decoration:none}:host .bci-core-paginator-pages-container>*,:host .bci-core-paginator-pages-container>*:hover,:host .bci-core-paginator-pages-container>*:active,:host .bci-core-paginator-pages-container>*:focus{cursor:pointer;font-size:16px;display:inline-block;min-width:32px;width:32px;height:32px;line-height:32px;border-radius:50%;text-align:center;margin-right:8px;color:#000000;background-color:transparent}:host .bci-core-paginator-pages-container>*:first-child,:host .bci-core-paginator-pages-container>*:last-child,:host .bci-core-paginator-pages-container>*:hover:first-child,:host .bci-core-paginator-pages-container>*:hover:last-child,:host .bci-core-paginator-pages-container>*:active:first-child,:host .bci-core-paginator-pages-container>*:active:last-child,:host .bci-core-paginator-pages-container>*:focus:first-child,:host .bci-core-paginator-pages-container>*:focus:last-child{height:24px;line-height:1;width:24px;min-width:24px;color:#000000;background-color:transparent}:host .bci-core-paginator-pages-container>*:first-child:hover,:host .bci-core-paginator-pages-container>*:last-child:hover,:host .bci-core-paginator-pages-container>*:hover:first-child:hover,:host .bci-core-paginator-pages-container>*:hover:last-child:hover,:host .bci-core-paginator-pages-container>*:active:first-child:hover,:host .bci-core-paginator-pages-container>*:active:last-child:hover,:host .bci-core-paginator-pages-container>*:focus:first-child:hover,:host .bci-core-paginator-pages-container>*:focus:last-child:hover{color:#007bc0}:host .bci-core-paginator-pages-container>*:first-child:active,:host .bci-core-paginator-pages-container>*:last-child:active,:host .bci-core-paginator-pages-container>*:hover:first-child:active,:host .bci-core-paginator-pages-container>*:hover:last-child:active,:host .bci-core-paginator-pages-container>*:active:first-child:active,:host .bci-core-paginator-pages-container>*:active:last-child:active,:host .bci-core-paginator-pages-container>*:focus:first-child:active,:host .bci-core-paginator-pages-container>*:focus:last-child:active{color:#00629a}:host .bci-core-paginator-pages-container>*:first-child:disabled,:host .bci-core-paginator-pages-container>*:first-child.disabled,:host .bci-core-paginator-pages-container>*:last-child:disabled,:host .bci-core-paginator-pages-container>*:last-child.disabled,:host .bci-core-paginator-pages-container>*:hover:first-child:disabled,:host .bci-core-paginator-pages-container>*:hover:first-child.disabled,:host .bci-core-paginator-pages-container>*:hover:last-child:disabled,:host .bci-core-paginator-pages-container>*:hover:last-child.disabled,:host .bci-core-paginator-pages-container>*:active:first-child:disabled,:host .bci-core-paginator-pages-container>*:active:first-child.disabled,:host .bci-core-paginator-pages-container>*:active:last-child:disabled,:host .bci-core-paginator-pages-container>*:active:last-child.disabled,:host .bci-core-paginator-pages-container>*:focus:first-child:disabled,:host .bci-core-paginator-pages-container>*:focus:first-child.disabled,:host .bci-core-paginator-pages-container>*:focus:last-child:disabled,:host .bci-core-paginator-pages-container>*:focus:last-child.disabled{color:#c1c7cc;cursor:default}:host .bci-core-paginator-pages-container>*:first-child::after,:host .bci-core-paginator-pages-container>*:hover:first-child::after,:host .bci-core-paginator-pages-container>*:active:first-child::after,:host .bci-core-paginator-pages-container>*:focus:first-child::after{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;content:\\\"\\\\e0a0\\\";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}:host .bci-core-paginator-pages-container>*:last-child,:host .bci-core-paginator-pages-container>*:hover:last-child,:host .bci-core-paginator-pages-container>*:active:last-child,:host .bci-core-paginator-pages-container>*:focus:last-child{margin-right:0px}:host .bci-core-paginator-pages-container>*:last-child::after,:host .bci-core-paginator-pages-container>*:hover:last-child::after,:host .bci-core-paginator-pages-container>*:active:last-child::after,:host .bci-core-paginator-pages-container>*:focus:last-child::after{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;content:\\\"\\\\e181\\\";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}:host .bci-core-paginator-pages-container>*:hover{background-color:#e0e2e5}:host .bci-core-paginator-pages-container>*:hover *{background-color:#e0e2e5}:host .bci-core-paginator-pages-container>*:active{background-color:#c1c7cc}:host .bci-core-paginator-pages-container>*:active *{background-color:#c1c7cc}:host .bci-core-paginator-pages-container>*:not(:last-child):not(:first-child):disabled,:host .bci-core-paginator-pages-container *:not(:last-child):not(:first-child).disabled{cursor:default;color:#c1c7cc;background-color:#ffffff}:host .bci-core-paginator-pages-container>*:not(:last-child):not(:first-child):disabled *,:host .bci-core-paginator-pages-container *:not(:last-child):not(:first-child).disabled *{cursor:default;color:#c1c7cc;background-color:#ffffff}:host .bci-core-paginator-pages-container>*:first-child:disabled,:host .bci-core-paginator-pages-container *:last-child:disabled *:first-child.disabled,:host .bci-core-paginator-pages-container *:last-child.disabled{color:#c1c7cc;background-color:transparent;cursor:default}:host .bci-core-paginator-pages-container>*.selected{color:#ffffff;background-color:#007bc0;cursor:default}:host .bci-core-paginator-pages-container>*.selected:hover{background-color:#00629a}:host .bci-core-paginator-pages-container>*.selected:active{background-color:#004975}:host .bci-core-paginator-pages-container>*.selected:disabled,:host .bci-core-paginator-pages-container>*.selected.disabled{background-color:#c1c7cc;color:#8a9097}:host .bci-core-paginator-pages-container>*.wide{width:fit-content;max-width:fit-content;border-radius:25px;padding:0 8px}:host .bci-core-paginator-pages-container>*.ellipsis{color:#000000;cursor:default}:host .bci-core-paginator-pages-container>*.ellipsis:hover{color:#000000;background-color:transparent}:host .bci-core-paginator-pages-container>*.mobile{display:none}:host .mdc-select+.bci-core-paginator-pages-container{height:48px;align-items:center}}@media (max-width: 480px){:host{flex-flow:column nowrap;align-items:center}:host .mdc-select{width:100%}:host .mdc-select div.bci-core-paginator-dropdown{width:100%;margin-bottom:15px}:host div.bci-core-paginator-pages-container{display:flex;flex-flow:row nowrap;width:100%;justify-content:space-between}:host div.bci-core-paginator-pages-container *,:host div.bci-core-paginator-pages-container *:hover,:host div.bci-core-paginator-pages-container *:active,:host div.bci-core-paginator-pages-container *:focus{margin:0;padding:0;line-height:1.5;color:#000000;border:none;background:none;text-decoration:none}:host div.bci-core-paginator-pages-container>*,:host div.bci-core-paginator-pages-container>*:hover,:host div.bci-core-paginator-pages-container>*:active,:host div.bci-core-paginator-pages-container>*:focus{cursor:pointer;font-size:16px;display:inline-block;min-width:32px;width:32px;height:32px;line-height:32px;border-radius:50%;text-align:center;margin-right:8px;color:#000000;background-color:transparent;display:none}:host div.bci-core-paginator-pages-container>*:first-child,:host div.bci-core-paginator-pages-container>*:last-child,:host div.bci-core-paginator-pages-container>*:hover:first-child,:host div.bci-core-paginator-pages-container>*:hover:last-child,:host div.bci-core-paginator-pages-container>*:active:first-child,:host div.bci-core-paginator-pages-container>*:active:last-child,:host div.bci-core-paginator-pages-container>*:focus:first-child,:host div.bci-core-paginator-pages-container>*:focus:last-child{height:24px;line-height:1;width:24px;min-width:24px;color:#000000;background-color:transparent}:host div.bci-core-paginator-pages-container>*:first-child:hover,:host div.bci-core-paginator-pages-container>*:last-child:hover,:host div.bci-core-paginator-pages-container>*:hover:first-child:hover,:host div.bci-core-paginator-pages-container>*:hover:last-child:hover,:host div.bci-core-paginator-pages-container>*:active:first-child:hover,:host div.bci-core-paginator-pages-container>*:active:last-child:hover,:host div.bci-core-paginator-pages-container>*:focus:first-child:hover,:host div.bci-core-paginator-pages-container>*:focus:last-child:hover{color:#007bc0}:host div.bci-core-paginator-pages-container>*:first-child:active,:host div.bci-core-paginator-pages-container>*:last-child:active,:host div.bci-core-paginator-pages-container>*:hover:first-child:active,:host div.bci-core-paginator-pages-container>*:hover:last-child:active,:host div.bci-core-paginator-pages-container>*:active:first-child:active,:host div.bci-core-paginator-pages-container>*:active:last-child:active,:host div.bci-core-paginator-pages-container>*:focus:first-child:active,:host div.bci-core-paginator-pages-container>*:focus:last-child:active{color:#00629a}:host div.bci-core-paginator-pages-container>*:first-child:disabled,:host div.bci-core-paginator-pages-container>*:first-child.disabled,:host div.bci-core-paginator-pages-container>*:last-child:disabled,:host div.bci-core-paginator-pages-container>*:last-child.disabled,:host div.bci-core-paginator-pages-container>*:hover:first-child:disabled,:host div.bci-core-paginator-pages-container>*:hover:first-child.disabled,:host div.bci-core-paginator-pages-container>*:hover:last-child:disabled,:host div.bci-core-paginator-pages-container>*:hover:last-child.disabled,:host div.bci-core-paginator-pages-container>*:active:first-child:disabled,:host div.bci-core-paginator-pages-container>*:active:first-child.disabled,:host div.bci-core-paginator-pages-container>*:active:last-child:disabled,:host div.bci-core-paginator-pages-container>*:active:last-child.disabled,:host div.bci-core-paginator-pages-container>*:focus:first-child:disabled,:host div.bci-core-paginator-pages-container>*:focus:first-child.disabled,:host div.bci-core-paginator-pages-container>*:focus:last-child:disabled,:host div.bci-core-paginator-pages-container>*:focus:last-child.disabled{color:#c1c7cc;cursor:default}:host div.bci-core-paginator-pages-container>*:first-child::after,:host div.bci-core-paginator-pages-container>*:hover:first-child::after,:host div.bci-core-paginator-pages-container>*:active:first-child::after,:host div.bci-core-paginator-pages-container>*:focus:first-child::after{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;content:\\\"\\\\e0a0\\\";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}:host div.bci-core-paginator-pages-container>*:last-child,:host div.bci-core-paginator-pages-container>*:hover:last-child,:host div.bci-core-paginator-pages-container>*:active:last-child,:host div.bci-core-paginator-pages-container>*:focus:last-child{margin-right:0px}:host div.bci-core-paginator-pages-container>*:last-child::after,:host div.bci-core-paginator-pages-container>*:hover:last-child::after,:host div.bci-core-paginator-pages-container>*:active:last-child::after,:host div.bci-core-paginator-pages-container>*:focus:last-child::after{font-family:\\\"Bosch-Ic\\\";font-style:normal;font-weight:normal;font-variant:normal;text-transform:none;font-size:24px;content:\\\"\\\\e181\\\";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}:host div.bci-core-paginator-pages-container>*:disabled,:host div.bci-core-paginator-pages-container *.disabled{cursor:default;color:#c1c7cc;background-color:#ffffff}:host div.bci-core-paginator-pages-container>*:disabled *,:host div.bci-core-paginator-pages-container *.disabled *{cursor:default;color:#c1c7cc;background-color:#ffffff}:host div.bci-core-paginator-pages-container>*.wide{width:fit-content;max-width:fit-content;border-radius:25px;padding:0 8px}:host div.bci-core-paginator-pages-container>*.mobile,:host div.bci-core-paginator-pages-container *:first-child,:host div.bci-core-paginator-pages-container *:last-child{display:inline}}\";\nconst Paginator = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.page = createEvent(this, \"page\", 7);\n    this.pagesNumber = 0;\n    this.previousPageIndex = 0;\n    this.previousPageSize = 0;\n    this.isFirstLoad = true;\n    this.isPageSizeUpdate = false;\n    this.pageSize = 10;\n    this.pageSizeOptions = '[]';\n    this.length = 0;\n    this.pageIndex = 0;\n    this.showPageSizeSelector = false;\n    this.pageIndexCalculationOff = false;\n    this.insideDialogContainer = false;\n    this.paginatorInput = {\n      length: 0,\n      pageIndex: 0,\n      pageSize: 10,\n      pageSizeOptions: [],\n      showPageSizeSelector: false\n    };\n    this.pages = [];\n  }\n  watchLengthHandler(newValue) {\n    this.paginatorInput = Object.assign(Object.assign({}, this.paginatorInput), {\n      length: newValue\n    });\n    this.recalculatePages();\n  }\n  watchPageSizeOptionsHandler(newValue) {\n    this.paginatorInput = Object.assign(Object.assign({}, this.paginatorInput), {\n      pageSizeOptions: typeof newValue === 'string' ? JSON.parse(newValue) : newValue\n    });\n    this.recalculatePages();\n  }\n  watchPageIndex(newValue) {\n    this.paginatorInput = Object.assign(Object.assign({}, this.paginatorInput), {\n      pageIndex: newValue\n    });\n  }\n  watchShowPageSizeSelector(newValue) {\n    this.paginatorInput = Object.assign(Object.assign({}, this.paginatorInput), {\n      showPageSizeSelector: newValue\n    });\n  }\n  watchPageSize(newValue) {\n    this.paginatorInput = Object.assign(Object.assign({}, this.paginatorInput), {\n      pageSize: newValue\n    });\n    this.recalculatePages();\n  }\n  clickHandler(ev) {\n    const currentTarget = ev.composedPath()[0];\n    if (!currentTarget || currentTarget.offsetParent === this.menuElement) {\n      return;\n    }\n    if (this.selectElement !== undefined && this.selectElement.className.includes('mdc-select--activated') && this.mdcMenu !== undefined) {\n      this.mdcMenu.open = false;\n    }\n  }\n  async componentWillLoad() {\n    await setLocale();\n    this.paginatorInput = {\n      length: this.length,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      pageSizeOptions: typeof this.pageSizeOptions === 'string' ? JSON.parse(this.pageSizeOptions) : this.pageSizeOptions,\n      showPageSizeSelector: this.showPageSizeSelector\n    };\n    this.previousPageIndex = this.paginatorInput.pageIndex;\n    this.previousPageSize = this.paginatorInput.pageSize;\n    this.recalculatePages();\n    this.isFirstLoad = false;\n  }\n  componentDidLoad() {\n    if (this.selectElement !== undefined && this.menuElement !== undefined) {\n      this.mdcSelect = new MDCSelect(this.selectElement);\n      this.mdcMenu = new MDCMenu(this.menuElement);\n    }\n  }\n  disconnectedCallback() {\n    if (this.mdcSelect === undefined) {\n      return;\n    }\n    this.mdcSelect.destroy();\n    if (this.mdcMenu === undefined) {\n      return;\n    }\n    this.mdcMenu.destroy();\n  }\n  render() {\n    const hidePageSizeSelector = this.shouldHidePageSizeSelector();\n    const hidePageIndexSelector = this.shouldHidePageIndexSelector();\n    return h(Host, {\n      class: {\n        'hidden-page-size-selector': hidePageSizeSelector,\n        hidden: hidePageSizeSelector && hidePageIndexSelector\n      }\n    }, this.renderPageSizeSelector(hidePageSizeSelector), this.renderPageIndexSelector(hidePageIndexSelector));\n  }\n  shouldHidePageSizeSelector() {\n    return !this.paginatorInput.showPageSizeSelector || this.paginatorInput.pageSizeOptions.length === 0 || this.paginatorInput.length <= Math.min(...this.paginatorInput.pageSizeOptions);\n  }\n  shouldHidePageIndexSelector() {\n    return this.paginatorInput.length <= this.paginatorInput.pageSize;\n  }\n  renderPageIndexSelector(hidden) {\n    return h(\"div\", {\n      role: \"navigation\",\n      \"aria-label\": translate('pagination'),\n      class: {\n        'bci-core-paginator-pages-container': true,\n        hidden: hidden\n      }\n    }, h(\"button\", {\n      onClick: () => this.previousPage(),\n      \"data-test\": \"webcore.webcomponents.paginator.component.bci-paginator.page-prev\",\n      disabled: this.isAtFirstPage(),\n      \"aria-label\": translate('goToPreviousPage'),\n      class: `bci-core-paginator-page-prev ${this.isAtFirstPage() ? 'disabled' : ''}`\n    }), this.positionBasedRender(), this.renderMobilePages(), h(\"button\", {\n      onClick: () => this.nextPage(),\n      \"data-test\": \"webcore.webcomponents.paginator.component.bci-paginator.page-next\",\n      disabled: this.isAtLastPage(),\n      \"aria-label\": translate('goToNextPage'),\n      class: `bci-core-paginator-page-next ${this.isAtLastPage() ? 'disabled' : ''}`\n    }));\n  }\n  updatePageSize(newVal) {\n    this.previousPageSize = this.paginatorInput.pageSize;\n    this.paginatorInput = Object.assign(Object.assign({}, this.paginatorInput), {\n      pageSize: parseFloat(newVal)\n    });\n    this.isPageSizeUpdate = true;\n    this.recalculatePages();\n  }\n  updatePageIndex(newVal) {\n    if (newVal < 0 || newVal > this.pagesNumber - 1) {\n      return;\n    }\n    this.previousPageIndex = this.paginatorInput.pageIndex;\n    this.paginatorInput = Object.assign(Object.assign({}, this.paginatorInput), {\n      pageIndex: newVal\n    });\n    this.positionBasedRender();\n    this.emitPageEvent();\n  }\n  previousPage() {\n    this.updatePageIndex(this.paginatorInput.pageIndex - 1);\n  }\n  nextPage() {\n    this.updatePageIndex(this.paginatorInput.pageIndex + 1);\n  }\n  recalculatePages() {\n    let pageIndex;\n    this.pagesNumber = Math.ceil(this.paginatorInput.length / this.paginatorInput.pageSize);\n    if (!this.pageIndexCalculationOff) {\n      const indexOfFirstItemOnCurrentPage = this.paginatorInput.pageIndex * this.previousPageSize;\n      const newPageIndex = Math.floor(indexOfFirstItemOnCurrentPage / this.paginatorInput.pageSize);\n      pageIndex = newPageIndex < this.pagesNumber ? newPageIndex : this.pagesNumber - 1;\n    } else {\n      pageIndex = this.isPageSizeUpdate ? 0 : this.pageIndex;\n    }\n    this.updatePageIndex(pageIndex);\n    this.pages = [...Array(this.pagesNumber)].map((_, i) => i + 1);\n    this.isPageSizeUpdate = false;\n    this.previousPageSize = this.paginatorInput.pageSize;\n  }\n  isAtFirstPage() {\n    return this.paginatorInput.pageIndex === 0;\n  }\n  isAtLastPage() {\n    return this.paginatorInput.pageIndex === this.pagesNumber - 1;\n  }\n  emitPageEvent() {\n    if (!this.isFirstLoad) {\n      this.page.emit({\n        length: this.paginatorInput.length,\n        pageIndex: this.paginatorInput.pageIndex,\n        pageSize: this.paginatorInput.pageSize,\n        previousPageIndex: this.previousPageIndex\n      });\n    }\n  }\n  getSelectedMenuItemClass(option) {\n    return this.paginatorInput.pageSize === option ? ' mdc-list-item--selected' : '';\n  }\n  getPageButtonClass(index, page) {\n    let buttonClass = 'bci-core-paginator-page-button ';\n    buttonClass += this.paginatorInput.pageIndex === index ? ' selected' : '';\n    buttonClass += page.toString().length > 2 ? ' wide' : '';\n    return buttonClass;\n  }\n  getEllipsesElement() {\n    return h(\"span\", {\n      class: \"ellipsis\"\n    }, \"...\");\n  }\n  getPageButtonElement(page, index) {\n    return h(\"button\", {\n      key: index,\n      onClick: () => this.updatePageIndex(index),\n      \"aria-selected\": this.paginatorInput.pageIndex === index ? 'true' : 'false',\n      \"aria-label\": translate('goToPageX').replace('{page}', page),\n      \"data-test\": \"webcore.webcomponents.paginator.component.bci-paginator.page-button\",\n      class: `${this.getPageButtonClass(index, page)}`\n    }, page);\n  }\n  getPredicateBasedRender(acc, page, index, pagePredicate, ellipsisPredicate) {\n    if (pagePredicate) {\n      return acc.concat(this.getPageButtonElement(page, index));\n    } else if (ellipsisPredicate) {\n      return acc.concat(this.getEllipsesElement());\n    } else {\n      return acc;\n    }\n  }\n  positionBasedRender() {\n    if (this.paginatorInput.pageIndex < 4) {\n      return this.pages.reduce((acc, page, index) => this.getPredicateBasedRender(acc, page, index, index < 5 || index === this.pages.length - 1, index === 5), []);\n    } else if (this.paginatorInput.pageIndex > this.pagesNumber - 5) {\n      return this.pages.reduce((acc, page, index) => this.getPredicateBasedRender(acc, page, index, index === 0 || index > this.pages.length - 6, index === 1), []);\n    } else {\n      return this.pages.reduce((acc, page, index) => this.getPredicateBasedRender(acc, page, index, index === 0 || index === this.pages.length - 1 || index >= this.paginatorInput.pageIndex - 1 && index <= this.paginatorInput.pageIndex + 1, index === 1 || index === this.pages.length - 2), []);\n    }\n  }\n  renderMobilePages() {\n    return h(\"span\", {\n      class: \"mobile\"\n    }, this.paginatorInput.pageIndex + 1, \"/\", this.pagesNumber);\n  }\n  renderPageSizeSelector(hidden) {\n    return h(\"div\", {\n      class: {\n        'mdc-select': true,\n        'mdc-select--filled': true,\n        hidden: hidden\n      },\n      ref: el => this.selectElement = el\n    }, h(\"div\", {\n      class: \"mdc-select__anchor bci-core-paginator-dropdown\",\n      role: \"button\"\n    }, h(\"span\", {\n      class: \"mdc-select__ripple\"\n    }), h(\"span\", {\n      class: \"mdc-floating-label mdc-floating-label--float-above\"\n    }, translate('listItemsPerPage')), h(\"span\", {\n      class: \"mdc-select__selected-text\"\n    }), h(\"span\", {\n      class: \"mdc-select__dropdown-icon bosch-ic bosch-ic-down\"\n    })), h(\"div\", {\n      class: {\n        'mdc-select__menu': true,\n        'mdc-menu': true,\n        'mdc-menu-surface': true,\n        'mdc-menu-surface--fixed': this.insideDialogContainer,\n        'mdc-menu-surface--fullwidth': !this.insideDialogContainer,\n        'bci-core-paginator-dropdown-select': true\n      },\n      ref: el => this.menuElement = el\n    }, h(\"ul\", {\n      class: \"mdc-list\",\n      role: \"listbox\"\n    }, this.paginatorInput.pageSizeOptions.map((option, index) => h(\"li\", {\n      \"data-test\": \"webcore.webcomponents.paginator.component.bci-paginator.pagesize-option\",\n      \"data-value\": option,\n      key: index,\n      \"aria-selected\": \"false\",\n      role: \"option\",\n      onClick: () => this.updatePageSize(option),\n      class: `mdc-list-item ${this.getSelectedMenuItemClass(option)}`\n    }, h(\"span\", {\n      class: \"mdc-list-item__text\"\n    }, option))))));\n  }\n  static get watchers() {\n    return {\n      \"length\": [\"watchLengthHandler\"],\n      \"pageSizeOptions\": [\"watchPageSizeOptionsHandler\"],\n      \"pageIndex\": [\"watchPageIndex\"],\n      \"showPageSizeSelector\": [\"watchShowPageSizeSelector\"],\n      \"pageSize\": [\"watchPageSize\"]\n    };\n  }\n};\nPaginator.style = paginatorComponentCss;\nexport { Paginator as bci_paginator };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,IAAI,eAAe;AAAA,EACjB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AACR;AAwBA,IAAI;AAAA;AAAA,EAA0C,SAAU,QAAQ;AAC9D,cAAUA,6BAA4B,MAAM;AAC5C,aAASA,4BAA2B,SAAS;AAC3C,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,4BAA2B,cAAc,GAAG,OAAO,CAAC,KAAK;AAC7G,YAAM,2BAA2B,WAAY;AAC3C,cAAM,wBAAwB;AAAA,MAChC;AACA,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,6BAA4B,cAAc;AAAA,MAC9D,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,6BAA4B,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAIlE,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,4BAA4B,WAAY;AACtC,mBAAO;AAAA,UACT;AAAA,UACA,8BAA8B,WAAY;AACxC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,4BAA2B,UAAU,OAAO,WAAY;AACtD,WAAK,QAAQ,2BAA2B,gBAAgB,KAAK,wBAAwB;AAAA,IACvF;AACA,IAAAA,4BAA2B,UAAU,UAAU,WAAY;AACzD,WAAK,QAAQ,6BAA6B,gBAAgB,KAAK,wBAAwB;AAAA,IACzF;AAIA,IAAAA,4BAA2B,UAAU,WAAW,WAAY;AAC1D,aAAO,KAAK,QAAQ,SAAS;AAAA,IAC/B;AAKA,IAAAA,4BAA2B,UAAU,QAAQ,SAAU,aAAa;AAClE,UAAI,cAAcA,4BAA2B,WAAW;AACxD,UAAI,aAAa;AACf,aAAK,QAAQ,SAAS,WAAW;AAAA,MACnC,OAAO;AACL,aAAK,QAAQ,YAAY,WAAW;AAAA,MACtC;AAAA,IACF;AAKA,IAAAA,4BAA2B,UAAU,QAAQ,SAAU,aAAa;AAClE,UAAI,KAAKA,4BAA2B,YAClC,oBAAoB,GAAG,mBACvB,cAAc,GAAG;AACnB,UAAI,aAAa;AACf,aAAK,QAAQ,SAAS,iBAAiB;AAAA,MACzC,OAAO;AACL,aAAK,QAAQ,YAAY,iBAAiB;AAC1C,aAAK,QAAQ,YAAY,WAAW;AAAA,MACtC;AAAA,IACF;AAKA,IAAAA,4BAA2B,UAAU,cAAc,SAAU,YAAY;AACvE,UAAI,iBAAiBA,4BAA2B,WAAW;AAC3D,UAAI,YAAY;AACd,aAAK,QAAQ,SAAS,cAAc;AAAA,MACtC,OAAO;AACL,aAAK,QAAQ,YAAY,cAAc;AAAA,MACzC;AAAA,IACF;AACA,IAAAA,4BAA2B,UAAU,0BAA0B,WAAY;AACzE,UAAI,cAAcA,4BAA2B,WAAW;AACxD,WAAK,QAAQ,YAAY,WAAW;AAAA,IACtC;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAAgC,SAAU,QAAQ;AACpD,cAAUC,mBAAkB,MAAM;AAClC,aAASA,oBAAmB;AAC1B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,kBAAiB,WAAW,SAAU,MAAM;AAC1C,aAAO,IAAIA,kBAAiB,IAAI;AAAA,IAClC;AAKA,IAAAA,kBAAiB,UAAU,QAAQ,SAAU,aAAa;AACxD,WAAK,WAAW,MAAM,WAAW;AAAA,IACnC;AAKA,IAAAA,kBAAiB,UAAU,QAAQ,SAAU,aAAa;AACxD,WAAK,WAAW,MAAM,WAAW;AAAA,IACnC;AAKA,IAAAA,kBAAiB,UAAU,cAAc,SAAU,YAAY;AAC7D,WAAK,WAAW,YAAY,UAAU;AAAA,IACxC;AACA,IAAAA,kBAAiB,UAAU,WAAW,WAAY;AAChD,aAAO,KAAK,WAAW,SAAS;AAAA,IAClC;AACA,IAAAA,kBAAiB,UAAU,uBAAuB,WAAY;AAC5D,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,iBAAO,MAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QAC9C;AAAA,QACA,UAAU,WAAY;AACpB,iBAAO,oBAAoB,MAAM,IAAI;AAAA,QACvC;AAAA,QACA,4BAA4B,SAAU,SAAS,SAAS;AACtD,iBAAO,MAAM,OAAO,SAAS,OAAO;AAAA,QACtC;AAAA,QACA,8BAA8B,SAAU,SAAS,SAAS;AACxD,iBAAO,MAAM,SAAS,SAAS,OAAO;AAAA,QACxC;AAAA,MACF;AAEA,aAAO,IAAI,2BAA2B,OAAO;AAAA,IAC/C;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AAwBd,IAAI,eAAe;AAAA,EACjB,oBAAoB;AAAA,EACpB,0BAA0B;AAC5B;AAwBA,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AAC3D,cAAUC,0BAAyB,MAAM;AACzC,aAASA,yBAAwB,SAAS;AACxC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,yBAAwB,cAAc,GAAG,OAAO,CAAC,KAAK;AAC1G,YAAM,uBAAuB,SAAU,KAAK;AAC1C,cAAM,oBAAoB,GAAG;AAAA,MAC/B;AACA,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,0BAAyB,cAAc;AAAA,MAC3D,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,0BAAyB,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAI/D,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,sBAAsB,WAAY;AAChC,mBAAO;AAAA,UACT;AAAA,UACA,wBAAwB,WAAY;AAClC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,yBAAwB,UAAU,OAAO,WAAY;AACnD,WAAK,QAAQ,qBAAqB,iBAAiB,KAAK,oBAAoB;AAAA,IAC9E;AACA,IAAAA,yBAAwB,UAAU,UAAU,WAAY;AACtD,WAAK,QAAQ,uBAAuB,iBAAiB,KAAK,oBAAoB;AAAA,IAChF;AACA,IAAAA,yBAAwB,UAAU,WAAW,WAAY;AACvD,WAAK,QAAQ,YAAY,aAAa,wBAAwB;AAC9D,WAAK,QAAQ,SAAS,aAAa,kBAAkB;AAAA,IACvD;AACA,IAAAA,yBAAwB,UAAU,kBAAkB,SAAU,aAAa;AACzE,WAAK,QAAQ,SAAS,oBAAoB,cAAc,WAAW;AAAA,IACrE;AACA,IAAAA,yBAAwB,UAAU,aAAa,WAAY;AACzD,WAAK,QAAQ,SAAS,aAAa,wBAAwB;AAAA,IAC7D;AACA,IAAAA,yBAAwB,UAAU,sBAAsB,SAAU,KAAK;AAGrE,UAAI,iBAAiB,KAAK,QAAQ,SAAS,aAAa,wBAAwB;AAChF,UAAI,IAAI,iBAAiB,WAAW;AAClC,YAAI,gBAAgB;AAClB,eAAK,QAAQ,YAAY,aAAa,kBAAkB;AACxD,eAAK,QAAQ,YAAY,aAAa,wBAAwB;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACvB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,eAAc,WAAW,SAAU,MAAM;AACvC,aAAO,IAAIA,eAAc,IAAI;AAAA,IAC/B;AAIA,IAAAA,eAAc,UAAU,WAAW,WAAY;AAC7C,WAAK,WAAW,SAAS;AAAA,IAC3B;AAIA,IAAAA,eAAc,UAAU,aAAa,WAAY;AAC/C,WAAK,WAAW,WAAW;AAAA,IAC7B;AAKA,IAAAA,eAAc,UAAU,kBAAkB,SAAU,aAAa;AAC/D,WAAK,WAAW,gBAAgB,WAAW;AAAA,IAC7C;AACA,IAAAA,eAAc,UAAU,uBAAuB,WAAY;AACzD,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,iBAAO,MAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QAC9C;AAAA,QACA,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,SAAS,SAAS;AAAA,QAChD;AAAA,QACA,UAAU,SAAU,cAAc,OAAO;AACvC,iBAAO,MAAM,KAAK,MAAM,YAAY,cAAc,KAAK;AAAA,QACzD;AAAA,QACA,sBAAsB,SAAU,SAAS,SAAS;AAChD,iBAAO,MAAM,OAAO,SAAS,OAAO;AAAA,QACtC;AAAA,QACA,wBAAwB,SAAU,SAAS,SAAS;AAClD,iBAAO,MAAM,SAAS,SAAS,OAAO;AAAA,QACxC;AAAA,MACF;AAEA,aAAO,IAAI,wBAAwB,OAAO;AAAA,IAC5C;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AAwBd,IAAI,YAAY;AAAA,EACd,wBAAwB;AAC1B;AACA,IAAI,YAAY;AAAA;AAAA,EAEd,uBAAuB;AACzB;AACA,IAAI,eAAe;AAAA,EACjB,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,kBAAkB;AACpB;AAwBA,IAAI;AAAA;AAAA,EAA2C,SAAU,QAAQ;AAC/D,cAAUC,8BAA6B,MAAM;AAC7C,aAASA,6BAA4B,SAAS;AAC5C,aAAO,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,6BAA4B,cAAc,GAAG,OAAO,CAAC,KAAK;AAAA,IAC3G;AACA,WAAO,eAAeA,8BAA6B,WAAW;AAAA,MAC5D,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,8BAA6B,cAAc;AAAA,MAC/D,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,8BAA6B,WAAW;AAAA,MAC5D,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,8BAA6B,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAInE,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,uBAAuB,WAAY;AACjC,mBAAO;AAAA,UACT;AAAA,UACA,0BAA0B,WAAY;AACpC,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AAID,IAAAA,6BAA4B,UAAU,QAAQ,SAAU,YAAY;AAClE,UAAI,kBAAkBA,6BAA4B,WAAW;AAC7D,UAAI,aAAa,GAAG;AAClB,sBAAc,UAAU;AAAA,MAC1B;AACA,WAAK,QAAQ,sBAAsB,UAAU;AAC7C,WAAK,QAAQ,SAAS,eAAe;AAAA,IACvC;AAIA,IAAAA,6BAA4B,UAAU,aAAa,WAAY;AAC7D,UAAI,kBAAkBA,6BAA4B,WAAW;AAC7D,WAAK,QAAQ,YAAY,eAAe;AACxC,WAAK,QAAQ,yBAAyB;AAAA,IACxC;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,qBAAoB;AAC3B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,mBAAkB,WAAW,SAAU,MAAM;AAC3C,aAAO,IAAIA,mBAAkB,IAAI;AAAA,IACnC;AACA,IAAAA,mBAAkB,UAAU,qBAAqB,WAAY;AAC3D,WAAK,eAAe,KAAK,KAAK,cAAc,UAAU,sBAAsB;AAC5E,UAAI,QAAQ,KAAK,KAAK,cAAc,MAAM,2BAA2B,WAAW,IAAI;AACpF,UAAI,OAAO;AACT,cAAM,MAAM,qBAAqB;AACjC,aAAK,KAAK,UAAU,IAAI,aAAa,gBAAgB;AACrD,8BAAsB,WAAY;AAChC,gBAAM,MAAM,qBAAqB;AAAA,QACnC,CAAC;AAAA,MACH,OAAO;AACL,aAAK,KAAK,UAAU,IAAI,aAAa,QAAQ;AAAA,MAC/C;AAAA,IACF;AAKA,IAAAA,mBAAkB,UAAU,QAAQ,SAAU,YAAY;AACxD,WAAK,WAAW,MAAM,UAAU;AAAA,IAClC;AAIA,IAAAA,mBAAkB,UAAU,aAAa,WAAY;AACnD,WAAK,WAAW,WAAW;AAAA,IAC7B;AACA,IAAAA,mBAAkB,UAAU,uBAAuB,WAAY;AAC7D,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,iBAAO,MAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QAC9C;AAAA,QACA,uBAAuB,SAAU,OAAO;AACtC,gBAAM,aAAa,MAAM,YAAY,SAAS,QAAQ,IAAI;AAAA,QAC5D;AAAA,QACA,0BAA0B,WAAY;AACpC,gBAAM,aAAa,MAAM,eAAe,OAAO;AAAA,QACjD;AAAA,MACF;AAEA,aAAO,IAAI,4BAA4B,OAAO;AAAA,IAChD;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AAwBd,IAAI,eAAe;AAAA,EACjB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,MAAM;AAAA,EACN,mBAAmB;AACrB;AACA,IAAI,YAAY;AAAA,EACd,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,cAAc;AAAA,EACd,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,YAAY;AACd;AACA,IAAI,UAAU;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,2BAA2B;AAC7B;AAwBA,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACvD,cAAUC,sBAAqB,MAAM;AAMrC,aAASA,qBAAoB,SAAS,eAAe;AACnD,UAAI,kBAAkB,QAAQ;AAC5B,wBAAgB,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,qBAAoB,cAAc,GAAG,OAAO,CAAC,KAAK;AAEtG,YAAM,WAAW;AAKjB,YAAM,aAAa;AAEnB,YAAM,uBAAuB;AAC7B,YAAM,iBAAiB;AACvB,YAAM,oBAAoB,QAAQ;AAClC,YAAM,uBAAuB;AAC7B,YAAM,kBAAkB;AACxB,YAAM,cAAc,cAAc;AAClC,YAAM,aAAa,cAAc;AACjC,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,sBAAqB,cAAc;AAAA,MACvD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,WAAW;AAAA,MACpD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,WAAW;AAAA,MACpD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,sBAAqB,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAI3D,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,UACT;AAAA,UACA,sBAAsB,WAAY;AAChC,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAY;AACtB,mBAAO;AAAA,UACT;AAAA,UACA,eAAe,WAAY;AACzB,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAY;AACtB,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,uBAAuB,WAAY;AACjC,mBAAO;AAAA,UACT;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,UACT;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,UACT;AAAA,UACA,wBAAwB,WAAY;AAClC,mBAAO;AAAA,UACT;AAAA,UACA,cAAc,WAAY;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,iBAAiB,WAAY;AAC3B,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,WAAW,WAAY;AACrB,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,sBAAsB,WAAY;AAChC,mBAAO;AAAA,UACT;AAAA,UACA,qBAAqB,WAAY;AAC/B,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,sBAAsB,WAAY;AAChC,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,UACA,mBAAmB,WAAY;AAC7B,mBAAO,CAAC;AAAA,UACV;AAAA,UACA,wBAAwB,WAAY;AAClC,mBAAO;AAAA,UACT;AAAA,UACA,uBAAuB,WAAY;AACjC,mBAAO;AAAA,UACT;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AAED,IAAAA,qBAAoB,UAAU,mBAAmB,WAAY;AAC3D,aAAO,KAAK,QAAQ,iBAAiB;AAAA,IACvC;AACA,IAAAA,qBAAoB,UAAU,mBAAmB,SAAU,OAAO,WAAW,YAAY;AACvF,UAAI,cAAc,QAAQ;AACxB,oBAAY;AAAA,MACd;AACA,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,UAAI,SAAS,KAAK,QAAQ,iBAAiB,GAAG;AAC5C;AAAA,MACF;AACA,UAAI,UAAU,QAAQ,aAAa;AACjC,aAAK,QAAQ,gBAAgB,EAAE;AAAA,MACjC,OAAO;AACL,aAAK,QAAQ,gBAAgB,KAAK,QAAQ,uBAAuB,KAAK,EAAE,KAAK,CAAC;AAAA,MAChF;AACA,WAAK,QAAQ,iBAAiB,KAAK;AACnC,UAAI,WAAW;AACb,aAAK,QAAQ,UAAU;AAAA,MACzB;AACA,UAAI,CAAC,cAAc,KAAK,sBAAsB,OAAO;AACnD,aAAK,aAAa;AAAA,MACpB;AACA,WAAK,oBAAoB;AAAA,IAC3B;AACA,IAAAA,qBAAoB,UAAU,WAAW,SAAU,OAAO,YAAY;AACpE,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,UAAI,QAAQ,KAAK,QAAQ,kBAAkB,EAAE,QAAQ,KAAK;AAC1D,WAAK;AAAA,QAAiB;AAAA;AAAA,QAAuB;AAAA,QAAO;AAAA,MAAU;AAAA,IAChE;AACA,IAAAA,qBAAoB,UAAU,WAAW,WAAY;AACnD,UAAI,QAAQ,KAAK,QAAQ,iBAAiB;AAC1C,UAAI,iBAAiB,KAAK,QAAQ,kBAAkB;AACpD,aAAO,UAAU,QAAQ,cAAc,eAAe,KAAK,IAAI;AAAA,IACjE;AACA,IAAAA,qBAAoB,UAAU,cAAc,WAAY;AACtD,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,cAAc,SAAU,YAAY;AAChE,WAAK,WAAW;AAChB,UAAI,KAAK,UAAU;AACjB,aAAK,QAAQ,SAAS,aAAa,QAAQ;AAC3C,aAAK,QAAQ,UAAU;AAAA,MACzB,OAAO;AACL,aAAK,QAAQ,YAAY,aAAa,QAAQ;AAAA,MAChD;AACA,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,YAAY,KAAK,QAAQ;AAAA,MAC5C;AACA,UAAI,KAAK,UAAU;AAGjB,aAAK,QAAQ,uBAAuB,UAAU;AAAA,MAChD,OAAO;AACL,aAAK,QAAQ,oBAAoB,YAAY,GAAG;AAAA,MAClD;AACA,WAAK,QAAQ,oBAAoB,iBAAiB,KAAK,SAAS,SAAS,CAAC;AAAA,IAC5E;AAEA,IAAAA,qBAAoB,UAAU,WAAW,WAAY;AACnD,WAAK,QAAQ,SAAS,aAAa,SAAS;AAC5C,WAAK,QAAQ,SAAS;AACtB,WAAK,aAAa;AAClB,WAAK,QAAQ,oBAAoB,iBAAiB,MAAM;AAAA,IAC1D;AAIA,IAAAA,qBAAoB,UAAU,uBAAuB,SAAU,SAAS;AACtE,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,WAAW,OAAO;AAAA,MACpC;AAAA,IACF;AAKA,IAAAA,qBAAoB,UAAU,SAAS,WAAY;AACjD,UAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,YAAI,iBAAiB,KAAK,SAAS,EAAE,SAAS;AAC9C,YAAI,YAAY,KAAK,QAAQ,SAAS,aAAa,OAAO;AAC1D,YAAI,sBAAsB,kBAAkB;AAC5C,YAAI,aAAa,KAAK,QAAQ,SAAS,aAAa,QAAQ;AAC5D,aAAK,aAAa,mBAAmB;AACrC,aAAK,QAAQ,WAAW,mBAAmB;AAC3C,aAAK,QAAQ,iBAAiB,UAAU;AAAA,MAC1C;AAAA,IACF;AAKA,IAAAA,qBAAoB,UAAU,gBAAgB,WAAY;AACxD,UAAI,iBAAiB,KAAK,QAAQ,kBAAkB;AACpD,UAAI,gBAAgB,eAAe,QAAQ,KAAK,SAAS,CAAC;AAC1D,WAAK;AAAA,QAAiB;AAAA;AAAA,QAA+B;AAAA;AAAA,QAAwB;AAAA,MAAI;AAAA,IACnF;AACA,IAAAA,qBAAoB,UAAU,mBAAmB,WAAY;AAC3D,UAAI,KAAK,QAAQ,kBAAkB,EAAE,WAAW,GAAG;AACjD;AAAA,MACF;AAEA,UAAI,gBAAgB,KAAK,iBAAiB;AAC1C,UAAI,iBAAiB,iBAAiB,IAAI,gBAAgB;AAC1D,WAAK,QAAQ,qBAAqB,cAAc;AAAA,IAClD;AACA,IAAAA,qBAAoB,UAAU,oBAAoB,WAAY;AAC5D,WAAK,QAAQ,oBAAoB,iBAAiB,OAAO;AAAA,IAC3D;AACA,IAAAA,qBAAoB,UAAU,mBAAmB,WAAY;AAC3D,WAAK,QAAQ,YAAY,aAAa,SAAS;AAC/C,WAAK,aAAa;AAElB,UAAI,CAAC,KAAK,QAAQ,sBAAsB,GAAG;AACzC,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAIA,IAAAA,qBAAoB,UAAU,eAAe,WAAY;AACvD,WAAK,OAAO;AACZ,WAAK,QAAQ,aAAa,KAAK,SAAS,CAAC;AACzC,UAAI,aAAa,KAAK,QAAQ,SAAS,aAAa,QAAQ;AAC5D,UAAI,cAAc,KAAK,sBAAsB;AAC3C,aAAK,SAAS,KAAK,QAAQ,CAAC;AAAA,MAC9B;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,uBAAuB,SAAU,OAAO;AACpE,WAAK;AAAA,QAAiB;AAAA;AAAA,QAAuB;AAAA,MAAI;AAAA,IACnD;AAIA,IAAAA,qBAAoB,UAAU,cAAc,WAAY;AACtD,WAAK,QAAQ,SAAS,aAAa,OAAO;AAC1C,WAAK,OAAO;AACZ,WAAK,QAAQ,mBAAmB;AAAA,IAClC;AAIA,IAAAA,qBAAoB,UAAU,aAAa,WAAY;AACrD,UAAI,KAAK,YAAY;AACnB;AAAA,MACF;AACA,WAAK,KAAK;AAAA,IACZ;AACA,IAAAA,qBAAoB,UAAU,cAAc,SAAU,aAAa;AACjE,UAAI,KAAK,YAAY,KAAK,iBAAiB;AACzC;AAAA,MACF;AACA,WAAK,wBAAwB;AAC7B,UAAI,KAAK,YAAY;AACnB,aAAK,QAAQ,UAAU;AACvB;AAAA,MACF;AACA,WAAK,QAAQ,gBAAgB,WAAW;AACxC,WAAK,SAAS;AAAA,IAChB;AAKA,IAAAA,qBAAoB,UAAU,gBAAgB,SAAU,OAAO;AAC7D,UAAI,KAAK,cAAc,CAAC,KAAK,QAAQ,SAAS,aAAa,OAAO,GAAG;AACnE;AAAA,MACF;AACA,UAAI,UAAU,aAAa,KAAK,MAAM,IAAI;AAC1C,UAAI,UAAU,aAAa,KAAK,MAAM,IAAI;AAC1C,UAAI,UAAU,aAAa,KAAK,MAAM,IAAI;AAC1C,UAAI,YAAY,aAAa,KAAK,MAAM,IAAI;AAC5C,UAAI,aAAa,MAAM,WAAW,MAAM;AAExC,UAAI,CAAC,eAAe,CAAC,WAAW,MAAM,OAAO,MAAM,IAAI,WAAW,KAAK,WAAW,KAAK,QAAQ,sBAAsB,IAAI;AACvH,YAAI,MAAM,UAAU,MAAM,MAAM;AAChC,YAAI,qBAAqB,KAAK,QAAQ,mBAAmB,KAAK,KAAK,iBAAiB,CAAC;AACrF,YAAI,sBAAsB,GAAG;AAC3B,eAAK,iBAAiB,kBAAkB;AAAA,QAC1C;AACA,cAAM,eAAe;AACrB;AAAA,MACF;AACA,UAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW;AAClD;AAAA,MACF;AACA,WAAK,SAAS;AACd,YAAM,eAAe;AAAA,IACvB;AAIA,IAAAA,qBAAoB,UAAU,eAAe,SAAU,WAAW;AAChE,UAAI,CAAC,KAAK,QAAQ,WAAW,GAAG;AAC9B;AAAA,MACF;AACA,UAAI,YAAY,KAAK,QAAQ,SAAS,aAAa,OAAO;AAC1D,UAAI,WAAW;AACb,YAAI,aAAa,QAAQ;AACzB,YAAI,aAAa,KAAK,QAAQ,cAAc,IAAI;AAChD,aAAK,QAAQ,aAAa,UAAU;AAAA,MACtC,WAAW,CAAC,WAAW;AACrB,aAAK,QAAQ,aAAa;AAAA,MAC5B;AAAA,IACF;AAIA,IAAAA,qBAAoB,UAAU,0BAA0B,SAAU,OAAO;AACvE,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,aAAa,KAAK;AAAA,MACrC;AAAA,IACF;AAIA,IAAAA,qBAAoB,UAAU,wBAAwB,SAAU,SAAS;AACvE,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,WAAW,OAAO;AAAA,MACrC;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,0BAA0B,WAAY;AAClE,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,0BAA0B,SAAU,sBAAsB;AACtF,WAAK,uBAAuB;AAAA,IAC9B;AACA,IAAAA,qBAAoB,UAAU,WAAW,SAAU,SAAS;AAC1D,UAAI,CAAC,KAAK,sBAAsB;AAC9B,aAAK,iBAAiB;AAAA,MACxB;AACA,WAAK,QAAQ,oBAAoB,iBAAiB,CAAC,SAAS,SAAS,CAAC;AACtE,UAAI,SAAS;AACX,aAAK,QAAQ,YAAY,aAAa,OAAO;AAC7C,aAAK,QAAQ,gBAAgB,aAAa,YAAY;AAAA,MACxD,OAAO;AACL,aAAK,QAAQ,SAAS,aAAa,OAAO;AAC1C,aAAK,QAAQ,aAAa,aAAa,YAAY;AAAA,MACrD;AACA,WAAK,uBAAuB,OAAO;AAAA,IACrC;AACA,IAAAA,qBAAoB,UAAU,UAAU,WAAY;AAClD,UAAI,KAAK,wBAAwB,KAAK,QAAQ,SAAS,aAAa,QAAQ,KAAK,CAAC,KAAK,QAAQ,SAAS,aAAa,QAAQ,GAAG;AAG9H,eAAO,KAAK,iBAAiB,MAAM,QAAQ,gBAAgB,KAAK,iBAAiB,MAAM,KAAK,QAAQ,KAAK,SAAS,CAAC;AAAA,MACrH;AACA,aAAO,KAAK;AAAA,IACd;AACA,IAAAA,qBAAoB,UAAU,cAAc,SAAU,YAAY;AAChE,UAAI,YAAY;AACd,aAAK,QAAQ,SAAS,aAAa,QAAQ;AAAA,MAC7C,OAAO;AACL,aAAK,QAAQ,YAAY,aAAa,QAAQ;AAAA,MAChD;AACA,WAAK,QAAQ,oBAAoB,iBAAiB,WAAW,SAAS,CAAC;AACvE,WAAK,QAAQ,iBAAiB,UAAU;AAAA,IAC1C;AACA,IAAAA,qBAAoB,UAAU,cAAc,WAAY;AACtD,aAAO,KAAK,QAAQ,oBAAoB,eAAe,MAAM;AAAA,IAC/D;AACA,IAAAA,qBAAoB,UAAU,OAAO,WAAY;AAC/C,UAAI,WAAW,KAAK,QAAQ,iBAAiB;AAC7C,UAAI,UAAU;AACZ,aAAK,QAAQ,qBAAqB,QAAQ;AAC1C,aAAK,QAAQ,oBAAoB,OAAO,YAAY;AAAA,MACtD;AACA,WAAK,QAAQ,iBAAiB,KAAK;AACnC,WAAK,YAAY,KAAK,QAAQ,SAAS,aAAa,QAAQ,CAAC;AAC7D,WAAK,uBAAuB,CAAC,KAAK,QAAQ,SAAS,aAAa,OAAO,CAAC;AACxE,WAAK,OAAO;AACZ,WAAK,cAAc;AAAA,IACrB;AAIA,IAAAA,qBAAoB,UAAU,OAAO,WAAY;AAC/C,WAAK,QAAQ,YAAY,aAAa,OAAO;AAC7C,WAAK,OAAO;AACZ,WAAK,QAAQ,qBAAqB;AAClC,UAAI,aAAa,KAAK,QAAQ,SAAS,aAAa,QAAQ;AAC5D,UAAI,cAAc,KAAK,sBAAsB;AAC3C,aAAK,SAAS,KAAK,QAAQ,CAAC;AAAA,MAC9B;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,yBAAyB,SAAU,SAAS;AACxE,UAAI,CAAC,KAAK,YAAY;AACpB;AAAA,MACF;AACA,WAAK,WAAW,YAAY,OAAO;AACnC,UAAI,oBAAoB,KAAK,WAAW,UAAU;AAClD,UAAI,eAAe,KAAK,WAAW,MAAM;AACzC,UAAI,qBAAqB,cAAc;AACrC,aAAK,QAAQ,oBAAoB,UAAU,kBAAkB,YAAY;AAAA,MAC3E,OAAO;AAGL,aAAK,QAAQ,uBAAuB,UAAU,gBAAgB;AAAA,MAChE;AAAA,IACF;AACA,IAAAA,qBAAoB,UAAU,0BAA0B,WAAY;AAClE,UAAI,QAAQ;AACZ,mBAAa,KAAK,oBAAoB;AACtC,WAAK,uBAAuB,WAAW,WAAY;AACjD,cAAM,kBAAkB;AAAA,MAC1B,GAAG,QAAQ,yBAAyB;AACpC,WAAK,kBAAkB;AAAA,IACzB;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAIC,aAAY;AAAA,EACd,aAAa;AAAA,EACb,MAAM;AACR;AACA,IAAI,aAAa;AAAA,EACf,4BAA4B;AAAA,EAC5B,uCAAuC;AACzC;AAwBA,IAAI;AAAA;AAAA,EAA6C,SAAU,QAAQ;AACjE,cAAUC,gCAA+B,MAAM;AAC/C,aAASA,+BAA8B,SAAS;AAC9C,aAAO,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,+BAA8B,cAAc,GAAG,OAAO,CAAC,KAAK;AAAA,IAC7G;AACA,WAAO,eAAeA,gCAA+B,cAAc;AAAA,MACjE,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,gCAA+B,WAAW;AAAA,MAC9D,KAAK,WAAY;AACf,eAAOD;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeC,gCAA+B,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAIrE,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,aAAa,WAAY;AACvB,mBAAO;AAAA,UACT;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO;AAAA,UACT;AAAA,UACA,SAAS,WAAY;AACnB,mBAAO;AAAA,UACT;AAAA,UACA,SAAS,WAAY;AACnB,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAY;AACtB,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAY;AACtB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AAID,IAAAA,+BAA8B,UAAU,QAAQ,WAAY;AAC1D,aAAO,KAAK,QAAQ,QAAQ,IAAI;AAAA,IAClC;AAIA,IAAAA,+BAA8B,UAAU,YAAY,WAAY;AAC9D,aAAO,KAAK,QAAQ,QAAQD,WAAU,WAAW,MAAM;AAAA,IACzD;AAIA,IAAAC,+BAA8B,UAAU,aAAa,SAAU,SAAS;AACtE,WAAK,QAAQ,WAAW,OAAO;AAAA,IACjC;AASA,IAAAA,+BAA8B,UAAU,gBAAgB,SAAU,cAAc;AAC9E,UAAI,cAAc;AAChB,aAAK,QAAQ,SAAS,WAAW,0BAA0B;AAAA,MAC7D,OAAO;AACL,aAAK,QAAQ,YAAY,WAAW,0BAA0B;AAAA,MAChE;AAAA,IACF;AAMA,IAAAA,+BAA8B,UAAU,6BAA6B,SAAU,cAAc;AAC3F,UAAI,cAAc;AAChB,aAAK,QAAQ,SAAS,WAAW,qCAAqC;AAAA,MACxE,OAAO;AACL,aAAK,QAAQ,YAAY,WAAW,qCAAqC;AAAA,MAC3E;AAAA,IACF;AAMA,IAAAA,+BAA8B,UAAU,kBAAkB,WAAY;AACpE,aAAO,KAAK,QAAQ,SAAS,WAAW,0BAA0B;AAAA,IACpE;AAKA,IAAAA,+BAA8B,UAAU,+BAA+B,WAAY;AACjF,aAAO,KAAK,QAAQ,SAAS,WAAW,qCAAqC;AAAA,IAC/E;AAKA,IAAAA,+BAA8B,UAAU,cAAc,SAAU,eAAe;AAC7E,UAAI,kBAAkB,KAAK,QAAQ,SAAS,WAAW,0BAA0B;AACjF,UAAI,CAAC,iBAAiB;AAGpB;AAAA,MACF;AACA,UAAI,4BAA4B,KAAK,QAAQ,SAAS,WAAW,qCAAqC;AAGtG,UAAI,mBAAmB,CAAC,iBAAiB;AACzC,UAAI,kBAAkB;AACpB,aAAK,mBAAmB;AAGxB,YAAI,CAAC,eAAe;AAClB,eAAK,QAAQ,QAAQD,WAAU,MAAM,OAAO;AAAA,QAC9C,OAAO;AACL,eAAK,QAAQ,WAAWA,WAAU,IAAI;AAAA,QACxC;AACA;AAAA,MACF;AAEA,WAAK,QAAQ,WAAWA,WAAU,IAAI;AACtC,WAAK,KAAK;AAAA,IACZ;AAIA,IAAAC,+BAA8B,UAAU,qBAAqB,WAAY;AACvE,WAAK,QAAQ,WAAWD,WAAU,WAAW;AAAA,IAC/C;AAIA,IAAAC,+BAA8B,UAAU,OAAO,WAAY;AACzD,WAAK,QAAQ,QAAQD,WAAU,aAAa,MAAM;AAAA,IACpD;AACA,WAAOC;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACvD,cAAUC,sBAAqB,MAAM;AACrC,aAASA,uBAAsB;AAC7B,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,qBAAoB,WAAW,SAAU,MAAM;AAC7C,aAAO,IAAIA,qBAAoB,IAAI;AAAA,IACrC;AACA,WAAO,eAAeA,qBAAoB,WAAW,uBAAuB;AAAA;AAAA,MAE1E,KAAK,WAAY;AACf,eAAO,KAAK;AAAA,MACd;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,qBAAoB,UAAU,uBAAuB,WAAY;AAC/D,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,iBAAO,MAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QAC9C;AAAA,QACA,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,SAAS,SAAS;AAAA,QAChD;AAAA,QACA,SAAS,SAAU,MAAM;AACvB,iBAAO,MAAM,KAAK,aAAa,IAAI;AAAA,QACrC;AAAA,QACA,SAAS,SAAU,MAAM,OAAO;AAC9B,iBAAO,MAAM,KAAK,aAAa,MAAM,KAAK;AAAA,QAC5C;AAAA,QACA,YAAY,SAAU,MAAM;AAC1B,iBAAO,MAAM,KAAK,gBAAgB,IAAI;AAAA,QACxC;AAAA,QACA,YAAY,SAAU,SAAS;AAC7B,gBAAM,KAAK,cAAc;AAAA,QAC3B;AAAA,MACF;AAEA,aAAO,IAAI,8BAA8B,OAAO;AAAA,IAClD;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AAwBd,IAAIC,WAAU;AAAA,EACZ,YAAY;AAAA,EACZ,WAAW;AACb;AAwBA,IAAI,qBAAqB,CAAC,SAAS,SAAS;AAC5C,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AAC3D,cAAUC,0BAAyB,MAAM;AACzC,aAASA,yBAAwB,SAAS;AACxC,UAAI,QAAQ,OAAO,KAAK,MAAM,SAAS,SAAS,CAAC,GAAGA,yBAAwB,cAAc,GAAG,OAAO,CAAC,KAAK;AAC1G,YAAM,gBAAgB;AACtB,YAAM,qBAAqB,SAAU,KAAK;AACxC,cAAM,kBAAkB,GAAG;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AACA,WAAO,eAAeA,0BAAyB,WAAW;AAAA,MACxD,KAAK,WAAY;AACf,eAAOD;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeC,0BAAyB,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAI/D,KAAK,WAAY;AAEf,eAAO;AAAA,UACL,SAAS,WAAY;AACnB,mBAAO;AAAA,UACT;AAAA,UACA,SAAS,WAAY;AACnB,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAY;AACtB,mBAAO;AAAA,UACT;AAAA,UACA,YAAY,WAAY;AACtB,mBAAO;AAAA,UACT;AAAA,UACA,4BAA4B,WAAY;AACtC,mBAAO;AAAA,UACT;AAAA,UACA,8BAA8B,WAAY;AACxC,mBAAO;AAAA,UACT;AAAA,UACA,kBAAkB,WAAY;AAC5B,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MAEF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,yBAAwB,UAAU,OAAO,WAAY;AACnD,UAAI,KAAK;AACT,WAAK,gBAAgB,KAAK,QAAQ,QAAQ,UAAU;AACpD,UAAI;AACF,iBAAS,uBAAuB,SAAS,kBAAkB,GAAG,yBAAyB,qBAAqB,KAAK,GAAG,CAAC,uBAAuB,MAAM,yBAAyB,qBAAqB,KAAK,GAAG;AACtM,cAAI,UAAU,uBAAuB;AACrC,eAAK,QAAQ,2BAA2B,SAAS,KAAK,kBAAkB;AAAA,QAC1E;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,0BAA0B,CAAC,uBAAuB,SAAS,KAAK,qBAAqB,QAAS,IAAG,KAAK,oBAAoB;AAAA,QAChI,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,IAAAA,yBAAwB,UAAU,UAAU,WAAY;AACtD,UAAI,KAAK;AACT,UAAI;AACF,iBAAS,uBAAuB,SAAS,kBAAkB,GAAG,yBAAyB,qBAAqB,KAAK,GAAG,CAAC,uBAAuB,MAAM,yBAAyB,qBAAqB,KAAK,GAAG;AACtM,cAAI,UAAU,uBAAuB;AACrC,eAAK,QAAQ,6BAA6B,SAAS,KAAK,kBAAkB;AAAA,QAC5E;AAAA,MACF,SAAS,OAAO;AACd,cAAM;AAAA,UACJ,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,YAAI;AACF,cAAI,0BAA0B,CAAC,uBAAuB,SAAS,KAAK,qBAAqB,QAAS,IAAG,KAAK,oBAAoB;AAAA,QAChI,UAAE;AACA,cAAI,IAAK,OAAM,IAAI;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,IAAAA,yBAAwB,UAAU,cAAc,SAAU,UAAU;AAClE,UAAI,CAAC,KAAK,eAAe;AACvB;AAAA,MACF;AACA,UAAI,UAAU;AACZ,aAAK,QAAQ,QAAQ,YAAY,IAAI;AACrC,aAAK,QAAQ,WAAW,MAAM;AAAA,MAChC,OAAO;AACL,aAAK,QAAQ,QAAQ,YAAY,KAAK,aAAa;AACnD,aAAK,QAAQ,QAAQ,QAAQD,SAAQ,SAAS;AAAA,MAChD;AAAA,IACF;AACA,IAAAC,yBAAwB,UAAU,eAAe,SAAU,OAAO;AAChE,WAAK,QAAQ,QAAQ,cAAc,KAAK;AAAA,IAC1C;AACA,IAAAA,yBAAwB,UAAU,aAAa,SAAU,SAAS;AAChE,WAAK,QAAQ,WAAW,OAAO;AAAA,IACjC;AACA,IAAAA,yBAAwB,UAAU,oBAAoB,SAAU,KAAK;AACnE,UAAI,aAAa,IAAI,QAAQ,WAAW,IAAI,YAAY;AACxD,UAAI,IAAI,SAAS,WAAW,YAAY;AACtC,aAAK,QAAQ,iBAAiB;AAAA,MAChC;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,aAAa;AAAA;AAwBf,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AACjD,cAAUC,gBAAe,MAAM;AAC/B,aAASA,iBAAgB;AACvB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,eAAc,WAAW,SAAU,MAAM;AACvC,aAAO,IAAIA,eAAc,IAAI;AAAA,IAC/B;AACA,WAAO,eAAeA,eAAc,WAAW,uBAAuB;AAAA;AAAA,MAEpE,KAAK,WAAY;AACf,eAAO,KAAK;AAAA,MACd;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,eAAc,UAAU,uBAAuB,WAAY;AACzD,UAAI,QAAQ;AAIZ,UAAI,UAAU;AAAA,QACZ,SAAS,SAAU,MAAM;AACvB,iBAAO,MAAM,KAAK,aAAa,IAAI;AAAA,QACrC;AAAA,QACA,SAAS,SAAU,MAAM,OAAO;AAC9B,iBAAO,MAAM,KAAK,aAAa,MAAM,KAAK;AAAA,QAC5C;AAAA,QACA,YAAY,SAAU,MAAM;AAC1B,iBAAO,MAAM,KAAK,gBAAgB,IAAI;AAAA,QACxC;AAAA,QACA,YAAY,SAAU,SAAS;AAC7B,gBAAM,KAAK,cAAc;AAAA,QAC3B;AAAA,QACA,4BAA4B,SAAU,SAAS,SAAS;AACtD,iBAAO,MAAM,OAAO,SAAS,OAAO;AAAA,QACtC;AAAA,QACA,8BAA8B,SAAU,SAAS,SAAS;AACxD,iBAAO,MAAM,SAAS,SAAS,OAAO;AAAA,QACxC;AAAA,QACA,kBAAkB,WAAY;AAC5B,iBAAO,MAAM;AAAA,YAAK,wBAAwB,QAAQ;AAAA,YAAY,CAAC;AAAA,YAAiB;AAAA;AAAA,UAAuB;AAAA,QACzG;AAAA,MACF;AAEA,aAAO,IAAI,wBAAwB,OAAO;AAAA,IAC5C;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AAwBd,IAAI;AAAA;AAAA,EAAyB,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,aAAY;AACnB,aAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,IAC7D;AACA,IAAAA,WAAU,WAAW,SAAU,MAAM;AACnC,aAAO,IAAIA,WAAU,IAAI;AAAA,IAC3B;AACA,IAAAA,WAAU,UAAU,aAAa,SAAU,cAAc,mBAAmB,gBAAgB,aAAa,aAAa,mBAAmB;AACvI,UAAI,iBAAiB,QAAQ;AAC3B,uBAAe,SAAU,IAAI;AAC3B,iBAAO,IAAI,iBAAiB,EAAE;AAAA,QAChC;AAAA,MACF;AACA,UAAI,sBAAsB,QAAQ;AAChC,4BAAoB,SAAU,IAAI;AAChC,iBAAO,IAAI,cAAc,EAAE;AAAA,QAC7B;AAAA,MACF;AACA,UAAI,mBAAmB,QAAQ;AAC7B,yBAAiB,SAAU,IAAI;AAC7B,iBAAO,IAAI,kBAAkB,EAAE;AAAA,QACjC;AAAA,MACF;AACA,UAAI,gBAAgB,QAAQ;AAC1B,sBAAc,SAAU,IAAI;AAC1B,iBAAO,IAAI,QAAQ,EAAE;AAAA,QACvB;AAAA,MACF;AACA,UAAI,gBAAgB,QAAQ;AAC1B,sBAAc,SAAU,IAAI;AAC1B,iBAAO,IAAI,cAAc,EAAE;AAAA,QAC7B;AAAA,MACF;AACA,UAAI,sBAAsB,QAAQ;AAChC,4BAAoB,SAAU,IAAI;AAChC,iBAAO,IAAI,oBAAoB,EAAE;AAAA,QACnC;AAAA,MACF;AACA,WAAK,eAAe,KAAK,KAAK,cAAc,UAAU,sBAAsB;AAC5E,WAAK,eAAe,KAAK,KAAK,cAAc,UAAU,sBAAsB;AAC5E,WAAK,cAAc,KAAK,KAAK,cAAc,UAAU,qBAAqB;AAC1E,UAAI,CAAC,KAAK,cAAc;AACtB,cAAM,IAAI,MAAM,mFAAmF,MAAM,UAAU,yBAAyB,IAAI;AAAA,MAClJ;AACA,UAAI,KAAK,aAAa,aAAa,UAAU,aAAa,GAAG;AAC3D,YAAI,oBAAoB,SAAS,eAAe,KAAK,aAAa,aAAa,UAAU,aAAa,CAAC;AACvG,YAAI,mBAAmB;AACrB,eAAK,aAAa,kBAAkB,iBAAiB;AAAA,QACvD;AAAA,MACF;AACA,WAAK,UAAU,WAAW;AAC1B,UAAI,eAAe,KAAK,KAAK,cAAc,UAAU,cAAc;AACnE,WAAK,QAAQ,eAAe,aAAa,YAAY,IAAI;AACzD,UAAI,oBAAoB,KAAK,KAAK,cAAc,UAAU,oBAAoB;AAC9E,WAAK,aAAa,oBAAoB,kBAAkB,iBAAiB,IAAI;AAC7E,UAAI,iBAAiB,KAAK,KAAK,cAAc,UAAU,gBAAgB;AACvE,WAAK,UAAU,iBAAiB,eAAe,cAAc,IAAI;AACjE,UAAI,cAAc,KAAK,KAAK,cAAc,UAAU,qBAAqB;AACzE,UAAI,aAAa;AACf,aAAK,cAAc,YAAY,WAAW;AAAA,MAC5C;AACA,UAAI,CAAC,KAAK,KAAK,UAAU,SAAS,aAAa,QAAQ,GAAG;AACxD,aAAK,SAAS,KAAK,aAAa;AAAA,MAClC;AAAA,IACF;AAKA,IAAAA,WAAU,UAAU,qBAAqB,WAAY;AACnD,UAAI,QAAQ;AACZ,WAAK,cAAc,WAAY;AAC7B,cAAM,WAAW,YAAY;AAAA,MAC/B;AACA,WAAK,aAAa,WAAY;AAC5B,cAAM,WAAW,WAAW;AAAA,MAC9B;AACA,WAAK,cAAc,SAAU,KAAK;AAChC,cAAM,aAAa,MAAM;AACzB,cAAM,WAAW,YAAY,MAAM,yBAAyB,GAAG,CAAC;AAAA,MAClE;AACA,WAAK,gBAAgB,SAAU,KAAK;AAClC,cAAM,WAAW,cAAc,GAAG;AAAA,MACpC;AACA,WAAK,uBAAuB,SAAU,KAAK;AACzC,cAAM,WAAW,qBAAqB,IAAI,OAAO,KAAK;AAAA,MACxD;AACA,WAAK,mBAAmB,WAAY;AAClC,cAAM,WAAW,iBAAiB;AAAA,MACpC;AACA,WAAK,mBAAmB,WAAY;AAClC,cAAM,WAAW,iBAAiB;AAAA,MACpC;AACA,WAAK,oBAAoB,WAAY;AACnC,cAAM,WAAW,kBAAkB;AAAA,MACrC;AACA,WAAK,aAAa,iBAAiB,SAAS,KAAK,WAAW;AAC5D,WAAK,aAAa,iBAAiB,QAAQ,KAAK,UAAU;AAC1D,WAAK,aAAa,iBAAiB,SAAS,KAAK,WAAW;AAC5D,WAAK,aAAa,iBAAiB,WAAW,KAAK,aAAa;AAChE,WAAK,KAAK,OAAO,UAAU,cAAc,KAAK,gBAAgB;AAC9D,WAAK,KAAK,OAAO,UAAU,eAAe,KAAK,iBAAiB;AAChE,WAAK,KAAK,OAAO,UAAU,cAAc,KAAK,gBAAgB;AAC9D,WAAK,KAAK,OAAO,QAAU,gBAAgB,KAAK,oBAAoB;AACpE,UAAI,KAAK,aAAa;AACpB,YAAI,KAAK,YAAY,OAAO;AAI1B,eAAK,WAAW;AAAA,YAAS,KAAK,YAAY;AAAA;AAAA,YAAwB;AAAA,UAAI;AACtE,eAAK,WAAW,OAAO;AACvB;AAAA,QACF;AACA,aAAK,YAAY,QAAQ,KAAK;AAAA,MAChC;AAAA,IACF;AACA,IAAAA,WAAU,UAAU,UAAU,WAAY;AACxC,WAAK,aAAa,oBAAoB,SAAS,KAAK,WAAW;AAC/D,WAAK,aAAa,oBAAoB,QAAQ,KAAK,UAAU;AAC7D,WAAK,aAAa,oBAAoB,WAAW,KAAK,aAAa;AACnE,WAAK,aAAa,oBAAoB,SAAS,KAAK,WAAW;AAC/D,WAAK,KAAK,SAAS,UAAU,cAAc,KAAK,gBAAgB;AAChE,WAAK,KAAK,SAAS,UAAU,cAAc,KAAK,gBAAgB;AAChE,WAAK,KAAK,SAAS,QAAU,gBAAgB,KAAK,oBAAoB;AACtE,WAAK,KAAK,QAAQ;AAClB,UAAI,KAAK,QAAQ;AACf,aAAK,OAAO,QAAQ;AAAA,MACtB;AACA,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,QAAQ;AAAA,MACvB;AACA,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,QAAQ;AAAA,MAC3B;AACA,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,QAAQ;AAAA,MAC1B;AACA,aAAO,UAAU,QAAQ,KAAK,IAAI;AAAA,IACpC;AACA,WAAO,eAAeA,WAAU,WAAW,SAAS;AAAA,MAClD,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,SAAS;AAAA,MAClC;AAAA,MACA,KAAK,SAAU,OAAO;AACpB,aAAK,WAAW,SAAS,KAAK;AAAA,MAChC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,WAAU,UAAU,WAAW,SAAU,OAAO,YAAY;AAC1D,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,WAAK,WAAW,SAAS,OAAO,UAAU;AAAA,IAC5C;AACA,WAAO,eAAeA,WAAU,WAAW,iBAAiB;AAAA,MAC1D,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,iBAAiB;AAAA,MAC1C;AAAA,MACA,KAAK,SAAU,eAAe;AAC5B,aAAK,WAAW;AAAA,UAAiB;AAAA;AAAA,UAA8B;AAAA,QAAI;AAAA,MACrE;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,IAAAA,WAAU,UAAU,mBAAmB,SAAU,eAAe,YAAY;AAC1E,UAAI,eAAe,QAAQ;AACzB,qBAAa;AAAA,MACf;AACA,WAAK,WAAW;AAAA,QAAiB;AAAA;AAAA,QAA8B;AAAA,QAAM;AAAA,MAAU;AAAA,IACjF;AACA,WAAO,eAAeA,WAAU,WAAW,YAAY;AAAA,MACrD,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,YAAY;AAAA,MACrC;AAAA,MACA,KAAK,SAAU,UAAU;AACvB,aAAK,WAAW,YAAY,QAAQ;AACpC,YAAI,KAAK,aAAa;AACpB,eAAK,YAAY,WAAW;AAAA,QAC9B;AAAA,MACF;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,wBAAwB;AAAA,MACjE,KAAK,SAAU,OAAO;AACpB,aAAK,WAAW,wBAAwB,KAAK;AAAA,MAC/C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,sBAAsB;AAAA;AAAA;AAAA;AAAA,MAI/D,KAAK,SAAU,SAAS;AACtB,aAAK,WAAW,sBAAsB,OAAO;AAAA,MAC/C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,qBAAqB;AAAA;AAAA;AAAA;AAAA,MAI9D,KAAK,SAAU,SAAS;AACtB,aAAK,WAAW,qBAAqB,OAAO;AAAA,MAC9C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOjE,KAAK,SAAU,sBAAsB;AACnC,aAAK,WAAW,wBAAwB,oBAAoB;AAAA,MAC9D;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,SAAS;AAAA;AAAA;AAAA;AAAA,MAIlD,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,QAAQ;AAAA,MACjC;AAAA;AAAA;AAAA;AAAA,MAIA,KAAK,SAAU,SAAS;AACtB,aAAK,WAAW,SAAS,OAAO;AAAA,MAClC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA,MAIrD,KAAK,WAAY;AACf,eAAO,KAAK,WAAW,YAAY;AAAA,MACrC;AAAA;AAAA;AAAA;AAAA,MAIA,KAAK,SAAU,YAAY;AACzB,aAAK,WAAW,YAAY,UAAU;AAAA,MACxC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAChB,CAAC;AAKD,IAAAA,WAAU,UAAU,SAAS,WAAY;AACvC,WAAK,WAAW,OAAO;AAAA,IACzB;AAKA,IAAAA,WAAU,UAAU,gBAAgB,WAAY;AAC9C,WAAK,WAAW,cAAc;AAC9B,WAAK,KAAK,OAAO;AAEjB,WAAK,iBAAiB,KAAK,KAAK,MAAM,IAAI,SAAU,IAAI;AACtD,eAAO,GAAG,aAAa,UAAU,UAAU,KAAK;AAAA,MAClD,CAAC;AACD,UAAI,KAAK,aAAa;AACpB,aAAK,YAAY,QAAQ,KAAK;AAAA,MAChC;AAAA,IACF;AACA,IAAAA,WAAU,UAAU,uBAAuB,WAAY;AAGrD,UAAI,UAAU,SAAS,SAAS,SAAS,SAAS,CAAC,GAAG,KAAK,wBAAwB,CAAC,GAAG,KAAK,wBAAwB,CAAC,GAAG,KAAK,yBAAyB,CAAC,GAAG,KAAK,uBAAuB,CAAC;AACvL,aAAO,IAAI,oBAAoB,SAAS,KAAK,iBAAiB,CAAC;AAAA,IACjE;AAIA,IAAAA,WAAU,UAAU,YAAY,SAAU,aAAa;AACrD,WAAK,cAAc,KAAK,KAAK,cAAc,UAAU,aAAa;AAClE,WAAK,OAAO,YAAY,KAAK,WAAW;AACxC,WAAK,KAAK,eAAe;AACzB,WAAK,KAAK,kBAAkB;AAC5B,WAAK,iBAAiB,KAAK,KAAK,MAAM,IAAI,SAAU,IAAI;AACtD,eAAO,GAAG,aAAa,UAAU,UAAU,KAAK;AAAA,MAClD,CAAC;AAAA,IACH;AACA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC7C,UAAI,QAAQ;AAIZ,UAAI,UAAU,SAAS,SAAS,CAAC,GAAG,UAAU,cAAc;AAAA,QAC1D,MAAM,KAAK;AAAA,MACb,CAAC,CAAC,GAAG;AAAA,QACH,4BAA4B,SAAU,SAAS,SAAS;AACtD,gBAAM,aAAa,iBAAiB,SAAS,OAAO;AAAA,QACtD;AAAA,QACA,8BAA8B,SAAU,SAAS,SAAS;AACxD,gBAAM,aAAa,oBAAoB,SAAS,OAAO;AAAA,QACzD;AAAA,MACF,CAAC;AAED,aAAO,IAAI,UAAU,KAAK,cAAc,IAAI,oBAAoB,OAAO,CAAC;AAAA,IAC1E;AACA,IAAAA,WAAU,UAAU,0BAA0B,WAAY;AACxD,UAAI,QAAQ;AAEZ,aAAO;AAAA,QACL,iBAAiB,SAAU,UAAU,MAAM;AACzC,iBAAO,SAAS,aAAa,IAAI;AAAA,QACnC;AAAA,QACA,iBAAiB,SAAU,MAAM;AAC/B,gBAAM,aAAa,cAAc;AAAA,QACnC;AAAA,QACA,uBAAuB,WAAY;AACjC,iBAAO,SAAS,kBAAkB,MAAM;AAAA,QAC1C;AAAA,QACA,qBAAqB,SAAU,MAAM;AACnC,iBAAO,MAAM,aAAa,aAAa,IAAI;AAAA,QAC7C;AAAA,QACA,qBAAqB,SAAU,MAAM,OAAO;AAC1C,gBAAM,aAAa,aAAa,MAAM,KAAK;AAAA,QAC7C;AAAA,QACA,wBAAwB,SAAU,MAAM;AACtC,gBAAM,aAAa,gBAAgB,IAAI;AAAA,QACzC;AAAA,QACA,cAAc,SAAU,WAAW;AACjC,gBAAM,YAAY,UAAU,IAAI,SAAS;AAAA,QAC3C;AAAA,QACA,iBAAiB,SAAU,WAAW;AACpC,gBAAM,YAAY,UAAU,OAAO,SAAS;AAAA,QAC9C;AAAA,QACA,UAAU,WAAY;AACpB,gBAAM,KAAK,OAAO;AAAA,QACpB;AAAA,QACA,WAAW,WAAY;AACrB,gBAAM,KAAK,OAAO;AAAA,QACpB;AAAA,QACA,kBAAkB,WAAY;AAC5B,iBAAO,MAAM,KAAK,cAAc,UAAU,sBAAsB;AAAA,QAClE;AAAA,QACA,sBAAsB,SAAU,UAAU;AACxC,gBAAM,KAAK,iBAAiB,QAAQ;AAAA,QACtC;AAAA,QACA,qBAAqB,SAAU,cAAc;AAC3C,gBAAM,KAAK,gBAAgB,YAAY;AAAA,QACzC;AAAA,QACA,kBAAkB,SAAU,WAAW;AACrC,gBAAM,KAAK,YAAY;AAAA,QACzB;AAAA,QACA,kBAAkB,WAAY;AAC5B,cAAI,QAAQ,MAAM,KAAK;AACvB,iBAAO,iBAAiB,QAAQ,MAAM,CAAC,IAAI;AAAA,QAC7C;AAAA,QACA,kBAAkB,SAAU,OAAO;AACjC,gBAAM,KAAK,gBAAgB;AAAA,QAC7B;AAAA,QACA,sBAAsB,SAAU,OAAO;AACrC,gBAAM,KAAK,MAAM,KAAK,EAAE,MAAM;AAAA,QAChC;AAAA,QACA,kBAAkB,WAAY;AAC5B,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA;AAAA,QAEA,mBAAmB,WAAY;AAC7B,iBAAO,MAAM;AAAA,QACf;AAAA,QACA,wBAAwB,SAAU,OAAO;AACvC,iBAAO,MAAM,KAAK,sBAAsB,KAAK;AAAA,QAC/C;AAAA,QACA,uBAAuB,WAAY;AACjC,iBAAO,MAAM,KAAK;AAAA,QACpB;AAAA,QACA,oBAAoB,SAAU,UAAU,eAAe;AACrD,iBAAO,MAAM,KAAK,mBAAmB,UAAU,aAAa;AAAA,QAC9D;AAAA,MACF;AAAA,IAEF;AACA,IAAAA,WAAU,UAAU,0BAA0B,WAAY;AACxD,UAAI,QAAQ;AAEZ,aAAO;AAAA,QACL,UAAU,SAAU,WAAW;AAC7B,gBAAM,KAAK,UAAU,IAAI,SAAS;AAAA,QACpC;AAAA,QACA,aAAa,SAAU,WAAW;AAChC,gBAAM,KAAK,UAAU,OAAO,SAAS;AAAA,QACvC;AAAA,QACA,UAAU,SAAU,WAAW;AAC7B,iBAAO,MAAM,KAAK,UAAU,SAAS,SAAS;AAAA,QAChD;AAAA,QACA,iBAAiB,SAAU,aAAa;AACtC,gBAAM,cAAc,MAAM,WAAW,gBAAgB,WAAW;AAAA,QAClE;AAAA,QACA,oBAAoB,WAAY;AAC9B,gBAAM,cAAc,MAAM,WAAW,SAAS;AAAA,QAChD;AAAA,QACA,sBAAsB,WAAY;AAChC,gBAAM,cAAc,MAAM,WAAW,WAAW;AAAA,QAClD;AAAA,QACA,cAAc,SAAU,OAAO;AAC7B,cAAI,MAAM,aAAa;AACrB,kBAAM,YAAY,QAAQ;AAAA,UAC5B;AACA,cAAI,QAAQ,MAAM;AAClB,gBAAM;AAAA,YAAK,UAAU;AAAA,YAAc;AAAA,cACjC;AAAA,cACA;AAAA,YACF;AAAA,YAAG;AAAA;AAAA,UAAwB;AAAA,QAC7B;AAAA,MACF;AAAA,IAEF;AACA,IAAAA,WAAU,UAAU,2BAA2B,WAAY;AACzD,UAAI,QAAQ;AAEZ,aAAO;AAAA,QACL,YAAY,WAAY;AACtB,iBAAO,QAAQ,MAAM,OAAO;AAAA,QAC9B;AAAA,QACA,cAAc,SAAU,YAAY;AAClC,gBAAM,WAAW,MAAM,QAAQ,MAAM,UAAU;AAAA,QACjD;AAAA,QACA,cAAc,WAAY;AACxB,gBAAM,WAAW,MAAM,QAAQ,WAAW;AAAA,QAC5C;AAAA,MACF;AAAA,IAEF;AACA,IAAAA,WAAU,UAAU,yBAAyB,WAAY;AACvD,UAAI,QAAQ;AAEZ,aAAO;AAAA,QACL,UAAU,WAAY;AACpB,iBAAO,CAAC,CAAC,MAAM;AAAA,QACjB;AAAA,QACA,YAAY,SAAU,aAAa;AACjC,gBAAM,SAAS,MAAM,MAAM,MAAM,WAAW;AAAA,QAC9C;AAAA,QACA,eAAe,WAAY;AACzB,iBAAO,MAAM,QAAQ,MAAM,MAAM,SAAS,IAAI;AAAA,QAChD;AAAA,QACA,kBAAkB,SAAU,YAAY;AACtC,gBAAM,SAAS,MAAM,MAAM,YAAY,UAAU;AAAA,QACnD;AAAA,MACF;AAAA,IAEF;AAIA,IAAAA,WAAU,UAAU,2BAA2B,SAAU,KAAK;AAC5D,UAAI,mBAAmB,IAAI,OAAO,sBAAsB;AACxD,UAAI,cAAc,KAAK,aAAa,GAAG,IAAI,IAAI,QAAQ,CAAC,EAAE,UAAU,IAAI;AACxE,aAAO,cAAc,iBAAiB;AAAA,IACxC;AACA,IAAAA,WAAU,UAAU,eAAe,SAAU,KAAK;AAChD,aAAO,QAAQ,IAAI,OAAO;AAAA,IAC5B;AAIA,IAAAA,WAAU,UAAU,mBAAmB,WAAY;AACjD,aAAO;AAAA,QACL,YAAY,KAAK,aAAa,KAAK,WAAW,sBAAsB;AAAA,QACpE,aAAa,KAAK,cAAc,KAAK,YAAY,sBAAsB;AAAA,MACzE;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE,YAAY;AAAA;AACd,IAAM,wBAAwB;AAC9B,IAAM,YAAY,MAAM;AAAA,EACtB,YAAY,SAAS;AACnB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,OAAO,YAAY,MAAM,QAAQ,CAAC;AACvC,SAAK,cAAc;AACnB,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAC5B,SAAK,0BAA0B;AAC/B,SAAK,wBAAwB;AAC7B,SAAK,iBAAiB;AAAA,MACpB,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,MACV,iBAAiB,CAAC;AAAA,MAClB,sBAAsB;AAAA,IACxB;AACA,SAAK,QAAQ,CAAC;AAAA,EAChB;AAAA,EACA,mBAAmB,UAAU;AAC3B,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,GAAG;AAAA,MAC1E,QAAQ;AAAA,IACV,CAAC;AACD,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,4BAA4B,UAAU;AACpC,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,GAAG;AAAA,MAC1E,iBAAiB,OAAO,aAAa,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,IACzE,CAAC;AACD,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,eAAe,UAAU;AACvB,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,GAAG;AAAA,MAC1E,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,0BAA0B,UAAU;AAClC,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,GAAG;AAAA,MAC1E,sBAAsB;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,cAAc,UAAU;AACtB,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,GAAG;AAAA,MAC1E,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,aAAa,IAAI;AACf,UAAM,gBAAgB,GAAG,aAAa,EAAE,CAAC;AACzC,QAAI,CAAC,iBAAiB,cAAc,iBAAiB,KAAK,aAAa;AACrE;AAAA,IACF;AACA,QAAI,KAAK,kBAAkB,UAAa,KAAK,cAAc,UAAU,SAAS,uBAAuB,KAAK,KAAK,YAAY,QAAW;AACpI,WAAK,QAAQ,OAAO;AAAA,IACtB;AAAA,EACF;AAAA,EACM,oBAAoB;AAAA;AACxB,YAAM,UAAU;AAChB,WAAK,iBAAiB;AAAA,QACpB,QAAQ,KAAK;AAAA,QACb,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,QACf,iBAAiB,OAAO,KAAK,oBAAoB,WAAW,KAAK,MAAM,KAAK,eAAe,IAAI,KAAK;AAAA,QACpG,sBAAsB,KAAK;AAAA,MAC7B;AACA,WAAK,oBAAoB,KAAK,eAAe;AAC7C,WAAK,mBAAmB,KAAK,eAAe;AAC5C,WAAK,iBAAiB;AACtB,WAAK,cAAc;AAAA,IACrB;AAAA;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,kBAAkB,UAAa,KAAK,gBAAgB,QAAW;AACtE,WAAK,YAAY,IAAI,UAAU,KAAK,aAAa;AACjD,WAAK,UAAU,IAAI,QAAQ,KAAK,WAAW;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,cAAc,QAAW;AAChC;AAAA,IACF;AACA,SAAK,UAAU,QAAQ;AACvB,QAAI,KAAK,YAAY,QAAW;AAC9B;AAAA,IACF;AACA,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA,EACA,SAAS;AACP,UAAM,uBAAuB,KAAK,2BAA2B;AAC7D,UAAM,wBAAwB,KAAK,4BAA4B;AAC/D,WAAO,EAAE,MAAM;AAAA,MACb,OAAO;AAAA,QACL,6BAA6B;AAAA,QAC7B,QAAQ,wBAAwB;AAAA,MAClC;AAAA,IACF,GAAG,KAAK,uBAAuB,oBAAoB,GAAG,KAAK,wBAAwB,qBAAqB,CAAC;AAAA,EAC3G;AAAA,EACA,6BAA6B;AAC3B,WAAO,CAAC,KAAK,eAAe,wBAAwB,KAAK,eAAe,gBAAgB,WAAW,KAAK,KAAK,eAAe,UAAU,KAAK,IAAI,GAAG,KAAK,eAAe,eAAe;AAAA,EACvL;AAAA,EACA,8BAA8B;AAC5B,WAAO,KAAK,eAAe,UAAU,KAAK,eAAe;AAAA,EAC3D;AAAA,EACA,wBAAwB,QAAQ;AAC9B,WAAO,EAAE,OAAO;AAAA,MACd,MAAM;AAAA,MACN,cAAc,UAAU,YAAY;AAAA,MACpC,OAAO;AAAA,QACL,sCAAsC;AAAA,QACtC;AAAA,MACF;AAAA,IACF,GAAG,EAAE,UAAU;AAAA,MACb,SAAS,MAAM,KAAK,aAAa;AAAA,MACjC,aAAa;AAAA,MACb,UAAU,KAAK,cAAc;AAAA,MAC7B,cAAc,UAAU,kBAAkB;AAAA,MAC1C,OAAO,gCAAgC,KAAK,cAAc,IAAI,aAAa,EAAE;AAAA,IAC/E,CAAC,GAAG,KAAK,oBAAoB,GAAG,KAAK,kBAAkB,GAAG,EAAE,UAAU;AAAA,MACpE,SAAS,MAAM,KAAK,SAAS;AAAA,MAC7B,aAAa;AAAA,MACb,UAAU,KAAK,aAAa;AAAA,MAC5B,cAAc,UAAU,cAAc;AAAA,MACtC,OAAO,gCAAgC,KAAK,aAAa,IAAI,aAAa,EAAE;AAAA,IAC9E,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,eAAe,QAAQ;AACrB,SAAK,mBAAmB,KAAK,eAAe;AAC5C,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,GAAG;AAAA,MAC1E,UAAU,WAAW,MAAM;AAAA,IAC7B,CAAC;AACD,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,gBAAgB,QAAQ;AACtB,QAAI,SAAS,KAAK,SAAS,KAAK,cAAc,GAAG;AAC/C;AAAA,IACF;AACA,SAAK,oBAAoB,KAAK,eAAe;AAC7C,SAAK,iBAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,cAAc,GAAG;AAAA,MAC1E,WAAW;AAAA,IACb,CAAC;AACD,SAAK,oBAAoB;AACzB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,eAAe;AACb,SAAK,gBAAgB,KAAK,eAAe,YAAY,CAAC;AAAA,EACxD;AAAA,EACA,WAAW;AACT,SAAK,gBAAgB,KAAK,eAAe,YAAY,CAAC;AAAA,EACxD;AAAA,EACA,mBAAmB;AACjB,QAAI;AACJ,SAAK,cAAc,KAAK,KAAK,KAAK,eAAe,SAAS,KAAK,eAAe,QAAQ;AACtF,QAAI,CAAC,KAAK,yBAAyB;AACjC,YAAM,gCAAgC,KAAK,eAAe,YAAY,KAAK;AAC3E,YAAM,eAAe,KAAK,MAAM,gCAAgC,KAAK,eAAe,QAAQ;AAC5F,kBAAY,eAAe,KAAK,cAAc,eAAe,KAAK,cAAc;AAAA,IAClF,OAAO;AACL,kBAAY,KAAK,mBAAmB,IAAI,KAAK;AAAA,IAC/C;AACA,SAAK,gBAAgB,SAAS;AAC9B,SAAK,QAAQ,CAAC,GAAG,MAAM,KAAK,WAAW,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC;AAC7D,SAAK,mBAAmB;AACxB,SAAK,mBAAmB,KAAK,eAAe;AAAA,EAC9C;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,eAAe,cAAc;AAAA,EAC3C;AAAA,EACA,eAAe;AACb,WAAO,KAAK,eAAe,cAAc,KAAK,cAAc;AAAA,EAC9D;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,KAAK,KAAK;AAAA,QACb,QAAQ,KAAK,eAAe;AAAA,QAC5B,WAAW,KAAK,eAAe;AAAA,QAC/B,UAAU,KAAK,eAAe;AAAA,QAC9B,mBAAmB,KAAK;AAAA,MAC1B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,yBAAyB,QAAQ;AAC/B,WAAO,KAAK,eAAe,aAAa,SAAS,6BAA6B;AAAA,EAChF;AAAA,EACA,mBAAmB,OAAO,MAAM;AAC9B,QAAI,cAAc;AAClB,mBAAe,KAAK,eAAe,cAAc,QAAQ,cAAc;AACvE,mBAAe,KAAK,SAAS,EAAE,SAAS,IAAI,UAAU;AACtD,WAAO;AAAA,EACT;AAAA,EACA,qBAAqB;AACnB,WAAO,EAAE,QAAQ;AAAA,MACf,OAAO;AAAA,IACT,GAAG,KAAK;AAAA,EACV;AAAA,EACA,qBAAqB,MAAM,OAAO;AAChC,WAAO,EAAE,UAAU;AAAA,MACjB,KAAK;AAAA,MACL,SAAS,MAAM,KAAK,gBAAgB,KAAK;AAAA,MACzC,iBAAiB,KAAK,eAAe,cAAc,QAAQ,SAAS;AAAA,MACpE,cAAc,UAAU,WAAW,EAAE,QAAQ,UAAU,IAAI;AAAA,MAC3D,aAAa;AAAA,MACb,OAAO,GAAG,KAAK,mBAAmB,OAAO,IAAI,CAAC;AAAA,IAChD,GAAG,IAAI;AAAA,EACT;AAAA,EACA,wBAAwB,KAAK,MAAM,OAAO,eAAe,mBAAmB;AAC1E,QAAI,eAAe;AACjB,aAAO,IAAI,OAAO,KAAK,qBAAqB,MAAM,KAAK,CAAC;AAAA,IAC1D,WAAW,mBAAmB;AAC5B,aAAO,IAAI,OAAO,KAAK,mBAAmB,CAAC;AAAA,IAC7C,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,QAAI,KAAK,eAAe,YAAY,GAAG;AACrC,aAAO,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,UAAU,KAAK,wBAAwB,KAAK,MAAM,OAAO,QAAQ,KAAK,UAAU,KAAK,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,IAC9J,WAAW,KAAK,eAAe,YAAY,KAAK,cAAc,GAAG;AAC/D,aAAO,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,UAAU,KAAK,wBAAwB,KAAK,MAAM,OAAO,UAAU,KAAK,QAAQ,KAAK,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,IAC9J,OAAO;AACL,aAAO,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,UAAU,KAAK,wBAAwB,KAAK,MAAM,OAAO,UAAU,KAAK,UAAU,KAAK,MAAM,SAAS,KAAK,SAAS,KAAK,eAAe,YAAY,KAAK,SAAS,KAAK,eAAe,YAAY,GAAG,UAAU,KAAK,UAAU,KAAK,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC;AAAA,IAC/R;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,EAAE,QAAQ;AAAA,MACf,OAAO;AAAA,IACT,GAAG,KAAK,eAAe,YAAY,GAAG,KAAK,KAAK,WAAW;AAAA,EAC7D;AAAA,EACA,uBAAuB,QAAQ;AAC7B,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,QACL,cAAc;AAAA,QACd,sBAAsB;AAAA,QACtB;AAAA,MACF;AAAA,MACA,KAAK,QAAM,KAAK,gBAAgB;AAAA,IAClC,GAAG,EAAE,OAAO;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,EAAE,QAAQ;AAAA,MACX,OAAO;AAAA,IACT,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,OAAO;AAAA,IACT,GAAG,UAAU,kBAAkB,CAAC,GAAG,EAAE,QAAQ;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC,GAAG,EAAE,QAAQ;AAAA,MACZ,OAAO;AAAA,IACT,CAAC,CAAC,GAAG,EAAE,OAAO;AAAA,MACZ,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,YAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,2BAA2B,KAAK;AAAA,QAChC,+BAA+B,CAAC,KAAK;AAAA,QACrC,sCAAsC;AAAA,MACxC;AAAA,MACA,KAAK,QAAM,KAAK,cAAc;AAAA,IAChC,GAAG,EAAE,MAAM;AAAA,MACT,OAAO;AAAA,MACP,MAAM;AAAA,IACR,GAAG,KAAK,eAAe,gBAAgB,IAAI,CAAC,QAAQ,UAAU,EAAE,MAAM;AAAA,MACpE,aAAa;AAAA,MACb,cAAc;AAAA,MACd,KAAK;AAAA,MACL,iBAAiB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAM,KAAK,eAAe,MAAM;AAAA,MACzC,OAAO,iBAAiB,KAAK,yBAAyB,MAAM,CAAC;AAAA,IAC/D,GAAG,EAAE,QAAQ;AAAA,MACX,OAAO;AAAA,IACT,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,EAChB;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,UAAU,CAAC,oBAAoB;AAAA,MAC/B,mBAAmB,CAAC,6BAA6B;AAAA,MACjD,aAAa,CAAC,gBAAgB;AAAA,MAC9B,wBAAwB,CAAC,2BAA2B;AAAA,MACpD,YAAY,CAAC,eAAe;AAAA,IAC9B;AAAA,EACF;AACF;AACA,UAAU,QAAQ;", "names": ["MDCFloatingLabelFoundation", "MDCFloatingLabel", "MDCLineRippleFoundation", "MDCLineRipple", "MDCNotchedOutlineFoundation", "MDCNotchedOutline", "MDCSelectFoundation", "strings$1", "MDCSelectHelperTextFoundation", "MDCSelectHelperText", "strings", "MDCSelectIconFoundation", "MDCSelectIcon", "MDCSelect"]}