﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BCI.DocupediaBot.Persistence.EF.Migrations
{

    public partial class Addnewfields : Migration
    {

        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Content_Page_PageId",
                table: "Content");

            migrationBuilder.DropForeignKey(
                name: "FK_Page_Collection_CollectionId",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Page_CollectionId",
                table: "Page");

            migrationBuilder.DropIndex(
                name: "IX_Content_PageId",
                table: "Content");

            migrationBuilder.RenameColumn(
                name: "Name",
                table: "Page",
                newName: "Url");

            migrationBuilder.RenameIndex(
                name: "IX_Page_Name",
                table: "Page",
                newName: "IX_Page_Url");

            migrationBuilder.AddColumn<bool>(
                name: "IsIncludeChild",
                table: "Page",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "SourceId",
                table: "Page",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "Page",
                type: "text",
                nullable: false,
                defaultValue: "");
        }


        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsIncludeChild",
                table: "Page");

            migrationBuilder.DropColumn(
                name: "SourceId",
                table: "Page");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "Page");

            migrationBuilder.RenameColumn(
                name: "Url",
                table: "Page",
                newName: "Name");

            migrationBuilder.RenameIndex(
                name: "IX_Page_Url",
                table: "Page",
                newName: "IX_Page_Name");

            migrationBuilder.CreateIndex(
                name: "IX_Page_CollectionId",
                table: "Page",
                column: "CollectionId");

            migrationBuilder.CreateIndex(
                name: "IX_Content_PageId",
                table: "Content",
                column: "PageId");

            migrationBuilder.AddForeignKey(
                name: "FK_Content_Page_PageId",
                table: "Content",
                column: "PageId",
                principalTable: "Page",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Page_Collection_CollectionId",
                table: "Page",
                column: "CollectionId",
                principalTable: "Collection",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
