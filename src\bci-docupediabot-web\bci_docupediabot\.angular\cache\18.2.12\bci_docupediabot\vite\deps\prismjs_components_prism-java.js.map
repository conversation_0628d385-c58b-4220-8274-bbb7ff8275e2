{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-java.js"], "sourcesContent": ["(function (Prism) {\n  var keywords = /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\\s*[(){}[\\]<>=%~.:,;?+\\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/;\n\n  // full package (optional) + parent classes (optional)\n  var classNamePrefix = /(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/.source;\n\n  // based on the java naming conventions\n  var className = {\n    pattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n    lookbehind: true,\n    inside: {\n      'namespace': {\n        pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n        inside: {\n          'punctuation': /\\./\n        }\n      },\n      'punctuation': /\\./\n    }\n  };\n  Prism.languages.java = Prism.languages.extend('clike', {\n    'string': {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': [className, {\n      // variables, parameters, and constructor references\n      // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n      pattern: RegExp(/(^|[^\\w.])/.source + classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()]|\\s*(?:\\[[\\s,]*\\]\\s*)?::\\s*new\\b)/.source),\n      lookbehind: true,\n      inside: className.inside\n    }, {\n      // class names based on keyword\n      // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n      pattern: RegExp(/(\\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\\s+)/.source + classNamePrefix + /[A-Z]\\w*\\b/.source),\n      lookbehind: true,\n      inside: className.inside\n    }],\n    'keyword': keywords,\n    'function': [Prism.languages.clike.function, {\n      pattern: /(::\\s*)[a-z_]\\w*/,\n      lookbehind: true\n    }],\n    'number': /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n    'operator': {\n      pattern: /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n      lookbehind: true\n    },\n    'constant': /\\b[A-Z][A-Z_\\d]+\\b/\n  });\n  Prism.languages.insertBefore('java', 'string', {\n    'triple-quoted-string': {\n      // http://openjdk.java.net/jeps/355#Description\n      pattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    'char': {\n      pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n      greedy: true\n    }\n  });\n  Prism.languages.insertBefore('java', 'class-name', {\n    'annotation': {\n      pattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    'generics': {\n      pattern: /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n      inside: {\n        'class-name': className,\n        'keyword': keywords,\n        'punctuation': /[<>(),.:]/,\n        'operator': /[?&|]/\n      }\n    },\n    'import': [{\n      pattern: RegExp(/(\\bimport\\s+)/.source + classNamePrefix + /(?:[A-Z]\\w*|\\*)(?=\\s*;)/.source),\n      lookbehind: true,\n      inside: {\n        'namespace': className.inside.namespace,\n        'punctuation': /\\./,\n        'operator': /\\*/,\n        'class-name': /\\w+/\n      }\n    }, {\n      pattern: RegExp(/(\\bimport\\s+static\\s+)/.source + classNamePrefix + /(?:\\w+|\\*)(?=\\s*;)/.source),\n      lookbehind: true,\n      alias: 'static',\n      inside: {\n        'namespace': className.inside.namespace,\n        'static': /\\b\\w+$/,\n        'punctuation': /\\./,\n        'operator': /\\*/,\n        'class-name': /\\w+/\n      }\n    }],\n    'namespace': {\n      pattern: RegExp(/(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/.source.replace(/<keyword>/g, function () {\n        return keywords.source;\n      })),\n      lookbehind: true,\n      inside: {\n        'punctuation': /\\./\n      }\n    }\n  });\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAChB,MAAI,WAAW;AAGf,MAAI,kBAAkB,6CAA6C;AAGnE,MAAI,YAAY;AAAA,IACd,SAAS,OAAO,aAAa,SAAS,kBAAkB,gCAAgC,MAAM;AAAA,IAC9F,YAAY;AAAA,IACZ,QAAQ;AAAA,MACN,aAAa;AAAA,QACX,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,IACjB;AAAA,EACF;AACA,EAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,IACrD,UAAU;AAAA,MACR,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV;AAAA,IACA,cAAc,CAAC,WAAW;AAAA;AAAA;AAAA,MAGxB,SAAS,OAAO,aAAa,SAAS,kBAAkB,+DAA+D,MAAM;AAAA,MAC7H,YAAY;AAAA,MACZ,QAAQ,UAAU;AAAA,IACpB,GAAG;AAAA;AAAA;AAAA,MAGD,SAAS,OAAO,kFAAkF,SAAS,kBAAkB,aAAa,MAAM;AAAA,MAChJ,YAAY;AAAA,MACZ,QAAQ,UAAU;AAAA,IACpB,CAAC;AAAA,IACD,WAAW;AAAA,IACX,YAAY,CAACA,OAAM,UAAU,MAAM,UAAU;AAAA,MAC3C,SAAS;AAAA,MACT,YAAY;AAAA,IACd,CAAC;AAAA,IACD,UAAU;AAAA,IACV,YAAY;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,IAC7C,wBAAwB;AAAA;AAAA,MAEtB,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,OAAO;AAAA,IACT;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACD,EAAAA,OAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,IACjD,cAAc;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,QACN,cAAc;AAAA,QACd,WAAW;AAAA,QACX,eAAe;AAAA,QACf,YAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA,UAAU,CAAC;AAAA,MACT,SAAS,OAAO,gBAAgB,SAAS,kBAAkB,0BAA0B,MAAM;AAAA,MAC3F,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,aAAa,UAAU,OAAO;AAAA,QAC9B,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF,GAAG;AAAA,MACD,SAAS,OAAO,yBAAyB,SAAS,kBAAkB,qBAAqB,MAAM;AAAA,MAC/F,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,QAAQ;AAAA,QACN,aAAa,UAAU,OAAO;AAAA,QAC9B,UAAU;AAAA,QACV,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,IACD,aAAa;AAAA,MACX,SAAS,OAAO,qJAAqJ,OAAO,QAAQ,cAAc,WAAY;AAC5M,eAAO,SAAS;AAAA,MAClB,CAAC,CAAC;AAAA,MACF,YAAY;AAAA,MACZ,QAAQ;AAAA,QACN,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF,CAAC;AACH,GAAG,KAAK;", "names": ["Prism"]}