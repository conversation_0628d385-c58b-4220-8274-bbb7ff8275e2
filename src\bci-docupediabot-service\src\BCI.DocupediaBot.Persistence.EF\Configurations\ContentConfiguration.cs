﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class ContentConfiguration : IEntityTypeConfiguration<Content>
  {
    public void Configure(EntityTypeBuilder<Content> entityBuilder)
    {
      entityBuilder.ToTable(nameof(Content));


      entityBuilder.HasIndex(x => x.SourceId).IsUnique();
      entityBuilder.HasIndex(x => x.Title);
      entityBuilder.HasIndex(x => x.VersionNo);
      entityBuilder.HasIndex(x => x.EmbeddingVersionNo);
      entityBuilder.HasIndex(x => x.SourceModificationTime);


      entityBuilder.HasIndex(x => new { x.SourceId, x.VersionNo, x.EmbeddingVersionNo });


      entityBuilder.HasIndex(x => new { x.TenantId, x.Is<PERSON>ele<PERSON> });
      entityBuilder.HasIndex(x => new { x.Creator, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.CreationTime, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.VersionNo, x.TenantId, x.IsDeleted });
		}
	}
}
