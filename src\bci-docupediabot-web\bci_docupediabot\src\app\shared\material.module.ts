/**
 * Copyright (C) 2023 Robert Bosch Manufacturing Solutions GmbH, Germany. All rights reserved.
 */

import { NgModule } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatStepperModule } from '@angular/material/stepper';
import { MatSliderModule } from '@angular/material/slider';

@NgModule({
    exports: [
        MatAutocompleteModule,
        MatInputModule,
        MatDatepickerModule,
        MatCheckboxModule,
        MatNativeDateModule,
        MatFormFieldModule,
        MatIconModule,
        MatListModule,
        MatExpansionModule,
        MatSelectModule,
        MatOptionModule,
        MatSliderModule,
        MatCardModule,
        MatButtonModule,
        MatButtonToggleModule,
        MatToolbarModule,
        MatSidenavModule,
        MatSlideToggleModule,
        MatPaginatorModule,
        MatTableModule,
        MatTabsModule,
        MatMenuModule,
        MatChipsModule,
        MatDialogModule,
        MatRadioModule,
        MatProgressBarModule,
        MatStepperModule,
    ],
})
export class MaterialModule { }
