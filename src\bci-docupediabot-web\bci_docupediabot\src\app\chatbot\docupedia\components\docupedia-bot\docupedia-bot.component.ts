import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { ChatModel } from '@shared/enums';
import { Collection } from '@shared/models/docupedia.model';
import { ChatbotService } from '@shared/services/docupedia/chatbot.service';
import { CollectionService } from '@shared/services/docupedia/collection.service';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ChatMessage,
  ChatStateService,
} from '@shared/services/docupedia/chat-state.service';
import { LoggerService } from '@shared/services/logger.service';
import { AuthService } from '@shared/services/auth.service';
import * as Prism from 'prismjs';
import 'prismjs/components/prism-csharp';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-markup';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-yaml';
import 'prismjs/components/prism-java';

interface ExtendedChatMessage extends ChatMessage {
  isLoading?: boolean;
}

@Component({
  selector: 'app-chatbot-docupedia',
  templateUrl: './docupedia-bot.component.html',
  styleUrls: ['./docupedia-bot.component.scss'],
})
export class DocupediaBotComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('chatMessages') chatMessages!: ElementRef;
  @ViewChild('userInput') userInput!: ElementRef;

  collections: Collection[] = [];
  hasInteracted = false;
  messages: ExtendedChatMessage[] = [];
  isLoading = false;
  private subscription = new Subscription();
  baseUrl = 'https://inside-docupedia.bosch.com';
  errorMessage: string | null = null;
  selectedCollectionAction: string = '';
  selectedCollection: string = '';
  chatModels = Object.keys(ChatModel)
    .filter((key) => isNaN(Number(key)))
    .map((key) => ({ name: key, value: key }));
  selectedModel: string = '';

  constructor(
    private apiService: ChatbotService,
    private collectionService: CollectionService,
    private sanitizer: DomSanitizer,
    private chatStateService: ChatStateService,
    private cdr: ChangeDetectorRef,
    private logger: LoggerService,
    private authService: AuthService
  ) {}

  get filteredCollections(): Collection[] {
    return this.collections.filter(
      (collection) => collection.status === 1 && collection.isEmbedding === true
    );
  }

  ngOnInit(): void {
    this.loadCollections();
    this.restoreState();
  }

  private restoreState(): void {
    this.subscription.add(
      this.chatStateService.selectedModel$.subscribe((model) => {
        if (model && this.chatModels.find((m) => m.value === model)) {
          this.selectedModel = model;
        } else if (!model && this.chatModels.length > 0) {
          this.selectedModel = this.chatModels[0].value;
          this.chatStateService.setSelectedModel(this.selectedModel);
        }
        this.cdr.detectChanges();
      })
    );


    this.subscription.add(
      this.chatStateService.selectedCollection$.subscribe((collectionId) => {
        if (
          collectionId &&
          this.filteredCollections.find((c) => c.id === collectionId)
        ) {
          this.selectedCollection = collectionId;
        } else if (this.filteredCollections.length > 0) {
          this.selectedCollection = this.filteredCollections[0].id;
          this.chatStateService.setSelectedCollection(this.selectedCollection);
        }
        this.cdr.detectChanges();
      })
    );


    this.subscription.add(
      this.chatStateService.messages$.subscribe((messages) => {
        this.messages = messages.map((msg) => ({
          ...msg,
          formattedContent:
            msg.type === 'bot' ? this.formatResponse(msg.content) : msg.content,
        }));
        this.cdr.detectChanges();
        this.scrollToBottom();
        this.highlightCode();
      })
    );


    this.subscription.add(
      this.chatStateService.hasInteracted$.subscribe((interacted) => {
        this.hasInteracted = interacted;
        this.cdr.detectChanges();
      })
    );
  }

  loadCollections(): void {
    this.subscription.add(
      this.collectionService.getCollections().subscribe({
        next: (collections) => {
          this.collections = collections;

          const currentUser = this.authService.getCurrentUser();
          const favoriteCollectionId = currentUser?.favCollecitonId;

          let selectedCollectionId = '';
          if (favoriteCollectionId &&
              this.filteredCollections.find((c) => c.id === favoriteCollectionId)) {
            selectedCollectionId = favoriteCollectionId;
          } else {
            const savedCollection = this.chatStateService.getSelectedCollection();
            if (savedCollection &&
                this.filteredCollections.find((c) => c.id === savedCollection)) {
              selectedCollectionId = savedCollection;
            } else if (this.filteredCollections.length > 0) {
              selectedCollectionId = this.filteredCollections[0].id;
            }
          }

          if (selectedCollectionId) {
            this.selectedCollection = selectedCollectionId;
            this.chatStateService.setSelectedCollection(this.selectedCollection);
          }

          this.cdr.detectChanges();
        },
        error: (err) => {
          this.errorMessage = err.message ?? 'Failed to load collections';
          this.logger.error('Failed to load collections:', err);
        },
      })
    );
  }

  resetChat(): void {
    this.chatStateService.clearState();
    this.hasInteracted = false;
    this.messages = [];
    this.isLoading = false;
    this.errorMessage = null;
    this.selectedCollectionAction = '';


    const savedModel = this.chatStateService.getSelectedModel();
    if (savedModel && this.chatModels.find(m => m.value === savedModel)) {
      this.selectedModel = savedModel;
    } else if (this.chatModels.length > 0) {
      this.selectedModel = this.chatModels[0].value;
    }
    this.chatStateService.setSelectedModel(this.selectedModel);


    const savedCollection = this.chatStateService.getSelectedCollection();
    if (savedCollection && this.filteredCollections.find(c => c.id === savedCollection)) {
      this.selectedCollection = savedCollection;
    } else {
      const currentUser = this.authService.getCurrentUser();
      const favoriteCollectionId = currentUser?.favCollecitonId;

      if (favoriteCollectionId && this.filteredCollections.find(c => c.id === favoriteCollectionId)) {
        this.selectedCollection = favoriteCollectionId;
      } else if (this.filteredCollections.length > 0) {
        this.selectedCollection = this.filteredCollections[0].id;
      }
    }
    this.chatStateService.setSelectedCollection(this.selectedCollection);

    this.cdr.detectChanges();
  }

  ngAfterViewInit(): void {
    this.scrollToBottom();
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private scrollToBottom(): void {
    if (this.chatMessages) {
      setTimeout(() => {
        this.chatMessages.nativeElement.scrollTop =
          this.chatMessages.nativeElement.scrollHeight;
      }, 0);
    }
  }

  private highlightCode(): void {
    if (this.chatMessages) {
      setTimeout(() => {
        Prism.highlightAllUnder(this.chatMessages.nativeElement);
      }, 0);
    }
  }

  onModelChange(): void {
    this.chatStateService.setSelectedModel(this.selectedModel); // Save model selection
    this.cdr.detectChanges();
  }

  onCollectionChange(): void {
    this.chatStateService.setSelectedCollection(this.selectedCollection); // Save Collection selection
    this.cdr.detectChanges();
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !this.isLoading) {
      this.sendMessage();
    }
  }

  copyToClipboard(content: string): void {
    navigator.clipboard.writeText(content).then(() => {

      this.logger.info('Content copied to clipboard');
    }).catch(err => {
      this.logger.error('Failed to copy content: ', err);
    });
  }

  sendMessage(): void {
    const userInputElement = this.userInput?.nativeElement as HTMLInputElement;
    if (!userInputElement) return;

    const message = userInputElement.value.trim();

    if (!message || this.isLoading) return;
    if (!this.selectedCollection) {
      this.errorMessage =
        'Please select a collection before sending a message.';
      return;
    }
    if (!this.selectedModel) {
      this.errorMessage =
        'Please select a chat model before sending a message.';
      return;
    }

    this.hasInteracted = true;
    this.chatStateService.setHasInteracted(true);
    this.isLoading = true;
    userInputElement.value = '';
    this.errorMessage = null;


    const userMessage: ExtendedChatMessage = {
      type: 'user',
      content: message,
      formattedContent: message,
    };
    this.messages.push(userMessage);
    this.chatStateService.addMessage(userMessage);

    this.cdr.detectChanges();
    this.scrollToBottom();

    const context = JSON.stringify(
      this.chatStateService.getMessages()
        .filter((msg) => msg.type === 'user')
        .slice(-2)
        .map((msg) => ({
          type: msg.type,
          content: msg.content,
        }))
    );

    this.logger.debug('Context for API call:', context);

    const selectedCollectionObj = this.collections.find(
      (collection) => collection.id === this.selectedCollection
    );
    if (!selectedCollectionObj) {
      this.logger.error(`Collection with id ${this.selectedCollection} not found`);
      this.isLoading = false;
      return;
    }

    const chatModel: ChatModel = this.getChatModelFromString(
      this.selectedModel
    );

    this.subscription.add(
      this.apiService
        .sendChatMessage(
          message,
          chatModel,
          selectedCollectionObj.embeddingModel,
          selectedCollectionObj.id,
          context
        )
        .subscribe({
          next: (response) => {
            this.isLoading = false;

            const formattedResponse = this.formatResponse(response);
            const botMessage: ExtendedChatMessage = {
              type: 'bot',
              content: response,
              formattedContent: '',
              isLoading: false,
            };
            this.messages.push(botMessage);

            this.typewriterEffect(formattedResponse, botMessage);
          },
          error: (err) => {
            this.isLoading = false;
            this.errorMessage = err.message ?? 'Failed to send message';
            this.logger.error('Chat error:', err);
          },
        })
    );
  }

  private getChatModelFromString(modelKey: string): ChatModel {
    const model = ChatModel[modelKey as keyof typeof ChatModel];
    if (model === undefined) {
      this.logger.warn(`Unknown chat model key: ${modelKey}, defaulting to Azure`);
      return ChatModel.Azure;
    }
    return model;
  }

  private typewriterEffect(fullContent: string, message: ChatMessage): void {

    const htmlRegex =
      /<(a|table|img|pre|div|p|span|strong|em|ul|ol|li|tr|td|th|thead|tbody|tfoot)[^>]*>[\s\S]*?<\/\1>|<(img|br|hr)[^>]*\/?>/gi;
    const parts: { text: string; isHtml: boolean }[] = [];
    let lastIndex = 0;

    while (true) {
      const match = htmlRegex.exec(fullContent);
      if (!match) break;
      const textBefore = fullContent.slice(lastIndex, match.index);
      if (textBefore) {
        parts.push({ text: textBefore, isHtml: false });
      }
      parts.push({ text: match[0], isHtml: true });
      lastIndex = htmlRegex.lastIndex;
    }
    const remainingText = fullContent.slice(lastIndex);
    if (remainingText) {
      parts.push({ text: remainingText, isHtml: false });
    }

    let currentPartIndex = 0;
    let currentCharIndex = 0;
    let currentContent = '';

    const interval = setInterval(() => {
      if (currentPartIndex >= parts.length) {
        clearInterval(interval);
        this.chatStateService.addMessage({
          ...message,
          formattedContent: currentContent,
        });
        this.scrollToBottom();
        this.highlightCode();
        return;
      }

      const currentPart = parts[currentPartIndex];
      if (currentPart.isHtml) {
        currentContent += currentPart.text;
        message.formattedContent = currentContent;
        currentPartIndex++;
      } else if (currentCharIndex < currentPart.text.length) {
        currentContent += currentPart.text[currentCharIndex];
        message.formattedContent = currentContent;
        currentCharIndex++;
      } else {
        currentPartIndex++;
        currentCharIndex = 0;
      }

      this.cdr.detectChanges();
      this.scrollToBottom();
    }, 50);
  }

  public formatResponse(response: string): string {
    let formatted = response;
    formatted = formatted.replace(
      /<li>\s*<p>(.*?)<\/p>\s*<\/li>/gi,
      '<li>$1</li>'
    );
    formatted = formatted.replace(/\n\s*\n+/g, '\n\n').trim();
    formatted = formatted.replace(/src='\//g, `src='${this.baseUrl}/`);
    const standaloneUrlRegex = /(?:[[()])(https?:\/\/[^\s\])]+)(?:[\])])/g;
    formatted = formatted.replace(standaloneUrlRegex, (_, url) => {
      const fullUrl =
        this.baseUrl && !/^https?:\/\//i.test(url)
          ? `${this.baseUrl}${url}`
          : url;
      return `<a href="${fullUrl}" target="_blank">LINK</a>`;
    });

    const urlWithPrefixRegex1 = /\[Url: (https?:\/\/[^\s\]]+)\]/g;
    formatted = formatted.replace(urlWithPrefixRegex1, (_, url) => {
      const fullUrl =
        this.baseUrl && !/^https?:\/\//i.test(url)
          ? `${this.baseUrl}${url}`
          : url;
      return `[<a href="${fullUrl}" target="_blank">LINK</a>]`;
    });

    return formatted;
  }

  onCollectionActionChange() {
    if (!this.selectedCollection) {
      this.messages.push({
        type: 'bot',
        content: 'Please select a Collection first!',
        formattedContent: 'Please select a Collection first!',
      });
      return;
    }

    const userInputElement = this.userInput?.nativeElement as HTMLInputElement;
    if (!userInputElement) return;

    switch (this.selectedCollectionAction) {
      case 'summarize':
        userInputElement.value = `Summarizing Collection`;
        this.sendMessage();
        break;
      case 'recent':
        userInputElement.value = `Listing recently updated documents`;
        this.sendMessage();
        break;
      case 'duplicates':
        userInputElement.value = `Finding documents with duplications`;
        this.sendMessage();
        break;
      default:
        break;
    }
    this.hasInteracted = true;
    this.chatStateService.setHasInteracted(true);
  }
}
