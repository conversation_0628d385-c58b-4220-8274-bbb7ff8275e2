{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-tsx.js"], "sourcesContent": ["(function (Prism) {\n  var typescript = Prism.util.clone(Prism.languages.typescript);\n  Prism.languages.tsx = Prism.languages.extend('jsx', typescript);\n\n  // doesn't work with TS because TS is too complex\n  delete Prism.languages.tsx['parameter'];\n  delete Prism.languages.tsx['literal-property'];\n\n  // This will prevent collisions between TSX tags and TS generic types.\n  // Idea by https://github.com/karlhorky\n  // Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-710666928\n  var tag = Prism.languages.tsx.tag;\n  tag.pattern = RegExp(/(^|[^\\w$]|(?=<\\/))/.source + '(?:' + tag.pattern.source + ')', tag.pattern.flags);\n  tag.lookbehind = true;\n})(Prism);"], "mappings": ";CAAC,SAAUA,QAAO;AAChB,MAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAC5D,EAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,OAAO,UAAU;AAG9D,SAAOA,OAAM,UAAU,IAAI,WAAW;AACtC,SAAOA,OAAM,UAAU,IAAI,kBAAkB;AAK7C,MAAI,MAAMA,OAAM,UAAU,IAAI;AAC9B,MAAI,UAAU,OAAO,qBAAqB,SAAS,QAAQ,IAAI,QAAQ,SAAS,KAAK,IAAI,QAAQ,KAAK;AACtG,MAAI,aAAa;AACnB,GAAG,KAAK;", "names": ["Prism"]}