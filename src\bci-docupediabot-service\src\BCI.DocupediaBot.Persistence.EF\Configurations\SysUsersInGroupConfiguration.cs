﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class SysUsersInGroupConfiguration : IEntityTypeConfiguration<SysUsersInGroup>
  {
    public void Configure(EntityTypeBuilder<SysUsersInGroup> entityBuilder)
    {
      entityBuilder.ToTable(nameof(SysUsersInGroup));


      entityBuilder.HasIndex(x => x.UserId);
      entityBuilder.HasIndex(x => x.GroupId);
      entityBuilder.HasIndex(x => new { x.UserId, x.GroupId }).IsUnique();


      entityBuilder.HasIndex(x => new { x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.UserId, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.GroupId, x.TenantId, x.Is<PERSON>eleted });
    }
  }
}
