﻿using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;


namespace BCI.DocupediaBot.Application.Services.Page
{
  public interface IPagesInCollectionService
	{
		Task<List<Guid>> QueryPageIdsByCollectionIdAsync(Guid collectionId);
		Task<List<PagesInCollection>> QueryMappingsByCollectionIdAsync(Guid collectionId);
		Task<ResponseResult> DeleteMappingAsync(Guid pageId, Guid collectionId);
		Task<ResponseResult> UpdateMappingAsync(Guid pageId, Guid collectionId, bool isEmbedding);
		Task<ResponseResult> DeleteMappingsByCollectionIdAsync(Guid collectionId);
		Task<bool> GetEmbeddingAsync(Guid pageId, Guid collectionId);
		Task AddMappingAsync(Guid collectionId, Guid pageId);

	}
}
