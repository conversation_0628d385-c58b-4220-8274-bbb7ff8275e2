<nav mat-tab-nav-bar [tabPanel]="tabPanel">
  <div
    mat-tab-link
    routerLink="deepdoc-bot"
    routerLinkActive
    #rla="routerLinkActive"
    (click)="activeLink = 'deepdoc-bot'"
    [active]="activeLink === 'deepdoc-bot'"
  >
    Chat with Bot
  </div>
  <div
    mat-tab-link
    routerLink="deepdoc-import"
    routerLinkActive
    #rla="routerLinkActive"
    (click)="activeLink = 'deepdoc-import'"
    [active]="activeLink === 'deepdoc-import'"
  >
    Content Management
  </div>
</nav>
<div class="router-content">
  <router-outlet #tabPanel></router-outlet>
</div>
