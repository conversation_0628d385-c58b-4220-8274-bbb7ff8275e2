{"version": 3, "sources": ["../../../../../../node_modules/prismjs/components/prism-json.js"], "sourcesContent": ["// https://www.json.org/json-en.html\nPrism.languages.json = {\n  'property': {\n    pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n    lookbehind: true,\n    greedy: true\n  },\n  'string': {\n    pattern: /(^|[^\\\\])\"(?:\\\\.|[^\\\\\"\\r\\n])*\"(?!\\s*:)/,\n    lookbehind: true,\n    greedy: true\n  },\n  'comment': {\n    pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    greedy: true\n  },\n  'number': /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n  'punctuation': /[{}[\\],]/,\n  'operator': /:/,\n  'boolean': /\\b(?:false|true)\\b/,\n  'null': {\n    pattern: /\\bnull\\b/,\n    alias: 'keyword'\n  }\n};\nPrism.languages.webmanifest = Prism.languages.json;"], "mappings": ";AACA,MAAM,UAAU,OAAO;AAAA,EACrB,YAAY;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,EACV;AAAA,EACA,WAAW;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AAAA,EACA,UAAU;AAAA,EACV,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AACF;AACA,MAAM,UAAU,cAAc,MAAM,UAAU;", "names": []}