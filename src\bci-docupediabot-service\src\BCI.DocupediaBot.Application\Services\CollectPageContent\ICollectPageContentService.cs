﻿using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Infrastructure.AppService;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.CollectPageContent
{
  public interface ICollectPageContentService : IAppService
  {
		public Task CollectPageContentRecursiveAsync(PageResponseDTO pageDto, string userToken, string baseUrl);
		public Task<ContentDocupediaResponseDTO> GetPageContentAsync(string apiUrl, string sourceId, string userToken);
		public Task<int> GetPageContentVersionAsync(string apiUrl, string sourceId, string userToken);
	}
}
