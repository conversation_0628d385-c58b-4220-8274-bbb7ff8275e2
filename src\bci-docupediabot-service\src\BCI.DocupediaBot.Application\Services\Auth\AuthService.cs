﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Auth;
using BCI.DocupediaBot.Application.Contracts.Dtos.SysUser;
using BCI.DocupediaBot.Application.Services.SysUser;
using BCI.DocupediaBot.Infrastructure.Abstractions;
using BCI.DocupediaBot.Infrastructure.Configuration;
using Bosch.Foundation.Abstractions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.DirectoryServices;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.Auth
{
  public class AuthService : IAuthService
  {
    private readonly IOptions<JwtTokenOptions> _jwtTokenOptions;
    private readonly ILogger<AuthService> _logger;
    private readonly ITenantAccessor _tenantAccessor;
    private readonly ICurrentUserAccessor _currentUserAccessor;
    private readonly IMapper _mapper;
    private readonly ISysUserService _sysUserService;
    private readonly IMemoryCache _memoryCache;


    public AuthService(
      ILogger<AuthService> logger,
      ITenantAccessor tenantAccessor,
      IOptions<JwtTokenOptions> jwtTokenOptions,
      ICurrentUserAccessor currentUserAccessor,
      IMapper mapper,
      ISysUserService sysUserService,
      IMemoryCache memoryCache)
    {
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
      _tenantAccessor = tenantAccessor;
      _jwtTokenOptions = jwtTokenOptions;
      _currentUserAccessor = currentUserAccessor;
      _mapper = mapper;
      _sysUserService = sysUserService;
      _memoryCache = memoryCache;
    }

    public async Task<LoginSuccessDTO> LoginAsync(LoginDTO dto)
    {
      if (string.IsNullOrEmpty("LDAP://10.187.33.234"))
      {
        _logger.LogWarning("LDAP URL is not configured.");
        return null;
      }

      var ldapUrlList = "LDAP://10.187.33.234".Split(';');
      foreach (var ldapUrl in ldapUrlList)
      {
        var entry = new DirectoryEntry(ldapUrl);
        var search = new DirectorySearcher(entry)
        {
          Filter = $"(&(objectClass=user)(sAMAccountName={dto.UserId}))",
          SearchScope = SearchScope.Subtree
        };

        string[] requiredProperties = { "givenName", "sn", "mail", "displayName", "department" };
        foreach (var p in requiredProperties)
        {
          search.PropertiesToLoad.Add(p);
        }

        var result = search.FindOne();
        if (result != null)
        {
          try
          {
            using (var objUserEntry = new DirectoryEntry(ldapUrl, dto.UserId, dto.Password))
            {
              var nativeObject = objUserEntry.NativeObject;

              var userInfo = new Dictionary<string, string>();
              foreach (var prop in requiredProperties)
              {
                if (result.Properties.Contains(prop) && result.Properties[prop].Count > 0)
                {
                  userInfo[prop] = result.Properties[prop][0].ToString();
                }
              }

              var sysUserAddDTO = new SysUserAddDTO
              {
                UserNTAccount = dto.UserId,
                GivenName = userInfo.ContainsKey("givenName") ? userInfo["givenName"] : string.Empty,
                SN = userInfo.ContainsKey("sn") ? userInfo["sn"] : string.Empty,
                Mail = userInfo.ContainsKey("mail") ? userInfo["mail"] : string.Empty,
                UserName = userInfo.ContainsKey("displayName") ? userInfo["displayName"] : string.Empty,
                Department = userInfo.ContainsKey("department") ? userInfo["department"] : string.Empty,
                Status = 1
              };

              SysUserResponseDTO sysUserResponseDTO;


              var existingUser = await _sysUserService.QueryUserByNTAccountAsync(dto.UserId);

              if (existingUser != null)
              {
                _logger.LogInformation("User with NTAccount {UserNTAccount} already exists.", dto.UserId);
                sysUserResponseDTO = existingUser;
              }
              else
              {

                var addResult = await _sysUserService.AddUserAsync(sysUserAddDTO);
                if (!addResult.IsSuccess)
                {
                  _logger.LogWarning("Failed to add user with NTAccount {UserNTAccount}: {Error}", dto.UserId, addResult.Msg);
                  return null;
                }


                if (addResult.Data is Guid newUserId && newUserId != Guid.Empty)
                {

                  var newUser = await _sysUserService.QueryUserWithGroupsAsync(newUserId);
                  if (newUser != null)
                  {
                    sysUserResponseDTO = newUser;
                    _logger.LogInformation("Successfully retrieved new user with ID: {UserId}", sysUserResponseDTO.Id);
                  }
                  else
                  {
                    _logger.LogError("Failed to retrieve newly created user with ID: {UserId}", newUserId);
                    return null;
                  }
                }
                else
                {
                  _logger.LogError("Failed to get valid user ID from add result. NTAccount: {UserNTAccount}", dto.UserId);
                  return null;
                }
              }

              var token = CreateToken(sysUserAddDTO);
              var refreshToken = GeneratePasswordKey();

              // Store refresh token with user info in cache for 7 days
              var cacheKey = $"refresh_token_{refreshToken}";
              var cacheOptions = new MemoryCacheEntryOptions
              {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(7)
              };
              _memoryCache.Set(cacheKey, sysUserAddDTO, cacheOptions);

              return new LoginSuccessDTO
              {
                Token = token,
                RefreshToken = refreshToken,
                sysUserResponseDTO = sysUserResponseDTO
              };
            }
          }
          catch (DirectoryServicesCOMException)
          {
            _logger.LogWarning("Invalid password for user: {Username}", dto.UserId);
            return null;
          }
          catch (Exception ex)
          {
            _logger.LogError(ex, "LDAP validation error for user: {Username}", dto.UserId);
            return null;
          }
        }
      }

      _logger.LogWarning("User {Username} not found in LDAP.", dto.UserId);
      return null;
    }

    public async Task<RefreshTokenResponseDTO> RefreshTokenAsync(RefreshTokenRequestDTO dto)
    {
      try
      {
        var cacheKey = $"refresh_token_{dto.RefreshToken}";

        if (_memoryCache.TryGetValue(cacheKey, out SysUserAddDTO cachedUser))
        {
          // Generate new tokens
          var newAccessToken = CreateToken(cachedUser);
          var newRefreshToken = GeneratePasswordKey();

          // Remove old refresh token from cache
          _memoryCache.Remove(cacheKey);

          // Store new refresh token with user info in cache for 7 days
          var newCacheKey = $"refresh_token_{newRefreshToken}";
          var cacheOptions = new MemoryCacheEntryOptions
          {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromDays(7)
          };
          _memoryCache.Set(newCacheKey, cachedUser, cacheOptions);

          _logger.LogInformation("Token refreshed successfully for user: {Username}", cachedUser.UserNTAccount);

          return new RefreshTokenResponseDTO
          {
            Token = newAccessToken,
            RefreshToken = newRefreshToken
          };
        }

        _logger.LogWarning("Invalid or expired refresh token");
        return null;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Error refreshing token");
        return null;
      }
    }

    private static string GeneratePasswordKey()
    {
      var byes = new byte[32];
      Guid.NewGuid().ToByteArray().CopyTo(byes, 0);
      Guid.NewGuid().ToByteArray().CopyTo(byes, 16);
      return Convert.ToBase64String(byes);
    }

    private string CreateToken(SysUserAddDTO user)
    {
      var claims = new List<Claim>
      {
          new("userid", user.UserNTAccount ?? string.Empty),
          new("username", user.UserName ?? string.Empty),
          new("givenname", user.GivenName ?? string.Empty),
          new("surname", user.SN ?? string.Empty),
          new("mail", user.Mail ?? string.Empty),
          new("department", user.Department ?? string.Empty)
      };

      var options = _jwtTokenOptions.Value;
      var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(options.Secret));
      var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

      var jwtToken = new JwtSecurityToken(options.Issuer, null, claims,
          expires: DateTime.UtcNow.AddMinutes(options.AccessExpiration),
          signingCredentials: credentials);

      return new JwtSecurityTokenHandler().WriteToken(jwtToken);
    }
  }
}