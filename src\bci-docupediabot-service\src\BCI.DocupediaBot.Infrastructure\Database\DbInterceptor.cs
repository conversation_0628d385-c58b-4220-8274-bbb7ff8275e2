﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using System.Threading;
using BCI.DocupediaBot.Infrastructure.Entity;
using BCI.DocupediaBot.Infrastructure.Exceptions;

namespace BCI.DocupediaBot.Infrastructure.Database
{
  [ExcludeFromCodeCoverage]
  internal class DbInterceptor : SaveChangesInterceptor
  {
    private readonly string user;
    private readonly string tenantId;

    public DbInterceptor(string user, string tenantId)
    {
      this.user = user;
      this.tenantId = tenantId;
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(DbContextEventData eventData, InterceptionResult<int> result, CancellationToken cancellationToken = default)
    {
      AutoUpdateAuditField(eventData);
      AutoUpdateTenantIdField(eventData);
      AutoUpdateSoftDeleteField(eventData);

      return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
      AutoUpdateAuditField(eventData);
      AutoUpdateTenantIdField(eventData);
      AutoUpdateSoftDeleteField(eventData);

      return base.SavingChanges(eventData, result);
    }

    private void AutoUpdateAuditField(DbContextEventData eventData)
    {
      foreach (var entity in eventData.Context.ChangeTracker.Entries())
      {
        var auditEntity = entity.Entity as IAuditEntity;
        switch (entity.State)
        {
          case EntityState.Unchanged:
            break;
          case EntityState.Added:
            if (auditEntity != null)
            {
              auditEntity.Creator = user;
              auditEntity.CreationTime = DateTimeOffset.Now.DateTime;
            }
            break;
          case EntityState.Modified:
            if (auditEntity != null)
            {
              auditEntity.Modifier = user;
              auditEntity.ModificationTime = DateTimeOffset.Now.DateTime;
            }
            break;

        }

      }
    }

    private void AutoUpdateTenantIdField(DbContextEventData eventData)
    {
      foreach (var entity in eventData.Context.ChangeTracker.Entries())
      {
        var tenantEntity = entity.Entity as ITenantEntity;
        switch (entity.State)
        {
          case EntityState.Unchanged:
            break;
          case EntityState.Added:
            if (tenantEntity != null && string.IsNullOrWhiteSpace(tenantEntity.TenantId))
            {

              tenantEntity.TenantId = tenantId;
            }
            break;
          case EntityState.Modified:
            if (tenantEntity != null && string.IsNullOrWhiteSpace(tenantEntity.TenantId))
            {
              entity.CurrentValues.TryGetValue("Id", out string id);
              throw new TenantIdEmptyException($"The entity({id})'s tenant id is empty, stop updating.");
            }
            break;

        }

      }
    }

    private static void AutoUpdateSoftDeleteField(DbContextEventData eventData)
    {
      foreach (var entity in eventData.Context.ChangeTracker.Entries())
      {
        if (entity.State == EntityState.Unchanged)
        {
          continue;
        }

        if (entity.State == EntityState.Deleted)
        {
          var softDeleteEntity = entity.Entity as ISoftDeleteEntity;
          if (softDeleteEntity != null)
          {
            entity.State = EntityState.Modified;
            softDeleteEntity.IsDeleted = true;
          }
        }
      }
    }
  }
}
