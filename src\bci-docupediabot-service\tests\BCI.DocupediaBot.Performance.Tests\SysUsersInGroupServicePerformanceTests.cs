using BCI.DocupediaBot.Application.Services.Cache;
using BCI.DocupediaBot.Application.Services.SysUsersInGroup;
using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;

namespace BCI.DocupediaBot.Performance.Tests
{



  public class SysUsersInGroupServicePerformanceTests
  {
    private readonly ITestOutputHelper _output;
    private readonly Mock<ISysUsersInGroupRepository> _mockRepository;
    private readonly Mock<ILogger<SysUsersInGroupService>> _mockLogger;
    private readonly Mock<ILogger<UserGroupCacheService>> _mockCacheLogger;
    private readonly IMemoryCache _memoryCache;
    private readonly IUserGroupCacheService _cacheService;

    public SysUsersInGroupServicePerformanceTests(ITestOutputHelper output)
    {
      _output = output;
      _mockRepository = new Mock<ISysUsersInGroupRepository>();
      _mockLogger = new Mock<ILogger<SysUsersInGroupService>>();
      _mockCacheLogger = new Mock<ILogger<UserGroupCacheService>>();
      _memoryCache = new MemoryCache(new MemoryCacheOptions());
      _cacheService = new UserGroupCacheService(_memoryCache, _mockCacheLogger.Object);
    }

    [Fact]
    public async Task QueryGroupIdsByUserIdAsync_WithCache_ShouldBeFasterThanWithoutCache()
    {

      var userId = Guid.NewGuid();
      var groupIds = Enumerable.Range(1, 10).Select(_ => Guid.NewGuid()).ToList();

      _mockRepository.Setup(r => r.GetGroupIdsByUserIdAsync(userId))
        .ReturnsAsync(groupIds)
        .Callback(() => Task.Delay(50));

      var service = new SysUsersInGroupService(_mockRepository.Object, _cacheService, _mockLogger.Object);


      var sw1 = Stopwatch.StartNew();
      var result1 = await service.QueryGroupIdsByUserIdAsync(userId);
      sw1.Stop();


      var sw2 = Stopwatch.StartNew();
      var result2 = await service.QueryGroupIdsByUserIdAsync(userId);
      sw2.Stop();


      Assert.Equal(groupIds.Count, result1.Count);
      Assert.Equal(groupIds.Count, result2.Count);
      Assert.True(sw2.ElapsedMilliseconds < sw1.ElapsedMilliseconds,
        $"Cached call ({sw2.ElapsedMilliseconds}ms) should be faster than non-cached call ({sw1.ElapsedMilliseconds}ms)");

      _output.WriteLine($"First call (no cache): {sw1.ElapsedMilliseconds}ms");
      _output.WriteLine($"Second call (with cache): {sw2.ElapsedMilliseconds}ms");
      _output.WriteLine($"Performance improvement: {((double)(sw1.ElapsedMilliseconds - sw2.ElapsedMilliseconds) / sw1.ElapsedMilliseconds * 100):F1}%");
    }

    [Fact]
    public async Task QueryGroupIdsByUserIdsBatchAsync_ShouldBeMoreEfficientThanIndividualCalls()
    {

      var userIds = Enumerable.Range(1, 100).Select(_ => Guid.NewGuid()).ToList();
      var batchResult = userIds.ToDictionary(id => id, id => Enumerable.Range(1, 5).Select(_ => Guid.NewGuid()).ToList());

      _mockRepository.Setup(r => r.GetGroupIdsByUserIdsBatchAsync(It.IsAny<IEnumerable<Guid>>()))
        .ReturnsAsync(batchResult);

      _mockRepository.Setup(r => r.GetGroupIdsByUserIdAsync(It.IsAny<Guid>()))
        .Returns<Guid>(id => Task.FromResult(batchResult.GetValueOrDefault(id, new List<Guid>())));

      var service = new SysUsersInGroupService(_mockRepository.Object, _cacheService, _mockLogger.Object);


      var sw1 = Stopwatch.StartNew();
      var batchResults = await service.QueryGroupIdsByUserIdsBatchAsync(userIds);
      sw1.Stop();


      var sw2 = Stopwatch.StartNew();
      var individualResults = new Dictionary<Guid, List<Guid>>();
      foreach (var userId in userIds)
      {
        individualResults[userId] = await service.QueryGroupIdsByUserIdAsync(userId);
      }
      sw2.Stop();


      Assert.Equal(userIds.Count, batchResults.Count);
      Assert.Equal(userIds.Count, individualResults.Count);
      Assert.True(sw1.ElapsedMilliseconds < sw2.ElapsedMilliseconds,
        $"Batch call ({sw1.ElapsedMilliseconds}ms) should be faster than individual calls ({sw2.ElapsedMilliseconds}ms)");

      _output.WriteLine($"Batch call: {sw1.ElapsedMilliseconds}ms");
      _output.WriteLine($"Individual calls: {sw2.ElapsedMilliseconds}ms");
      _output.WriteLine($"Performance improvement: {((double)(sw2.ElapsedMilliseconds - sw1.ElapsedMilliseconds) / sw2.ElapsedMilliseconds * 100):F1}%");
    }

    [Fact]
    public async Task IsUserInGroupAsync_WithCache_ShouldReduceDatabaseCalls()
    {

      var userId = Guid.NewGuid();
      var groupId = Guid.NewGuid();
      var callCount = 0;

      _mockRepository.Setup(r => r.ExistsMappingAsync(userId, groupId))
        .ReturnsAsync(true)
        .Callback(() => callCount++);

      var service = new SysUsersInGroupService(_mockRepository.Object, _cacheService, _mockLogger.Object);


      var results = new List<bool>();
      for (int i = 0; i < 5; i++)
      {
        results.Add(await service.IsUserInGroupAsync(userId, groupId));
      }


      Assert.All(results, result => Assert.True(result));
      Assert.Equal(1, callCount);

      _output.WriteLine($"Database calls: {callCount} (expected: 1)");
      _output.WriteLine("Cache successfully reduced database calls from 5 to 1");
    }

    [Theory]
    [InlineData(10)]
    [InlineData(50)]
    [InlineData(100)]
    [InlineData(500)]
    public async Task BatchQueryPerformance_ShouldScaleWell(int userCount)
    {

      var userIds = Enumerable.Range(1, userCount).Select(_ => Guid.NewGuid()).ToList();
      var batchResult = userIds.ToDictionary(id => id, id => Enumerable.Range(1, 3).Select(_ => Guid.NewGuid()).ToList());

      _mockRepository.Setup(r => r.GetGroupIdsByUserIdsBatchAsync(It.IsAny<IEnumerable<Guid>>()))
        .ReturnsAsync(batchResult);

      var service = new SysUsersInGroupService(_mockRepository.Object, _cacheService, _mockLogger.Object);


      var sw = Stopwatch.StartNew();
      var results = await service.QueryGroupIdsByUserIdsBatchAsync(userIds);
      sw.Stop();


      Assert.Equal(userCount, results.Count);

      var avgTimePerUser = (double)sw.ElapsedMilliseconds / userCount;
      _output.WriteLine($"Users: {userCount}, Total time: {sw.ElapsedMilliseconds}ms, Avg per user: {avgTimePerUser:F2}ms");


      Assert.True(avgTimePerUser < 1.0, $"Average time per user ({avgTimePerUser:F2}ms) should be less than 1ms");
    }

    [Fact]
    public async Task CacheEviction_ShouldWorkCorrectly()
    {

      var userId = Guid.NewGuid();
      var groupId = Guid.NewGuid();
      var callCount = 0;

      _mockRepository.Setup(r => r.GetGroupIdsByUserIdAsync(userId))
        .ReturnsAsync(new List<Guid> { groupId })
        .Callback(() => callCount++);

      var service = new SysUsersInGroupService(_mockRepository.Object, _cacheService, _mockLogger.Object);


      await service.QueryGroupIdsByUserIdAsync(userId);
      Assert.Equal(1, callCount);


      await service.QueryGroupIdsByUserIdAsync(userId);
      Assert.Equal(1, callCount);


      await _cacheService.ClearUserCacheAsync(userId);


      await service.QueryGroupIdsByUserIdAsync(userId);
      Assert.Equal(2, callCount);

      _output.WriteLine("Cache eviction working correctly");
    }
  }
}
