import {
  ArrayDataSource,
  DataSource,
  SelectionModel,
  UniqueSelectionDispatcher,
  _DisposeViewRepeaterStrategy,
  _RecycleViewRepeaterStrategy,
  _VIEW_REPEATER_STRATEGY,
  _ViewRepeaterOperation,
  getMultipleValuesInSingleSelectionError,
  isDataSource
} from "./chunk-7UMYULAP.js";
import "./chunk-X3P73C3G.js";
import "./chunk-DWV65MH7.js";
import "./chunk-KXOMTDJ6.js";
import "./chunk-KRK546R7.js";
import "./chunk-Y4T55RDF.js";
export {
  ArrayDataSource,
  DataSource,
  SelectionModel,
  UniqueSelectionDispatcher,
  _DisposeViewRepeaterStrategy,
  _RecycleViewRepeaterStrategy,
  _VIEW_REPEATER_STRATEGY,
  _ViewRepeaterOperation,
  getMultipleValuesInSingleSelectionError,
  isDataSource
};
//# sourceMappingURL=@angular_cdk_collections.js.map
