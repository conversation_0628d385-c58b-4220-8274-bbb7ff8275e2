﻿using Microsoft.Extensions.Configuration;

namespace BCI.DocupediaBot.Infrastructure.Extensions
{
	public static class LdapUtility
	{
		private static IConfiguration _configuration;
		private static string _isEnableLdapLogin;
		private static string _ldapUrl;
		private static string _ldapBURegex;

		public static void Initialize(IConfiguration configuration)
		{
			_configuration = configuration;
		}

		public static string[] GetLDAPProperties()
		{
			return new[] {

                   "distinguishedname",

                   "cn",

                   "mailnickname",





                   "memberof",

                   "accountexpires",
									 "displayname",
									 "primarygroupid",



                   "objectcategory",



                   "useraccountcontrol",
									 "physicaldeliveryofficename",
									 "samaccountname",
									 "usercertificate",
									 "givename",
									 "mail",
									 "userparameters",
									 "adspath",
									 "homemta",




                   "name",

                   "legacychangedn",
									 "proxyaddress",
									 "department",
									 "userprincipalname",

                   "objectsid",
									 "sn",
									 "mdbusedefaults",
									 "telephonenumber",



                   "company",
									 "otherTelephone",
									 "givenName",
									 "mobile",
									 "description"
							 };
		}
		public static string[] GetSimpleLDAPProperties()
		{
			return new[] {
									 "displayname",
									 "sn",
									 "givenName",
									 "mobile",
									 "telephonenumber",
									 "mail",
									 "samaccountname",
									 "department"
							 };
		}

	}
}
