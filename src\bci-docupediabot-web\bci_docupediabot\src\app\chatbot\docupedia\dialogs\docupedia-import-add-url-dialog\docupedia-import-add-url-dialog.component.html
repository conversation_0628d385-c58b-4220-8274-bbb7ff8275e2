<bci-modal-component [title]="'Add New Page'" [closeIcon]="true" (closeHandler)="close()" class="modal">
  <p class="modal-subheading">Enter Confluence page URL to add to the collection. The page title will be automatically retrieved.</p>
  <form [formGroup]="pageForm" class="modal-form">
    <mat-form-field appearance="fill" class="full-width">
      <mat-label>Confluence URL</mat-label>
      <input matInput formControlName="url" required
             placeholder="Enter Confluence page URL" />
      <mat-error *ngIf="pageForm.get('url')?.hasError('required')">
        URL is required
      </mat-error>
      <mat-error *ngIf="pageForm.get('url')?.hasError('invalidUrl')">
        Please enter a valid URL
      </mat-error>
      <mat-error *ngIf="pageForm.get('url')?.hasError('invalidConfluenceDomain')">
        URL must be from inside-docupedia.bosch.com/confluence
      </mat-error>
      <mat-error *ngIf="pageForm.get('url')?.hasError('unsupportedUrlType')">
        Unsupported URL format. Please use Pretty URL (/display/SPACE/PageTitle) or PageId URL (/pages/viewpage.action?pageId=123456)
      </mat-error>
    </mat-form-field>

    <mat-checkbox formControlName="isIncludeChild" checked="true">Include Child Pages</mat-checkbox>
  </form>

  <div actions>
    <button bciPrimaryButton [disabled]="submitDisabled" (click)="save()">Save</button>
    <button bciSecondaryButton (click)="close()">Cancel</button>
  </div>
</bci-modal-component>
