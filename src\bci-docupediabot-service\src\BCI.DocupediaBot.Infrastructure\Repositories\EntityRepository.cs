﻿using BCI.DocupediaBot.Infrastructure.Entities;
using BCI.DocupediaBot.Infrastructure.IRepositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Infrastructure.Repositories
{
  public class EntityRepository<T> : IEntityRepository<T> where T : class, IEntity
  {
    private readonly DbContext _dbContext;
    public EntityRepository(DbContext dbContext)
    {
      _dbContext = dbContext;
    }

    public DbContext DbContext => _dbContext;

    public virtual async Task CreateAsync(T entity)
    {
      await _dbContext.Set<T>().AddAsync(entity);
      await _dbContext.SaveChangesAsync();
    }

    public virtual async Task UpdateAsync(T entity)
    {
      _dbContext.Set<T>().Attach(entity);
      _dbContext.Update(entity);
      await _dbContext.SaveChangesAsync();
    }

    public virtual async Task DeleteAsync(T entity)
    {
      _dbContext.Set<T>().Attach(entity);
      _dbContext.Set<T>().Remove(entity);
      await _dbContext.SaveChangesAsync();
    }

    public virtual Task<List<T>> GetAllAsync(bool noTracing = true)
    {
      IQueryable<T> query = _dbContext.Set<T>();
      if (noTracing)
      {
        query = query.AsNoTracking();
      }
      return query.ToListAsync();
    }

    public virtual async Task<T> GetAsync(object id)
    {
      var entity = await _dbContext.Set<T>().FindAsync(id);

      if (entity != null)
      {
        _dbContext.Entry(entity).State = EntityState.Detached;
      }

      return entity;
    }

    public virtual Task<List<T>> QueryAsync(Expression<Func<T, bool>> expression, bool noTracing = true)
    {
      IQueryable<T> query = _dbContext.Set<T>();
      if (noTracing)
      {
        query = query.AsNoTracking();
      }

      return query.Where(expression).ToListAsync();
    }

    public virtual Task<long> CountAsync(Expression<Func<T, bool>> expression, bool noTracing = true)
    {
      IQueryable<T> query = _dbContext.Set<T>();
      if (noTracing)
      {
        query = query.AsNoTracking();
      }

      return query.LongCountAsync(expression);
    }

    public virtual async Task<(List<T> Items, long TotalCount)> PageAsync(int? pageNumber, int? pageSize, Expression<Func<T, bool>> expression, bool noTracing = true)
    {
      ValidatePageParameters(pageNumber, pageSize);

      IQueryable<T> query = _dbContext.Set<T>();
      if (noTracing)
      {
        query = query.AsNoTracking();
      }

      query = query.Where(expression);
      var totalCount = await query.CountAsync();

      if (pageNumber.HasValue && pageSize.HasValue)
      {
        query = query.Skip((pageNumber.Value - 1) * pageSize.Value).Take(pageSize.Value);
      }

      var items = await query.ToListAsync();

      return (items, totalCount);
    }

    public virtual async Task CreateRangeAsync(IList<T> entities)
    {
      await _dbContext.Set<T>().AddRangeAsync(entities);
      await _dbContext.SaveChangesAsync();
    }

    public virtual async Task UpdateRangeAsync(IList<T> entities)
    {
      _dbContext.Set<T>().AttachRange(entities);
      _dbContext.UpdateRange(entities);
      await _dbContext.SaveChangesAsync();
    }

    public virtual async Task DeleteRangeAsync(IList<T> entities)
    {
      _dbContext.Set<T>().AttachRange(entities);
      _dbContext.Set<T>().RemoveRange(entities);
      await _dbContext.SaveChangesAsync();
    }

    private static void ValidatePageParameters(int? pageNumber, int? pageSize)
    {
      if (pageNumber.HasValue && pageSize.HasValue)
      {
        if (pageNumber < 1)
        {
          throw new ArgumentException("Can not less then 1", nameof(pageNumber));
        }
        if (pageSize < 1)
        {
          throw new ArgumentException("Can not less then 1", nameof(pageSize));
        }
      }
    }
  }
}
