{"items": [{"id": "urn.com.bosch.bci.entitytype.apis", "name": "API endpoint", "type": "api", "description": "The resouce of public API", "privileges": ["read", "add", "modify", "delete"]}, {"id": "health", "type": "urn:com:bosch:bci:operation", "name": "Health endpoint - Provides information about the service and its dependencies", "description": "Get service status via information", "privileges": ["execute"]}, {"id": "urn.com.bosch.bci.entitytype.moduleviews", "name": "views", "type": "view", "description": "The resource for the view/menu", "privileges": ["read"]}, {"description": "The resource of OSS Disclosure Document", "name": "Serivce OSS Disclosure", "id": "urn.com.bosch.bci.entitytype.disclosure", "type": "disclosure-document", "privileges": ["read"]}]}