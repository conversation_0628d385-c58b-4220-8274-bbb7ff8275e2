﻿using BCI.DocupediaBot.Application.Services.Cache;
using BCI.DocupediaBot.Domain.Dtos.SharedDto;
using BCI.DocupediaBot.Domain.Entities;
using BCI.DocupediaBot.Domain.IRepositories;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.SysUsersInGroup
{
  public class SysUsersInGroupService : ISysUsersInGroupService
  {
    private readonly ISysUsersInGroupRepository _sysUsersInGroupRepository;
    private readonly IUserGroupCacheService _cacheService;
    private readonly ILogger<SysUsersInGroupService> _logger;

    public SysUsersInGroupService(
      ISysUsersInGroupRepository sysUsersInGroupRepository,
      IUserGroupCacheService cacheService,
      ILogger<SysUsersInGroupService> logger)
    {
      _sysUsersInGroupRepository = sysUsersInGroupRepository ?? throw new ArgumentNullException(nameof(sysUsersInGroupRepository));
      _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
      _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ResponseResult> AddMappingAsync(Guid userId, Guid groupId)
    {
      _logger.LogInformation("Adding mapping between user {UserId} and group {GroupId}", userId, groupId);

      try
      {

        var exists = await _sysUsersInGroupRepository.ExistsMappingAsync(userId, groupId);

        if (exists)
        {
          _logger.LogWarning("Mapping already exists between user {UserId} and group {GroupId}", userId, groupId);
          return new ResponseResult { IsSuccess = true, Msg = "Mapping already exists." };
        }

        var mapping = new Domain.Entities.SysUsersInGroup
        {
          UserId = userId,
          GroupId = groupId
        };

        await _sysUsersInGroupRepository.CreateAsync(mapping);


        await _cacheService.ClearUserCacheAsync(userId);
        await _cacheService.ClearGroupCacheAsync(groupId);

        _logger.LogInformation("Mapping created successfully between user {UserId} and group {GroupId}", userId, groupId);
        return new ResponseResult { IsSuccess = true, Msg = "Mapping created successfully." };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to add mapping between user {UserId} and group {GroupId}", userId, groupId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to create mapping: {ex.Message}" };
      }
    }

    public async Task<ResponseResult> DeleteMappingAsync(Guid userId, Guid groupId)
    {
      _logger.LogInformation("Deleting mapping between user {UserId} and group {GroupId}", userId, groupId);

      try
      {
        var mappings = await _sysUsersInGroupRepository.QueryAsync(m => m.UserId == userId && m.GroupId == groupId);
        var mapping = mappings.FirstOrDefault();

        if (mapping == null)
        {
          _logger.LogWarning("No mapping found between user {UserId} and group {GroupId}", userId, groupId);
          return new ResponseResult { IsSuccess = true, Msg = "No mapping found to delete." };
        }

        await _sysUsersInGroupRepository.DeleteAsync(mapping);


        await _cacheService.ClearUserCacheAsync(userId);
        await _cacheService.ClearGroupCacheAsync(groupId);

        _logger.LogInformation("Mapping deleted successfully between user {UserId} and group {GroupId}", userId, groupId);
        return new ResponseResult { IsSuccess = true, Msg = "Mapping deleted successfully." };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete mapping between user {UserId} and group {GroupId}", userId, groupId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete mapping: {ex.Message}" };
      }
    }

    public async Task<ResponseResult> DeleteMappingsByGroupIdAsync(Guid groupId)
    {
      _logger.LogInformation("Deleting all mappings for group {GroupId}", groupId);

      try
      {
        var mappingsToDelete = await _sysUsersInGroupRepository.QueryAsync(m => m.GroupId == groupId);

        if (!mappingsToDelete.Any())
        {
          _logger.LogInformation("No mappings found for group {GroupId}", groupId);
          return new ResponseResult { IsSuccess = true, Msg = $"No mappings found to delete for group {groupId}." };
        }

        await _sysUsersInGroupRepository.DeleteRangeAsync(mappingsToDelete);


        await _cacheService.ClearGroupCacheAsync(groupId);

        foreach (var mapping in mappingsToDelete)
        {
          await _cacheService.ClearUserCacheAsync(mapping.UserId);
        }

        _logger.LogInformation("Deleted {Count} mappings for group {GroupId}", mappingsToDelete.Count, groupId);
        return new ResponseResult
        {
          IsSuccess = true,
          Msg = $"Deleted {mappingsToDelete.Count} user mappings from group {groupId}."
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete mappings for group {GroupId}", groupId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete group mappings: {ex.Message}" };
      }
    }

    public async Task<ResponseResult> DeleteMappingsByUserIdAsync(Guid userId)
    {
      _logger.LogInformation("Deleting all mappings for user {UserId}", userId);

      try
      {
        var mappingsToDelete = await _sysUsersInGroupRepository.QueryAsync(m => m.UserId == userId);

        if (!mappingsToDelete.Any())
        {
          _logger.LogInformation("No mappings found for user {UserId}", userId);
          return new ResponseResult { IsSuccess = true, Msg = $"No mappings found to delete for user {userId}." };
        }

        await _sysUsersInGroupRepository.DeleteRangeAsync(mappingsToDelete);


        await _cacheService.ClearUserCacheAsync(userId);

        foreach (var mapping in mappingsToDelete)
        {
          await _cacheService.ClearGroupCacheAsync(mapping.GroupId);
        }

        _logger.LogInformation("Deleted {Count} mappings for user {UserId}", mappingsToDelete.Count, userId);
        return new ResponseResult
        {
          IsSuccess = true,
          Msg = $"Deleted {mappingsToDelete.Count} group mappings from user {userId}."
        };
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to delete mappings for user {UserId}", userId);
        return new ResponseResult { IsSuccess = false, Msg = $"Failed to delete user mappings: {ex.Message}" };
      }
    }

    public async Task<List<Guid>> QueryGroupIdsByUserIdAsync(Guid userId)
    {
      _logger.LogInformation("Querying group IDs for user {UserId}", userId);

      try
      {

        var cachedGroupIds = await _cacheService.GetUserGroupsAsync(userId);
        if (cachedGroupIds != null)
        {
          _logger.LogDebug("Found {Count} groups for user {UserId} from cache", cachedGroupIds.Count, userId);
          return cachedGroupIds;
        }


        var groupIds = await _sysUsersInGroupRepository.GetGroupIdsByUserIdAsync(userId);


        await _cacheService.SetUserGroupsAsync(userId, groupIds);

        _logger.LogInformation("Found {Count} groups for user {UserId} from database", groupIds.Count, userId);
        return groupIds;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query group IDs for user {UserId}", userId);
        return new List<Guid>();
      }
    }

    public async Task<List<Guid>> QueryUserIdsByGroupIdAsync(Guid groupId)
    {
      _logger.LogInformation("Querying user IDs for group {GroupId}", groupId);

      try
      {

        var cachedUserIds = await _cacheService.GetGroupUsersAsync(groupId);
        if (cachedUserIds != null)
        {
          _logger.LogDebug("Found {Count} users for group {GroupId} from cache", cachedUserIds.Count, groupId);
          return cachedUserIds;
        }


        var userIds = await _sysUsersInGroupRepository.GetUserIdsByGroupIdAsync(groupId);


        await _cacheService.SetGroupUsersAsync(groupId, userIds);

        _logger.LogInformation("Found {Count} users for group {GroupId} from database", userIds.Count, groupId);
        return userIds;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to query user IDs for group {GroupId}", groupId);
        return new List<Guid>();
      }
    }


    public async Task<Dictionary<Guid, List<Guid>>> QueryGroupIdsByUserIdsBatchAsync(IEnumerable<Guid> userIds)
    {
      _logger.LogInformation("Batch querying group IDs for {Count} users", userIds.Count());

      try
      {
        var userIdList = userIds.ToList();


        var cachedResults = await _cacheService.GetUserGroupsBatchAsync(userIdList);
        var missingUserIds = userIdList.Where(id => !cachedResults.ContainsKey(id)).ToList();

        if (!missingUserIds.Any())
        {
          _logger.LogDebug("All {Count} users found in cache", userIdList.Count);
          return cachedResults;
        }


        var dbResults = await _sysUsersInGroupRepository.GetGroupIdsByUserIdsBatchAsync(missingUserIds);


        await _cacheService.SetUserGroupsBatchAsync(dbResults);


        foreach (var kvp in dbResults)
        {
          cachedResults[kvp.Key] = kvp.Value;
        }

        _logger.LogInformation("Batch query completed for {Count} users ({CachedCount} from cache, {DbCount} from database)",
          userIdList.Count, userIdList.Count - missingUserIds.Count, missingUserIds.Count);

        return cachedResults;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to batch query group IDs for users");
        return new Dictionary<Guid, List<Guid>>();
      }
    }

    public async Task<Dictionary<Guid, List<Guid>>> QueryUserIdsByGroupIdsBatchAsync(IEnumerable<Guid> groupIds)
    {
      _logger.LogInformation("Batch querying user IDs for {Count} groups", groupIds.Count());

      try
      {
        var groupIdList = groupIds.ToList();


        var cachedResults = await _cacheService.GetGroupUsersBatchAsync(groupIdList);
        var missingGroupIds = groupIdList.Where(id => !cachedResults.ContainsKey(id)).ToList();

        if (!missingGroupIds.Any())
        {
          _logger.LogDebug("All {Count} groups found in cache", groupIdList.Count);
          return cachedResults;
        }


        var dbResults = await _sysUsersInGroupRepository.GetUserIdsByGroupIdsBatchAsync(missingGroupIds);


        await _cacheService.SetGroupUsersBatchAsync(dbResults);


        foreach (var kvp in dbResults)
        {
          cachedResults[kvp.Key] = kvp.Value;
        }

        _logger.LogInformation("Batch query completed for {Count} groups ({CachedCount} from cache, {DbCount} from database)",
          groupIdList.Count, groupIdList.Count - missingGroupIds.Count, missingGroupIds.Count);

        return cachedResults;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to batch query user IDs for groups");
        return new Dictionary<Guid, List<Guid>>();
      }
    }

    public async Task<List<Guid>> QueryUserIdsByGroupIdsFlattenAsync(IEnumerable<Guid> groupIds)
    {
      _logger.LogInformation("Flatten querying user IDs for {Count} groups", groupIds.Count());

      try
      {
        var groupIdList = groupIds.ToList();
        if (!groupIdList.Any())
        {
          return new List<Guid>();
        }

        var mappings = await _sysUsersInGroupRepository.QueryAsync(m => groupIdList.Contains(m.GroupId));
        var userIds = mappings.Select(m => m.UserId).Distinct().ToList();

        _logger.LogInformation("Found {Count} unique users across {GroupCount} groups", userIds.Count, groupIdList.Count);
        return userIds;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to flatten query user IDs for groups");
        return new List<Guid>();
      }
    }

    public async Task<List<Guid>> QueryGroupIdsByUserIdsFlattenAsync(IEnumerable<Guid> userIds)
    {
      _logger.LogInformation("Flatten querying group IDs for {Count} users", userIds.Count());

      try
      {
        var userIdList = userIds.ToList();
        if (!userIdList.Any())
        {
          return new List<Guid>();
        }

        var mappings = await _sysUsersInGroupRepository.QueryAsync(m => userIdList.Contains(m.UserId));
        var groupIds = mappings.Select(m => m.GroupId).Distinct().ToList();

        _logger.LogInformation("Found {Count} unique groups across {UserCount} users", groupIds.Count, userIdList.Count);
        return groupIds;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to flatten query group IDs for users");
        return new List<Guid>();
      }
    }

    public async Task<bool> IsUserInGroupAsync(Guid userId, Guid groupId)
    {
      _logger.LogInformation("Checking if user {UserId} is in group {GroupId}", userId, groupId);

      try
      {

        var cachedResult = await _cacheService.IsUserInGroupAsync(userId, groupId);
        if (cachedResult.HasValue)
        {
          _logger.LogDebug("User {UserId} is {Status} in group {GroupId} (from cache)",
            userId, cachedResult.Value ? "present" : "not present", groupId);
          return cachedResult.Value;
        }


        var exists = await _sysUsersInGroupRepository.ExistsMappingAsync(userId, groupId);


        await _cacheService.SetUserInGroupAsync(userId, groupId, exists);

        _logger.LogInformation("User {UserId} is {Status} in group {GroupId} (from database)",
          userId, exists ? "present" : "not present", groupId);
        return exists;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to check if user {UserId} is in group {GroupId}", userId, groupId);
        return false;
      }
    }

    public async Task<bool> HasCommonGroupAsync(Guid userId1, Guid userId2)
    {
      _logger.LogInformation("Checking if users {UserId1} and {UserId2} have common groups", userId1, userId2);

      try
      {
        var user1Groups = await QueryGroupIdsByUserIdAsync(userId1);
        var user2Groups = await QueryGroupIdsByUserIdAsync(userId2);

        var hasCommon = user1Groups.Any(groupId => user2Groups.Contains(groupId));

        _logger.LogInformation("Users {UserId1} and {UserId2} {Status} common groups", userId1, userId2, hasCommon ? "have" : "don't have");
        return hasCommon;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "Failed to check common groups for users {UserId1} and {UserId2}", userId1, userId2);
        return false;
      }
    }
  }
}