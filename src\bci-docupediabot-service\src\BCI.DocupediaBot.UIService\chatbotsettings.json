﻿{
  "Chatbot": {
    "SpaDevServerUrl": "http://localhost:4200",
    "VectorDB": {
      "Active": "QdrantDev",
      "Instances": {
        "QdrantLocal": {
          "GrpcAddress": "http://localhost:6334"
        },
        "QdrantDev": {
          "GrpcAddress": "http://***********:6334"
        }
      }
    },
    "Docupedia": {
      "ApiVersion": "rest/api",
      "API": {
        "GetContentbyId": "content/{0}",
        "SearchPageBySpaceAndTitle": "content?spaceKey={0}&title={1}",
        "CreatePage": "content",
        "GetChildPages": "content/{0}/child/page"
      },
      "DefaultExpands": {
        "FullContent": "?expand=version,metadata.labels,children.page,body.view&start={0}",
        "Basic": ""
      },
      "PageLimit": 100
    },
    "Chunk": {
      "MaxTokensPerChunk": 100,
      "OverlapRatio": 0.2
    },
    "Model": {
      "Providers": {
        "Azure": {
          "ApiKey": "********************************",
          "Endpoint": "https://bci-ai-cn.openai.azure.com/",
          "Completion": {
            "Active": "gpt-4.1-mini",
            "Deployments": {
              "gpt-4o-mini": {
                "Model": "gpt-4o-mini",
                "DeploymentName": "bcieswcngpt4omini",
                "MaxOutputTokens": 9000,
                "MaxInputTokens": 60000,
                "Temperature": 0.3,
                "TopP": 0.9
              },
              "gpt-4.1-mini": {
                "Model": "gpt-4.1-mini",
                "DeploymentName": "gpt-4.1-mini",
                "MaxOutputTokens": 9000,
                "MaxInputTokens": 60000,
                "Temperature": 0.3,
                "TopP": 0.9
              }
            }
          },
          "Embedding": {
            "Active": "text-embedding-3-large",
            "Deployments": {
              "text-embedding-3-small": {
                "Model": "text-embedding-3-small",
                "DeploymentName": "bci-esw-cntext-embedding-3-small",
                "dimensions": 1536,
                "maxTokens": 8192
              },
              "text-embedding-3-large": {
                "Model": "text-embedding-3-large",
                "DeploymentName": "bci-esw-cntext-embedding-3-large",
                "dimensions": 3072,
                "maxTokens": 8192
              }
            }
          }
        },
        "Ollama": {
          "Endpoint": "https://meslab.apac.bosch.com",
          "Completion": {
            "Active": "qwq",
            "Deployments": {
              "qwq": {
                "Model": "qwq",
                "MaxOutputTokens": 2048,
                "MaxInputTokens": 16000,
                "Temperature": 0.7,
                "TopP": 0.95
              },
              "qwen2.5@3b": {
                "Model": "qwen2.5@3b",
                "MaxOutputTokens": 2048,
                "MaxInputTokens": 8000,
                "Temperature": 0.7,
                "TopP": 0.95
              },
              "deepseek-r1@8b": {
                "Model": "deepseek-r1@8b",
                "MaxOutputTokens": 2048,
                "MaxInputTokens": 16000,
                "Temperature": 0.7,
                "TopP": 0.95
              },
              "llama3.1": {
                "Model": "llama3.1",
                "MaxOutputTokens": 2048,
                "MaxInputTokens": 16000,
                "Temperature": 0.7,
                "TopP": 0.95
              }
            }
          },
          "Embedding": {
            "Active": "nomic-embed-text",
            "Deployments": {
              "nomic-embed-text": {
                "Model": "nomic-embed-text",
                "dimensions": 768,
                "maxTokens": 8192
              },
              "bge-m3@latest": {
                "Model": "bge-m3@latest",
                "dimensions": 1024,
                "maxTokens": 8192
              }
            }
          }
        }
      }
    },
    "Proxy": {
      "Url": "http://rb-proxy-apac.bosch.com:8080",
      "Username": "BAP9BE",
      "Password": "Bosch12345",
      "TimeoutSeconds": 60
    },
    "QueryOptions": {
      "UseSpelling": true,
      "UseRewrite": true,
      "UseSimple": true,
      "UseMultiLanguage": true,
      "Limit": 5,
      "ScoreThreshold": 0.3,
      "InputTokenReserveRatio": 0.8
    }
  },
  "DataGeneration": {
    "QualityGate": {
      "SourceUrl": "https://inside-docupedia.bosch.com/confluence/display/PROM/PROMAP",
      "TargetUrl": "https://inside-docupedia.bosch.com/confluence/display/BCIESWCN/Quality+Gate"
    }
  }
}