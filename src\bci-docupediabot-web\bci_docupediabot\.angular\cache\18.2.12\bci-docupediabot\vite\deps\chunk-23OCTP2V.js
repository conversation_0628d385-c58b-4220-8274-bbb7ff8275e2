import {
  __async
} from "./chunk-Y4T55RDF.js";

// node_modules/@bci-web-core/web-components/dist/esm/overlay-handler-210e45aa.js
var WidthObserver = class {
  constructor(element, callbacks, considerPaddings = true) {
    this.element = element;
    this.callbacks = callbacks;
    this.considerPaddings = considerPaddings;
    this.breakpointVP1 = 480;
    this.breakpointVP2 = 768;
    this.containerPaddingForPhone = 14;
    this.containerPaddingForTablet = 16;
    this.desktopBreakpoint = this.breakpointVP2 - 2 * this.containerPaddingForTablet;
    this.tabletBreakpoint = this.breakpointVP1 - 2 * this.containerPaddingForPhone;
    this.resizeObserver = new ResizeObserver(() => this.updateViewport());
    this.isObserving = false;
  }
  connect() {
    this.isObserving = true;
    this.resizeObserver.observe(this.element);
    this.updateViewport();
  }
  disconnect() {
    this.isObserving = false;
    this.resizeObserver.unobserve(this.element);
  }
  isConnected() {
    return this.isObserving;
  }
  get currentViewport() {
    return this._currentViewport;
  }
  updateViewport() {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const availableWidth = this.element.clientWidth;
    if (availableWidth === 0) {
      return;
    }
    const showVP1 = availableWidth <= (this.considerPaddings ? this.tabletBreakpoint : this.breakpointVP1);
    const showVP2 = availableWidth <= (this.considerPaddings ? this.desktopBreakpoint : this.breakpointVP2);
    if (showVP1) {
      if (this._currentViewport !== "vp1") {
        (_b = (_a = this.callbacks).onVp1) === null || _b === void 0 ? void 0 : _b.call(_a);
        this._currentViewport = "vp1";
      }
    } else if (showVP2) {
      if (this._currentViewport !== "vp2") {
        (_d = (_c = this.callbacks).onVp2) === null || _d === void 0 ? void 0 : _d.call(_c);
        this._currentViewport = "vp2";
      }
    } else {
      if (this._currentViewport !== "vp3") {
        (_f = (_e = this.callbacks).onVp3) === null || _f === void 0 ? void 0 : _f.call(_e);
        this._currentViewport = "vp3";
      }
    }
    (_h = (_g = this.callbacks).onChange) === null || _h === void 0 ? void 0 : _h.call(_g, availableWidth);
  }
};
var OverlayHandler = class {
  constructor(_fullwidth = false, overlay = void 0, host = void 0, dialog = void 0) {
    this._fullwidth = _fullwidth;
    this.visible = false;
    this.isFocussed = false;
    this.overlayOpenedEvent = new CustomEvent("OverlayOpenedEvent");
    this.overlay = overlay;
    this.host = host;
    this.dialog = dialog;
  }
  get overlay() {
    return this._overlay;
  }
  set overlay(overlay) {
    this._overlay = overlay;
    if (this._overlay) {
      this._overlay.host = this.host;
      this._overlay.dialog = this.dialog;
    }
  }
  get host() {
    return this._host;
  }
  set host(host) {
    var _a, _b;
    if (this._host === host) {
      return;
    }
    this._host = host;
    const isConnected = (_b = (_a = this.widthObserver) === null || _a === void 0 ? void 0 : _a.isConnected()) !== null && _b !== void 0 ? _b : false;
    if (this.widthObserver) {
      this.widthObserver.disconnect();
    }
    if (this._fullwidth && host !== void 0) {
      this.widthObserver = new WidthObserver(host, {
        onChange: (width) => this.updateOverlayWidth(width)
      });
      if (isConnected) {
        this.widthObserver.connect();
      }
    }
    if (this.overlay) {
      this.overlay.host = host;
    }
  }
  get dialog() {
    return this._dialog;
  }
  set dialog(dialog) {
    this._dialog = dialog;
    if (this.overlay) {
      this.overlay.dialog = dialog;
    }
  }
  isReady() {
    return this.host !== void 0 && this.overlay !== void 0 && this.dialog !== void 0;
  }
  isHidden() {
    return this.isReady() && !this.visible;
  }
  isVisible() {
    return this.isReady() && this.visible;
  }
  show() {
    return __async(this, null, function* () {
      var _a;
      if (this.isHidden()) {
        document.dispatchEvent(this.overlayOpenedEvent);
        (_a = this.widthObserver) === null || _a === void 0 ? void 0 : _a.connect();
        yield this.handleVisibilityChanged(() => this.overlay.show());
        this.setupFocusObserver();
        this.setupScrollObserver();
        this.setupMutationObserver();
        document.addEventListener("OverlayOpenedEvent", () => {
          this.hide();
        });
      }
    });
  }
  hide() {
    return __async(this, null, function* () {
      var _a;
      (_a = this.widthObserver) === null || _a === void 0 ? void 0 : _a.disconnect();
      if (this.isVisible()) {
        this.removeFocusObserver();
        this.removeScrollObserver();
        this.removeMutationObserver();
        document.removeEventListener("OverlayOpenedEvent", () => {
        });
        return this.handleVisibilityChanged(() => this.overlay.hide());
      }
    });
  }
  updateOverlayWidth(width) {
    this.dialog.style.width = `${width}px`;
    this.updateOverlay();
  }
  handleVisibilityChanged(action) {
    return __async(this, null, function* () {
      var _a;
      const oldVisibility = this.visible;
      this.visible = yield action();
      if (oldVisibility !== this.visible) {
        (_a = this.visibilityChangeObserver) === null || _a === void 0 ? void 0 : _a.call(this, this.visible);
      }
    });
  }
  setupFocusObserver() {
    document.addEventListener("focusin", (event) => this.handleFocus(event));
  }
  removeFocusObserver() {
    document.removeEventListener("focusin", (event) => this.handleFocus(event));
  }
  handleFocus(event) {
    var _a;
    const oldFocusState = this.isFocussed;
    this.isFocussed = event.relatedTarget === this.dialog.parentNode || event.target === this.dialog.parentNode;
    if (oldFocusState != this.isFocussed) {
      (_a = this.focusChangeObserver) === null || _a === void 0 ? void 0 : _a.call(this, this.isFocussed);
    }
  }
  setupScrollObserver() {
    window.addEventListener("scroll", () => this.updateOverlay());
    window.addEventListener("wheel", () => this.updateOverlay());
  }
  removeScrollObserver() {
    window.removeEventListener("scroll", () => this.updateOverlay());
    window.removeEventListener("wheel", () => this.updateOverlay());
  }
  setupMutationObserver() {
    this.resizeObserver = new ResizeObserver((entries) => {
      entries.forEach((_) => {
        this.updateOverlay();
      });
    });
    this.resizeObserver.observe(this.dialog);
  }
  removeMutationObserver() {
    var _a;
    (_a = this.resizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();
  }
  updateOverlay() {
    window.requestAnimationFrame(() => this.overlay.update());
  }
};

export {
  WidthObserver,
  OverlayHandler
};
//# sourceMappingURL=chunk-23OCTP2V.js.map
