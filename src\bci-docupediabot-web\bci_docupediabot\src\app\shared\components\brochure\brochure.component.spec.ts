import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { BrochureComponent } from './brochure.component';

describe('BrochureComponent', () => {
  let component: BrochureComponent;
  let fixture: ComponentFixture<BrochureComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BrochureComponent],
      imports: [
        MatToolbarModule,
        MatIconModule,
        MatCardModule,
        MatChipsModule,
        MatTableModule,
        MatButtonModule,
        NoopAnimationsModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BrochureComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have platform stats', () => {
    expect(component.platformStats).toBeDefined();
    expect(component.platformStats.users).toBe('1000+');
    expect(component.platformStats.documents).toBe('50万+');
    expect(component.platformStats.queries).toBe('100万+');
    expect(component.platformStats.accuracy).toBe('95%');
  });

  it('should have core features', () => {
    expect(component.coreFeatures).toBeDefined();
    expect(component.coreFeatures.length).toBe(8);
    expect(component.coreFeatures[0].title).toBe('企业级安全');
  });

  it('should have tech stack', () => {
    expect(component.techStack).toBeDefined();
    expect(component.techStack.length).toBe(6);
    expect(component.techStack[0].layer).toBe('前端层');
  });

  it('should have integrations', () => {
    expect(component.integrations).toBeDefined();
    expect(component.integrations.length).toBe(4);
    expect(component.integrations.find(i => i.name === 'RAGFlow')).toBeDefined();
    expect(component.integrations.find(i => i.name === 'n8n')).toBeDefined();
  });

  it('should have performance metrics', () => {
    expect(component.performanceMetrics).toBeDefined();
    expect(component.performanceMetrics.length).toBe(6);
  });

  it('should have competitor comparison', () => {
    expect(component.competitorComparison).toBeDefined();
    expect(component.competitorComparison.length).toBe(6);
  });

  it('should scroll to section', () => {
    const mockElement = document.createElement('div');
    mockElement.scrollIntoView = jasmine.createSpy('scrollIntoView');
    spyOn(document, 'getElementById').and.returnValue(mockElement);

    component.scrollToSection('test-section');

    expect(document.getElementById).toHaveBeenCalledWith('test-section');
    expect(mockElement.scrollIntoView).toHaveBeenCalledWith({
      behavior: 'smooth',
      block: 'start'
    });
  });

  it('should print brochure', () => {
    spyOn(window, 'print');
    component.printBrochure();
    expect(window.print).toHaveBeenCalled();
  });

  it('should share brochure with native share API', () => {
    const mockShare = jasmine.createSpy('share').and.returnValue(Promise.resolve());
    (navigator as any).share = mockShare;

    component.shareBrochure();

    expect(mockShare).toHaveBeenCalledWith({
      title: 'BCI GenAI Platform 2025',
      text: '下一代企业级智能知识管理平台',
      url: window.location.href
    });
  });

  it('should fallback to clipboard when native share is not available', async () => {
    (navigator as any).share = undefined;
    const mockWriteText = jasmine.createSpy('writeText').and.returnValue(Promise.resolve());
    Object.defineProperty(navigator, 'clipboard', {
      value: { writeText: mockWriteText },
      writable: true
    });

    component.shareBrochure();

    expect(mockWriteText).toHaveBeenCalledWith(window.location.href);
  });

  it('should get star array correctly', () => {
    const stars3 = component.getStarArray(3);
    expect(stars3).toEqual([true, true, true, false, false]);

    const stars5 = component.getStarArray(5);
    expect(stars5).toEqual([true, true, true, true, true]);

    const stars0 = component.getStarArray(0);
    expect(stars0).toEqual([false, false, false, false, false]);
  });
});
