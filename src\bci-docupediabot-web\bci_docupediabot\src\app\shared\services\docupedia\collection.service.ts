import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map, Observable } from 'rxjs';
import { ApiResponse, Collection } from '@shared/models/docupedia.model';
import { ResponseResult } from '@shared/models/share.model';
import { environment } from 'src/environments/environment';
import { EmbeddingModel } from '@shared/enums';

@Injectable({
  providedIn: 'root'
})
export class CollectionService {
  private apiUrl = `${environment.baseUrl}/api/collections`;

  constructor(private http: HttpClient) {}

  getCollections(): Observable<Collection[]> {
    return this.http.get<ApiResponse<Collection[]>>(`${this.apiUrl}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  addCollection(collection: Collection): Observable<ResponseResult> {
    return this.http.post<ApiResponse<ResponseResult>>(`${this.apiUrl}`, collection).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  deleteCollection(collectionId: string): Observable<ResponseResult> {
    return this.http.delete<ApiResponse<ResponseResult>>(`${this.apiUrl}/${collectionId}`).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  updateCollection(collectionId: string, collection: Partial<Collection>): Observable<ResponseResult> {
    return this.http.put<ApiResponse<ResponseResult>>(`${this.apiUrl}/${collectionId}`, collection).pipe(
      map(response => {
        console.log(response.success);
        if (!response.success) {
          console.log(response.message);
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  updateCollectionContents(collectionId: string): Observable<ResponseResult> {
    const url = `${this.apiUrl}/${collectionId}/contents/update`;
    return this.http.post<ApiResponse<ResponseResult>>(url, null).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }

  embeddingCollectionContents(collectionId: string, embeddingModel: EmbeddingModel): Observable<ResponseResult> {
    const url = `${this.apiUrl}/${collectionId}/contents/embed`;
    return this.http.post<ApiResponse<ResponseResult>>(url, { embeddingModel }).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      })
    );
  }
}
