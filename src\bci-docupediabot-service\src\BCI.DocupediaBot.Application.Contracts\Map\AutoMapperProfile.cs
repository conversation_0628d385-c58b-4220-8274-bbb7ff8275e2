﻿using AutoMapper;
using BCI.DocupediaBot.Application.Contracts.Dtos.Chat;
using BCI.DocupediaBot.Application.Contracts.Dtos.Collection;
using BCI.DocupediaBot.Application.Contracts.Dtos.Content;
using BCI.DocupediaBot.Application.Contracts.Dtos.Page;
using BCI.DocupediaBot.Application.Contracts.Dtos.SysGroup;
using BCI.DocupediaBot.Application.Contracts.Dtos.SysUser;
using BCI.DocupediaBot.Domain.Entities;
using System;

namespace BCI.DocupediaBot.Application.Contracts.Map
{
  public class OrganizationProfile : Profile
  {
    public OrganizationProfile()
    {
			CreateMap<Collection, CollectionAddDTO>().ReverseMap();
			CreateMap<Collection, CollectionUpdateDTO>().ReverseMap();
			CreateMap<Collection, CollectionResponseDTO>().ReverseMap();

			CreateMap<Page, PageAddDTO>()
				.ForMember(dest => dest.CollectionId, opt => opt.Ignore())
				.ForMember(dest => dest.UrlType, opt => opt.Ignore())
				.ForMember(dest => dest.UrlData, opt => opt.Ignore())
				.ForMember(dest => dest.UserToken, opt => opt.Ignore())
				.ReverseMap()
				.ForMember(dest => dest.Id, opt => opt.Ignore());
			CreateMap<Page, PageResponseDTO>().ReverseMap();

      CreateMap<SysUser, SysUserAddDTO>().ReverseMap();
      CreateMap<SysUser, SysUserUpdateDTO>().ReverseMap();
      CreateMap<SysUser, SysUserResponseDTO>().ReverseMap();
      CreateMap<SysUserAddDTO, SysUserResponseDTO>().ReverseMap();

      CreateMap<SysGroup, SysGroupAddDTO>().ReverseMap();
      CreateMap<SysGroup, SysGroupUpdateDTO>().ReverseMap();
      CreateMap<SysGroup, SysGroupResponseDTO>().ReverseMap();

      CreateMap<Content, ContentAddDTO>().ReverseMap();
			CreateMap<Content, ContentUpdateDTO>().ReverseMap();
			CreateMap<Content, ContentResponseDTO>().ReverseMap();
			CreateMap<ContentResponseDTO, ContentUpdateDTO>().ReverseMap();
			CreateMap<ContentDocupediaResponseDTO, ContentAddDTO>()
        .ForMember(dest => dest.SourceId, opt => opt.MapFrom(source => source.Id))
        .ForMember(dest => dest.SourceModificationTime, opt => opt.MapFrom(source => source.Version.When.ToUniversalTime()))
        .ForMember(dest => dest.VersionNo, opt => opt.MapFrom(source => source.Version.Number))
        .ForMember(dest => dest.OriginContent, opt => opt.MapFrom(source => source.Body.View.Value));
			CreateMap<Content, AllPageContentsDTO>().ReverseMap();
			CreateMap<Content, ContentDocupediaResponseDTO>()
				.ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SourceId));

      CreateMap<ChatHistory, ChatHistoryAddDTO>().ReverseMap();
    }
	}
}
