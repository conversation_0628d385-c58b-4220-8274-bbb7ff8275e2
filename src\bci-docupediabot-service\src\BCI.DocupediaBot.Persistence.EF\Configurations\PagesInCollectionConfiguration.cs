﻿using BCI.DocupediaBot.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace BCI.DocupediaBot.Persistence.EF.Configurations
{
  public class PagesInCollectionConfiguration : IEntityTypeConfiguration<PagesInCollection>
  {
    public void Configure(EntityTypeBuilder<PagesInCollection> entityBuilder)
    {
      entityBuilder.ToTable(nameof(PagesInCollection));


      entityBuilder.HasIndex(x => x.CollectionId);
      entityBuilder.HasIndex(x => x.PageId);
      entityBuilder.HasIndex(x => x.IsEmbedding);
      entityBuilder.HasIndex(x => new { x.CollectionId, x.PageId }).IsUnique();
      entityBuilder.HasIndex(x => new { x.CollectionId, x.IsEmbedding });


      entityBuilder.HasIndex(x => new { x.TenantId, x.Is<PERSON>eleted });
      entityBuilder.HasIndex(x => new { x.CollectionId, x.TenantId, x.IsDeleted });
      entityBuilder.HasIndex(x => new { x.PageId, x.TenantId, x.IsDeleted });
    }
  }
}
