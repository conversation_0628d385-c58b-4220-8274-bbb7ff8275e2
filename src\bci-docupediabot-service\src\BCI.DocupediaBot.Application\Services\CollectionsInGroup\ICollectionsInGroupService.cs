using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BCI.DocupediaBot.Application.Services.CollectionsInGroup
{
  public interface ICollectionsInGroupService
  {
    Task<List<Guid>> GetGroupIdsByCollectionIdAsync(Guid collectionId);
    Task<List<Guid>> GetCollectionIdsByGroupIdAsync(Guid groupId);
    Task<List<Guid>> GetCollectionIdsByGroupIdsAsync(IEnumerable<Guid> groupIds);
    Task<bool> ExistsMappingAsync(Guid collectionId, Guid groupId);
    Task AddMappingAsync(Guid collectionId, Guid groupId);
    Task AddMappingsAsync(Guid collectionId, IEnumerable<Guid> groupIds);
    Task DeleteByCollectionIdAsync(Guid collectionId);
    Task DeleteByGroupIdAsync(Guid groupId);
    Task DeleteMappingAsync(Guid collectionId, Guid groupId);
    Task UpdateCollectionGroupsAsync(Guid collectionId, IEnumerable<Guid> groupIds);
    Task<Dictionary<Guid, List<Guid>>> GetGroupIdsByCollectionIdsBatchAsync(IEnumerable<Guid> collectionIds);
  }
}
