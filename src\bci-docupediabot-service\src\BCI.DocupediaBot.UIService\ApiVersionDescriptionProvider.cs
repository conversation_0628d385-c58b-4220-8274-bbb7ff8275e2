﻿using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;

namespace BCI.DocupediaBot.UIService
{
  public class CustomApiVersionDescriptionProvider : DefaultApiVersionDescriptionProvider, IApiVersionDescriptionProvider
  {
    private readonly List<ApiVersionDescription> _apiVersionDescriptions;

    protected virtual List<ApiVersionDescription> AdditionalApiVersionDescriptions { get; } = new List<ApiVersionDescription>();


    public new IReadOnlyList<ApiVersionDescription> ApiVersionDescriptions => _apiVersionDescriptions;

    public CustomApiVersionDescriptionProvider(IActionDescriptorCollectionProvider actionDescriptorCollectionProvider, IOptions<ApiExplorerOptions> apiExplorerOptions)
      : base(actionDescriptorCollectionProvider, apiExplorerOptions)
    {
      _apiVersionDescriptions = new List<ApiVersionDescription>();
      _apiVersionDescriptions.AddRange(base.ApiVersionDescriptions);
      _apiVersionDescriptions.AddRange(AdditionalApiVersionDescriptions);
      _apiVersionDescriptions = (from a in _apiVersionDescriptions
        orderby a.ApiVersion descending, a.GroupName
        select a).ToList();
    }
  }

  public class ApiVersionDescriptionProvider : CustomApiVersionDescriptionProvider
  {
    public ApiVersionDescriptionProvider(IActionDescriptorCollectionProvider actionDescriptorCollectionProvider, IOptions<ApiExplorerOptions> apiExplorerOptions) : base(actionDescriptorCollectionProvider, apiExplorerOptions) { }

    protected override List<ApiVersionDescription> AdditionalApiVersionDescriptions => new();
  }
}
