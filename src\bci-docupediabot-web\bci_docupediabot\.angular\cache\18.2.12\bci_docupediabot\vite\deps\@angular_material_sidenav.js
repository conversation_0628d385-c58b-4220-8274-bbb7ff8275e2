import {
  MAT_DRAWER_DEFAULT_AUTOSIZE,
  MAT_DRAWER_DEFAULT_AUTOSIZE_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>av<PERSON>ontent,
  MatSidenavModule,
  matD<PERSON><PERSON><PERSON>nimations,
  throwMatDuplicatedDrawerError
} from "./chunk-3ZDSG5LX.js";
import "./chunk-3QZRZCTL.js";
import "./chunk-SMRBKZC6.js";
import "./chunk-PXNPNZO3.js";
import "./chunk-TQKN2ASK.js";
import "./chunk-5X2BSBAM.js";
import "./chunk-X3P73C3G.js";
import "./chunk-DWV65MH7.js";
import "./chunk-KXOMTDJ6.js";
import "./chunk-KRK546R7.js";
import "./chunk-Y4T55RDF.js";
export {
  MAT_DRAWER_DEFAULT_AUTOSIZE,
  MAT_<PERSON><PERSON><PERSON><PERSON>_DEFAULT_AUTO<PERSON><PERSON><PERSON>_FA<PERSON>OR<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>av,
  MatSidenavContainer,
  MatSidenavContent,
  MatSidenavModule,
  matDrawerAnimations,
  throwMatDuplicatedDrawerError
};
//# sourceMappingURL=@angular_material_sidenav.js.map
