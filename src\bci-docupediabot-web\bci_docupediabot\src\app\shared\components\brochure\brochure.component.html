<bci-page-content>
  <bci-master-view [isPadded]="true">
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
      <button type="button" mat-raised-button color="primary" (click)="switchToEnglish()">
        Switch to English
      </button>
    </div>

    <!-- 系统概述 -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🌟 系统概述</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>
          <strong>BCI GenAI Platform</strong> 是博世RBAC BCI开发的企业级智能知识管理平台，专为大型制造企业的复杂知识生态而设计。
          系统采用创新的<strong>PointId差异更新机制</strong>和<strong>综合分块技术</strong>，结合Qdrant向量数据库的语义检索能力，
          实现了图文并茂、准确率高达95%的智能问答效果。通过DDD多租户架构、LDAP身份认证和细粒度权限控制，
          为全球团队提供安全、高效、智能的知识服务。
        </p>

        <div class="unique-features">
          <h4>🎯 创新且很实用设计</h4>
          <ul>
            <li><strong>Docupedia深度集成</strong>：对Docupedia深度优化，支持V1和V2版本的超过14000个Space</li>
            <li><strong>PointId差异更新</strong>：避免全量重建，实现增量更新，节省时间的同时还能大大节约Token(省钱)</li>
            <li><strong>多模态RAG融合</strong>：文本+图像+表格统一检索和结构化输出，文档溯源，用户体验很好</li>
            <li><strong>语义向量缓存</strong>：通过部分缓存机制，让你的问题一直会有上下文，关闭系统也没事儿</li>
            <li><strong>混合AI架构</strong>：Azure OpenAI + Ollama本地部署，通过配置就可以兼容多种LLM</li>
          </ul>
        </div>

        <!-- 核心数据 -->
        <div class="stats-row">
          <div class="stat-item">
            <strong>{{platformStats.users}}</strong>
            <span>活跃用户</span>
          </div>
          <div class="stat-item">
            <strong>{{platformStats.space}}</strong>
            <span>Docupedia 空间</span>
          </div>
          <div class="stat-item">
            <strong>{{platformStats.queries}}</strong>
            <span>查询次数</span>
          </div>
          <div class="stat-item">
            <strong>{{platformStats.accuracy}}</strong>
            <span>准确率</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- 核心功能特性 -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🚀 核心功能特性</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="features-grid">
          <div *ngFor="let feature of coreFeatures" class="feature-item">
            <div class="feature-header">
              <span class="feature-icon">{{feature.icon}}</span>
              <h4>{{feature.title}}</h4>
            </div>
            <p>{{feature.description}}</p>
            <div class="tech-details">
              <small>{{feature.techDetails}}</small>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- 技术架构 -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🏗️ 技术架构</mat-card-title>
        <mat-card-subtitle>基于DDD的多层架构设计，支持企业级SAAS部署</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="tech-stack-table">
          <table mat-table [dataSource]="techStack" class="tech-table">
            <ng-container matColumnDef="layer">
              <th mat-header-cell *matHeaderCellDef>架构层级</th>
              <td mat-cell *matCellDef="let element">
                <strong>{{element.layer}}</strong>
                <div class="layer-description">{{element.description}}</div>
              </td>
            </ng-container>

            <ng-container matColumnDef="technologies">
              <th mat-header-cell *matHeaderCellDef>核心技术与特性</th>
              <td mat-cell *matCellDef="let element">
                <span class="tech-tags">
                  <span *ngFor="let tech of element.technologies" class="tech-chip">{{tech}}</span>
                </span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="['layer', 'technologies']"></tr>
            <tr mat-row *matRowDef="let row; columns: ['layer', 'technologies'];"></tr>
          </table>
        </div>

        <div class="architecture-highlights">
          <h4>🏗️ 架构设计亮点</h4>
          <div class="highlights-grid">
            <div class="highlight-item">
              <strong>DDD领域驱动</strong>
              <p>清晰的业务边界，高内聚低耦合的微服务架构</p>
            </div>
            <div class="highlight-item">
              <strong>多租户隔离</strong>
              <p>数据库级别隔离，确保企业数据安全与性能</p>
            </div>
            <div class="highlight-item">
              <strong>向量检索优化</strong>
              <p>PointId机制实现增量更新，避免全量重建开销</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- 平台集成生态 -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🔗 平台集成生态</mat-card-title>
        <mat-card-subtitle>开放式平台架构，深度集成外部工具和企业系统</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="integrations-compact">
          <div *ngFor="let integration of integrations" class="integration-compact-item">
            <div class="integration-compact-header">
              <span class="integration-icon" [style.color]="integration.color">{{integration.icon}}</span>
              <div class="integration-compact-info">
                <h4>{{integration.name}}</h4>
                <span class="status-badge" [style.background-color]="integration.color">
                  {{integration.status}}
                </span>
              </div>
            </div>
            <p class="integration-description">{{integration.description}}</p>
            <div class="feature-tags">
              <span *ngFor="let feature of integration.features" class="feature-tag">
                ✓ {{feature}}
              </span>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- 实际应用场景 -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>🎯 实际应用场景</mat-card-title>
        <mat-card-subtitle>真实业务场景中的智能知识管理应用</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="scenarios-grid">
          <div class="scenario-category">
            <h4>🏭 工业制造场景</h4>
            <div class="scenario-examples">
              <div class="scenario-item">
                <h5>生产工艺标准查询</h5>
                <p><strong>场景</strong>: 工艺工程师需要快速查找特定产品的生产工艺参数</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "电机定子绕组的绝缘处理工艺温度和时间要求？"</div>
                  <div class="system-answer">🤖 根据《电机制造工艺规范》第4.3节，绝缘处理工艺：<br>
                  • 预烘温度：80-100°C，持续2小时<br>
                  • 浸漆温度：40-60°C，真空度≥0.08MPa<br>
                  • 固化温度：130-150°C，持续8小时<br>
                  • 冷却要求：自然冷却至室温，避免急冷</div>
                </div>
              </div>

              <div class="scenario-item">
                <h5>设备故障诊断支持</h5>
                <p><strong>场景</strong>: 维修技师遇到设备异常，需要快速定位故障原因</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "数控机床出现X轴定位精度超差，可能的原因和解决方案？"</div>
                  <div class="system-answer">🤖 根据《数控设备故障诊断手册》，X轴精度超差常见原因：<br>
                  <strong>机械原因</strong>：丝杠磨损、导轨间隙过大<br>
                  <strong>电气原因</strong>：编码器故障、伺服参数漂移<br>
                  <strong>检查步骤</strong>：1.测量反向间隙 2.检查编码器信号 3.校准伺服参数<br>
                  <strong>预防措施</strong>：定期润滑、精度补偿、参数备份</div>
                </div>
              </div>
            </div>
          </div>

          <div class="scenario-category">
            <h4>🌱 ESG合规场景</h4>
            <div class="scenario-examples">
              <div class="scenario-item">
                <h5>企业尽职调查</h5>
                <p><strong>场景</strong>: 合规团队需要快速获取供应商的ESG表现数据</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "如何评估新供应商的碳排放和环境合规情况？"</div>
                  <div class="system-answer">🤖 根据《供应商ESG尽调标准》V2.1，评估框架包括：<br>
                  <strong>环境指标</strong>：碳排放强度、能源使用效率、废物处理合规<br>
                  <strong>社会指标</strong>：员工安全记录、劳工权益保护、社区影响<br>
                  <strong>治理指标</strong>：反腐败政策、透明度报告、风险管理体系<br>
                  <strong>认证要求</strong>：ISO 14001、OHSAS 18001、SA 8000</div>
                </div>
              </div>

              <div class="scenario-item">
                <h5>Benchmark报告分析</h5>
                <p><strong>场景</strong>: 战略规划团队需要对比行业ESG最佳实践</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "汽车行业在碳中和方面的领先企业有哪些最佳实践？"</div>
                  <div class="system-answer">🤖 基于《汽车行业ESG Benchmark报告》2024版分析：<br>
                  <strong>沃尔沃</strong>：2030年全电动化，供应链碳中和承诺<br>
                  <strong>宝马</strong>：循环经济模式，95%材料可回收利用<br>
                  <strong>特斯拉</strong>：垂直整合，自建电池回收体系<br>
                  <strong>关键指标</strong>：Scope 3排放减少30%，可再生能源使用率80%+</div>
                </div>
              </div>
            </div>
          </div>

          <div class="scenario-category">
            <h4>🎯 质量管理场景</h4>
            <div class="scenario-examples">
              <div class="scenario-item">
                <h5>Quality Gate文档自动生成</h5>
                <p><strong>场景</strong>: 项目团队需要快速生成QG所需的各类文档，符合开发规范</p>
                <div class="scenario-dialog">
                  <div class="user-question">❓ "帮我生成电机项目QG3阶段的FMEA文档模板和检查清单"</div>
                  <div class="system-answer">🤖 基于《QG标准模板库》和项目信息，自动生成：<br>
                  <strong>FMEA文档</strong>：包含功能分析、失效模式、影响分析、RPN评估<br>
                  <strong>检查清单</strong>：设计评审要点、测试验证项目、文档完整性检查<br>
                  <strong>风险评估</strong>：关键风险识别、缓解措施、责任人分配<br>
                  <strong>合规检查</strong>：自动对照开发规范，标注缺失项目</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- 技术实现流程 -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>⚙️ 技术实现流程</mat-card-title>
        <mat-card-subtitle>深度解析系统如何实现高准确率的智能问答</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="tech-flow-container">
          <!-- 文档处理流程 -->
          <div class="flow-section">
            <h4>📄 文档处理与向量化流程</h4>
            <div class="flow-steps-horizontal">
              <div class="flow-step-compact">
                <div class="step-number">1</div>
                <h5>智能分块</h5>
                <p>语义边界分块、重叠窗口、动态调整</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">2</div>
                <h5>多模态提取</h5>
                <p>OCR识别、表格解析、图像描述</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">3</div>
                <h5>批量向量化</h5>
                <p>动态批量、质量控制、异常检测</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">4</div>
                <h5>PointId更新</h5>
                <p>哈希比对、增量更新、版本管理</p>
              </div>
            </div>
          </div>

          <!-- 检索与召回流程 -->
          <div class="flow-section">
            <h4>🔍 智能检索与召回流程</h4>
            <div class="flow-steps-horizontal">
              <div class="flow-step-compact">
                <div class="step-number">1</div>
                <h5>问题优化</h5>
                <p>意图识别、关键词扩展、多语言标准化</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">2</div>
                <h5>多策略召回</h5>
                <p>语义检索、BM25、元数据过滤</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">3</div>
                <h5>智能重排序</h5>
                <p>相似度归一化、时效性权重、反馈学习</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">4</div>
                <h5>上下文构建</h5>
                <p>片段聚合、长度控制、去重合并</p>
              </div>
            </div>
          </div>

          <!-- 提示词工程与生成 -->
          <div class="flow-section">
            <h4>🎯 提示词工程与答案生成</h4>
            <div class="flow-steps-horizontal">
              <div class="flow-step-compact">
                <div class="step-number">1</div>
                <h5>提示词构建</h5>
                <p>领域模板、角色定义、Few-shot示例</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">2</div>
                <h5>混合AI推理</h5>
                <p>云端+本地、负载均衡、成本优化</p>
              </div>
              <div class="flow-arrow">→</div>
              <div class="flow-step-compact">
                <div class="step-number">3</div>
                <h5>质量控制</h5>
                <p>事实验证、相关性评分、安全过滤</p>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- 特色功能展示 -->
    <mat-card class="section-card">
      <mat-card-header>
        <mat-card-title>✨ 特色功能展示</mat-card-title>
        <mat-card-subtitle>系统独有的智能分析与管理功能</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="special-features">
          <div class="feature-showcase">
            <h4>📊 Collection智能总结</h4>
            <div class="feature-demo">
              <div class="demo-description">
                <p>系统能够自动分析Collection中的所有文档，生成智能总结报告</p>
                <ul>
                  <li><strong>内容概览</strong>：文档类型分布、主题分析、关键词云</li>
                  <li><strong>质量评估</strong>：文档完整性、信息密度、更新频率</li>
                  <li><strong>使用统计</strong>：热门查询、用户偏好、访问趋势</li>
                  <li><strong>优化建议</strong>：内容补充建议、结构优化方案</li>
                </ul>
              </div>
              <div class="demo-example">
                <div class="summary-card">
                  <h5>📋 Collection "汽车制造工艺" 分析报告</h5>
                  <div class="summary-stats">
                    <span class="stat">📄 文档总数: 156</span>
                    <span class="stat">🔥 热门主题: 焊接工艺(23%), 涂装流程(18%)</span>
                    <span class="stat">⚠️ 发现重复: 12个相似文档</span>
                    <span class="stat">📈 查询增长: +15% (本月)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="feature-showcase">
            <h4>🔍 重复内容智能检测</h4>
            <div class="feature-demo">
              <div class="demo-description">
                <p>基于语义相似度的重复内容检测，帮助优化知识库质量</p>
                <ul>
                  <li><strong>语义去重</strong>：识别表达不同但含义相同的内容</li>
                  <li><strong>版本追踪</strong>：检测同一文档的不同版本</li>
                  <li><strong>相似度评分</strong>：量化内容重复程度</li>
                  <li><strong>合并建议</strong>：提供内容整合方案</li>
                </ul>
              </div>
              <div class="demo-example">
                <div class="duplicate-detection">
                  <h5>🚨 检测到重复内容</h5>
                  <div class="duplicate-item">
                    <div class="similarity-score">相似度: 87%</div>
                    <div class="doc-pair">
                      <span>📄 "焊接安全操作规程_V1.2.pdf"</span>
                      <span>📄 "电焊作业安全标准_2024.docx"</span>
                    </div>
                    <div class="suggestion">💡 建议: 合并为统一的安全操作标准</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="feature-showcase">
            <h4>📈 知识图谱关联分析</h4>
            <div class="feature-demo">
              <div class="demo-description">
                <p>自动构建知识关联网络，发现隐藏的知识连接</p>
                <ul>
                  <li><strong>实体识别</strong>：自动提取关键概念和实体</li>
                  <li><strong>关系挖掘</strong>：发现概念间的关联关系</li>
                  <li><strong>知识推荐</strong>：基于关联度推荐相关内容</li>
                  <li><strong>缺失发现</strong>：识别知识体系中的空白点</li>
                </ul>
              </div>
              <div class="demo-example">
                <div class="knowledge-graph">
                  <h5>🕸️ "电机制造" 知识关联图</h5>
                  <div class="graph-nodes">
                    <span class="node primary">电机制造</span>
                    <span class="node">→ 绕组工艺</span>
                    <span class="node">→ 质量检测</span>
                    <span class="node">→ 安全标准</span>
                    <span class="node missing">❓ 环保要求 (缺失)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

  </bci-master-view>
</bci-page-content>
